﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmJinbaoCourse
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmJinbaoCourse))
        Me.btn_close = New iTong.Components.tbButton()
        Me.pic_iconLarge = New iTong.Components.tbPictureBox()
        Me.btnInstall = New iTong.Components.tbButton()
        Me.btnStep2 = New iTong.Components.tbButton()
        Me.btnStep3 = New iTong.Components.tbButton()
        Me.btnStep1 = New iTong.Components.tbButton()
        Me.lblstepOne = New System.Windows.Forms.Label()
        Me.lblStepTwo = New System.Windows.Forms.Label()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.lblStepThree = New System.Windows.Forms.Label()
        CType(Me.pic_iconLarge, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(499, 3)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 6
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'pic_iconLarge
        '
        Me.pic_iconLarge.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pic_iconLarge.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.pic_iconLarge.Image = Global.iTong.My.Resources.Resources.gif_getinivationcode
        Me.pic_iconLarge.Location = New System.Drawing.Point(31, 83)
        Me.pic_iconLarge.Name = "pic_iconLarge"
        Me.pic_iconLarge.Size = New System.Drawing.Size(213, 156)
        Me.pic_iconLarge.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.pic_iconLarge.TabIndex = 124
        Me.pic_iconLarge.TabStop = False
        Me.pic_iconLarge.tbAutoSize = False
        Me.pic_iconLarge.tbBackgroundImage = Nothing
        Me.pic_iconLarge.tbSplit = "0,0,0,0"
        '
        'btnInstall
        '
        Me.btnInstall.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnInstall.BackColor = System.Drawing.Color.Transparent
        Me.btnInstall.BindingForm = Nothing
        Me.btnInstall.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btnInstall.ForeColor = System.Drawing.Color.White
        Me.btnInstall.Location = New System.Drawing.Point(376, 313)
        Me.btnInstall.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnInstall.Name = "btnInstall"
        Me.btnInstall.Padding = New System.Windows.Forms.Padding(23, 2, 23, 2)
        Me.btnInstall.Selectable = True
        Me.btnInstall.Size = New System.Drawing.Size(113, 30)
        Me.btnInstall.TabIndex = 134
        Me.btnInstall.tbAdriftIconWhenHover = False
        Me.btnInstall.tbAutoSize = False
        Me.btnInstall.tbAutoSizeEx = True
        Me.btnInstall.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnInstall.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbBadgeNumber = 0
        Me.btnInstall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstall.tbEndEllipsis = False
        Me.btnInstall.tbIconHoldPlace = True
        Me.btnInstall.tbIconImage = Nothing
        Me.btnInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInstall.tbIconMore = False
        Me.btnInstall.tbIconMouseDown = Nothing
        Me.btnInstall.tbIconMouseHover = Nothing
        Me.btnInstall.tbIconMouseLeave = Nothing
        Me.btnInstall.tbIconPlaceText = 2
        Me.btnInstall.tbIconReadOnly = Nothing
        Me.btnInstall.tbImageMouseDown = Nothing
        Me.btnInstall.tbImageMouseHover = Nothing
        Me.btnInstall.tbImageMouseLeave = Nothing
        Me.btnInstall.tbProgressValue = 50
        Me.btnInstall.tbReadOnly = False
        Me.btnInstall.tbReadOnlyText = False
        Me.btnInstall.tbShadow = False
        Me.btnInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstall.tbShowDot = False
        Me.btnInstall.tbShowMoreIconImg = CType(resources.GetObject("btnInstall.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstall.tbShowNew = False
        Me.btnInstall.tbShowProgress = False
        Me.btnInstall.tbShowTip = True
        Me.btnInstall.tbShowToolTipOnButton = False
        Me.btnInstall.tbSplit = "13,11,13,11"
        Me.btnInstall.tbText = "我知道了"
        Me.btnInstall.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbTextColor = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDown = System.Drawing.Color.White
        Me.btnInstall.tbTextColorHover = System.Drawing.Color.White
        Me.btnInstall.tbTextMouseDownPlace = 0
        Me.btnInstall.tbToolTip = ""
        Me.btnInstall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnInstall.VisibleEx = True
        '
        'btnStep2
        '
        Me.btnStep2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep2.BackColor = System.Drawing.Color.Transparent
        Me.btnStep2.BindingForm = Nothing
        Me.btnStep2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep2.Location = New System.Drawing.Point(283, 143)
        Me.btnStep2.Name = "btnStep2"
        Me.btnStep2.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep2.Selectable = True
        Me.btnStep2.Size = New System.Drawing.Size(18, 17)
        Me.btnStep2.TabIndex = 135
        Me.btnStep2.tbAdriftIconWhenHover = False
        Me.btnStep2.tbAutoSize = False
        Me.btnStep2.tbAutoSizeEx = True
        Me.btnStep2.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep2
        Me.btnStep2.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep2.tbBadgeNumber = 0
        Me.btnStep2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep2.tbEndEllipsis = False
        Me.btnStep2.tbIconHoldPlace = True
        Me.btnStep2.tbIconImage = Nothing
        Me.btnStep2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep2.tbIconMore = False
        Me.btnStep2.tbIconMouseDown = Nothing
        Me.btnStep2.tbIconMouseHover = Nothing
        Me.btnStep2.tbIconMouseLeave = Nothing
        Me.btnStep2.tbIconPlaceText = 2
        Me.btnStep2.tbIconReadOnly = Nothing
        Me.btnStep2.tbImageMouseDown = Nothing
        Me.btnStep2.tbImageMouseHover = Nothing
        Me.btnStep2.tbImageMouseLeave = Nothing
        Me.btnStep2.tbProgressValue = 50
        Me.btnStep2.tbReadOnly = False
        Me.btnStep2.tbReadOnlyText = False
        Me.btnStep2.tbShadow = False
        Me.btnStep2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep2.tbShowDot = False
        Me.btnStep2.tbShowMoreIconImg = CType(resources.GetObject("btnStep2.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep2.tbShowNew = False
        Me.btnStep2.tbShowProgress = False
        Me.btnStep2.tbShowTip = True
        Me.btnStep2.tbShowToolTipOnButton = False
        Me.btnStep2.tbSplit = "0,0,0,0"
        Me.btnStep2.tbText = ""
        Me.btnStep2.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.tbTextColor = System.Drawing.Color.White
        Me.btnStep2.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep2.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep2.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep2.tbTextMouseDownPlace = 0
        Me.btnStep2.tbToolTip = ""
        Me.btnStep2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.VisibleEx = True
        '
        'btnStep3
        '
        Me.btnStep3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep3.BackColor = System.Drawing.Color.Transparent
        Me.btnStep3.BindingForm = Nothing
        Me.btnStep3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep3.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep3.Location = New System.Drawing.Point(283, 202)
        Me.btnStep3.Name = "btnStep3"
        Me.btnStep3.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep3.Selectable = True
        Me.btnStep3.Size = New System.Drawing.Size(18, 17)
        Me.btnStep3.TabIndex = 136
        Me.btnStep3.tbAdriftIconWhenHover = False
        Me.btnStep3.tbAutoSize = False
        Me.btnStep3.tbAutoSizeEx = True
        Me.btnStep3.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep3
        Me.btnStep3.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep3.tbBadgeNumber = 0
        Me.btnStep3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep3.tbEndEllipsis = False
        Me.btnStep3.tbIconHoldPlace = True
        Me.btnStep3.tbIconImage = Nothing
        Me.btnStep3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep3.tbIconMore = False
        Me.btnStep3.tbIconMouseDown = Nothing
        Me.btnStep3.tbIconMouseHover = Nothing
        Me.btnStep3.tbIconMouseLeave = Nothing
        Me.btnStep3.tbIconPlaceText = 2
        Me.btnStep3.tbIconReadOnly = Nothing
        Me.btnStep3.tbImageMouseDown = Nothing
        Me.btnStep3.tbImageMouseHover = Nothing
        Me.btnStep3.tbImageMouseLeave = Nothing
        Me.btnStep3.tbProgressValue = 50
        Me.btnStep3.tbReadOnly = False
        Me.btnStep3.tbReadOnlyText = False
        Me.btnStep3.tbShadow = False
        Me.btnStep3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep3.tbShowDot = False
        Me.btnStep3.tbShowMoreIconImg = CType(resources.GetObject("btnStep3.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep3.tbShowNew = False
        Me.btnStep3.tbShowProgress = False
        Me.btnStep3.tbShowTip = True
        Me.btnStep3.tbShowToolTipOnButton = False
        Me.btnStep3.tbSplit = "0,0,0,0"
        Me.btnStep3.tbText = ""
        Me.btnStep3.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.tbTextColor = System.Drawing.Color.White
        Me.btnStep3.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep3.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep3.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep3.tbTextMouseDownPlace = 0
        Me.btnStep3.tbToolTip = ""
        Me.btnStep3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep3.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.VisibleEx = True
        '
        'btnStep1
        '
        Me.btnStep1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep1.BackColor = System.Drawing.Color.Transparent
        Me.btnStep1.BindingForm = Nothing
        Me.btnStep1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep1.Location = New System.Drawing.Point(283, 98)
        Me.btnStep1.Name = "btnStep1"
        Me.btnStep1.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep1.Selectable = True
        Me.btnStep1.Size = New System.Drawing.Size(18, 17)
        Me.btnStep1.TabIndex = 137
        Me.btnStep1.tbAdriftIconWhenHover = False
        Me.btnStep1.tbAutoSize = False
        Me.btnStep1.tbAutoSizeEx = True
        Me.btnStep1.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep1
        Me.btnStep1.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep1.tbBadgeNumber = 0
        Me.btnStep1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep1.tbEndEllipsis = False
        Me.btnStep1.tbIconHoldPlace = True
        Me.btnStep1.tbIconImage = Nothing
        Me.btnStep1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep1.tbIconMore = False
        Me.btnStep1.tbIconMouseDown = Nothing
        Me.btnStep1.tbIconMouseHover = Nothing
        Me.btnStep1.tbIconMouseLeave = Nothing
        Me.btnStep1.tbIconPlaceText = 2
        Me.btnStep1.tbIconReadOnly = Nothing
        Me.btnStep1.tbImageMouseDown = Nothing
        Me.btnStep1.tbImageMouseHover = Nothing
        Me.btnStep1.tbImageMouseLeave = Nothing
        Me.btnStep1.tbProgressValue = 50
        Me.btnStep1.tbReadOnly = False
        Me.btnStep1.tbReadOnlyText = False
        Me.btnStep1.tbShadow = False
        Me.btnStep1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep1.tbShowDot = False
        Me.btnStep1.tbShowMoreIconImg = CType(resources.GetObject("btnStep1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep1.tbShowNew = False
        Me.btnStep1.tbShowProgress = False
        Me.btnStep1.tbShowTip = True
        Me.btnStep1.tbShowToolTipOnButton = False
        Me.btnStep1.tbSplit = "0,0,0,0"
        Me.btnStep1.tbText = ""
        Me.btnStep1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.tbTextColor = System.Drawing.Color.White
        Me.btnStep1.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep1.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep1.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep1.tbTextMouseDownPlace = 0
        Me.btnStep1.tbToolTip = ""
        Me.btnStep1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.VisibleEx = True
        '
        'lblstepOne
        '
        Me.lblstepOne.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblstepOne.BackColor = System.Drawing.Color.Transparent
        Me.lblstepOne.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblstepOne.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblstepOne.Location = New System.Drawing.Point(318, 99)
        Me.lblstepOne.Name = "lblstepOne"
        Me.lblstepOne.Size = New System.Drawing.Size(162, 14)
        Me.lblstepOne.TabIndex = 138
        Me.lblstepOne.Text = "安装1元夺宝"
        '
        'lblStepTwo
        '
        Me.lblStepTwo.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblStepTwo.BackColor = System.Drawing.Color.Transparent
        Me.lblStepTwo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepTwo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStepTwo.Location = New System.Drawing.Point(318, 144)
        Me.lblStepTwo.Name = "lblStepTwo"
        Me.lblStepTwo.Size = New System.Drawing.Size(162, 14)
        Me.lblStepTwo.TabIndex = 138
        Me.lblStepTwo.Text = "打开1元夺宝，并登录"
        '
        'Label2
        '
        Me.Label2.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.Label2.Location = New System.Drawing.Point(318, 168)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(162, 14)
        Me.Label2.TabIndex = 138
        Me.Label2.Text = "（手机号或第三方账号均可）"
        '
        'lblStepThree
        '
        Me.lblStepThree.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblStepThree.BackColor = System.Drawing.Color.Transparent
        Me.lblStepThree.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepThree.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStepThree.Location = New System.Drawing.Point(318, 203)
        Me.lblStepThree.Name = "lblStepThree"
        Me.lblStepThree.Size = New System.Drawing.Size(200, 14)
        Me.lblStepThree.TabIndex = 138
        Me.lblStepThree.Text = "个人中心 > 分享赚钱 > 获得邀请码"
        '
        'frmJinbaoCourse
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(530, 355)
        Me.Controls.Add(Me.Label2)
        Me.Controls.Add(Me.lblStepThree)
        Me.Controls.Add(Me.lblStepTwo)
        Me.Controls.Add(Me.lblstepOne)
        Me.Controls.Add(Me.btnStep3)
        Me.Controls.Add(Me.btnStep1)
        Me.Controls.Add(Me.btnStep2)
        Me.Controls.Add(Me.btnInstall)
        Me.Controls.Add(Me.pic_iconLarge)
        Me.Controls.Add(Me.btn_close)
        Me.Name = "frmJinbaoCourse"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,55"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleFont = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "如何获取邀请码"
        CType(Me.pic_iconLarge, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents pic_iconLarge As iTong.Components.tbPictureBox
    Friend WithEvents btnInstall As iTong.Components.tbButton
    Friend WithEvents btnStep2 As iTong.Components.tbButton
    Friend WithEvents btnStep3 As iTong.Components.tbButton
    Friend WithEvents btnStep1 As iTong.Components.tbButton
    Friend WithEvents lblstepOne As System.Windows.Forms.Label
    Friend WithEvents lblStepTwo As System.Windows.Forms.Label
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents lblStepThree As System.Windows.Forms.Label
End Class
