﻿Imports ProtoBuf

Public Class QQDB

    Private ReadOnly mDBFile As String = String.Empty
    Private ReadOnly mQQFriendListFile As String = String.Empty
    Private ReadOnly mQQFriendFilePath As String = String.Empty
    Public ReadOnly Property DbFile() As String
        Get
            Return Me.mDBFile
        End Get
    End Property
    Public ReadOnly Property QQFriendListFile() As String
        Get
            Return Me.mQQFriendListFile
        End Get
    End Property

    Private mConn As Object = Nothing

#Region "--- 构造函数 ---"

    Private Sub New(ByVal DBFile As String, ByVal QQFriendListFile As String)
        Me.mDBFile = DBFile
        Me.mQQFriendListFile = QQFriendListFile
        Me.mQQFriendFilePath = Path.Combine(Path.GetDirectoryName(mQQFriendListFile), "outData.txt")
        Me.Reload()
        Me.ReadPlist()
    End Sub

    Public Sub Dispose()
        Try
            Me.CloseDB()

            instance = Nothing
        Catch
        End Try
    End Sub

#End Region

#Region "--- 单实例 ---"

    Private Shared instance As QQDB = Nothing
    Private Shared ReadOnly locker As New Object()
    Private Shared lstInstance As New Dictionary(Of String, QQDB)

    Public Shared Function GetInstance(ByVal DBFile As String, ByVal QQFriendListFile As String) As QQDB
        If lstInstance.ContainsKey(DBFile) AndAlso lstInstance(DBFile) IsNot Nothing AndAlso String.Compare(DBFile, lstInstance(DBFile).mDBFile, True) <> 0 Then
            lstInstance(DBFile).Dispose()
            lstInstance(DBFile) = Nothing
            lstInstance.Remove(DBFile)
        End If

        If instance Is Nothing Then
            SyncLock locker
                If lstInstance.ContainsKey(DBFile) Then
                    lstInstance(DBFile) = New QQDB(DBFile, QQFriendListFile)
                Else
                    lstInstance.Add(DBFile, New QQDB(DBFile, QQFriendListFile))
                End If

            End SyncLock
        End If

        Return lstInstance(DBFile)
    End Function

#End Region

#Region "--- 加载数据库 ---"

    Public Function Reload() As Boolean
        Dim result As Boolean = False
        Try
            If mConn IsNot Nothing AndAlso mConn.State = System.Data.ConnectionState.Open Then
                mConn.Close()
            End If

            If Not File.Exists(mDBFile & "-shm") Then
                mConn = SQLiteClass.CreateConnectionFromFile(mDBFile)
            End If

            If mConn Is Nothing Then
                mConn = SQLiteClass3.CreateConnectionFromFile(mDBFile)
            End If

            result = True

        Catch ex As Exception
            Common.LogException(ex.ToString, "Reload")
        End Try

        Return result
    End Function

    Public Function CloseDB() As Boolean
        Dim result As Boolean = False
        Try
            If mConn IsNot Nothing AndAlso mConn.State = System.Data.ConnectionState.Open Then
                mConn.Close()
            End If
            result = True
        Catch
        End Try

        Return result
    End Function

#End Region

#Region "--- 获取联系人列表 ---"

    Private Sub ReadPlist()
        Dim br As BinaryReader
        Dim bw As BinaryWriter
        Dim fileInfo As FileInfo = New FileInfo(mQQFriendListFile)
        Dim fileSize As Long = fileInfo.Length
        Try
            br = New BinaryReader(New FileStream(mQQFriendListFile, FileMode.Open))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ReadPlist")
            Return
        End Try

        Try
            Dim inData As Byte() = br.ReadBytes(fileSize)
            Dim key As String = "QQFriendListSave" '密钥
            Dim keyData As Byte() = Encoding.ASCII.GetBytes(key.ToCharArray())
            Dim qqCrypter As QQCrypt = New QQCrypt()
            Dim outData As Byte() = qqCrypter.QQ_Decrypt(inData, keyData) '解密文件
            br.Close()
            Try
                If File.Exists(Me.mQQFriendFilePath) Then
                    Try
                        File.Delete(Me.mQQFriendFilePath)
                    Catch
                    End Try
                End If
                bw = New BinaryWriter(New FileStream(Me.mQQFriendFilePath, FileMode.Create))
                bw.Write(outData)
                bw.Close()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "ReadPlist")
                Return
            End Try
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ReadPlist")
            Return
        End Try
    End Sub

    Public Function Get_Friend_Info() As Dictionary(Of String, List(Of QQFriendInfo))
        Dim dict As New Dictionary(Of String, List(Of QQFriendInfo))
        Try
            If Not File.Exists(Me.mQQFriendFilePath) Then
                Me.ReadPlist()
            End If
            If Not File.Exists(Me.mQQFriendFilePath) Then
                GoTo Do_Exit
            End If

            Dim fs As FileStream = New FileStream(Me.mQQFriendFilePath, FileMode.Open, FileAccess.Read)
            Dim array As Byte() = New Byte(fs.Length - 1) {}
            fs.Read(array, 0, array.Length)
            fs.Close()

            'protobuf 反序列化为对象  
            Dim result As QQLinkmanModel = Me.DeSerialize(array)
            dict = Me.ConvertQQLinkmanModelToDict(result)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_Friend_Info")
        End Try
Do_Exit:
        Return dict
    End Function

    Public Function GetSession() As List(Of String)
        Dim lstSession As New List(Of String)
        '联系人
        Try
            Dim pstrSQL As String = "Select * From tb_c2cTables"
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        Dim strTableName As String = Common.GetValue(Of String)(dr("uin"), String.Empty)

                        If Not String.IsNullOrEmpty(strTableName) Then
                            lstSession.Add(strTableName)
                        End If
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "GetSession_Chat1")
                    End Try
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSession_Chat")
        End Try

        'QQ群
        Try
            Dim strSql As String = "Select groupcode From tb_troop_recent"
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSql, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        Dim strTableName As String = Common.GetValue(Of String)(dr("groupcode"), String.Empty)

                        If Not String.IsNullOrEmpty(strTableName) Then
                            lstSession.Add(String.Format("tb_TroopMsg_{0}", strTableName))
                        End If
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "GetSession_Group1")
                    End Try
                Next
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSession_Group")
        End Try

        'QQ讨论组
        Try
            Dim strSql As String = "Select DiscussUin From tb_discuss_recent"
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSql, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        Dim strTableName As String = Common.GetValue(Of String)(dr("DiscussUin"), String.Empty)

                        If Not String.IsNullOrEmpty(strTableName) Then
                            lstSession.Add(String.Format("tb_discussGrp_{0}", strTableName))
                        End If
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "GetSession_Discuss1")
                    End Try
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSession_Discuss")
        End Try

        Return lstSession
    End Function

    Private Function ConvertQQLinkmanModelToDict(result As QQLinkmanModel) As Dictionary(Of String, List(Of QQFriendInfo))
        Dim dict As New Dictionary(Of String, List(Of QQFriendInfo))
        Try
            If result Is Nothing OrElse result.lstLinkmanGroup.Count = 0 Then
                GoTo Do_Exit
            End If

            For Each item As QQLinkmanGroupModel In result.lstLinkmanGroup
                Dim fInfo As QQFriendInfo = Nothing
                Dim lstFriend As New List(Of QQFriendInfo)
                If item.lstFriend Is Nothing Then
                    Continue For
                End If
                For Each QModel As QQFriendModel In item.lstFriend
                    fInfo = New QQFriendInfo()
                    fInfo.StrQQNum = QModel.strQQNum
                    fInfo.NickName = QModel.strNickname
                    fInfo.NoteName = QModel.strRemark
                    fInfo.UsrName = IIf(QModel.strRemark.Length = 0, QModel.strNickname, QModel.strRemark)
                    lstFriend.Add(fInfo)
                Next
                If Not dict.ContainsKey(item.strName) Then
                    dict.Add(item.strName, lstFriend)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ConvertQQLinkmanModelToDict")
        End Try
Do_Exit:
        Return dict
    End Function

#End Region

#Region "--- 获取群列表\讨论组 ---"

    Public Sub Get_Group_Info(ByRef dict As Dictionary(Of String, List(Of QQFriendInfo)))
        Try
            Dim lst As New List(Of QQFriendInfo)
            Dim info As QQFriendInfo = Nothing
            Dim pstrSQL As String = "Select * From tb_troop"
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

            '新版QQ获取群
            If dt Is Nothing OrElse dt.Rows.Count = 0 Then
                pstrSQL = "Select * From tb_troop_new"
                dt = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
            End If
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        Dim strNum As String = Common.GetValue(Of String)(dr("groupcode"), String.Empty)
                        Dim strName As String = Common.GetValue(Of String)(dr("GroupName"), String.Empty)
                        info = New QQFriendInfo()
                        info.StrQQNum = strNum
                        info.NickName = strName
                        info.StrChatRoomMem = "True"
                        info.IsGroup = True

                        lst.Add(info)
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "Get_Chat_Table")
                    End Try
                Next
            End If

            If lst.Count > 0 Then
                dict.Add("troop", lst)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_Group_Info")
        End Try
    End Sub

    Public Sub Get_Discuss_Info(ByRef dict As Dictionary(Of String, List(Of QQFriendInfo)))
        Try
            Dim lst As New List(Of QQFriendInfo)
            Dim info As QQFriendInfo = Nothing
            Dim pstrSQL As String = "Select * From tb_discussGrp_list"
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        Dim strNum As String = Common.GetValue(Of String)(dr("uin"), String.Empty)
                        Dim strName As String = Common.GetValue(Of String)(dr("Name"), String.Empty)
                        info = New QQFriendInfo()
                        info.StrQQNum = strNum
                        info.NickName = strName
                        info.StrChatRoomMem = "True"
                        info.IsDiscuss = True

                        lst.Add(info)
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "Get_Discuss_Info1")
                    End Try
                Next
            End If

            If lst.Count > 0 Then
                dict.Add("discuss", lst)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_Discuss_Info")
        End Try
    End Sub

#End Region

#Region "--- 获取聊天记录 ---"

    'Public Function Get_Chat_Info(ByVal strNum As String, ByVal isGroup As Boolean) As List(Of QQChatInfo)
    '    Dim lstInfos As New List(Of QQChatInfo)
    '    Try
    '        Dim strTableName As String = String.Format("tb_c2cMsg_{0}", strNum)
    '        Dim pstrSQL As String = String.Format("select * from ( select  * from {0} order by time desc ) a order by a.time", strTableName)
    '        If isGroup Then
    '            strTableName = String.Format("tb_TroopMsg_{0}", strNum)
    '            pstrSQL = String.Format("select * from ( select  nickName,msgId,strMsg,read,sMsgType,MsgTime,picUrl,SendUin from {0} order by MsgTime desc ) a order by a.MsgTime", strTableName)
    '        End If

    '        lstInfos = Me.ExecuteSQL(strNum, pstrSQL, isGroup)
    '    Catch ex As Exception

    '    End Try
    '    Return lstInfos
    'End Function

    'Public Function Get_Chat_Info(ByVal strNum As String, ByVal startIndex As Integer, ByVal count As Integer, ByVal isGroup As Boolean, Optional ByVal strSearchText As String = "") As List(Of QQChatInfo)
    '    Dim lstInfos As New List(Of QQChatInfo)
    '    Try
    '        Dim strTableName As String = String.Format("tb_c2cMsg_{0}", strNum)
    '        Dim strWhere As String = "where 1=1 "
    '        If strSearchText.Length > 0 Then
    '            If isGroup Then
    '                strWhere = String.Format(" where strMsg like '%{0}%' ", strSearchText)
    '            Else
    '                strWhere = String.Format(" where content like '%{0}%' ", strSearchText)
    '            End If
    '        End If
    '        Dim pstrSQL As String = String.Format("select * from ( select  * from {0} {3} order by time desc limit {1},{2}) a order by a.time", strTableName, startIndex, count, strWhere)
    '        If isGroup Then
    '            strTableName = String.Format("tb_TroopMsg_{0}", strNum)
    '            pstrSQL = String.Format("select * from ( select  nickName,msgId,strMsg,read,sMsgType,MsgTime,picUrl,SendUin from {0} {3} order by MsgTime desc limit {1},{2}) a order by a.MsgTime", strTableName, startIndex, count, strWhere)
    '        End If

    '        lstInfos = Me.ExecuteSQL(strNum, pstrSQL, isGroup)
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "Get_Chat_Info")
    '    End Try
    '    Return lstInfos
    'End Function

    Public Function Get_Chat_Info_ByTime_All(ByVal strNum As String, ByVal lstTime As List(Of DateTime), ByVal isGroup As Boolean, ByVal isDiscuss As Boolean) As List(Of QQChatInfo)
        Dim lstInfos As New List(Of QQChatInfo)
        Dim strWhere As String = ""
        Dim strTableName As String = ""
        Dim pstrSQL As String = ""
        If lstTime IsNot Nothing AndAlso lstTime.Count = 2 Then
            Dim longStartTime As Long = 0
            Dim longEndTime As Long = 0
            Try
                longStartTime = WeixinHelper.ConvertPcTimeToWeixin(lstTime(0).Date)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "Get_Chat_Info_ByTime_All1")
            End Try

            Try
                longEndTime = WeixinHelper.ConvertPcTimeToWeixin(lstTime(1).AddDays(1).Date)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "Get_Chat_Info_ByTime_All2")
            End Try

            If isGroup Then
                strTableName = String.Format("tb_TroopMsg_{0}", strNum)
                strWhere = String.Format(" and MsgTime >= {0} and MsgTime <={1} ", longStartTime, longEndTime)
                pstrSQL = String.Format("select * from ( select  nickName,msgId,strMsg,read,sMsgType,MsgTime,picUrl,SendUin from {0} where 1=1 {1} order by MsgTime ) a order by a.MsgTime", strTableName, strWhere)
            ElseIf isDiscuss Then
                strTableName = String.Format("tb_discussGrp_{0}", strNum)
                strWhere = String.Format(" and MsgTime >= {0} and MsgTime <={1} ", longStartTime, longEndTime)
                pstrSQL = String.Format("select * from ( select nickName,msgId,Msg,read,MsgType,MsgTime,picUrl,SendUin from {0} where 1=1 {1} order by MsgTime ) a order by a.MsgTime", strTableName, strWhere)
            Else
                strTableName = String.Format("tb_c2cMsg_{0}", strNum)
                strWhere = String.Format(" and time >= {0} and time <={1} ", longStartTime, longEndTime)
                pstrSQL = String.Format("select  * from {0} where 1=1 {1} order by time", strTableName, strWhere)
            End If
        End If
        lstInfos = Me.ExecuteSQL(strNum, pstrSQL, isGroup, isDiscuss)
        Return lstInfos
    End Function

    Public Function Get_Chat_Info_ByTime(ByVal strNum As String, ByVal endID As Integer, ByVal count As Integer, ByVal lstTime As List(Of DateTime), ByVal isGroup As Boolean, ByVal isDiscuss As Boolean) As List(Of QQChatInfo)
        Dim lstInfos As New List(Of QQChatInfo)
        Dim strWhere As String = ""
        Dim strTableName As String = ""
        Dim pstrSQL As String = ""
        If lstTime IsNot Nothing AndAlso lstTime.Count = 2 Then
            Dim longStartTime As Long = WeixinHelper.ConvertPcTimeToWeixin(lstTime(0).[Date])
            Dim longEndTime As Long = WeixinHelper.ConvertPcTimeToWeixin(lstTime(1).AddDays(1).[Date])
            If isGroup Then
                strTableName = String.Format("tb_TroopMsg_{0}", strNum)
                strWhere = String.Format(" and MsgTime >= {0} and MsgTime <={1} ", longStartTime, longEndTime)
            ElseIf isDiscuss Then
                strTableName = String.Format("tb_discussGrp_{0}", strNum)
                strWhere = String.Format(" and MsgTime >= {0} and MsgTime <={1} ", longStartTime, longEndTime)
            Else
                strTableName = String.Format("tb_c2cMsg_{0}", strNum)
                strWhere = String.Format(" and time >= {0} and time <={1} ", longStartTime, longEndTime)
            End If

            Dim longCreateTime As Long = GetMsgCreateTime(strNum, endID, isGroup, isDiscuss, strWhere)
            If longCreateTime = 0 And endID > 0 Then
                Common.LogException("查询时间出错", "Get_Chat_Info_ByTime")
                Return lstInfos
            End If

            If longCreateTime > 0 Then
                If isGroup OrElse isDiscuss Then
                    strWhere = strWhere & String.Format(" and MsgTime <{0} ", longCreateTime)
                Else
                    strWhere = strWhere & String.Format(" and time <{0} ", longCreateTime)
                End If
            End If

            If isGroup Then
                pstrSQL = String.Format("select * from (select nickName,msgId,strMsg,read,sMsgType,MsgTime,picUrl,SendUin from {0} where 1=1 {1} order by MsgTime desc limit 0,{2})a order by  a.MsgTime", strTableName, strWhere, count)
            ElseIf isDiscuss Then
                pstrSQL = String.Format("select * from (select nickName,msgId,Msg,read,MsgType,MsgTime,picUrl,SendUin from {0} where 1=1 {1} order by MsgTime desc limit 0,{2})a order by  a.MsgTime", strTableName, strWhere, count)
            Else
                pstrSQL = String.Format("select * from (select * from {0} where 1=1 {1} order by time desc limit 0,{2})a order by  a.time", strTableName, strWhere, count)
            End If
        End If
        lstInfos = Me.ExecuteSQL(strNum, pstrSQL, isGroup, isDiscuss)
        Return lstInfos
    End Function

    Private Function GetTableName(ByVal strNum As String, ByVal isGroup As Boolean, ByVal isDiscuss As Boolean)
        Dim strTableName As String = ""
        If isGroup Then
            strTableName = String.Format("tb_TroopMsg_{0}", strNum)
        ElseIf isDiscuss Then
            strTableName = String.Format("tb_discussGrp_{0}", strNum)
        Else
            strTableName = String.Format("tb_c2cMsg_{0}", strNum)
        End If
        Return strTableName
    End Function

    '获取（大于ID）查看上文消息
    Public Function Get_Chat_Info_ByWhere(ByVal strNum As String, ByVal endID As Integer, ByVal count As Integer, ByVal isGroup As Boolean, ByVal isDiscuss As Boolean, Optional ByVal strSearchText As String = "") As List(Of QQChatInfo)
        Dim lstInfos As New List(Of QQChatInfo)
        Dim strTableName As String = Me.GetTableName(strNum, isGroup, isDiscuss)
        Dim strWhere As String = ""

        If strSearchText.Length > 0 Then
            If isGroup Then
                strWhere = String.Format(" and strMsg like '%{0}%' ", strSearchText)
            ElseIf isDiscuss Then
                strWhere = String.Format(" and Msg like '%{0}%' ", strSearchText)
            Else
                strWhere = String.Format(" and content like '%{0}%' ", strSearchText)
            End If
        End If

        '先查出时间，然后根据时间进行分页
        Dim longCreateTime As Long = GetMsgCreateTime(strNum, endID, isGroup, isDiscuss, strWhere)
        If longCreateTime = 0 And endID > 0 Then
            Common.LogException("查询时间出错", "Get_Chat_Info_ByWhere")
            Return lstInfos
        End If

        If longCreateTime > 0 Then
            If isGroup OrElse isDiscuss Then
                strWhere = strWhere & String.Format(" and MsgTime <{0} ", longCreateTime)
            Else
                strWhere = strWhere & String.Format(" and time <{0} ", longCreateTime)
            End If
        End If

        Dim pstrSQL As String = String.Empty

        If isGroup Then
            pstrSQL = String.Format("select * from (select msgId,SendUin,MsgTime,sMsgType,strMsg,nickName,picUrl,read from {0} where 1=1 {2} order by MsgTime desc limit 0,{1})a order by  a.MsgTime", strTableName, count, strWhere)
        ElseIf isDiscuss Then
            pstrSQL = String.Format("select * from (select msgId,SendUin,MsgTime,MsgType,Msg,nickName,picUrl,read from {0} where 1=1 {2} order by MsgTime desc limit 0,{1})a order by  a.MsgTime", strTableName, count, strWhere)
        Else
            pstrSQL = String.Format("select * from (select  * from {0} where 1=1 {2} order by time desc limit 0,{1})a order by  a.time", strTableName, count, strWhere)
        End If

        lstInfos = Me.ExecuteSQL(strNum, pstrSQL, isGroup, isDiscuss)

        Return lstInfos
    End Function

    Private Function GetMsgCreateTime(ByVal strNum As String, ByVal endID As Integer, ByVal isGroup As Boolean, ByVal isDiscuss As Boolean, ByVal strWhere As String) As Long
        Dim longCreateTime As Long = 0

        If endID <= 0 Then
            Return longCreateTime
        End If

        Dim strTableName As String = Me.GetTableName(strNum, isGroup, isDiscuss)
      
        '先查出时间，然后根据时间进行分页
        Dim pstrSQL As String = String.Format("select time from {0} where msgId={1} {2} ", strTableName, endID, strWhere)
        If isGroup OrElse isDiscuss Then
            pstrSQL = String.Format("select MsgTime from {0} where msgId={1} {2} ", strTableName, endID, strWhere)
        End If
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    If isGroup OrElse isDiscuss Then
                        longCreateTime = Common.GetValue(Of Long)(dr("MsgTime"), 0)
                    Else
                        longCreateTime = Common.GetValue(Of Long)(dr("time"), 0)
                    End If
                    Exit For
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "GetMsgCreateTime")
                End Try
            Next
        End If
        Return longCreateTime
    End Function

    Private Function ExecuteSQL(ByVal strNum As String, ByVal pstrSQL As String, ByVal isGroup As Boolean, ByVal isDiscuss As Boolean) As List(Of QQChatInfo)
        Dim lstInfos As New List(Of QQChatInfo)
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim info As QQChatInfo = Nothing
                    If isGroup Then
                        info = ConvertTroopMsgToChatInfo(strNum, dr)
                    ElseIf isDiscuss Then
                        info = ConvertDiscussMsgToChatInfo(strNum, dr)
                    Else
                        info = ConvertC2CMsgToChatInfo(dr)
                    End If
                    If info IsNot Nothing Then
                        lstInfos.Add(info)
                    End If
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "ExecuteSQL")
                End Try
            Next
        End If

        Return lstInfos
    End Function

    Private Function ConvertC2CMsgToChatInfo(ByVal dr As DataRow) As QQChatInfo
        Dim info As New QQChatInfo()
        Try
            info.StrUsrName = Common.GetValue(Of String)(dr("uin"), String.Empty)
            info.IntID = Common.GetValue(Of Integer)(dr("msgId"), 0)
            info.StrMessage = Common.GetValue(Of String)(dr("content"), String.Empty)
            info.IntStatus = Common.GetValue(Of Integer)(dr("read"), 0)
            'info.ImgStatus = Common.GetValue(Of Integer)(dr("ImgStatus"), 0)
            Dim intType As Integer = Common.GetValue(Of Integer)(dr("type"), 0)
            info.SCType = ConvertSCType(intType)
            Dim intFlag As Integer = Common.GetValue(Of Integer)(dr("flag"), 0)
            If intFlag = 0 Then
                info.IntDes = 1
            Else
                info.IntDes = 0
            End If

            Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("time"), 0)
            info.IntCreateTime = longCreateTime
            info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
            info.StrPicUrl = Common.GetValue(Of String)(dr("picUrl"), String.Empty)
            info.StrSendUin = Common.GetValue(Of String)(dr("uin"), String.Empty)
            If intType = 311 AndAlso (info.StrMessage.Contains("""redtype"":1") OrElse info.StrMessage.Contains("""redtype"":2")) Then
                info.StrMessage = "[QQ红包]"
            ElseIf intType = 311 AndAlso info.StrMessage.Contains("""redtype"":0") Then
                info.StrMessage = "[QQ转账]"
            End If
            '如果消息记录显示是109且没有数据的就不显示出来
            If intType = 109 AndAlso String.IsNullOrEmpty(info.StrMessage) Then
                info = Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ConvertC2CMsgToChatInfo")
        End Try
        Return info
    End Function

    Private Function ConvertSCType(intType As Integer) As SociaChatType
        Dim scType As SociaChatType = SociaChatType.Text
        Select Case intType
            Case 0
                scType = SociaChatType.Text
            Case 1, 11001
                scType = SociaChatType.Picture
            Case 3, 11003
                scType = SociaChatType.Voice
            Case 0, 351
                scType = SociaChatType.SpecialExpression
            Case 150, 15
                scType = SociaChatType.VideoConnected
            Case 4
                scType = SociaChatType.File
            Case 141
                scType = SociaChatType.webpage
            Case 181
                scType = SociaChatType.SmallVideo
            Case 332, 352
                scType = SociaChatType.SystemMessages

        End Select
        Return scType
    End Function

    Private Function ConvertTroopMsgToChatInfo(ByVal strNum As String, ByVal dr As DataRow) As QQChatInfo
        Dim info As New QQChatInfo()
        Try
            info.StrUsrName = strNum
            info.StrNickName = Common.GetValue(Of String)(dr("nickName"), String.Empty)
            info.IntID = Common.GetValue(Of Integer)(dr("msgId"), 0)
            info.StrMessage = Common.GetValue(Of String)(dr("strMsg"), String.Empty)
            info.IntStatus = Common.GetValue(Of Integer)(dr("read"), 0)
            'info.ImgStatus = Common.GetValue(Of Integer)(dr("ImgStatus"), 0)
            Dim intType As Integer = Common.GetValue(Of Integer)(dr("sMsgType"), 0)
            info.SCType = ConvertSCType(intType)
            'info.Des = Common.GetValue(Of Integer)(dr("Des"), 0) '0发送；1接收
            Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("MsgTime"), 0)
            info.IntCreateTime = longCreateTime
            info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
            info.StrPicUrl = Common.GetValue(Of String)(dr("picUrl"), String.Empty)
            info.StrSendUin = Common.GetValue(Of String)(dr("SendUin"), String.Empty)
            If intType = 311 AndAlso (info.StrMessage.Contains("""redtype"":1") OrElse info.StrMessage.Contains("""redtype"":2")) Then
                info.StrMessage = "[QQ红包]"
            ElseIf intType = 311 AndAlso info.StrMessage.Contains("""redtype"":0") Then
                info.StrMessage = "[QQ转账]"
            End If
            '如果消息记录显示是109且没有数据的就不显示出来
            If intType = 109 AndAlso String.IsNullOrEmpty(info.StrMessage) Then
                info = Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ConvertTroopMsgToChatInfo")
        End Try
        Return info
    End Function

    Private Function ConvertDiscussMsgToChatInfo(ByVal strNum As String, ByVal dr As DataRow) As QQChatInfo
        Dim info As New QQChatInfo()
        Try
            info.StrUsrName = strNum
            info.StrNickName = Common.GetValue(Of String)(dr("nickName"), String.Empty)
            info.IntID = Common.GetValue(Of Integer)(dr("msgId"), 0)
            info.StrMessage = Common.GetValue(Of String)(dr("Msg"), String.Empty)
            info.IntStatus = Common.GetValue(Of Integer)(dr("read"), 0)
            'info.ImgStatus = Common.GetValue(Of Integer)(dr("ImgStatus"), 0)
            Dim intType As Integer = Common.GetValue(Of Integer)(dr("MsgType"), 0)
            info.SCType = ConvertSCType(intType)
            'info.Des = Common.GetValue(Of Integer)(dr("Des"), 0) '0发送；1接收
            Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("MsgTime"), 0)
            info.IntCreateTime = longCreateTime
            info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
            info.StrPicUrl = Common.GetValue(Of String)(dr("picUrl"), String.Empty)
            info.StrSendUin = Common.GetValue(Of String)(dr("SendUin"), String.Empty)
            If intType = 311 AndAlso (info.StrMessage.Contains("""redtype"":1") OrElse info.StrMessage.Contains("""redtype"":2")) Then
                info.StrMessage = "[QQ红包]"
            ElseIf intType = 311 AndAlso info.StrMessage.Contains("""redtype"":0") Then
                info.StrMessage = "[QQ转账]"
            End If
            '如果消息记录显示是109且没有数据的就不显示出来
            If intType = 109 AndAlso String.IsNullOrEmpty(info.StrMessage) Then
                info = Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ConvertDiscussMsgToChatInfo")
        End Try
        Return info
    End Function

#End Region

#Region "--- 查找功能 ---"

    Public Function SearchFriend(ByVal dictAllFriend As Dictionary(Of String, QQFriendInfo), ByVal strSearchText As String) As Dictionary(Of String, QQFriendInfo)
        Dim dictResultFriend As New Dictionary(Of String, QQFriendInfo)
        If dictAllFriend Is Nothing OrElse dictAllFriend.Count <= 0 Then
            Return dictResultFriend
        End If

        Try
            '名字有带关键字的记录
            For Each item As String In dictAllFriend.Keys
                Dim info As QQFriendInfo = dictAllFriend(item)
                If (info.NickName IsNot Nothing AndAlso info.NickName.Contains(strSearchText)) OrElse _
                   (info.NoteName IsNot Nothing AndAlso info.NoteName.Contains(strSearchText)) OrElse _
                   (info.StrGroupName IsNot Nothing AndAlso info.StrGroupName.Contains(strSearchText)) OrElse _
                   (info.UsrName IsNot Nothing AndAlso info.UsrName.Contains(strSearchText)) Then
                    dictResultFriend.Add(item, info)
                    info.SearchTime = Nothing
                    info.SearchText = ""
                End If
            Next
            Application.DoEvents()
            '聊天内容有带关键字的
            Dim lstKeys As List(Of String) = Me.SearchFriendChat(dictAllFriend, strSearchText)
            If lstKeys Is Nothing OrElse lstKeys.Count <= 0 Then
                Return dictResultFriend
            End If

            For Each itemFriend As QQFriendInfo In dictAllFriend.Values
                Dim isExist As Boolean = False
                For Each itemSearch As String In lstKeys
                    If itemSearch.Length AndAlso itemFriend IsNot Nothing AndAlso itemFriend.StrQQNum.Length > 0 AndAlso itemSearch = itemFriend.StrQQNum Then
                        isExist = True
                        itemFriend.SearchText = strSearchText
                        itemFriend.SearchTime = Nothing
                        Exit For
                    End If
                Next
                If isExist AndAlso Not dictResultFriend.ContainsKey(itemFriend.StrQQNum) Then
                    '内容包含关键字的在查看的时候就只显示关键字的记录
                    dictResultFriend.Add(itemFriend.StrQQNum, itemFriend)
                End If
            Next


            'For Each item As String In lstKeys
            '    If item.Length <= 0 OrElse Not dictAllFriend.ContainsKey(item) Then
            '        Continue For
            '    End If
            '    Dim info As QQFriendInfo = dictAllFriend(item)
            '    info.SearchText = strSearchText
            '    info.SearchTime = Nothing
            '    If Not dictResultFriend.ContainsKey(item) Then
            '        '内容包含关键字的在查看的时候就只显示关键字的记录
            '        dictResultFriend.Add(item, info)
            '    End If
            'Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "QQDB_SearchFriend")
        End Try
        Return dictResultFriend
    End Function

    Private Function SearchFriendChat(ByVal dictAllFriend As Dictionary(Of String, QQFriendInfo), ByVal strSearchText As String) As List(Of String)
        Dim lstReturn As New List(Of String)
        Dim dictTemp As New Dictionary(Of String, QQFriendInfo)
        For Each item As String In dictAllFriend.Keys
            Dim info As QQFriendInfo = dictAllFriend(item)
            If info Is Nothing OrElse info.DTLastSendTime = DateTime.MinValue Then
                Continue For
            End If
            dictTemp.Add(item, info)
            '300个对话查一次
            If dictTemp.Count >= 300 Then
                lstReturn.AddRange(Me.SearchFriendChatExecuteSQL(dictTemp, strSearchText))
                dictTemp.Clear()
            End If
        Next

        If dictTemp.Count > 0 Then
            lstReturn.AddRange(Me.SearchFriendChatExecuteSQL(dictTemp, strSearchText))
        End If
        Return lstReturn
    End Function

    Private Function SearchFriendChatExecuteSQL(ByVal dictTemp As Dictionary(Of String, QQFriendInfo), ByVal strSearchText As String) As List(Of String)
        Dim lstReturn As New List(Of String)
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(Me.SearchFriendChatSQL(dictTemp, strSearchText), mConn)
        Dim longCreateTime As Long = 0
        If dt Is Nothing OrElse dt.Rows.Count <= 0 Then
            Return lstReturn
        End If
        For Each dr As DataRow In dt.Rows
            Dim strKey As String = Common.GetValue(Of String)(dr("uin"), String.Empty)
            If strKey.Length <= 0 Then
                Continue For
            End If
            lstReturn.Add(strKey)
        Next
        Return lstReturn
    End Function

    Private Function SearchFriendChatSQL(ByVal dictAllFriend As Dictionary(Of String, QQFriendInfo), ByVal strSearchText As String) As String
        Dim strSql As String = ""
        Dim strTableName As String = ""

        For Each item As QQFriendInfo In dictAllFriend.Values
            If item.DTLastSendTime = DateTime.MinValue Then
                Continue For
            End If
            Dim pstrSQL As String = ""
            If item.IsGroup Then
                strTableName = String.Format("tb_TroopMsg_{0}", item.StrQQNum)
                pstrSQL = String.Format("select DISTINCT {2} as uin from {0} where strMsg like '%{1}%' ", strTableName, strSearchText, item.StrQQNum)
            ElseIf item.IsDiscuss Then
                strTableName = String.Format("tb_discussGrp_{0}", item.StrQQNum)
                pstrSQL = String.Format("select DISTINCT {2} as uin from {0} where Msg like '%{1}%' ", strTableName, strSearchText, item.StrQQNum)
            Else
                strTableName = String.Format("tb_c2cMsg_{0}", item.StrQQNum)
                pstrSQL = String.Format("select DISTINCT uin from {0} where content like '%{1}%' ", strTableName, strSearchText)
            End If

            If strSql.Length > 0 Then
                strSql = strSql & vbCrLf & "union" & vbCrLf & pstrSQL
            Else
                strSql = pstrSQL
            End If
        Next
        Return strSql
    End Function

#End Region

#Region "--- protobuf ---"

    ' 将消息序列化为二进制的方法  
    ' < param name="model">要序列化的对象< /param>  
    Public Function Serialize(model As QQLinkmanModel) As Byte()
        Try
            '涉及格式转换，需要用到流，将二进制序列化到流中  
            Using ms As New MemoryStream()
                '使用ProtoBuf工具的序列化方法  
                ProtoBuf.Serializer.Serialize(Of QQLinkmanModel)(ms, model)
                '定义二级制数组，保存序列化后的结果  
                Dim result As Byte() = New Byte(ms.Length - 1) {}
                '将流的位置设为0，起始点  
                ms.Position = 0
                '将流中的内容读取到二进制数组中  
                ms.Read(result, 0, result.Length)
                Return result
            End Using
        Catch ex As Exception
            Common.LogException("反序列化失败: " + ex.ToString(), "Serialize")
            Return Nothing
        End Try
    End Function

    ' 将收到的消息反序列化成对象  
    ' < returns>The serialize.< /returns>  
    ' < param name="msg">收到的消息.</param>  
    Public Function DeSerialize(msg As Byte()) As QQLinkmanModel
        Try
            Using ms As New MemoryStream()
                '将消息写入流中  
                ms.Write(msg, 0, msg.Length)
                '将流的位置归0  
                ms.Position = 0
                '使用工具反序列化对象  
                Dim result As QQLinkmanModel = ProtoBuf.Serializer.Deserialize(Of QQLinkmanModel)(ms)
                Return result
            End Using
        Catch ex As Exception
            Common.LogException("反序列化失败: " + ex.ToString(), "DeSerialize")
            Return Nothing
        End Try
    End Function

#End Region

#Region "--- 获取不在好友列表里面的昵称 ---"

    Public Function GetNickName(ByVal strQQId) As String
        Dim strNickName As String = String.Empty
        Dim strSql As String = String.Format("SELECT Nick FROM tb_TroopMem WHERE MemUin={0}", strQQId)
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSql, mConn)
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    strNickName = Common.GetValue(Of String)(dr("Nick"), String.Empty)
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "QQDB_GetNickName")
                End Try
            Next
        End If

        If String.IsNullOrEmpty(strNickName) Then
            strSql = String.Format("SELECT interemark FROM tb_discussGrp_member WHERE uin={0}", strQQId)
            dt = SQLiteClass.ExecuteSQL(strSql, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        strNickName = Common.GetValue(Of String)(dr("interemark"), String.Empty)
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "QQDB_GetNickName1")
                    End Try
                Next
            End If
        End If

        If String.IsNullOrEmpty(strNickName) Then
            strNickName = strQQId
        End If
        Return strNickName
    End Function

#End Region

End Class

<ProtoContract> _
Public Class QQLinkmanModel
    '添加特性，表示该字段可以被序列化，1可以理解为下标  
    <ProtoMember(2)> _
    Public lstLinkmanGroup As List(Of QQLinkmanGroupModel)
    <ProtoMember(3)> _
    Public strMainQQNum As String

End Class

<ProtoContract> _
Public Class QQLinkmanGroupModel
    '添加特性，表示该字段可以被序列化，1可以理解为下标  
    <ProtoMember(1)> _
    Public strIndex As String
    <ProtoMember(2)> _
    Public strName As String
    <ProtoMember(3)> _
    Public strCount As String
    <ProtoMember(8)> _
    Public lstFriend As List(Of QQFriendModel)
End Class

<ProtoContract> _
Public Class QQFriendModel
    '添加特性，表示该字段可以被序列化，1可以理解为下标  
    <ProtoMember(1)> _
    Public strQQNum As String
    <ProtoMember(2)> _
    Public strRemark As String
    <ProtoMember(26)> _
    Public strNickname As String
End Class
