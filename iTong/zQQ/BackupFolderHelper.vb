﻿Public Class BackupFolderHelper
    Implements IDisposable

    Private mLanguage As LanguageInterface = Nothing
    Private mDevice As iPhoneDevice = Nothing
    Private mApplication As IApplication = Nothing

    Public mPathContainer As String = String.Empty
    Public mUIFileSharingEnabled As Boolean = True
    Public mAfcHandler As System.IntPtr = IntPtr.Zero
    Public mListDir As New List(Of String)
    Public mStrSKU As String = "com.tencent.mqq"

#Region "--- 单例 ---"
    Private Shared mDictInstances As New Dictionary(Of String, BackupFolderHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New(ByVal app As IApplication, ByVal dev As iPhoneDevice)
        Me.mLanguage = LanguageInterface.Instance()
        Me.mDevice = dev
        Me.mApplication = app
    End Sub

    Private Shared mHelper As BackupFolderHelper = Nothing

    Public Shared Function GetInstance(ByVal app As IApplication, ByVal dev As iPhoneDevice) As BackupFolderHelper
        Dim helper As BackupFolderHelper = Nothing
        Dim blnRetry As Boolean = False

        SyncLock _lockGetInstance
DO_RETRY:
            Try
                If dev Is Nothing Then
                    If Not mDictInstances.ContainsKey(NoDevcieIdentifier) Then
                        mDictInstances.Add(NoDevcieIdentifier, New BackupFolderHelper(dev, app))
                    End If
                    helper = mDictInstances.Item(NoDevcieIdentifier)

                Else
                    If mDictInstances.ContainsKey(dev.Identifier) Then
                        If mDictInstances(dev.Identifier).mDevice.IsConnected = False Then
                            mDictInstances.Remove(dev.Identifier)
                            mDictInstances.Add(dev.Identifier, New BackupFolderHelper(dev, app))
                        End If
                    Else
                        SyncLock _lockGetInstance
                            If Not mDictInstances.ContainsKey(dev.Identifier) Then
                                mDictInstances.Add(dev.Identifier, New BackupFolderHelper(dev, app))
                            End If
                        End SyncLock
                    End If
                    helper = mDictInstances(dev.Identifier)
                End If
            Catch ex As Exception
                If Not blnRetry Then
                    blnRetry = True
                    GoTo DO_RETRY
                End If
            End Try
        End SyncLock
        Return helper
    End Function

#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
                mDictInstances.Clear()

            End If
        End If
        disposedValue = True
    End Sub

#End Region

    Public Function Exists(ByVal strPath As String, ByVal strPathNew As String) As Boolean
        If Me.CheckOpenByAfc2() Then
            Return Me.mDevice.Exists(Me.mDevice.DefaultAfcHandle, Me.mPathContainer & strPath)

        ElseIf Me.mUIFileSharingEnabled Then
            Return Me.mDevice.Exists(Me.mAfcHandler, strPath)

        Else
            'Dim strPathNew As String = FormatPath(strPath)
            If File.Exists(strPathNew) Then
                Return True
            ElseIf Directory.Exists(strPathNew) Then
                Return True
            Else
                Dim strNewPath As String = String.Format("/var/mobile/Applications/{1}/{0}/", strPath.Trim("/"c), mStrSKU)
                If Me.mListDir.Contains(strNewPath) Then
                    Return True
                Else
                    Return False
                End If
            End If
        End If
    End Function

    Public Function CheckOpenByAfc2() As Boolean
        Return Me.mDevice IsNot Nothing AndAlso Me.mDevice.Jailbreaked AndAlso Me.mDevice.VersionNumber >= 830
    End Function

    Public Function GetDBTimeOnPhone(ByVal strDBOnPhone As String, ByVal strPathNew As String)
        Dim dateDBPhone As String = String.Empty
        Dim info As iPhoneFileInfo = Nothing

        Me.IsDirectory(strDBOnPhone, info, strPathNew)

        If info IsNot Nothing Then
            dateDBPhone = info.st_mtime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))
        End If

        Return dateDBPhone
    End Function

    Public Function IsDirectory(ByVal strPath As String, ByRef e As iPhoneFileInfo, ByVal strPathNew As String) As Boolean
        If Me.CheckOpenByAfc2() Then
            Return Me.mDevice.IsDirectory(Me.mDevice.DefaultAfcHandle, Me.mPathContainer & strPath, e)

        ElseIf Me.mUIFileSharingEnabled Then
            Return Me.mDevice.IsDirectory(Me.mAfcHandler, strPath, e)

        Else
            'Dim strPathNew As String = FormatPath(strPath)
            If File.Exists(strPathNew) Then
                e = New iPhoneFileInfo(New FileInfo(strPathNew))
                Return True

            ElseIf Directory.Exists(strPathNew) Then
                e = New iPhoneFileInfo(New DirectoryInfo(strPathNew))
                Return True
            Else
                e = Nothing
                Return False
            End If
        End If
    End Function

    Public Function DownFromPhone(ByVal strFileOnPhone As String, ByVal strFileOnPC As String, ByVal strPathNew As String) As Boolean
        Dim blnResult As Boolean = False

        If File.Exists(strFileOnPC) Then
            Dim infoPhone As iPhoneFileInfo = Nothing
            Dim infoPC As FileInfo = New FileInfo(strFileOnPC)

            'Dim strPathNew As String = FormatPath(strFileOnPhone)
            Me.IsDirectory(strFileOnPhone, infoPhone, strPathNew)
            If infoPhone IsNot Nothing Then
                If infoPC.Length = infoPhone.st_size Then
                    blnResult = True
                Else
                    blnResult = Me.DownFromPhoneEx(strFileOnPhone, strFileOnPC, strPathNew)
                End If
            End If
        Else
            blnResult = Me.DownFromPhoneEx(strFileOnPhone, strFileOnPC, strPathNew)
        End If

        Return blnResult
    End Function

    Public Function DownFromPhoneEx(ByVal strFileOnPhone As String, ByVal strFileOnPC As String, ByVal strPath As String) As Boolean
        If Me.CheckOpenByAfc2() Then
            Return Me.mDevice.DownFromPhone(Me.mDevice.DefaultAfcHandle, Me.mPathContainer & strFileOnPhone, strFileOnPC)

        ElseIf Me.mUIFileSharingEnabled Then
            'Common.LogException(String.Format("1.Download weixin files {0}  {1}   {2}", Me.mAfcHandler, strFileOnPhone, strFileOnPC))
            Return Me.mDevice.DownFromPhone(Me.mAfcHandler, strFileOnPhone, strFileOnPC)
        Else
            Dim blnResult As Boolean = False

            Try
                'Dim strPath As String = FormatPath(strFileOnPhone)
                'Common.LogException(String.Format("2.Download weixin files {0}  {1}   {2}", strPath, strFileOnPhone, strFileOnPC))
                If File.Exists(strPath) Then
                    File.Copy(strPath, strFileOnPC, True)
                    blnResult = True
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmWeixin.DownFromPhoneEx")
            End Try

            Return blnResult
        End If
    End Function


#Region "--- 公共方法 ---"

    Public Shared Function CheckInFolder(ByVal dirPath As String, ByVal subPath As String) As Boolean
        Dim isSubPath As String = False
        Dim strNewPath As String = subPath.TrimEnd("/").Replace(dirPath.TrimEnd("/") & "/", "")
        If Not strNewPath.Contains("/") Then
            isSubPath = True
        End If
        Return isSubPath
    End Function

    Public Shared Function IsFolder(ByVal record As MBFileRecord)
        Dim blnIsFolder As Boolean = False
        If (record.Mode = 16877 OrElse record.Mode = 16895 OrElse record.Mode = 16895 OrElse record.Mode = 16872 OrElse record.Mode = 16832 OrElse record.Mode = 16841) AndAlso record.FileLength = 0 Then
            blnIsFolder = True
        End If
        Return blnIsFolder
    End Function

    Public Shared Function IsNum(ByVal strValue As String) As Boolean
        Try
            For i As Integer = 0 To strValue.Length - 1
                Dim tempByte As Byte = Convert.ToByte(strValue(i))
                If (tempByte < 48) OrElse (tempByte > 57) Then
                    Return False
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "BackupFolderHelper_IsNum")
        End Try
        Return True
    End Function

#End Region

End Class

