﻿Imports System.Threading
Imports System.Text.RegularExpressions
Imports System.Web

Public Class frmQQ

    Private mHelper As IMExportHelper
    Private dgvFriend As tbDataGridViewEx = Nothing
    Private mDefaultHeadIcon As Image = Nothing

    Private mIsFromBackup As Boolean = False
    Private mStrBackupFolder As String = ""

    Private mLstMBFileRecord As List(Of MBFileRecord)
    Private mQQSKU As String = "com.tencent.mqq"
    Private mUIFileSharingEnabled As Boolean = True

    Private mDicQQAccountPath As New Dictionary(Of String, String)
    Private mListDir As New List(Of String)
    Protected mDictSha1 As New Dictionary(Of String, String)

    Private mCurrentUserName As String = ""
    Private mCurrentUserNum As String = ""
    Private mIsChangeUser As Boolean = False

    Private mPathOnPhone As String = ""

    Private mBackupFolderHelper As BackupFolderHelper
    Private mQQBackupCache As String = ""

    Private mPathQQOnPC_Temp As String = ""
    Private mPathQQOnPC_Cache As String = ""

    Private mDictFriendInfo As Dictionary(Of String, List(Of QQFriendInfo))                     '所有联系人表（分组,FriendInfo）
    Private mDictFriendInfoEx As Dictionary(Of String, QQFriendInfo)                     '所有联系人表（分组,FriendInfo）

    Private mQQDB As QQDB
    Private mQQHelper As QQHelper

    Private mLstChatTable As List(Of String)
    Private mAppIconSize As New Size(44, 44)
    Private mGraphic As Graphics = Nothing

    Private mIsSelectChageUser As Boolean = False
    Private mIsSelect As Boolean = False

    Private mLstCurrentInfos As New List(Of QQChatInfo)
    Private mLstCurrentChatInfo As New List(Of QQChatInfo)                             '当前web页展示的所有聊天信息（ChatInfo）
    Private mLstSearchCurrentInfos As New List(Of QQChatInfo)
    Private mBlnReloadFriendCompleted As Boolean = False
    Private mDefaultChatCount As Integer = 30
    Private mThreadGetChat As Thread = Nothing

    Private mIsWin7 As Boolean = False
    Private mPlayer As IPluginPlayer
    Private mRegular As String = String.Empty

    Private mThreadLoadHeadIcon As Thread = Nothing
    Private mDictDelChatInfo As New Dictionary(Of String, List(Of QQChatInfo))          '被删除的聊天信息
    Private mDictDelFriendInfo As Dictionary(Of String, QQFriendInfo) = New Dictionary(Of String, QQFriendInfo) '被删除的聊天信息 对应的联系人
    Private mBackupDeviceUDID As String = ""
    Private mAfcHandler As System.IntPtr = IntPtr.Zero
    Private mPathContainer As String = String.Empty
    Private mBackInfo As BackupInfo

    Private mSelectAllClick As Boolean = False
    Private mIsAddRow As Boolean = False

    Private mIsFromQQMain As Boolean = False
    Private mChargeHelper As ChargeHelper = Nothing
    Private mTDFindData As Thread = Nothing
    Private mFQQHelper As FindQQDataHelper = Nothing
    Private mStrDBQQOnPC As String = ""

    Private mShowTime As Integer = 0
    Private tmrShowCount As New System.Timers.Timer(1000)

    Public Property IsFromQQMain() As Boolean
        Get
            Return Me.mIsFromQQMain
        End Get
        Set(ByVal value As Boolean)
            Me.mIsFromQQMain = value

            If value Then
                Me.tlpQQ.Dock = DockStyle.Fill
            Else
                Me.tlpQQ.Dock = DockStyle.None
            End If
        End Set
    End Property

    Private mCurrentIdentify As String = ""
    Private mNewBackup As Boolean = False
    Private mBackupHelper As BackupHelper = Nothing
    Private mBackupMediaSucceed As Boolean = False       '有的设备没办法备份出媒体数据



#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice, ByVal blnNewBackup As Boolean)
        Me.New(application, device, False, "")
        Me.mDevice = device
        Me.mNewBackup = blnNewBackup
    End Sub

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice, ByVal isFromBackup As Boolean, ByVal strBackupFolder As String)

        MyBase.New(application, device)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.Weixin
        Me.tbAutoSetFormSize = True
        Me.mDefaultHeadIcon = My.Resources.weixin_icon_default.Clone
        Me.Size = New Size(970, 650)
        Me.Icon = My.Resources.iTong

        Me.mIsFromBackup = isFromBackup
        Me.mStrBackupFolder = strBackupFolder

        Try
            If Me.mIsFromBackup Then
                Me.mCurrentIdentify = New BackupInfo(Me.mStrBackupFolder).Identifier
            Else
                Me.mCurrentIdentify = Me.mDevice.Identifier
            End If
        Catch ex As Exception
        End Try

    End Sub

    Private Sub InitDataGridView()
        Me.dgvFriend = New tbDataGridViewEx
        With Me.dgvFriend
            .SuspendLayout()

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 58
            .ColumnHeadersVisible = False
            .tbShowNoData = False
            .MultiSelect = True
            .RowsDefaultCellStyle.BackColor = Color.White
            .AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(251, 251, 251)
            .RowsDefaultCellStyle.SelectionBackColor = Color.FromArgb(226, 244, 251)
            .BackgroundColor = Color.White
            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            '1、用户名（key）
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colBlank", "", 15, False, False, DataGridViewContentAlignment.MiddleLeft, True))

            '2、昵称（显示）
            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Main.Button.Contact"), _
                                                                                                                                                                      165, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            '设置样式
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.Font = Common.CreateFont(Me.Font.Name, 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black
            colName.DefaultCellStyle = cellStyle

            .Columns.Add(colName)
            '3、时间
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colTime", Me.Language.GetString("Music.Label.Duration"), _
                                                                   50, True, True, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colMD5", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))

            .ResumeLayout()
        End With

        RemoveHandler dgvFriend.SelectionChanged, AddressOf dgvFriend_SelectionChanged
        AddHandler dgvFriend.SelectionChanged, AddressOf dgvFriend_SelectionChanged

        Utility.AddForm2Panel(Me.dgvFriend, Me.pnlFriend)
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Dim para As New IMInstancePara()
        para.iPhone = Me.mDevice
        para.SocialType = ExportSocialType.QQ
        para.BackupFolder = Me.mStrBackupFolder
        para.ListRecord = Me.mLstMBFileRecord
        para.IsFullBackup = Me.mNewBackup

        Me.mHelper = IMExportHelper.Instance(para)

        Me.SetViewStyle(QQViewStyle.Loading)

        Me.InitDataGridView()

        AddHandler Me.tmrShowCount.Elapsed, AddressOf tmrShowCount_Elapsed

        Me.SetPnlCount(False)

        '加载事件放在线程里面就不会卡
        Dim thr As New Thread(New ThreadStart(AddressOf InitInThread))
        With thr
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub InitInThread()
        Me.mQQHelper = New QQHelper()
        Me.mBackupFolderHelper = New BackupFolderHelper(Me.mDevice)
        Me.mIsWin7 = (System.Environment.OSVersion.Version >= New Version("6.1"))
        Me.InitMusicPlayer()

        ChatWebPage.InitChatWebPage(False)

        Me.mRegular = ServerIniSetting.GetRegularForWeixin()

        Me.InitmChargeHelper()
        If Me.mIsFromBackup AndAlso Me.mBackInfo Is Nothing Then
            Me.mBackInfo = New BackupInfo(Me.mStrBackupFolder)
        End If
        Me.SetBackupInfo()

        Me.LoadFriend(True)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.tsmiExcelEx.Text = Me.Language.GetString("Backup.Button.ExportToExcel") '"导出为Excel文件"
        Me.tsmiTxtEx.Text = Me.Language.GetString("Backup.Button.ExportToTxt") '"导出为Txt文件"
        Me.tsmiHtml.Text = Me.Language.GetString("Backup.Button.ExportToHtml") '"导出为Html文件"

        Me.tsmiPicEx.Text = Me.Language.GetString("Weixin.Lable.ExportPic")                               '"导出图片"
        Me.tsmiAudioEx.Text = Me.Language.GetString("Weixin.Lable.ExportAudio")                           '"导出语音"
        Me.tsmiVideoEx.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideo")                      '"导出小视频"

        Dim fontF As Font = Common.CreateFont("Arial", 9.0!, FontStyle.Regular)
        Me.lblRegister.ColorTextList.Clear()
        Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("MainVip.Button.Register"), 1, System.Drawing.Color.FromArgb(0, 169, 255), fontF, False, True) '"激活"
        Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("WhatsApp.Message.RegisterWarn"), 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False) '"试用部分内容用*号代替，请点击右上角"
        Me.lblRegister.TextAlign = ContentAlignment.MiddleCenter

        Me.lblReadError.ColorTextList.Clear()
        Me.lblReadError.ColorTextList.AddItem(Me.Language.GetString("WhatsApp.Message.LoadErrorPlease"), 0, System.Drawing.Color.FromArgb(106, 106, 106), fontF, False) '读取失败，请
        Me.lblReadError.ColorTextList.AddItem(Me.Language.GetString("Common.Retry"), 1, System.Drawing.Color.FromArgb(0, 169, 255), fontF, False, True)  '重试
        Me.lblReadError.ColorTextList.AddItem(Me.Language.GetString("WhatsApp.Message.TryOtherRecoveryMode"), 2, System.Drawing.Color.FromArgb(106, 106, 106), fontF, False) '或尝试其他恢复模式
        AddHandler Me.lblReadError.TextClick, AddressOf lblReadError_TextClick
        AddHandler Me.lblReadError.SizeChange, AddressOf lblReadError_SizeChange
        Me.lblReadError.TextAlign = ContentAlignment.MiddleCenter

        Me.llblRefresh.Text = Me.Language.GetString("Common.Backup.BackupNewData")
        Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading")
        Me.lblLoadData.Text = Me.Language.GetString("App.Cell.Loading")
        Me.lblStartTime.Text = Me.Language.GetString("Contact.Label.Date") '"日期"

        Me.txtSearchChat.SearchTipText = Me.Language.GetString("Weixin.tbSearch.SearchTip") '"按回车搜索消息记录" 

        Me.cbxSelectAll.Text = Me.Language.GetString("Common.SelectAll")

        Me.btnExportSetting.Text = Me.Language.GetString("Weixin.Lable.ExportSetting") '导出设置
        Me.btnTimeScreen.Text = Me.Language.GetString("Weixin.Lable.TimeFilter") '按时间筛选
        Me.btnOK.Text = Me.Language.GetString("Common.OK")
        Me.btnExport.Text = Me.Language.GetString("WhatsApp.Label.ExportRecover")
        Me.btnDataRecoveryToPhone.Text = Me.Language.GetString("Weixin.Lable.RestoreToPhone") '恢复到手机

        Me.dtpStart.Value = CType("1970-01-02 00:00:01", DateTime)
        'Me.dtpStart.MaxDate = Now

        Me.dtpEnd.Value = CType("2999-01-01 00:00:01", DateTime)
        'Me.dtpEnd.MinDate = Now
        Me.btnTime.Text = Me.Language.GetString("Weixin.Item.All")

        Me.btnWeCahtUsers.Enabled = False
        Me.btnWeCahtUsers.Text = Me.Language.GetString("Download.Label.Loading")

        Dim strFindDataInfo As String = Me.Language.GetString("Weixin.Lable.ContactNames") '"左侧联系人昵称显示「红色」为有找到删除数据"

        Me.Selection(strFindDataInfo, rtxtFindDataInfo, "「", "」", Color.Red)

        Try
            RichTextBox1.Clear()
            RichTextBox1.SelectionAlignment = HorizontalAlignment.Left
            RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint8"))
            RichTextBox1.AppendText(vbCrLf)
            RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint2"))
            RichTextBox1.AppendText(vbCrLf)
            RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint9"))
        Catch
        End Try
    End Sub

    Private Sub SetBackupInfo()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf SetBackupInfo))
        Else
            If Me.mBackInfo IsNot Nothing Then
                Dim strDeviceName As String = Me.mBackInfo.DisplayName
                If strDeviceName.Length > 10 Then
                    strDeviceName = strDeviceName.Substring(0, 8) & "..."
                End If

                Me.lblFromBackupMsg.Text = String.Format(Me.Language.GetString("WhatsApp.Message.BackupInfo"), strDeviceName, Me.mBackInfo.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute))) '"当前数据是【{0}-{1}】备份文件中读取。"
                If Me.GetCurrentDevice() Is Nothing Then
                    Me.llblRefresh.Visible = False
                Else
                    Me.llblRefresh.Visible = True
                End If
            ElseIf Me.mDevice IsNot Nothing Then
                Me.lblFromBackupMsg.Visible = False
                Me.llblRefresh.Visible = False
            End If
        End If
    End Sub

    Private Sub InitmChargeHelper()
        If Me.mChargeHelper Is Nothing Then
#If IS_ITONG Then
            Me.mChargeHelper = ChargeHelper.Instance(PayVersions.iTongWeChatAssistant)
#Else
            Me.mChargeHelper = ChargeHelper.Instance()
#End If
            AddHandler Me.mChargeHelper.RegistrCompleted, AddressOf ChargeHelper_RegistrCompleted
            AddHandler Me.mChargeHelper.WeChatMigrationChargeHandler, AddressOf ChargeHelper_WeChatMigrationChargeHandler
            Me.mChargeHelper.LoadInfoFromServer()
            Me.SetLblRegisterInfo()
        End If
    End Sub

    Private Function GetCurrentDevice() As iPhoneDevice
        Dim dev As iPhoneDevice = Nothing
#If IS_RECOVERY Then
        Dim frmMain As MainFormDataRecovery = CType(Me.mApplication, MainFormDataRecovery)
        If frmMain.Device IsNot Nothing AndAlso frmMain.Device.IsConnected Then
            dev = frmMain.Device
        End If
#End If
        Return dev
    End Function

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Utility.AbortThread(Me.mThreadGetChat)
        Utility.AbortThread(Me.mThreadLoadHeadIcon)

        Try
            If Me.mPlayer IsNot Nothing Then
                RemoveHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange
            End If

            If Me.mBackupHelper IsNot Nothing Then
                RemoveHandler Me.mBackupHelper.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged
            End If

            If Me.tmrShowCount IsNot Nothing Then
                Me.tmrShowCount.Close()
                RemoveHandler Me.tmrShowCount.Elapsed, AddressOf tmrShowCount_Elapsed
            End If
        Catch
        End Try

    End Sub

    Protected Overrides Sub OnFormClosed(e As FormClosedEventArgs)
        MyBase.OnFormClosed(e)
        Common.KillProcess("tbWCRHelper")
        Try
            If Me.mFQQHelper IsNot Nothing Then
                RemoveHandler Me.mFQQHelper.QQRProgressEventHandler, AddressOf FWCDHelper_WCRProgressEventHandler
                Me.mFQQHelper.Dispose()
                Me.mFQQHelper = Nothing
            End If
        Catch
        End Try

        Try
            RemoveHandler Me.mChargeHelper.RegistrCompleted, AddressOf ChargeHelper_RegistrCompleted
        Catch ex As Exception
        End Try
    End Sub
    Private Sub InitMusicPlayer()
        Me.mPlayer = PluginPlayer.Instance()
        Me.mPlayer.PlayMode = UserPlayMode.OrderPlay

        If Me.mPlayer.GetUserPlaylist(Me.Name) Is Nothing Then
            Me.mPlayer.AddUserPlaylist(Me.Name)
        End If

        AddHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange
    End Sub

    Private Sub SetWebBackColor()
        Dim writer As New iTong.CoreFoundation.JsonWriter()
        Dim dicColor As New JsonObject
        dicColor.Add("bgcolor", "#fff")
        Dim dicConfig As New JsonObject
        dicConfig.Add("config", dicColor)
        dicConfig.Write(writer)
        ChatWebPage.ShowChatOnWebPage(Me.wbsChat, writer.ToString())
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private currentSender As Object = Nothing
    Private currente As EventArgs = Nothing
    Private Sub ChargeHelper_RegistrCompleted(sender As Object, e As RegistrCompletedEventArgs)
        '如果用户注册成功后就进行设备注册。不然当前的设备没办法使用
        If Me.mBackInfo IsNot Nothing Then
            ChargeHelper.Instance.SetDeviceRegistedLocal(Me.mBackInfo.Identifier, Me.mBackInfo.SerialNumber, Me.mBackInfo.ProductType, Me.mBackInfo.DeviceName)
        ElseIf Me.mDevice IsNot Nothing Then
            ChargeHelper.Instance.SetDeviceRegistedLocal(Me.mDevice)
        End If

        '提交注册信息到服务器，上面的代码只本地注册
        ChargeHelper.Instance().RegistDevice()

        SetLblRegisterInfo()
        If currentSender IsNot Nothing AndAlso currente IsNot Nothing Then
            dgvFriend_SelectionChanged(currentSender, currente)
        End If
    End Sub

    Private Sub ChargeHelper_WeChatMigrationChargeHandler(sender As Object, e As EventArgs)
        SetLblRegisterInfo()
    End Sub

    Private Sub SetLblRegisterInfo()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf SetLblRegisterInfo))
        Else
            Try
                Dim fontF As Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
                Dim isRegisterVisible As Boolean = False
                If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None AndAlso (Not Me.mChargeHelper.mChargeInfo.Registered OrElse Me.mChargeHelper.mChargeInfo.Overdue OrElse Not Me.mChargeHelper.CheckDeviceRegisted(Me.mCurrentIdentify)) Then
                    isRegisterVisible = True
                End If

                Me.lblRegister.Visible = isRegisterVisible

                Me.lblRegister.ColorTextList.Clear()
                If Me.mChargeHelper.mChargeInfo.Registered AndAlso Not Me.mChargeHelper.mChargeInfo.Overdue AndAlso Not Me.mChargeHelper.CheckDeviceRegisted(Me.mCurrentIdentify) Then
                    Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("MainVip.Message.DeviceOverAmountEx"), 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False) '试用版预览部分内容用“*”代替
                Else
                    Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("Weixin.Lable.RegisterWarn"), 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False) '试用版预览部分内容用“*”代替
                End If
                Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("Weixin.Lable.BuyNow"), 1, System.Drawing.Color.FromArgb(0, 169, 255), fontF, False, True) '"立即购买"
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetLblRegisterInfo")
            End Try
        End If
    End Sub

    Private Sub btnWeCahtUsers_TextChanged(sender As Object, e As EventArgs) Handles btnWeCahtUsers.TextChanged
        If Me.mCurrentUserName = Me.btnWeCahtUsers.Text Then
            Me.mIsChangeUser = False
        Else
            Me.mCurrentUserName = Me.btnWeCahtUsers.Text
            Me.mIsChangeUser = True
        End If
    End Sub

    Private Sub btnWeCahtUsers_Click(sender As Object, e As EventArgs) Handles btnWeCahtUsers.Click
        Try
            Me.mIsSelectChageUser = True
            Me.CreateWeChatUsers()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnSelectDevice_Click")
        End Try
    End Sub

    Private Sub CreateWeChatUsers()
        Dim menu As New tbContextMenuStrip()

        Dim menuInfoItem As ToolStripMenuItem = Nothing

        For Each item As KeyValuePair(Of String, String) In Me.mDicQQAccountPath
            menuInfoItem = menu.Items.Add(item.Key, Nothing)
            menuInfoItem.Tag = item.Value

            If btnWeCahtUsers.Text = item.Key Then
                menuInfoItem.Checked = True
            Else
                menuInfoItem.Checked = False
            End If

            AddHandler menuInfoItem.Click, AddressOf OnQQUsersMenuItemClick
        Next

        AddHandler menu.Closed, AddressOf OnQQUsersMenuClosed
        menu.Show(btnWeCahtUsers, New Point(0, btnWeCahtUsers.Height))
    End Sub

    Private Sub OnQQUsersMenuItemClick(sender As Object, e As EventArgs)
        Try
            Me.mDictDelChatInfo.Clear()
            Me.mDictDelFriendInfo.Clear()
            Dim item As ToolStripMenuItem = CType(sender, ToolStripMenuItem)
            Me.btnWeCahtUsers.Text = item.Text
            Me.mCurrentUserNum = item.Text
            Me.mIsSelect = True
            Me.txtSearchChat.Text = String.Empty
            Me.dtpStart.Value = CType("1970-01-02 00:00:01", DateTime)
            Me.dtpEnd.Value = Now
            Me.btnTime.Text = Me.Language.GetString("Weixin.Item.All")
            Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading")
            Me.SetPnlCount(False)
            LoadFriend()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnQQUsersMenuItemClick")
        End Try
    End Sub

    Private Sub OnQQUsersMenuClosed(sender As Object, e As ToolStripDropDownClosedEventArgs)
        Try
            Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
            RemoveHandler menu.Closed, AddressOf OnQQUsersMenuClosed
        Catch ex As Exception
        End Try
    End Sub

    Private Sub dgvFriend_SelectionChanged(ByVal sender As Object, ByVal e As EventArgs)
        If Me.mSelectAllClick = True Then
            Return
        End If
        Me.SetLinkmanMsg()
        currentSender = sender
        currente = e
        Try
            Dim grid As tbDataGridViewEx = CType(sender, tbDataGridViewEx)
            Dim intColumnIndex As Integer = grid.CurrentCell.ColumnIndex
            If grid.tbCanResponse = False OrElse intColumnIndex = 0 Then
                Return
            End If
        Catch
        End Try

        Me.mLstCurrentInfos.Clear()
        Me.mLstCurrentChatInfo.Clear()

        If Not Me.mBlnReloadFriendCompleted OrElse Me.dgvFriend.SelectedRows() Is Nothing OrElse Me.dgvFriend.SelectedRows().Count <= 0 Then
            Return
        End If
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)

        Me.SetWebBackColor()
        Dim row As DataGridViewRow = Me.dgvFriend.SelectedRows(0)
        Dim item As QQFriendInfo = CType(row.Tag, QQFriendInfo)
        item.SearchTime = Nothing
        'If item.IsFindDate AndAlso Not IniSetting.IsWeChatShare() Then
        '    Return
        'End If
        Me.ShowShare(False, True)
        Me.pnlLoadData.Visible = True
        Me.pnlLoadData.BringToFront()
        Me.LoadChat(row)
    End Sub

    Private Sub wbsChat_Navigating(sender As Object, e As WebBrowserNavigatingEventArgs) Handles wbsChat.Navigating
        Dim url As String = e.Url.ToString

        If url.StartsWith("weixin://getnews", StringComparison.OrdinalIgnoreCase) Then
            Me.GetNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://viewphoto", StringComparison.OrdinalIgnoreCase) Then
            Me.ShowPhoto(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://copy", StringComparison.OrdinalIgnoreCase) Then
            Me.CopyNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportphoto", StringComparison.OrdinalIgnoreCase) Then
            Me.ExportMedia(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportvoice", StringComparison.OrdinalIgnoreCase) Then
            Me.ExportMedia(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportvideo", StringComparison.OrdinalIgnoreCase) Then
            Me.ExportMedia(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://playvoice", StringComparison.OrdinalIgnoreCase) Then
            Me.PlayVoice(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://getcontextnews", StringComparison.OrdinalIgnoreCase) Then
            'Me.GetContextNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase) Then
            Me.Navigate(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://playvideo", StringComparison.OrdinalIgnoreCase) Then
            e.Cancel = True
            If Me.mBackupMediaSucceed Then
                tbSplashBox.ShowMessage(Me.Language.GetString("Weixin.Message.ExportToView"), Me)
            End If
        End If
    End Sub

    Private Sub CopyNews(ByVal url As String)
        If Me.CheckChargeOverdue Then
            Return
        End If

        Dim chat As QQChatInfo = Me.GetChatForWeb(url)
        If chat Is Nothing Then
            tbSplashBox.ShowMessage(Me.Language.GetString("Weixin.Message.CopyFail"), Me)
            Return
        End If

        Try
            Dim strContent As String = chat.StrMessage
            Clipboard.SetText(strContent)
            tbSplashBox.ShowMessage(Me.Language.GetString("Welcome.Message.CopySucceed"), Me)
        Catch
            tbSplashBox.ShowMessage(Me.Language.GetString("Weixin.Message.CopyFail"), Me)
        End Try
    End Sub

    Private Sub btnNewExport_Click(sender As Object, e As EventArgs)
        If Me.dgvFriend.Rows.Count = 0 Then
            '联系人为空。
            tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Me.cmsExportNews.Show(Me.btnExport, 0, Me.btnExport.Height + 3)
    End Sub

    Private Sub tsmiExcelEx_Click(sender As Object, e As EventArgs) Handles tsmiExcelEx.Click
        Me.NewExport(ExportType.ExportToExcel)
    End Sub

    Private Sub tsmiTxtEx_Click(sender As Object, e As EventArgs) Handles tsmiTxtEx.Click
        Me.NewExport(ExportType.ExportToTxt)
    End Sub

    Private Sub tsmiHtml_Click(sender As Object, e As EventArgs) Handles tsmiHtml.Click
        Me.NewExport(ExportType.ExportToHtml)
    End Sub

    Private Sub tsmiPicEx_Click(sender As Object, e As EventArgs) Handles tsmiPicEx.Click
        Me.ExportMediaByType("Picture")
    End Sub

    Private Sub tsmiAudioEx_Click(sender As Object, e As EventArgs) Handles tsmiAudioEx.Click
        Me.ExportMediaByType("Audio")
    End Sub

    Private Sub tsmiVideoEx_Click(sender As Object, e As EventArgs) Handles tsmiVideoEx.Click
        Me.ExportMediaByType("Video")
    End Sub

    'Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
    '    If Me.mQQDB IsNot Nothing Then
    '        Me.mQQDB.Dispose()
    '    End If
    '    Me.mDictDelChatInfo.Clear()
    '    Me.LoadFriend(True)
    'End Sub

    Private Sub lblR_SizeChanged(sender As Object, e As EventArgs) Handles llblRefresh.SizeChanged, lblFromBackupMsg.SizeChanged, pnlBottomMessage.SizeChanged
        Try
            Me.llblRefresh.Location = New Point(Me.lblFromBackupMsg.Left + Me.lblFromBackupMsg.Size.Width, Me.lblFromBackupMsg.Location.Y)
        Catch
        End Try
    End Sub

    Private Sub txtSearchChat_ClearClick(sender As Object, e As EventArgs) Handles txtSearchChat.ClearClick
        For Each item As QQFriendInfo In Me.mDictFriendInfoEx.Values
            item.SearchText = String.Empty
            item.SearchTime = Nothing
        Next

        For Each item As QQFriendInfo In Me.mDictDelFriendInfo.Values
            item.SearchText = String.Empty
            item.SearchTime = Nothing
        Next
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.ShowShare(False, True)
        Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"
        Me.AddDictToDataGridView(Me.mDictFriendInfoEx, False)
        Me.AddDictToDataGridView(Me.mDictDelFriendInfo, False, True)
        Me.LoadHeadIcon()
        Me.UpdateDataGridViewRow()
    End Sub

    Private Sub txtSearchChat_KeyDown(sender As Object, e As KeyEventArgs) Handles txtSearchChat.KeyDown
        If e.KeyCode <> Keys.Enter Then
            Return
        End If
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.ChangeLoadDataVisible(True)
        Me.ShowShare(False, True)
        Dim strSearchText As String = Me.txtSearchChat.Text
        Dim dictSearchResult As Dictionary(Of String, QQFriendInfo) = Me.mQQDB.SearchFriend(Me.mDictFriendInfoEx, strSearchText)

        Dim dictDelSearchResult As Dictionary(Of String, QQFriendInfo) = New System.Collections.Generic.Dictionary(Of String, CoreModule.QQFriendInfo)()
        For Each strUsrName As String In Me.mDictDelFriendInfo.Keys

            '如果当前找回联系人已经在上面一步搜索到了，则添加到删除搜索到的列表中
            If dictSearchResult.ContainsKey(strUsrName) Then
                dictSearchResult.Remove(strUsrName)
                Dim info As QQFriendInfo = mDictDelFriendInfo(strUsrName)
                info.SearchText = strSearchText
                dictDelSearchResult.Add(strUsrName, info)
                Continue For
            End If


            Dim lstInfos As New List(Of QQChatInfo)
            Me.GetDeleteChatInfo(strUsrName, lstInfos, DataType.Normal, strSearchText)
            If lstInfos IsNot Nothing AndAlso lstInfos.Count > 0 Then

                If Not dictDelSearchResult.ContainsKey(strUsrName) Then
                    Dim info As QQFriendInfo = mDictDelFriendInfo(strUsrName)
                    info.SearchText = strSearchText
                    dictDelSearchResult.Add(strUsrName, info)
                End If
            End If
        Next

        If dictSearchResult Is Nothing OrElse dictSearchResult.Count = 0 Then
            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.SearchNoResult")) '"搜索不到结果"
        Else
            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"
        End If

        Me.AddDictToDataGridView(dictSearchResult, False)
        Me.AddDictToDataGridView(dictDelSearchResult, False, True)
        Me.ChangeLoadDataVisible(False)
        Me.LoadHeadIcon()
        Me.UpdateDataGridViewRow()
    End Sub

    Private Sub btnExportSetting_Click(sender As Object, e As EventArgs) Handles btnExportSetting.Click
        Dim frm As New frmWeChatSetting()
        frm.ShowDialog(Me)
    End Sub

    Private Sub btnTimeScreen_Click(sender As Object, e As EventArgs) Handles btnTimeScreen.Click, picTimeScreen.Click
        Try
            Me.pnlTimeSelect.Visible = Not Me.pnlTimeSelect.Visible
            If Me.pnlTimeSelect.Visible Then
                Me.picTimeScreen.Image = My.Resources.Resources.TimeScreenUp
            Else
                Me.picTimeScreen.Image = My.Resources.Resources.TimeScreenUpDowm
            End If
            Me.dtpStart.Value = CType("1970-01-02 00:00:10", DateTime)
            Me.dtpStart.Value = CType("1970-01-02 00:00:01", DateTime)
            Me.SetViewStyleTimeSelect()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnTimeScreen_Click")
        End Try
    End Sub

    Private Sub btnBackLoading_Click(sender As Object, e As EventArgs) Handles btnBack.Click, btnBackLoading.Click
        Me.Close()
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        Me.cmsShowSession.Show(Me.btnFilter, 0, Me.btnFilter.Height + 3)
    End Sub

    Private Sub lblRegister_TextClick(sender As Object, e As EventArgs) Handles lblRegister.TextClick
        ChargeHelper.Instance().ShowChargeForm(Me)
    End Sub

    Private Sub llblRefresh_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles llblRefresh.LinkClicked
#If IS_RECOVERY Then
        Dim frmMain As MainFormDataRecovery = CType(Me.mApplication, MainFormDataRecovery)
        Dim dev As iPhoneDevice = frmMain.Device
        If dev Is Nothing Then
            Return
        End If

        If tbMessageBox.Show(Me, Me.Language.GetString("Common.Backup.Exit"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) <> Windows.Forms.DialogResult.OK Then
            Return
        End If

        If dev.Identifier <> Me.mBackInfo.Identifier Then
            tbMessageBox.Show(Me, Me.Language.GetString("Common.Backup.DataExplainEx"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning) '"当前连接设备与备份文件的设备不一致，无法获取最新。"
            Return
        End If
        frmMain.StartBackpuDevice()
#End If
    End Sub

    Private Sub btnTime_Click(sender As Object, e As EventArgs) Handles btnTime.Click
        Dim menu As New tbContextMenuStrip()
        Dim menuInfoItem As ToolStripMenuItem = Nothing

        Dim lstTimes As List(Of String) = New List(Of String)
        lstTimes.Add(Me.Language.GetString("Download.Message.Today"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.Week"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.Month"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.ThreeMonth"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.Year"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.All"))

        For Each strTime As String In lstTimes
            menuInfoItem = menu.Items.Add(strTime, Nothing)

            If btnTime.Text = strTime Then
                menuInfoItem.Checked = True
            Else
                menuInfoItem.Checked = False
            End If
            AddHandler menuInfoItem.Click, AddressOf OnTimeMenuItemClick
        Next

        AddHandler menu.Closed, AddressOf OnTimeMenuClosed
        menu.Show(btnTime, New Point(0, btnTime.Height))
    End Sub

    Private Sub OnTimeMenuClosed(sender As Object, e As ToolStripDropDownClosedEventArgs)
        Try
            Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
            RemoveHandler menu.Closed, AddressOf OnTimeMenuClosed
        Catch ex As Exception
        End Try
    End Sub

    Private Sub OnTimeMenuItemClick(sender As Object, e As EventArgs)
        Try
            Dim intDays As Integer = 0
            Dim StartTime As DateTime = DateTime.MinValue
            Dim EndTime As DateTime = Now
            Dim item As ToolStripMenuItem = CType(sender, ToolStripMenuItem)
            Select Case item.Text
                Case Me.Language.GetString("Download.Message.Today") '今天
                    intDays = 0
                Case Me.Language.GetString("Weixin.Item.Week") '最近一周
                    intDays = 7
                Case Me.Language.GetString("Weixin.Item.Month") '最近一个月
                    intDays = 30
                Case Me.Language.GetString("Weixin.Item.ThreeMonth") '最近三个月
                    intDays = 30 * 3
                Case Me.Language.GetString("Weixin.Item.Year")  '最近一年
                    intDays = 30 * 12
                Case Me.Language.GetString("Weixin.Item.All")
                    intDays = -1
            End Select
            Me.btnTime.Text = item.Text

            If intDays = 0 Then
                Me.dtpStart.Value = CType(DateTime.Now.ToString("yyyy-MM-dd 00:00:01"), DateTime)
            ElseIf intDays = -1 Then
                Me.dtpStart.Value = CType("1970-01-02 00:00:01", DateTime)
            Else
                Me.dtpStart.Value = EndTime.AddDays(-intDays)
            End If
            Me.dtpEnd.Value = Now
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnTimeMenuItemClick")
        End Try
    End Sub

    Private Sub cbxSelectAll_CheckedChanged(sender As Object, e As EventArgs) Handles cbxSelectAll.CheckedChanged
        If Me.mSelectAllClick = True Then
            Return
        End If

        Me.mSelectAllClick = True
        For Each item As DataGridViewRow In Me.dgvFriend.Rows
            If item.Selected <> Me.cbxSelectAll.Checked Then
                item.Selected = Me.cbxSelectAll.Checked
                item.Selected = Me.cbxSelectAll.Checked
            End If
        Next
        Me.SetLinkmanMsg()
        Me.mSelectAllClick = False
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        If Me.dgvFriend.Rows.Count = 0 Then
            '联系人为空。
            tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Me.cmsExportEx.Show(Me.btnExport, Me.btnExport.Width - Me.cmsExportEx.Width, Me.btnExport.Height + 3)
    End Sub

    Private Sub tsmiAll_Click(sender As Object, e As EventArgs) Handles tsmiAll.Click
        Me.NewExport(ExportType.ExportToAll)
    End Sub

    Private Sub pnlLoadData_SizeChanged(sender As Object, e As EventArgs) Handles pnlLoadData.SizeChanged
        Try
            Me.lblSystemInfo.Location = New Point((Me.pnlContent.Width - Me.lblSystemInfo.Width) / 2, (Me.pnlContent.Height - Me.lblSystemInfo.Height) / 2)
            Me.pnlLoadData.Location = New Point((Me.pnlContent.Width - Me.pnlLoadData.Width) / 2, (Me.pnlContent.Height - Me.pnlLoadData.Height) / 2)
        Catch ex As Exception

        End Try
    End Sub

    Private Sub pnlShare_SizeChanged(sender As Object, e As EventArgs)
        Try
            Me.pnlShare.Location = New Point((Me.pnlContent.Width - Me.pnlShare.Width) / 2, (Me.pnlContent.Height - Me.pnlShare.Height) / 2)
        Catch ex As Exception

        End Try
    End Sub

    Private Sub pnlFindData_SizeChanged(sender As Object, e As EventArgs)
        Try
            Me.pnlFindData.Location = New Point((Me.pnlContent.Width - Me.pnlFindData.Width) / 2, (Me.pnlContent.Height - Me.pnlFindData.Height) / 2)
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.mLstCurrentChatInfo.Clear()

        Me.pnlLoadData.Visible = False

        Dim lstTime As New List(Of DateTime)
        lstTime.Add(Me.dtpStart.Value)
        lstTime.Add(Me.dtpEnd.Value)
        Me.LoadChatForTimeLoaction(lstTime)
    End Sub

    Private Sub lblSystemInfo_Click(sender As Object, e As EventArgs)
#If IS_WECHAT Then
        If Folder.AppType = RunType.iWeChatMigration Then
            Common.OpenExplorer(WebUrl.iTunesBackupCourse)
        Else
            Common.OpenExplorer("http://wechat.tongbu.com/guide/1")
        End If
#Else
        Common.OpenExplorer(WebUrl.iTunesBackupCourse)
#End If
    End Sub

    Private Sub pnlContent_SizeChanged(sender As Object, e As EventArgs) Handles pnlContent.SizeChanged
        Try
            Me.lblSystemInfo.Location = New Point((Me.pnlContent.Width - Me.lblSystemInfo.Width) / 2, (Me.pnlContent.Height - Me.lblSystemInfo.Height) / 2)
            Me.pnlLoadData.Location = New Point((Me.pnlContent.Width - Me.pnlLoadData.Width) / 2, (Me.pnlContent.Height - Me.pnlLoadData.Height) / 2)
            Me.pnlFindData.Location = New Point((Me.pnlContent.Width - Me.pnlFindData.Width) / 2, (Me.pnlContent.Height - Me.pnlFindData.Height) / 2)
            Me.pnlShare.Location = New Point((Me.pnlContent.Width - Me.pnlShare.Width) / 2, (Me.pnlContent.Height - Me.pnlShare.Height) / 2)
        Catch ex As Exception

        End Try
    End Sub

    Private Sub lblReadError_TextClick(sender As Object, e As EventArgs) Handles lblReadError.TextClick
        Me.SetViewStyle(QQViewStyle.Loading)
        Me.LoadFriend()
    End Sub

    Private Sub lblReadError_SizeChange(sender As Object, e As EventArgs)
        Me.lblReadError.Size = New Size(Me.lblReadError.mMeWidth + 10, Me.lblReadError.Size.Height)
        Me.lblReadError.Location = New Point((Me.pnlError.Width - Me.lblReadError.Size.Width) / 2, Me.lblReadError.Location.Y)
    End Sub

    Private Sub DoExit()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf DoExit))
        Else
            Me.Close()
        End If
    End Sub

#End Region

#Region "--- 界面展示 ---"

    Private Delegate Sub SetViewStyleHandler(ByVal style As QQViewStyle, ByVal isJump As Boolean, ByVal isFromBackup As Boolean)
    Public Sub SetViewStyle(ByVal style As QQViewStyle, Optional ByVal isJump As Boolean = False, Optional ByVal isFromBackup As Boolean = False)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), style, isJump, isFromBackup)
        Else
            Try
                Select Case style
                    Case QQViewStyle.Loading
                        If Me.tlpQQ.ColumnStyles(0).Width <> 100 Then
                            Me.tlpQQ.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(0).Width = 100
                            Me.tlpQQ.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(1).Width = 0
                            Me.tlpQQ.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(2).Width = 0
                            Me.tlpQQ.ColumnStyles(3).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(3).Width = 0

                            Me.pnlLoad.Visible = True

                        End If

                    Case QQViewStyle.Uninstalled
                        If Me.tlpQQ.ColumnStyles(1).Width <> 100 Then
                            Me.tlpQQ.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(0).Width = 0
                            Me.tlpQQ.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(1).Width = 100
                            Me.tlpQQ.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(2).Width = 0
                            Me.tlpQQ.ColumnStyles(3).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(3).Width = 0

                            Me.pnlLoad.Visible = False
                        End If

                    Case QQViewStyle.Content
                        If Me.tlpQQ.ColumnStyles(3).Width <> 100 Then
                            Me.tlpQQ.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(0).Width = 0
                            Me.tlpQQ.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(1).Width = 0
                            Me.tlpQQ.ColumnStyles(2).SizeType = SizeType.Absolute
                            Me.tlpQQ.ColumnStyles(2).Width = 294
                            Me.tlpQQ.ColumnStyles(3).SizeType = SizeType.Percent
                            Me.tlpQQ.ColumnStyles(3).Width = 100

                            Me.pnlLoad.Visible = False
                        End If
                End Select
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetViewStyle")
            End Try
            Application.DoEvents()
        End If
    End Sub

    Public Sub SetViewStyleTimeSelect()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf SetViewStyleTimeSelect))
        Else
            Try
                If Me.tlpQQ.RowStyles(1).Height <> 39 Then
                    Me.tlpQQ.RowStyles(1).Height = 39
                Else
                    Me.tlpQQ.RowStyles(1).Height = 0
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetViewStyle")
            End Try
            Application.DoEvents()
        End If
    End Sub

    Private Sub tmrShowCount_Elapsed(sender As Object, e As Timers.ElapsedEventArgs)
        If Me.mShowTime < 15 Then
            Me.mShowTime = Me.mShowTime + 1
            Return
        End If
        Me.tmrShowCount.Stop()
        Utility.WaitSeconds(1)
        Me.SetPnlCount(False)
    End Sub

    Private Sub SetPnlCount(ByVal isShow As Boolean)
        Me.mShowTime = 0
        If isShow Then
            Me.tmrShowCount.Start()
        Else
            Me.tmrShowCount.Close()
        End If
        Try
            Me.SetPnlCountVisible(isShow)
        Catch ex As Exception
            Me.SetPnlCountVisible(isShow)
            Common.LogException(ex.ToString(), "SetPnlCount")
        End Try
    End Sub

    Private Delegate Sub SetPnlCountVisibleHandler(ByVal blnVisible As Boolean)
    Private Sub SetPnlCountVisible(ByVal blnVisible As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetPnlCountVisibleHandler(AddressOf SetPnlCountVisible), blnVisible)
            Else
                Me.pnlCount.Visible = blnVisible
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetPnlCountVisible")
        End Try
    End Sub
#End Region

#Region "--- 加载联系人 聊天数据 ---"

    Private Sub LoadFriend(Optional ByVal blnRefresh As Boolean = False)
        Try
            If Me.bgwLoadFriend.IsBusy() Then
                Me.bgwLoadFriend.CancelAsync()
            End If
            Me.pnlShare.Visible = False
            Me.pnlFindData.Visible = False
            Me.pnlLoadData.Visible = False
            Me.bgwLoadFriend.RunWorkerAsync(blnRefresh)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadFriend")
        End Try

    End Sub

    Private Sub bgwLoadFriend_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwLoadFriend.DoWork
        Me.SetViewStyle(QQViewStyle.Loading)

        Dim state As QQState = QQState.Succeed
        Try
            Dim blnRefresh As Boolean = e.Argument
            '1.如果从设备读取就先备份文件
            state = Me.BackupFilesFromDevice()
            If state = QQState.NoInstalled OrElse state = QQState.CanNotAccess OrElse state = QQState.PwdBackup OrElse state = QQState.BackupFailure OrElse state = QQState.CancelEncrypted Then
                GoTo DO_EXIT
            End If
            Common.Log("qq:1.备份完成")
            '读取取备份文件中的内容
            state = Me.LoadFilesFromBackup(blnRefresh)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadFriend_DoWork")
            state = QQState.NoDB
        End Try
DO_EXIT:
        e.Result = state
    End Sub

    Private Function BackupFilesFromDevice() As WeixinState
        Me.mBackupHelper = BackupHelper.Instance(Me.mDevice)
        AddHandler Me.mBackupHelper.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged

        Dim state As WeixinState = WeixinState.Loading
        Try
            If Not Me.mIsFromBackup Then
                If Me.UnInstalledCloseMe() Then
                    Return state
                End If
                '如果备份过就不用重新备份了
                If Me.mBackupHelper.iDataRecoveryBackupFolder.Length > 0 Then
                    Me.mStrBackupFolder = Me.mBackupHelper.iDataRecoveryBackupFolder
                Else
                    Me.mStrBackupFolder = BackupHelper.GetBackUpFolder(Me.mNewBackup, Me.mDevice.Identifier)
                    Dim callback As New BackupRestoreHandler(AddressOf mb2_Backup_BackupRestore)
                    If Not Me.mDevice.mb2_Backup(Me.mStrBackupFolder, callback, Me.mNewBackup) Then
                        '备份失败
                        Me.mBackupHelper.iDataRecoveryBackupFolder = String.Empty
                        Me.SetViewStyle(QQViewStyle.Uninstalled)
                        state = WeixinState.BackupFailure
                        GoTo DO_EXIT
                    Else
                        Me.mBackupHelper.AddLstFullBackupList(Me.mStrBackupFolder)
                        '备份成功后显示聊天记录
                        Me.mBackupHelper.iDataRecoveryBackupFolder = Me.mStrBackupFolder
                        Me.mBackupHelper.GetFullBackupList(True)
                    End If
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmQQ_BackupDevice")
        End Try
DO_EXIT:
        Return state
    End Function

    Private Function UnInstalledCloseMe() As Boolean
        Dim blnRetuirn As Boolean = False
#If IS_RECOVERY Then
        Try
            Dim isInstalled As Boolean = False
            Try
                '判断是否 安装软件
                If Me.mDevice IsNot Nothing Then
                    '从设备读取  直接判断设备是否安装
                    Dim dictApp As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User, True)
                    If dictApp.ContainsKey(Me.mQQSKU) Then
                        isInstalled = True
                    End If

                Else
                    '从备份读取判断备份是否存在
                    Dim lstMBFileRecord As List(Of MBFileRecord) = mbdb.Reload(Me.mStrBackupFolder, True)
                    If lstMBFileRecord.Count > 0 Then
                        For Each item As MBFileRecord In lstMBFileRecord
                            If item.PathOnPhone IsNot Nothing AndAlso item.PathOnPhone.Contains("/var/mobile/Applications/" & Me.mQQSKU) Then
                                isInstalled = True
                                Exit For
                            End If
                        Next
                    End If
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "UnInstalledCloseMe")
            End Try

            If Not isInstalled Then
                Dim strMsg As String = "检测尚未安装iPhone版QQ" & vbCrLf & "目前暂不支持QQ国际版和QQHD版等！"
                tbMessageBox.Show(Me.mApplication, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                CType(Me.mApplication, MainFormDataRecovery).RecoveryListBack()
                Me.DoExit()
                blnRetuirn = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UnInstalledCloseMe")
        End Try
#End If
        Return blnRetuirn
    End Function

    Private Sub OnDecryptProgressChanged(sender As Object, args As BackupDecryptArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupDecryptArgs)(AddressOf OnDecryptProgressChanged), sender, args)
        Else
            Me.lblLoading.Text = String.Format("{0}{1}%", Me.Language.GetString("Backup.Message.Decrypteding"), args.intProgress)
        End If
    End Sub

    Private Delegate Sub mb2_Backup_BackupRestoreHandler(ByVal e As BackupRestoreEventArgs)
    Private Sub mb2_Backup_BackupRestore(ByVal e As BackupRestoreEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New mb2_Backup_BackupRestoreHandler(AddressOf mb2_Backup_BackupRestore), e)
        Else
            Dim sType As BackupRestoreErrorType = BackupRestoreErrorType.None
            If e.ErrorMsg.Length > 0 Then
                Dim args As New BackupRestoreEventArgs
                args.ErrorMsg = e.ErrorMsg
                sType = BackupHelper.GetErrotType(BackupRestore.Backup, args)

                Try
                    If sType = BackupRestoreErrorType.DiskSpaceNotEnough Then
                        Me.lblError.Text = Me.Language.GetString("Common.Message.DiskSpaceNotEnough")
                    Else
                        Me.lblError.Text = args.ErrorMsg
                    End If
                Catch
                End Try
            Else
                Dim strProgress As Integer = CInt(e.Progress).ToString().PadLeft(2, " "c)
                Me.lblLoading.Text = String.Format("{0}{1}%", Me.Language.GetString("Backup.Message.BeinBackuping"), strProgress) ' "正在备份中..."
            End If
        End If
    End Sub

    Private Function DecryptBackupFile() As QQState
        Dim state As QQState = QQState.Succeed
#If IS_RECOVERY Then
        Dim frmMain As MainFormDataRecovery = CType(Me.mApplication, MainFormDataRecovery)
        Dim info As BackupInfo = Me.mBackInfo
        If Me.mBackInfo Is Nothing Then
            info = New BackupInfo(Me.mStrBackupFolder)
        End If
        Dim isCancel As Boolean = False
        If Not frmMain.CheckIsEncrypted(info, isCancel, False, "", Me.mBackupHelper) Then
            If isCancel Then
                Me.DoExit()
            Else
                Me.SetViewStyle(QQViewStyle.Uninstalled)
            End If
            Common.Log("qq:4.解密失败")
            Return QQState.DecryptFailure
        End If

        Dim strEncryptedBackup As String = ""
        If info.IsEncrypted AndAlso Directory.Exists(Path.Combine(info.FolderPath, iPhoneDevice.Unback_Dir)) Then
            info = New BackupInfo(Path.Combine(info.FolderPath, iPhoneDevice.Unback_Dir))
            Me.mStrBackupFolder = info.FolderPath
            Me.mBackInfo = info
            Common.Log("qq:4.解密成功")
        End If
#End If
        Return state
    End Function

    Private Function LoadFilesFromBackup(ByVal blnRefresh As Boolean) As QQState
        Dim state As QQState = QQState.Succeed

        Try
            state = Me.DecryptBackupFile()
            If state = QQState.DecryptFailure Then
                GoTo DO_EXIT
            End If

            If Me.mStrBackupFolder.Length > 0 Then
                Me.mLstMBFileRecord = mbdb.Reload(Me.mStrBackupFolder, True)
            End If

            Me.mUIFileSharingEnabled = False
            Me.mBackupFolderHelper.mUIFileSharingEnabled = Me.mUIFileSharingEnabled
            Me.InitBackupDir()
            Common.Log("qq:1.获取文件")

            '2.获取用户名
            state = Me.GetAccount()
            If state = QQState.NoAccount Then
                GoTo DO_EXIT
            End If
            Common.Log("qq:2.获取用户名")
            '3.下载数据库
            Dim strDBQQOnPC As String = ""
            Dim strQQFriendOnPC As String = ""
            state = Me.DownLoadQQDB(strDBQQOnPC, strQQFriendOnPC)
            If state = QQState.NoAccount OrElse state = QQState.DownloadDBFailed Then
                GoTo DO_EXIT
            End If
            Common.Log("qq:3.下载数据库")

            '4、读取信息
            Me.mQQDB = QQDB.GetInstance(strDBQQOnPC, strQQFriendOnPC)
            '获取 好友
            Me.mDictFriendInfo = Me.mQQDB.Get_Friend_Info()
            '获取 群
            Me.mQQDB.Get_Group_Info(Me.mDictFriendInfo)

            Me.mQQDB.Get_Discuss_Info(Me.mDictFriendInfo)

            Me.mLstChatTable = Me.mQQDB.GetSession()

            Me.mDictFriendInfoEx = New Dictionary(Of String, QQFriendInfo)
            For Each item As KeyValuePair(Of String, List(Of QQFriendInfo)) In Me.mDictFriendInfo
                For Each fInfo As QQFriendInfo In item.Value
                    If Not Me.mDictFriendInfoEx.ContainsKey(fInfo.StrQQNum) Then
                        Me.mDictFriendInfoEx.Add(fInfo.StrQQNum, fInfo)
                    End If
                Next
            Next

            Me.AddQQDeleteFriend()

            For Each Item As QQFriendInfo In Me.mDictFriendInfoEx.Values
                If Me.IsContainChatTable(Item.StrQQNum, Me.mDictFriendInfoEx) Then
                    Dim lstInfos As List(Of QQChatInfo) = Me.mQQDB.Get_Chat_Info_ByWhere(Item.StrQQNum, -1, 1, Item.IsGroup, Item.IsDiscuss)

                    If lstInfos.Count = 1 Then
                        For Each Item1 As QQChatInfo In lstInfos
                            Item.DTLastSendTime = Item1.DTCreateTime
                            Item.StrLastSendMsg = Me.GetChatMsg(Item1, Item1.SCType)
                        Next
                    End If

                End If
            Next

            state = QQState.Succeed
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadFriend_DoWork_LoadFilesFromBackup")
            state = QQState.NoDB
        End Try
DO_EXIT:
        Return state
    End Function

    Private Sub AddQQDeleteFriend()
        '部分不是好友的聊天记录也要显示出来
        For Each item As String In Me.mLstChatTable
            If Not item.StartsWith("tb_c2cMsg") OrElse item = "tb_c2cMsg_10000" Then
                Continue For
            End If
            item = item.Replace("tb_c2cMsg_", "")
            If Not Me.mDictFriendInfoEx.ContainsKey(item) Then
                Dim newFriendInfo As New QQFriendInfo
                newFriendInfo.StrQQNum = item
                newFriendInfo.NickName = item
                newFriendInfo.NoteName = item
                newFriendInfo.UsrName = item
                Me.mDictFriendInfoEx.Add(item, newFriendInfo)
            End If
        Next
    End Sub

    Private Function GetChatMsg(ByVal Chat As QQChatInfo, ByVal type As ChatMsgType) As String
        Dim strText As String = String.Empty

        Select Case Chat.SCType
            Case SociaChatType.Text  '"1"
                strText = Chat.StrMessage
            Case SociaChatType.Picture '"3"
                strText = "[" & Me.Language.GetString("Main.Button.Photo") & "]" '"[图片]"
            Case SociaChatType.Audio  '"34"
                strText = "[" & Me.Language.GetString("Weixin.Item.Audio") & "]" '"[语音]"
            Case SociaChatType.NameCard '"42"
                strText = "[" & Me.Language.GetString("Weixin.Item.BusinessCard") & "]" '"[名片]"
            Case SociaChatType.Video  '"43"
                strText = "[" & Me.Language.GetString("Media.Type.Video") & "]" '"[视频]"
            Case SociaChatType.SpecialExpression  '"47"
                strText = "[" & Me.Language.GetString("Weixin.Item.Expression") & "]" ' "[动画表情]"
            Case SociaChatType.Location  '"48"
                strText = "[" & Me.Language.GetString("Weixin.Item.Location") & "]" '"[地理位置]"
            Case SociaChatType.webpage '"49"
                strText = "[" & Me.Language.GetString("Weixin.Item.WebPage") & "]" '"[网页]"
            Case SociaChatType.VideoConnected  '"50"
                strText = "[" & Me.Language.GetString("Weixin.Item.Video") & "]" '"[视频聊天]"
            Case SociaChatType.SmallVideo '"62"
                strText = "[" & Me.Language.GetString("Weixin.Lable.SmallVideo") & "]" '"[视频聊天]"
            Case SociaChatType.SystemMessages  '"10000"
                strText = Chat.StrMessage
        End Select

        Return strText
    End Function

    Private Sub CheckBackupMediaSucceed()
        For Each item As String In Me.mDicQQAccountPath.Keys
            Dim strImg As String = String.Format("/var/mobile/Applications/{0}/Documents/{1}/image_original/", Me.mQQSKU, item)
            Dim strVideo As String = String.Format("/var/mobile/Applications/{0}/Documents/{1}/ShortVideo/", Me.mQQSKU, item)
            Dim strAudio As String = String.Format("/var/mobile/Applications/{0}/Documents/{1}/Audio/", Me.mQQSKU, item)
            If Me.mListDir.Contains(strImg) OrElse Me.mListDir.Contains(strVideo) OrElse Me.mListDir.Contains(strAudio) Then
                Me.mBackupMediaSucceed = True
                Exit For
            End If
        Next
    End Sub

    Private Sub InitBackupDir(Optional ByVal isNewBack As Boolean = False)
        Dim strDir As String = "/"
        Me.mBackupMediaSucceed = False
        Me.mListDir.Clear()

        Me.mDicQQAccountPath.Clear()

        Dim strQQStarts As String = String.Format("/var/mobile/Applications/{0}/Documents/contents/", Me.mQQSKU)
        Dim strQQEnd As String = "/QQ.db"

        Dim isCondition As Boolean = False
        For Each item As MBFileRecord In Me.mLstMBFileRecord
            '多张号信息读取 ------------
            If item.PathOnPhone Is Nothing Then
                Continue For
            End If

            isCondition = item.PathOnPhone.StartsWith(strQQStarts) AndAlso item.PathOnPhone.EndsWith(strQQEnd)

            If isCondition Then
                Dim strKey As String = item.PathOnPhone.Replace(strQQStarts, "").Replace(strQQEnd, "")
                If BackupFolderHelper.IsNum(strKey) Then
                    If Not Me.mDicQQAccountPath.ContainsKey(strKey) Then
                        Me.mDicQQAccountPath.Add(strKey, item.PathOnPC)
                    End If
                End If
            End If
            '--------------------------------

            If Not item.Domain.Contains(String.Format("AppDomain-{0}", Me.mQQSKU)) OrElse Not item.PathOnPhone.StartsWith(strDir) OrElse item.PathOnPhone.TrimEnd("/").Length <= strDir.TrimEnd("/").Length Then
                Continue For
            End If

            If Not BackupFolderHelper.CheckInFolder(strDir, item.PathOnPhone) Then
                Continue For
            End If

            Me.mDictSha1(item.PathOnPhone) = item.PathOnPC

            If BackupFolderHelper.IsFolder(item) Then
                Dim strNewDir As String = item.PathOnPhone.TrimEnd("/"c) & "/"
                If Not Me.mListDir.Contains(strNewDir) Then
                    Me.mListDir.Add(strNewDir)
                End If
            Else
                Dim arrDir() As String = item.PathOnPhone.Substring((strDir.TrimEnd("/") & "/").Length).Split("/")
                If arrDir.Length = 0 Then
                    Continue For
                End If

                Dim strNewDir As String = strDir.TrimEnd("/"c) & "/"
                For intI As Integer = 0 To arrDir.Length - 2
                    strNewDir &= arrDir(intI) & "/"

                    If Not Me.mListDir.Contains(strNewDir) Then
                        Me.mListDir.Add(strNewDir)
                    End If
                Next
            End If
        Next
        Me.mBackupFolderHelper.mListDir = Me.mListDir
        Me.CheckBackupMediaSucceed()
    End Sub

    Private Function GetAccount() As QQState
        Dim state As WeixinState = WeixinState.Loading

        If Me.mIsChangeUser Then
            Return state
        End If

        If (Not Me.mIsSelect) Then
            If Me.mDicQQAccountPath.Count > 0 Then
                For Each item As String In Me.mDicQQAccountPath.Keys
                    Me.mCurrentUserNum = item
                    'Me.btnWeCahtUsers.Text = item
                    'Me.btnWeCahtUsers.Tag = item
                    Exit For
                Next
            End If
        End If

        'Me.btnWeCahtUsers.Enabled = True
        SetbtnQQUsers(Me.mCurrentUserNum, True)
        If Me.mCurrentUserNum.Length = 0 Then
            state = WeixinState.NoAccount
        End If

        Return state
    End Function

    Private Delegate Sub SetbtnWeCahtUsersHandler(ByVal strText As String, ByVal isEnabled As Boolean)
    Private Sub SetbtnQQUsers(ByVal strText As String, ByVal isEnabled As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetbtnWeCahtUsersHandler(AddressOf SetbtnQQUsers), strText, isEnabled)
            Else
                Me.btnWeCahtUsers.Text = strText
                Me.btnWeCahtUsers.Enabled = isEnabled
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetbtnWeCahtUsers")
        End Try
    End Sub

    Protected Overridable Function FormatPath(ByVal strPath As String) As String
        'If Me.mIsFromBackup Then

        Dim strNewPath As String = String.Format("/var/mobile/Applications/{1}/{0}", strPath.Trim("/"c), mQQSKU)
        If Me.mDictSha1.ContainsKey(strNewPath) Then
            Return Me.mDictSha1(strNewPath)
        Else
            Return ""
        End If
        'Else
        '    Return mQQBackupCache & strPath.Replace("/", "\")
        'End If
    End Function

    Private Function DownLoadQQDB(ByRef strDBQQOnPC As String, ByRef strQQFriendOnPC As String) As QQState
        Dim state As QQState = QQState.Loading
        Try

            Me.mPathOnPhone = String.Format("/Documents/contents/{0}", Me.mCurrentUserNum)

            'QQ.db  QQFriendList_v3.plist
            Dim strQQdbPath As String = String.Format("{0}/QQ.db", Me.mPathOnPhone)
            Dim strQQFriendList_v3plist As String = String.Format("{0}/QQFriendList_v3.plist", Me.mPathOnPhone)

            Me.mPathQQOnPC_Temp = Folder.GetTempFilePath()
            Me.mPathQQOnPC_Cache = String.Format("{0}QQ\{1}", Folder.CacheFolder, Me.mCurrentUserNum)

            If Me.mIsFromBackup AndAlso Directory.Exists(Me.mPathQQOnPC_Temp) Then
                Try
                    If Directory.Exists(mPathQQOnPC_Temp) Then
                        Directory.Delete(mPathQQOnPC_Temp, True)
                    End If
                Catch
                End Try
            End If

            Dim strDirDBOnPC As String = String.Format("{0}\DB", Me.mPathQQOnPC_Temp)
            Folder.CheckFolder(strDirDBOnPC)
            strDBQQOnPC = Path.Combine(strDirDBOnPC, "QQ.db")
            strQQFriendOnPC = Path.Combine(strDirDBOnPC, "QQFriendList_v3.plist")

            Dim strPathNew As String = FormatPath(Me.mPathOnPhone)
            If Not Me.mBackupFolderHelper.Exists(Me.mPathOnPhone, strPathNew) Then
                state = QQState.BackupFailure
                GoTo DO_EXIT
            End If

            '1、删除旧数据库
            Dim dateDBPhone As String = Me.mBackupFolderHelper.GetDBTimeOnPhone(Me.mPathOnPhone, strPathNew)
            If File.Exists(strDBQQOnPC) Then
                Dim dateDBPC As String = IniSetting.GetMMDate()

                If String.Compare(dateDBPC, dateDBPhone, True) <> 0 Then
                    Try
                        File.Delete(strDBQQOnPC)

                        If File.Exists(strDBQQOnPC & "-shm") Then
                            File.Delete(strDBQQOnPC & "-shm")
                        End If
                        If File.Exists(strDBQQOnPC & "-wal") Then
                            File.Delete(strDBQQOnPC & "-wal")
                        End If

                        If File.Exists(strQQFriendOnPC) Then
                            File.Delete(strQQFriendOnPC)
                        End If

                    Catch
                    End Try
                End If
            End If

            '2、重新下载数据库
            If Not File.Exists(strDBQQOnPC) Then
                strPathNew = FormatPath(strQQdbPath)
                If Me.mBackupFolderHelper.DownFromPhone(strQQdbPath, strDBQQOnPC, strPathNew) Then
                    strPathNew = FormatPath(strQQdbPath & "-shm")
                    Me.mBackupFolderHelper.DownFromPhone(strQQdbPath & "-shm", strDBQQOnPC & "-shm", strPathNew)
                    strPathNew = FormatPath(strQQdbPath & "-wal")
                    Me.mBackupFolderHelper.DownFromPhone(strQQdbPath & "-wal", strDBQQOnPC & "-wal", strPathNew)

                    strPathNew = FormatPath(strQQFriendList_v3plist)
                    Me.mBackupFolderHelper.DownFromPhone(strQQFriendList_v3plist, strQQFriendOnPC, strPathNew)

                    IniSetting.SetMMDate(dateDBPhone)
                Else
                    state = WeixinState.DownloadDBFailed
                End If
            End If

            Me.mStrDBQQOnPC = strDBQQOnPC

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownLoadQQDB")
        End Try
DO_EXIT:
        Return state
    End Function

    Private Sub bgwLoadFriend_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLoadFriend.RunWorkerCompleted
        Try
            If Me.IsDisposed = True Then
                Return
            End If

            Dim state As QQState = CType(e.Result, QQState)

            If state <> QQState.Succeed Then
                '加载失败显示加载失败
                Me.SetViewStyle(QQViewStyle.Uninstalled)
                Return
            End If

            Me.wbsChat.Navigate(ChatWebPage.WebPagePath)
            Me.SetViewStyle(QQViewStyle.Content)
            Me.mDictFriendInfoEx = Me.SortFriendInfo(Me.mDictFriendInfoEx, SortType.DESC)
            Me.AddDictToDataGridView(mDictFriendInfoEx, False)
            Me.LoadHeadIcon()

            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"

            If Me.mLstChatTable.Count = 0 Then
                Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Button.OpenTutorialEx"), True) '"微信信息获取失败，点击查看教程 >>"
            End If

            '---------删除找回---------
            If IniSetting.GetShowWeChatIsDoFindDate() Then
                Dim isIgnoreNumber As Boolean = False
                If tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Weixin.Button.AutoFindDataEx"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, Me.Language.GetString("App.Button.IgnoreNumber"), isIgnoreNumber, New String() {Me.Language.GetString("Weixin.Button.Recoverdata"), Me.Language.GetString("Weixin.Button.WantExport")}, Me.Language.GetString("Weixin.Button.Reinstall"), tbMessageBox.ToolTipType.Hint) = Windows.Forms.DialogResult.OK Then
                    IniSetting.SetWeChatIsDoFindDate(True)
                Else
                    IniSetting.SetWeChatIsDoFindDate(False)
                End If
                IniSetting.SetShowWeChatIsDoFindDate(isIgnoreNumber)
            End If
            If IniSetting.GetWeChatIsDoFindDate() Then
                Me.FindData()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadFriend_RunWorkerCompleted")
        End Try
    End Sub

    Private Delegate Function AddDictToDataGridViewHandler(ByVal dict As Dictionary(Of String, QQFriendInfo), ByVal isRefresh As Boolean, ByVal IsFindData As Boolean) As List(Of tbDataGridViewRow)
    Private Function AddDictToDataGridView(ByVal dict As Dictionary(Of String, QQFriendInfo), ByVal isRefresh As Boolean, Optional ByVal IsFindData As Boolean = False) As List(Of tbDataGridViewRow)
        Dim rowFindData As New List(Of tbDataGridViewRow)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New AddDictToDataGridViewHandler(AddressOf AddDictToDataGridView), New Object() {dict, isRefresh, IsFindData})
            Else
                Me.mBlnReloadFriendCompleted = False
                Me.dgvFriend.SuspendLayout()
                If Not IsFindData Then
                    Me.dgvFriend.Rows.Clear()
                End If

                For Each item As QQFriendInfo In dict.Values
                    If item.DTLastSendTime = DateTime.MinValue Then
                        Continue For
                    End If

                    Dim row As New tbDataGridViewRow()
                    row.Height = 58

                    Dim strName As String = Utility.ReplaceWinIllegalName(IIf(item.NoteName.Length > 0, item.NoteName, item.NickName) & "")

                    row.CreateCells(Me.dgvFriend, _
                                    False, _
                                    "", _
                                    strName & vbCrLf & Me.OperateLastChatText(item.StrLastSendMsg), _
                                    Me.GetLastChatTime(item.DTLastSendTime) & vbCrLf & " ", _
                                    item.IsFindDate, _
                                    item.StrQQNum)
                    Me.mIsAddRow = True
                    If IsFindData Then
                        '如果是数据找回 先检测是否已经存在如果已经存在要先删除 在添加到最前面
                        Me.FindGridViewRow(item.StrQQNum)
                        Me.dgvFriend.Rows.InsertRange(0, row)
                        rowFindData.Add(row)
                    Else
                        Me.dgvFriend.Rows.Add(row)
                    End If
                    Me.mIsAddRow = False

                    Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                    cellName.tbIconShadow = True
                    cellName.tbIconSize = Me.mAppIconSize
                    cellName.tbIcon = Me.mDefaultHeadIcon
                    Dim isLock As Boolean = False
                    'If item.IsFindDate AndAlso Not IniSetting.IsWeChatShare() Then
                    '    isLock = True
                    'End If
                    cellName.ShowWechatMessageLock = isLock
                    row.Tag = item
                Next
                Me.SetLinkmanMsg()

                Me.dgvFriend.ResumeLayout()
                Me.mBlnReloadFriendCompleted = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
        Return rowFindData
    End Function
    Private Delegate Sub UpdateHeadIconToDataGridViewHandler(ByVal row As tbDataGridViewRow)
    Private Sub UpdateHeadIconToDataGridView(ByVal row As tbDataGridViewRow)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateHeadIconToDataGridViewHandler(AddressOf UpdateHeadIconToDataGridView), row)
        Else
            Try
                Dim Item As QQFriendInfo = CType(row.Tag, QQFriendInfo)

                Dim img As Image = Me.GetHeadIconFromPC(Item)

                If img IsNot Nothing Then
                    Dim cellName As tbDataGridViewTextBoxCell = row.Cells("colName")
                    cellName.tbIcon = img
                    Application.DoEvents()
                End If
            Catch ex As Exception
                Common.LogException(String.Format("colName:{0}  colMD5:{1}  ", row.Cells("colName").Value, row.Cells("colMD5").Value) & ex.ToString(), "UpdateHeadIconToDataGridView")
            End Try
        End If
    End Sub

#End Region

#Region "--- 加载聊天消息 ---"

    '1、获取最新50条
    Private Sub LoadChat(ByVal row As DataGridViewRow)
        If row Is Nothing Then
            Return
        End If

        Dim Item As QQFriendInfo = CType(row.Tag, QQFriendInfo)

        Me.LoadChat(Item.StrQQNum, Item.IsGroup, Item.IsDiscuss, -1, Item.SearchText, Item.SearchTime)
    End Sub

    '2、获取endID以前50条消息（endID为-1时，获取最新50条信息）
    Private Sub LoadChat(ByVal strNum As String, ByVal isGroup As Boolean, ByVal isDiscuss As Boolean, ByVal endID As Integer, ByVal strSearchText As String, ByVal lstDateTime As List(Of DateTime))
        Try
            If Me.mThreadGetChat IsNot Nothing AndAlso Me.mThreadGetChat.ThreadState <> ThreadState.Stopped Then
                Me.mThreadGetChat.Abort()
            End If
        Catch
        End Try

        Me.mThreadGetChat = New Thread(New ParameterizedThreadStart(AddressOf LoadChatInThread))
        With Me.mThreadGetChat
            .IsBackground = True
            .Start(New Object() {strNum, isGroup, isDiscuss, endID, strSearchText, lstDateTime})
        End With
    End Sub

    Private Sub LoadChatInThread(ByVal obj As Object)
        Try
            Dim strNum As String = obj(0)
            Dim isGroup As Boolean = obj(1)
            Dim isDiscuss As Boolean = obj(2)
            Dim intEndID As Integer = obj(3)
            Dim strSearchText As String = obj(4)
            Dim lstDateTime As List(Of DateTime) = obj(5)
            If Not Me.IsContainChatTable(strNum, Me.mDictFriendInfoEx) AndAlso (strNum <> IMExportHelper.UnKnownContact OrElse strNum <> IMExportHelper.UnKnownContact) Then
                Return
            End If

            Dim lstInfos As List(Of QQChatInfo) = Nothing
            Dim type As DataType = DataType.Normal
            If strNum <> IMExportHelper.UnKnownContactEx Then
                If lstDateTime IsNot Nothing Then
                    '按日期查找
                    lstInfos = Me.mQQDB.Get_Chat_Info_ByTime(strNum, intEndID, Me.mDefaultChatCount, lstDateTime, isGroup, isDiscuss)
                Else
                    '按文本查找，或者查找条件为空
                    lstInfos = Me.mQQDB.Get_Chat_Info_ByWhere(strNum, intEndID, Me.mDefaultChatCount, isGroup, isDiscuss, strSearchText)
                End If

                If intEndID > 0 Then
                    type = DataType.GetNews
                End If
            Else
                If intEndID > 0 Then
                    type = DataType.GetNews
                End If
                lstInfos = New List(Of QQChatInfo)

            End If

            If isDiscuss Then
                isGroup = True
            End If
            Me.ShowChatOnWeb(strNum, lstInfos, type, isGroup, strSearchText)
            'If String.IsNullOrEmpty(strSearchText) Then
            '    Me.ShowChatOnWeb(strNum, lstInfos, type, isGroup)
            'Else
            '    ShowSearchChatOnWeb(strNum, lstInfos, type, isGroup, strSearchText)
            'End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadChatInThread")
        End Try
    End Sub

    Private Function GetChatJson(strNum As String, lstInfos As List(Of QQChatInfo), type As DataType, ByVal isGroup As Boolean, Optional ByVal strSearchText As String = "") As String
        Dim writer As New iTong.CoreFoundation.JsonWriter()
        Try
            Dim arrChats As New JsonArray
            Dim dicChat As JsonObject
            Dim iconPath As String = "http://q1.qlogo.cn/g?b=qq&nk={0}&s=100"
            Dim strTypeForWeb As String = String.Empty    '0：文本、表情，1：图片，3：语音，181：小视频，141：网页
            Dim arrHtml5ContentForWeb As JsonArray
            Dim strMsgForWeb As String = String.Empty
            Dim strTimeForWeb As String = String.Empty
            Dim startTime As DateTime = DateTime.MinValue

            For Each item As QQChatInfo In lstInfos
                arrHtml5ContentForWeb = Nothing
                strMsgForWeb = item.StrMessage
                Dim dicContent As JsonObject = Nothing
                '0：文本、表情，1：图片，3：语音，181：小视频，141：网页
                'web页 0文本，1语音，2图片，3网页
                strTypeForWeb = "0"
                Select Case item.SCType
                    Case SociaChatType.Text  '"0"
                        strTypeForWeb = "0"
                        strMsgForWeb = item.StrMessage 'Me.GetChatMediaPathFromPC(item, True, MediaType.Picture)

                        If Not String.IsNullOrEmpty(strSearchText) AndAlso strMsgForWeb.Contains(strSearchText) Then
                            strMsgForWeb = strMsgForWeb.Replace(strSearchText, "~" & strSearchText & "~(background-color:#ffff00;color:#fffff;)")
                        End If

                    Case SociaChatType.SpecialExpression
                        strTypeForWeb = "0"
                        strMsgForWeb = item.StrMessage

                    Case SociaChatType.Picture  '"1"
                        strTypeForWeb = "2"
                        strMsgForWeb = Me.GetChatMediaPathFromPC(item, True, QQMediaType.Picture)
                        dicContent = Me.GetImageContent(item.StrMessage, strMsgForWeb)
                        If dicContent IsNot Nothing Then
                            strTypeForWeb = 6
                        End If

                    Case SociaChatType.Video  '"1"
                        strTypeForWeb = "5"
                        strMsgForWeb = Me.GetChatMediaPathFromPC(item, True, QQMediaType.Video)

                    Case SociaChatType.Audio  '"3"
                        strTypeForWeb = "1"
                        strMsgForWeb = GetChatVoiceLength(item.StrMessage, False)

                    Case SociaChatType.SmallVideo  '"181"
                        strTypeForWeb = "5"
                        strMsgForWeb = Me.GetChatMediaPathFromPC(item, True, QQMediaType.Video)

                    Case SociaChatType.File
                        strTypeForWeb = "0"
                        Dim strFileName() As String = item.StrMessage.Split("|")
                        If strFileName IsNot Nothing AndAlso strFileName.Length > 0 Then
                            strMsgForWeb = String.Format("[{0}]", Me.Language.GetString("WeChat.Lable.File")) & strFileName(0)
                        Else
                            strMsgForWeb = String.Format("[{0}]", Me.Language.GetString("WeChat.Lable.File")) & item.StrMessage
                        End If

                    Case SociaChatType.webpage '"141"
                        strTypeForWeb = "3"
                        arrHtml5ContentForWeb = Nothing 'Me.GetWebPageJsonArray(strMsgForWeb) '网页json

                        Try
                            '兼容链接（只有title和url）
                            If arrHtml5ContentForWeb Is Nothing OrElse arrHtml5ContentForWeb.Count = 0 Then
                                Dim webLinkJsonArray As JsonArray = Me.GetWebLinkJsonArray(strMsgForWeb)

                                If webLinkJsonArray IsNot Nothing AndAlso webLinkJsonArray.Count > 0 Then
                                    Dim dicWebPage As JsonObject = webLinkJsonArray(0)
                                    Dim strTitle As String = CType(dicWebPage("title"), JsonString).Value
                                    Dim strUrl As String = CType(dicWebPage("url"), JsonString).Value

                                    If Not String.IsNullOrEmpty(strTitle) AndAlso Not String.IsNullOrEmpty(strUrl) Then
                                        strTypeForWeb = "0"
                                        arrHtml5ContentForWeb = Nothing

                                        'strMsgForWeb = String.Format("<span class=""title left""><a href=""weixin://navigate?url={0}""><font><u>{1}</u></font></a></span>", strUrl, strTitle)
                                        strMsgForWeb = String.Format("[{1}]({0})", strUrl, strTitle)

                                    Else
                                        arrHtml5ContentForWeb = Nothing
                                        'strFromForWeb = "1"
                                        strTypeForWeb = "0"
                                        strMsgForWeb = "[" & Me.Language.GetString("Weixin.Item.Expression") & "]"  '"[动画表情]"

                                        'If Item.IsDelete Then
                                        '    strMsgForWeb = "~" & strMsgForWeb & "~(background-color:#fffff;color:#FF0000;)"
                                        'End If

                                    End If
                                Else
                                    strTypeForWeb = "0"
                                End If
                            End If
                        Catch ex As Exception
                            Common.LogException(ex.ToString(), "GetWebLinkJsonArray2")
                        End Try
                    Case SociaChatType.SystemMessages  '"10000"
                        strTypeForWeb = "4"
                End Select

                If isGroup Then
                    If item.StrSendUin = Me.mCurrentUserNum Then
                        item.IntDes = 1
                    End If
                End If


                'If item.Des = 1 Then
                '    strNickName = Me.btnWeCahtUsers.Text
                'Else
                '    If Me.mDictFriendInfoEx.ContainsKey(item.SendUin) Then
                '        Dim fInfo As FriendInfo = Me.mDictFriendInfoEx(item.SendUin)
                '        strNickName = IIf(fInfo.NoteName.Length = 0, fInfo.UsrName, fInfo.NoteName)
                '    End If
                'End If
                'If strNickName.Length = 0 Then
                '    strNickName = item.UsrName
                'End If

                dicChat = New JsonObject()
                dicChat.Add("from", item.IntDes) '0：对方，1：自己
                dicChat.Add("type", strTypeForWeb)
                dicChat.Add("user", item.StrNickName)
                Dim strCode As String = Me.mCurrentUserNum
                If item.IntDes = 0 Then
                    strCode = item.StrSendUin
                End If
                dicChat.Add("icon", String.Format(iconPath, strCode))

                If arrHtml5ContentForWeb IsNot Nothing Then
                    dicChat.Add("content", arrHtml5ContentForWeb)
                ElseIf dicContent IsNot Nothing Then
                    dicChat.Add("content", dicContent)
                Else
                    dicChat.Add("content", strMsgForWeb)
                End If

                strTimeForWeb = String.Empty
                If startTime = DateTime.MinValue OrElse item.DTCreateTime.Subtract(startTime).TotalMinutes > 5 Then
                    startTime = item.DTCreateTime
                    strTimeForWeb = item.DTCreateTime.ToString("MM-dd HH:mm:ss")
                End If

                dicChat.Add("time", strTimeForWeb)
                dicChat.Add("friend", item.StrUsrName)
                dicChat.Add("id", item.IntID.ToString())
                '标志是否删除 1：是  0：否  
                dicChat.Add("delete", IIf(item.IsDelete, 1, 0))

                arrChats.Add(dicChat)
            Next

            '添加语言包
            Dim dicLanguage As New JsonObject
            dicLanguage.Add("weixincopy", Language.GetString("Common.Button.Copy"))
            dicLanguage.Add("weixinexport", Language.GetString("Media.Button.Export"))
            dicLanguage.Add("weixingetmore", Language.GetString("Weixin.Button.GetMoreNews"))
            dicLanguage.Add("weixingetcontext", Language.GetString("Weixin.Button.GetContextNews"))

            Dim dicOption As New JsonArray
            dicOption.Add("weixincopy")
            dicOption.Add("weixinexport")

            Dim dicConfig As New JsonObject
            dicConfig.Add("bgcolor", "#fff")

            Dim dicChats As New JsonObject
            dicChats.Add("type", type.GetHashCode.ToString())
            dicChats.Add("data", arrChats)
            dicChats.Add("language", dicLanguage)
            dicChats.Add("msgOption", dicOption)
            dicChats.Add("config", dicConfig)
            dicChats.Write(writer)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetChatJson")
        End Try
        Return writer.ToString()
    End Function

    Private Function GetImageContent(ByVal strContent As String, ByVal strImgPath As String) As JsonObject
        Dim dicContent As JsonObject = Nothing
        Try
            If Not String.IsNullOrEmpty(strContent) AndAlso strContent.Contains("<img>") AndAlso strContent.Contains("</img>") Then
                Dim indexImgStar As Integer = strContent.IndexOf("<img>", 0)
                Dim indexImgEnd As Integer = strContent.IndexOf("</img>", 0)
                Dim imgText As String = strContent.Substring(indexImgStar, indexImgEnd - indexImgStar + 6)
                strContent = strContent.Replace(imgText, "")
                If strContent.Length > 0 Then
                    dicContent = New JsonObject
                    dicContent.Add("text", strContent)
                    dicContent.Add("img", strImgPath)
                End If
            End If
        Catch ex As Exception

        End Try
        Return dicContent
    End Function

#End Region

#Region "--- Web 操作---"

    Private Function GetChatForWeb(ByVal url As String) As QQChatInfo
        Dim result As QQChatInfo = Nothing
        Dim strUserId As String = Utility.GetParamValueFromQuery("friend", url)
        Dim strID As String = Utility.GetParamValueFromQuery("id", url)

        If String.IsNullOrEmpty(strUserId) OrElse String.IsNullOrEmpty(strID) Then
            Return result
        End If

        For Each Item As QQChatInfo In Me.mLstCurrentChatInfo
            If String.Compare(Item.StrUsrName, strUserId, True) = 0 AndAlso _
               String.Compare(Item.IntID.ToString(), strID, True) = 0 Then

                result = Item

                Exit For
            End If
        Next

        Return result
    End Function

    Private Sub GetNews(ByVal url As String)
        Dim strUsrName As String = Utility.GetParamValueFromQuery("friend", url)
        Dim strID As String = Utility.GetParamValueFromQuery("id", url)
        Dim strSearchText As String = String.Empty
        Dim lstDatetime As List(Of DateTime)
        If String.IsNullOrEmpty(strUsrName) OrElse String.IsNullOrEmpty(strID) Then
            Return
        End If
        Dim isGroup As Boolean = False
        Dim isDiscuss As Boolean = False
        Try
            If Me.mDictFriendInfo.ContainsKey("troop") Then
                Dim lstFriend As List(Of QQFriendInfo) = Me.mDictFriendInfo("troop")
                If lstFriend IsNot Nothing AndAlso lstFriend.Count > 0 Then
                    For Each fInfo As QQFriendInfo In lstFriend
                        If fInfo.StrQQNum = strUsrName Then
                            isGroup = True
                            Exit For
                        End If
                    Next
                End If
            End If
            If Me.mDictFriendInfo.ContainsKey("discuss") Then
                Dim lstFriend As List(Of QQFriendInfo) = Me.mDictFriendInfo("discuss")
                If lstFriend IsNot Nothing AndAlso lstFriend.Count > 0 Then
                    For Each fInfo As QQFriendInfo In lstFriend
                        If fInfo.StrQQNum = strUsrName Then
                            isDiscuss = True
                            Exit For
                        End If
                    Next
                End If
            End If
            If Me.mDictFriendInfoEx.ContainsKey(strUsrName) Then
                Dim info As QQFriendInfo = Me.mDictFriendInfoEx(strUsrName)
                strSearchText = info.SearchText
                lstDatetime = info.SearchTime
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetNews_1")
        End Try

        Me.LoadChat(strUsrName, isGroup, isDiscuss, strID, strSearchText, lstDatetime)
    End Sub

    Private Sub ShowPhoto(ByVal url As String)
        If Not Me.mBackupMediaSucceed Then
            tbMessageBox.Show(Me, Me.Language.GetString("Recovery.Message.GetMediaDataFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If Me.CheckChargeOverdue Then
            Return
        End If

        Dim chat As QQChatInfo = Me.GetChatForWeb(url)
        If chat IsNot Nothing Then
            If chat.SCType = SociaChatType.SmallVideo Then 'String.Compare(chat.Type, "181") = 0
                Me.ChangeStateText(Me.Language.GetString("Weixin.Lable.SmallVideo"))        '"小视频"
                Return
            End If
            '下载图片
            Dim lstInfos As New List(Of QQChatInfo)
            lstInfos.Add(chat)

            'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, False, QQMediaType.Picture, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)

            Dim para As New IMExportPara()
            para.ExportType = ExportType.ExportOnlyPhoto
            para.SaveFolder = Me.mPathQQOnPC_Temp

            Dim friendInfo As New QQFriendInfo()
            friendInfo.UsrName = chat.StrSendUin

            Me.mHelper.DownloadQQFiles(para, chat.StrNickName, chat)

            '获取大图
            Dim imgPath As String = Me.GetChatMediaPathFromPC(chat, False, QQMediaType.Picture)

            '没有可供预览的大图
            If File.Exists(imgPath) Then
                '展示图片
                Dim showPicFrm As New tbImageViewForm()
                showPicFrm.SrcPicture = Utility.GetImageFormFile(imgPath)
                showPicFrm.Icon = My.Resources.iTong
                showPicFrm.ViewType = ViewType.FromPC
                showPicFrm.ShowListView = False
                showPicFrm.HideButton = ToolBarButtonType.Main Or ToolBarButtonType.Delete Or ToolBarButtonType.WeiBo Or ToolBarButtonType.Prev Or ToolBarButtonType.Next
                showPicFrm.ShowDialog()
            Else
                Me.ChangeStateText(Me.Language.GetString("Weixin.Message.NoLargeImage")) '"没有可供预览的大图"
            End If
        End If
    End Sub

    Private Sub ExportMedia(ByVal url As String)
        If Not Me.mBackupMediaSucceed Then
            tbMessageBox.Show(Me, Me.Language.GetString("Recovery.Message.GetMediaDataFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        If Me.CheckChargeOverdue() Then
            Return
        End If

        Dim chat As QQChatInfo = Me.GetChatForWeb(url)
        If chat.SCType = SociaChatType.SmallVideo Then ' String.Compare(chat.Type, "181") = 0
            Me.DoExportVideo(chat)

        ElseIf chat.SCType = SociaChatType.Audio Then
            Me.DoExportAudio(chat)

        Else
            Me.DoExportPhoto(chat)

        End If
    End Sub

    Private Sub DoExportPhoto(ByVal chat As QQChatInfo)
        If chat IsNot Nothing Then
            Dim fileDlg As SaveFileDialog = New SaveFileDialog()

            fileDlg.InitialDirectory = IniSetting.GetWeixinSaveFolder
            fileDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & " (*.png)|*.png"
            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = chat.DTCreateTime.ToString("yyyyMMdd_HHmmss")

            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName
                IniSetting.SetWeixinSaveFolder(Path.GetDirectoryName(strFilePath))
                '下载图片
                'Dim lstInfos As New List(Of QQChatInfo)
                'lstInfos.Add(chat)

                'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, False, QQMediaType.Picture, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)
                Dim para As New IMExportPara()
                para.ExportType = ExportType.ExportOnlyPhoto

                Me.mHelper.DownloadQQFiles(para, chat.StrNickName, chat)

                '获取图片路径
                Dim imgPath As String = Me.GetChatMediaPathFromPC(chat, False, QQMediaType.Picture)
                If String.IsNullOrEmpty(imgPath) Then
                    imgPath = Me.GetChatMediaPathFromPC(chat, False, QQMediaType.Picture)
                End If

                '拷贝到指定的目录
                If File.Exists(imgPath) Then
                    File.Copy(imgPath, strFilePath)

                    Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                    Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！

                    Me.ChangeStateText(strText)
                End If

            End If
        End If
    End Sub

    Private Sub DoExportVideo(ByVal chat As QQChatInfo)
        If chat IsNot Nothing Then
            Dim fileDlg As SaveFileDialog = New SaveFileDialog()

            fileDlg.InitialDirectory = IniSetting.GetWeixinSaveFolder
            fileDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & " (*.mp4)|*.mp4"
            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = chat.DTCreateTime.ToString("yyyyMMdd_HHmmss")

            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName
                IniSetting.SetWeixinSaveFolder(Path.GetDirectoryName(strFilePath))

                '下载图片
                'Dim lstInfos As New List(Of QQChatInfo)
                'lstInfos.Add(chat)

                'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, False, QQMediaType.Video, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)
                Dim para As New IMExportPara()
                para.ExportType = ExportType.ExportOnlyVideo
                para.SaveFolder = Me.mPathQQOnPC_Temp

                Me.mHelper.DownloadQQFiles(para, chat.StrNickName, chat)

                '获取图片路径
                Dim imgPath As String = Me.GetChatMediaPathFromPC(chat, False, QQMediaType.Video)
                If String.IsNullOrEmpty(imgPath) Then
                    'imgPath = Me.GetChatVideoPathFromPC(chat, True)
                    '"无法导出小视频，请到微信点击查看后再导出。"
                    Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CanotExportSmallVideo"))
                    Return
                End If

                '拷贝到指定的目录
                If File.Exists(imgPath) Then
                    File.Copy(imgPath, strFilePath)

                    Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                    Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！

                    Me.ChangeStateText(strText)
                End If

            End If
        End If
    End Sub

    Private Sub DoExportAudio(ByVal chat As QQChatInfo)
        If chat IsNot Nothing Then
            Dim fileDlg As SaveFileDialog = New SaveFileDialog()

            fileDlg.InitialDirectory = IniSetting.GetWeixinSaveFolder
            fileDlg.Filter = Me.Language.GetString("Weixin.OpenFileDialog.AmrFile") & " (*.wav)|*.wav"
            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = String.Format("{0}_{1}", chat.DTCreateTime.ToString("yyyyMMdd_HHmmss"), chat.IntCreateTime)

            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName
                IniSetting.SetWeixinSaveFolder(Path.GetDirectoryName(strFilePath))

                '下载图片
                'Dim lstInfos As New List(Of QQChatInfo)
                'lstInfos.Add(chat)

                'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, False, QQMediaType.Audio, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)

                Dim para As New IMExportPara()
                para.ExportType = ExportType.ExportOnlyAudio
                para.SaveFolder = Me.mPathQQOnPC_Temp

                Me.mHelper.DownloadQQFiles(para, chat.StrNickName, chat)

                '获取图片路径
                Dim audioPath As String = Me.GetChatMediaPathFromPC(chat, False, QQMediaType.Audio)
                If String.IsNullOrEmpty(audioPath) Then
                    Me.ChangeStateText(Me.Language.GetString("Contact.Message.ExportFailure"))
                    Return
                End If

                '拷贝到指定的目录
                If File.Exists(audioPath) Then
                    File.Copy(audioPath, strFilePath)
                    Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                    Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！
                    Me.ChangeStateText(strText)
                End If

            End If
        End If
    End Sub

    Private Sub PlayVoice(ByVal url As String)
        If Not Me.mBackupMediaSucceed Then
            tbMessageBox.Show(Me, Me.Language.GetString("Recovery.Message.GetMediaDataFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        If Me.CheckChargeOverdue Then
            Return
        End If

        Dim chat As QQChatInfo = Me.GetChatForWeb(url)

        If chat Is Nothing Then
            Return
        End If

        'QQ 低于1s不能发送
        'win7下，低于一秒的不去播放
        'If Me.mIsWin7 Then
        '    Dim singleLength As Single = Me.GetChatVoiceLength(chat.Message, False)  '获取时长的最大整数
        '    If singleLength <= 1.3 Then
        '        Return
        '    End If
        'End If

        '下载语音
        Dim dictChatInfo As New Dictionary(Of Integer, QQChatInfo)
        dictChatInfo.Add(chat.IntID, chat)

        'Dim lstChatInfo As New List(Of QQChatInfo)
        'lstChatInfo.Add(chat)
        'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstChatInfo, False, QQMediaType.Audio, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)

        Dim para As New IMExportPara()
        para.ExportType = ExportType.ExportOnlyVideo
        para.SaveFolder = Me.mPathQQOnPC_Temp

        Me.mHelper.DownloadQQFiles(para, chat.StrNickName, chat)

        '获取语音路径（wav格式）
        Dim audioPath As String = Me.GetChatMediaPathFromPC(chat, False, QQMediaType.Audio)

        If File.Exists(audioPath) Then
            Dim blnNeedPlay As Boolean = True

            If Me.mPlayer.PlayState = WMPPlayState.wmppsPlaying Then
                Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL

                'web页面暂停之前的
                If String.Compare(strFilePlaying, audioPath, True) = 0 Then
                    blnNeedPlay = False         '如果正在播放，播放的是同一首歌，则暂停
                Else
                    blnNeedPlay = True          '如果是新的语音，则播放新的语音
                End If

                Me.mPlayer.Pause()
                Me.ShowAudioWebView(strFilePlaying, False)
            End If

            If blnNeedPlay Then
                'Debug.Print("播放")
                Me.mPlayer.GetUserPlaylist(Me.Name).Clear()
                Me.mPlayer.GetUserPlaylist(Me.Name).Add(audioPath)
                'Me.mPlayer.GetUserPlaylist(Me.Name).SetItem(0, strWavFile)
                Me.mPlayer.CurrentUserPlaylist = Me.mPlayer.GetUserPlaylist(Me.Name)
                Me.mPlayer.Play()
                Me.ShowAudioWebView(chat.IntID.ToString(), True)
                Me.mCurrentPlayId = chat.IntID.ToString()
            End If
        Else
            Dim strError As String = Me.Language.GetString("Weixin.Message.PlayFail") & " " & String.Format(Me.Language.GetString("Download.Message.ContactMe"), Utility.TongbuContact, Utility.TongbuTel)     '"如有疑问，请联系客服QQ：{0}    电话：{1}"
            Me.ChangeStateText(strError) '"播放语音失败！"
        End If
    End Sub

    Private mCurrentPlayId As String = String.Empty
    Private Sub OnPlayStateChange(ByVal sender As Object, ByVal e As MediaPlayStateChangedEventArgs)
        If e.NewPlayState = WMPPlayState.wmppsStopped Then
            If Me.mPlayer.CurrentMedia IsNot Nothing Then
                Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL
                Me.mPlayer.GetUserPlaylist(Me.Name).Remove(strFilePlaying)
                If Me.mCurrentPlayId.Length > 0 Then
                    Me.ShowAudioWebView(Me.mCurrentPlayId, False)
                End If
            End If
        End If
    End Sub

    Private Sub Navigate(ByVal url As String)
        Dim startText As String = "?url="
        Dim startIndex As Integer = url.IndexOf(startText)
        Dim strUrl As String = url.Substring(startIndex + startText.Length).Trim()

        If Not String.IsNullOrEmpty(strUrl) Then
            Dim strPrefix As String = "http://"
            If Not strUrl.StartsWith(strPrefix) Then
                strUrl = strPrefix & strUrl
            End If

            Common.OpenExplorer(strUrl)
        End If
    End Sub

#End Region

#Region "--- 下载展示头像 ---"

    Private Sub LoadHeadIcon(Optional ByVal rowFindData As List(Of tbDataGridViewRow) = Nothing)
        Try
            If Me.mThreadLoadHeadIcon IsNot Nothing AndAlso Me.mThreadLoadHeadIcon.ThreadState <> ThreadState.Stopped Then
                Me.mThreadLoadHeadIcon.Abort()
            End If
        Catch
        End Try

        Dim objpara As Object() = New Object() {rowFindData}
        Me.mThreadLoadHeadIcon = New Thread(AddressOf LoadHeadIconInThread)
        With Me.mThreadLoadHeadIcon
            .IsBackground = True
            .Start(objpara)
        End With
    End Sub

    Private Sub LoadHeadIconInThread(ByVal obj As Object)
        Try
            Dim rowFindData As List(Of tbDataGridViewRow) = CType(obj(0), List(Of tbDataGridViewRow))

            If rowFindData Is Nothing Then
                rowFindData = New List(Of tbDataGridViewRow)
                For Each row As tbDataGridViewRow In Me.dgvFriend.Rows
                    rowFindData.Add(row)
                Next
            End If

            Dim strPathOnPC As String = String.Format("{0}\Usr", Me.mPathQQOnPC_Cache)
            Folder.CheckFolder(strPathOnPC)
            For Each row As tbDataGridViewRow In rowFindData
                Try
                    Dim Item As QQFriendInfo = CType(row.Tag, QQFriendInfo)
                    Dim isGroup As Boolean = Item.IsGroup
                    Dim strUrl As String = ""
                    If isGroup Then
                        strUrl = String.Format("http://p.qlogo.cn/gh/{0}/{0}/100", Item.StrQQNum)
                    ElseIf Item.IsDiscuss Then
                        strUrl = String.Format("http://q1.qlogo.cn/g?b=qq&nk={0}&s=100", 0)
                    Else
                        strUrl = String.Format("http://q1.qlogo.cn/g?b=qq&nk={0}&s=100", Item.StrQQNum)
                    End If

                    Dim strFileOnPC As String = String.Empty
                    Dim strFileName = String.Format("{0}.pic_usr", Item.StrQQNum)
                    strFileOnPC = String.Format("{0}\{1}", strPathOnPC, strFileName)

                    Common.DownloadImage(strUrl, 20000, strFileOnPC)

                    Me.UpdateHeadIconToDataGridView(row)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "LoadHeadIconInThread")
                End Try
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadHeadIconInThread")
        End Try
        'Me.ChangeButtonRefreshEnable(True)
    End Sub

#End Region

#Region "--- 导出 ---"

    Private Function CheckExport(Optional ByVal blnRecoveryData As Boolean = False) As IMExportPara
        Dim para As IMExportPara = Nothing

        Try
            If Me.dgvFriend.Rows.Count = 0 Then
                '联系人为空。
                tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                GoTo DoExit
            End If

            If Me.dgvFriend Is Nothing OrElse Me.dgvFriend.SelectedRows.Count <= 0 Then
                tbMessageBox.Show(Me.mApplication, _
                                 Me.Language.GetString("WeChat.Button.SelectContact"), _
                                  Me.Language.GetString("Common.Info"), _
                                  MessageBoxButtons.OK, _
                                   MessageBoxIcon.Warning)
                '"请选择要导出的联系人"
                GoTo DoExit
            End If

            para = New IMExportPara()

            If blnRecoveryData Then
                For Each row As tbDataGridViewRow In Me.dgvFriend.SelectedRows
                    Dim rowitem As QQFriendInfo = CType(row.Tag, QQFriendInfo)
                    If rowitem.IsFindDate Then
                        para.ListExport.Add(rowitem)
                    End If
                Next
                
            Else
                For Each row As tbDataGridViewRow In Me.dgvFriend.SelectedRows
                    para.ListExport.Add(row.Tag)
                Next
            End If

            If Me.pnlTimeSelect.Visible Then
                para.DateStart = DateTime.Parse(Me.dtpStart.Value.ToString("yyyy-MM-dd 00:00:01"))
                para.DateEnd = DateTime.Parse(Me.dtpEnd.Value.ToString("yyyy-MM-dd 23:59:59"))
            Else
                para.DateStart = DateTime.Parse("1970-01-01 00:00:01")
                para.DateStart = DateTime.Parse("2999-01-01 00:00:01")
            End If

            para.SaveFolder = Me.mPathQQOnPC_Temp

            'obj = New Object() {Me.mPathContainer, Me.mUIFileSharingEnabled, Me.mAfcHandler, Me.mIsFromBackup, False, Me.mQQSKU, Me.mDictSha1, Me.mQQBackupCache, Me.mPathOnPhone, Me.mPathQQOnPC_Temp, Me.mListDir, Me.mLstMBFileRecord}

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckExport")
        End Try

DoExit:
        Return para
    End Function

    Private Sub ExportMediaByType(ByVal strType As String)
        If Not Me.mBackupMediaSucceed Then
            tbMessageBox.Show(Me, Me.Language.GetString("Recovery.Message.GetMediaDataFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        If Me.CheckChargeOverdue() Then
            Return
        End If

        Dim para As IMExportPara = Me.CheckExport()
        If para Is Nothing Then
            Return
        End If

        Me.ShowExportProgressNew(para)

        'Dim lstRows As List(Of DataGridViewRow) = Nothing
        'Dim lstTime As New List(Of DateTime)
        'Dim strDeviceName As String = ""
        'Dim obj As Object() = Nothing

        'If Not CheckExport(lstRows, lstTime, strDeviceName, obj) Then
        '    Return
        'End If

        'Dim strPath As String = Me.mWeixinImportOrExportHelper.ExportMedia(lstRows, lstTime, strDeviceName, strType, Me.btnWeCahtUsers.Text, obj, Me.mCurrentUserNum, Me.mBackupFolderHelper, Nothing)
        'If Not String.IsNullOrEmpty(strPath) Then
        '    ShowExportProgressNew(strPath)
        'End If
    End Sub

    Private Sub NewExport(eType As ExportType, Optional ByVal blnRecoveryData As Boolean = False)

        If Me.CheckChargeOverdue() Then
            Return
        End If

        'Dim lstRows As List(Of DataGridViewRow) = Nothing
        'Dim lstTime As New List(Of DateTime)
        'Dim strDeviceName As String = ""
        'Dim obj As Object() = Nothing

        'If Not CheckExport(lstRows, lstTime, strDeviceName, obj, blnRecoveryData) Then
        '    Return
        'End If

        Dim para As IMExportPara = Me.CheckExport(blnRecoveryData)
        If para Is Nothing Then
            Return
        End If

        Dim isAudioConvert As Boolean = True

        Dim lstType As New List(Of ExportType)
        If eType = ExportType.ExportToAll Then
            lstType.Add(ExportType.ExportToTxt)
            lstType.Add(ExportType.ExportToExcel)
            lstType.Add(ExportType.ExportToHtml)

        ElseIf eType = ExportType.ExportToSetting Then
            If IniSetting.GetWCExportFormatTxt() Then
                lstType.Add(ExportType.ExportToTxt)
            End If
            If IniSetting.GetWCExportFormatExcel() Then
                lstType.Add(ExportType.ExportToExcel)
            End If
            If IniSetting.GetWCExportFormatHtml() Then
                lstType.Add(ExportType.ExportToHtml)
            End If

        Else
            lstType.Add(eType)
        End If

        eType = ExportType.None
        For Each t As ExportType In lstType
            eType = eType Or t
        Next

        Me.ShowExportProgressNew(para)

        'Dim strPath As String = ""
        'Me.mWeixinImportOrExportHelper.mCurrentQQNum = Me.mCurrentUserNum
        'strPath = Me.mWeixinImportOrExportHelper.ExportWhatsAppAllInfo(lstRows, Me.mDictDelChatInfo, lstTime, Me.btnWeCahtUsers.Text, strDeviceName, lstType, obj, blnRecoveryData, Me.mBackupDeviceUDID)

        'If Not String.IsNullOrEmpty(strPath) Then
        '    ShowExportProgressNew(strPath)
        'End If
    End Sub

    Private Function CheckChargeOverdue() As Boolean
        Dim isRelust As Boolean = False
        Try
            If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
                isRelust = True
                If Not Me.mChargeHelper.mChargeInfo.Registered OrElse Me.mChargeHelper.mChargeInfo.Overdue OrElse Not Me.mChargeHelper.CheckDeviceRegisted(Me.mCurrentIdentify) Then
                    Me.mChargeHelper.CheckIsChargeApp(FunctionKey.Weixin, 0, Me.mCurrentIdentify)
                End If
                If Me.mChargeHelper.mChargeInfo.Registered AndAlso Not Me.mChargeHelper.mChargeInfo.Overdue AndAlso Me.mChargeHelper.CheckDeviceRegisted(Me.mCurrentIdentify) Then
                    isRelust = False
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckChargeOverdue")
        End Try
Do_Exit:
        Return isRelust
    End Function

    Private Sub ShowExportProgressNew(para As IMExportPara)
        Try
            Dim frmExport As New frmExportProgress(Me.mApplication, Me.mHelper, para)
            frmExport.ShowDialog(Me)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowExportProgress")
        End Try
    End Sub

#End Region

#Region "--- 可以抽公共方法 ---"

    Private Function GetHeadIconFromPC(ByVal item As QQFriendInfo) As Image
        Dim img As Image = Nothing
        Dim imgPath As String = String.Format("{0}\Usr\{1}.pic_usr", Me.mPathQQOnPC_Cache, item.StrQQNum)

        If File.Exists(imgPath) Then
            img = Utility.GetImageFormFile(imgPath, True)
        End If

        '如果图片无法使用就删除掉，下次进来还会重新加载
        If img Is Nothing Then
            Try
                If Common.IsTestMode() AndAlso File.Exists(imgPath) Then
                    File.Delete(imgPath)
                End If
            Catch ex As Exception
            End Try

            img = Me.mDefaultHeadIcon.Clone()
            'Common.LogException("imgPath：" & imgPath, "GetHeadIconFromPC")
        Else
            'Common.LogException("imgNotNothing  imgPath：" & imgPath, "GetHeadIconFromPC")
        End If

        Return img
    End Function

    '展示搜索结果
    Private Sub ShowSearchChatOnWeb(ByVal strUsrName As String, _
                                                            ByVal lstInfos As List(Of QQChatInfo), _
                                                            ByVal type As DataType, ByVal isGroup As Boolean, _
                                                            ByVal strSearchText As String, _
                                                            Optional ByVal blnSearchAll As Boolean = False)

        Me.GetDeleteChatInfo(strUsrName, lstInfos, DataType.Normal, strSearchText)

        If Not blnSearchAll Then
            Me.mLstCurrentChatInfo = lstInfos

            If lstInfos Is Nothing OrElse lstInfos.Count = 0 Then
                Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.SearchNoResult")) '"搜索不到结果"
                Return
            End If
        Else
            If lstInfos Is Nothing OrElse lstInfos.Count = 0 Then
                Return
            End If

            If Me.mLstCurrentChatInfo Is Nothing Then
                Me.mLstCurrentChatInfo = New List(Of QQChatInfo)
            End If

            For Each ItemTemp As QQChatInfo In lstInfos
                Me.mLstCurrentChatInfo.Add(ItemTemp)
            Next
        End If

        '下载图片（只下载缩略图，查看大图时，才下载大图。）
        'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, True, QQMediaType.Picture, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)

        '下载小视频
        'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, True, QQMediaType.Video, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)

        Dim para As New IMExportPara()
        para.ExportType = ExportType.ExportOnlyPhoto Or ExportType.ExportOnlyVideo

        For Each chat As QQChatInfo In lstInfos
            Me.mHelper.DownloadQQFiles(para, strUsrName, chat)
        Next

        Dim strJson As String = Me.GetChatJson(strUsrName, lstInfos, DataType.Search, isGroup, strSearchText)
        Me.ShowChatOnWebPage(strJson)
        Me.pnlLoadData.Visible = False
    End Sub

    '展示连贯的聊天记录（不展示搜索结果）
    Private Sub ShowChatOnWeb(ByVal strNum As String, ByVal lstInfos As List(Of QQChatInfo), ByVal type As DataType, ByVal isGroup As Boolean, Optional ByVal strSearchText As String = "")

        Dim iCount As Integer = Me.GetDeleteChatInfo(strNum, lstInfos, type, strSearchText)
        If iCount > 0 Then
            Dim strText = String.Format(Me.Language.GetString("Weixin.Lable.CurrentFindRecords"), iCount) '"红色标识为找回数据，当前联系人找回[{0}]条"
            Dim colSelect As Color = Color.FromArgb(166, 55, 0)
            Me.Selection(strText, Me.rtxtPrompt, "[", "]", colSelect, HorizontalAlignment.Left)
            Me.SetPnlCount(True)
        Else
            Me.SetPnlCount(False)
        End If

        ''下载图片（只下载缩略图，查看大图时，才下载大图。）
        'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, True, QQMediaType.Picture, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)

        ''下载小视频
        'Me.mWeixinImportOrExportHelper.DownloadChatMediaFromPhone(lstInfos, True, QQMediaType.Video, Me.mCurrentUserNum, Me.mBackupFolderHelper, Me.mPathQQOnPC_Temp)

        Dim para As New IMExportPara()
        para.ExportType = ExportType.ExportOnlyPhoto Or ExportType.ExportOnlyVideo
        para.SaveFolder = Me.mPathQQOnPC_Temp

        For Each item As QQChatInfo In lstInfos
            Me.mHelper.DownloadQQFiles(para, strNum, item)
        Next


        Dim strJson As String = Me.GetChatJson(strNum, lstInfos, type, isGroup, strSearchText)
        Me.ShowChatOnWebPage(strJson)
        Select Case type
            Case DataType.Normal
                If lstInfos.Count < Me.mDefaultChatCount Then
                    ChatWebPage.ShowMoreButton(Me.wbsChat, False)
                Else
                    ChatWebPage.ShowMoreButton(Me.wbsChat, True)
                End If

            Case DataType.GetNews
                If lstInfos.Count < Me.mDefaultChatCount Then
                    ChatWebPage.ShowMoreButton(Me.wbsChat, False)
                Else
                    ChatWebPage.ShowMoreButton(Me.wbsChat, True)
                End If

                If Me.mLstCurrentChatInfo Is Nothing Then
                    Me.mLstCurrentChatInfo = New List(Of QQChatInfo)
                End If

                For Each Item As QQChatInfo In Me.mLstCurrentChatInfo
                    lstInfos.Add(Item)
                Next

            Case DataType.TimeLocate
                ChatWebPage.ShowMoreButton(Me.wbsChat, True)

            Case DataType.GetContextNews
                ChatWebPage.ShowMoreButton(Me.wbsChat, True)

        End Select
        '赋给全局变量，复制当前页时使用。
        Me.mLstCurrentChatInfo = lstInfos
        ChangeLoadDataVisible(False)
    End Sub

    Private Function GetDeleteChatInfo(strUsrName As String, ByRef lstInfos As List(Of QQChatInfo), type As DataType, Optional ByVal strSearchText As String = "") As Integer
        Dim iCount As Integer = 0
        Try
            Dim lstDel As List(Of QQChatInfo) = Me.GetDelChatInfo(strUsrName)

            If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
                If Not Me.mChargeHelper.mChargeInfo.Registered OrElse Me.mChargeHelper.mChargeInfo.Overdue OrElse Not Me.mChargeHelper.CheckDeviceRegisted(Me.mCurrentIdentify) Then
                    For Each item As QQChatInfo In lstDel
                        If item.SCType = SociaChatType.Text OrElse item.SCType = SociaChatType.SpecialExpression Then
                            item.StrMessage = Me.MessageEncrypt(item.StrMessage)
                        End If
                    Next
                End If
            End If

            Dim lstDelTemp As New List(Of QQChatInfo)

            Dim isSearch As Boolean = False
            If strSearchText.Length > 0 Then
                For Each info As QQChatInfo In lstDel
                    If info.StrMessage.Contains(strSearchText.Trim()) Then
                        lstDelTemp.Add(info)
                    End If
                Next
                isSearch = True
            Else
                lstDelTemp = lstDel
            End If
            If lstDelTemp.Count > 0 Then
                lstDelTemp.Sort(New QQCreateTimeComparer(SortType.DESC))
            End If

            Dim iFCount As Integer = 0
            If isSearch Then
                '’查询
                For Each item As QQChatInfo In lstDelTemp
                    If (DateTime.Compare(item.DTCreateTime, Me.dtpEnd.Value) < 0) AndAlso (DateTime.Compare(item.DTCreateTime, Me.dtpStart.Value) >= 0) And Not Me.mLstSearchCurrentInfos.Contains(item) Then
                        If iFCount >= mDefaultChatCount Then
                            Exit For
                        End If
                        lstInfos.Add(item)
                        Me.mLstSearchCurrentInfos.Add(item)
                        iFCount = iFCount + 1
                    End If
                Next
            Else
                '非查询
                If lstDelTemp.Count > 0 AndAlso lstInfos.Count > 0 Then
                    Dim dtEnd As DateTime = lstInfos(lstInfos.Count - 1).DTCreateTime
                    Dim dtStart As DateTime = lstInfos(0).DTCreateTime
                    For Each cInfo As QQChatInfo In lstDelTemp
                        If iFCount >= mDefaultChatCount Then
                            Exit For
                        End If
                        cInfo.IntID = lstInfos(0).IntID 'lstInfos(lstInfos.Count - 1).ID

                        '已添加显示过无需在添加
                        If Me.mLstCurrentInfos.Contains(cInfo) Then
                            Continue For
                        End If

                        If (DateTime.Compare(cInfo.DTCreateTime, dtEnd) < 0) AndAlso (DateTime.Compare(cInfo.DTCreateTime, dtStart) >= 0) Then
                            lstInfos.Add(cInfo)
                            Me.mLstCurrentInfos.Add(cInfo)
                            iFCount = iFCount + 1

                            'ElseIf lstInfos.Count < mDefaultChatCount Then
                            '    lstInfos.Add(cInfo)
                            '    Me.mLstCurrentInfos.Add(cInfo)
                            '    iFCount = iFCount + 1
                        End If
                    Next
                ElseIf lstInfos.Count = 0 Then
                    If Me.mLstCurrentChatInfo.Count = 0 Then
                        For Each cInfo As QQChatInfo In lstDelTemp
                            If iFCount >= mDefaultChatCount Then
                                Exit For
                            End If
                            lstInfos.Add(cInfo)
                            Me.mLstCurrentInfos.Add(cInfo)
                            iFCount = iFCount + 1
                        Next
                    Else
                        Dim dtEnd As DateTime = Me.mLstCurrentChatInfo(0).DTCreateTime
                        For Each cInfo As QQChatInfo In lstDelTemp
                            If iFCount >= mDefaultChatCount Then
                                Exit For
                            End If
                            cInfo.IntID = Me.GetChatInfoID()
                            If DateTime.Compare(cInfo.DTCreateTime, dtEnd) < 0 And Not Me.mLstCurrentInfos.Contains(cInfo) Then
                                lstInfos.Add(cInfo)
                                Me.mLstCurrentInfos.Add(cInfo)
                                iFCount = iFCount + 1

                            End If
                        Next
                    End If
                End If
            End If
            lstInfos.Sort(New QQCreateTimeComparer(SortType.ASC))
            iCount = lstDel.Count
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDeleteChatInfo")
        End Try
        Return iCount
    End Function

    Private Function GetDelChatInfo(strName As String) As List(Of QQChatInfo)
        Dim lstTempE As List(Of QQChatInfo) = New List(Of QQChatInfo)

        If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
            lstTempE = GetLstDelChatInfo(strName)
        Else
            If strName = IMExportHelper.UnKnownContact Then
                If Me.mDictDelChatInfo.ContainsKey(strName) Then
                    lstTempE = Me.mDictDelChatInfo(strName)
                End If
            ElseIf strName = IMExportHelper.UnKnownContactEx Then
                Dim lstTemp As List(Of String) = Me.GetUnknownEx()
                For Each strKey As String In lstTemp
                    Dim lstChatInfoTemp As List(Of QQChatInfo) = New List(Of QQChatInfo)
                    If Me.mDictDelChatInfo.ContainsKey(strKey) Then
                        lstChatInfoTemp = Me.mDictDelChatInfo(strKey)
                        For Each item As QQChatInfo In lstChatInfoTemp
                            lstTempE.Add(item)
                        Next
                    End If
                Next
            Else
                Dim fInfo As QQFriendInfo = Nothing
                If Me.mDictDelFriendInfo.ContainsKey(strName) Then
                    fInfo = Me.mDictDelFriendInfo(strName)
                End If

                If fInfo Is Nothing Then
                    GoTo Do_Exit
                End If

                If Me.mDictDelChatInfo.ContainsKey(fInfo.StrQQNum) Then
                    lstTempE = Me.mDictDelChatInfo(fInfo.StrQQNum)
                End If
            End If

        End If
Do_Exit:
        Dim lstChatInfo As List(Of QQChatInfo) = New List(Of QQChatInfo)
        Dim tempInfo As QQChatInfo
        If lstTempE IsNot Nothing AndAlso lstTempE.Count > 0 Then
            For Each item As QQChatInfo In lstTempE
                tempInfo = New QQChatInfo()
                tempInfo.DTCreateTime = item.DTCreateTime
                tempInfo.ImgStatus = item.ImgStatus
                tempInfo.IntCreateTime = item.IntCreateTime
                tempInfo.IntDes = item.IntDes
                tempInfo.IntID = item.IntID
                tempInfo.IntStatus = item.IntStatus
                tempInfo.IsDelete = item.IsDelete
                tempInfo.IsWhatsAppChatRoom = item.IsWhatsAppChatRoom
                tempInfo.SCType = item.SCType
                tempInfo.StrMessage = item.StrMessage
                tempInfo.StrNickName = item.StrNickName
                tempInfo.StrPicUrl = item.StrPicUrl
                tempInfo.StrSendUin = item.StrSendUin
                tempInfo.StrUsrName = item.StrUsrName
                lstChatInfo.Add(tempInfo)
            Next
        End If
        Return lstChatInfo
    End Function

    Private Function GetLstDelChatInfo(ByVal strName As String) As List(Of QQChatInfo)
        Dim lstChatInfo As List(Of QQChatInfo) = New List(Of QQChatInfo)
        Try
            'Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(strName))
            'If strName.Contains(IMExportHelper.UnKnownContact) Then
            '    strMD5 = strName
            'End If
            Dim strMD5 As String = strName
            If Me.mDictDelChatInfo.ContainsKey(strMD5) Then
                lstChatInfo = Me.mDictDelChatInfo(strMD5)
            End If

            If lstChatInfo.Count = 0 AndAlso strName.Contains(IMExportHelper.UnKnownContact) Then
                strMD5 = strName.Replace(IMExportHelper.UnKnownContact & "_", "")
            End If
            If Me.mDictDelChatInfo.ContainsKey(strMD5) Then
                lstChatInfo = Me.mDictDelChatInfo(strMD5)
            End If
        Catch
        End Try
        Return lstChatInfo
    End Function

    Private Function GetUnknownEx() As List(Of String)
        Dim lstTemp As New List(Of String)
        Try
            For Each strKey As String In Me.mDictDelChatInfo.Keys
                If strKey = IMExportHelper.UnKnownContact Then
                    Continue For
                End If

                If Not Me.IsContain(strKey) Then
                    lstTemp.Add(strKey)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetUnknownEx")
        End Try
        Return lstTemp
    End Function

    Private Function IsContain(strKey As String) As Boolean
        Dim isResult As Boolean = False
        For Each item As QQFriendInfo In Me.mDictFriendInfoEx.Values
            If item.StrQQNum = strKey Then
                isResult = True
                Exit For
            End If
        Next
        Return isResult
    End Function

    Private Delegate Sub UpdateDataGridViewRowHandler()
    Private Sub UpdateDataGridViewRow()
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateDataGridViewRowHandler(AddressOf UpdateDataGridViewRow))
        Else
            Try
                Dim colStatus As Color = Color.FromArgb(153, 153, 153)

                For Each row As tbDataGridViewRow In Me.dgvFriend.Rows
                    Dim rowItem As QQFriendInfo = CType(row.Tag, QQFriendInfo)
                    If rowItem.IsFindDate Then
                        colStatus = Color.FromArgb(255, 92, 92)
                    Else
                        colStatus = Color.FromArgb(153, 153, 153)
                    End If
                    Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                    cellName.tbForeColor = colStatus
                    cellName.tbSelectionForeColor = colStatus
                    'If IniSetting.IsWeChatShare() Then
                    '    cellName.ShowWechatMessageLock = False
                    'End If

                Next

            Catch ex As Exception
                Common.LogException(ex.ToString(), "UpdateDataGridViewRow")
            End Try
        End If
    End Sub

    Private Function GetChatInfoID() As Integer
        Dim id As Integer = 0
        Try
            id = Me.mLstCurrentChatInfo(Me.mLstCurrentChatInfo.Count - 1).IntID
            Dim index As Integer = 0
            For index = 0 To Me.mLstCurrentChatInfo.Count - 1 Step +1
                If Not Me.mLstCurrentChatInfo(index).IsDelete Then
                    id = Me.mLstCurrentChatInfo(index).IntID
                    Exit For
                End If
            Next

        Catch ex As Exception
        End Try
        Return id
    End Function

    Private Function MessageEncrypt(ByVal strMsg As String, Optional ByVal strShowCharacter As String = "*", Optional ByVal intShowLength As Integer = 2)
        Dim strValue As String = ""
        Try
            If strMsg.Length <= intShowLength Then ' OrElse strMsg.Contains("【试用版预览数据时部分内容已用*号代替，点击右上角「激活」按钮购买后查看】")
                strValue = strMsg
                GoTo DO_Exit
            End If

            Dim intCurrentIndex As Integer = 1
            For Each item As Char In strMsg
                If intCurrentIndex <= intShowLength Then
                    strValue = strValue & item
                Else
                    strValue = strValue & strShowCharacter
                End If
                intCurrentIndex = intCurrentIndex + 1
            Next
        Catch ex As Exception
            strValue = strMsg
            Common.LogException(ex.ToString(), "MessageEncrypt")
        End Try
DO_Exit:

        'If strValue.Length > 0 AndAlso Not strMsg.Contains("【试用版预览数据时部分内容已用*号代替，点击右上角「激活」按钮购买后查看】") Then
        '    strValue = String.Format("{0}{1}【试用版预览数据时部分内容已用*号代替，点击右上角「激活」按钮购买后查看】", strValue, vbCrLf)
        'End If
        Return strValue
    End Function

    Private Delegate Sub ChangeLoadDataVisibleHandler(ByVal isVisible As Boolean)
    Private Sub ChangeLoadDataVisible(ByVal isVisible As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ChangeLoadDataVisibleHandler(AddressOf ChangeLoadDataVisible), isVisible)
            Else
                Me.pnlLoadData.Visible = isVisible
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeLoadDataVisible")
        End Try
    End Sub

    Private Function GetChatMediaPathFromPC(ByVal chat As QQChatInfo, ByVal blnThum As Boolean, ByVal mType As QQMediaType) As String
        Dim imgPath As String = String.Empty
        Try
            If chat Is Nothing Then
                Return imgPath
            End If

            Dim strValue As String = ""
            Dim strMType As String = ""
            Select Case mType
                Case QQMediaType.Picture
                    strMType = "Img"
                    strValue = chat.StrPicUrl

                Case QQMediaType.Video
                    strMType = "Video"
                    strValue = chat.StrPicUrl

                Case QQMediaType.Audio
                    strMType = "Audio"
                    strValue = chat.StrPicUrl

            End Select

            Dim strFileName As String = IMExportHelper.GetQQFileNameOnPC(chat, blnThum)
            If mType = QQMediaType.Audio Then
                strFileName = String.Format("{0}_{1}.wav", chat.DTCreateTime.ToString("yyyyMMddHHmmss"), chat.IntID)
            Else
                strFileName = strFileName.Replace("png", "pic_thum")
            End If
            Dim strPathOnPC As String = String.Format("{0}\{1}\{2}\{3}", Me.mPathQQOnPC_Temp, strMType, Me.mCurrentUserNum, strFileName)

            If File.Exists(strPathOnPC) Then
                imgPath = strPathOnPC
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetChatMediaPathFromPC")
        End Try
        Return imgPath
    End Function

    Private Function GetChatVoiceLength(ByVal strContent As String, ByVal blnRound As Boolean) As Single
        Dim singleLength As Single = 0
        Dim strContent1 As String = strContent
        Dim strKey As String = "+"

        If strContent1.Length > 0 AndAlso strContent1.Contains(strKey) Then
            Dim startIndex As Integer = strContent1.IndexOf(strKey)
            strContent1 = strContent.Substring(startIndex + strKey.Length)

            Try
                singleLength = Single.Parse(strContent1)
            Catch
            End Try

            If blnRound Then
                singleLength = System.Math.Floor(singleLength + 0.5)          '四舍五入
            End If
        End If

        Return singleLength
    End Function

    Private Sub ShowChatOnWebPage(ByVal strJson As String)
        Me.ShowSystemInfoOnPC(False, "")
        ChatWebPage.ShowChatOnWebPage(Me.wbsChat, strJson)
        ChatWebPage.ShowMoreButton(Me.wbsChat, False)
        Me.SetBrowserShow()
    End Sub

    Private Sub SetBrowserShow()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf SetBrowserShow))
        Else
            Me.wbsChat.Visible = True
        End If
    End Sub

    Private Enum DataType
        Normal = 0              '普通展示
        GetNews = 1             '查看更多消息
        Search = 2              '搜索
        TimeLocate = 3          '时间定位
        GetContextNews = 4      '查看前后消息
    End Enum

    Private Function SortFriendInfo(ByVal dict As Dictionary(Of String, QQFriendInfo), Optional ByVal sort As SortType = SortType.DESC) As Dictionary(Of String, QQFriendInfo)
        Dim dictReturn As New Dictionary(Of String, QQFriendInfo)
        Try
            If dict Is Nothing OrElse dict.Count = 0 Then
                Return dictReturn
            End If

            Dim lst As New List(Of QQFriendInfo)
            Dim lstMinValue As New List(Of QQFriendInfo)

            For Each Item As QQFriendInfo In dict.Values
                If Item.DTLastSendTime = DateTime.MinValue Then
                    lstMinValue.Add(Item)
                Else
                    lst.Add(Item)
                End If
            Next

            lst.Sort(New QQLastChatTimeComparer(sort))

            For Each Item As QQFriendInfo In lst
                If Not dictReturn.ContainsKey(Item.StrQQNum) Then
                    dictReturn.Add(Item.StrQQNum, Item)
                End If
            Next

            For Each Item As QQFriendInfo In lstMinValue
                If Not dictReturn.ContainsKey(Item.StrQQNum) Then
                    dictReturn.Add(Item.StrQQNum, Item)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SortFriendInfo")
        End Try
        Return dictReturn
    End Function

    Private Function IsContainChatTable(ByVal UsrName As String, ByVal dictFriendInfo As Dictionary(Of String, QQFriendInfo)) As Boolean
        Dim isContain As Boolean = False

        If String.IsNullOrEmpty(UsrName) OrElse dictFriendInfo Is Nothing Then
            Return isContain
        End If

        Dim info As QQFriendInfo = dictFriendInfo(UsrName)
        If info Is Nothing Then
            Return isContain
        End If

        Dim strChatTableName As String = String.Format("tb_c2cMsg_{0}", info.StrQQNum)
        If info.IsGroup Then
            strChatTableName = String.Format("tb_TroopMsg_{0}", info.StrQQNum)
        ElseIf info.IsDiscuss Then
            strChatTableName = String.Format("tb_discussGrp_{0}", info.StrQQNum)
        End If
        If Me.mLstChatTable.Contains(strChatTableName) OrElse info.IsFindDate Then
            isContain = True
        End If

        Return isContain
    End Function

    '截取最后一条消息（展示在界面）
    Private Function OperateLastChatText(ByVal strLastChatText As String) As String
        If strLastChatText.Length = 0 Then
            Return " "
        End If

        Try
            '截取
            If Me.mGraphic Is Nothing Then
                Me.mGraphic = Me.CreateGraphics()
            End If

            Dim strResult As String = strLastChatText
            Dim strFormat As StringFormat = GuiHelper.CreateStringFormat(ContentAlignment.MiddleLeft, False)
            Dim charactersFitted As Integer
            Dim linesFilled As Integer
            Dim sizeCommment As SizeF = Me.mGraphic.MeasureString(strLastChatText, New Font("宋体", 9, FontStyle.Regular), _
                                                                                                          New SizeF(100, 15.2), strFormat, charactersFitted, linesFilled)

            If charactersFitted <> strLastChatText.Length AndAlso strLastChatText.Length > 3 Then
                strResult = strLastChatText.Substring(0, IIf(charactersFitted - 3 <= 0, 0, charactersFitted - 3)) & "..."
            End If

            Return strResult
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OperateLastChatText")
            Return strLastChatText
        End Try
    End Function

    Private Function GetLastChatTime(ByVal LastChatTime As DateTime) As String
        Dim strTime As String = String.Empty
        Dim ts As TimeSpan = Now.Date.Subtract(LastChatTime.Date) '只需比较日期就好
        Try
            Select Case ts.Days
                Case 0
                    strTime = LastChatTime.ToString("HH:mm")
                Case 1
                    strTime = Me.Language.GetString("Note.Label.Yesterday") '"昨天"
                Case Else
                    Dim strYear As String = LastChatTime.Year.ToString().Substring(2, 2)
                    Dim strMonth As String = LastChatTime.Month.ToString()
                    Dim strDay As String = LastChatTime.Day.ToString()
                    strTime = String.Format("{0}-{1}-{2}", strYear, strMonth, strDay)
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetLastChatTime")
        End Try


        Return strTime
    End Function

    Private Sub FindGridViewRow(strKey As String)
        For Each row As tbDataGridViewRow In Me.dgvFriend.Rows
            If row.Cells("colMD5").Value = strKey Then
                Me.dgvFriend.Rows.Remove(row)
            End If
        Next
    End Sub

    Private Function FormatStringForWebPage(ByVal strContent As String)
        Dim value As String = strContent

        If Not String.IsNullOrEmpty(value) Then
            If value.StartsWith("<![CDATA[") AndAlso value.EndsWith("]]>") Then

                value = value.TrimStart(New Char() {"<"c, "!"c, "["c, "C"c, "D"c, "A"c, "T"c, "A"c, "["c})
                value = value.TrimEnd(New Char() {"]"c, "]"c, ">"c})

            End If
        End If

        Return value
    End Function

    '提取表情的正则方法
    Private Function OperateForExpression(ByVal strContent As String) As String
        Dim regular As String = "\[(?<expression>.*?)\]"
        Dim dt As DataTable = Utility.GetMatchStringByRegularExpressions(strContent, New String(0) {regular}, New String(0) {"expression"})

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Dim strKey As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("expression").ToString()))
                strKey = "[" & strKey & "]"

                Dim strExpressionPath As String = Me.GetExpressionPath(strKey)

                If Not String.IsNullOrEmpty(strExpressionPath) Then
                    strContent = strContent.Replace(strKey, strExpressionPath)
                End If
            Next
        End If

        Return strContent
    End Function

    '获取表情路径（相对路径）
    Private Function GetExpressionPath(ByVal strKey As String) As String
        Dim strName As String = String.Empty

        Select Case strKey
            Case "[微笑]"
                strName = "Expression_1"
            Case "[撇嘴]"
                strName = "Expression_2"
            Case "[色]"
                strName = "Expression_3"
            Case "[发呆]", "[發呆]"
                strName = "Expression_4"
            Case "[得意]"
                strName = "Expression_5"
            Case "[流泪]", "[流淚]"
                strName = "Expression_6"
            Case "[害羞]"
                strName = "Expression_7"
            Case "[闭嘴]", "[閉嘴]"
                strName = "Expression_8"
            Case "[睡]"
                strName = "Expression_9"
            Case "[大哭]"
                strName = "Expression_10"
            Case "[尴尬]", "[尷尬]"
                strName = "Expression_11"
            Case "[发怒]", "[發怒]"
                strName = "Expression_12"
            Case "[调皮]", "[調皮]"
                strName = "Expression_13"
            Case "[呲牙]"
                strName = "Expression_14"
            Case "[惊讶]", "[驚訝]"
                strName = "Expression_15"
            Case "[难过]", "[難過]"
                strName = "Expression_16"
            Case "[酷]"
                strName = "Expression_17"
            Case "[冷汗]"
                strName = "Expression_18"
            Case "[抓狂]"
                strName = "Expression_19"
            Case "[吐]"
                strName = "Expression_20"
            Case "[偷笑]"
                strName = "Expression_21"
            Case "[愉快]"
                strName = "Expression_22"
            Case "[白眼]"
                strName = "Expression_23"
            Case "[傲慢]"
                strName = "Expression_24"
            Case "[饥饿]", "[饑餓]"
                strName = "Expression_25"
            Case "[困]"
                strName = "Expression_26"
            Case "[惊恐]", "[驚恐]"
                strName = "Expression_27"
            Case "[流汗]"
                strName = "Expression_28"
            Case "[憨笑]"
                strName = "Expression_29"
            Case "[悠闲]", "[悠閒]"
                strName = "Expression_30"
            Case "[奋斗]", "[奮鬥]"
                strName = "Expression_31"
            Case "[咒骂]", "[咒罵]"
                strName = "Expression_32"
            Case "[疑问]"
                strName = "Expression_33"
            Case "[嘘]"
                strName = "Expression_34"
            Case "[晕]", "[暈]"
                strName = "Expression_35"
            Case "[疯了]", "[瘋了]"
                strName = "Expression_36"
            Case "[衰]"
                strName = "Expression_37"
            Case "[骷髅]", "[骷髏]"
                strName = "Expression_38"
            Case "[敲打]"
                strName = "Expression_39"
            Case "[再见]"
                strName = "Expression_40"
            Case "[擦汗]"
                strName = "Expression_41"
            Case "[抠鼻]", "[摳鼻]"
                strName = "Expression_42"
            Case "[鼓掌]"
                strName = "Expression_43"
            Case "[糗大了]"
                strName = "Expression_44"
            Case "[坏笑]", "[壞笑]"
                strName = "Expression_45"
            Case "[左哼哼]"
                strName = "Expression_46"
            Case "[右哼哼]"
                strName = "Expression_47"
            Case "[哈欠]"
                strName = "Expression_48"
            Case "[鄙视]", "[鄙視]"
                strName = "Expression_49"
            Case "[委屈]"
                strName = "Expression_50"
            Case "[快哭了]"
                strName = "Expression_51"
            Case "[阴险]", "[陰險]"
                strName = "Expression_52"
            Case "[亲亲]", "[親親]"
                strName = "Expression_53"
            Case "[吓]", "[嚇]"
                strName = "Expression_54"
            Case "[可怜]", "[可憐]"
                strName = "Expression_55"
            Case "[菜刀]"
                strName = "Expression_56"
            Case "[西瓜]"
                strName = "Expression_57"
            Case "[啤酒]"
                strName = "Expression_58"
            Case "[篮球]", "[籃球]"
                strName = "Expression_59"
            Case "[乒乓]"
                strName = "Expression_60"
            Case "[咖啡]"
                strName = "Expression_61"
            Case "[饭]", "[飯]"
                strName = "Expression_62"
            Case "[猪头]", "[豬頭]"
                strName = "Expression_63"
            Case "[玫瑰]"
                strName = "Expression_64"
            Case "[凋谢]"
                strName = "Expression_65"
            Case "[嘴唇]"
                strName = "Expression_66"
            Case "[爱心]", "[愛心]"
                strName = "Expression_67"
            Case "[心碎]"
                strName = "Expression_68"
            Case "[蛋糕]"
                strName = "Expression_69"
            Case "[闪电]", "[閃電]"
                strName = "Expression_70"
            Case "[炸弹]", "[炸彈]"
                strName = "Expression_71"
            Case "[刀]"
                strName = "Expression_72"
            Case "[足球]"
                strName = "Expression_73"
            Case "[瓢虫]", "[瓢蟲]"
                strName = "Expression_74"
            Case "[便便]"
                strName = "Expression_75"
            Case "[月亮]"
                strName = "Expression_76"
            Case "[太阳]", "[太陽]"
                strName = "Expression_77"
            Case "[礼物]", "[禮物]"
                strName = "Expression_78"
            Case "[拥抱]", "[擁抱]"
                strName = "Expression_79"
            Case "[强]"
                strName = "Expression_80"
            Case "[弱]"
                strName = "Expression_81"
            Case "[握手]"
                strName = "Expression_82"
            Case "[胜利]", "[勝利]"
                strName = "Expression_83"
            Case "[抱拳]"
                strName = "Expression_84"
            Case "[勾引]"
                strName = "Expression_85"
            Case "[拳头]", "[拳頭]"
                strName = "Expression_86"
            Case "[差劲]", "[差勁]"
                strName = "Expression_87"
            Case "[爱你]", "[愛你]"
                strName = "Expression_88"
            Case "[NO]"
                strName = "Expression_89"
            Case "[OK]"
                strName = "Expression_90"
            Case "[爱情]", "[愛情]"
                strName = "Expression_91"
            Case "[飞吻]", "[飛吻]"
                strName = "Expression_92"
            Case "[跳跳]"
                strName = "Expression_93"
            Case "[发抖]", "[發抖]"
                strName = "Expression_94"
            Case "[怄火]", "[慪火]"
                strName = "Expression_95"
            Case "[转圈]", "[轉圈]"
                strName = "Expression_96"
            Case "[磕头]", "[磕頭]"
                strName = "Expression_97"
            Case "[回头]", "[回頭]"
                strName = "Expression_98"
            Case "[跳绳]", "[跳繩]"
                strName = "Expression_99"
            Case "[挥手]", "[揮手]"
                strName = "Expression_100"
            Case "[激动]", "[激動]"
                strName = "Expression_101"
            Case "[街舞]"
                strName = "Expression_102"
            Case "[献吻]", "[獻吻]"
                strName = "Expression_103"
            Case "[左太极]", "[左太極]"
                strName = "Expression_104"
            Case "[右太极]", "[右太極]"
                strName = "Expression_105"
        End Select

        Dim result As String = String.Empty
        If Not String.IsNullOrEmpty(strName) Then
            'result = String.Format("<img src=""./expression/{0}.png"" width=""21px"" height=""21px""/>", strName)
            result = String.Format("^./expression/{0}.png^(21,21)", strName)
        Else
            Common.LogException(strKey, "GetExpressionPath")
        End If

        Return result
    End Function

    Private Sub ShowAudioWebView(ByVal strWavFile As String, ByVal blnPlay As Boolean)
        If String.IsNullOrEmpty(strWavFile) Then
            Return
        End If

        Dim strID As String = Path.GetFileNameWithoutExtension(strWavFile)

        ChatWebPage.ControlVoiceView(Me.wbsChat, strID, blnPlay)
    End Sub

    Private Function GetWebPageJsonArray(ByVal strContent As String) As JsonArray
        '.*?代表任意字符（不包括换行及特殊字符）
        '[\w\W]*?代表任意字符（包括换行及特殊字符）
        'Dim regular As String = "<title>(?<title>.*?)</title>[\w\W]*?<url>(?<url>.*?)</url>[\w\W]*?<pub_time>(?<pub_time>.*?)</pub_time>[\w\W]*?<cover>(?<cover>.*?)</cover>[\w\W]*?<digest>(?<digest>.*?)</digest>"
        Dim regular As String = "url=""(?<url>.*?)""[\w\W]*?<title>(?<title>.*?)</title>"

        '如果服务器有设置值则使用服务上的值
        If Not String.IsNullOrEmpty(Me.mRegular) Then
            regular = Me.mRegular
        End If

        Return Me.GetWebPageJsonArray(strContent, regular)
    End Function

    Private Function GetWebPageJsonArray(ByVal strContent As String, ByVal regular As String) As JsonArray
        Dim arrWebPages As New JsonArray
        Dim dicWebPage As JsonObject

        Dim dt As DataTable = Utility.GetMatchStringByRegularExpressions(strContent, New String(0) {regular}, New String(4) {"title", "url", "pub_time", "cover", "digest"})

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    dicWebPage = New JsonObject()
                    '标题
                    Dim strTitle As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("title").ToString()))
                    dicWebPage.Add("title", strTitle)

                    '网页url
                    Dim strUrl As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("url").ToString()))
                    dicWebPage.Add("url", strUrl)

                    '发布时间
                    Dim strPubTime As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("pub_time").ToString()))
                    Dim PubTime As DateTime = DateTime.MinValue

                    Try
                        If Not String.IsNullOrEmpty(strPubTime) Then
                            Dim longPubTime As Long = Long.Parse(strPubTime)
                            PubTime = WechatHelper.ConvertWeixinToPcTime(longPubTime)
                        End If
                    Catch
                    End Try

                    If PubTime = DateTime.MinValue Then
                        strPubTime = String.Empty
                    Else
                        strPubTime = PubTime.ToString("MM-dd")
                    End If

                    dicWebPage.Add("pub_time", strPubTime)

                    '封面地址
                    Dim strCover As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("cover").ToString()))
                    dicWebPage.Add("cover", strCover)

                    '摘要
                    Dim strDigest As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("digest").ToString()))
                    dicWebPage.Add("digest", strDigest)

                    arrWebPages.Add(dicWebPage)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "GetWebPageJsonArray")
                End Try
            Next
        Else
            'Common.LogException(strContent, "无法兼容的网页")
        End If

        Return arrWebPages
    End Function

    Private Function GetWebLinkJsonArray(ByVal strContent As String) As JsonArray
        Dim regular As String = "url=""(?<url>.*?)""[\w\W]*?<title>(?<title>.*?)</title>"

        Return Me.GetWebPageJsonArray(strContent, regular)
    End Function

#End Region

#Region "--- 查找 ---"

    '4、时间定位（根据起始时间，获取到现在的消息）
    Private Sub LoadChatForTimeLoaction(ByVal lstTime As List(Of DateTime))
        Try
            If Me.mThreadGetChat IsNot Nothing AndAlso Me.mThreadGetChat.ThreadState <> ThreadState.Stopped Then
                Me.mThreadGetChat.Abort()
            End If
        Catch
        End Try

        Me.mThreadGetChat = New Thread(New ParameterizedThreadStart(AddressOf LoadChatForTimeLoactionInThread))
        With Me.mThreadGetChat
            .IsBackground = True
            .Start(lstTime)
        End With
    End Sub

    Private Sub LoadChatForTimeLoactionInThread(ByVal obj As Object)
        Try
            If Me.dgvFriend.SelectedRows.Count > 0 Then
                Dim row As DataGridViewRow = Me.dgvFriend.SelectedRows(0)
                Dim Item As QQFriendInfo = CType(row.Tag, QQFriendInfo)
                Dim lstInfos As List(Of QQChatInfo) = Nothing
                If Item.UsrName <> IMExportHelper.UnKnownContact Then
                    Item.SearchTime = obj
                    lstInfos = Me.mQQDB.Get_Chat_Info_ByTime(Item.StrQQNum, -1, mDefaultChatCount, CType(obj, List(Of DateTime)), Item.IsGroup, Item.IsDiscuss)
                Else
                    lstInfos = New List(Of QQChatInfo)
                End If

                Dim isDelete As Boolean = False

                'Dim lstDel As List(Of WeChatChatInfo) = Me.GetDelChatInfo(Item.UsrName)
                'For Each info As WeChatChatInfo In lstDel
                '    If info.DTCreateTime >= startTime AndAlso info.DTCreateTime <= endTime Then
                '        isDelete = True
                '        Exit For
                '    End If
                'Next

                If Not isDelete AndAlso (lstInfos Is Nothing OrElse lstInfos.Count = 0) Then
                    '纪龙龙（删掉）
                    Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.NoSearchResult")) '您选择的时间内没有消息，请您重新选择
                Else
                    Me.ShowChatOnWeb(Item.StrQQNum, lstInfos, DataType.TimeLocate, Item.IsGroup)
                End If
            Else
                Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.InfoSelectFriend")) '"请选中联系人再查询！"
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadChatForTimeLoactionInThread")
        End Try
    End Sub

#End Region

    Private Delegate Sub ChangeStateHandler(ByVal Text As String)
    Private Sub ChangeStateText(ByVal Text As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ChangeStateHandler(AddressOf ChangeStateText), Text)
            Else
                Me.lblState.Text = Text
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeStateText")
        End Try
    End Sub

    Private Sub SetLinkmanMsg()
        Try
            Me.mSelectAllClick = True
            If Not Me.mIsAddRow AndAlso Me.dgvFriend.Rows.Count > 0 AndAlso Me.dgvFriend.SelectedRows IsNot Nothing AndAlso Me.dgvFriend.SelectedRows.Count = Me.dgvFriend.Rows.Count Then
                Me.cbxSelectAll.Checked = True
            Else
                Me.cbxSelectAll.Checked = False
            End If
            Me.mSelectAllClick = False

            Me.lblLinkmanMsg.Text = String.Format(Me.Language.GetString("WeChat.Lable.LinkmanMsg"), Me.dgvFriend.SelectedRows.Count, Me.dgvFriend.Rows.Count) '已选择 {0} 项，全部 {1} 项
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetLinkmanMsg")
        End Try
    End Sub

    Private Delegate Sub ShowSystemInfoOnPCHandler(ByVal blnVisible As Boolean, ByVal strText As String, ByVal isBackupFailure As Boolean)
    Private Sub ShowSystemInfoOnPC(ByVal blnVisible As Boolean, ByVal strText As String, Optional ByVal isBackupFailure As Boolean = False)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowSystemInfoOnPCHandler(AddressOf ShowSystemInfoOnPC), blnVisible, strText, isBackupFailure)
        Else
            'Me.pnlInfo.Visible = blnVisible
            'Me.lblInfo.Text = strText
            RemoveHandler Me.lblSystemInfo.Click, AddressOf lblSystemInfo_Click
            If isBackupFailure Then
                Me.lblSystemInfo.ForeColor = Color.FromArgb(5, 152, 242)
                AddHandler Me.lblSystemInfo.Click, AddressOf lblSystemInfo_Click
                Me.lblSystemInfo.Cursor = Cursors.Hand
            Else
                Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
                Me.lblSystemInfo.Cursor = Cursors.Default
            End If
            Me.lblSystemInfo.Visible = blnVisible
            If Me.lblSystemInfo.Visible Then
                Me.lblSystemInfo.BringToFront()
            End If
            Me.lblSystemInfo.Text = strText

            If blnVisible Then
                Me.pnlLoadData.Visible = False
                Me.wbsChat.Visible = False
            End If
        End If
    End Sub


#Region "--- 找回数据 ---"
    Private Sub btnFinalData_Click(sender As Object, e As EventArgs) Handles btnFinalData.Click
        Me.btnFinalData.tbShowDot = False
        Me.FindData()
        'ActionCollectHelper.WeChatFindData(Me.mDevice, ModelKey.Weixin, ModelKey.FindWeChatData, ActionDataType.Click, FunctionSucceed.Succeed)
        'IniSetting.SetShowFindWeChatDataNew()
    End Sub

    Private Sub FindData()
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        If Me.mStrDBQQOnPC.Length = 0 OrElse Not File.Exists(mStrDBQQOnPC) Then
            tbMessageBox.Show(Me, Me.Language.GetString("Common.WeChat.DBExist"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1) '"微信数据库不存在"
            'If Me.mFrmWeChatMain IsNot Nothing Then
            '    Me.mFrmWeChatMain.Close()
            'End If
            Me.Close()
            Return
        End If

        Try
            If Me.mTDFindData IsNot Nothing AndAlso Me.mTDFindData.ThreadState <> ThreadState.Stopped Then
                Me.mTDFindData.Abort()
            End If
        Catch
        End Try

        Me.mFQQHelper = FindQQDataHelper.GetInstance(mStrDBQQOnPC)
        RemoveHandler Me.mFQQHelper.QQRProgressEventHandler, AddressOf FWCDHelper_WCRProgressEventHandler
        AddHandler Me.mFQQHelper.QQRProgressEventHandler, AddressOf FWCDHelper_WCRProgressEventHandler

        Me.mTDFindData = New Thread(AddressOf DoFindData)
        With Me.mTDFindData
            .IsBackground = True
            .Start()
        End With

    End Sub

    Private Sub DoFindData()
        Try
            Me.mIntWCRProgress = 0
            Me.SetProgress(Me.mIntWCRProgress)
            Me.ShowFindData(True)
            Me.ShowSystemInfoOnPC(False, "")
            Me.ChangeStateText("")
            Me.FindDataByDll()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoFindData")
        End Try
    End Sub

    Private Sub FindDataByDll()
        Try
            'Try
            '    '调试代码使用
            '    Dim strRepeateDll As String = Path.Combine(Path.GetDirectoryName(mStrDBQQOnPC), "repeate.dll")
            '    If Not File.Exists(strRepeateDll) Then
            '        Dim fs As FileStream = File.Create(strRepeateDll)
            '        fs.Close()
            '    End If
            'Catch ex As Exception

            'End Try
            Me.mFQQHelper.QQRFindData(Me.mStrDBQQOnPC, "", Me.mDictFriendInfoEx)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "FindDataByDll")
        End Try
    End Sub

    Private mIntWCRProgress As Integer = 0
    Private Sub FWCDHelper_WCRProgressEventHandler(sender As Object, args As FindDataEventArgs)
        Dim isKill As Boolean = False
        Try
            Select Case args.WType
                Case WCRType.Record
                    If args.WCFInfo IsNot Nothing Then
                        Me.AddFindDeleteDataGridView(args.WCFInfo)
                    End If

                Case WCRType.Progress
                    If Me.mIntWCRProgress <= args.IntProgress Then
                        Me.mIntWCRProgress = args.IntProgress
                        Me.SetProgress(args.IntProgress)
                    End If

                Case WCRType.ErrorM, WCRType.Exception
                    Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Button.RecoveredNoData")) '"对不起！未找回任何删除数据。"
                    Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
                    Common.LogException(String.Format("ErrorMessage:{0} ErrorCode:{1}", args.StrMsg, args.IntProgress), "<<<---FWCDHelper_WCRProgressEventHandler")
                    Me.ShowFindData(False)
                    Common.KillProcess("tbWCRHelper")
                    isKill = True
                    'Return

                Case WCRType.Abnormal
                    Me.ShowSystemInfoOnPC(True, "找回程序出现异常崩溃")
                    Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
                    Me.ShowFindData(False)
                    Me.ShowShare(False, False)
                    Common.KillProcess("tbWCRHelper")
                    isKill = True

            End Select

            If Me.mIntWCRProgress >= 100 Then
                Me.mDictDelChatInfo = args.DictDelChatInfo
                Me.mDictDelFriendInfo = args.DictDelFriendInfo
                Me.mDictFriendInfoEx = args.DictFriendInfo

                FindResult()

                Me.wbsChat.Navigate(ChatWebPage.WebPagePath)
                Me.mDictDelFriendInfo = Me.SortFriendInfo(Me.mDictDelFriendInfo, SortType.ASC)
                Dim rowFindData As List(Of tbDataGridViewRow) = Me.AddDictToDataGridView(Me.mDictDelFriendInfo, False, True)
                Me.LoadHeadIcon(rowFindData)
                Me.UpdateDataGridViewRow()

                Me.ShowFindData(False)
                Common.KillProcess("tbWCRHelper")
                isKill = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "FWCDHelper_WCRProgressEventHandler")
        End Try
        Try
            If isKill Then
                If Me.mFQQHelper IsNot Nothing Then
                    RemoveHandler Me.mFQQHelper.QQRProgressEventHandler, AddressOf FWCDHelper_WCRProgressEventHandler
                    Me.mFQQHelper.Dispose()
                    Me.mFQQHelper = Nothing
                End If
            End If
        Catch
        End Try
    End Sub

    Private Delegate Sub SetProgressHandler(ByVal strProgress As String)
    Private Sub SetProgress(ByVal strProgress As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetProgressHandler(AddressOf SetProgress), strProgress)
        Else
            Try
                Me.lblDoing.Text = String.Format("{0}%", strProgress)
                Application.DoEvents()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetProgress")
            End Try
        End If
    End Sub

    Private Delegate Sub SetWeiXinProgressHandler(ByVal strProgress As String)
    Public Sub SetWeiXinProgress(ByVal strProgress As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetWeiXinProgressHandler(AddressOf SetWeiXinProgress), strProgress)
        Else
            Try
                Me.lblLoading.Text = String.Format("{0}%", strProgress)
                Application.DoEvents()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetWeiXinProgress")
            End Try
        End If
    End Sub

    Private Delegate Sub ShowFindDataHandler(ByVal blnVisible As Boolean)
    Private Sub ShowFindData(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowFindDataHandler(AddressOf ShowFindData), blnVisible)
        Else
            Me.pnlFindData.Visible = blnVisible
            If Me.pnlFindData.Visible Then
                Me.pnlFindData.BringToFront()
            End If

            'Me.btnBackup.Enabled = Not blnVisible
            'Me.btnRestore.Enabled = Not blnVisible
            'Me.btnRefresh.Enabled = Not blnVisible
            Me.btnFinalData.Enabled = Not blnVisible
            Me.btnDataRecoveryToPhone.Enabled = Not blnVisible
            Me.btnWeCahtUsers.Enabled = Not blnVisible
            Me.txtSearchChat.ReadOnly = blnVisible
            Me.btnOK.Enabled = Not blnVisible
            Me.btnTime.Enabled = Not blnVisible
            'Me.txtSearchFriend.ReadOnly = blnVisible
            Me.btnFilter.Enabled = Not blnVisible
            Me.dgvFriend.Enabled = Not blnVisible
            Me.btnTimeScreen.Enabled = Not blnVisible
            Me.picTimeScreen.Enabled = Not blnVisible
            Me.btnExportSetting.Enabled = Not blnVisible
            Me.btnExport.Enabled = Not blnVisible
            Me.llblRefresh.Enabled = Not blnVisible
            'If Me.mFrmWeChatMain IsNot Nothing Then
            '    Me.mFrmWeChatMain.llblRefresh.Enabled = Not blnVisible
            'End If
        End If
    End Sub

    Private Sub FindResult()
        If Me.mDictDelChatInfo.Count = 0 Then
            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Button.RecoveredNoData")) '"对不起！未找回任何删除数据。"
            Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
        Else
            Dim iCount As Integer = 0
            For Each temp As KeyValuePair(Of String, List(Of QQChatInfo)) In Me.mDictDelChatInfo
                iCount = iCount + temp.Value.Count
            Next
            Dim strText As String = String.Format(Me.Language.GetString("Weixin.Lable.FindRecords"), String.Format("[{0}]", iCount)) '"共找回{0}条删除的数据"
            Dim colSelect As Color = Color.FromArgb(5, 152, 245)
            Me.Selection(strText, Me.rtxtFindDelCount, "[", "]", colSelect)
            Me.lblSystemInfo.Visible = False
            Me.ShowShare(True, False)
        End If
    End Sub

    Private Delegate Sub SelectionHandler(ByVal strText As String, rtxt As RichTextBox, ByVal strTagState As String, ByVal strTagEnd As String, ByVal colSelect As Color, ByVal align As HorizontalAlignment)
    Private Sub Selection(ByVal strText As String, rtxt As RichTextBox, ByVal strTagState As String, ByVal strTagEnd As String, ByVal colSelect As Color, Optional ByVal align As HorizontalAlignment = HorizontalAlignment.Center)
        If Me.InvokeRequired Then
            Me.Invoke(New SelectionHandler(AddressOf Selection), strText, rtxt, strTagState, strTagEnd, colSelect, align)
        Else
            Try
                If strText.Length = 0 Then
                    Return
                End If
                Dim iStart As Integer = strText.IndexOf(strTagState)
                Dim iLength As Integer = strText.IndexOf(strTagEnd) - iStart - 1
                rtxt.Text = strText
                Try
                    rtxt.SelectionStart = iStart + 1
                    rtxt.SelectionLength = iLength
                    rtxt.SelectionColor = colSelect
                Catch ex As Exception
                End Try
                rtxt.SelectionAlignment = align
            Catch ex As Exception
                Common.LogException(ex.ToString(), "Selection")
            End Try
        End If
    End Sub

    Private Delegate Function AddFindDeleteDataGridViewHandler(ByVal wcfInfo As QQFriendInfo) As List(Of tbDataGridViewRow)
    Private Function AddFindDeleteDataGridView(ByVal wcfInfo As QQFriendInfo) As List(Of tbDataGridViewRow)
        Dim rowFindData As New List(Of tbDataGridViewRow)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New AddFindDeleteDataGridViewHandler(AddressOf AddFindDeleteDataGridView), New Object() {wcfInfo})
            Else
                Me.dgvFriend.SuspendLayout()

                Try
                    Dim row As New tbDataGridViewRow()
                    row.Height = 58

                    Dim strName As String = wcfInfo.UsrName
                    If wcfInfo.NoteName IsNot Nothing AndAlso wcfInfo.NoteName.Length > 0 Then
                        strName = Utility.ReplaceWinIllegalName(wcfInfo.NoteName)
                    ElseIf wcfInfo.NickName IsNot Nothing Then
                        strName = Utility.ReplaceWinIllegalName(wcfInfo.NickName)
                    End If

                    row.CreateCells(Me.dgvFriend, _
                                    False, _
                                    "", _
                                    strName & vbCrLf & Me.OperateLastChatText(wcfInfo.StrLastSendMsg), _
                                    Me.GetLastChatTime(wcfInfo.DTLastSendTime) & vbCrLf & " ", _
                                    wcfInfo.IsFindDate, _
                                    wcfInfo.StrQQNum)


                    '先检测是否已经存在如果已经存在要先删除 在添加到最前面
                    Me.FindGridViewRow(wcfInfo.StrQQNum)
                    Me.dgvFriend.Rows.InsertRange(0, row)
                    Me.mIsAddRow = True
                    rowFindData.Add(row)
                    Me.mIsAddRow = False

                    Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                    cellName.tbIconShadow = True
                    cellName.tbIconSize = Me.mAppIconSize
                    cellName.tbIcon = Me.mDefaultHeadIcon
                    Dim isLock As Boolean = False

                    Dim colStatus As Color = Color.FromArgb(255, 92, 92)
                    cellName.tbForeColor = colStatus
                    cellName.tbSelectionForeColor = colStatus

                    If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
                        isLock = False
                    Else
                        isLock = True
                    End If

                    cellName.ShowWechatMessageLock = isLock
                    row.Tag = wcfInfo
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "AddFindDeleteDataGridView")
                End Try

                Me.SetLinkmanMsg()

                Me.dgvFriend.ResumeLayout()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
        Return rowFindData
    End Function

    Private Delegate Sub ShowShareHandler(ByVal blnVisible As Boolean, ByVal isClick As Boolean)
    Private Sub ShowShare(ByVal blnVisible As Boolean, ByVal isClick As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowShareHandler(AddressOf ShowShare), blnVisible, isClick)
        Else
            If Not blnVisible Then
                'Me.pnlClickShare.Visible = blnVisible
                Me.pnlShare.Visible = blnVisible
                Return
            End If

            If isClick Then
                'Me.pnlClickShare.Visible = True
                'Me.pnlClickShare.BringToFront()
                Me.pnlShare.Visible = False
            Else
                'Me.pnlClickShare.Visible = False
                Me.pnlShare.Visible = True
            End If

            If Me.pnlShare.Visible Then
                Me.pnlShare.BringToFront()
            End If
        End If
    End Sub
#End Region


    Private Sub dtpStart_ValueChanged(sender As Object, e As EventArgs) Handles dtpStart.ValueChanged
        If Me.dtpStart.Value > Me.dtpEnd.Value Then
            Me.dtpEnd.Value = Me.dtpStart.Value
        End If
    End Sub

    Private Sub dtpEnd_ValueChanged(sender As Object, e As EventArgs) Handles dtpEnd.ValueChanged
        If Me.dtpStart.Value > Me.dtpEnd.Value Then
            Me.dtpStart.Value = Me.dtpEnd.Value
        End If
    End Sub
End Class

Public Enum QQViewStyle
    Loading
    Uninstalled
    Content
End Enum

#Region "---  FriendInfo按照LastChatTime排序  ---"

Public Class QQLastChatTimeComparer
    Implements IComparer(Of QQFriendInfo)

    Private type As SortType = SortType.ASC

    Public Sub New(ByVal tempType As SortType)
        type = tempType
    End Sub

    Public Function Compare(ByVal x As QQFriendInfo, ByVal y As QQFriendInfo) As Integer Implements System.Collections.Generic.IComparer(Of QQFriendInfo).Compare
        Dim result As Integer = 0

        If type = SortType.ASC Then
            result = x.DTLastSendTime.CompareTo(y.DTLastSendTime)
        Else
            result = y.DTLastSendTime.CompareTo(x.DTLastSendTime)
        End If

        Return result
    End Function

End Class

#End Region


Public Class QQCreateTimeComparer
    Implements IComparer(Of QQChatInfo)

    Private type As SortType = SortType.ASC

    Public Sub New(ByVal tempType As SortType)
        type = tempType
    End Sub

    Public Function Compare(ByVal x As QQChatInfo, ByVal y As QQChatInfo) As Integer Implements System.Collections.Generic.IComparer(Of QQChatInfo).Compare
        Dim result As Integer = 0

        If type = SortType.ASC Then
            result = x.DTCreateTime.CompareTo(y.DTCreateTime)
        Else
            result = y.DTCreateTime.CompareTo(x.DTCreateTime)
        End If

        Return result
    End Function

End Class