﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmQQ
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmQQ))
        Me.tlpQQ = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlLoad = New iTong.Components.tbPanel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.pnlError = New iTong.Components.tbPanel()
        Me.lblError = New System.Windows.Forms.Label()
        Me.picError = New System.Windows.Forms.PictureBox()
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnFinalData = New iTong.Components.tbButton()
        Me.TbPanel3 = New iTong.Components.tbPanel()
        Me.btnBack = New iTong.Components.tbButton()
        Me.picTimeScreen = New System.Windows.Forms.PictureBox()
        Me.btnExportSetting = New iTong.Components.tbButton()
        Me.btnWeCahtUsers = New iTong.Components.tbButton()
        Me.txtSearchChat = New iTong.Components.tbSearch()
        Me.btnTimeScreen = New iTong.Components.tbButton()
        Me.pnlContent = New iTong.Components.tbPanel()
        Me.pnlWeb = New iTong.Components.tbPanel()
        Me.pnlCount = New System.Windows.Forms.Panel()
        Me.rtxtPrompt = New System.Windows.Forms.RichTextBox()
        Me.pnlFindData = New iTong.Components.tbPanel()
        Me.lblFindData = New iTong.Components.tbLabel()
        Me.lblDoing = New iTong.Components.tbLabel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.RichTextBox2 = New System.Windows.Forms.RichTextBox()
        Me.pnlShare = New System.Windows.Forms.Panel()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.rtxtFindDataInfo = New System.Windows.Forms.RichTextBox()
        Me.rtxtFindDelCount = New System.Windows.Forms.RichTextBox()
        Me.pnlLoadData = New iTong.Components.tbPanel()
        Me.lblLoadData = New iTong.Components.tbLabel()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.lblSystemInfo = New iTong.Components.tbLabel()
        Me.wbsChat = New iTong.Components.tbWebBrowserEx()
        Me.pnlTimeSelect = New iTong.Components.tbPanel()
        Me.TbPanel4 = New iTong.Components.tbPanel()
        Me.btnTime = New iTong.Components.tbButton()
        Me.btnOK = New iTong.Components.tbButton()
        Me.dtpStart = New System.Windows.Forms.DateTimePicker()
        Me.lblStartTime = New System.Windows.Forms.Label()
        Me.dtpEnd = New System.Windows.Forms.DateTimePicker()
        Me.lblEndTime = New System.Windows.Forms.Label()
        Me.pnlBottomMessage = New iTong.Components.tbPanel()
        Me.TbPanel6 = New iTong.Components.tbPanel()
        Me.btnExport = New iTong.Components.tbButton()
        Me.llblRefresh = New System.Windows.Forms.LinkLabel()
        Me.lblState = New iTong.Components.tbLabel()
        Me.lblFromBackupMsg = New iTong.Components.tbLabel()
        Me.btnDataRecoveryToPhone = New iTong.Components.tbButton()
        Me.pnlLeft = New iTong.Components.tbPanel()
        Me.TbPanel9 = New iTong.Components.tbPanel()
        Me.lblLinkmanMsg = New iTong.Components.tbLabel()
        Me.TbPanel7 = New iTong.Components.tbPanel()
        Me.TbPanel1 = New iTong.Components.tbPanel()
        Me.TbPanel8 = New iTong.Components.tbPanel()
        Me.TbPanel5 = New iTong.Components.tbPanel()
        Me.cbxSelectAll = New iTong.Components.tbCheckBox()
        Me.btnFilter = New iTong.Components.tbButton()
        Me.pnlFriend = New iTong.Components.tbPanel()
        Me.TbPanel2 = New iTong.Components.tbPanel()
        Me.btnBackLoading = New iTong.Components.tbButton()
        Me.bgwLoadFriend = New System.ComponentModel.BackgroundWorker()
        Me.cmsExportNews = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.cmsShowSession = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiShowAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShowDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShowNotDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.cmsExportEx = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiFormat = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExcelEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTxtEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiHtml = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiMedia = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiPicEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAudioEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiVideoEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator2 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.lblReadError = New iTong.tbBattery()
        Me.lblRegister = New iTong.tbBattery()
        Me.tlpQQ.SuspendLayout()
        Me.pnlLoad.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlError.SuspendLayout()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTop.SuspendLayout()
        CType(Me.picTimeScreen, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlContent.SuspendLayout()
        Me.pnlWeb.SuspendLayout()
        Me.pnlCount.SuspendLayout()
        Me.pnlFindData.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlShare.SuspendLayout()
        Me.pnlLoadData.SuspendLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTimeSelect.SuspendLayout()
        Me.pnlBottomMessage.SuspendLayout()
        Me.pnlLeft.SuspendLayout()
        Me.TbPanel9.SuspendLayout()
        Me.TbPanel1.SuspendLayout()
        Me.TbPanel2.SuspendLayout()
        Me.cmsShowSession.SuspendLayout()
        Me.cmsExportEx.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(1471, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(1447, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(1423, 0)
        '
        'tlpQQ
        '
        Me.tlpQQ.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tlpQQ.BackColor = System.Drawing.Color.White
        Me.tlpQQ.ColumnCount = 4
        Me.tlpQQ.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.tlpQQ.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 200.0!))
        Me.tlpQQ.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 294.0!))
        Me.tlpQQ.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpQQ.Controls.Add(Me.pnlLoad, 0, 1)
        Me.tlpQQ.Controls.Add(Me.pnlError, 1, 1)
        Me.tlpQQ.Controls.Add(Me.pnlTop, 2, 0)
        Me.tlpQQ.Controls.Add(Me.pnlContent, 3, 1)
        Me.tlpQQ.Controls.Add(Me.pnlBottomMessage, 2, 3)
        Me.tlpQQ.Controls.Add(Me.pnlLeft, 2, 1)
        Me.tlpQQ.Controls.Add(Me.TbPanel2, 0, 0)
        Me.tlpQQ.Location = New System.Drawing.Point(2, 32)
        Me.tlpQQ.Margin = New System.Windows.Forms.Padding(0)
        Me.tlpQQ.Name = "tlpQQ"
        Me.tlpQQ.RowCount = 4
        Me.tlpQQ.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.tlpQQ.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 41.0!))
        Me.tlpQQ.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpQQ.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.tlpQQ.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.tlpQQ.Size = New System.Drawing.Size(1492, 757)
        Me.tlpQQ.TabIndex = 22
        '
        'pnlLoad
        '
        Me.pnlLoad.BackColor = System.Drawing.Color.White
        Me.pnlLoad.Controls.Add(Me.lblLoading)
        Me.pnlLoad.Controls.Add(Me.picLoading)
        Me.pnlLoad.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlLoad.Location = New System.Drawing.Point(0, 40)
        Me.pnlLoad.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoad.Name = "pnlLoad"
        Me.tlpQQ.SetRowSpan(Me.pnlLoad, 3)
        Me.pnlLoad.Size = New System.Drawing.Size(200, 717)
        Me.pnlLoad.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoad.TabIndex = 0
        Me.pnlLoad.tbBackgroundImage = Nothing
        Me.pnlLoad.tbShowWatermark = False
        Me.pnlLoad.tbSplit = "0,0,0,0"
        Me.pnlLoad.tbWatermark = Nothing
        Me.pnlLoad.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoad.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(71, 321)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(150, 45)
        Me.lblLoading.TabIndex = 15
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picLoading
        '
        Me.picLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading.BackColor = System.Drawing.Color.White
        Me.picLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.picLoading.Location = New System.Drawing.Point(44, 331)
        Me.picLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(24, 24)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picLoading.TabIndex = 14
        Me.picLoading.TabStop = False
        '
        'pnlError
        '
        Me.pnlError.BackColor = System.Drawing.Color.White
        Me.pnlError.Controls.Add(Me.lblReadError)
        Me.pnlError.Controls.Add(Me.lblError)
        Me.pnlError.Controls.Add(Me.picError)
        Me.pnlError.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlError.Location = New System.Drawing.Point(200, 40)
        Me.pnlError.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlError.Name = "pnlError"
        Me.tlpQQ.SetRowSpan(Me.pnlError, 3)
        Me.pnlError.Size = New System.Drawing.Size(200, 717)
        Me.pnlError.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlError.TabIndex = 0
        Me.pnlError.tbBackgroundImage = Nothing
        Me.pnlError.tbShowWatermark = False
        Me.pnlError.tbSplit = "0,0,0,0"
        Me.pnlError.tbWatermark = Nothing
        Me.pnlError.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlError.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblError
        '
        Me.lblError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblError.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblError.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblError.Location = New System.Drawing.Point(-30, 401)
        Me.lblError.Name = "lblError"
        Me.lblError.Size = New System.Drawing.Size(260, 45)
        Me.lblError.TabIndex = 97
        Me.lblError.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picError
        '
        Me.picError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picError.BackColor = System.Drawing.Color.Transparent
        Me.picError.Image = Global.iTong.My.Resources.Resources.icon_readerror
        Me.picError.Location = New System.Drawing.Point(60, 240)
        Me.picError.Margin = New System.Windows.Forms.Padding(0)
        Me.picError.Name = "picError"
        Me.picError.Size = New System.Drawing.Size(81, 102)
        Me.picError.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picError.TabIndex = 96
        Me.picError.TabStop = False
        '
        'pnlTop
        '
        Me.pnlTop.BackColor = System.Drawing.Color.White
        Me.tlpQQ.SetColumnSpan(Me.pnlTop, 2)
        Me.pnlTop.Controls.Add(Me.btnFinalData)
        Me.pnlTop.Controls.Add(Me.TbPanel3)
        Me.pnlTop.Controls.Add(Me.btnBack)
        Me.pnlTop.Controls.Add(Me.picTimeScreen)
        Me.pnlTop.Controls.Add(Me.btnExportSetting)
        Me.pnlTop.Controls.Add(Me.btnWeCahtUsers)
        Me.pnlTop.Controls.Add(Me.txtSearchChat)
        Me.pnlTop.Controls.Add(Me.btnTimeScreen)
        Me.pnlTop.Controls.Add(Me.lblRegister)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTop.Location = New System.Drawing.Point(400, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(1092, 40)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 0
        Me.pnlTop.tbBackgroundImage = Nothing
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnFinalData
        '
        Me.btnFinalData.BackColor = System.Drawing.Color.Transparent
        Me.btnFinalData.BindingForm = Nothing
        Me.btnFinalData.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFinalData.Location = New System.Drawing.Point(201, 9)
        Me.btnFinalData.Margin = New System.Windows.Forms.Padding(0)
        Me.btnFinalData.Name = "btnFinalData"
        Me.btnFinalData.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFinalData.Selectable = True
        Me.btnFinalData.Size = New System.Drawing.Size(81, 23)
        Me.btnFinalData.TabIndex = 105
        Me.btnFinalData.tbAdriftIconWhenHover = False
        Me.btnFinalData.tbAutoSize = False
        Me.btnFinalData.tbAutoSizeEx = True
        Me.btnFinalData.tbBackgroundImage = Nothing
        Me.btnFinalData.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnFinalData.tbBadgeNumber = 0
        Me.btnFinalData.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFinalData.tbEndEllipsis = False
        Me.btnFinalData.tbIconHoldPlace = True
        Me.btnFinalData.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_finddata
        Me.btnFinalData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFinalData.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnFinalData.tbIconMore = False
        Me.btnFinalData.tbIconMouseDown = Nothing
        Me.btnFinalData.tbIconMouseHover = Nothing
        Me.btnFinalData.tbIconMouseLeave = Nothing
        Me.btnFinalData.tbIconPlaceText = 2
        Me.btnFinalData.tbIconReadOnly = Nothing
        Me.btnFinalData.tbImageMouseDown = Nothing
        Me.btnFinalData.tbImageMouseHover = Nothing
        Me.btnFinalData.tbImageMouseLeave = Nothing
        Me.btnFinalData.tbProgressValue = 50
        Me.btnFinalData.tbReadOnly = False
        Me.btnFinalData.tbReadOnlyText = False
        Me.btnFinalData.tbShadow = False
        Me.btnFinalData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFinalData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFinalData.tbShowDot = False
        Me.btnFinalData.tbShowMoreIconImg = CType(resources.GetObject("btnFinalData.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFinalData.tbShowNew = False
        Me.btnFinalData.tbShowProgress = False
        Me.btnFinalData.tbShowTip = True
        Me.btnFinalData.tbShowToolTipOnButton = False
        Me.btnFinalData.tbSplit = "0,0,0,0"
        Me.btnFinalData.tbText = "找回数据"
        Me.btnFinalData.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFinalData.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnFinalData.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnFinalData.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnFinalData.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnFinalData.tbTextMouseDownPlace = 2
        Me.btnFinalData.tbToolTip = ""
        Me.btnFinalData.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFinalData.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFinalData.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFinalData.VisibleEx = True
        '
        'TbPanel3
        '
        Me.TbPanel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.TbPanel3.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.TbPanel3.Location = New System.Drawing.Point(0, 39)
        Me.TbPanel3.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel3.Name = "TbPanel3"
        Me.TbPanel3.Size = New System.Drawing.Size(1092, 1)
        Me.TbPanel3.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel3.TabIndex = 104
        Me.TbPanel3.tbBackgroundImage = Nothing
        Me.TbPanel3.tbShowWatermark = False
        Me.TbPanel3.tbSplit = "0,0,0,0"
        Me.TbPanel3.tbWatermark = Nothing
        Me.TbPanel3.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel3.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnBack
        '
        Me.btnBack.BackColor = System.Drawing.Color.Transparent
        Me.btnBack.BindingForm = Nothing
        Me.btnBack.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBack.Location = New System.Drawing.Point(10, 12)
        Me.btnBack.Name = "btnBack"
        Me.btnBack.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBack.Selectable = True
        Me.btnBack.Size = New System.Drawing.Size(16, 14)
        Me.btnBack.TabIndex = 103
        Me.btnBack.tbAdriftIconWhenHover = False
        Me.btnBack.tbAutoSize = False
        Me.btnBack.tbAutoSizeEx = True
        Me.btnBack.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_retreat
        Me.btnBack.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBack.tbBadgeNumber = 0
        Me.btnBack.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBack.tbEndEllipsis = False
        Me.btnBack.tbIconHoldPlace = True
        Me.btnBack.tbIconImage = Nothing
        Me.btnBack.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBack.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnBack.tbIconMore = False
        Me.btnBack.tbIconMouseDown = Nothing
        Me.btnBack.tbIconMouseHover = Nothing
        Me.btnBack.tbIconMouseLeave = Nothing
        Me.btnBack.tbIconPlaceText = 2
        Me.btnBack.tbIconReadOnly = Nothing
        Me.btnBack.tbImageMouseDown = Nothing
        Me.btnBack.tbImageMouseHover = Nothing
        Me.btnBack.tbImageMouseLeave = Nothing
        Me.btnBack.tbProgressValue = 50
        Me.btnBack.tbReadOnly = False
        Me.btnBack.tbReadOnlyText = False
        Me.btnBack.tbShadow = False
        Me.btnBack.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBack.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBack.tbShowDot = False
        Me.btnBack.tbShowMoreIconImg = CType(resources.GetObject("btnBack.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBack.tbShowNew = False
        Me.btnBack.tbShowProgress = False
        Me.btnBack.tbShowTip = True
        Me.btnBack.tbShowToolTipOnButton = False
        Me.btnBack.tbSplit = "12,1,1,1"
        Me.btnBack.tbText = ""
        Me.btnBack.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnBack.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnBack.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBack.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBack.tbTextMouseDownPlace = 2
        Me.btnBack.tbToolTip = ""
        Me.btnBack.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBack.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnBack.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.VisibleEx = True
        '
        'picTimeScreen
        '
        Me.picTimeScreen.Image = Global.iTong.My.Resources.Resources.TimeScreenUpDowm
        Me.picTimeScreen.Location = New System.Drawing.Point(391, 7)
        Me.picTimeScreen.Margin = New System.Windows.Forms.Padding(0)
        Me.picTimeScreen.Name = "picTimeScreen"
        Me.picTimeScreen.Size = New System.Drawing.Size(9, 24)
        Me.picTimeScreen.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picTimeScreen.TabIndex = 100
        Me.picTimeScreen.TabStop = False
        '
        'btnExportSetting
        '
        Me.btnExportSetting.BackColor = System.Drawing.Color.Transparent
        Me.btnExportSetting.BindingForm = Nothing
        Me.btnExportSetting.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExportSetting.Location = New System.Drawing.Point(404, 8)
        Me.btnExportSetting.Margin = New System.Windows.Forms.Padding(0)
        Me.btnExportSetting.Name = "btnExportSetting"
        Me.btnExportSetting.Padding = New System.Windows.Forms.Padding(5, 2, 5, 0)
        Me.btnExportSetting.Selectable = True
        Me.btnExportSetting.Size = New System.Drawing.Size(81, 23)
        Me.btnExportSetting.TabIndex = 99
        Me.btnExportSetting.tbAdriftIconWhenHover = False
        Me.btnExportSetting.tbAutoSize = False
        Me.btnExportSetting.tbAutoSizeEx = True
        Me.btnExportSetting.tbBackgroundImage = Nothing
        Me.btnExportSetting.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnExportSetting.tbBadgeNumber = 0
        Me.btnExportSetting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExportSetting.tbEndEllipsis = False
        Me.btnExportSetting.tbIconHoldPlace = True
        Me.btnExportSetting.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_exportsetting
        Me.btnExportSetting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExportSetting.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExportSetting.tbIconMore = False
        Me.btnExportSetting.tbIconMouseDown = Nothing
        Me.btnExportSetting.tbIconMouseHover = Nothing
        Me.btnExportSetting.tbIconMouseLeave = Nothing
        Me.btnExportSetting.tbIconPlaceText = 2
        Me.btnExportSetting.tbIconReadOnly = Nothing
        Me.btnExportSetting.tbImageMouseDown = Nothing
        Me.btnExportSetting.tbImageMouseHover = Nothing
        Me.btnExportSetting.tbImageMouseLeave = Nothing
        Me.btnExportSetting.tbProgressValue = 50
        Me.btnExportSetting.tbReadOnly = False
        Me.btnExportSetting.tbReadOnlyText = False
        Me.btnExportSetting.tbShadow = False
        Me.btnExportSetting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExportSetting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExportSetting.tbShowDot = False
        Me.btnExportSetting.tbShowMoreIconImg = CType(resources.GetObject("btnExportSetting.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExportSetting.tbShowNew = False
        Me.btnExportSetting.tbShowProgress = False
        Me.btnExportSetting.tbShowTip = True
        Me.btnExportSetting.tbShowToolTipOnButton = False
        Me.btnExportSetting.tbSplit = "0,0,0,0"
        Me.btnExportSetting.tbText = "导出设置"
        Me.btnExportSetting.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportSetting.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnExportSetting.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnExportSetting.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExportSetting.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExportSetting.tbTextMouseDownPlace = 2
        Me.btnExportSetting.tbToolTip = ""
        Me.btnExportSetting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExportSetting.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExportSetting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportSetting.VisibleEx = True
        '
        'btnWeCahtUsers
        '
        Me.btnWeCahtUsers.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnWeCahtUsers.BackColor = System.Drawing.Color.White
        Me.btnWeCahtUsers.BindingForm = Nothing
        Me.btnWeCahtUsers.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnWeCahtUsers.Location = New System.Drawing.Point(39, 8)
        Me.btnWeCahtUsers.Margin = New System.Windows.Forms.Padding(0)
        Me.btnWeCahtUsers.Name = "btnWeCahtUsers"
        Me.btnWeCahtUsers.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnWeCahtUsers.Selectable = True
        Me.btnWeCahtUsers.Size = New System.Drawing.Size(136, 24)
        Me.btnWeCahtUsers.TabIndex = 71
        Me.btnWeCahtUsers.tbAdriftIconWhenHover = False
        Me.btnWeCahtUsers.tbAutoSize = False
        Me.btnWeCahtUsers.tbAutoSizeEx = False
        Me.btnWeCahtUsers.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_bg_border
        Me.btnWeCahtUsers.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnWeCahtUsers.tbBadgeNumber = 0
        Me.btnWeCahtUsers.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWeCahtUsers.tbEndEllipsis = False
        Me.btnWeCahtUsers.tbIconHoldPlace = True
        Me.btnWeCahtUsers.tbIconImage = Nothing
        Me.btnWeCahtUsers.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWeCahtUsers.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnWeCahtUsers.tbIconMore = True
        Me.btnWeCahtUsers.tbIconMouseDown = Nothing
        Me.btnWeCahtUsers.tbIconMouseHover = Nothing
        Me.btnWeCahtUsers.tbIconMouseLeave = Nothing
        Me.btnWeCahtUsers.tbIconPlaceText = 2
        Me.btnWeCahtUsers.tbIconReadOnly = Nothing
        Me.btnWeCahtUsers.tbImageMouseDown = Nothing
        Me.btnWeCahtUsers.tbImageMouseHover = Nothing
        Me.btnWeCahtUsers.tbImageMouseLeave = Nothing
        Me.btnWeCahtUsers.tbProgressValue = 50
        Me.btnWeCahtUsers.tbReadOnly = False
        Me.btnWeCahtUsers.tbReadOnlyText = False
        Me.btnWeCahtUsers.tbShadow = False
        Me.btnWeCahtUsers.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnWeCahtUsers.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnWeCahtUsers.tbShowDot = False
        Me.btnWeCahtUsers.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnWeCahtUsers.tbShowNew = False
        Me.btnWeCahtUsers.tbShowProgress = False
        Me.btnWeCahtUsers.tbShowTip = True
        Me.btnWeCahtUsers.tbShowToolTipOnButton = False
        Me.btnWeCahtUsers.tbSplit = "2,3,5,3"
        Me.btnWeCahtUsers.tbText = ""
        Me.btnWeCahtUsers.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWeCahtUsers.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWeCahtUsers.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWeCahtUsers.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWeCahtUsers.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnWeCahtUsers.tbTextMouseDownPlace = 2
        Me.btnWeCahtUsers.tbToolTip = ""
        Me.btnWeCahtUsers.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnWeCahtUsers.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWeCahtUsers.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWeCahtUsers.VisibleEx = True
        '
        'txtSearchChat
        '
        Me.txtSearchChat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearchChat.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchChat.Location = New System.Drawing.Point(919, 8)
        Me.txtSearchChat.MaxLength = 32767
        Me.txtSearchChat.Name = "txtSearchChat"
        Me.txtSearchChat.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchChat.ReadOnly = False
        Me.txtSearchChat.SearchText = ""
        Me.txtSearchChat.SearchTipText = "Search"
        Me.txtSearchChat.ShowClear = True
        Me.txtSearchChat.ShowClearAlways = False
        Me.txtSearchChat.ShowMore = False
        Me.txtSearchChat.ShowSearch = True
        Me.txtSearchChat.Size = New System.Drawing.Size(170, 23)
        Me.txtSearchChat.TabIndex = 69
        Me.txtSearchChat.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchChat.Timer = True
        Me.txtSearchChat.TimerInterval = 0.5R
        '
        'btnTimeScreen
        '
        Me.btnTimeScreen.BackColor = System.Drawing.Color.Transparent
        Me.btnTimeScreen.BindingForm = Nothing
        Me.btnTimeScreen.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnTimeScreen.Location = New System.Drawing.Point(298, 8)
        Me.btnTimeScreen.Margin = New System.Windows.Forms.Padding(0)
        Me.btnTimeScreen.Name = "btnTimeScreen"
        Me.btnTimeScreen.Padding = New System.Windows.Forms.Padding(5, 2, 5, 0)
        Me.btnTimeScreen.Selectable = True
        Me.btnTimeScreen.Size = New System.Drawing.Size(93, 23)
        Me.btnTimeScreen.TabIndex = 101
        Me.btnTimeScreen.tbAdriftIconWhenHover = False
        Me.btnTimeScreen.tbAutoSize = False
        Me.btnTimeScreen.tbAutoSizeEx = True
        Me.btnTimeScreen.tbBackgroundImage = Nothing
        Me.btnTimeScreen.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnTimeScreen.tbBadgeNumber = 0
        Me.btnTimeScreen.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTimeScreen.tbEndEllipsis = False
        Me.btnTimeScreen.tbIconHoldPlace = True
        Me.btnTimeScreen.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_timescreen
        Me.btnTimeScreen.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnTimeScreen.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnTimeScreen.tbIconMore = False
        Me.btnTimeScreen.tbIconMouseDown = Nothing
        Me.btnTimeScreen.tbIconMouseHover = Nothing
        Me.btnTimeScreen.tbIconMouseLeave = Nothing
        Me.btnTimeScreen.tbIconPlaceText = 2
        Me.btnTimeScreen.tbIconReadOnly = Nothing
        Me.btnTimeScreen.tbImageMouseDown = Nothing
        Me.btnTimeScreen.tbImageMouseHover = Nothing
        Me.btnTimeScreen.tbImageMouseLeave = Nothing
        Me.btnTimeScreen.tbProgressValue = 50
        Me.btnTimeScreen.tbReadOnly = False
        Me.btnTimeScreen.tbReadOnlyText = False
        Me.btnTimeScreen.tbShadow = False
        Me.btnTimeScreen.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTimeScreen.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTimeScreen.tbShowDot = False
        Me.btnTimeScreen.tbShowMoreIconImg = CType(resources.GetObject("btnTimeScreen.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnTimeScreen.tbShowNew = False
        Me.btnTimeScreen.tbShowProgress = False
        Me.btnTimeScreen.tbShowTip = True
        Me.btnTimeScreen.tbShowToolTipOnButton = False
        Me.btnTimeScreen.tbSplit = "0,0,0,0"
        Me.btnTimeScreen.tbText = "按时间筛选"
        Me.btnTimeScreen.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTimeScreen.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTimeScreen.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnTimeScreen.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnTimeScreen.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnTimeScreen.tbTextMouseDownPlace = 2
        Me.btnTimeScreen.tbToolTip = ""
        Me.btnTimeScreen.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnTimeScreen.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTimeScreen.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTimeScreen.VisibleEx = True
        '
        'pnlContent
        '
        Me.pnlContent.Controls.Add(Me.pnlWeb)
        Me.pnlContent.Controls.Add(Me.pnlTimeSelect)
        Me.pnlContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContent.Location = New System.Drawing.Point(694, 40)
        Me.pnlContent.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContent.Name = "pnlContent"
        Me.tlpQQ.SetRowSpan(Me.pnlContent, 2)
        Me.pnlContent.Size = New System.Drawing.Size(798, 637)
        Me.pnlContent.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContent.TabIndex = 0
        Me.pnlContent.tbBackgroundImage = Nothing
        Me.pnlContent.tbShowWatermark = False
        Me.pnlContent.tbSplit = "0,0,0,0"
        Me.pnlContent.tbWatermark = Nothing
        Me.pnlContent.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContent.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlWeb
        '
        Me.pnlWeb.Controls.Add(Me.pnlCount)
        Me.pnlWeb.Controls.Add(Me.pnlFindData)
        Me.pnlWeb.Controls.Add(Me.pnlShare)
        Me.pnlWeb.Controls.Add(Me.pnlLoadData)
        Me.pnlWeb.Controls.Add(Me.lblSystemInfo)
        Me.pnlWeb.Controls.Add(Me.wbsChat)
        Me.pnlWeb.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlWeb.Location = New System.Drawing.Point(0, 40)
        Me.pnlWeb.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlWeb.Name = "pnlWeb"
        Me.pnlWeb.Size = New System.Drawing.Size(798, 597)
        Me.pnlWeb.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlWeb.TabIndex = 3
        Me.pnlWeb.tbBackgroundImage = Nothing
        Me.pnlWeb.tbShowWatermark = False
        Me.pnlWeb.tbSplit = "0,0,0,0"
        Me.pnlWeb.tbWatermark = Nothing
        Me.pnlWeb.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlWeb.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlCount
        '
        Me.pnlCount.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlCount.BackColor = System.Drawing.Color.FromArgb(CType(CType(208, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.pnlCount.Controls.Add(Me.rtxtPrompt)
        Me.pnlCount.Location = New System.Drawing.Point(0, 573)
        Me.pnlCount.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlCount.Name = "pnlCount"
        Me.pnlCount.Size = New System.Drawing.Size(798, 24)
        Me.pnlCount.TabIndex = 14
        '
        'rtxtPrompt
        '
        Me.rtxtPrompt.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rtxtPrompt.BackColor = System.Drawing.Color.FromArgb(CType(CType(208, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.rtxtPrompt.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtPrompt.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtPrompt.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.rtxtPrompt.Location = New System.Drawing.Point(4, 5)
        Me.rtxtPrompt.Multiline = False
        Me.rtxtPrompt.Name = "rtxtPrompt"
        Me.rtxtPrompt.ReadOnly = True
        Me.rtxtPrompt.ShowSelectionMargin = True
        Me.rtxtPrompt.Size = New System.Drawing.Size(790, 14)
        Me.rtxtPrompt.TabIndex = 12
        Me.rtxtPrompt.Text = "红色标识为找回数据，当前联系人找回18条"
        '
        'pnlFindData
        '
        Me.pnlFindData.BackColor = System.Drawing.Color.White
        Me.pnlFindData.Controls.Add(Me.lblFindData)
        Me.pnlFindData.Controls.Add(Me.lblDoing)
        Me.pnlFindData.Controls.Add(Me.PictureBox1)
        Me.pnlFindData.Controls.Add(Me.RichTextBox2)
        Me.pnlFindData.Location = New System.Drawing.Point(53, 236)
        Me.pnlFindData.Name = "pnlFindData"
        Me.pnlFindData.Size = New System.Drawing.Size(564, 288)
        Me.pnlFindData.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFindData.TabIndex = 13
        Me.pnlFindData.tbBackgroundImage = Nothing
        Me.pnlFindData.tbShowWatermark = False
        Me.pnlFindData.tbSplit = "0,0,0,0"
        Me.pnlFindData.tbWatermark = Nothing
        Me.pnlFindData.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFindData.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlFindData.Visible = False
        '
        'lblFindData
        '
        Me.lblFindData.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblFindData.BackColor = System.Drawing.Color.Transparent
        Me.lblFindData.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblFindData.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFindData.Location = New System.Drawing.Point(4, 57)
        Me.lblFindData.Name = "lblFindData"
        Me.lblFindData.Size = New System.Drawing.Size(556, 21)
        Me.lblFindData.TabIndex = 33
        Me.lblFindData.tbAdriftWhenHover = False
        Me.lblFindData.tbAutoEllipsis = False
        Me.lblFindData.tbAutoSize = False
        Me.lblFindData.tbHideImage = False
        Me.lblFindData.tbIconImage = Nothing
        Me.lblFindData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFindData.tbIconPlaceText = 5
        Me.lblFindData.tbShadow = False
        Me.lblFindData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFindData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFindData.tbShowScrolling = False
        Me.lblFindData.Text = "正在为您找回删除的聊天记录，请稍候..."
        Me.lblFindData.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblDoing
        '
        Me.lblDoing.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblDoing.BackColor = System.Drawing.Color.Transparent
        Me.lblDoing.Font = New System.Drawing.Font("宋体", 18.0!)
        Me.lblDoing.ForeColor = System.Drawing.Color.FromArgb(CType(CType(36, Byte), Integer), CType(CType(142, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblDoing.Location = New System.Drawing.Point(247, 149)
        Me.lblDoing.Name = "lblDoing"
        Me.lblDoing.Size = New System.Drawing.Size(70, 32)
        Me.lblDoing.TabIndex = 30
        Me.lblDoing.tbAdriftWhenHover = False
        Me.lblDoing.tbAutoEllipsis = False
        Me.lblDoing.tbAutoSize = False
        Me.lblDoing.tbHideImage = False
        Me.lblDoing.tbIconImage = Nothing
        Me.lblDoing.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDoing.tbIconPlaceText = 5
        Me.lblDoing.tbShadow = False
        Me.lblDoing.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDoing.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDoing.tbShowScrolling = False
        Me.lblDoing.Text = "数据找回中，请耐心等候 ..."
        Me.lblDoing.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.gif_loading_132
        Me.PictureBox1.Location = New System.Drawing.Point(216, 99)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(132, 132)
        Me.PictureBox1.TabIndex = 31
        Me.PictureBox1.TabStop = False
        '
        'RichTextBox2
        '
        Me.RichTextBox2.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.RichTextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox2.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.RichTextBox2.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.RichTextBox2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.RichTextBox2.Location = New System.Drawing.Point(43, 71)
        Me.RichTextBox2.Name = "RichTextBox2"
        Me.RichTextBox2.ReadOnly = True
        Me.RichTextBox2.Size = New System.Drawing.Size(478, 117)
        Me.RichTextBox2.TabIndex = 32
        Me.RichTextBox2.Text = ""
        Me.RichTextBox2.Visible = False
        '
        'pnlShare
        '
        Me.pnlShare.BackColor = System.Drawing.Color.White
        Me.pnlShare.Controls.Add(Me.RichTextBox1)
        Me.pnlShare.Controls.Add(Me.rtxtFindDataInfo)
        Me.pnlShare.Controls.Add(Me.rtxtFindDelCount)
        Me.pnlShare.Location = New System.Drawing.Point(17, 263)
        Me.pnlShare.Name = "pnlShare"
        Me.pnlShare.Size = New System.Drawing.Size(564, 288)
        Me.pnlShare.TabIndex = 11
        Me.pnlShare.Visible = False
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.White
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.RichTextBox1.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.RichTextBox1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(209, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RichTextBox1.Location = New System.Drawing.Point(113, 148)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.ReadOnly = True
        Me.RichTextBox1.Size = New System.Drawing.Size(448, 106)
        Me.RichTextBox1.TabIndex = 30
        Me.RichTextBox1.Text = ""
        '
        'rtxtFindDataInfo
        '
        Me.rtxtFindDataInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.rtxtFindDataInfo.BackColor = System.Drawing.Color.White
        Me.rtxtFindDataInfo.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtFindDataInfo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtFindDataInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.rtxtFindDataInfo.Location = New System.Drawing.Point(3, 115)
        Me.rtxtFindDataInfo.Multiline = False
        Me.rtxtFindDataInfo.Name = "rtxtFindDataInfo"
        Me.rtxtFindDataInfo.ReadOnly = True
        Me.rtxtFindDataInfo.ShowSelectionMargin = True
        Me.rtxtFindDataInfo.Size = New System.Drawing.Size(558, 21)
        Me.rtxtFindDataInfo.TabIndex = 12
        Me.rtxtFindDataInfo.Text = "左侧联系人昵称显示红色为有找到删除数据"
        '
        'rtxtFindDelCount
        '
        Me.rtxtFindDelCount.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.rtxtFindDelCount.BackColor = System.Drawing.Color.White
        Me.rtxtFindDelCount.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtFindDelCount.Font = New System.Drawing.Font("宋体", 16.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtFindDelCount.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.rtxtFindDelCount.Location = New System.Drawing.Point(2, 74)
        Me.rtxtFindDelCount.Multiline = False
        Me.rtxtFindDelCount.Name = "rtxtFindDelCount"
        Me.rtxtFindDelCount.ReadOnly = True
        Me.rtxtFindDelCount.ShowSelectionMargin = True
        Me.rtxtFindDelCount.Size = New System.Drawing.Size(559, 30)
        Me.rtxtFindDelCount.TabIndex = 11
        Me.rtxtFindDelCount.Text = "共找回8条删除的数据"
        '
        'pnlLoadData
        '
        Me.pnlLoadData.BackColor = System.Drawing.Color.White
        Me.pnlLoadData.Controls.Add(Me.lblLoadData)
        Me.pnlLoadData.Controls.Add(Me.PictureBox2)
        Me.pnlLoadData.Location = New System.Drawing.Point(376, 133)
        Me.pnlLoadData.Name = "pnlLoadData"
        Me.pnlLoadData.Size = New System.Drawing.Size(564, 288)
        Me.pnlLoadData.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoadData.TabIndex = 9
        Me.pnlLoadData.tbBackgroundImage = Nothing
        Me.pnlLoadData.tbShowWatermark = False
        Me.pnlLoadData.tbSplit = "0,0,0,0"
        Me.pnlLoadData.tbWatermark = Nothing
        Me.pnlLoadData.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoadData.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlLoadData.Visible = False
        '
        'lblLoadData
        '
        Me.lblLoadData.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblLoadData.BackColor = System.Drawing.Color.Transparent
        Me.lblLoadData.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.lblLoadData.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblLoadData.Location = New System.Drawing.Point(5, 158)
        Me.lblLoadData.Name = "lblLoadData"
        Me.lblLoadData.Size = New System.Drawing.Size(554, 21)
        Me.lblLoadData.TabIndex = 33
        Me.lblLoadData.tbAdriftWhenHover = False
        Me.lblLoadData.tbAutoEllipsis = False
        Me.lblLoadData.tbAutoSize = False
        Me.lblLoadData.tbHideImage = False
        Me.lblLoadData.tbIconImage = Nothing
        Me.lblLoadData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoadData.tbIconPlaceText = 5
        Me.lblLoadData.tbShadow = False
        Me.lblLoadData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLoadData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLoadData.tbShowScrolling = False
        Me.lblLoadData.Text = "数据加载中，请稍候..."
        Me.lblLoadData.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox2
        '
        Me.PictureBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.PictureBox2.Image = Global.iTong.My.Resources.Resources.gif_loading_32
        Me.PictureBox2.Location = New System.Drawing.Point(266, 108)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(32, 32)
        Me.PictureBox2.TabIndex = 31
        Me.PictureBox2.TabStop = False
        '
        'lblSystemInfo
        '
        Me.lblSystemInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSystemInfo.BackColor = System.Drawing.Color.White
        Me.lblSystemInfo.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.lblSystemInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblSystemInfo.Location = New System.Drawing.Point(128, 283)
        Me.lblSystemInfo.Name = "lblSystemInfo"
        Me.lblSystemInfo.Size = New System.Drawing.Size(542, 30)
        Me.lblSystemInfo.TabIndex = 10
        Me.lblSystemInfo.tbAdriftWhenHover = False
        Me.lblSystemInfo.tbAutoEllipsis = False
        Me.lblSystemInfo.tbAutoSize = False
        Me.lblSystemInfo.tbHideImage = False
        Me.lblSystemInfo.tbIconImage = Nothing
        Me.lblSystemInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSystemInfo.tbIconPlaceText = 5
        Me.lblSystemInfo.tbShadow = False
        Me.lblSystemInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSystemInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSystemInfo.tbShowScrolling = False
        Me.lblSystemInfo.Text = "查看消息，请选中左侧联系人"
        Me.lblSystemInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblSystemInfo.Visible = False
        '
        'wbsChat
        '
        Me.wbsChat.AllowWebBrowserDrop = False
        Me.wbsChat.Client = ""
        Me.wbsChat.DeviceList = Nothing
        Me.wbsChat.Dock = System.Windows.Forms.DockStyle.Fill
        Me.wbsChat.IsConnected = False
        Me.wbsChat.IsWebBrowserContextMenuEnabled = False
        Me.wbsChat.Jailbreaked = False
        Me.wbsChat.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.wbsChat.LoadingFont = Nothing
        Me.wbsChat.LoadingGif = CType(resources.GetObject("wbsChat.LoadingGif"), System.Drawing.Image)
        Me.wbsChat.Location = New System.Drawing.Point(0, 0)
        Me.wbsChat.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbsChat.Name = "wbsChat"
        Me.wbsChat.ScriptErrorsSuppressed = True
        Me.wbsChat.ShowLoadingWait = False
        Me.wbsChat.ShowNavigateErrorPage = True
        Me.wbsChat.ShowProgress = True
        Me.wbsChat.Size = New System.Drawing.Size(798, 597)
        Me.wbsChat.SN = ""
        Me.wbsChat.TabIndex = 2
        Me.wbsChat.UserInfo = ""
        Me.wbsChat.UserInfoEncode = ""
        Me.wbsChat.Visible = False
        '
        'pnlTimeSelect
        '
        Me.pnlTimeSelect.BackColor = System.Drawing.Color.White
        Me.pnlTimeSelect.Controls.Add(Me.TbPanel4)
        Me.pnlTimeSelect.Controls.Add(Me.btnTime)
        Me.pnlTimeSelect.Controls.Add(Me.btnOK)
        Me.pnlTimeSelect.Controls.Add(Me.dtpStart)
        Me.pnlTimeSelect.Controls.Add(Me.lblStartTime)
        Me.pnlTimeSelect.Controls.Add(Me.dtpEnd)
        Me.pnlTimeSelect.Controls.Add(Me.lblEndTime)
        Me.pnlTimeSelect.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlTimeSelect.Location = New System.Drawing.Point(0, 0)
        Me.pnlTimeSelect.Margin = New System.Windows.Forms.Padding(0, 1, 0, 1)
        Me.pnlTimeSelect.Name = "pnlTimeSelect"
        Me.pnlTimeSelect.Size = New System.Drawing.Size(798, 40)
        Me.pnlTimeSelect.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTimeSelect.TabIndex = 0
        Me.pnlTimeSelect.tbBackgroundImage = Nothing
        Me.pnlTimeSelect.tbShowWatermark = False
        Me.pnlTimeSelect.tbSplit = "3,3,3,3"
        Me.pnlTimeSelect.tbWatermark = Nothing
        Me.pnlTimeSelect.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTimeSelect.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlTimeSelect.Visible = False
        '
        'TbPanel4
        '
        Me.TbPanel4.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.TbPanel4.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.TbPanel4.Location = New System.Drawing.Point(0, 39)
        Me.TbPanel4.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel4.Name = "TbPanel4"
        Me.TbPanel4.Size = New System.Drawing.Size(798, 1)
        Me.TbPanel4.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel4.TabIndex = 106
        Me.TbPanel4.tbBackgroundImage = Nothing
        Me.TbPanel4.tbShowWatermark = False
        Me.TbPanel4.tbSplit = "0,0,0,0"
        Me.TbPanel4.tbWatermark = Nothing
        Me.TbPanel4.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel4.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnTime
        '
        Me.btnTime.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnTime.BackColor = System.Drawing.Color.White
        Me.btnTime.BindingForm = Nothing
        Me.btnTime.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnTime.Location = New System.Drawing.Point(53, 9)
        Me.btnTime.Margin = New System.Windows.Forms.Padding(0)
        Me.btnTime.Name = "btnTime"
        Me.btnTime.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnTime.Selectable = True
        Me.btnTime.Size = New System.Drawing.Size(99, 23)
        Me.btnTime.TabIndex = 75
        Me.btnTime.tbAdriftIconWhenHover = False
        Me.btnTime.tbAutoSize = False
        Me.btnTime.tbAutoSizeEx = False
        Me.btnTime.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_bg_border
        Me.btnTime.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnTime.tbBadgeNumber = 0
        Me.btnTime.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTime.tbEndEllipsis = False
        Me.btnTime.tbIconHoldPlace = True
        Me.btnTime.tbIconImage = Nothing
        Me.btnTime.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnTime.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnTime.tbIconMore = True
        Me.btnTime.tbIconMouseDown = Nothing
        Me.btnTime.tbIconMouseHover = Nothing
        Me.btnTime.tbIconMouseLeave = Nothing
        Me.btnTime.tbIconPlaceText = 2
        Me.btnTime.tbIconReadOnly = Nothing
        Me.btnTime.tbImageMouseDown = Nothing
        Me.btnTime.tbImageMouseHover = Nothing
        Me.btnTime.tbImageMouseLeave = Nothing
        Me.btnTime.tbProgressValue = 50
        Me.btnTime.tbReadOnly = False
        Me.btnTime.tbReadOnlyText = False
        Me.btnTime.tbShadow = False
        Me.btnTime.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTime.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTime.tbShowDot = False
        Me.btnTime.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnTime.tbShowNew = False
        Me.btnTime.tbShowProgress = False
        Me.btnTime.tbShowTip = True
        Me.btnTime.tbShowToolTipOnButton = False
        Me.btnTime.tbSplit = "2,3,5,3"
        Me.btnTime.tbText = ""
        Me.btnTime.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnTime.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTime.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTime.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTime.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnTime.tbTextMouseDownPlace = 2
        Me.btnTime.tbToolTip = ""
        Me.btnTime.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnTime.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTime.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTime.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(376, 10)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(41, 21)
        Me.btnOK.TabIndex = 74
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = True
        Me.btnOK.tbBackgroundImage = Nothing
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "查找"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnOK.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOK.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOK.tbTextMouseDownPlace = 2
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.VisibleEx = True
        '
        'dtpStart
        '
        Me.dtpStart.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.dtpStart.Checked = False
        Me.dtpStart.CustomFormat = "yyyy-MM-dd"
        Me.dtpStart.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.dtpStart.Location = New System.Drawing.Point(155, 10)
        Me.dtpStart.Name = "dtpStart"
        Me.dtpStart.Size = New System.Drawing.Size(100, 21)
        Me.dtpStart.TabIndex = 70
        '
        'lblStartTime
        '
        Me.lblStartTime.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblStartTime.AutoSize = True
        Me.lblStartTime.BackColor = System.Drawing.Color.Transparent
        Me.lblStartTime.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblStartTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblStartTime.Location = New System.Drawing.Point(15, 14)
        Me.lblStartTime.Name = "lblStartTime"
        Me.lblStartTime.Size = New System.Drawing.Size(35, 12)
        Me.lblStartTime.TabIndex = 71
        Me.lblStartTime.Text = "日期:"
        Me.lblStartTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'dtpEnd
        '
        Me.dtpEnd.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.dtpEnd.Checked = False
        Me.dtpEnd.CustomFormat = "yyyy-MM-dd"
        Me.dtpEnd.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.dtpEnd.Location = New System.Drawing.Point(270, 10)
        Me.dtpEnd.Name = "dtpEnd"
        Me.dtpEnd.Size = New System.Drawing.Size(100, 21)
        Me.dtpEnd.TabIndex = 72
        '
        'lblEndTime
        '
        Me.lblEndTime.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblEndTime.AutoSize = True
        Me.lblEndTime.BackColor = System.Drawing.Color.Transparent
        Me.lblEndTime.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblEndTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblEndTime.Location = New System.Drawing.Point(255, 14)
        Me.lblEndTime.Name = "lblEndTime"
        Me.lblEndTime.Size = New System.Drawing.Size(17, 12)
        Me.lblEndTime.TabIndex = 73
        Me.lblEndTime.Text = "～"
        Me.lblEndTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlBottomMessage
        '
        Me.pnlBottomMessage.BackColor = System.Drawing.Color.White
        Me.tlpQQ.SetColumnSpan(Me.pnlBottomMessage, 2)
        Me.pnlBottomMessage.Controls.Add(Me.TbPanel6)
        Me.pnlBottomMessage.Controls.Add(Me.btnExport)
        Me.pnlBottomMessage.Controls.Add(Me.llblRefresh)
        Me.pnlBottomMessage.Controls.Add(Me.lblState)
        Me.pnlBottomMessage.Controls.Add(Me.lblFromBackupMsg)
        Me.pnlBottomMessage.Controls.Add(Me.btnDataRecoveryToPhone)
        Me.pnlBottomMessage.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlBottomMessage.Location = New System.Drawing.Point(400, 677)
        Me.pnlBottomMessage.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBottomMessage.Name = "pnlBottomMessage"
        Me.pnlBottomMessage.Size = New System.Drawing.Size(1092, 80)
        Me.pnlBottomMessage.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBottomMessage.TabIndex = 0
        Me.pnlBottomMessage.tbBackgroundImage = Nothing
        Me.pnlBottomMessage.tbShowWatermark = False
        Me.pnlBottomMessage.tbSplit = "0,0,0,0"
        Me.pnlBottomMessage.tbWatermark = Nothing
        Me.pnlBottomMessage.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBottomMessage.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel6
        '
        Me.TbPanel6.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.TbPanel6.Dock = System.Windows.Forms.DockStyle.Top
        Me.TbPanel6.Location = New System.Drawing.Point(0, 0)
        Me.TbPanel6.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel6.Name = "TbPanel6"
        Me.TbPanel6.Size = New System.Drawing.Size(1092, 1)
        Me.TbPanel6.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel6.TabIndex = 106
        Me.TbPanel6.tbBackgroundImage = Nothing
        Me.TbPanel6.tbShowWatermark = False
        Me.TbPanel6.tbSplit = "0,0,0,0"
        Me.TbPanel6.tbWatermark = Nothing
        Me.TbPanel6.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel6.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnExport
        '
        Me.btnExport.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("Arial", 12.0!)
        Me.btnExport.Location = New System.Drawing.Point(943, 19)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(130, 38)
        Me.btnExport.TabIndex = 90
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = False
        Me.btnExport.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = False
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Nothing
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 3
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = Nothing
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "导出到电脑"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.White
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.White
        Me.btnExport.tbTextColorDown = System.Drawing.Color.White
        Me.btnExport.tbTextColorHover = System.Drawing.Color.White
        Me.btnExport.tbTextMouseDownPlace = 0
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("Arial", 12.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'llblRefresh
        '
        Me.llblRefresh.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.llblRefresh.AutoSize = True
        Me.llblRefresh.BackColor = System.Drawing.Color.Transparent
        Me.llblRefresh.Cursor = System.Windows.Forms.Cursors.Hand
        Me.llblRefresh.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.llblRefresh.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.llblRefresh.LinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Location = New System.Drawing.Point(78, 19)
        Me.llblRefresh.Name = "llblRefresh"
        Me.llblRefresh.Size = New System.Drawing.Size(65, 12)
        Me.llblRefresh.TabIndex = 88
        Me.llblRefresh.TabStop = True
        Me.llblRefresh.Text = "获取最新>>"
        Me.llblRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.llblRefresh.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(19, 50)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(647, 18)
        Me.lblState.TabIndex = 30
        Me.lblState.tbAdriftWhenHover = False
        Me.lblState.tbAutoEllipsis = False
        Me.lblState.tbAutoSize = False
        Me.lblState.tbHideImage = False
        Me.lblState.tbIconImage = Nothing
        Me.lblState.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblState.tbIconPlaceText = 5
        Me.lblState.tbShadow = False
        Me.lblState.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblState.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblState.tbShowScrolling = False
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblState.Visible = False
        '
        'lblFromBackupMsg
        '
        Me.lblFromBackupMsg.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblFromBackupMsg.AutoSize = True
        Me.lblFromBackupMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblFromBackupMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFromBackupMsg.Location = New System.Drawing.Point(19, 19)
        Me.lblFromBackupMsg.Name = "lblFromBackupMsg"
        Me.lblFromBackupMsg.Size = New System.Drawing.Size(53, 12)
        Me.lblFromBackupMsg.TabIndex = 87
        Me.lblFromBackupMsg.tbAdriftWhenHover = False
        Me.lblFromBackupMsg.tbAutoEllipsis = False
        Me.lblFromBackupMsg.tbAutoSize = True
        Me.lblFromBackupMsg.tbHideImage = False
        Me.lblFromBackupMsg.tbIconImage = Nothing
        Me.lblFromBackupMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFromBackupMsg.tbIconPlaceText = 5
        Me.lblFromBackupMsg.tbShadow = False
        Me.lblFromBackupMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFromBackupMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFromBackupMsg.tbShowScrolling = False
        Me.lblFromBackupMsg.Text = "备份信息"
        '
        'btnDataRecoveryToPhone
        '
        Me.btnDataRecoveryToPhone.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDataRecoveryToPhone.BackColor = System.Drawing.Color.Transparent
        Me.btnDataRecoveryToPhone.BindingForm = Nothing
        Me.btnDataRecoveryToPhone.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnDataRecoveryToPhone.Location = New System.Drawing.Point(763, 19)
        Me.btnDataRecoveryToPhone.Name = "btnDataRecoveryToPhone"
        Me.btnDataRecoveryToPhone.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnDataRecoveryToPhone.Selectable = True
        Me.btnDataRecoveryToPhone.Size = New System.Drawing.Size(150, 38)
        Me.btnDataRecoveryToPhone.TabIndex = 85
        Me.btnDataRecoveryToPhone.tbAdriftIconWhenHover = False
        Me.btnDataRecoveryToPhone.tbAutoSize = False
        Me.btnDataRecoveryToPhone.tbAutoSizeEx = False
        Me.btnDataRecoveryToPhone.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnDataRecoveryToPhone.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDataRecoveryToPhone.tbBadgeNumber = 0
        Me.btnDataRecoveryToPhone.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDataRecoveryToPhone.tbEndEllipsis = False
        Me.btnDataRecoveryToPhone.tbIconHoldPlace = True
        Me.btnDataRecoveryToPhone.tbIconImage = Nothing
        Me.btnDataRecoveryToPhone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryToPhone.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDataRecoveryToPhone.tbIconMore = False
        Me.btnDataRecoveryToPhone.tbIconMouseDown = Nothing
        Me.btnDataRecoveryToPhone.tbIconMouseHover = Nothing
        Me.btnDataRecoveryToPhone.tbIconMouseLeave = Nothing
        Me.btnDataRecoveryToPhone.tbIconPlaceText = 2
        Me.btnDataRecoveryToPhone.tbIconReadOnly = Nothing
        Me.btnDataRecoveryToPhone.tbImageMouseDown = Nothing
        Me.btnDataRecoveryToPhone.tbImageMouseHover = Nothing
        Me.btnDataRecoveryToPhone.tbImageMouseLeave = Nothing
        Me.btnDataRecoveryToPhone.tbProgressValue = 50
        Me.btnDataRecoveryToPhone.tbReadOnly = False
        Me.btnDataRecoveryToPhone.tbReadOnlyText = False
        Me.btnDataRecoveryToPhone.tbShadow = False
        Me.btnDataRecoveryToPhone.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDataRecoveryToPhone.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDataRecoveryToPhone.tbShowDot = False
        Me.btnDataRecoveryToPhone.tbShowMoreIconImg = Nothing
        Me.btnDataRecoveryToPhone.tbShowNew = False
        Me.btnDataRecoveryToPhone.tbShowProgress = False
        Me.btnDataRecoveryToPhone.tbShowTip = True
        Me.btnDataRecoveryToPhone.tbShowToolTipOnButton = False
        Me.btnDataRecoveryToPhone.tbSplit = "13,11,13,11"
        Me.btnDataRecoveryToPhone.tbText = "恢复到手机"
        Me.btnDataRecoveryToPhone.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryToPhone.tbTextColor = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextColorDown = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextColorHover = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextMouseDownPlace = 0
        Me.btnDataRecoveryToPhone.tbToolTip = ""
        Me.btnDataRecoveryToPhone.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDataRecoveryToPhone.tbToolTipFont = New System.Drawing.Font("宋体", 12.0!)
        Me.btnDataRecoveryToPhone.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryToPhone.Visible = False
        Me.btnDataRecoveryToPhone.VisibleEx = True
        '
        'pnlLeft
        '
        Me.pnlLeft.BackColor = System.Drawing.Color.White
        Me.pnlLeft.Controls.Add(Me.TbPanel9)
        Me.pnlLeft.Controls.Add(Me.TbPanel7)
        Me.pnlLeft.Controls.Add(Me.TbPanel1)
        Me.pnlLeft.Controls.Add(Me.pnlFriend)
        Me.pnlLeft.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlLeft.Location = New System.Drawing.Point(400, 40)
        Me.pnlLeft.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLeft.Name = "pnlLeft"
        Me.tlpQQ.SetRowSpan(Me.pnlLeft, 2)
        Me.pnlLeft.Size = New System.Drawing.Size(294, 637)
        Me.pnlLeft.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLeft.TabIndex = 4
        Me.pnlLeft.tbBackgroundImage = Nothing
        Me.pnlLeft.tbShowWatermark = False
        Me.pnlLeft.tbSplit = "0,0,0,0"
        Me.pnlLeft.tbWatermark = Nothing
        Me.pnlLeft.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLeft.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel9
        '
        Me.TbPanel9.Controls.Add(Me.lblLinkmanMsg)
        Me.TbPanel9.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.TbPanel9.Location = New System.Drawing.Point(0, 604)
        Me.TbPanel9.Name = "TbPanel9"
        Me.TbPanel9.Size = New System.Drawing.Size(293, 33)
        Me.TbPanel9.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel9.TabIndex = 18
        Me.TbPanel9.tbBackgroundImage = Nothing
        Me.TbPanel9.tbShowWatermark = False
        Me.TbPanel9.tbSplit = "0,0,0,0"
        Me.TbPanel9.tbWatermark = Nothing
        Me.TbPanel9.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel9.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLinkmanMsg
        '
        Me.lblLinkmanMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblLinkmanMsg.Location = New System.Drawing.Point(4, 7)
        Me.lblLinkmanMsg.Name = "lblLinkmanMsg"
        Me.lblLinkmanMsg.Size = New System.Drawing.Size(268, 19)
        Me.lblLinkmanMsg.TabIndex = 35
        Me.lblLinkmanMsg.tbAdriftWhenHover = False
        Me.lblLinkmanMsg.tbAutoEllipsis = False
        Me.lblLinkmanMsg.tbAutoSize = False
        Me.lblLinkmanMsg.tbHideImage = False
        Me.lblLinkmanMsg.tbIconImage = Nothing
        Me.lblLinkmanMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLinkmanMsg.tbIconPlaceText = 5
        Me.lblLinkmanMsg.tbShadow = False
        Me.lblLinkmanMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLinkmanMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLinkmanMsg.tbShowScrolling = False
        Me.lblLinkmanMsg.Text = "已选择 123 项，全部 123 项"
        Me.lblLinkmanMsg.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TbPanel7
        '
        Me.TbPanel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.TbPanel7.Dock = System.Windows.Forms.DockStyle.Right
        Me.TbPanel7.Location = New System.Drawing.Point(293, 40)
        Me.TbPanel7.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel7.Name = "TbPanel7"
        Me.TbPanel7.Size = New System.Drawing.Size(1, 597)
        Me.TbPanel7.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel7.TabIndex = 106
        Me.TbPanel7.tbBackgroundImage = Nothing
        Me.TbPanel7.tbShowWatermark = False
        Me.TbPanel7.tbSplit = "0,0,0,0"
        Me.TbPanel7.tbWatermark = Nothing
        Me.TbPanel7.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel7.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel1
        '
        Me.TbPanel1.BackColor = System.Drawing.Color.White
        Me.TbPanel1.Controls.Add(Me.TbPanel8)
        Me.TbPanel1.Controls.Add(Me.TbPanel5)
        Me.TbPanel1.Controls.Add(Me.cbxSelectAll)
        Me.TbPanel1.Controls.Add(Me.btnFilter)
        Me.TbPanel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.TbPanel1.Location = New System.Drawing.Point(0, 0)
        Me.TbPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel1.Name = "TbPanel1"
        Me.TbPanel1.Size = New System.Drawing.Size(294, 40)
        Me.TbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel1.TabIndex = 2
        Me.TbPanel1.tbBackgroundImage = Nothing
        Me.TbPanel1.tbShowWatermark = False
        Me.TbPanel1.tbSplit = "3,3,3,3"
        Me.TbPanel1.tbWatermark = Nothing
        Me.TbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel8
        '
        Me.TbPanel8.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.TbPanel8.Dock = System.Windows.Forms.DockStyle.Right
        Me.TbPanel8.Location = New System.Drawing.Point(293, 0)
        Me.TbPanel8.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel8.Name = "TbPanel8"
        Me.TbPanel8.Size = New System.Drawing.Size(1, 39)
        Me.TbPanel8.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel8.TabIndex = 107
        Me.TbPanel8.tbBackgroundImage = Nothing
        Me.TbPanel8.tbShowWatermark = False
        Me.TbPanel8.tbSplit = "0,0,0,0"
        Me.TbPanel8.tbWatermark = Nothing
        Me.TbPanel8.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel8.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel5
        '
        Me.TbPanel5.BackColor = System.Drawing.Color.FromArgb(CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.TbPanel5.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.TbPanel5.Location = New System.Drawing.Point(0, 39)
        Me.TbPanel5.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel5.Name = "TbPanel5"
        Me.TbPanel5.Size = New System.Drawing.Size(294, 1)
        Me.TbPanel5.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel5.TabIndex = 106
        Me.TbPanel5.tbBackgroundImage = Nothing
        Me.TbPanel5.tbShowWatermark = False
        Me.TbPanel5.tbSplit = "0,0,0,0"
        Me.TbPanel5.tbWatermark = Nothing
        Me.TbPanel5.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel5.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'cbxSelectAll
        '
        Me.cbxSelectAll.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.cbxSelectAll.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxSelectAll.AutoSize = True
        Me.cbxSelectAll.BackColor = System.Drawing.Color.Transparent
        Me.cbxSelectAll.Font = New System.Drawing.Font("Arial", 9.0!)
        Me.cbxSelectAll.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cbxSelectAll.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cbxSelectAll.Location = New System.Drawing.Point(2, 9)
        Me.cbxSelectAll.Name = "cbxSelectAll"
        Me.cbxSelectAll.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxSelectAll.Size = New System.Drawing.Size(99, 23)
        Me.cbxSelectAll.TabIndex = 90
        Me.cbxSelectAll.tbAdriftIconWhenHover = False
        Me.cbxSelectAll.tbAutoSize = True
        Me.cbxSelectAll.tbAutoSizeEx = False
        Me.cbxSelectAll.tbIconChecked = CType(resources.GetObject("cbxSelectAll.tbIconChecked"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbIconCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbIconCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbIconHoldPlace = True
        Me.cbxSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxSelectAll.tbIconIndeterminate = CType(resources.GetObject("cbxSelectAll.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconIndeterminateMouseDown = Nothing
        Me.cbxSelectAll.tbIconIndeterminateMouseHover = Nothing
        Me.cbxSelectAll.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxSelectAll.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbIconPlaceText = 1
        Me.cbxSelectAll.tbIconUnChecked = CType(resources.GetObject("cbxSelectAll.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconUnCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbIconUnCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbImageBackground = Nothing
        Me.cbxSelectAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxSelectAll.tbImageCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbImageCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbImageCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbReadOnly = False
        Me.cbxSelectAll.tbShadow = False
        Me.cbxSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxSelectAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxSelectAll.tbSplit = "3,3,3,3"
        Me.cbxSelectAll.tbToolTip = ""
        Me.cbxSelectAll.Text = "全选"
        Me.cbxSelectAll.UseVisualStyleBackColor = False
        '
        'btnFilter
        '
        Me.btnFilter.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnFilter.BackColor = System.Drawing.Color.Transparent
        Me.btnFilter.BindingForm = Nothing
        Me.btnFilter.Font = New System.Drawing.Font("Arial", 9.0!)
        Me.btnFilter.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.btnFilter.Location = New System.Drawing.Point(146, 9)
        Me.btnFilter.Name = "btnFilter"
        Me.btnFilter.Padding = New System.Windows.Forms.Padding(15, 3, 15, 3)
        Me.btnFilter.Selectable = True
        Me.btnFilter.Size = New System.Drawing.Size(118, 23)
        Me.btnFilter.TabIndex = 89
        Me.btnFilter.tbAdriftIconWhenHover = False
        Me.btnFilter.tbAutoSize = False
        Me.btnFilter.tbAutoSizeEx = False
        Me.btnFilter.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnFilter.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFilter.tbBadgeNumber = 0
        Me.btnFilter.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFilter.tbEndEllipsis = False
        Me.btnFilter.tbIconHoldPlace = True
        Me.btnFilter.tbIconImage = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnFilter.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnFilter.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFilter.tbIconMore = False
        Me.btnFilter.tbIconMouseDown = Nothing
        Me.btnFilter.tbIconMouseHover = Nothing
        Me.btnFilter.tbIconMouseLeave = Nothing
        Me.btnFilter.tbIconPlaceText = 5
        Me.btnFilter.tbIconReadOnly = Nothing
        Me.btnFilter.tbImageMouseDown = Nothing
        Me.btnFilter.tbImageMouseHover = Nothing
        Me.btnFilter.tbImageMouseLeave = Nothing
        Me.btnFilter.tbProgressValue = 50
        Me.btnFilter.tbReadOnly = False
        Me.btnFilter.tbReadOnlyText = False
        Me.btnFilter.tbShadow = False
        Me.btnFilter.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFilter.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFilter.tbShowDot = False
        Me.btnFilter.tbShowMoreIconImg = Nothing
        Me.btnFilter.tbShowNew = False
        Me.btnFilter.tbShowProgress = False
        Me.btnFilter.tbShowTip = True
        Me.btnFilter.tbShowToolTipOnButton = False
        Me.btnFilter.tbSplit = "13,11,13,11"
        Me.btnFilter.tbText = "显示所有"
        Me.btnFilter.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFilter.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextMouseDownPlace = 0
        Me.btnFilter.tbToolTip = ""
        Me.btnFilter.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFilter.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnFilter.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFilter.Visible = False
        Me.btnFilter.VisibleEx = True
        '
        'pnlFriend
        '
        Me.pnlFriend.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlFriend.BackColor = System.Drawing.Color.White
        Me.pnlFriend.Location = New System.Drawing.Point(0, 40)
        Me.pnlFriend.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFriend.Name = "pnlFriend"
        Me.pnlFriend.Size = New System.Drawing.Size(293, 582)
        Me.pnlFriend.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFriend.TabIndex = 1
        Me.pnlFriend.tbBackgroundImage = Nothing
        Me.pnlFriend.tbShowWatermark = False
        Me.pnlFriend.tbSplit = "0,0,0,0"
        Me.pnlFriend.tbWatermark = Nothing
        Me.pnlFriend.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFriend.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel2
        '
        Me.TbPanel2.BackColor = System.Drawing.Color.White
        Me.tlpQQ.SetColumnSpan(Me.TbPanel2, 2)
        Me.TbPanel2.Controls.Add(Me.btnBackLoading)
        Me.TbPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TbPanel2.Location = New System.Drawing.Point(0, 0)
        Me.TbPanel2.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel2.Name = "TbPanel2"
        Me.TbPanel2.Size = New System.Drawing.Size(400, 40)
        Me.TbPanel2.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel2.TabIndex = 5
        Me.TbPanel2.tbBackgroundImage = Nothing
        Me.TbPanel2.tbShowWatermark = False
        Me.TbPanel2.tbSplit = "0,0,0,0"
        Me.TbPanel2.tbWatermark = Nothing
        Me.TbPanel2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel2.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnBackLoading
        '
        Me.btnBackLoading.BackColor = System.Drawing.Color.Transparent
        Me.btnBackLoading.BindingForm = Nothing
        Me.btnBackLoading.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBackLoading.Location = New System.Drawing.Point(10, 14)
        Me.btnBackLoading.Name = "btnBackLoading"
        Me.btnBackLoading.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBackLoading.Selectable = True
        Me.btnBackLoading.Size = New System.Drawing.Size(16, 14)
        Me.btnBackLoading.TabIndex = 81
        Me.btnBackLoading.tbAdriftIconWhenHover = False
        Me.btnBackLoading.tbAutoSize = False
        Me.btnBackLoading.tbAutoSizeEx = True
        Me.btnBackLoading.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_retreat
        Me.btnBackLoading.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBackLoading.tbBadgeNumber = 0
        Me.btnBackLoading.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackLoading.tbEndEllipsis = False
        Me.btnBackLoading.tbIconHoldPlace = True
        Me.btnBackLoading.tbIconImage = Nothing
        Me.btnBackLoading.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackLoading.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnBackLoading.tbIconMore = False
        Me.btnBackLoading.tbIconMouseDown = Nothing
        Me.btnBackLoading.tbIconMouseHover = Nothing
        Me.btnBackLoading.tbIconMouseLeave = Nothing
        Me.btnBackLoading.tbIconPlaceText = 2
        Me.btnBackLoading.tbIconReadOnly = Nothing
        Me.btnBackLoading.tbImageMouseDown = Nothing
        Me.btnBackLoading.tbImageMouseHover = Nothing
        Me.btnBackLoading.tbImageMouseLeave = Nothing
        Me.btnBackLoading.tbProgressValue = 50
        Me.btnBackLoading.tbReadOnly = False
        Me.btnBackLoading.tbReadOnlyText = False
        Me.btnBackLoading.tbShadow = False
        Me.btnBackLoading.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackLoading.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackLoading.tbShowDot = False
        Me.btnBackLoading.tbShowMoreIconImg = CType(resources.GetObject("btnBackLoading.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBackLoading.tbShowNew = False
        Me.btnBackLoading.tbShowProgress = False
        Me.btnBackLoading.tbShowTip = True
        Me.btnBackLoading.tbShowToolTipOnButton = False
        Me.btnBackLoading.tbSplit = "12,1,1,1"
        Me.btnBackLoading.tbText = ""
        Me.btnBackLoading.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackLoading.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnBackLoading.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnBackLoading.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackLoading.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackLoading.tbTextMouseDownPlace = 2
        Me.btnBackLoading.tbToolTip = ""
        Me.btnBackLoading.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackLoading.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnBackLoading.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackLoading.VisibleEx = True
        '
        'bgwLoadFriend
        '
        Me.bgwLoadFriend.WorkerSupportsCancellation = True
        '
        'cmsExportNews
        '
        Me.cmsExportNews.AccessibleDescription = "153x26"
        Me.cmsExportNews.DropShadowEnabled = False
        Me.cmsExportNews.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsExportNews.Name = "munOperate"
        Me.cmsExportNews.Size = New System.Drawing.Size(61, 4)
        Me.cmsExportNews.tbBackColor = System.Drawing.Color.White
        Me.cmsExportNews.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExportNews.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExportNews.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'cmsShowSession
        '
        Me.cmsShowSession.AccessibleDescription = "161x92"
        Me.cmsShowSession.DropShadowEnabled = False
        Me.cmsShowSession.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsShowSession.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiShowAll, Me.tsmiShowDelete, Me.tsmiShowNotDelete})
        Me.cmsShowSession.Name = "munOperate"
        Me.cmsShowSession.Size = New System.Drawing.Size(161, 70)
        Me.cmsShowSession.tbBackColor = System.Drawing.Color.White
        Me.cmsShowSession.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsShowSession.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsShowSession.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiShowAll
        '
        Me.tsmiShowAll.Name = "tsmiShowAll"
        Me.tsmiShowAll.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowAll.Text = "显示所有"
        '
        'tsmiShowDelete
        '
        Me.tsmiShowDelete.Name = "tsmiShowDelete"
        Me.tsmiShowDelete.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowDelete.Text = "只显示删除文件"
        '
        'tsmiShowNotDelete
        '
        Me.tsmiShowNotDelete.Name = "tsmiShowNotDelete"
        Me.tsmiShowNotDelete.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowNotDelete.Text = "显示已存在文件"
        '
        'cmsExportEx
        '
        Me.cmsExportEx.AccessibleDescription = "137x76"
        Me.cmsExportEx.DropShadowEnabled = False
        Me.cmsExportEx.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsExportEx.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiFormat, Me.tsmiMedia, Me.ToolStripSeparator2, Me.tsmiAll})
        Me.cmsExportEx.Name = "munOperate"
        Me.cmsExportEx.Size = New System.Drawing.Size(137, 76)
        Me.cmsExportEx.tbBackColor = System.Drawing.Color.White
        Me.cmsExportEx.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExportEx.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExportEx.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiFormat
        '
        Me.tsmiFormat.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExcelEx, Me.tsmiTxtEx, Me.tsmiHtml})
        Me.tsmiFormat.Name = "tsmiFormat"
        Me.tsmiFormat.Size = New System.Drawing.Size(136, 22)
        Me.tsmiFormat.Text = "按格式导出"
        '
        'tsmiExcelEx
        '
        Me.tsmiExcelEx.Name = "tsmiExcelEx"
        Me.tsmiExcelEx.Size = New System.Drawing.Size(165, 22)
        Me.tsmiExcelEx.Text = "导出为Excel文件"
        '
        'tsmiTxtEx
        '
        Me.tsmiTxtEx.Name = "tsmiTxtEx"
        Me.tsmiTxtEx.Size = New System.Drawing.Size(165, 22)
        Me.tsmiTxtEx.Text = "导出为Txt文件"
        '
        'tsmiHtml
        '
        Me.tsmiHtml.Name = "tsmiHtml"
        Me.tsmiHtml.Size = New System.Drawing.Size(165, 22)
        Me.tsmiHtml.Text = "导出为Html文件"
        '
        'tsmiMedia
        '
        Me.tsmiMedia.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiPicEx, Me.tsmiAudioEx, Me.tsmiVideoEx})
        Me.tsmiMedia.Name = "tsmiMedia"
        Me.tsmiMedia.Size = New System.Drawing.Size(136, 22)
        Me.tsmiMedia.Text = "按媒体导出"
        '
        'tsmiPicEx
        '
        Me.tsmiPicEx.Name = "tsmiPicEx"
        Me.tsmiPicEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiPicEx.Text = "导出图片"
        '
        'tsmiAudioEx
        '
        Me.tsmiAudioEx.Name = "tsmiAudioEx"
        Me.tsmiAudioEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiAudioEx.Text = "导出语音"
        '
        'tsmiVideoEx
        '
        Me.tsmiVideoEx.Name = "tsmiVideoEx"
        Me.tsmiVideoEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiVideoEx.Text = "导出小视频"
        '
        'ToolStripSeparator2
        '
        Me.ToolStripSeparator2.Name = "ToolStripSeparator2"
        Me.ToolStripSeparator2.Size = New System.Drawing.Size(133, 6)
        '
        'tsmiAll
        '
        Me.tsmiAll.Name = "tsmiAll"
        Me.tsmiAll.Size = New System.Drawing.Size(136, 22)
        Me.tsmiAll.Text = "导出全部"
        '
        'lblReadError
        '
        Me.lblReadError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblReadError.BackColor = System.Drawing.Color.Transparent
        Me.lblReadError.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblReadError.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.lblReadError.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblReadError.Location = New System.Drawing.Point(-178, 362)
        Me.lblReadError.Name = "lblReadError"
        Me.lblReadError.Size = New System.Drawing.Size(556, 16)
        Me.lblReadError.TabIndex = 98
        Me.lblReadError.Text = "读取失败,请重试或尝试其他恢复模式"
        Me.lblReadError.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblRegister
        '
        Me.lblRegister.BackColor = System.Drawing.Color.Transparent
        Me.lblRegister.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblRegister.Font = New System.Drawing.Font("Arial", 9.0!)
        Me.lblRegister.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblRegister.Location = New System.Drawing.Point(499, 10)
        Me.lblRegister.Name = "lblRegister"
        Me.lblRegister.Size = New System.Drawing.Size(356, 19)
        Me.lblRegister.TabIndex = 102
        Me.lblRegister.Text = "试用版预览部分内容用""*""代替 立即购买"
        Me.lblRegister.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmQQ
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(1495, 790)
        Me.Controls.Add(Me.tlpQQ)
        Me.Name = "frmQQ"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,55"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "QQ"
        Me.Controls.SetChildIndex(Me.tlpQQ, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.tlpQQ.ResumeLayout(False)
        Me.pnlLoad.ResumeLayout(False)
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlError.ResumeLayout(False)
        CType(Me.picError, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTop.ResumeLayout(False)
        CType(Me.picTimeScreen, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlContent.ResumeLayout(False)
        Me.pnlWeb.ResumeLayout(False)
        Me.pnlCount.ResumeLayout(False)
        Me.pnlFindData.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlShare.ResumeLayout(False)
        Me.pnlLoadData.ResumeLayout(False)
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTimeSelect.ResumeLayout(False)
        Me.pnlTimeSelect.PerformLayout()
        Me.pnlBottomMessage.ResumeLayout(False)
        Me.pnlBottomMessage.PerformLayout()
        Me.pnlLeft.ResumeLayout(False)
        Me.TbPanel9.ResumeLayout(False)
        Me.TbPanel1.ResumeLayout(False)
        Me.TbPanel2.ResumeLayout(False)
        Me.cmsShowSession.ResumeLayout(False)
        Me.cmsExportEx.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents tlpQQ As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlLoad As iTong.Components.tbPanel
    Friend WithEvents pnlError As iTong.Components.tbPanel
    Friend WithEvents pnlTop As iTong.Components.tbPanel
    Friend WithEvents pnlTimeSelect As iTong.Components.tbPanel
    Friend WithEvents pnlContent As iTong.Components.tbPanel
    Friend WithEvents pnlBottomMessage As iTong.Components.tbPanel
    Friend WithEvents pnlFriend As iTong.Components.tbPanel
    Friend WithEvents bgwLoadFriend As System.ComponentModel.BackgroundWorker
    Friend WithEvents btnWeCahtUsers As iTong.Components.tbButton
    Friend WithEvents wbsChat As iTong.Components.tbWebBrowserEx
    Friend WithEvents btnTime As iTong.Components.tbButton
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents dtpStart As System.Windows.Forms.DateTimePicker
    Friend WithEvents txtSearchChat As iTong.Components.tbSearch
    Friend WithEvents lblStartTime As System.Windows.Forms.Label
    Friend WithEvents dtpEnd As System.Windows.Forms.DateTimePicker
    Friend WithEvents lblEndTime As System.Windows.Forms.Label
    Friend WithEvents lblState As iTong.Components.tbLabel
    Friend WithEvents cmsExportNews As iTong.Components.tbContextMenuStrip
    Friend WithEvents pnlLeft As iTong.Components.tbPanel
    Friend WithEvents TbPanel1 As iTong.Components.tbPanel
    Friend WithEvents btnFilter As iTong.Components.tbButton
    Friend WithEvents cbxSelectAll As iTong.Components.tbCheckBox
    Friend WithEvents btnDataRecoveryToPhone As iTong.Components.tbButton
    Public WithEvents llblRefresh As System.Windows.Forms.LinkLabel
    Friend WithEvents lblFromBackupMsg As iTong.Components.tbLabel
    Friend WithEvents btnTimeScreen As iTong.Components.tbButton
    Friend WithEvents picTimeScreen As System.Windows.Forms.PictureBox
    Friend WithEvents btnExportSetting As iTong.Components.tbButton
    Friend WithEvents pnlWeb As iTong.Components.tbPanel
    Friend WithEvents lblRegister As iTong.tbBattery
    Friend WithEvents btnExport As iTong.Components.tbButton
    Friend WithEvents btnBack As iTong.Components.tbButton
    Friend WithEvents lblReadError As iTong.tbBattery
    Friend WithEvents lblError As System.Windows.Forms.Label
    Friend WithEvents picError As System.Windows.Forms.PictureBox
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents cmsShowSession As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiShowAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShowDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShowNotDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents TbPanel2 As iTong.Components.tbPanel
    Friend WithEvents btnBackLoading As iTong.Components.tbButton
    Friend WithEvents TbPanel3 As iTong.Components.tbPanel
    Friend WithEvents TbPanel4 As iTong.Components.tbPanel
    Friend WithEvents TbPanel6 As iTong.Components.tbPanel
    Friend WithEvents TbPanel7 As iTong.Components.tbPanel
    Friend WithEvents TbPanel5 As iTong.Components.tbPanel
    Friend WithEvents TbPanel8 As iTong.Components.tbPanel
    Friend WithEvents TbPanel9 As iTong.Components.tbPanel
    Friend WithEvents lblLinkmanMsg As iTong.Components.tbLabel
    Friend WithEvents cmsExportEx As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiFormat As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExcelEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTxtEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiHtml As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiMedia As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiPicEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAudioEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiVideoEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripSeparator2 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlLoadData As iTong.Components.tbPanel
    Friend WithEvents lblLoadData As iTong.Components.tbLabel
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents lblSystemInfo As iTong.Components.tbLabel
    Friend WithEvents btnFinalData As iTong.Components.tbButton
    Friend WithEvents pnlFindData As iTong.Components.tbPanel
    Friend WithEvents lblFindData As iTong.Components.tbLabel
    Friend WithEvents lblDoing As iTong.Components.tbLabel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents RichTextBox2 As System.Windows.Forms.RichTextBox
    Friend WithEvents pnlShare As System.Windows.Forms.Panel
    Friend WithEvents RichTextBox1 As System.Windows.Forms.RichTextBox
    Friend WithEvents rtxtFindDataInfo As System.Windows.Forms.RichTextBox
    Friend WithEvents rtxtFindDelCount As System.Windows.Forms.RichTextBox
    Friend WithEvents pnlCount As System.Windows.Forms.Panel
    Friend WithEvents rtxtPrompt As System.Windows.Forms.RichTextBox
End Class
