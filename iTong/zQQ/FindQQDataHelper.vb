﻿Imports System.Runtime.InteropServices
Public Class FindQQDataHelper
    Implements IDisposable
    Private mLanguage As LanguageInterface = Nothing
    Private Shared mStrWCRHelperPath As String = Path.Combine(Folder.AppFolder, "tbWCRHelper.exe")
    Private Const LOWVERSION As String = "1.0.4.2"  '找回模块最小的dll，可以防止刷机dll不符合导致的问题
    Private mIsCheckVersion As Boolean = True

    Public Event QQRProgressEventHandler(ByVal sender As Object, ByVal args As FindQQDataEventArgs)

    Public mDictDelChatInfo As New Dictionary(Of String, List(Of QQChatInfo))          '被删除的聊天信息
    Public mDictDelFriendInfo As Dictionary(Of String, QQFriendInfo) = New Dictionary(Of String, QQFriendInfo) '被删除的聊天信息 对应的联系人
    Public mDictFriendInfo As Dictionary(Of String, QQFriendInfo)                     '所有联系人表（UserName,FriendInfo）

    Const WM_COPYDATA As Integer = &H4A
    Const SC_RESTORE As Int32 = &HF122

    Friend WithEvents mFindDataTimer As System.Timers.Timer = New System.Timers.Timer()


#Region "IDisposable Support"
    Private disposedValue As Boolean ' 检测冗余的调用

    ' IDisposable
    Protected Overridable Sub Dispose(disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO:  释放托管状态(托管对象)。
            End If

            ' TODO:  释放非托管资源(非托管对象)并重写下面的 Finalize()。
            ' TODO:  将大型字段设置为 null。
        End If
        Me.disposedValue = True
    End Sub

    ' TODO:  仅当上面的 Dispose(ByVal disposing As Boolean)具有释放非托管资源的代码时重写 Finalize()。
    'Protected Overrides Sub Finalize()
    '    ' 不要更改此代码。    请将清理代码放入上面的 Dispose(ByVal disposing As Boolean)中。
    '    Dispose(False)
    '    MyBase.Finalize()
    'End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。
    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。    请将清理代码放入上面的 Dispose (disposing As Boolean)中。
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub
#End Region

#Region "--- 单例 ---"

    Private Shared _lockGetInstance As New Object

    Public Sub New(ByVal strDBMMOnPC As String)
        Me.mLanguage = LanguageInterface.Instance()
    End Sub

    Private Shared mHelper As FindQQDataHelper = Nothing

    Public Shared Function GetInstance(ByVal strDBMMOnPC As String) As FindQQDataHelper
        If mHelper Is Nothing Then
            mHelper = New FindQQDataHelper(strDBMMOnPC)
        End If
        Return mHelper
    End Function


#End Region

    Public Function QQRFindData(ByVal strPath As String, ByVal strMD5 As String, ByVal dictFriendInfo As Dictionary(Of String, QQFriendInfo)) As Boolean
        Dim blnResule As Boolean = False
        Me.mDictFriendInfo = dictFriendInfo
        Me.mDictDelChatInfo = New Dictionary(Of String, List(Of QQChatInfo))
        Me.mDictDelFriendInfo = New Dictionary(Of String, QQFriendInfo)
        Common.KillProcess("tbWCRHelper")
        Try
            If CheckWCRHelperDll() Then
                Try
                    mFindDataTimer.Interval = 5000
                    mFindDataTimer.Start()
                Catch
                End Try

                Dim strSend As String = String.Format("qqRcovery:{0}", strPath)
                Me.DoSendMessage(strSend)
                blnResule = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WCRFindData")
        End Try


        Return blnResule
    End Function

    Private Function CheckWCRHelperDll() As Boolean
        Dim blnResult As Boolean = True
        Dim arg As FindQQDataEventArgs = New FindQQDataEventArgs(WCRType.Exception, 501, Me.mLanguage.GetString("WeChat.Message.LoseProgram")) '"数据找回程序丢失，请重装同步助手！"
        If Not File.Exists(mStrWCRHelperPath) Then
            blnResult = False
            GoTo DoExit
        End If

        Try
            '校验刷机tblibidevicerestore.dll是否存在以及他的版本号是否正确
            If Not mIsCheckVersion Then
                mIsCheckVersion = True

                Dim strFiletblibidevicerestore = Path.Combine(Folder.AppFolder, "WeChat\WeChatHelper.dll")
                If Not File.Exists(strFiletblibidevicerestore) Then
                    blnResult = False
                    GoTo DoExit
                End If

                Dim strVersion As String = FileVersionInfo.GetVersionInfo(strFiletblibidevicerestore).FileVersion
                Dim ver As New Version(strVersion)
                Dim verLow As New Version(LOWVERSION)
                If ver < verLow Then
                    blnResult = False
                    GoTo DoExit
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckTBFlashHerper")
        End Try

DoExit:
        If Not blnResult Then
            WCRProgress(arg)
        End If
        Return blnResult
    End Function

    Private Sub WCRProgress(arg As FindQQDataEventArgs)
        Try
            If arg.WType = WCRType.Abnormal OrElse arg.WType = WCRType.ErrorM OrElse arg.WType = WCRType.Exception OrElse arg.IntProgress >= 100 Then
                mFindDataTimer.Stop()
            End If

            RaiseEvent QQRProgressEventHandler(Me, arg)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RestoreProgress")
        End Try
    End Sub

    Private Sub Timer_Tick(ByVal sender As System.Object, ByVal e As System.Timers.ElapsedEventArgs) Handles mFindDataTimer.Elapsed
        Try
            If Not Common.ProcessExist("tbWCRHelper") Then
                mFindDataTimer.Stop()
                Dim args As FindQQDataEventArgs = New FindQQDataEventArgs(WCRType.Abnormal, 0, "")
                Me.WCRProgress(args)
            End If
        Catch ex As Exception

        End Try
    End Sub

#Region "--- 发送消息 ---"

    Private Sub DoSendMessage(strSend As String)
        Dim iHandle As Integer = GetIHandle()
        Try
            Dim isOpen As Boolean = False
            If iHandle = 0 Then
                If File.Exists(mStrWCRHelperPath) Then
                    Dim pro As New System.Diagnostics.Process
                    pro.StartInfo.FileName = mStrWCRHelperPath
                    pro.StartInfo.Arguments = strSend
                    pro.Start()
                    isOpen = True
                End If
            End If

            If iHandle = 0 Then
                Dim intCount As Integer = 0
                While intCount < 10
                    iHandle = GetIHandle()
                    If iHandle > 0 Then
                        Exit While
                    End If
                    intCount = intCount + 1
                    Utility.WaitSeconds(1)
                End While
            End If

            If iHandle > 0 Then
                If isOpen Then
                    If Me.SendiCloverProcFormHandle() Then
                        isOpen = False
                    End If
                End If

                Dim sarr As Byte() = System.Text.Encoding.[Default].GetBytes(strSend)
                Dim len As Integer = sarr.Length
                Dim cds As COPYDATASTRUCT
                cds.dwData = CType(100, IntPtr)
                cds.lpData = strSend
                cds.cbData = len + 1
                SendMessage(iHandle, WM_COPYDATA, SC_RESTORE, cds)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoSendMessage")
        End Try
    End Sub

    <DllImport("User32.dll", EntryPoint:="SendMessage")> _
    Private Shared Function SendMessage(hWnd As Integer, Msg As Integer, wParam As Integer, ByRef lParam As COPYDATASTRUCT) As Integer
    End Function

    <DllImport("User32.dll", EntryPoint:="FindWindow")> _
    Private Shared Function FindWindow(lpClassName As String, lpWindowName As String) As Integer
    End Function

    Private Function GetIHandle() As Integer
        Dim iHandle As Integer = 0
        Try
            iHandle = FindWindow(Nothing, "WeChatHelperMain")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetIHandle")
        End Try
        Return iHandle
    End Function


    Private Function SendiCloverProcFormHandle() As Boolean

        Dim isResult As Boolean = False
        Try
            Dim ProcFormHandle As IntPtr = IntPtr.Zero
            For Each item As Form In Application.OpenForms
                'If item.InvokeRequired Then
                '    item.Invoke(New Threading.ThreadStart(AddressOf SendiCloverProcFormHandleHandler))
                'End If

#If IS_RECOVERY Then
                If TypeOf item Is MainFormDataRecovery Then
                    ProcFormHandle = GetFormHandle(item) 'item.Handle
                    Exit For
                End If
#Else
                If TypeOf item Is ProcForm Then
                    ProcFormHandle = item.Handle
                    Exit For
                End If
#End If

            Next
            Me.DoSendMessage("iCloverProcFormHandle" & ProcFormHandle.ToString())
            isResult = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SendiCloverProcFormHandle")
        End Try
        Return isResult
    End Function


    Private Delegate Function GetFormHandleHandler(ByVal item As Form) As IntPtr
    Private Function GetFormHandle(ByVal item As Form) As IntPtr
        Dim ProcFormHandle As IntPtr = IntPtr.Zero
        If item.InvokeRequired Then
            ProcFormHandle = item.Invoke(New GetFormHandleHandler(AddressOf GetFormHandle), item)
        Else
            Try
                ProcFormHandle = item.Handle
            Catch ex As Exception

            End Try
        End If
        Return ProcFormHandle
    End Function

#End Region

#Region "--- 接收消息 ---"

    Public Sub WCRDataProgress(lpData As String)
        Try
            Dim arrData As String() = lpData.Split("﹁")
            Dim wType As WCRType = GetStatusFromString(arrData(0))
            If wType = WCRType.None Then
                Return
            End If

            Dim intCodeOrProgress As Integer = 0
            Dim strContent As String = ""
            Dim isFinish As Boolean = False

            Select Case wType
                Case WCRType.ErrorM, WCRType.Exception
                    intCodeOrProgress = arrData(1)
                    strContent = arrData(2)

                Case WCRType.Progress
                    intCodeOrProgress = arrData(1)
                    If intCodeOrProgress >= 100 Then
                        isFinish = True
                    End If

                Case WCRType.Record
                    Dim strTemp As String = lpData.Replace(String.Format("{0}﹁{1}﹁", wType.ToString(), arrData(1)), "")
                    'Dim bTemp As Byte() = Encoding.Default.GetBytes(strTemp)
                    'strTemp = Encoding.UTF8().GetString(bTemp)
                    Me.WCRDataRecord(strTemp)
            End Select
            If wType <> WCRType.Record Then
                Dim args As New FindQQDataEventArgs(wType, intCodeOrProgress, strContent)
                If isFinish Then
                    args = New FindQQDataEventArgs(wType, intCodeOrProgress, strContent, Me.mDictDelChatInfo, Me.mDictDelFriendInfo, Me.mDictFriendInfo)
                End If
                Me.WCRProgress(args)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WCRDataProgress")
        End Try
    End Sub

    Private Sub WCRDataRecord(ByVal strContent As String)
        Try
            Dim strKey As String = ""
            Dim info As QQChatInfo
            Dim isGroup As Boolean = False
            Dim isDiscuss As Boolean = False
            If strContent.Contains("tb_TroopMsg") Then
                '群消息
                info = ConvertToTroopMsgByJson(strContent)
                isGroup = True
            ElseIf strContent.Contains("tb_discussGrp") Then
                '讨论组
                info = ConvertToDiscussGrpByJson(strContent)
                isDiscuss = True
            ElseIf strContent.Contains("tb_c2cMsg") Then
                '私聊消息
                info = ConvertToc2cMsgByJson(strContent)
                
            End If
            If info IsNot Nothing AndAlso Not String.IsNullOrEmpty(info.StrSendUin) AndAlso Not String.IsNullOrEmpty(info.StrMessage) Then
                Me.AddDelChatInfo(info.StrUsrName, info)
                Me.AddDelFriendInfo(info.StrUsrName, info.StrUsrName, isGroup, isDiscuss)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WCRDataRecord")
        End Try
    End Sub

    Private Function ConvertToc2cMsgByJson(ByVal strContent As String) As QQChatInfo

        Dim info As QQChatInfo = New QQChatInfo()
        Try
            Dim jsonObj As JsonObject = JsonParser.ParseString(strContent)
            If jsonObj.ContainsKey("uin") Then
                info.StrUsrName = CType(jsonObj("uin").ToString(), JsonString).Value()
                info.StrSendUin = CType(jsonObj("uin").ToString(), JsonString).Value()
            End If

            If jsonObj.ContainsKey("msgId") Then
                info.IntID = CType(jsonObj("msgId").ToString(), JsonNumber).Value()
            End If

            If jsonObj.ContainsKey("content") Then

                'Dim byt As Byte() = Encoding.UTF8.GetBytes(jsonObj("content"))
                info.StrMessage = CType(jsonObj("content"), JsonString).Value()
            End If

            If jsonObj.ContainsKey("read") Then
                info.IntStatus = CType(jsonObj("read").ToString(), JsonNumber).Value()
            End If

            If jsonObj.ContainsKey("type") Then
                'info.SCType = CType(jsonObj("type"), JsonNumber).Value()
                info.SCType = ConvertSCType(CType(jsonObj("type").ToString(), JsonNumber).Value())
            End If

            If jsonObj.ContainsKey("flag") Then
                Dim intFlag As Integer = CType(jsonObj("flag").ToString(), JsonNumber).Value()
                If intFlag = 0 Then
                    info.IntDes = 1
                Else
                    info.IntDes = 0
                End If
            End If

            If jsonObj.ContainsKey("time") Then
                Dim longCreateTime As Long = CType(jsonObj("time"), JsonNumber).Value()
                info.IntCreateTime = longCreateTime
                info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
            End If

            If jsonObj.ContainsKey("picUrl") Then
                Dim jsonType As IJsonType = jsonObj("picUrl")
                If Not (TypeOf jsonType Is JsonNull) Then
                    info.StrPicUrl = CType(jsonObj("picUrl"), JsonString).Value()
                End If
            End If
            info.IsDelete = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ConvertToc2cMsgByJson")
        End Try
        Return info
    End Function

    Private Function ConvertToTroopMsgByJson(ByVal strContent As String) As QQChatInfo
        Dim info As QQChatInfo = New QQChatInfo()
        Try
            Dim jsonObj As JsonObject = JsonParser.ParseString(strContent)
            If jsonObj.ContainsKey("tableName") Then
                Dim strTableName As String = CType(jsonObj("tableName"), JsonString).Value()
                info.StrUsrName = strTableName.Replace("tb_TroopMsg_", "")
            End If

            'If jsonObj.ContainsKey("uin") Then
            '    info.StrUsrName = CType(jsonObj("nickName"), JsonString).Value()

            'End If
            If jsonObj.ContainsKey("SendUin") Then
                info.StrSendUin = CType(jsonObj("SendUin").ToString().Replace("""", ""), JsonNumber).Value().ToString()
            End If

            If jsonObj.ContainsKey("msgId") Then
                info.IntID = CType(jsonObj("msgId").ToString(), JsonNumber).Value()
            End If

            If jsonObj.ContainsKey("strMsg") Then
                info.StrMessage = CType(jsonObj("strMsg"), JsonString).Value()
            End If

            If jsonObj.ContainsKey("read") Then
                info.IntStatus = CType(jsonObj("read").ToString(), JsonNumber).Value()
            End If

            If jsonObj.ContainsKey("sMsgType") Then
                'info.SCType = CType(jsonObj("sMsgType"), JsonNumber).Value()
                info.SCType = ConvertSCType(CType(jsonObj("sMsgType").ToString(), JsonNumber).Value())
            End If

            If jsonObj.ContainsKey("nickName") Then
                info.StrNickName = CType(jsonObj("nickName"), JsonString).Value()
            End If

            If String.IsNullOrEmpty(info.StrNickName) Then
                info.StrNickName = info.StrSendUin
            End If
            'If jsonObj.ContainsKey("flag") Then
            '    Dim intFlag As Integer = CType(jsonObj("flag"), JsonNumber).Value()
            '    If intFlag = 0 Then
            '        info.IntDes = 1
            '    Else
            '        info.IntDes = 0
            '    End If
            'End If

            If jsonObj.ContainsKey("MsgTime") Then
                Dim longCreateTime As Long = CType(jsonObj("MsgTime").ToString(), JsonNumber).Value()
                info.IntCreateTime = longCreateTime
                info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
            End If

            If jsonObj.ContainsKey("picUrl") Then
                Dim jsonType As IJsonType = jsonObj("picUrl")
                If Not (TypeOf jsonType Is JsonNull) Then
                    info.StrPicUrl = CType(jsonObj("picUrl"), JsonString).Value()
                End If
            End If

            info.IsDelete = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ConvertToTroopMsgByJson")
        End Try
        Return info
    End Function

    Private Function ConvertToDiscussGrpByJson(ByVal strContent As String) As QQChatInfo
        Dim info As QQChatInfo = New QQChatInfo()
        Try
            Dim jsonObj As JsonObject = JsonParser.ParseString(strContent)
            If jsonObj.ContainsKey("DiscussUin") Then
                info.StrUsrName = CType(jsonObj("DiscussUin"), JsonString).Value()
            End If

            'If jsonObj.ContainsKey("uin") Then
            '    info.StrUsrName = CType(jsonObj("nickName"), JsonString).Value()

            'End If
            If jsonObj.ContainsKey("SendUin") Then
                info.StrSendUin = CType(jsonObj("SendUin"), JsonString).Value()
            End If

            If jsonObj.ContainsKey("MsgId") Then
                info.IntID = CType(jsonObj("MsgId").ToString(), JsonNumber).Value()
            End If

            If jsonObj.ContainsKey("Msg") Then
                info.StrMessage = CType(jsonObj("Msg"), JsonString).Value()
            End If

            If jsonObj.ContainsKey("Read") Then
                info.IntStatus = CType(jsonObj("Read").ToString(), JsonNumber).Value()
            End If

            If jsonObj.ContainsKey("MsgType") Then
                'info.SCType = CType(jsonObj("MsgType"), JsonNumber).Value()
                info.SCType = ConvertSCType(CType(jsonObj("MsgType").ToString(), JsonNumber).Value())
            End If

            If jsonObj.ContainsKey("NickName") Then
                info.StrNickName = CType(jsonObj("NickName"), JsonString).Value()
            End If

            If String.IsNullOrEmpty(info.StrNickName) Then
                info.StrNickName = info.StrSendUin
            End If

            'If jsonObj.ContainsKey("flag") Then
            '    Dim intFlag As Integer = CType(jsonObj("flag"), JsonNumber).Value()
            '    If intFlag = 0 Then
            '        info.IntDes = 1
            '    Else
            '        info.IntDes = 0
            '    End If
            'End If

            If jsonObj.ContainsKey("MsgTime") Then
                Dim longCreateTime As Long = CType(jsonObj("MsgTime").ToString(), JsonNumber).Value()
                info.IntCreateTime = longCreateTime
                info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
            End If

            If jsonObj.ContainsKey("picUrl") Then
                Dim jsonType As IJsonType = jsonObj("picUrl")
                If Not (TypeOf jsonType Is JsonNull) Then
                    info.StrPicUrl = CType(jsonObj("picUrl"), JsonString).Value()
                End If
            End If

            info.IsDelete = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ConvertToDiscussGrpByJson")
        End Try
        Return info
    End Function

    Private Function ConvertSCType(intType As Integer) As SociaChatType
        Dim scType As SociaChatType = SociaChatType.Text
        Select Case intType
            Case 0
                scType = SociaChatType.Text
            Case 1
                scType = SociaChatType.Picture
            Case 3
                scType = SociaChatType.Voice
            Case 0, 351
                scType = SociaChatType.SpecialExpression
            Case 150, 15
                scType = SociaChatType.VideoConnected
            Case 4
                scType = SociaChatType.File
            Case 141
                scType = SociaChatType.Location
            Case 181
                scType = SociaChatType.SmallVideo
            Case 332, 352
                scType = SociaChatType.SystemMessages
        End Select
        Return scType
    End Function

    Private Function GetFindName(strMd5 As String) As String
        Dim strUserName As String = strMd5
        Dim isContain As Boolean = False
        Try
            If strUserName.StartsWith(WeixinImportOrExportHelper.mStrUnknown) Then
                GoTo Do_Exit
            End If
            For Each item As KeyValuePair(Of String, QQFriendInfo) In Me.mDictFriendInfo
                'If item.Value.Md5 = strMd5 Then
                '    strUserName = item.Value.UsrName
                '    isContain = True
                '    Continue For
                'End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "FindWeChatDataHelper_GetFindName")
        End Try
        If Not isContain Then
            strUserName = WeixinImportOrExportHelper.mStrUnknown & "_" & strUserName
        End If
Do_Exit:
        Return strUserName
    End Function

    Public Shared Function GetStatusFromString(strStatus As String) As WCRType
        Dim status As WCRType = WCRType.None
        Try
            status = DirectCast([Enum].Parse(GetType(WCRType), strStatus, True), WCRType)
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
        Return status
    End Function

#End Region

    Private Sub AddDelChatInfo(strKey As String, cInfo As QQChatInfo)
        Try
            Dim lstWCCInfo As List(Of QQChatInfo) = New List(Of QQChatInfo)
            If Me.mDictDelChatInfo.ContainsKey(strKey) Then
                Dim LstTempWCCInfo As List(Of QQChatInfo) = Me.mDictDelChatInfo(strKey)
                LstTempWCCInfo.Add(cInfo)
                lstWCCInfo = LstTempWCCInfo
                Me.mDictDelChatInfo.Remove(strKey)
            Else
                lstWCCInfo.Add(cInfo)
            End If
            Me.mDictDelChatInfo.Add(strKey, lstWCCInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddDelChatInfo")
        End Try
    End Sub

    Private Sub AddDelFriendInfo(strKey As String, strUserName As String, Optional ByVal isGroup As Boolean = False, Optional ByVal isDiscuss As Boolean = False)
        Try
            If Not Me.mDictDelFriendInfo.ContainsKey(strUserName) Then
                Dim fInfo As QQFriendInfo = Me.GetFriendInfo(strKey)
                If strUserName.StartsWith(WeixinImportOrExportHelper.mStrUnknown) OrElse fInfo Is Nothing Then
                    fInfo = New QQFriendInfo()
                    fInfo.UsrName = strUserName
                    fInfo.NickName = strUserName
                    fInfo.NoteName = strUserName
                    If Not String.IsNullOrEmpty(fInfo.UsrName) Then
                        fInfo.StrQQNum = fInfo.UsrName
                    End If
                    'fInfo.Md5 = strKey
                End If
                fInfo.IsFindDate = True
                If Me.mDictDelChatInfo.ContainsKey(strKey) Then
                    fInfo.DTLastSendTime = Me.mDictDelChatInfo(strKey)(0).DTCreateTime
                    'If strUserName.StartsWith(WeixinImportOrExportHelper.mStrUnknown) Then
                    '    fInfo.StrLastSendMsg = Me.mDictDelChatInfo(fInfo.Md5)(0).StrMessage
                    'End If
                Else
                    fInfo.DTLastSendTime = DateTime.Now
                End If

                fInfo.IsGroup = isGroup
                fInfo.IsDiscuss = isDiscuss


                Me.mDictDelFriendInfo.Add(strUserName, fInfo)
                If Not Me.mDictFriendInfo.ContainsKey(strUserName) Then
                    Me.mDictFriendInfo.Add(strUserName, fInfo)
                Else
                    Dim wcfInfo As QQFriendInfo = Me.mDictFriendInfo(strUserName)
                    Me.mDictFriendInfo.Remove(strUserName)
                    wcfInfo.IsFindDate = True
                    Me.mDictFriendInfo.Add(strUserName, wcfInfo)
                End If

                Me.WCRProgress(New FindQQDataEventArgs(fInfo, WCRType.Record))
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddDelFriendInfo")
        End Try
    End Sub

    Private Function GetFriendInfo(ByVal strKey As String) As QQFriendInfo
        Dim fInfo As QQFriendInfo = Nothing
        For Each item As QQFriendInfo In Me.mDictFriendInfo.Values
            If item.StrQQNum = strKey Then
                fInfo = New QQFriendInfo()
                fInfo.StrChatRoomMem = item.StrChatRoomMem
                fInfo.IsFindDate = item.IsFindDate
                fInfo.StrLastSendMsg = item.StrLastSendMsg
                fInfo.DTLastSendTime = item.DTLastSendTime
                'fInfo.Md5 = item.Md5
                fInfo.StrQQNum = item.StrQQNum
                fInfo.NickName = item.NickName
                fInfo.NoteName = item.NoteName
                fInfo.StrProfile = item.StrProfile
                fInfo.IntSex = item.IntSex
                fInfo.SFType = item.SFType
                fInfo.UsrName = item.UsrName
                Exit For
            End If
        Next
        Return fInfo
    End Function
    Public Class FindQQDataEventArgs
        Inherits EventArgs

        Dim mIntProgress As Integer = 0
        Public Property IntProgress() As Integer
            Get
                Return Me.mIntProgress
            End Get
            Set(ByVal value As Integer)
                Me.mIntProgress = value
            End Set
        End Property

        Dim mWType As WCRType = WCRType.None
        Public Property WType() As WCRType
            Get
                Return Me.mWType
            End Get
            Set(ByVal value As WCRType)
                Me.mWType = value
            End Set
        End Property

        Dim mStrMsg As String = ""
        Public Property StrMsg() As String
            Get
                Return Me.mStrMsg
            End Get
            Set(ByVal value As String)
                Me.mStrMsg = value
            End Set
        End Property

        Dim mQQInfo As QQFriendInfo = Nothing
        Public Property WCFInfo() As QQFriendInfo
            Get
                Return Me.mQQInfo
            End Get
            Set(ByVal value As QQFriendInfo)
                Me.mQQInfo = value
            End Set
        End Property

        Dim mDictDelChatInfo As New Dictionary(Of String, List(Of QQChatInfo))          '被删除的聊天信息
        Public Property DictDelChatInfo() As Dictionary(Of String, List(Of QQChatInfo))
            Get
                Return Me.mDictDelChatInfo
            End Get
            Set(ByVal value As Dictionary(Of String, List(Of QQChatInfo)))
                Me.mDictDelChatInfo = value
            End Set
        End Property

        Dim mDictDelFriendInfo As New Dictionary(Of String, QQFriendInfo) '被删除的聊天信息 对应的联系人
        Public Property DictDelFriendInfo() As Dictionary(Of String, QQFriendInfo)
            Get
                Return Me.mDictDelFriendInfo
            End Get
            Set(ByVal value As Dictionary(Of String, QQFriendInfo))
                Me.mDictDelFriendInfo = value
            End Set
        End Property

        Dim mDictFriendInfo As New Dictionary(Of String, QQFriendInfo)                     '所有联系人表（UserName,FriendInfo）
        Public Property DictFriendInfo() As Dictionary(Of String, QQFriendInfo)
            Get
                Return Me.mDictFriendInfo
            End Get
            Set(ByVal value As Dictionary(Of String, QQFriendInfo))
                Me.mDictFriendInfo = value
            End Set
        End Property

        Public Sub New(ByVal wType As WCRType, ByVal intProgress As Integer, ByVal strMsg As String)
            Me.mIntProgress = intProgress
            Me.mWType = wType
            Me.mStrMsg = strMsg
        End Sub

        Public Sub New(WInfo As QQFriendInfo, ByVal wType As WCRType)
            Me.mQQInfo = WInfo
            Me.mWType = wType
        End Sub
        Public Sub New(ByVal wType As WCRType, ByVal intProgress As Integer, ByVal strMsg As String, dicDelChatInfo As Dictionary(Of String, List(Of QQChatInfo)), dicDelFriendInfo As Dictionary(Of String, QQFriendInfo), dicFriendInfo As Dictionary(Of String, QQFriendInfo))
            Me.mIntProgress = intProgress
            Me.mWType = wType
            Me.mStrMsg = strMsg

            Me.mDictDelChatInfo = dicDelChatInfo
            Me.mDictDelFriendInfo = dicDelFriendInfo
            Me.mDictFriendInfo = dicFriendInfo
        End Sub
    End Class
End Class
