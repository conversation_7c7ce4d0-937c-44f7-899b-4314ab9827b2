﻿Imports System.Threading

'1.我的世界盒子导入地图到我的世界
'2.同步推铃声导入到手机

Public Class MyWorldHelper
    Implements IDisposable

    Private mThrLoad As Thread = Nothing                    '加载设备安装列表信息
    Private mDevice As iPhoneDevice = Nothing
    Private mApplication As IApplication = Nothing
    Private mTimer As System.Timers.Timer = Nothing
    Private Shared mDictInstance As New Dictionary(Of String, MyWorldHelper)(StringComparer.InvariantCultureIgnoreCase)                '一个设备返回一个实例
    Private Shared mLocker As New Object

    Private mThrResource As Thread = Nothing                'copy地图操作同步推铃声放在同一个线程

    '我的世界参数
    Private mMyWorldInfo As PackageInfo = Nothing
    Private mMyWorldBoxInfo As PackageInfo = Nothing
    Private mMyWorldIndntify As List(Of String) = ServerIniSetting.GetMyWorldIdentify
    Private mIsLoaded As Boolean = False

    '同步推参数
    Private mLstTuiIndntify As New List(Of String)
    Private mDictRingtines As New Dictionary(Of Object, Object)
    Private mDictRingtinesCompleted As New Dictionary(Of Object, Object)

#Region "--- 初始化 ---"

    Public Shared Function GetInstance(ByVal dev As iPhoneDevice, ByVal app As IApplication) As MyWorldHelper
        Dim helper As MyWorldHelper = Nothing
        Dim blnRetry As Boolean = False

        SyncLock mLocker
            Try
                If dev IsNot Nothing AndAlso dev.IsConnected Then
                    If mDictInstance.ContainsKey(dev.Identifier) Then
                        Dim tmpHelper As MyWorldHelper = mDictInstance(dev.Identifier)
                        If tmpHelper.mDevice.IsConnected = False AndAlso dev.IsConnected Then
                            mDictInstance.Remove(dev.Identifier)
                            mDictInstance.Add(dev.Identifier, New MyWorldHelper(dev, app))
                            '释放资源
                            tmpHelper.Dispose()
                        End If

                    Else
                        mDictInstance.Add(dev.Identifier, New MyWorldHelper(dev, app))

                    End If

                    helper = mDictInstance.Item(dev.Identifier)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "MyWorldHelper_GetInstance")
            End Try
        End SyncLock

        Return helper
    End Function

    Public Shared Sub RemoveInstance(ByVal dev As iPhoneDevice)
        SyncLock mLocker
            Try
                If dev IsNot Nothing AndAlso mDictInstance.ContainsKey(dev.Identifier) Then
                    Dim tmpHelper As MyWorldHelper = mDictInstance(dev.Identifier)
                    mDictInstance.Remove(dev.Identifier)
                    tmpHelper.Dispose()
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "MyWorldHelper_RemoveInstance")
            End Try
        End SyncLock
    End Sub

    Public Sub New(ByVal dev As iPhoneDevice, ByVal app As IApplication)
        Me.mDevice = dev
        Me.mApplication = app
        Me.Reload()
        Me.InitTimer()
    End Sub

    Private Sub Reload()
        Try
            If Utility.CheckThreadIsAlive(Me.mThrLoad) Then
                Return
            End If

            Me.mThrLoad = New Thread(AddressOf ReloadThread)
            With mThrLoad
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_Load")
        End Try
    End Sub

    Private Sub ReloadThread()
        Try
            Dim dict As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            If dict Is Nothing OrElse dict.Count = 0 Then
                Utility.WaitSeconds(5)
                dict = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            End If

            For Each item As FileSharingPackageInfo In dict.Values
                '我的世界
                For Each strWorldId As String In Me.mMyWorldIndntify
                    If item.Identifier = strWorldId Then
                        Me.mMyWorldInfo = item
                        Continue For
                    End If
                Next
                '我的世界盒子
                If item.IsMyWorldBox Then
                    Me.mMyWorldBoxInfo = item
                End If

                '同步推
                If item.IsTui Then
                    Me.mLstTuiIndntify.Add(item.Identifier)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_LoadThread")
        End Try
    End Sub

    Private Sub InitTimer()
        Me.mTimer = New System.Timers.Timer()
        Me.mTimer.Interval = 5000
        AddHandler Me.mTimer.Elapsed, AddressOf mTimer_Elapsed

        Me.mTimer.Start()
    End Sub

    Private Sub mTimer_Elapsed(ByVal sender As Object, ByVal e As System.Timers.ElapsedEventArgs)
        Me.StartCheckResources()
    End Sub

    Private Sub StartCheckResources()
        Try
            If Utility.CheckThreadIsAlive(Me.mThrResource) Then
                Return
            End If

            Me.mThrResource = New Thread(AddressOf ResourceThread)
            With mThrResource
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper.StartCheckResources")
        End Try
    End Sub

    Private Sub ResourceThread()
        '去掉我的世界盒子逻辑
        'Me.MyWorldTimerEvent()

        '同步铃声资源
        'Me.TuiTimerEvent()
    End Sub

#End Region

#Region "--- 我的世界处理事件 ---"

    Private Sub MyWorldTimerEvent()
        '如果我的世界和我和世界盒子有一个不存在就不处理
        If Me.mMyWorldInfo Is Nothing OrElse Me.mMyWorldBoxInfo Is Nothing Then
            Return
        End If

        Try
            Dim handMyWorld As IntPtr = Me.mDevice.GetAfcByFileSharing(Me.mMyWorldInfo.Identifier, HouseArrestType.ResourceOne, False)
            Dim handMyWorldBox As IntPtr = Me.mDevice.GetAfcByFileSharing(Me.mMyWorldBoxInfo.Identifier, HouseArrestType.ResourceTwo, False)

            If handMyWorld = IntPtr.Zero OrElse handMyWorldBox = IntPtr.Zero Then
                Common.LogException(String.Format("App:{0} Box:{1}", handMyWorld, handMyWorldBox), "MyWorldHelper_GetAppHandlerErrot")
                Return
            End If

            Me.CopyMaps(handMyWorld, handMyWorldBox)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_mTimer_Elapsed")
        End Try
    End Sub

#Region "--- 连接 ---"

    Private Sub SetConnectMyWorld(ByVal handMyWorldBox As IntPtr)
        Try
            '助手有连接手机的时候就创建这个文件，手机端有检测到这个文件存在证明是和助手相连
            Dim strConnPath As String = "/Documents/watcher/"
            Dim strConnFile As String = "/Documents/watcher/connectToTongbu.plist"
            Me.mDevice.CheckFolder(strConnPath, handMyWorldBox)
            Dim dictResult As New Dictionary(Of Object, Object)
            dictResult.Add("timeInterval", DateTime.Now.ToFileTimeUtc)
            Me.UploadPlist(handMyWorldBox, dictResult, strConnFile, False)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_SetConnect")
        End Try
    End Sub

#End Region

#Region "--- 导入地图 ---"

    Private Sub CopyMaps(ByVal handMyWorld As IntPtr, ByVal handMyWorldBox As IntPtr)
        Try
            Dim strImportList As String = "/Documents/readynput/inputList.plist"

            '设置盒子与助手的连接文件
            Me.SetConnectMyWorld(handMyWorldBox)

            '刚连接进来的时候更新一次已导入列表
            If Not Me.mIsLoaded Then
                Me.mIsLoaded = True
                Me.GetMyWorldMpas(handMyWorld, handMyWorldBox)
            End If

            '取得要copy的文件列表
            Dim dict As Dictionary(Of Object, Object) = Me.GetInputList(handMyWorldBox, strImportList)
            If dict Is Nothing OrElse dict.Count <= 0 Then
                Return
            End If

            '导入地图
            Me.CopyMapToMyWorld(handMyWorld, handMyWorldBox, dict)

            '地图导入完成后清空要导入的列表
            Me.UploadPlist(handMyWorldBox, Nothing, strImportList)

            '导入完成后再更新一次已导入列表。
            Me.GetMyWorldMpas(handMyWorld, handMyWorldBox)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_CopyFilesThread")
        End Try
    End Sub

    Private Function GetInputList(ByVal handMyWorldBox As IntPtr, ByVal strImportList As String)
        '取得要copy的文件列表
        Dim strImportListOnPC As String = Folder.GetTempFilePath()
        Dim dict As New Dictionary(Of Object, Object)
        If Me.mDevice.Exists(handMyWorldBox, strImportList) AndAlso Me.mDevice.DownFromPhone(handMyWorldBox, strImportList, strImportListOnPC) Then
            dict = Device.CoreFoundation.ReadPlist_managed(strImportListOnPC)
        End If
        Return dict
    End Function

    Private Sub CopyMapToMyWorld(ByVal handMyWorld As IntPtr, ByVal handMyWorldBox As IntPtr, ByVal dict As Dictionary(Of Object, Object))
        Dim strMapPathBox As String = "/Documents/minecraftWorlds/"
        Dim strMapPath As String = "/Documents/games/com.mojang/minecraftWorlds/"
        Dim strImportCompleteList As String = "/Documents/back/complete.plist"

        '导入地图
        Dim dictResult As Dictionary(Of Object, Object)
        Dim lstSucceed As New List(Of String)
        Dim lstFailure As New List(Of String)

        Me.mDevice.CheckFolder(strMapPath, handMyWorld)

        If Not dict.ContainsKey("gameName") OrElse dict("gameName") Is Nothing Then
            Return
        End If

        Try
            For Each item As Object In dict("gameName")
                Dim strMapPathOnBoxPC As String = Folder.GetTempFilePath    '地图缓存在本地电脑的位置
                '下载地图
                Dim strMapPathOnBox As String = strMapPathBox & item
                Dim downResult As Boolean = Me.mDevice.DownFromPhone(handMyWorldBox, strMapPathOnBox, strMapPathOnBoxPC)
                If Not downResult Then
                    Utility.WaitSeconds(0.5)
                    downResult = Me.mDevice.DownFromPhone(handMyWorldBox, strMapPathOnBox, strMapPathOnBoxPC)
                End If

                '上传地图到我的世界
                Dim strMapPathOnMyWorld As String = strMapPath & item
                Dim uploadResult As Boolean = False
                If downResult Then
                    uploadResult = Me.mDevice.CopyToPhone(handMyWorld, strMapPathOnBoxPC, strMapPathOnMyWorld)
                    If Not uploadResult Then
                        Utility.WaitSeconds(0.5)
                        uploadResult = Me.mDevice.CopyToPhone(handMyWorld, strMapPathOnBoxPC, strMapPathOnMyWorld)
                    End If
                End If

                '验证是不是上传成功
                If downResult AndAlso uploadResult Then
                    lstSucceed.Add(item)
                Else
                    lstFailure.Add(item)
                    Try
                        Me.mDevice.DeleteDirectory(handMyWorld, strMapPathOnMyWorld)
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "MyWorldHelper_UploadFailure_deleteFolder")
                    End Try
                End If
                dictResult = New Dictionary(Of Object, Object)
                dictResult.Add("succeed", lstSucceed.ToArray())
                dictResult.Add("failure", lstFailure.ToArray())
                dictResult.Add("count", CType(dict("gameName"), Object()).Length)
                '更新导入结果
                Me.UploadPlist(handMyWorldBox, dictResult, strImportCompleteList)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_CopyMapToMyWorld")
        End Try
    End Sub

    Private Sub GetMyWorldMpas(ByVal handMyWorld As IntPtr, ByVal handMyWorldBox As IntPtr)
        Try
            '更新已导入的地图列表。
            Dim strMapPath As String = "/Documents/games/com.mojang/minecraftWorlds/"
            Dim strFiles() As String = Me.mDevice.GetFiles(handMyWorld, strMapPath, True)
            Dim dictFileList As New Dictionary(Of Object, Object)
            If strFiles IsNot Nothing AndAlso strFiles.Length > 0 Then
                dictFileList.Add("gameName", strFiles)
            End If

            Dim strImportList As String = "/Documents/back/minecraftWorldsList.plist"
            Me.UploadPlist(handMyWorldBox, dictFileList, strImportList)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_GetMyWorldMpas")
        End Try
    End Sub

    Public Sub CheckMyWorld(ByVal appInfo As PackageInfo)
        '如果用户安装了我的世界或者我的世界盒子就重新刷新一下安装列表
        Dim blnIsMyWorld As Boolean = False
        For Each item As String In Me.mMyWorldIndntify
            If appInfo.Identifier = item Then
                blnIsMyWorld = True
            End If
        Next

        If blnIsMyWorld OrElse appInfo.IsMyWorldBox Then
            Me.Reload()
        End If
    End Sub

#End Region

#End Region

#Region "--- 同步推铃声处理事件 ---"

    Private Enum TuiEvent
        Connect = 0
        Synching = 1
        Synched = 2
    End Enum

#Region "--- 与同步推信息交互 ---"

    Private Sub SetTuiPlist(ByVal handTui As IntPtr, ByVal cmdEvent As TuiEvent, ByVal data As Dictionary(Of Object, Object))
        Dim strConnPath As String = "/Documents/Tongbu/RingtoneWatch"
        Dim strConnFile As String = "/Documents/Tongbu/RingtoneWatch/command.plist"
        Me.mDevice.CheckFolder(strConnPath, handTui)
        Dim dictResult As New Dictionary(Of Object, Object)
        dictResult.Add("cmd", Convert.ToInt32(cmdEvent))
        dictResult.Add("time", DateTime.Now.ToFileTimeUtc)
        dictResult.Add("data", data)
        Me.UploadPlist(handTui, dictResult, strConnFile, True)
    End Sub

    '取得同步推上要同步的铃声文件列表
    Private Function GetTuiPlist(ByVal handTui As IntPtr) As Dictionary(Of Object, Object)
        Dim strConnFile As String = "/Documents/Tongbu/Ringtone/command.plist"
        '取得要copy的文件列表
        Dim strImportListOnPC As String = Folder.GetTempFilePath()
        Dim dict As New Dictionary(Of Object, Object)
        If Me.mDevice.Exists(handTui, strConnFile) AndAlso Me.mDevice.DownFromPhone(handTui, strConnFile, strImportListOnPC) Then
            dict = Device.CoreFoundation.ReadPlist_managed(strImportListOnPC)
        End If
        Return dict
    End Function

    '删除设备上的Plist文件
    Private Sub DeleteTuiPlist(ByVal handTui As IntPtr)
        Dim strConnFile As String = "/Documents/Tongbu/Ringtone/command.plist"
        If Me.mDevice.Exists(handTui, strConnFile) Then
            Me.mDevice.DeleteFile(handTui, strConnFile)
        End If
    End Sub

#End Region

#Region "--- 同步推铃声处理事件 ---"

    Private Sub TuiTimerEvent()
        Try
            If Me.mDevice.CheckIsSync Then
                Return
            End If

            For Each item As String In Me.mLstTuiIndntify
                Dim handTui As IntPtr = Me.mDevice.GetAfcByFileSharing(item, HouseArrestType.ResourceOne, False)
                If handTui = IntPtr.Zero Then
                    Continue For
                End If

                '设置同步推与助手的连接文件
                Me.SetTuiPlist(handTui, TuiEvent.Connect, Nothing)

                Dim dev As UsbDevice = CType(Me.mApplication, MainForm).GetDevice(Me.mDevice.Identifier)
                If dev Is Nothing Then
                    Return
                End If

                Dim dictRingtines As Dictionary(Of Object, Object) = Me.GetRingtoneFiles(handTui)
                If dictRingtines Is Nothing OrElse dictRingtines.Count <= 0 Then
                    Continue For
                End If
                Me.mDictRingtines = dictRingtines
                Me.mDictRingtinesCompleted.Clear()
                Me.SetTuiPlist(handTui, TuiEvent.Synching, Nothing)
                Dim intCount As Integer = 0

                Dim lstRingFiles As List(Of String) = Me.DownloadRingtonesToPC(handTui, dictRingtines)
                '导入铃声到设备上
                Dim helper As New iPhoneImportHelper(dev, Me.mApplication, Nothing)
                helper.StartImport(ImportType.Ringtone, lstRingFiles, Me.mApplication)
                AddHandler helper.ImportCompelete, AddressOf OnImportComplete
                intCount = 0
                While helper.Status <> ImportStatus.ImportCompleted
                    Utility.WaitSeconds(1)
                    intCount += 1
                    If intCount Mod 5 = 0 Then
                        '刷新连接状态
                        Me.SetTuiPlist(handTui, TuiEvent.Synching, Nothing)
                    End If
                End While

                Me.SetTuiPlist(handTui, TuiEvent.Synched, mDictRingtinesCompleted)
                Me.DeleteTuiPlist(handTui)
                Utility.WaitSeconds(1)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper_TuiRingtoneThread")
        End Try
    End Sub

    Private Function DownloadRingtonesToPC(ByVal handTui As IntPtr, ByVal dictRingtines As Dictionary(Of Object, Object)) As List(Of String)
        Dim lstRingFiles As New List(Of String)
        Dim intCount As Integer = 0
        For Each strRingFile As String In dictRingtines.Keys
            Dim strFilePathPhone As String = dictRingtines(strRingFile)
            Dim strFilePathPC As String = Path.Combine(Folder.RingtoneFolder, Path.GetFileName(strFilePathPhone))
            If Me.mDevice.DownFromPhone(handTui, strFilePathPhone, strFilePathPC) Then
                lstRingFiles.Add(strFilePathPC)
            End If
            intCount += 1
            If intCount Mod 5 = 0 Then
                '刷新连接状态
                Me.SetTuiPlist(handTui, TuiEvent.Connect, Nothing)
            End If
        Next
        Return lstRingFiles
    End Function

    Private Function GetRingtoneFiles(ByVal handTui As IntPtr) As Dictionary(Of Object, Object)
        Dim dictReturn As Dictionary(Of Object, Object) = Nothing
        Dim dictRingtines As Dictionary(Of Object, Object) = Me.GetTuiPlist(handTui)
        If dictRingtines IsNot Nothing AndAlso dictRingtines.Count > 0 Then
            If dictRingtines.ContainsKey("data") AndAlso TypeOf dictRingtines("data") Is Dictionary(Of Object, Object) Then
                dictReturn = dictRingtines("data")
            End If
        End If
        Return dictReturn
    End Function

    Private Delegate Sub OnImportProgressHandler(ByVal sender As Object, ByVal args As ImportProgressArgs)
    Private Sub OnImportProgress(ByVal sender As Object, ByVal args As ImportProgressArgs)
        Try


            If args.CurrentFileTransfer = args.CurrentFileLength Then
                '导入一个完成
                If args.ProgressString = Me.mApplication.Language.GetString("Media.Message.Finished") Then
                    Dim filename As String = Path.GetFileName(args.LocalFileName)
                    For Each key As String In Me.mDictRingtines.Keys
                        If mDictRingtines(key).ToString().Contains(filename) AndAlso Not mDictRingtinesCompleted.ContainsKey(key) Then
                            Me.mDictRingtinesCompleted.Add(key, mDictRingtines(key))
                            Exit For
                        End If
                    Next
                End If


            End If


        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.OnImportProgress")
        End Try


    End Sub

    Private Delegate Sub OnImportCompleteHandler(ByVal sender As Object, ByVal args As ImportCompleteArgs)
    Private Sub OnImportComplete(ByVal sender As Object, ByVal args As ImportCompleteArgs)
        Try
            Dim item As iPhoneImportHelper = sender

            Dim list As New List(Of String)
            For Each filename As String In item.mListFiles
                filename = Path.GetFileNameWithoutExtension(filename)
                list.Add(filename)
            Next

            For Each filePathOnPC As String In item.mListCancelFiles
                Dim filename As String = Path.GetFileNameWithoutExtension(filePathOnPC)

                If list.Contains(filename) Then
                    list.Remove(filename)
                End If
            Next

            For Each filePathOnPc As String In list
                Dim filename As String = Path.GetFileName(filePathOnPc)
                For Each key As String In Me.mDictRingtines.Keys
                    If mDictRingtines(key).ToString().Contains(filename) AndAlso Not mDictRingtinesCompleted.ContainsKey(key) Then
                        Me.mDictRingtinesCompleted.Add(key, mDictRingtines(key))
                        Exit For
                    End If
                Next
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "OnImportComplete")
        End Try
    End Sub

#End Region

#End Region

    Private Sub UploadPlist(ByVal hPtr As IntPtr, ByVal dictValue As Dictionary(Of Object, Object), ByVal strPathOnPhone As String, Optional ByVal isRename As Boolean = True)
        Dim strPathOnPC As String = Folder.GetTempFilePath()
        If dictValue Is Nothing Then
            dictValue = New Dictionary(Of Object, Object)
        End If
        Device.CoreFoundation.CreatePlist(dictValue, strPathOnPC)
        '是否用上传后重新命名的方式修改文件
        Dim strRename As String = ""
        If isRename Then
            strRename = "tmp"
        End If

        Dim strTmpOnPhone As String = strPathOnPhone & strRename
        If Me.mDevice.Exists(hPtr, strPathOnPhone) Then
            Me.mDevice.DeleteFile(hPtr, strPathOnPhone)
        End If
        Dim succeed As Boolean = Me.mDevice.CopyToPhone(hPtr, strPathOnPC, strTmpOnPhone)
        If Not succeed Then
            'succeed = Me.mDevice.CopyToPhone(hPtr, strPathOnPC, strTmpOnPhone)
            If Not succeed AndAlso Me.mDevice.Exists(hPtr, strTmpOnPhone) Then
                Me.mDevice.DeleteFile(hPtr, strTmpOnPhone)
                succeed = Me.mDevice.CopyToPhone(hPtr, strPathOnPC, strTmpOnPhone)
            End If
        End If
        If isRename Then
            succeed = Me.mDevice.Rename(hPtr, strTmpOnPhone, strPathOnPhone)
        End If
    End Sub

#Region "IDisposable Support"
    Private disposedValue As Boolean ' 检测冗余的调用

    ' IDisposable
    Protected Overridable Sub Dispose(disposing As Boolean)
        Try
            If Me.mTimer IsNot Nothing Then
                Me.mTimer.Close()
                RemoveHandler Me.mTimer.Elapsed, AddressOf mTimer_Elapsed
                Me.mTimer = Nothing
            End If

            Utility.AbortThread(Me.mThrLoad)
            Utility.AbortThread(Me.mThrResource)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "MyWorldHelper.Dispose")
        End Try
    End Sub

    ' TODO:  仅当上面的 Dispose(ByVal disposing As Boolean)具有释放非托管资源的代码时重写 Finalize()。
    'Protected Overrides Sub Finalize()
    '    ' 不要更改此代码。    请将清理代码放入上面的 Dispose(ByVal disposing As Boolean)中。
    '    Dispose(False)
    '    MyBase.Finalize()
    'End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。
    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。    请将清理代码放入上面的 Dispose (disposing As Boolean)中。
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub
#End Region

End Class
