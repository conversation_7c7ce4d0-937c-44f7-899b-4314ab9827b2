﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPersonalCenter
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPersonalCenter))
        Me.pnlContainer = New iTong.Components.tbPanel
        Me.wbsSite = New iTong.Components.tbWebBrowserEx
        Me.btn_close = New iTong.Components.tbButton
        Me.btn_normal = New iTong.Components.tbButton
        Me.btn_minimize = New iTong.Components.tbButton
        Me.pnlLoading = New System.Windows.Forms.Panel
        Me.lblLoading = New System.Windows.Forms.Label
        Me.pbLoading = New System.Windows.Forms.PictureBox
        Me.pnlContainer.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'pnlContainer
        '
        Me.pnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainer.Controls.Add(Me.wbsSite)
        Me.pnlContainer.Location = New System.Drawing.Point(1, 32)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(868, 587)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 0
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'wbsSite
        '
        Me.wbsSite.AllowWebBrowserDrop = False
        Me.wbsSite.Client = ""
        Me.wbsSite.DeviceList = Nothing
        Me.wbsSite.Dock = System.Windows.Forms.DockStyle.Fill
        Me.wbsSite.IsConnected = False
        Me.wbsSite.IsWebBrowserContextMenuEnabled = False
        Me.wbsSite.Jailbreaked = False
        Me.wbsSite.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.wbsSite.LoadingFont = Nothing
        Me.wbsSite.LoadingGif = CType(resources.GetObject("wbsSite.LoadingGif"), System.Drawing.Image)
        Me.wbsSite.Location = New System.Drawing.Point(0, 0)
        Me.wbsSite.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbsSite.Name = "wbsSite"
        Me.wbsSite.ShowLoadingWait = False
        Me.wbsSite.ShowNavigateErrorPage = True
        Me.wbsSite.ShowProgress = True
        Me.wbsSite.Size = New System.Drawing.Size(868, 587)
        Me.wbsSite.SN = ""
        Me.wbsSite.TabIndex = 0
        Me.wbsSite.UserInfo = "False"
        Me.wbsSite.UserInfoEncode = ""
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(845, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 24
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Nothing
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btn_normal
        '
        Me.btn_normal.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_normal.BackColor = System.Drawing.Color.Transparent
        Me.btn_normal.BindingForm = Nothing
        Me.btn_normal.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_normal.Location = New System.Drawing.Point(815, 2)
        Me.btn_normal.Name = "btn_normal"
        Me.btn_normal.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_normal.Selectable = True
        Me.btn_normal.Size = New System.Drawing.Size(24, 24)
        Me.btn_normal.TabIndex = 23
        Me.btn_normal.tbAdriftIconWhenHover = False
        Me.btn_normal.tbAutoSize = False
        Me.btn_normal.tbAutoSizeEx = False
        Me.btn_normal.tbBackgroundImage = Nothing
        Me.btn_normal.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btn_normal.tbBadgeNumber = 0
        Me.btn_normal.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_normal.tbEndEllipsis = False
        Me.btn_normal.tbIconHoldPlace = True
        Me.btn_normal.tbIconImage = Nothing
        Me.btn_normal.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_normal.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_normal.tbIconMore = False
        Me.btn_normal.tbIconMouseDown = Nothing
        Me.btn_normal.tbIconMouseHover = Nothing
        Me.btn_normal.tbIconMouseLeave = Nothing
        Me.btn_normal.tbIconPlaceText = 2
        Me.btn_normal.tbIconReadOnly = Nothing
        Me.btn_normal.tbImageMouseDown = Nothing
        Me.btn_normal.tbImageMouseHover = Nothing
        Me.btn_normal.tbImageMouseLeave = Nothing
        Me.btn_normal.tbReadOnly = False
        Me.btn_normal.tbReadOnlyText = False
        Me.btn_normal.tbShadow = False
        Me.btn_normal.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_normal.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_normal.tbShowDot = False
        Me.btn_normal.tbShowNew = False
        Me.btn_normal.tbShowToolTipOnButton = False
        Me.btn_normal.tbSplit = "3,3,3,3"
        Me.btn_normal.tbText = ""
        Me.btn_normal.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_normal.tbTextColor = System.Drawing.Color.White
        Me.btn_normal.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_normal.tbTextColorDown = System.Drawing.Color.White
        Me.btn_normal.tbTextColorHover = System.Drawing.Color.White
        Me.btn_normal.tbTextMouseDownPlace = 0
        Me.btn_normal.tbToolTip = ""
        Me.btn_normal.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_normal.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_normal.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_normal.VisibleEx = True
        '
        'btn_minimize
        '
        Me.btn_minimize.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_minimize.BackColor = System.Drawing.Color.Transparent
        Me.btn_minimize.BindingForm = Nothing
        Me.btn_minimize.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_minimize.Location = New System.Drawing.Point(785, 2)
        Me.btn_minimize.Name = "btn_minimize"
        Me.btn_minimize.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_minimize.Selectable = True
        Me.btn_minimize.Size = New System.Drawing.Size(24, 24)
        Me.btn_minimize.TabIndex = 22
        Me.btn_minimize.tbAdriftIconWhenHover = False
        Me.btn_minimize.tbAutoSize = False
        Me.btn_minimize.tbAutoSizeEx = False
        Me.btn_minimize.tbBackgroundImage = Nothing
        Me.btn_minimize.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btn_minimize.tbBadgeNumber = 0
        Me.btn_minimize.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_minimize.tbEndEllipsis = False
        Me.btn_minimize.tbIconHoldPlace = True
        Me.btn_minimize.tbIconImage = Nothing
        Me.btn_minimize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_minimize.tbIconMore = False
        Me.btn_minimize.tbIconMouseDown = Nothing
        Me.btn_minimize.tbIconMouseHover = Nothing
        Me.btn_minimize.tbIconMouseLeave = Nothing
        Me.btn_minimize.tbIconPlaceText = 2
        Me.btn_minimize.tbIconReadOnly = Nothing
        Me.btn_minimize.tbImageMouseDown = Nothing
        Me.btn_minimize.tbImageMouseHover = Nothing
        Me.btn_minimize.tbImageMouseLeave = Nothing
        Me.btn_minimize.tbReadOnly = False
        Me.btn_minimize.tbReadOnlyText = False
        Me.btn_minimize.tbShadow = False
        Me.btn_minimize.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_minimize.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_minimize.tbShowDot = False
        Me.btn_minimize.tbShowNew = False
        Me.btn_minimize.tbShowToolTipOnButton = False
        Me.btn_minimize.tbSplit = "3,3,3,3"
        Me.btn_minimize.tbText = ""
        Me.btn_minimize.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.tbTextColor = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorDown = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorHover = System.Drawing.Color.White
        Me.btn_minimize.tbTextMouseDownPlace = 0
        Me.btn_minimize.tbToolTip = ""
        Me.btn_minimize.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_minimize.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_minimize.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.VisibleEx = True
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(95, 8)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(272, 122)
        Me.pnlLoading.TabIndex = 25
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(122, 39)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(239, 45)
        Me.lblLoading.TabIndex = 10
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_24
        Me.pbLoading.Location = New System.Drawing.Point(90, 49)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(24, 24)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 0
        Me.pbLoading.TabStop = False
        '
        'frmPersonalCenter
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(870, 620)
        Me.Controls.Add(Me.pnlLoading)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.btn_normal)
        Me.Controls.Add(Me.btn_minimize)
        Me.Controls.Add(Me.pnlContainer)
        Me.Name = "frmPersonalCenter"
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "个人中心"
        Me.pnlContainer.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents wbsSite As tbWebBrowserEx
    Protected WithEvents btn_close As tbButton
    Protected WithEvents btn_normal As tbButton
    Protected WithEvents btn_minimize As tbButton
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
End Class
