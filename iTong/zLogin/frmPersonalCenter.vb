﻿Public Class frmPersonalCenter

    Private mApplication As IApplication = Nothing
    Protected mUrl As String = WebUrl.PageRepairPayment   'http://v3.tongbu.com/share/user.html#!payment
    Private mPluginLogin As PluginLogin
    Public mDeivce As iPhoneDevice = Nothing

#Region "----初始化----"

    Public Sub New(ByVal application As IApplication, ByVal device As IDevice)
        MyBase.New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.Language = application.Language
        Me.mApplication = application
        Me.mDeivce = device
        Me.FunctionMappingKey = FunctionKey.None
        Me.FilletRadius = 5
        If Me.mDeivce IsNot Nothing Then
            Me.Name = Me.mDeivce.Identifier & "frmPersonalCenter"
        End If
        Me.StartPosition = FormStartPosition.CenterParent
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.Icon = My.Resources.iTong
        Me.tbGuiBackground = My.Resources.Resources.frm_bg_state
        Me.tbSplit = "10,33,10,33"
        Me.Size = New Size(870, 620)
        Me.btn_normal.Visible = False

        '登陆接口
        Me.mPluginLogin = PluginLogin.Instance()
        RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        AddHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        RemoveHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent
        AddHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent

        'web操作
        Me.wbsSite.UserInfo = Me.mPluginLogin.UserInfo      '写入登陆信息到浏览器
        Me.wbsSite.UserInfoEncode = Me.mPluginLogin.UserInfoEncode      '写入登陆信息到浏览器
        Me.wbsSite.Name = Me.mUrl
        Me.wbsSite.Navigate(Me.mUrl)
        Me.wbsSite.ScriptErrorsSuppressed = True

        If Me.Parent Is Nothing Then
            Me.btn_close.tbBackgroundImage = My.Resources.btn_close
            Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
            Me.btn_normal.tbBackgroundImage = My.Resources.btn_max
            Me.btn_normal.tbBackgroundImageState = ImageState.ThreeState
            Me.btn_minimize.tbBackgroundImage = My.Resources.btn_min
            Me.btn_minimize.tbBackgroundImageState = ImageState.ThreeState
            Me.CanbeMove = True
            Me.ShowInTaskbar = True
        Else
            Me.CanbeMove = False
        End If
        Me.CanResize = False
        Me.pnlLoading.Size = Me.pnlContainer.Size
        Me.pnlLoading.Location = Me.pnlContainer.Location
        Me.pnlLoading.BringToFront()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Login.Label.PersonalCenter")           '"个人中心"
        Me.lblLoading.Text = Me.Language.GetString("File.Label.Loading") 'loading
    End Sub

    Private Sub frmPersonalCenter_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Resize
        Try
            If Me.btn_normal IsNot Nothing Then
                If Me.WindowState = FormWindowState.Maximized Then
                    If Me.Language IsNot Nothing Then
                        Me.btn_normal.tbToolTip = Me.Language.GetString("Common.Button.Restore") '"还原"
                    End If

                    Me.btn_normal.tbBackgroundImage = My.Resources.btn_restore
                    Me.btn_normal.tbBackgroundImageState = ImageState.ThreeState

                ElseIf Me.WindowState = FormWindowState.Normal Then
                    If Me.Language IsNot Nothing Then
                        Me.btn_normal.tbToolTip = Me.Language.GetString("Common.Button.Maximize") '"最大化"
                    End If
                    Me.btn_normal.tbBackgroundImage = My.Resources.btn_max
                    Me.btn_normal.tbBackgroundImageState = ImageState.ThreeState
                End If
            End If

        Catch ex As NullReferenceException
            Debug.Write(ex)
        End Try
    End Sub

    Public Sub RefreshUrl()
        Me.wbsSite.Refresh()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Try
            RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
            RemoveHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent
        Catch ex As Exception
        End Try
    End Sub

#End Region

    Private Delegate Sub PluginLogin_LoginEventHandler(ByVal sender As Object, ByVal e As EventArgs)
    Private Sub PluginLogin_LoginEvent(ByVal sender As Object, ByVal e As EventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New PluginLogin_LoginEventHandler(AddressOf PluginLogin_LoginEvent), sender, e)
        Else
            Try
                If sender Is Nothing OrElse Me.IsDisposed Then
                    Return
                End If
                Dim loginObj As PluginLogin = sender
                Me.wbsSite.UserInfo = loginObj.UserInfo
                Me.wbsSite.UserInfoEncode = loginObj.UserInfoEncode      '写入登陆信息到浏览器
                '重新登陆后刷新
                Me.RefreshUrl()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "Site_PluginLogin_LoginEvent")
            End Try
        End If
    End Sub

    Private Sub PluginLogin_LogoutEvent(ByVal sender As Object, ByVal e As EventArgs)
        Me.OnLoginOut(sender, e)
    End Sub

    Protected Overridable Sub OnLoginOut(ByVal sender As Object, ByVal e As EventArgs)
        Me.Close()
    End Sub

    Private Sub wbsSite_NewMessage(ByVal sender As tbWebBrowser, ByVal message As System.String) Handles wbsSite.NewMessage
        Try
            If Me.mDeivce Is Nothing OrElse Me.mDeivce.IsConnected = False Then
                Me.mDeivce = ProcForm.Instance.GetCurrentDevice(DeviceType.iOS)
            End If
            WebSiteHelper.Instance.WebBrowserNewMessage(Me.mDeivce, sender, message, Nothing)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmPersonalCenter_wbsSite_NewMessage")
        End Try
    End Sub

    Private Sub wbsSite_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs) Handles wbsSite.Navigating
        Try
            If Me.mDeivce Is Nothing OrElse Me.mDeivce.IsConnected = False Then
                Me.mDeivce = ProcForm.Instance.GetCurrentDevice(DeviceType.iOS)
            End If
            WebSiteHelper.Instance.WebBrowserNavigating(Me.mApplication, Me.mDeivce, sender, e)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmPersonalCenter_wbsSite_Navigating")
        End Try
    End Sub

    Private Sub wbsSite_NavigateError(ByVal sender As System.Object, ByVal e As WebBrowserNavigateErrorEventArgs) Handles wbsSite.NavigateError
        WebSiteHelper.Instance.WebBrowserNavigateError(Me.mApplication, sender, e)
        Me.pnlLoading.SendToBack()
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_state
        End If
    End Sub

    Private Sub wbsSite_Navigated(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatedEventArgs) Handles wbsSite.Navigated
        Me.pnlLoading.SendToBack()
    End Sub

    Protected Overrides Sub OnSizeChanged(ByVal e As System.EventArgs)
        MyBase.OnSizeChanged(e)
        Me.btn_minimize.Location = New Point(Me.btn_close.Left - Me.btn_minimize.Width - 2, Me.btn_minimize.Top)
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)
        Me.btn_minimize.Location = New Point(Me.btn_close.Left - Me.btn_minimize.Width - 2, Me.btn_minimize.Top)
    End Sub

End Class