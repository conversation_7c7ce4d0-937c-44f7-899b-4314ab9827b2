﻿Public Class frmLogin

    Private mApplication As IApplication
    Private mIsShowPwd As Boolean = False
    Private mPluginLogin As PluginLogin

#Region "--- 初始化 ---"

    Public Sub New(ByVal app As IApplication)
        Me.InitializeComponent()

        Me.Language = app.Language
        Me.CanResize = False
        Me.mApplication = app

        Me.Icon = My.Resources.iTong
        Me.tbGuiBackground = My.Resources.frm_bg_login
        Me.TopMost = True
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lblId.Text = Me.Language.GetString("Login.Label.TongbuAccount")          '"同步账号"
        Me.lblPwd.Text = Me.Language.GetString("Login.Label.TongbuPwd")             '"密      码"
        Me.btnGetId.Text = Me.Language.GetString("Login.Label.GetAccount")          '"注册账号"
        Me.btnGetPwd.Text = Me.Language.GetString("Login.Label.ForgetPwd")          '"忘记密码"
        Me.btnLogin.Text = Me.Language.GetString("Common.Login")            '"登录"
        Me.Text = Me.Language.GetString("Common.Login")            '"登录"
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.mPluginLogin = PluginLogin.Instance()
        RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        AddHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)
        Me.txtId.Focus()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Try
            RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub txtPwd_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPwd.KeyUp
        If Me.txtPwd.Text.Length <= 0 OrElse Me.mIsShowPwd Then
            Me.txtPwd.PasswordChar = ""
        Else
            Me.txtPwd.PasswordChar = "*"
        End If
    End Sub

    Private Sub pbShowPwd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pbShowPwd.Click
        If Me.mIsShowPwd Then
            Me.pbShowPwd.Image = My.Resources.btn_closedeye
            Me.mIsShowPwd = False
            If String.IsNullOrEmpty(Me.txtPwd.Text) = False Then
                Me.txtPwd.PasswordChar = "*"
            End If

        Else
            Me.pbShowPwd.Image = My.Resources.btn_openeye
            Me.mIsShowPwd = True
            Me.txtPwd.PasswordChar = ""
        End If
    End Sub

    Private Sub btnLogin_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnLogin.Click
        Me.Login()
    End Sub

    Private Sub txtPwd_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPwd.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.Login()
        End If
    End Sub

    Private Sub lblMessage_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblMessage.TextChanged
        If Me.lblMessage.Text.Length > 0 Then
            Me.picCheck.Visible = True
            Me.Height = 290
        Else
            Me.picCheck.Visible = False
        End If
    End Sub

    Private Sub txtId_Enter(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtId.Enter, txtPwd.Enter
        If sender IsNot Nothing AndAlso TypeOf sender Is tbTextBox Then
            CType(sender, tbTextBase).BringToFront()
        End If
    End Sub

    Private Sub PluginLogin_LoginEvent(ByVal sender As Object, ByVal e As EventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(AddressOf PluginLogin_LoginEvent), sender, e)
        Else
            Try
                If sender Is Nothing OrElse Me.IsDisposed = True Then
                    Return
                End If

                Dim loginObj As PluginLogin = sender
                If loginObj.IsLogin Then
                    Me.Close()
                    Return
                End If

                If loginObj.Des.Length > 0 Then
                    Me.lblMessage.Text = loginObj.Des
                    Return
                End If

                Select Case loginObj.Code
                    Case "-1"
                        Me.lblMessage.Text = Me.Language.GetString("Login.Message.AccountNotEmpty")             '"账号不能为空"
                    Case "-2"
                        Me.lblMessage.Text = Me.Language.GetString("Login.Message.AccountNotExist")             '"账号不存在"
                    Case "-3"
                        Me.lblMessage.Text = Me.Language.GetString("Login.Message.AccountDisable")              '"账号被禁用"
                    Case "-4"
                        Me.lblMessage.Text = Me.Language.GetString("Backup.Message.PwdIsError")                 '"密码错误"
                    Case "-5"
                        Me.lblMessage.Text = Me.Language.GetString("Login.Message.AccountIllegalCharacters")    '"账号存在非法字符"
                    Case "-6"
                        Me.lblMessage.Text = Me.Language.GetString("Login.Message.AccountLocked")               '"登录密码错误，账号被锁定1小时"
                    Case "-11"
                        Me.lblMessage.Text = Me.Language.GetString("App.Lable.Timeout")                         '"网络超时。"
                    Case Else
                        Me.lblMessage.Text = String.Format(String.Format("{0} ErrorCode:{1}", Me.Language.GetString("Login.Message.UnknowError"), loginObj.Code))               '"未知错误"
                End Select
                Me.lblMessage.Refresh()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmLogin_PluginLogin_LoginEvent")
            End Try
        End If
    End Sub

#End Region

    Private Sub Login()
        If Not Me.CheckInput() Then
            Return
        End If

        Me.mPluginLogin.Account = Common.ChangeStringFullMode2HalfMode(Me.txtId.Text)
        Me.mPluginLogin.PwdMd5 = Common.ChangeStringFullMode2HalfMode(Me.txtPwd.Text)
        Me.mPluginLogin.Login()
    End Sub

    Private Function CheckInput() As Boolean
        Dim isSucceed As Boolean = False

        If Not Common.NetworkIsAvailable Then
            'Me.lblMessage.ForeColor = Color.Red
            Me.lblMessage.Text = Me.Language.GetString("Welcome.Label.NetDisable")
            Return isSucceed
        End If

        If Me.txtId.Text.Trim.Length = 0 Then
            Me.txtId.Focus()
            'Me.lblMessage.ForeColor = Color.Red
            Me.lblMessage.Text = Me.Language.GetString("App.Lable.EnterAccount") '"请输入账号"
            Return isSucceed
        End If

        If Me.txtPwd.Text.Trim.Length = 0 Then
            Me.txtPwd.Focus()
            'Me.lblMessage.ForeColor = Color.Red
            Me.lblMessage.Text = Me.Language.GetString("App.Lable.EnterPassword") '"请输入密码"
            Return isSucceed
        End If

        isSucceed = True
        Return isSucceed
    End Function

    Public Shared Sub ShowLogin(ByVal owner As Form, Optional ByVal blnChangeAccount As Boolean = False)
        If PluginLogin.Instance().CheckLogin AndAlso blnChangeAccount = False Then
            Return
        End If
        Dim frm As frmLogin = Nothing

        If owner Is Nothing Then
            owner = Form.ActiveForm()
        End If

        For Each Item As Form In My.Application.OpenForms()
            If TypeOf Item Is frmLogin Then
                frm = Item
            End If

            If owner Is Nothing AndAlso TypeOf Item Is MainForm Then
                owner = Item
            End If
            If frm IsNot Nothing AndAlso owner IsNot Nothing Then
                Exit For
            End If
        Next

        If frm IsNot Nothing Then
            frm.Activate()
            frm.BringToFront()
        Else
            frm = New frmLogin(ProcForm.Instance().GetMainForm())
            frm.Show(owner)
        End If
    End Sub

    Private Sub btnGetPwd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGetPwd.Click
        Common.OpenExplorer(WebUrl.PageTongbuAccountForget)
    End Sub

    Private Sub btnGetId_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGetId.Click
        Dim strURL As String = WebUrl.PageTongbuAccount

#If IS_ITONG_ZJ Then
        strURL = WebUrl.PageTongbuZJAccount
        If AgentHelper.IsAgent Then
            strURL = AgentHelper.GetAgentRegister()
        End If
#End If

        Common.OpenExplorer(strURL)
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.tbGuiBackground = My.Resources.frm_bg_login
    End Sub

End Class