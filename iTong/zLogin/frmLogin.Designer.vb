﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmLogin
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmLogin))
        Me.btn_close = New iTong.Components.tbButton()
        Me.pbShowPwd = New System.Windows.Forms.PictureBox()
        Me.txtPwd = New iTong.Components.tbTextBox()
        Me.btnLogin = New iTong.Components.tbButton()
        Me.txtId = New iTong.Components.tbTextBox()
        Me.lblId = New iTong.Components.tbLabel()
        Me.lblPwd = New iTong.Components.tbLabel()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.picCheck = New System.Windows.Forms.PictureBox()
        Me.btnGetId = New iTong.Components.tbButton()
        Me.btnGetPwd = New iTong.Components.tbButton()
        CType(Me.pbShowPwd, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picCheck, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(384, 3)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 24
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'pbShowPwd
        '
        Me.pbShowPwd.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.pbShowPwd.BackColor = System.Drawing.Color.Transparent
        Me.pbShowPwd.Cursor = System.Windows.Forms.Cursors.Hand
        Me.pbShowPwd.Image = Global.iTong.My.Resources.Resources.btn_closedeye
        Me.pbShowPwd.Location = New System.Drawing.Point(322, 231)
        Me.pbShowPwd.Margin = New System.Windows.Forms.Padding(0)
        Me.pbShowPwd.Name = "pbShowPwd"
        Me.pbShowPwd.Size = New System.Drawing.Size(17, 16)
        Me.pbShowPwd.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.pbShowPwd.TabIndex = 67
        Me.pbShowPwd.TabStop = False
        Me.pbShowPwd.Visible = False
        '
        'txtPwd
        '
        Me.txtPwd.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.txtPwd.BackColor = System.Drawing.Color.White
        Me.txtPwd.BorderColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.txtPwd.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtPwd.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.txtPwd.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtPwd.ForeColor = System.Drawing.Color.Black
        Me.txtPwd.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtPwd.Location = New System.Drawing.Point(108, 170)
        Me.txtPwd.Margin = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtPwd.MaxLength = 50
        Me.txtPwd.Name = "txtPwd"
        Me.txtPwd.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtPwd.Size = New System.Drawing.Size(195, 30)
        Me.txtPwd.TabIndex = 65
        Me.txtPwd.Tag = Nothing
        Me.txtPwd.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtPwd.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtPwd.tbSelMark = True
        Me.txtPwd.tbTextBind = ""
        Me.txtPwd.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtPwd.TextImeMode = System.Windows.Forms.ImeMode.Disable
        Me.txtPwd.TextPadding = New System.Windows.Forms.Padding(10, 3, 3, 3)
        Me.txtPwd.TextTip = ""
        '
        'btnLogin
        '
        Me.btnLogin.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.btnLogin.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnLogin.BindingForm = Nothing
        Me.btnLogin.Location = New System.Drawing.Point(108, 221)
        Me.btnLogin.Margin = New System.Windows.Forms.Padding(0)
        Me.btnLogin.Name = "btnLogin"
        Me.btnLogin.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnLogin.Selectable = True
        Me.btnLogin.Size = New System.Drawing.Size(195, 35)
        Me.btnLogin.TabIndex = 66
        Me.btnLogin.tbAdriftIconWhenHover = False
        Me.btnLogin.tbAutoSize = False
        Me.btnLogin.tbAutoSizeEx = False
        Me.btnLogin.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnLogin.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnLogin.tbBadgeNumber = 0
        Me.btnLogin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnLogin.tbEndEllipsis = False
        Me.btnLogin.tbIconHoldPlace = True
        Me.btnLogin.tbIconImage = Nothing
        Me.btnLogin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnLogin.tbIconMore = False
        Me.btnLogin.tbIconMouseDown = Nothing
        Me.btnLogin.tbIconMouseHover = Nothing
        Me.btnLogin.tbIconMouseLeave = Nothing
        Me.btnLogin.tbIconPlaceText = 0
        Me.btnLogin.tbIconReadOnly = Nothing
        Me.btnLogin.tbImageMouseDown = Nothing
        Me.btnLogin.tbImageMouseHover = Nothing
        Me.btnLogin.tbImageMouseLeave = Nothing
        Me.btnLogin.tbProgressValue = 50
        Me.btnLogin.tbReadOnly = False
        Me.btnLogin.tbReadOnlyText = False
        Me.btnLogin.tbShadow = False
        Me.btnLogin.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnLogin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnLogin.tbShowDot = False
        Me.btnLogin.tbShowMoreIconImg = CType(resources.GetObject("btnLogin.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnLogin.tbShowNew = False
        Me.btnLogin.tbShowProgress = False
        Me.btnLogin.tbShowTip = True
        Me.btnLogin.tbShowToolTipOnButton = False
        Me.btnLogin.tbSplit = "10,10,10,10"
        Me.btnLogin.tbText = "登录"
        Me.btnLogin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.tbTextColor = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDisable = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDown = System.Drawing.Color.White
        Me.btnLogin.tbTextColorHover = System.Drawing.Color.White
        Me.btnLogin.tbTextMouseDownPlace = 0
        Me.btnLogin.tbToolTip = ""
        Me.btnLogin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnLogin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnLogin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.VisibleEx = True
        '
        'txtId
        '
        Me.txtId.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.txtId.BackColor = System.Drawing.Color.White
        Me.txtId.BorderColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.txtId.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtId.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.txtId.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtId.ForeColor = System.Drawing.Color.Black
        Me.txtId.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtId.Location = New System.Drawing.Point(108, 141)
        Me.txtId.Margin = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtId.MaxLength = 50
        Me.txtId.Name = "txtId"
        Me.txtId.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtId.Size = New System.Drawing.Size(195, 30)
        Me.txtId.TabIndex = 64
        Me.txtId.Tag = Nothing
        Me.txtId.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtId.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtId.tbSelMark = True
        Me.txtId.tbTextBind = ""
        Me.txtId.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtId.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtId.TextPadding = New System.Windows.Forms.Padding(10, 3, 3, 3)
        Me.txtId.TextTip = ""
        '
        'lblId
        '
        Me.lblId.Location = New System.Drawing.Point(6, 147)
        Me.lblId.Name = "lblId"
        Me.lblId.Size = New System.Drawing.Size(97, 18)
        Me.lblId.TabIndex = 72
        Me.lblId.tbAdriftWhenHover = False
        Me.lblId.tbAutoEllipsis = False
        Me.lblId.tbAutoSize = False
        Me.lblId.tbHideImage = False
        Me.lblId.tbIconImage = Nothing
        Me.lblId.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblId.tbIconPlaceText = 5
        Me.lblId.tbShadow = False
        Me.lblId.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblId.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblId.tbShowScrolling = False
        Me.lblId.Text = "账号"
        Me.lblId.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblPwd
        '
        Me.lblPwd.Location = New System.Drawing.Point(6, 176)
        Me.lblPwd.Name = "lblPwd"
        Me.lblPwd.Size = New System.Drawing.Size(97, 18)
        Me.lblPwd.TabIndex = 73
        Me.lblPwd.tbAdriftWhenHover = False
        Me.lblPwd.tbAutoEllipsis = False
        Me.lblPwd.tbAutoSize = False
        Me.lblPwd.tbHideImage = False
        Me.lblPwd.tbIconImage = Nothing
        Me.lblPwd.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblPwd.tbIconPlaceText = 5
        Me.lblPwd.tbShadow = False
        Me.lblPwd.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblPwd.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblPwd.tbShowScrolling = False
        Me.lblPwd.Text = "密码"
        Me.lblPwd.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblMessage.AutoEllipsis = True
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(129, 206)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(222, 20)
        Me.lblMessage.TabIndex = 74
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = True
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = True
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picCheck
        '
        Me.picCheck.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.picCheck.Image = Global.iTong.My.Resources.Resources.dgv_filenoexist
        Me.picCheck.Location = New System.Drawing.Point(113, 210)
        Me.picCheck.Name = "picCheck"
        Me.picCheck.Size = New System.Drawing.Size(12, 12)
        Me.picCheck.TabIndex = 75
        Me.picCheck.TabStop = False
        Me.picCheck.Visible = False
        '
        'btnGetId
        '
        Me.btnGetId.BackColor = System.Drawing.Color.Transparent
        Me.btnGetId.BindingForm = Nothing
        Me.btnGetId.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnGetId.Location = New System.Drawing.Point(306, 145)
        Me.btnGetId.Name = "btnGetId"
        Me.btnGetId.Padding = New System.Windows.Forms.Padding(0, 2, 5, 2)
        Me.btnGetId.Selectable = True
        Me.btnGetId.Size = New System.Drawing.Size(102, 23)
        Me.btnGetId.TabIndex = 76
        Me.btnGetId.tbAdriftIconWhenHover = False
        Me.btnGetId.tbAutoSize = False
        Me.btnGetId.tbAutoSizeEx = False
        Me.btnGetId.tbBackgroundImage = Nothing
        Me.btnGetId.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnGetId.tbBadgeNumber = 0
        Me.btnGetId.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGetId.tbEndEllipsis = False
        Me.btnGetId.tbIconHoldPlace = True
        Me.btnGetId.tbIconImage = Nothing
        Me.btnGetId.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetId.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGetId.tbIconMore = False
        Me.btnGetId.tbIconMouseDown = Nothing
        Me.btnGetId.tbIconMouseHover = Nothing
        Me.btnGetId.tbIconMouseLeave = Nothing
        Me.btnGetId.tbIconPlaceText = 2
        Me.btnGetId.tbIconReadOnly = Nothing
        Me.btnGetId.tbImageMouseDown = Nothing
        Me.btnGetId.tbImageMouseHover = Nothing
        Me.btnGetId.tbImageMouseLeave = Nothing
        Me.btnGetId.tbProgressValue = 50
        Me.btnGetId.tbReadOnly = False
        Me.btnGetId.tbReadOnlyText = False
        Me.btnGetId.tbShadow = False
        Me.btnGetId.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGetId.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGetId.tbShowDot = False
        Me.btnGetId.tbShowMoreIconImg = CType(resources.GetObject("btnGetId.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGetId.tbShowNew = False
        Me.btnGetId.tbShowProgress = False
        Me.btnGetId.tbShowTip = True
        Me.btnGetId.tbShowToolTipOnButton = False
        Me.btnGetId.tbSplit = "3,3,3,3"
        Me.btnGetId.tbText = "注册账号"
        Me.btnGetId.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnGetId.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(39, Byte), Integer), CType(CType(134, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.btnGetId.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(39, Byte), Integer), CType(CType(134, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.btnGetId.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(39, Byte), Integer), CType(CType(134, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.btnGetId.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(98, Byte), Integer), CType(CType(180, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnGetId.tbTextMouseDownPlace = 0
        Me.btnGetId.tbToolTip = ""
        Me.btnGetId.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGetId.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGetId.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnGetId.VisibleEx = True
        '
        'btnGetPwd
        '
        Me.btnGetPwd.BackColor = System.Drawing.Color.Transparent
        Me.btnGetPwd.BindingForm = Nothing
        Me.btnGetPwd.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnGetPwd.Location = New System.Drawing.Point(306, 174)
        Me.btnGetPwd.Name = "btnGetPwd"
        Me.btnGetPwd.Padding = New System.Windows.Forms.Padding(0, 2, 5, 2)
        Me.btnGetPwd.Selectable = True
        Me.btnGetPwd.Size = New System.Drawing.Size(102, 23)
        Me.btnGetPwd.TabIndex = 77
        Me.btnGetPwd.tbAdriftIconWhenHover = False
        Me.btnGetPwd.tbAutoSize = False
        Me.btnGetPwd.tbAutoSizeEx = False
        Me.btnGetPwd.tbBackgroundImage = Nothing
        Me.btnGetPwd.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnGetPwd.tbBadgeNumber = 0
        Me.btnGetPwd.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGetPwd.tbEndEllipsis = False
        Me.btnGetPwd.tbIconHoldPlace = True
        Me.btnGetPwd.tbIconImage = Nothing
        Me.btnGetPwd.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetPwd.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGetPwd.tbIconMore = False
        Me.btnGetPwd.tbIconMouseDown = Nothing
        Me.btnGetPwd.tbIconMouseHover = Nothing
        Me.btnGetPwd.tbIconMouseLeave = Nothing
        Me.btnGetPwd.tbIconPlaceText = 2
        Me.btnGetPwd.tbIconReadOnly = Nothing
        Me.btnGetPwd.tbImageMouseDown = Nothing
        Me.btnGetPwd.tbImageMouseHover = Nothing
        Me.btnGetPwd.tbImageMouseLeave = Nothing
        Me.btnGetPwd.tbProgressValue = 50
        Me.btnGetPwd.tbReadOnly = False
        Me.btnGetPwd.tbReadOnlyText = False
        Me.btnGetPwd.tbShadow = False
        Me.btnGetPwd.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGetPwd.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGetPwd.tbShowDot = False
        Me.btnGetPwd.tbShowMoreIconImg = CType(resources.GetObject("btnGetPwd.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGetPwd.tbShowNew = False
        Me.btnGetPwd.tbShowProgress = False
        Me.btnGetPwd.tbShowTip = True
        Me.btnGetPwd.tbShowToolTipOnButton = False
        Me.btnGetPwd.tbSplit = "3,3,3,3"
        Me.btnGetPwd.tbText = "忘记密码"
        Me.btnGetPwd.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnGetPwd.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(39, Byte), Integer), CType(CType(134, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.btnGetPwd.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(39, Byte), Integer), CType(CType(134, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.btnGetPwd.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(39, Byte), Integer), CType(CType(134, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.btnGetPwd.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(98, Byte), Integer), CType(CType(180, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnGetPwd.tbTextMouseDownPlace = 0
        Me.btnGetPwd.tbToolTip = ""
        Me.btnGetPwd.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGetPwd.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGetPwd.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnGetPwd.VisibleEx = True
        '
        'frmLogin
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(411, 279)
        Me.Controls.Add(Me.btnGetPwd)
        Me.Controls.Add(Me.btnGetId)
        Me.Controls.Add(Me.lblPwd)
        Me.Controls.Add(Me.lblId)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.pbShowPwd)
        Me.Controls.Add(Me.txtId)
        Me.Controls.Add(Me.txtPwd)
        Me.Controls.Add(Me.btnLogin)
        Me.Controls.Add(Me.picCheck)
        Me.Controls.Add(Me.lblMessage)
        Me.MinimumSize = New System.Drawing.Size(110, 127)
        Me.Name = "frmLogin"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbSplit = "55,124,55,3"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.Text = "账号登陆"
        CType(Me.pbShowPwd, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picCheck, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Protected WithEvents btn_close As tbButton
    Friend WithEvents pbShowPwd As System.Windows.Forms.PictureBox
    Friend WithEvents txtPwd As tbTextBox
    Friend WithEvents btnLogin As tbButton
    Friend WithEvents txtId As tbTextBox
    Friend WithEvents lblId As tbLabel
    Friend WithEvents lblPwd As tbLabel
    Friend WithEvents lblMessage As tbLabel
    Friend WithEvents picCheck As System.Windows.Forms.PictureBox
    Friend WithEvents btnGetId As tbButton
    Friend WithEvents btnGetPwd As tbButton
End Class
