<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="iTong.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  <system.net>
    <settings>
      <httpWebRequest useUnsafeHeaderParsing="true "/>
      <servicePointManager expect100Continue="false "/>
    </settings>
    <connectionManagement>
      <add address="*" maxconnection="1000"/>
    </connectionManagement>
  </system.net>
  <applicationSettings>
    <iTong.My.MySettings>
      <setting name="LangName" serializeAs="String">
        <value>zh-CN</value>
      </setting>
    </iTong.My.MySettings>
  </applicationSettings>  
</configuration>
