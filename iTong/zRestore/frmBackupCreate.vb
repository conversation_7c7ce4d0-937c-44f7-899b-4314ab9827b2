﻿Imports System.Threading
Imports iTong.CoreModule

Public Class frmBackupCreate

    Private mDgvBackupList As New tbDataGridViewEx
    Private mPlugInMobileDeviceManager As MobileDeviceManager
    Private mFrmBackupList As frmBackupList
    'Private mSelectedSN As String = ""
    Private mBackupOrRestore As BackupRestore
    Private mstrBackPath As String = ""
    Private mintBackProgress As Integer = 0
    Private mblnShowDiskMessage As Boolean = False
    Private mErrorMessage As String = ""
    Private mBackupHelper As BackupHelper

    Public mAutoClose As Boolean = False
    Private mBackupSucceed As Boolean = False

    Private mTDLoadCheckEncrypt As Thread = Nothing

    Private mBackupPath As String = String.Empty

    Public ReadOnly Property BackupPath() As String
        Get
            Return Me.mBackupPath
        End Get
    End Property

#Region "--- 初始化 ---"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Public Sub New(ByVal application As IApplication, ByVal iPhone As iPhoneDevice, ByVal backupOrRestore As BackupRestore, ByVal frmList As frmBackupList)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.mDevice = iPhone
        Me.Icon = My.Resources.iTong
        Me.Size = New Size(640, 462)
        Me.FilletRadius = 5
        Me.tbGuiBackground = My.Resources.frm_bg_sub
        Me.tbSplit = "8,55,8,55"

        Me.tbAutoSetFormSize = False
        Me.CanResize = False

        Me.mFrmBackupList = frmList
        Me.mBackupOrRestore = backupOrRestore

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.InitGridView()
        Me.InitBackupHelper()

        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False

        If Me.mBackupOrRestore = BackupRestore.Backup Then
            Me.pnlContainer.Enabled = True
            Me.txtiBackupFolder.Enabled = False
            Me.btnBacbupFolder.Enabled = False

            Dim strBackupFloder As String = IniSetting.GetiTunesBackupFloder()
            If strBackupFloder.Length > 0 AndAlso Directory.Exists(strBackupFloder) Then
                Me.txtiBackupFolder.Text = strBackupFloder
            Else
                Me.txtiBackupFolder.Text = BackupHelper.BackupFolderEx
            End If

        Else
            Me.rdbIncrem.Visible = False
            Me.rdbNew.Visible = False
            Me.txtiBackupFolder.Visible = False
            Me.btnBacbupFolder.Visible = False
            Me.pnlContainer.Top = Me.rdbIncrem.Top + 8
            Me.Size = New Size(Me.Width, 380)
            Me.pnlCount.Visible = False
            Me.pnlContainer.Dock = DockStyle.Fill
            Me.pnlContainerEx.Size = New Size(Me.pnlContainerEx.Size.Width, Me.pnlContainerEx.Size.Height) ' - Me.pnlCount.Size.Height
        End If
        Me.CanResize = False

        Dim listBackup As New List(Of BackupInfo)

        listBackup = BackupHelper.LstFullBackupList

        For Each Item As BackupInfo In listBackup
            If Me.mBackupOrRestore = BackupRestore.Backup Then
                If Me.mDevice.ProductVersion.Length > 0 AndAlso Item.ProductVersion.Length > 0 AndAlso Me.mDevice.SerialNumber = Item.SerialNumber Then
                    Me.AddRow(Item)
                End If
            Else
                If Me.mDevice.ProductVersion.Length > 0 AndAlso Item.ProductVersion.Length > 0 AndAlso Val(Me.mDevice.ProductVersion.Substring(0, 1)) >= Val(Item.ProductVersion.Substring(0, 1)) Then
                    If CheckCanRestore(Me.mDevice, Item) Then
                        Me.AddRow(Item)
                    End If
                End If
            End If
        Next


        If Me.mDgvBackupList.RowCount > 0 Then
            Me.rdbIncrem.Checked = True
        Else
            Me.rdbIncrem.Enabled = False
            Me.rdbNew.Checked = True
        End If

        If Me.mBackupOrRestore = BackupRestore.Restore AndAlso Me.mDgvBackupList.RowCount <= 0 Then
            Me.btnOK.Enabled = False
            Me.llblDeletePwd.Enabled = False
        End If

        CheckEncrypt()

    End Sub

    Private Sub InitBackupHelper()
        Me.mBackupHelper = BackupHelper.Instance(Me.mDevice)

        AddHandler Me.mBackupHelper.BackupProgressChanged, AddressOf Me.OnBackupProgressChanged
        AddHandler Me.mBackupHelper.DecryptInputPwd, AddressOf Me.OnDecryptInputPwd
    End Sub

    '判断当前的备份是否可以还原
    Public Shared Function CheckCanRestore(ByVal device As iPhoneDevice, ByVal Item As BackupInfo) As Boolean
        Dim blnReturn As Boolean = False
        If device Is Nothing OrElse Item Is Nothing Then
            Return blnReturn
        End If
        'Dim strDevVer() As String = device.ProductVersion.Split(".")
        'Dim strItemVer() As String = Item.ProductVersion.Split(".")
        'If strDevVer.Length = 0 OrElse strItemVer.Length = 0 Then
        '    Return blnReturn
        'End If

        'If Val(strDevVer(0)) > Val(strItemVer(0)) Then
        '    blnReturn = True
        'ElseIf strDevVer.Length >= 2 AndAlso strItemVer.Length >= 2 Then
        '    If Val(strDevVer(0)) = Val(strItemVer(0)) AndAlso Val(strDevVer(1)) >= Val(strItemVer(1)) Then
        '        blnReturn = True
        '    End If
        'End If
        Try
            If device.VersionNumber >= Item.VersionNumber Then
                blnReturn = True
            End If
        Catch ex As Exception
        End Try

        Return blnReturn
    End Function

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        If Me.mBackupOrRestore = BackupRestore.Backup Then
            Me.Text = Me.Language.GetString("Backup.Button.CreateBackup")    '"创建备份"
            Me.lblBackupType.Text = Me.Language.GetString("Backup.Label.BackupType")                '"请选择备份的方式"
            Me.rdbIncrem.Text = Me.Language.GetString("Backup.Button.BackupIncremental")            '"快速备份"
            Me.rdbNew.Text = Me.Language.GetString("Backup.Button.BackupNew")   '"新建备份"
            Me.btnBacbupFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")     '"更换"
            Me.btnOK.Text = Me.Language.GetString("Main.Button.Backup")                             '备份
            Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")                              '"取消"
        Else
            Me.Text = Me.Language.GetString("Backup.Button.Restore")              ' "从备份恢复"
            Me.lblBackupType.Text = Me.Language.GetString("Backup.Label.SelectBackupDevice")        '"请选择该设备的可用备份"
            Me.btnOK.Text = Me.Language.GetString("Common.Button.Restore")                          '"还原"
            Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")                              '"取消"
        End If
        Me.lblOpenTutorial.Text = Me.Language.GetString("Weixin.Button.OpenTutorial") '备份失败？备份卡在某一处不动？戳我>>
        Me.llblDeletePwd.Text = Me.Language.GetString("Common.DeleteBackupPwdEx") '"删除备份密码
    End Sub

    Private Sub InitGridView()
        Me.mDgvBackupList = New tbDataGridViewEx
        With Me.mDgvBackupList
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 26
            .AllowDrop = True
            .MultiSelect = False
            .tbShowNoData = False
            .Columns.Clear()

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Common.Label.Name"), _
                                                                   128, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colDateTime", Me.Language.GetString("App.Button.TimeSort"), _
                                                                   136, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)) '"时间"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), _
                                                                   90, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"Size"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colVersion", Me.Language.GetString("App.Column.Version"), _
                                                                   80, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"当前版本"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))
            .ContextMenuStrip = Me.munOperator
            .ResumeLayout()
        End With

        Me.pnlContainer.Controls.Add(Me.mDgvBackupList)
        Me.mDgvBackupList.BringToFront()
    End Sub

    Public Overrides Function CheckCanClose() As CloseStatus
        Return Me.mBackupHelper.CheckCanClose()
    End Function

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)


        '1.如果是备份 且 备份成功后自动关闭 
        If Me.mBackupOrRestore = BackupRestore.Backup AndAlso Me.mAutoClose AndAlso Me.mBackupSucceed Then
            GoTo Do_Exit
        End If

        If Me.CheckCanClose() = CloseStatus.UnClose AndAlso Not Me.mBackupSucceed Then
            Dim strMessage As String = String.Empty

            If Me.mBackupOrRestore = BackupRestore.Restore Then
                strMessage = Me.Language.GetString("Backup.Message.RestoreClose")                '"正在还原中，您确定要关闭当前窗体？"
            Else
                strMessage = Me.Language.GetString("Backup.Message.BackupClose")       '"正在备份中，您确定要关闭当前窗体？"
            End If

            If tbMessageBox.Show(Me, strMessage, Me.Language.GetString("Common.Info"), _
                                MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) <> Windows.Forms.DialogResult.OK Then
                pblnCancelClose = True
            Else
                If Me.mBackupHelper IsNot Nothing Then
                    RemoveHandler Me.mBackupHelper.BackupProgressChanged, AddressOf Me.OnBackupProgressChanged
                    RemoveHandler Me.mBackupHelper.DecryptInputPwd, AddressOf Me.OnDecryptInputPwd
                End If

                If Me.mBackupOrRestore = BackupRestore.Backup Then
                    Me.mBackupHelper.CancelBackup()
                End If
            End If

        End If

Do_Exit:
        Utility.AbortThread(Me.mTDLoadCheckEncrypt)
    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)
    End Sub

#End Region

#Region "--- 连接事件 ---"

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
        Me.mDevice = device
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub rdbIncrem_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdbIncrem.CheckedChanged, rdbNew.CheckedChanged
        Me.pnlContainer.Enabled = Me.rdbIncrem.Checked

        Me.txtiBackupFolder.Enabled = Not Me.rdbIncrem.Checked
        Me.btnBacbupFolder.Enabled = Not Me.rdbIncrem.Checked

    End Sub

    Private Sub tsmiOpenFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiOpenFolder.Click
        If Me.mDgvBackupList.Rows.Count > 0 AndAlso _
           Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso _
           Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing AndAlso _
           TypeOf Me.mDgvBackupList.SelectedRows(0).Tag Is BackupInfo Then

            Common.OpenExplorer(CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo).FolderPath)
        End If
    End Sub

    Private Sub btnBacbupFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBacbupFolder.Click
        Dim folderDialog As New FolderBrowserDialog()

        If folderDialog.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
            Me.txtiBackupFolder.Text = folderDialog.SelectedPath
            IniSetting.SetiTunesBackupFloder(Me.txtiBackupFolder.Text.Trim())
        End If
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        If Me.btnOK.Tag IsNot Nothing AndAlso TypeOf Me.btnOK.Tag Is String AndAlso Me.btnOK.Tag = "Finished" Then
#If IS_ITONG Then
            FlashHelper.GetInstance(Me.mDevice).IsBackup = True
#End If
            Me.Close()
            Return
        End If

        If Me.mBackupOrRestore = BackupRestore.Backup Then
            If Me.rdbIncrem.Checked Then
                Dim lst As New List(Of String)
                lst.Add(Me.Language.GetString("Backup.Button.BackupIncremental"))
                Dim frm As tbRemind = New Components.tbRemind(Me.mApplication, lst)
                If frm.ShowDialog(Me) <> Windows.Forms.DialogResult.OK Then
                    Return
                End If
            End If
            Me.StartBackup()
        Else
            Me.StartRestore()
        End If
    End Sub

    Private Sub StartBackup()
        Dim strBackupFolder As String = BackupHelper.BackupFolderEx
        If Not Me.rdbIncrem.Checked Then
            strBackupFolder = Me.txtiBackupFolder.Text
            If Not Directory.Exists(strBackupFolder) Then
                tbMessageBox.Show(Me, String.Format(Me.Language.GetString("File.Message.PathNoExist"), strBackupFolder), Me.Language.GetString("Common.Info"))
                Me.txtiBackupFolder.Focus()
                Return
            End If
        End If

        If rdbIncrem.Checked() Then
            Common.LogException(String.Format("用户操作 快速备份 {0}", Me.mDevice.Identifier), "StartBackup")
        Else
            Common.LogException(String.Format("用户操作 全新备份 {0}", Me.mDevice.Identifier), "StartBackup")
        End If

        strBackupFolder = Me.GetBackUpFolder(strBackupFolder)
        Me.mstrBackPath = strBackupFolder
        Me.mintBackProgress = 0
        Me.mblnShowDiskMessage = False

        '空间太少的时候提示用户
        Dim longSpace As Long = 100000000
        If Me.CheckDiskFull(longSpace) Then
            '"您的磁盘空间只剩下{0}，您确定要继续备份？"
            If tbMessageBox.Show(String.Format(Me.Language.GetString("Backup.Message.DiskIsFull"), Utility.FormatFileSize(longSpace)), Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.No Then
                Return
            End If
        End If

        Me.pbarProgress.tbPlayValue = 0
        Me.pbarProgress.Visible = True
        Me.pbarProgress.ProgressFormat = "0.####"
        Me.SetMessage(Color.Black, Me.Language.GetString("Backup.Message.BackupMessage"))       '"正在备份中，可能需要一些时间，请耐心等待..."
        Me.SetContralStatus(False)
        Me.mBackupHelper.StrCurrentBackupFolder = strBackupFolder
        Me.mBackupHelper.StartBackup(Me.rdbNew.Checked, mAutoClose)
    End Sub

    Private mFrmWeCahtMain As Form = Nothing
    Public Sub StartRestore(Optional ByVal strPath As String = "", Optional ByVal frm As Form = Nothing, Optional ByVal isFullBack As Boolean = True, Optional ByVal strSKU As String = "", Optional ByVal strUnbackDir As String = "", Optional ByVal strUnBackupPwd As String = "")
        Me.SeelectRowSelectByPath(strPath)

Do_RetryCheckiCloud:
        'icloud 提示
        Dim frmCC As frmCheckiCloud = Nothing
        'If Me.mDeviceMode = DeviceMode.NomalMode Then
        '判断 icloud 查找我的iphone是否打开
        Dim objValue As Object = Me.mDevice.GetDeviceValue("com.apple.fmip", "IsAssociated")
        Dim blnValue As Boolean = CType(objValue, Boolean)
        If blnValue Then
            frmCC = New frmCheckiCloud(iCloudType.FindMyIphoneCloseBackup)
        End If
        'End If

        If frmCC IsNot Nothing Then
            If frmCC.ShowDialog <> Windows.Forms.DialogResult.OK Then
                Return
            End If
            GoTo Do_RetryCheckiCloud
        End If

        If tbInputBox.Show(IIf(frm IsNot Nothing, frm, Me), "请根据教程双击Home退出所有的APP。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, "", False, My.Resources.backup_exitapp, "", "", "", False) <> Windows.Forms.DialogResult.OK Then
            Return
        End If

        Dim strMessage As String = String.Format(Me.Language.GetString("Backup.Message.BeforeRestoreWaning"), Me.mDevice.OriginalDeviceName)

        '"确认将所选备份文件恢复到 {0}？" & vbCrLf & _
        '"该操作会抹掉当前设备的照片和系统配制等数据，请谨慎操作！" & vbCrLf & _
        '"恢复过程中请不要断开连接，否则会造成数据丢失或设备故障。" & vbCrLf & _
        '"恢复完成后，设备将会自动重启。"
        Me.mFrmWeCahtMain = frm

        If strSKU.Length > 0 Then
            strMessage = String.Format(Me.Language.GetString("Backup.Message.BeforeRestoreWeChatWaning"), Me.mDevice.OriginalDeviceName)
            If tbMessageBox.Show(IIf(frm Is Nothing, Me, frm), strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, True, Me.Language.GetString("Weixin.Lable.AlreadyCloseWeixin"), "http://news.tongbu.com/93421.html") = Windows.Forms.DialogResult.No Then
                Return
            End If
        Else
            If tbMessageBox.Show(IIf(frm Is Nothing, Me, frm), strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo) = Windows.Forms.DialogResult.No Then
                Return
            End If
        End If

        Me.pbarProgress.tbPlayValue = 0
        Me.pbarProgress.Visible = True
        Me.SetMessage(Color.Black, Me.Language.GetString("Backup.Message.RestoreMessage"))      '"正在还原...(请不要断开连接)"
        Dim strBackupFolder As String = ""

        If Not String.IsNullOrEmpty(strPath) Then
            strBackupFolder = strPath
        Else
            If Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing AndAlso TypeOf Me.mDgvBackupList.SelectedRows(0).Tag Is BackupInfo Then
                strBackupFolder = CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo).FolderPath
            End If
        End If

        Me.SetContralStatus(False)
        If Me.mBackupHelper Is Nothing Then
            InitBackupHelper()
        End If
        Me.mBackupHelper.StartRestore(strBackupFolder, isFullBack, strSKU, strUnbackDir, strUnBackupPwd)
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click

        Me.Close()
    End Sub


#End Region

#Region "--- 备份与还原 ---"

    Public Sub OnBackupProgressChanged(ByVal sender As Object, ByVal args As BackupArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupArgs)(AddressOf OnBackupProgressChanged), sender, args)
        Else

            Dim frm As Form = Me
            If Me.mFrmWeCahtMain IsNot Nothing Then
                frm = Me.mFrmWeCahtMain
            End If

            Select Case args.Status
                Case BackupStatus.BackupOrRestore
                    If args.Progress = 100 Then
                        Me.pbarProgress.tbPlayValue = 99
                    Else
                        Me.pbarProgress.tbPlayValue = args.Progress
                    End If

                    Me.pbarProgress.Visible = True
                    Me.SetContralStatus(False)

                    Dim strMsg As String = IIf(args.BackupType = BackupRestore.Backup, Me.Language.GetString("Backup.Message.BackupMessage"), Me.Language.GetString("Backup.Message.RestoreMessage"))

                    Select Case args.ProgressType
                        Case ProgressType.UploadFileToPhone
                            strMsg = String.Format("{0}{1}{2}", Me.Language.GetString("File.Label.Uploading"), Me.Language.GetString("Common.Symbol.Colon"), args.Speed)

                        Case ProgressType.DownloadFileToPC
                            strMsg = String.Format("{0}{1}{2}", Me.Language.GetString("App.Cell.Downloading"), Me.Language.GetString("Common.Symbol.Colon"), args.Speed)

                        Case ProgressType.CreateDirectory
                            strMsg = Me.Language.GetString("Backup.Message.CreateDirectory")

                        Case ProgressType.CopyFile
                            strMsg = Me.Language.GetString("Backup.Message.CopyFile")

                        Case ProgressType.MoveFile
                            strMsg = Me.Language.GetString("Backup.Message.MoveFile")

                        Case ProgressType.RemoveFile
                            strMsg = Me.Language.GetString("Backup.Message.RemoveFile")

                        Case ProgressType.GetFreeDiskSpace
                            strMsg = Me.Language.GetString("Backup.Message.GetFreeDiskSpace")
                    End Select

                    Me.SetMessage(Color.Black, strMsg)
                    Me.lblPath.Text = args.CurrentPath

                Case BackupStatus.Failure
                    Me.SetContralStatus(True)
                    Me.SetMessage(Color.Red, args.ErrorMsg)

                    If args.ErrorType = BackupRestoreErrorType.Other Then
                        Me.RemoveNoExistFile()
                    End If

                    If args.BackupType = BackupRestore.Backup Then
                        If ServerIniSetting.GetShowWechatBackupTool Then
                            ''备份失败
                            ''可以试试【微信备份助手】，可以在不用数据线的情况下导出微信的消息。
                            ''是否需要了解？
                            'Dim strMsg As String = String.Format("{0}{1}{2}{1}{3}", _
                            '                                 Me.Language.GetString("Weixin.Message.BackUpFailure"), _
                            '                                 vbCrLf, _
                            '                                 Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistant"), _
                            '                                 Me.Language.GetString("WechatAssistant.Message.NeedToKnow"))
                            'If tbMessageBox.Show(Me, _
                            '                     strMsg, _
                            '                     Me.Language.GetString("Common.Info"), _
                            '                     MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Yes Then

                            '    Common.OpenExplorer(ServerIniSetting.GetUrlWechatBackupTooliOSBackupFailed)
                            'End If

                            '备份失败
                            '可能由于设备空间不足或电脑磁盘空间不足。您可以尝试使用iTunes备份后，
                            '使用同步助手更多-备份还原功能读取；或尝试使用微信备份助手。   WechatAssistant.Button.Specific  WechatAssistant.Button.iWechatAssistant
                            Dim strMsg As String = String.Format("{1}{0}{2}{3}", _
                                                               vbCrLf, _
                                                               Me.Language.GetString("Weixin.Message.BackUpFailure"), _
                                                               Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistantEx"), _
                                                               Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistantEx1"))

                            If tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2, "", False, New String() {Me.Language.GetString("WechatAssistant.Button.Specific"), Me.Language.GetString("WechatAssistant.Button.iWechatAssistant")}) = Windows.Forms.DialogResult.Yes Then
                                Common.OpenExplorer("http://news.tongbu.com/92201.html")
                            Else
                                Common.OpenExplorer(ServerIniSetting.GetUrlWechatBackupTooliOSBackupFailed)
                            End If

                        Else
                            '备份失败
                            tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.BackUpFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                        End If

                    ElseIf args.BackupType = BackupRestore.Restore Then
                        Dim strMsg As String = String.Format(Me.Language.GetString("WeChat.Message.RestoreError"), args.ErrorMsg) '"错误消息：" & vbCrLf & args.ErrorMsg & vbCrLf & "注意事项：" & vbCrLf & "1.请点击查看，确认下备份的数据是否能正常读取查看；" & vbCrLf & "2.请谨慎操作，防止数据备份覆盖或永久丢失；" & vbCrLf & "3.点击右上角加入客服QQ群联系客服。"                      
                        tbMessageBox.Show(frm, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                    End If

                Case BackupStatus.Succeed
                    Me.mBackupSucceed = True
                    Me.pbarProgress.tbPlayValue = 100
                    Me.SetMessage(Color.Black, IIf(args.BackupType = BackupRestore.Backup, Me.Language.GetString("App.Cell.BackupSucceed"), Me.Language.GetString("Backup.Message.RestoreSucceed")))
                    Me.SetContralStatus(True)
                    Me.SetFinished()
                    If args.BackupType = BackupRestore.Backup Then

                        Dim bakObject As BackupInfo = BackupHelper.CheckBackObjExist(args.FolderPath, 0)
                        If Me.mFrmBackupList IsNot Nothing Then
                            bakObject.Size = Me.mFrmBackupList.UpdateRowFileSize(args.FolderPath)
                        End If

                        If bakObject Is Nothing Then
                            bakObject = New BackupInfo(args.FolderPath)
                        End If
                        If Me.rdbNew.Checked Then
                            Me.AddRow(bakObject)
                            'If Me.mFrmBackupList IsNot Nothing Then
                            '    Me.mFrmBackupList.InsertXMForBackupPath(args.FolderPath)
                            'End If
                        Else
                            Me.UpdateRowFileSize(bakObject)
                        End If
                        Me.mBackupPath = args.FolderPath

                        If Me.mAutoClose Then
                            Me.Close()
                        Else
                            Dim strOk As String = Me.Language.GetString("WeChat.Button.CheckBackup") '"查看备份"
                            Dim isLook As Boolean = True
                            'If bakObject.IsEncrypted AndAlso Common.IOSStrVersionToIntVersion(bakObject.ProductVersion) >= 933 Then
                            '    strOk = Me.Language.GetString("Common.Button.OpenLocalFolder") '打开文件夹
                            '    isLook = False
                            'End If

                            If tbMessageBox.Show(Me, Me.Language.GetString("App.Cell.BackupSucceed"), Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2, "", False, New String() {strOk, Me.Language.GetString("Common.OK")}) = Windows.Forms.DialogResult.Yes Then
                                If isLook Then
                                    Dim frmOwm As frmDeviceBase = Me
                                    If Me.mFrmBackupList IsNot Nothing Then
                                        frmOwm = Me.mFrmBackupList
                                    End If
                                    frmBackupList.ShowDetail(frmOwm, Me.mApplication, bakObject, Me.Language, Me.mDevice)
                                Else
                                    Common.OpenExplorer(bakObject.FolderPath)
                                End If
                            End If
                            Me.Close()
                        End If
                    ElseIf args.BackupType = BackupRestore.Restore Then
                        tbMessageBox.Show(frm, Me.Language.GetString("Backup.Message.RestoreSucceed"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                    End If
            End Select


            '''''''''''''''''''''''''''行为数据收集'''''''''''
            Dim mode As ModelKey = ModelKey.BackupRestore
            If args.BackupType = BackupRestore.Backup Then
                mode = ModelKey.BackupBackup
            End If
            If args.Status = BackupStatus.Succeed Then
                ActionCollectHelper.BackupRestoreAccition(Me.mDevice, mode, FunctionSucceed.Succeed, "")
            ElseIf args.Status = BackupStatus.Failure Then
                ActionCollectHelper.BackupRestoreAccition(Me.mDevice, mode, FunctionSucceed.Failure, args.ErrorMsg)
            End If
            ''''''''''''''''''''''''''''''''''''''''''''''''''
        End If
    End Sub

    Public Sub OnDecryptInputPwd(ByVal sender As Object, ByVal args As BackupPwdArgs)
        Dim strPwd As String = String.Empty
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupPwdArgs)(AddressOf OnDecryptInputPwd), sender, args)
        Else
            '"您曾经在iTunes中备份加密，请输入设置的备份密码。"
            tbInputBox.Show(Me, Me.Language.GetString("Backup.Message.TypeInBackupPwd"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, MessageBoxDefaultButton.Button1, strPwd, True, My.Resources.backup_password) ', "iTunes备份密码相关教程", "http://news.tongbu.com/92502.html"
            args.Password = strPwd
        End If
    End Sub

    Private Sub RemoveNoExistFile()
        Try
            If Me.mBackupOrRestore = BackupRestore.Restore Then
                If Me.mDgvBackupList.SelectedRows Is Nothing OrElse Me.mDgvBackupList.SelectedRows(0).Tag Is Nothing OrElse Not TypeOf Me.mDgvBackupList.SelectedRows(0).Tag Is BackupInfo Then
                    Return
                End If
                Dim strPath As String = CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo).FolderPath
                Dim lstMBFileRecord As List(Of MBFileRecord) = mbdb.Reload(strPath, True)
                Dim lstMBFileRecordNoExist As New List(Of MBFileRecord)
                If lstMBFileRecord Is Nothing Then
                    Return
                End If
                For Each Item As MBFileRecord In lstMBFileRecord
                    If Not String.IsNullOrEmpty(Item.PathOnPC) AndAlso Not File.Exists(Item.PathOnPC) AndAlso Item.Size > 0 Then
                        lstMBFileRecordNoExist.Add(Item)
                    End If
                Next

                For Each Item As MBFileRecord In lstMBFileRecordNoExist
                    lstMBFileRecord.Remove(Item)
                Next
                If lstMBFileRecordNoExist.Count > 0 Then
                    mbdb.Write(strPath)
                End If

                lstMBFileRecordNoExist.Clear()
                lstMBFileRecordNoExist = Nothing

            End If
        Catch ex As Exception
        End Try

    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Sub SeelectRowSelectByPath(ByVal strPath As String)
        If String.IsNullOrEmpty(strPath) Then
            Return
        End If
        Dim ItemSelect As DataGridViewRow = Nothing
        For Each Item As DataGridViewRow In Me.mDgvBackupList.Rows
            If Item IsNot Nothing AndAlso Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is BackupInfo AndAlso CType(Item.Tag, BackupInfo).FolderPath = strPath Then
                Item.Selected = True
                ItemSelect = Item
            Else
                Item.Selected = False
            End If
        Next
        If ItemSelect IsNot Nothing Then
            ItemSelect.Selected = True
        End If
    End Sub

    Private Delegate Sub AddRowHandler(ByVal bakObject As BackupInfo)
    Private Sub AddRow(ByVal bakObject As BackupInfo)
        If Me.InvokeRequired Then
            Me.Invoke(New AddRowHandler(AddressOf AddRow), bakObject)
        Else
            Try
                Dim strSize As String = Utility.FormatFileSize(bakObject.Size)
                Dim index As Integer = Me.mDgvBackupList.Rows.Add(bakObject.DisplayName, _
                                                                                                    bakObject.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), _
                                                                                                    strSize, bakObject.ProductVersion, "")

                Dim row As tbDataGridViewRow = Me.mDgvBackupList.Rows(index)
                row.Tag = bakObject

                If index = 0 Then
                    row.Selected = True
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmBackupCreate_AddRow")
            End Try

        End If
    End Sub

    Public Sub UpdateRowFileSize(ByVal bakObject As BackupInfo)

        For Each Item As tbDataGridViewRow In Me.mDgvBackupList.Rows
            If Item.Tag Is Nothing OrElse Not (TypeOf Item.Tag Is BackupInfo) Then
                Continue For
            End If

            Dim obj As BackupInfo = Item.Tag

            If String.Compare(obj.FolderPath, bakObject.FolderPath) = 0 Then
                Item.Cells("colSize").Value = Utility.FormatFileSize(bakObject.Size)

                Exit For
            End If
        Next

    End Sub

    Private Sub SetFinished()
        If Me.InvokeRequired Then
            Me.Invoke(New Threading.ThreadStart(AddressOf SetFinished))
        Else
            Me.btnOK.Text = Me.Language.GetString("Media.Message.Finished")
            Me.btnOK.Tag = "Finished"
            Me.btnCancel.Visible = False
            Me.btnOK.Location = Me.btnCancel.Location
        End If
    End Sub

    Private Delegate Sub SetMessageHandler(ByVal clolr As Color, ByVal strMessage As String)
    Private Sub SetMessage(ByVal fontClolr As Color, ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetMessageHandler(AddressOf SetMessage), fontClolr, strMessage)
        Else
            Me.lblMessage.ForeColor = fontClolr
            Me.lblMessage.Text = strMessage
        End If
    End Sub

    Private Delegate Sub SetContralStatusHandler(ByVal blnEnable As Boolean)
    Private Sub SetContralStatus(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetContralStatusHandler(AddressOf SetContralStatus), blnEnable)
        Else
            Me.rdbIncrem.Enabled = blnEnable
            Me.rdbNew.Enabled = blnEnable

            Me.txtiBackupFolder.Enabled = blnEnable
            Me.btnBacbupFolder.Enabled = blnEnable

            Me.btnOK.Enabled = blnEnable
            Me.llblDeletePwd.Enabled = blnEnable
        End If
    End Sub

    Private Function GetBackUpFolder(ByVal strBackupFolder As String) As String
        If Me.rdbIncrem.Checked Then
            If Me.mDgvBackupList.Rows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows.Count > 0 Then
                Dim bakObject As BackupInfo = Me.mDgvBackupList.SelectedRows(0).Tag
                If bakObject IsNot Nothing Then
                    strBackupFolder = bakObject.FolderPath
                End If
            Else
                strBackupFolder = Path.Combine(strBackupFolder, Me.mDevice.Identifier)
            End If
        Else
            Dim strTempFolder As String = Path.Combine(strBackupFolder, Me.mDevice.Identifier)
            Dim intIndex As Integer = 0
            While True
                If Directory.Exists(strTempFolder) Then
                    intIndex += 1
                    strTempFolder = Path.Combine(strBackupFolder, Me.mDevice.Identifier & String.Format("({0})", intIndex))
                Else
                    Exit While
                End If
            End While
            strBackupFolder = strTempFolder
        End If
        Return strBackupFolder
    End Function

    Private Function CheckDiskFull(ByRef intRemainSpace As Long) As Boolean
        Dim isFull As Boolean = False
        Try
            Dim info As New DirectoryInfo(Me.mstrBackPath)
            Dim divInfo As New DriveInfo(info.Root.ToString)
            If divInfo.TotalFreeSpace < intRemainSpace Then
                isFull = True
                intRemainSpace = divInfo.TotalFreeSpace
            End If
        Catch ex As Exception
            Common.Log(ex.ToString)
        End Try

        Return isFull
    End Function

#End Region

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_sub
        End If
    End Sub

    Private Sub lblOpenTutorial_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lblOpenTutorial.LinkClicked
        If Folder.AppType = RunType.iDataRecovery OrElse Folder.AppType = RunType.iDataRecovery_Abroad Then
            Common.OpenExplorer("http://rollback.tongbu.com/guide")
            Return
        ElseIf Folder.AppType = RunType.iWeChatMigration Then
            Common.OpenExplorer("http://news.tongbu.com/92201.html")
            Return
        End If
#If IS_WECHAT Then
        Common.OpenExplorer("http://wechat.tongbu.com/guide/1")
#Else
        Common.OpenExplorer("http://news.tongbu.com/92201.html")
#End If
    End Sub

    Private Sub llblDeletePwd_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles llblDeletePwd.LinkClicked
        If BackupListDeleteHelper.GetInstance().DeleteiTunesBackupPassword(Me.Language, Me.mDevice) Then
            SetCheckEncrypt(False)
        End If
    End Sub


    Private Sub CheckEncrypt()
        Try
            If Me.mTDLoadCheckEncrypt IsNot Nothing AndAlso Me.mTDLoadCheckEncrypt.ThreadState <> ThreadState.Stopped Then
                Return
            End If
            Me.mTDLoadCheckEncrypt = New Thread(AddressOf DoCheckEncrypt)
            Me.mTDLoadCheckEncrypt.IsBackground = True
            Me.mTDLoadCheckEncrypt.Start()
            Utility.WaitSeconds(0.5)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckEncrypt")
        End Try
    End Sub

    Private Sub DoCheckEncrypt()
        Try
            Dim isCheckEncrypt As Boolean = False
            Dim intTryCount = 0
            If Me.mDevice IsNot Nothing Then
                isCheckEncrypt = Me.mDevice.mb2_CheckEncrypt()
                While Not isCheckEncrypt AndAlso intTryCount < 15
                    Utility.WaitSeconds(1)
                    isCheckEncrypt = Me.mDevice.mb2_CheckEncrypt()
                    intTryCount = intTryCount + 1
                End While
            End If
            SetCheckEncrypt(isCheckEncrypt)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoCheckEncrypt")
        End Try
    End Sub

    Private Delegate Sub SetCheckEncryptHandler(isCheckEncrypt As Boolean)
    Public Sub SetCheckEncrypt(isCheckEncrypt As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetCheckEncryptHandler(AddressOf SetCheckEncrypt), isCheckEncrypt)
        Else
            Try
                Me.llblDeletePwd.Visible = isCheckEncrypt
                Me.lblBackupPwdMsg.Visible = isCheckEncrypt
                Me.lblBackupPwdMsg.Text = Me.Language.GetString("Common.Backup.EncryptionPromptEx") '此设备已设置备份密码， 备份的文件无法查看。
                Me.llblDeletePwd.Location = New Point(Me.lblBackupPwdMsg.Width + 20, Me.lblBackupPwdMsg.Location.Y)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetCheckEncrypt")
            End Try
        End If
    End Sub

End Class