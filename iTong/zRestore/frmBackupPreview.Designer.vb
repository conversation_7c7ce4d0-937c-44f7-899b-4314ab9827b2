﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmBackupPreview
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBackupPreview))
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.Spliter2 = New System.Windows.Forms.Panel()
        Me.Spliter1 = New System.Windows.Forms.Panel()
        Me.tlpMain = New System.Windows.Forms.TableLayoutPanel()
        Me.tvwLeft = New iTong.tbTreeViewEx()
        Me.pnlControl = New iTong.Components.tbPanel()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.btnView = New iTong.Components.tbButton()
        Me.txtSearchFile = New iTong.Components.tbSearch()
        Me.btnViewLargeIcon = New iTong.Components.tbRadioButton()
        Me.btnViewList = New iTong.Components.tbRadioButton()
        Me.btnExport = New iTong.Components.tbButton()
        Me.pnlProgress = New iTong.Components.tbPanel()
        Me.pbarProgress = New iTong.Components.tbControlBar()
        Me.lblProgress = New iTong.Components.tbLabel()
        Me.btnAbrot = New iTong.Components.tbButton()
        Me.pnlFilePath = New iTong.Components.tbPanel()
        Me.txtFilePath = New iTong.tbTextBoxPath()
        Me.btnGoUp = New iTong.Components.tbButton()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.imlSmall = New System.Windows.Forms.ImageList(Me.components)
        Me.imlLarge = New System.Windows.Forms.ImageList(Me.components)
        Me.pnlLoading = New iTong.Components.tbPanel()
        Me.lblLoading = New iTong.Components.tbLabel()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.munOperator = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpen = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExport = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiCopyPath = New System.Windows.Forms.ToolStripMenuItem()
        Me.Spliter2.SuspendLayout()
        Me.tlpMain.SuspendLayout()
        Me.pnlControl.SuspendLayout()
        Me.pnlProgress.SuspendLayout()
        Me.pnlFilePath.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.munOperator.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(934, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(910, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(886, 0)
        '
        'pnlContainer
        '
        Me.pnlContainer.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(216, 66)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(740, 486)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 17
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'Spliter2
        '
        Me.Spliter2.Controls.Add(Me.Spliter1)
        Me.Spliter2.Cursor = System.Windows.Forms.Cursors.SizeWE
        Me.Spliter2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Spliter2.Location = New System.Drawing.Point(215, 0)
        Me.Spliter2.Margin = New System.Windows.Forms.Padding(0)
        Me.Spliter2.Name = "Spliter2"
        Me.tlpMain.SetRowSpan(Me.Spliter2, 4)
        Me.Spliter2.Size = New System.Drawing.Size(1, 582)
        Me.Spliter2.TabIndex = 2
        '
        'Spliter1
        '
        Me.Spliter1.BackColor = System.Drawing.Color.FromArgb(CType(CType(214, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.Spliter1.Cursor = System.Windows.Forms.Cursors.SizeWE
        Me.Spliter1.Dock = System.Windows.Forms.DockStyle.Left
        Me.Spliter1.Location = New System.Drawing.Point(0, 0)
        Me.Spliter1.Margin = New System.Windows.Forms.Padding(0)
        Me.Spliter1.Name = "Spliter1"
        Me.Spliter1.Size = New System.Drawing.Size(6, 582)
        Me.Spliter1.TabIndex = 2
        '
        'tlpMain
        '
        Me.tlpMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tlpMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.tlpMain.ColumnCount = 3
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 215.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.Controls.Add(Me.tvwLeft, 0, 0)
        Me.tlpMain.Controls.Add(Me.pnlControl, 2, 1)
        Me.tlpMain.Controls.Add(Me.pnlProgress, 2, 3)
        Me.tlpMain.Controls.Add(Me.pnlFilePath, 2, 0)
        Me.tlpMain.Controls.Add(Me.Spliter2, 1, 0)
        Me.tlpMain.Controls.Add(Me.pnlContainer, 2, 2)
        Me.tlpMain.Location = New System.Drawing.Point(1, 32)
        Me.tlpMain.Margin = New System.Windows.Forms.Padding(0)
        Me.tlpMain.Name = "tlpMain"
        Me.tlpMain.RowCount = 4
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 33.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 33.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.tlpMain.Size = New System.Drawing.Size(956, 582)
        Me.tlpMain.TabIndex = 22
        '
        'tvwLeft
        '
        Me.tvwLeft.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tvwLeft.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.tvwLeft.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tvwLeft.ForeColor = System.Drawing.Color.FromArgb(CType(CType(47, Byte), Integer), CType(CType(47, Byte), Integer), CType(CType(47, Byte), Integer))
        Me.tvwLeft.HideSelection = False
        Me.tvwLeft.HotTracking = True
        Me.tvwLeft.Indent = 14
        Me.tvwLeft.ItemHeight = 23
        Me.tvwLeft.Location = New System.Drawing.Point(0, 0)
        Me.tvwLeft.Margin = New System.Windows.Forms.Padding(0)
        Me.tvwLeft.Name = "tvwLeft"
        Me.tlpMain.SetRowSpan(Me.tvwLeft, 4)
        Me.tvwLeft.ShowRootLines = False
        Me.tvwLeft.Size = New System.Drawing.Size(215, 582)
        Me.tvwLeft.TabIndex = 26
        Me.tvwLeft.TreeViewImage = CType(resources.GetObject("tvwLeft.TreeViewImage"), System.Collections.Generic.Dictionary(Of String, System.Drawing.Image))
        '
        'pnlControl
        '
        Me.pnlControl.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlControl.Controls.Add(Me.btnRefresh)
        Me.pnlControl.Controls.Add(Me.btnDelete)
        Me.pnlControl.Controls.Add(Me.btnView)
        Me.pnlControl.Controls.Add(Me.txtSearchFile)
        Me.pnlControl.Controls.Add(Me.btnViewLargeIcon)
        Me.pnlControl.Controls.Add(Me.btnViewList)
        Me.pnlControl.Controls.Add(Me.btnExport)
        Me.pnlControl.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlControl.Location = New System.Drawing.Point(216, 33)
        Me.pnlControl.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlControl.Name = "pnlControl"
        Me.pnlControl.Size = New System.Drawing.Size(740, 33)
        Me.pnlControl.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlControl.TabIndex = 25
        Me.pnlControl.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlControl.tbShowWatermark = False
        Me.pnlControl.tbSplit = "3,3,3,3"
        Me.pnlControl.tbWatermark = Nothing
        Me.pnlControl.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlControl.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(240, 5)
        Me.btnRefresh.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(63, 23)
        Me.btnRefresh.TabIndex = 22
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = " 刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 2
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(159, 5)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(69, 23)
        Me.btnDelete.TabIndex = 13
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "13,11,13,11"
        Me.btnDelete.tbText = "Delete"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'btnView
        '
        Me.btnView.BackColor = System.Drawing.Color.Transparent
        Me.btnView.BindingForm = Nothing
        Me.btnView.Enabled = False
        Me.btnView.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnView.Location = New System.Drawing.Point(92, 5)
        Me.btnView.Name = "btnView"
        Me.btnView.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnView.Selectable = True
        Me.btnView.Size = New System.Drawing.Size(57, 23)
        Me.btnView.TabIndex = 12
        Me.btnView.tbAdriftIconWhenHover = False
        Me.btnView.tbAutoSize = False
        Me.btnView.tbAutoSizeEx = True
        Me.btnView.tbBackgroundImage = Nothing
        Me.btnView.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnView.tbBadgeNumber = 0
        Me.btnView.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnView.tbEndEllipsis = False
        Me.btnView.tbIconHoldPlace = True
        Me.btnView.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_view
        Me.btnView.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnView.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnView.tbIconMore = False
        Me.btnView.tbIconMouseDown = Nothing
        Me.btnView.tbIconMouseHover = Nothing
        Me.btnView.tbIconMouseLeave = Nothing
        Me.btnView.tbIconPlaceText = 2
        Me.btnView.tbIconReadOnly = Nothing
        Me.btnView.tbImageMouseDown = Nothing
        Me.btnView.tbImageMouseHover = Nothing
        Me.btnView.tbImageMouseLeave = Nothing
        Me.btnView.tbProgressValue = 50
        Me.btnView.tbReadOnly = False
        Me.btnView.tbReadOnlyText = False
        Me.btnView.tbShadow = False
        Me.btnView.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnView.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnView.tbShowDot = False
        Me.btnView.tbShowMoreIconImg = CType(resources.GetObject("btnView.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnView.tbShowNew = False
        Me.btnView.tbShowProgress = False
        Me.btnView.tbShowTip = True
        Me.btnView.tbShowToolTipOnButton = False
        Me.btnView.tbSplit = "13,11,13,11"
        Me.btnView.tbText = "View"
        Me.btnView.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnView.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnView.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnView.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnView.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnView.tbTextMouseDownPlace = 2
        Me.btnView.tbToolTip = ""
        Me.btnView.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnView.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnView.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnView.VisibleEx = True
        '
        'txtSearchFile
        '
        Me.txtSearchFile.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.txtSearchFile.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchFile.Location = New System.Drawing.Point(566, 5)
        Me.txtSearchFile.MaxLength = 32767
        Me.txtSearchFile.Name = "txtSearchFile"
        Me.txtSearchFile.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchFile.ReadOnly = False
        Me.txtSearchFile.SearchText = ""
        Me.txtSearchFile.SearchTipText = "Search"
        Me.txtSearchFile.ShowClear = True
        Me.txtSearchFile.ShowClearAlways = False
        Me.txtSearchFile.ShowMore = True
        Me.txtSearchFile.ShowSearch = True
        Me.txtSearchFile.Size = New System.Drawing.Size(166, 23)
        Me.txtSearchFile.TabIndex = 11
        Me.txtSearchFile.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchFile.Timer = True
        Me.txtSearchFile.TimerInterval = 0.5R
        '
        'btnViewLargeIcon
        '
        Me.btnViewLargeIcon.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnViewLargeIcon.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnViewLargeIcon.Checked = True
        Me.btnViewLargeIcon.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.btnViewLargeIcon.Location = New System.Drawing.Point(527, 6)
        Me.btnViewLargeIcon.Name = "btnViewLargeIcon"
        Me.btnViewLargeIcon.Size = New System.Drawing.Size(26, 21)
        Me.btnViewLargeIcon.TabIndex = 10
        Me.btnViewLargeIcon.TabStop = True
        Me.btnViewLargeIcon.tbAdriftIconWhenHover = False
        Me.btnViewLargeIcon.tbAutoSize = False
        Me.btnViewLargeIcon.tbAutoSizeEx = False
        Me.btnViewLargeIcon.tbBadgeNumber = 0
        Me.btnViewLargeIcon.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnViewLargeIcon.tbEnableToolTip = False
        Me.btnViewLargeIcon.tbIconCheckedMouseDown = Nothing
        Me.btnViewLargeIcon.tbIconCheckedMouseHover = Nothing
        Me.btnViewLargeIcon.tbIconCheckedMouseLeave = Nothing
        Me.btnViewLargeIcon.tbIconHoldPlace = True
        Me.btnViewLargeIcon.tbIconImage = Nothing
        Me.btnViewLargeIcon.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnViewLargeIcon.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnViewLargeIcon.tbIconMore = False
        Me.btnViewLargeIcon.tbIconMoreImage = CType(resources.GetObject("btnViewLargeIcon.tbIconMoreImage"), System.Drawing.Image)
        Me.btnViewLargeIcon.tbIconPlaceText = 5
        Me.btnViewLargeIcon.tbIconReadOnly = Nothing
        Me.btnViewLargeIcon.tbIconReadOnlyLight = Nothing
        Me.btnViewLargeIcon.tbIconUnCheckedMouseDown = Nothing
        Me.btnViewLargeIcon.tbIconUnCheckedMouseHover = Nothing
        Me.btnViewLargeIcon.tbIconUnCheckedMouseLeave = Nothing
        Me.btnViewLargeIcon.tbImageBackground = Nothing
        Me.btnViewLargeIcon.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnViewLargeIcon.tbImageCheckedMouseDown = Nothing
        Me.btnViewLargeIcon.tbImageCheckedMouseHover = Nothing
        Me.btnViewLargeIcon.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlargeicon_checked
        Me.btnViewLargeIcon.tbImageUnCheckedMouseDown = Nothing
        Me.btnViewLargeIcon.tbImageUnCheckedMouseHover = Nothing
        Me.btnViewLargeIcon.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlargeicon_unchecked
        Me.btnViewLargeIcon.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnViewLargeIcon.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnViewLargeIcon.tbReadOnly = False
        Me.btnViewLargeIcon.tbShadow = False
        Me.btnViewLargeIcon.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnViewLargeIcon.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnViewLargeIcon.tbShowBadgeNumber = True
        Me.btnViewLargeIcon.tbShowBadgeNumberWhenSelection = True
        Me.btnViewLargeIcon.tbShowNew = False
        Me.btnViewLargeIcon.tbSplit = "3,3,3,3"
        Me.btnViewLargeIcon.tbTag = Nothing
        Me.btnViewLargeIcon.tbToolTip = ""
        Me.btnViewLargeIcon.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnViewLargeIcon.Text = "  "
        Me.btnViewLargeIcon.UseVisualStyleBackColor = True
        '
        'btnViewList
        '
        Me.btnViewList.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnViewList.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnViewList.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.btnViewList.Location = New System.Drawing.Point(502, 6)
        Me.btnViewList.Name = "btnViewList"
        Me.btnViewList.Size = New System.Drawing.Size(26, 21)
        Me.btnViewList.TabIndex = 9
        Me.btnViewList.TabStop = True
        Me.btnViewList.tbAdriftIconWhenHover = False
        Me.btnViewList.tbAutoSize = False
        Me.btnViewList.tbAutoSizeEx = False
        Me.btnViewList.tbBadgeNumber = 0
        Me.btnViewList.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnViewList.tbEnableToolTip = False
        Me.btnViewList.tbIconCheckedMouseDown = Nothing
        Me.btnViewList.tbIconCheckedMouseHover = Nothing
        Me.btnViewList.tbIconCheckedMouseLeave = Nothing
        Me.btnViewList.tbIconHoldPlace = True
        Me.btnViewList.tbIconImage = Nothing
        Me.btnViewList.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnViewList.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnViewList.tbIconMore = False
        Me.btnViewList.tbIconMoreImage = CType(resources.GetObject("btnViewList.tbIconMoreImage"), System.Drawing.Image)
        Me.btnViewList.tbIconPlaceText = 5
        Me.btnViewList.tbIconReadOnly = Nothing
        Me.btnViewList.tbIconReadOnlyLight = Nothing
        Me.btnViewList.tbIconUnCheckedMouseDown = Nothing
        Me.btnViewList.tbIconUnCheckedMouseHover = Nothing
        Me.btnViewList.tbIconUnCheckedMouseLeave = Nothing
        Me.btnViewList.tbImageBackground = Nothing
        Me.btnViewList.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnViewList.tbImageCheckedMouseDown = Nothing
        Me.btnViewList.tbImageCheckedMouseHover = Nothing
        Me.btnViewList.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_checked
        Me.btnViewList.tbImageUnCheckedMouseDown = Nothing
        Me.btnViewList.tbImageUnCheckedMouseHover = Nothing
        Me.btnViewList.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unchecked
        Me.btnViewList.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnViewList.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnViewList.tbReadOnly = False
        Me.btnViewList.tbShadow = False
        Me.btnViewList.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnViewList.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnViewList.tbShowBadgeNumber = True
        Me.btnViewList.tbShowBadgeNumberWhenSelection = True
        Me.btnViewList.tbShowNew = False
        Me.btnViewList.tbSplit = "3,3,3,3"
        Me.btnViewList.tbTag = Nothing
        Me.btnViewList.tbToolTip = ""
        Me.btnViewList.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnViewList.Text = "  "
        Me.btnViewList.UseVisualStyleBackColor = True
        '
        'btnExport
        '
        Me.btnExport.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Enabled = False
        Me.btnExport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExport.Location = New System.Drawing.Point(9, 5)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(69, 23)
        Me.btnExport.TabIndex = 6
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = True
        Me.btnExport.tbBackgroundImage = Nothing
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = False
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Global.iTong.My.Resources.Resources.btn_export_4
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 2
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = CType(resources.GetObject("btnExport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "Export"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnExport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextMouseDownPlace = 2
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'pnlProgress
        '
        Me.pnlProgress.BackColor = System.Drawing.Color.FromArgb(CType(CType(205, Byte), Integer), CType(CType(215, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.pnlProgress.Controls.Add(Me.pbarProgress)
        Me.pnlProgress.Controls.Add(Me.lblProgress)
        Me.pnlProgress.Controls.Add(Me.btnAbrot)
        Me.pnlProgress.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlProgress.Location = New System.Drawing.Point(216, 552)
        Me.pnlProgress.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlProgress.Name = "pnlProgress"
        Me.pnlProgress.Size = New System.Drawing.Size(740, 30)
        Me.pnlProgress.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlProgress.TabIndex = 24
        Me.pnlProgress.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_status
        Me.pnlProgress.tbShowWatermark = False
        Me.pnlProgress.tbSplit = "3,3,3,3"
        Me.pnlProgress.tbWatermark = Nothing
        Me.pnlProgress.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlProgress.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pbarProgress
        '
        Me.pbarProgress.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pbarProgress.BackColor = System.Drawing.Color.Transparent
        Me.pbarProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.pbarProgress.Location = New System.Drawing.Point(591, 10)
        Me.pbarProgress.Name = "pbarProgress"
        Me.pbarProgress.Size = New System.Drawing.Size(110, 10)
        Me.pbarProgress.TabIndex = 25
        Me.pbarProgress.tbBackgroundImage = CType(resources.GetObject("pbarProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.pbarProgress.tbCanDragValue = False
        Me.pbarProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pbarProgress.tbDotImage = CType(resources.GetObject("pbarProgress.tbDotImage"), System.Drawing.Image)
        Me.pbarProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pbarProgress.tbDotMouseDown = Nothing
        Me.pbarProgress.tbDotMouseHover = Nothing
        Me.pbarProgress.tbDotMouseLeave = Nothing
        Me.pbarProgress.tbDownloadImage = Global.iTong.My.Resources.Resources.app_progress_background
        Me.pbarProgress.tbDownMax = 100
        Me.pbarProgress.tbDownMin = 0
        Me.pbarProgress.tbDownValue = 0
        Me.pbarProgress.tbIsWaiting = False
        Me.pbarProgress.tbPlayImage = Global.iTong.My.Resources.Resources.app_progress_value
        Me.pbarProgress.tbPlayMax = 100
        Me.pbarProgress.tbPlayMin = 0
        Me.pbarProgress.tbPlayValue = 50
        Me.pbarProgress.tbShowDot = False
        Me.pbarProgress.tbShowText = True
        Me.pbarProgress.tbSplit = "4,0,4,0"
        Me.pbarProgress.Text = "TbControlBar1"
        '
        'lblProgress
        '
        Me.lblProgress.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblProgress.AutoEllipsis = True
        Me.lblProgress.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblProgress.Location = New System.Drawing.Point(13, 5)
        Me.lblProgress.Name = "lblProgress"
        Me.lblProgress.Size = New System.Drawing.Size(572, 21)
        Me.lblProgress.TabIndex = 13
        Me.lblProgress.tbAdriftWhenHover = False
        Me.lblProgress.tbAutoEllipsis = True
        Me.lblProgress.tbAutoSize = False
        Me.lblProgress.tbHideImage = False
        Me.lblProgress.tbIconImage = Nothing
        Me.lblProgress.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblProgress.tbIconPlaceText = 5
        Me.lblProgress.tbShadow = False
        Me.lblProgress.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblProgress.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblProgress.tbShowScrolling = False
        Me.lblProgress.Text = "正在导入："""" 等N个文件 "
        Me.lblProgress.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnAbrot
        '
        Me.btnAbrot.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnAbrot.BackColor = System.Drawing.Color.Transparent
        Me.btnAbrot.BindingForm = Nothing
        Me.btnAbrot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAbrot.Location = New System.Drawing.Point(714, 8)
        Me.btnAbrot.Name = "btnAbrot"
        Me.btnAbrot.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnAbrot.Selectable = True
        Me.btnAbrot.Size = New System.Drawing.Size(15, 15)
        Me.btnAbrot.TabIndex = 12
        Me.btnAbrot.tbAdriftIconWhenHover = False
        Me.btnAbrot.tbAutoSize = False
        Me.btnAbrot.tbAutoSizeEx = True
        Me.btnAbrot.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_cancel
        Me.btnAbrot.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnAbrot.tbBadgeNumber = 0
        Me.btnAbrot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAbrot.tbEndEllipsis = False
        Me.btnAbrot.tbIconHoldPlace = True
        Me.btnAbrot.tbIconImage = Nothing
        Me.btnAbrot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAbrot.tbIconMore = False
        Me.btnAbrot.tbIconMouseDown = Nothing
        Me.btnAbrot.tbIconMouseHover = Nothing
        Me.btnAbrot.tbIconMouseLeave = Nothing
        Me.btnAbrot.tbIconPlaceText = 2
        Me.btnAbrot.tbIconReadOnly = Nothing
        Me.btnAbrot.tbImageMouseDown = Nothing
        Me.btnAbrot.tbImageMouseHover = Nothing
        Me.btnAbrot.tbImageMouseLeave = Nothing
        Me.btnAbrot.tbProgressValue = 50
        Me.btnAbrot.tbReadOnly = False
        Me.btnAbrot.tbReadOnlyText = False
        Me.btnAbrot.tbShadow = False
        Me.btnAbrot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAbrot.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAbrot.tbShowDot = False
        Me.btnAbrot.tbShowMoreIconImg = CType(resources.GetObject("btnAbrot.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAbrot.tbShowNew = False
        Me.btnAbrot.tbShowProgress = False
        Me.btnAbrot.tbShowTip = True
        Me.btnAbrot.tbShowToolTipOnButton = False
        Me.btnAbrot.tbSplit = "0,0,0,0"
        Me.btnAbrot.tbText = ""
        Me.btnAbrot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbTextColor = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDisable = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDown = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorHover = System.Drawing.Color.White
        Me.btnAbrot.tbTextMouseDownPlace = 0
        Me.btnAbrot.tbToolTip = ""
        Me.btnAbrot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAbrot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAbrot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.VisibleEx = True
        '
        'pnlFilePath
        '
        Me.pnlFilePath.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.pnlFilePath.Controls.Add(Me.txtFilePath)
        Me.pnlFilePath.Controls.Add(Me.btnGoUp)
        Me.pnlFilePath.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlFilePath.Location = New System.Drawing.Point(216, 0)
        Me.pnlFilePath.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFilePath.Name = "pnlFilePath"
        Me.pnlFilePath.Size = New System.Drawing.Size(740, 33)
        Me.pnlFilePath.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFilePath.TabIndex = 23
        Me.pnlFilePath.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlFilePath.tbShowWatermark = False
        Me.pnlFilePath.tbSplit = "3,3,3,3"
        Me.pnlFilePath.tbWatermark = Nothing
        Me.pnlFilePath.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFilePath.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'txtFilePath
        '
        Me.txtFilePath.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtFilePath.BackColor = System.Drawing.Color.Transparent
        Me.txtFilePath.FilePath = ""
        Me.txtFilePath.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.txtFilePath.Location = New System.Drawing.Point(33, 5)
        Me.txtFilePath.MaxLength = 32767
        Me.txtFilePath.Name = "txtFilePath"
        Me.txtFilePath.ReadOnly = False
        Me.txtFilePath.SearchTipText = "File Path"
        Me.txtFilePath.ShowAddToMyFavorite = False
        Me.txtFilePath.Size = New System.Drawing.Size(698, 25)
        Me.txtFilePath.TabIndex = 2
        Me.txtFilePath.tbAddToMyFavoriteBg = Nothing
        Me.txtFilePath.tbAddToMyFavoriteBgImageState = iTong.Components.ImageState.TwoState
        '
        'btnGoUp
        '
        Me.btnGoUp.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnGoUp.BackColor = System.Drawing.Color.Transparent
        Me.btnGoUp.BindingForm = Nothing
        Me.btnGoUp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnGoUp.Location = New System.Drawing.Point(8, 9)
        Me.btnGoUp.Name = "btnGoUp"
        Me.btnGoUp.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnGoUp.Selectable = True
        Me.btnGoUp.Size = New System.Drawing.Size(17, 16)
        Me.btnGoUp.TabIndex = 1
        Me.btnGoUp.tbAdriftIconWhenHover = False
        Me.btnGoUp.tbAutoSize = False
        Me.btnGoUp.tbAutoSizeEx = False
        Me.btnGoUp.tbBackgroundImage = Global.iTong.My.Resources.Resources.file_btn_4_back
        Me.btnGoUp.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnGoUp.tbBadgeNumber = 0
        Me.btnGoUp.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGoUp.tbEndEllipsis = False
        Me.btnGoUp.tbIconHoldPlace = True
        Me.btnGoUp.tbIconImage = Nothing
        Me.btnGoUp.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGoUp.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGoUp.tbIconMore = False
        Me.btnGoUp.tbIconMouseDown = Nothing
        Me.btnGoUp.tbIconMouseHover = Nothing
        Me.btnGoUp.tbIconMouseLeave = Nothing
        Me.btnGoUp.tbIconPlaceText = 2
        Me.btnGoUp.tbIconReadOnly = Nothing
        Me.btnGoUp.tbImageMouseDown = Nothing
        Me.btnGoUp.tbImageMouseHover = Nothing
        Me.btnGoUp.tbImageMouseLeave = Nothing
        Me.btnGoUp.tbProgressValue = 50
        Me.btnGoUp.tbReadOnly = False
        Me.btnGoUp.tbReadOnlyText = False
        Me.btnGoUp.tbShadow = False
        Me.btnGoUp.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGoUp.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGoUp.tbShowDot = False
        Me.btnGoUp.tbShowMoreIconImg = CType(resources.GetObject("btnGoUp.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGoUp.tbShowNew = False
        Me.btnGoUp.tbShowProgress = False
        Me.btnGoUp.tbShowTip = True
        Me.btnGoUp.tbShowToolTipOnButton = False
        Me.btnGoUp.tbSplit = "0,0,0,0"
        Me.btnGoUp.tbText = ""
        Me.btnGoUp.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGoUp.tbTextColor = System.Drawing.Color.White
        Me.btnGoUp.tbTextColorDisable = System.Drawing.Color.White
        Me.btnGoUp.tbTextColorDown = System.Drawing.Color.White
        Me.btnGoUp.tbTextColorHover = System.Drawing.Color.White
        Me.btnGoUp.tbTextMouseDownPlace = 0
        Me.btnGoUp.tbToolTip = ""
        Me.btnGoUp.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGoUp.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGoUp.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGoUp.VisibleEx = True
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(6, 619)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(946, 18)
        Me.lblMessage.TabIndex = 23
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = False
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = True
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'imlSmall
        '
        Me.imlSmall.ColorDepth = System.Windows.Forms.ColorDepth.Depth32Bit
        Me.imlSmall.ImageSize = New System.Drawing.Size(16, 16)
        Me.imlSmall.TransparentColor = System.Drawing.Color.Transparent
        '
        'imlLarge
        '
        Me.imlLarge.ColorDepth = System.Windows.Forms.ColorDepth.Depth32Bit
        Me.imlLarge.ImageSize = New System.Drawing.Size(75, 75)
        Me.imlLarge.TransparentColor = System.Drawing.Color.Transparent
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(463, 5)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(234, 22)
        Me.pnlLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading.TabIndex = 29
        Me.pnlLoading.tbBackgroundImage = Nothing
        Me.pnlLoading.tbShowWatermark = False
        Me.pnlLoading.tbSplit = "0,0,0,0"
        Me.pnlLoading.tbWatermark = Nothing
        Me.pnlLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(114, -7)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(128, 37)
        Me.lblLoading.TabIndex = 27
        Me.lblLoading.tbAdriftWhenHover = False
        Me.lblLoading.tbAutoEllipsis = False
        Me.lblLoading.tbAutoSize = False
        Me.lblLoading.tbHideImage = False
        Me.lblLoading.tbIconImage = Nothing
        Me.lblLoading.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoading.tbIconPlaceText = 5
        Me.lblLoading.tbShadow = False
        Me.lblLoading.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLoading.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLoading.tbShowScrolling = False
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(20, -36)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(91, 95)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 26
        Me.pbLoading.TabStop = False
        '
        'munOperator
        '
        Me.munOperator.AccessibleDescription = "153x92"
        Me.munOperator.DropShadowEnabled = False
        Me.munOperator.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munOperator.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpen, Me.tsmiExport, Me.tsmiCopyPath})
        Me.munOperator.Name = "munSearch"
        Me.munOperator.Size = New System.Drawing.Size(119, 70)
        Me.munOperator.tbBackColor = System.Drawing.Color.White
        Me.munOperator.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munOperator.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munOperator.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpen
        '
        Me.tsmiOpen.Name = "tsmiOpen"
        Me.tsmiOpen.Size = New System.Drawing.Size(118, 22)
        Me.tsmiOpen.Text = "打开"
        '
        'tsmiExport
        '
        Me.tsmiExport.Name = "tsmiExport"
        Me.tsmiExport.Size = New System.Drawing.Size(118, 22)
        Me.tsmiExport.Text = "导出"
        '
        'tsmiCopyPath
        '
        Me.tsmiCopyPath.Name = "tsmiCopyPath"
        Me.tsmiCopyPath.Size = New System.Drawing.Size(118, 22)
        Me.tsmiCopyPath.Text = "复制路径"
        '
        'frmBackupPreview
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(958, 641)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.tlpMain)
        Me.Controls.Add(Me.pnlLoading)
        Me.Name = "frmBackupPreview"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "Backup Preview"
        Me.Controls.SetChildIndex(Me.pnlLoading, 0)
        Me.Controls.SetChildIndex(Me.tlpMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Spliter2.ResumeLayout(False)
        Me.tlpMain.ResumeLayout(False)
        Me.pnlControl.ResumeLayout(False)
        Me.pnlProgress.ResumeLayout(False)
        Me.pnlFilePath.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.munOperator.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents Spliter2 As System.Windows.Forms.Panel
    Friend WithEvents Spliter1 As System.Windows.Forms.Panel
    Friend WithEvents tlpMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlFilePath As tbPanel
    Friend WithEvents txtFilePath As iTong.tbTextBoxPath
    Friend WithEvents btnGoUp As tbButton
    Friend WithEvents pnlProgress As tbPanel
    Friend WithEvents pbarProgress As tbControlBar
    Friend WithEvents lblProgress As tbLabel
    Friend WithEvents btnAbrot As tbButton
    Friend WithEvents pnlControl As tbPanel
    Friend WithEvents btnViewLargeIcon As tbRadioButton
    Friend WithEvents btnViewList As tbRadioButton
    Friend WithEvents btnExport As tbButton
    Friend WithEvents lblMessage As tbLabel
    Friend WithEvents imlSmall As System.Windows.Forms.ImageList
    Friend WithEvents imlLarge As System.Windows.Forms.ImageList
    Friend WithEvents tvwLeft As iTong.tbTreeViewEx
    Friend WithEvents pnlLoading As tbPanel
    Friend WithEvents lblLoading As tbLabel
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents txtSearchFile As tbSearch
    Friend WithEvents munOperator As tbContextMenuStrip
    Friend WithEvents tsmiOpen As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExport As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiCopyPath As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnView As tbButton
    Friend WithEvents btnDelete As iTong.Components.tbButton
    Friend WithEvents btnRefresh As iTong.Components.tbButton
End Class
