﻿Imports System.IO
Imports System.Data

Imports System.Data.SQLite3
Imports iTong.CoreFoundation
Imports iTong.CoreModule

Public Class BackupSMSDB
    Implements IDisposable

    Private Shared mLocker As New Object
    Private Shared mDictDB As New Dictionary(Of String, BackupSMSDB)
    Private mConn As SQLiteConnection = Nothing
    Private mTempFolder As String = ""
    Private mDbPathOnBackupFolder As String = ""
    Private mDbPathOnTempFolder As String = ""
    Private mBackObject As BackupInfo = Nothing
    Public mDevice As iPhoneDevice = Nothing
    Private mLoadGroupSucceed As Boolean = True         '加载数据失败有可能是加密备份的数据库



#Region "- 注册函数-"

    <System.Data.SQLite.SQLiteFunction(Arguments:=1, FuncType:=System.Data.SQLite.FunctionType.Scalar, Name:="read")> _
    Private Class Read
        Inherits System.Data.SQLite.SQLiteFunction
    End Class

    Private Sub RegFunc()
        System.Data.SQLite.SQLiteFunction.RegisterFunction(GetType(Read))
    End Sub

#End Region

    Public ReadOnly Property TempPath() As String
        Get
            Return mTempFolder
        End Get
    End Property

    Public ReadOnly Property IsPwdBackup() As Boolean
        Get
            '判断是否加密备份的数据库
            '1.文件大于0
            '2.加载数据库失败
            Dim blnReturn As Boolean = False
            Try
                If Not Me.mLoadGroupSucceed AndAlso File.Exists(Me.mDbPathOnTempFolder) Then
                    Dim info As New FileInfo(Me.mDbPathOnTempFolder)
                    If info.Length > 0 Then
                        blnReturn = True
                    End If
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "BackupSMSDB_IsPwdBackup")
            End Try
            Return blnReturn
        End Get
    End Property

    Public Sub New(ByVal strDBFilePath As String, ByVal backObj As BackupInfo, ByVal device As iPhoneDevice)
        Me.mTempFolder = Folder.GetTempFilePath()
        Folder.CheckFolder(Me.mTempFolder)
        Me.mDbPathOnBackupFolder = strDBFilePath
        Me.mBackObject = backObj
        Me.mDevice = device
    End Sub

    Public Shared Function Instance(ByVal strDBFilePath As String, ByVal backObj As BackupInfo, Optional ByVal device As iPhoneDevice = Nothing) As BackupSMSDB
        Dim strID As String = strDBFilePath.Replace("/", "").Replace("\", "").Replace(" ", "").Replace(".", "").Replace(":", "")
        Try
            SyncLock mLocker
                If Not String.IsNullOrEmpty(strID) AndAlso mDictDB.ContainsKey(strID) Then
                    Dim db As BackupSMSDB = mDictDB(strID)
                    If db Is Nothing Then
                        mDictDB.Remove(strID)

                        db = New BackupSMSDB(strDBFilePath, backObj, device)
                        mDictDB.Add(strID, db)
                    End If

                Else
                    Dim db As New BackupSMSDB(strDBFilePath, backObj, device)
                    mDictDB.Add(strID, db)

                End If
            End SyncLock
        Catch ex As Exception
        End Try
        Return mDictDB(strID)
    End Function

    Private Function Reload() As Boolean
        Dim blnResult As Boolean = False
        Dim strMsmDBName As String = "sms.db"
        If Me.mDbPathOnBackupFolder.Length > 0 Then
            strMsmDBName = Path.GetFileName(Me.mDbPathOnBackupFolder)
        End If

        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            Me.mDbPathOnTempFolder = Path.Combine(Me.mTempFolder, strMsmDBName)

            Dim strDBshm As String = Me.mDbPathOnTempFolder.Replace(String.Format("\{0}", strMsmDBName), String.Format("\{0}-shm", strMsmDBName))
            Dim strDBwal As String = Me.mDbPathOnTempFolder.Replace(String.Format("\{0}", strMsmDBName), String.Format("\sms.db-wal", strMsmDBName))
            Try
                If File.Exists(Me.mDbPathOnTempFolder) Then
                    File.Delete(Me.mDbPathOnTempFolder)
                End If
                If File.Exists(strDBshm) Then
                    File.Delete(strDBshm)
                End If

                If File.Exists(strDBwal) Then
                    File.Delete(strDBwal)
                End If
                If Me.mDevice Is Nothing Then
                    File.Copy(Me.mDbPathOnBackupFolder, Me.mDbPathOnTempFolder)
                    File.Copy(Me.mDbPathOnBackupFolder.ToLower.Replace(String.Format("\{0}", strMsmDBName), String.Format("\{0}-shm", strMsmDBName)), strDBshm)
                    File.Copy(Me.mDbPathOnBackupFolder.ToLower.Replace(String.Format("\{0}", strMsmDBName), String.Format("\{0}-wal", strMsmDBName)), strDBwal)
                Else
                    Try
                        If Directory.Exists(Me.mTempFolder) Then
                            Directory.Delete(Me.mTempFolder, True)
                        End If
                        Directory.CreateDirectory(Me.mTempFolder)
                        My.Computer.FileSystem.CopyDirectory(Path.GetDirectoryName(Me.mDbPathOnBackupFolder), Me.mTempFolder)

                    Catch ex As Exception
                    End Try
                End If
            Catch ex As Exception
            End Try
            If File.Exists(Me.mDbPathOnTempFolder) Then

                Me.mConn = SQLiteClass3.CreateConnectionFromFile(Me.mDbPathOnTempFolder)
                If Me.mConn IsNot Nothing Then
                    blnResult = True
                    Me.RegFunc()
                End If
            End If
        Catch ex As Exception
        End Try
        Return blnResult
    End Function

    Public Function GetAll() As List(Of SMSGroup)
        '加载数据文件
        Me.Reload()
        '取得所有短信信息

        Dim listInfo As List(Of SMSGroup) = Me.LoadGroup()

        Me.Dispose()
        Return listInfo
    End Function

    Private Function LoadGroup() As List(Of SMSGroup)
        Dim listInfo As New List(Of SMSGroup)
        Try
            If Me.GetProductVersion = 0 Then
                listInfo = Me.LoadGroup_6X()
                If listInfo.Count <= 0 Then
                    listInfo = Me.LoadGroup_5X()
                End If
            Else
                If Me.GetProductVersion >= 6 Then
                    listInfo = Me.LoadGroup_6X()
                Else
                    listInfo = Me.LoadGroup_5X()
                End If
            End If
        Catch ex As Exception
        End Try
        Return listInfo
    End Function

    Private Function GetProductVersion() As Integer
        Dim intReturn As Integer = 0
        Try
            Dim strVal As String = ""
            If Me.mBackObject IsNot Nothing Then
                strVal = Me.mBackObject.ProductVersion
            End If
            If Me.mDevice IsNot Nothing Then
                strVal = Me.mDevice.ProductVersion
            End If
            intReturn = Val(strVal)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "BackupSMSDB_GetProductVersion")
        End Try
        Return intReturn
    End Function

    Private Function LoadGroup_5X() As List(Of SMSGroup)
        Dim listInfo As New List(Of SMSGroup)
        Dim dict As New Dictionary(Of String, SMSGroup)

        Try
            Me.mLoadGroupSucceed = True
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = "SELECT * FROM group_member"
                Dim strError As String = ""
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn, strError)
                If Not String.IsNullOrEmpty(strError) Then
                    Me.mLoadGroupSucceed = False
                End If
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim groupID As String = Common.GetValue(Of String)(row("group_id"), String.Empty)
                        Dim phoneNumber As String = Common.GetValue(Of String)(row("address"), String.Empty)
                        Dim groupInfo As SMSGroup = Nothing

                        If dict.ContainsKey(groupID) Then
                            groupInfo = dict(groupID)
                            groupInfo.PhoneNumber &= "," & phoneNumber
                            Continue For
                        Else
                            groupInfo = New SMSGroup()
                            groupInfo.GroupROWID = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                            groupInfo.GroupID = Common.GetValue(Of String)(row("group_id"), String.Empty)
                            groupInfo.Country = Common.GetValue(Of String)(row("country"), String.Empty)
                            groupInfo.PhoneNumber = phoneNumber
                            groupInfo.Messages = Me.LoadMessage_5X(groupInfo)

                            dict(groupID) = groupInfo
                        End If

                        'If groupInfo.PhoneNumber.Contains("2996") Then
                        '    Dim a As String = ""
                        'End If

                        Dim group As SMSGroup = Me.GetGroupByPhoneNumber(groupInfo.PhoneNumber, listInfo)
                        If group Is Nothing Then
                            listInfo.Add(groupInfo)
                        Else
                            For Each Item As SMSMessage In groupInfo.Messages
                                group.Messages.Add(Item)
                            Next
                        End If
                    Next
                End If

                '加载iMessage的消息。iPad 5.1.1发送iMessage逻辑
                strSQL = "SELECT * FROM madrid_chat"
                dt = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim groupInfo As New SMSGroup
                        groupInfo.GroupROWID = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        groupInfo.GroupID = Common.GetValue(Of String)(row("account_id"), String.Empty)
                        groupInfo.PhoneNumber = Common.GetValue(Of String)(row("chat_identifier"), String.Empty)
                        groupInfo.Country = "cn"
                        groupInfo.Messages = Me.LoadiMessage_5X(groupInfo)

                        If groupInfo.PhoneNumber.Contains("2996") Then
                            Dim a As String = ""
                        End If

                        Dim group As SMSGroup = Me.GetGroupByPhoneNumber(groupInfo.PhoneNumber, listInfo)
                        If group Is Nothing Then
                            listInfo.Add(groupInfo)
                        Else
                            For Each Item As SMSMessage In groupInfo.Messages
                                group.Messages.Add(Item)
                            Next
                        End If
                    Next
                End If
            End If
        Catch ex As Exception
        End Try

        '排序
        Dim listInfoSort As New List(Of SMSGroup)
        For Each Item As SMSGroup In listInfo
            If listInfoSort.Count <= 0 Then
                listInfoSort.Add(Item)
            Else
                Dim index As Integer = 0
                For Each Item1 As SMSGroup In listInfoSort
                    If Item1.GroupDate < Item.GroupDate Then
                        Exit For
                    End If
                    index += 1
                Next

                listInfoSort.Insert(index, Item)
            End If
        Next
        listInfo = listInfoSort
        Return listInfo
    End Function

    Private Function LoadGroup_6X() As List(Of SMSGroup)
        Dim listInfo As New List(Of SMSGroup)
        Dim dict As New Dictionary(Of String, SMSGroup)

        Try
            Me.mLoadGroupSucceed = True
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = "SELECT * FROM chat"
                Dim strError As String = ""
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn, strError)
                If Not String.IsNullOrEmpty(strError) Then
                    Me.mLoadGroupSucceed = False
                End If
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim groupInfo As New SMSGroup
                        groupInfo.GroupROWID = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        groupInfo.GroupID = Common.GetValue(Of String)(row("chat_identifier"), String.Empty)
                        groupInfo.PhoneNumber = Common.GetValue(Of String)(row("chat_identifier"), String.Empty)
                        groupInfo.Country = "cn"
                        groupInfo.Messages = Me.LoadMessage_6X(groupInfo)

                        Dim group As SMSGroup = Me.GetGroupByPhoneNumber(groupInfo.PhoneNumber, listInfo)
                        If group Is Nothing Then
                            listInfo.Add(groupInfo)
                        End If
                    Next
                End If
            End If
        Catch ex As Exception
        End Try

        '排序
        Dim listInfoSort As New List(Of SMSGroup)
        For Each Item As SMSGroup In listInfo
            If listInfoSort.Count <= 0 Then
                listInfoSort.Add(Item)
            Else
                Dim index As Integer = 0
                For Each Item1 As SMSGroup In listInfoSort
                    If Item1.GroupDate < Item.GroupDate Then
                        Exit For
                    End If
                    index += 1
                Next

                listInfoSort.Insert(index, Item)
            End If
        Next
        listInfo = listInfoSort
        Return listInfo
    End Function

    Private Function GetGroupByPhoneNumber(ByVal strPhoneNumber As String, ByVal listInfo As List(Of SMSGroup)) As SMSGroup
        Dim group As SMSGroup = Nothing
        For Each Item As SMSGroup In listInfo
            If Item.PhoneNumber = strPhoneNumber Then
                group = Item
                Exit For
            End If
        Next

        Return group
    End Function

    Private Function LoadMessage_5X(ByRef group As SMSGroup) As List(Of SMSMessage)
        Dim listInfo As New List(Of SMSMessage)
        Try
            If Me.mConn IsNot Nothing Then

                Dim strSQL As String = String.Format("SELECT A.* FROM message A " & _
                                                    "Left Join group_member B ON B.group_id=A.group_id AND B.address=A.address " & _
                                                    "WHERE A.address like ""{0}"" ORDER BY A.date", Me.FormatAddressLike(group.PhoneNumber))
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                Dim strSqlAtt As String = "SELECT * FROM msg_pieces"
                Dim dtAtt As DataTable = SQLiteClass3.ExecuteSQL(strSqlAtt, Me.mConn)

                'If group.PhoneNumber.Contains("135") AndAlso group.PhoneNumber.Contains("7054") Then
                '    Dim a As String = ""
                'End If

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim smsInfo As New SMSMessage
                        smsInfo.MessageROWID = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        smsInfo.GroupID = Common.GetValue(Of String)(row("group_id"), String.Empty)
                        smsInfo.Country = Common.GetValue(Of String)(row("country"), String.Empty)
                        smsInfo.PhoneNumber = Common.GetValue(Of String)(row("address"), String.Empty)

                        If row("date") IsNot Nothing AndAlso Not TypeOf row("date") Is System.DBNull Then
                            Dim longDate As Long = 0
                            Dim dateStr As String = CType(row("date"), String)
                            If Not String.IsNullOrEmpty(dateStr) Then
                                Dim index As Integer = dateStr.IndexOf(".")
                                If index > -1 Then
                                    dateStr = dateStr.Substring(0, index)
                                End If
                                If Long.TryParse(dateStr, longDate) Then
                                    smsInfo.CreateDate = Common.ConvertToPcTime(longDate, Date.Parse("1970-01-01 08:00"))
                                End If
                            End If
                        End If

                        If group.GroupDate < smsInfo.CreateDate Then
                            group.GroupDate = smsInfo.CreateDate
                        End If

                        smsInfo.Content = Common.GetValue(Of String)(row("text"), String.Empty)
                        Dim strFalg As String = Common.GetValue(Of String)(row("flags"), String.Empty)
                        smsInfo.IsSend = IIf(strFalg = 3 OrElse strFalg = 16387, True, False)
                        smsInfo.Read = IIf(Common.GetValue(Of String)(row("read"), String.Empty) = 1, True, False)

                        If smsInfo.Content.Length <= 0 Then
                            Me.GetContactFormText(smsInfo, dtAtt)
                        End If
                        listInfo.Add(smsInfo)
                    Next
                End If
            End If
        Catch ex As Exception
        End Try
        Return listInfo
    End Function

    Private Function FormatAddressLike(ByVal strPhoneNumber As String) As String
        Dim strReturn As String = "%"
        If strPhoneNumber.Trim.Length > 0 Then
            Dim strNumberSplit() As String = strPhoneNumber.Split(New Char() {"-", " "})
            For Each Item As String In strNumberSplit
                strReturn &= Item.Replace("+86", "") & "%"
            Next
        End If

        strReturn = strReturn.TrimEnd("%")
        Return strReturn
    End Function

    Private Function LoadiMessage_5X(ByRef group As SMSGroup) As List(Of SMSMessage)
        Dim listInfo As New List(Of SMSMessage)
        Try
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = String.Format("SELECT * FROM message  " & _
                                                    "WHERE madrid_handle=""{0}"" ORDER BY date", group.PhoneNumber)

                Dim strSqlAtt As String = "SELECT * FROM madrid_attachment"
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                Dim dtAtt As DataTable = SQLiteClass3.ExecuteSQL(strSqlAtt, Me.mConn)

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim smsInfo As New SMSMessage
                        smsInfo.MessageROWID = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        smsInfo.GroupID = Common.GetValue(Of String)(row("madrid_account_guid"), String.Empty)
                        smsInfo.Country = "cn"
                        smsInfo.PhoneNumber = Common.GetValue(Of String)(row("madrid_handle"), String.Empty)

                        If row("date") IsNot Nothing AndAlso Not TypeOf row("date") Is System.DBNull Then
                            Dim longDate As Long = 0
                            Dim dateStr As String = CType(row("date"), String)
                            If Not String.IsNullOrEmpty(dateStr) Then
                                Dim index As Integer = dateStr.IndexOf(".")
                                If index > -1 Then
                                    dateStr = dateStr.Substring(0, index)
                                End If
                                If Long.TryParse(dateStr, longDate) Then
                                    smsInfo.CreateDate = Common.ConvertToPcTime(longDate, Date.Parse("1970-01-01 08:00"))
                                End If
                            End If
                        End If

                        If group.GroupDate < smsInfo.CreateDate Then
                            group.GroupDate = smsInfo.CreateDate
                        End If

                        smsInfo.Content = Common.GetValue(Of String)(row("text"), String.Empty).Trim(Me.GetSpecialChat)
                        smsInfo.IsSend = IIf(Common.GetValue(Of String)(row("madrid_flags"), String.Empty) = 36869, True, False)
                        smsInfo.Read = IIf(Common.GetValue(Of String)(row("madrid_date_read"), String.Empty) >= 0, True, False)

                        '加载iMessage图片
                        Me.LoadiMessagePic_5X(row, dtAtt, smsInfo)

                        listInfo.Add(smsInfo)
                    Next
                End If
            End If
        Catch ex As Exception
        End Try
        Return listInfo
    End Function

    Private Function GetSpecialChat() As String
        Dim bytess() As Byte = New Byte() {239, 191, 188}
        Return System.Text.Encoding.UTF8.GetString(bytess)
    End Function

    Private Sub LoadiMessagePic_5X(ByVal row As DataRow, ByVal dtAtt As DataTable, ByRef smsInfo As SMSMessage)
        Dim att As Byte() = Common.GetValue(row("madrid_attachmentInfo"), New Byte() {})
        If att.Length > 0 Then
            'Dim arrGuid(36 - 1) As Byte
            'Array.Copy(att, att.Length - 38, arrGuid, 0, arrGuid.Length)
            'Dim a As String = System.Text.Encoding.ASCII.GetString(arrGuid)

            Dim pic As New SMSMessagePic()
            pic.PicID = Me.GetPicAddressString(att)

            Dim strFilter As String = String.Format("attachment_guid='{0}'", pic.PicID)
            Dim arrRows() As DataRow = dtAtt.Select(strFilter, "ROWID ASC")
            If arrRows IsNot Nothing AndAlso arrRows.Length > 0 Then
                Dim arrType() As String = Common.GetValue(arrRows(0)("mime_type"), "").Split("/")

                pic.Type = arrType(arrType.Length - 1)
                If Me.mDevice Is Nothing Then
                    Dim pathOnPhone As String = Common.GetValue(arrRows(0)("filename"), "")
                    pic.PicPath = pathOnPhone
                    pic.PicPathOnPC = Path.Combine(mTempFolder, pathOnPhone.Replace("/", "\"))
                    pic.PartID = "-1"
                Else
                    pic.PicPath = Path.Combine(mTempFolder, Common.GetValue(arrRows(0)("filename"), "").Replace("/var/mobile/Library/SMS/", "").Replace("/", "\"))
                    pic.PicPathOnPC = pic.PicPath
                    pic.PartID = "-1"
                End If

                pic.Class = ImageVideo.Image
                smsInfo.Content &= String.Format("#Pic{0}#", 1)

                If smsInfo.Pics Is Nothing Then
                    smsInfo.Pics = New List(Of SMSMessagePic)
                End If
                smsInfo.Pics.Add(pic)
            End If

        End If
    End Sub

    Private Sub GetContactFormTextByiMessage(ByRef messageInfo As SMSMessage)
        Dim strReturn As String = ""
        Try

        Catch ex As Exception
        End Try
        messageInfo.Content = strReturn
    End Sub

    '短信内容为空的时候取Text里面的数据

    Private Sub GetContactFormText(ByRef messageInfo As SMSMessage, ByVal dtAtt As DataTable)
        Dim strReturn As String = ""
        Try
            If Me.mConn IsNot Nothing Then

                Dim strFilter As String = String.Format("message_id={0} ", messageInfo.MessageROWID)
                Dim arrRows() As DataRow = dtAtt.Select(strFilter, "content_id ASC")

                'Dim strSQL As String = String.Format("SELECT * FROM msg_pieces WHERE message_id={0} Order by content_id", messageInfo.MessageROWID)
                'Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                If arrRows IsNot Nothing AndAlso arrRows.Length > 0 Then
                    For Each row As DataRow In arrRows
                        Dim strType As String = Common.GetValue(Of String)(row("content_type"), "")

                        If strType.StartsWith("text", StringComparison.OrdinalIgnoreCase) Then
                            Dim buffer() As Byte = Common.GetValue(Of Byte())(row("data"), Nothing)
                            strReturn &= System.Text.ASCIIEncoding.UTF8.GetString(buffer) & vbCrLf
                        Else
                            Try
                                Dim strPic As String = Common.GetValue(Of String)(row("part_id"), "")
                                Dim pic As New SMSMessagePic
                                Dim arrType() As String = strType.Split("/")
                                Dim buffer() As Byte = Common.GetValue(Of Byte())(row("data"), Nothing)
                                pic.Type = arrType(arrType.Length - 1)
                                pic.PicID = messageInfo.MessageROWID
                                pic.PicPath = String.Format("{0}-{1}.{2}", pic.PicID, strPic, IIf(pic.Type = "jpeg", "jpg", pic.Type))
                                pic.PartID = strPic

                                If strType.Trim.Length > 0 Then
                                    Dim strlocation As String = Common.GetValue(Of String)(row("content_loc"), "")
                                    If strlocation.Length <= 0 Then
                                        Continue For
                                    End If
                                    If strType.StartsWith("image") Then
                                        pic.Picture = Me.GetPicFormData(buffer)
                                        strReturn &= String.Format("#Pic{0}#", strPic) & vbCrLf
                                        pic.Class = ImageVideo.Image

                                    ElseIf strType.StartsWith("video") Then
                                        strReturn &= String.Format("#Video{0}#", strPic) & vbCrLf
                                        pic.Class = ImageVideo.Video

                                    End If

                                    If messageInfo.Pics Is Nothing Then
                                        messageInfo.Pics = New List(Of SMSMessagePic)
                                    End If
                                    messageInfo.Pics.Add(pic)

                                End If
                            Catch ex As Exception
                            End Try
                        End If
                    Next
                End If
            End If
        Catch ex As Exception
        End Try
        messageInfo.Content = strReturn
    End Sub

    Private Function GetPicFormData(ByVal buffer() As Byte)
        '从资料库里取图片。

        Dim img As Image = Nothing
        Try
            If buffer IsNot Nothing Then
                Dim stream As New MemoryStream(buffer)
                img = Image.FromStream(stream, True)
            End If
        Catch ex As Exception
        End Try
        Return img
    End Function

    Private Function LoadMessage_6X(ByRef group As SMSGroup) As List(Of SMSMessage)
        Dim listInfo As New List(Of SMSMessage)
        Try
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = String.Format("SELECT A.* FROM message A " & _
                                                     "Left Join chat_message_join B ON A.ROWID=B.message_id " & _
                                                     "Left Join chat C              ON C.ROWID=B.chat_id " & _
                                                     "WHERE C.chat_identifier like '{0}' ORDER BY date", Me.FormatAddressLike(group.PhoneNumber))

                Dim strSqlAtt As String = "SELECT ROWID,mime_type,filename FROM attachment"
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                Dim dtAtt As DataTable = SQLiteClass3.ExecuteSQL(strSqlAtt, Me.mConn)

                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim smsInfo As New SMSMessage
                        smsInfo.MessageROWID = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        smsInfo.GroupID = Common.GetValue(Of String)(row("account_guid"), String.Empty)
                        smsInfo.Country = Common.GetValue(Of String)(row("country"), String.Empty)
                        smsInfo.PhoneNumber = group.PhoneNumber 'Common.GetValue(Of String)(row("address"), String.Empty)

                        If row("date") IsNot Nothing AndAlso Not TypeOf row("date") Is System.DBNull Then
                            Dim longDate As Long = 0
                            Dim dateStr As String = CType(row("date"), String)
                            If Not String.IsNullOrEmpty(dateStr) Then
                                Dim index As Integer = dateStr.IndexOf(".")
                                If index > -1 Then
                                    dateStr = dateStr.Substring(0, index)
                                End If

                                '兼容ios11短信时间
                                If Me.GetProductVersion >= 11 Then
                                    dateStr = dateStr.Substring(0, 9)
                                End If

                                If Long.TryParse(dateStr, longDate) Then
                                    smsInfo.CreateDate = Common.ConvertToPcTime(longDate, DateTime.Parse("2001-01-01 08:00"))
                                End If
                            End If
                        End If

                        If group.GroupDate < smsInfo.CreateDate Then
                            group.GroupDate = smsInfo.CreateDate
                        End If

                        smsInfo.Content = Common.GetValue(Of String)(row("text"), String.Empty).Trim(Me.GetSpecialChat)
                        smsInfo.IsSend = IIf(Common.GetValue(Of String)(row("is_sent"), String.Empty) = 1, True, False)
                        smsInfo.Read = IIf(Common.GetValue(Of String)(row("is_read"), String.Empty) = 1, True, False)
                        If smsInfo.IsSend = True Then
                            smsInfo.IsSend = IIf(Common.GetValue(Of String)(row("is_from_me"), String.Empty) = 1, True, False)
                        End If


                        Dim hasImage As Integer = Common.GetValue(Of Long)(row("cache_has_attachments"), 0)

                        If hasImage = 1 Then
                            Dim strServer As String = Common.GetValue(Of String)(row("service"), String.Empty)
                            Me.LoadiMessagePic_6X(row, dtAtt, smsInfo, strServer)
                        End If
                        listInfo.Add(smsInfo)
                    Next
                End If
            End If
        Catch ex As Exception
        End Try
        Return listInfo
    End Function

    Private Function ProductVersion() As String
        Dim strVersion As String = "0"
        If Me.mDevice IsNot Nothing Then
            strVersion = Me.mDevice.ProductVersion
        Else
            If Me.mBackObject IsNot Nothing Then
                strVersion = Me.mBackObject.ProductVersion
            End If
        End If
        Return strVersion
    End Function

    Private Sub LoadiMessagePic_6X(ByVal row As DataRow, ByVal dtAtt As DataTable, ByRef smsInfo As SMSMessage, ByVal server As String)
        Dim att As Byte() = Common.GetValue(row("attributedBody"), New Byte() {})
        'Dim arrGuid(36 - 1) As Byte
        'Try
        '    If Val(Me.ProductVersion.Substring(0, 1)) >= 7 Then
        '        If server.ToLower = "sms" Then
        '            Array.Copy(att, &HFB, arrGuid, 0, arrGuid.Length)
        '        Else
        '            Array.Copy(att, &H10A, arrGuid, 0, arrGuid.Length)
        '        End If

        '    Else
        '        If server.ToLower = "sms" Then
        '            Array.Copy(att, &HCE, arrGuid, 0, arrGuid.Length)
        '        Else
        '            Array.Copy(att, &HC8, arrGuid, 0, arrGuid.Length)
        '        End If
        '    End If
        'Catch ex As Exception
        'End Try
        'Dim b As String = System.Text.Encoding.ASCII.GetString(arrGuid)
        Try

       
        Dim pic As New SMSMessagePic()
        pic.PicID = Me.GetPicAddressString(att)

        Dim strFilter As String = String.Format("guid='{0}'", pic.PicID)
        Dim arrRows() As DataRow = dtAtt.Select(strFilter, "ROWID ASC")
        If arrRows IsNot Nothing AndAlso arrRows.Length > 0 Then
            Dim arrType() As String = Common.GetValue(arrRows(0)("mime_type"), "").Split("/")

            pic.Type = arrType(arrType.Length - 1)

            If Me.mDevice Is Nothing Then
                Dim pathOnPhone As String = Common.GetValue(arrRows(0)("filename"), "")
                pic.PicPath = pathOnPhone.Replace("~/Library/SMS/", "/var/mobile/Library/SMS/")
                pic.PicPathOnPC = Path.Combine(mTempFolder, pathOnPhone.Replace("~/Library/SMS/", "").Replace("/", "\"))
                pic.PartID = "-1"
            Else
                pic.PicPath = Path.Combine(mTempFolder, Common.GetValue(arrRows(0)("filename"), "").Replace("~/Library/SMS/", "").Replace("/", "\"))
                pic.PicPathOnPC = pic.PicPath
                pic.PartID = "-1"
            End If

            pic.Class = ImageVideo.Image
            smsInfo.Content &= String.Format("#Pic{0}#", 1)

            If smsInfo.Pics Is Nothing Then
                smsInfo.Pics = New List(Of SMSMessagePic)
            End If
                smsInfo.Pics.Add(pic)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "BavkupSMSDB_LoadiMessagePic_6X")
        End Try
    End Sub

    Private Function GetPicAddressString(ByVal att As Byte()) As String
        Dim strReturn As String = ""
        Try
            If att Is Nothing OrElse att.Length <= 0 Then
                Return strReturn
            End If
            Dim strKey As String = "GUIDAttributeName"
            Dim strArr As String = System.Text.Encoding.ASCII.GetString(att)
            Dim indexGUI As Integer = strArr.IndexOf(strKey)
            If indexGUI >= 0 Then
                Dim intStart As Integer = indexGUI + strKey.Length
                For Each chr As Char In strArr.ToCharArray(intStart, strArr.Length - intStart)
                    If (chr >= "0"c AndAlso chr <= "9"c) OrElse (chr >= "a"c AndAlso chr <= "f"c) OrElse (chr >= "A"c AndAlso chr <= "F"c) OrElse chr = "-" Then
                        strReturn &= chr
                        If strReturn.Length >= 36 Then
                            Exit For
                        End If
                    End If
                Next
            End If
            If strReturn.Length = 0 Then
                strKey = "NSString"
                strArr = System.Text.Encoding.ASCII.GetString(att)
                indexGUI = strArr.IndexOf(strKey)
                If indexGUI >= 0 Then
                    Dim intStart As Integer = indexGUI + strKey.Length
                    For Each chr As Char In strArr.ToCharArray(intStart, strArr.Length - intStart)
                        If (chr >= "0"c AndAlso chr <= "9"c) OrElse (chr >= "a"c AndAlso chr <= "f"c) OrElse (chr >= "A"c AndAlso chr <= "F"c) OrElse chr = "-" Then
                            strReturn &= chr
                            If strReturn.Length >= 36 Then
                                Exit For
                            End If
                        End If
                    Next
                End If

            End If
        Catch ex As Exception
        End Try
        Return strReturn
    End Function

    Public Function Delete(ByVal lstSMSIds As List(Of String)) As Integer
        Dim intReturn As String = -1
        If Me.mConn IsNot Nothing Then
            intReturn = 0
            Try
                Dim ids As String = ""
                For Each Item As String In lstSMSIds
                    If ids.Length > 0 Then
                        ids &= "," & Item
                    Else
                        ids = Item
                    End If
                Next
                ids = String.Format("({0})", ids)

                Dim a As String = ""
                Dim strSQL As String = "DELETE FROM message WHERE ROWID in " & ids
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, Nothing, a)

                a = ""
            Catch ex As Exception
                intReturn = -1
            End Try
        End If
        Return intReturn
    End Function

    Public Function Save() As Boolean
        Dim blnReturn As Boolean = True
        Try
            Me.mConn.Close()
            File.Copy(Me.mDbPathOnTempFolder, Me.mDbPathOnBackupFolder, True)
            Me.Reload()
        Catch ex As Exception
            blnReturn = False
        End Try

        Return blnReturn
    End Function

    Public Sub Close()
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If
        Catch ex As Exception
        End Try
    End Sub

#Region " IDisposable Support "

    Private disposedValue As Boolean = False        ' 检测冗余的调用

    ' IDisposable
    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If

            ' TODO: 释放共享的非托管资源
        End If
        Me.disposedValue = True
    End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

#End Region

    Public Shared Function GetAloneBackupMSM(ipDevice As iPhoneDevice, ByRef strAloneBackupPath As String, Optional ByRef isUnsaved As Boolean = False) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim strPath As String = Path.Combine(Folder.AloneSMSBackupsFolder, ipDevice.Identifier)
            If Not Directory.Exists(strPath) Then
                isUnsaved = True
                GoTo Do_Exit
            End If

            Dim arrFileInfo As FileInfo() = New DirectoryInfo(strPath).GetFiles()
            If arrFileInfo.Length = 0 Then
                isUnsaved = True
                GoTo Do_Exit
            End If

            For Each item As FileInfo In arrFileInfo
                If Path.GetExtension(item.FullName).ToLower() <> ".db" Then
                    Continue For
                End If
                If strAloneBackupPath.Length = 0 Then
                    strAloneBackupPath = item.FullName
                    Continue For
                End If
                If DateTime.Compare(item.LastWriteTime, New FileInfo(strAloneBackupPath).LastWriteTime) < 0 Then
                    Continue For
                End If
                strAloneBackupPath = item.FullName
            Next
            isRelust = True

Do_Exit:

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetAloneBackupMSM")
        End Try
        Return isRelust
    End Function

End Class