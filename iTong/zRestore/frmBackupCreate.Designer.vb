﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmBackupCreate
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBackupCreate))
        Me.btnCancel = New iTong.Components.tbButton()
        Me.btnOK = New iTong.Components.tbButton()
        Me.rdbIncrem = New iTong.Components.tbRadioButton()
        Me.lblBackupType = New System.Windows.Forms.Label()
        Me.rdbNew = New iTong.Components.tbRadioButton()
        Me.txtiBackupFolder = New iTong.Components.tbTextBox()
        Me.btnBacbupFolder = New iTong.Components.tbButton()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.pnlCount = New System.Windows.Forms.Panel()
        Me.lblOpenTutorial = New System.Windows.Forms.LinkLabel()
        Me.munOperator = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpenFolder = New System.Windows.Forms.ToolStripMenuItem()
        Me.pbarProgress = New iTong.Components.tbControlBar()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.btnSplitCommon = New iTong.Components.tbButton()
        Me.pnlContainerEx = New iTong.Components.tbPanel()
        Me.lblBackupPwdMsg = New iTong.Components.tbLabel()
        Me.llblDeletePwd = New System.Windows.Forms.LinkLabel()
        Me.lblPath = New iTong.Components.tbLabel()
        Me.pnlCount.SuspendLayout()
        Me.munOperator.SuspendLayout()
        Me.pnlContainerEx.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(744, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(720, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(696, 0)
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.Location = New System.Drawing.Point(675, 498)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(65, 23)
        Me.btnCancel.TabIndex = 71
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEnableEnter = True
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbIconSize = New System.Drawing.Size(0, 0)
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbProgressValue = 50
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowMoreIconImg = CType(resources.GetObject("btnCancel.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowProgress = False
        Me.btnCancel.tbShowTip = True
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbUpToDownWhenCenter = False
        Me.btnCancel.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(600, 498)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 70
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEnableEnter = True
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbIconSize = New System.Drawing.Size(0, 0)
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbUpToDownWhenCenter = False
        Me.btnOK.VisibleEx = True
        '
        'rdbIncrem
        '
        Me.rdbIncrem.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rdbIncrem.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbIncrem.BackColor = System.Drawing.Color.Transparent
        Me.rdbIncrem.Checked = True
        Me.rdbIncrem.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbIncrem.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbIncrem.Location = New System.Drawing.Point(29, 72)
        Me.rdbIncrem.Name = "rdbIncrem"
        Me.rdbIncrem.Size = New System.Drawing.Size(709, 22)
        Me.rdbIncrem.TabIndex = 72
        Me.rdbIncrem.TabStop = True
        Me.rdbIncrem.tbAdriftIconWhenHover = False
        Me.rdbIncrem.tbAutoSize = False
        Me.rdbIncrem.tbAutoSizeEx = False
        Me.rdbIncrem.tbBadgeNumber = 0
        Me.rdbIncrem.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbIncrem.tbEnableToolTip = False
        Me.rdbIncrem.tbIconCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbIncrem.tbIconCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbIncrem.tbIconCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbIncrem.tbIconHoldPlace = True
        Me.rdbIncrem.tbIconImage = Nothing
        Me.rdbIncrem.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbIncrem.tbIconImageState = iTong.Components.ImageState.OneState
        Me.rdbIncrem.tbIconMore = False
        Me.rdbIncrem.tbIconMoreImage = CType(resources.GetObject("rdbIncrem.tbIconMoreImage"), System.Drawing.Image)
        Me.rdbIncrem.tbIconPlaceText = 5
        Me.rdbIncrem.tbIconReadOnly = Nothing
        Me.rdbIncrem.tbIconReadOnlyLight = Nothing
        Me.rdbIncrem.tbIconUnCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbIncrem.tbIconUnCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbIncrem.tbIconUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbIncrem.tbImageBackground = Nothing
        Me.rdbIncrem.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.rdbIncrem.tbImageCheckedMouseDown = Nothing
        Me.rdbIncrem.tbImageCheckedMouseHover = Nothing
        Me.rdbIncrem.tbImageCheckedMouseLeave = Nothing
        Me.rdbIncrem.tbImageUnCheckedMouseDown = Nothing
        Me.rdbIncrem.tbImageUnCheckedMouseHover = Nothing
        Me.rdbIncrem.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbIncrem.tbNoShowBadgeNumberImage = CType(resources.GetObject("rdbIncrem.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.rdbIncrem.tbReadOnly = False
        Me.rdbIncrem.tbShadow = False
        Me.rdbIncrem.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbIncrem.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbIncrem.tbShowBadgeNumber = True
        Me.rdbIncrem.tbShowBadgeNumberWhenSelection = True
        Me.rdbIncrem.tbShowNew = False
        Me.rdbIncrem.tbSplit = "3,3,3,3"
        Me.rdbIncrem.tbTag = Nothing
        Me.rdbIncrem.tbToolTip = ""
        Me.rdbIncrem.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.rdbIncrem.Text = "快速备份"
        Me.rdbIncrem.UseVisualStyleBackColor = False
        '
        'lblBackupType
        '
        Me.lblBackupType.AutoSize = True
        Me.lblBackupType.BackColor = System.Drawing.Color.Transparent
        Me.lblBackupType.Font = New System.Drawing.Font("宋体", 9.5!, System.Drawing.FontStyle.Bold)
        Me.lblBackupType.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblBackupType.Location = New System.Drawing.Point(29, 49)
        Me.lblBackupType.Name = "lblBackupType"
        Me.lblBackupType.Size = New System.Drawing.Size(119, 13)
        Me.lblBackupType.TabIndex = 74
        Me.lblBackupType.Text = "请选择备份的方式"
        '
        'rdbNew
        '
        Me.rdbNew.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rdbNew.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbNew.BackColor = System.Drawing.Color.Transparent
        Me.rdbNew.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbNew.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbNew.Location = New System.Drawing.Point(29, 402)
        Me.rdbNew.Name = "rdbNew"
        Me.rdbNew.Size = New System.Drawing.Size(709, 22)
        Me.rdbNew.TabIndex = 75
        Me.rdbNew.tbAdriftIconWhenHover = False
        Me.rdbNew.tbAutoSize = False
        Me.rdbNew.tbAutoSizeEx = False
        Me.rdbNew.tbBadgeNumber = 0
        Me.rdbNew.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbNew.tbEnableToolTip = False
        Me.rdbNew.tbIconCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbNew.tbIconCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbNew.tbIconCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbNew.tbIconHoldPlace = True
        Me.rdbNew.tbIconImage = Nothing
        Me.rdbNew.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbNew.tbIconImageState = iTong.Components.ImageState.OneState
        Me.rdbNew.tbIconMore = False
        Me.rdbNew.tbIconMoreImage = CType(resources.GetObject("rdbNew.tbIconMoreImage"), System.Drawing.Image)
        Me.rdbNew.tbIconPlaceText = 5
        Me.rdbNew.tbIconReadOnly = Nothing
        Me.rdbNew.tbIconReadOnlyLight = Nothing
        Me.rdbNew.tbIconUnCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbNew.tbIconUnCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbNew.tbIconUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbNew.tbImageBackground = Nothing
        Me.rdbNew.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.rdbNew.tbImageCheckedMouseDown = Nothing
        Me.rdbNew.tbImageCheckedMouseHover = Nothing
        Me.rdbNew.tbImageCheckedMouseLeave = Nothing
        Me.rdbNew.tbImageUnCheckedMouseDown = Nothing
        Me.rdbNew.tbImageUnCheckedMouseHover = Nothing
        Me.rdbNew.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbNew.tbNoShowBadgeNumberImage = CType(resources.GetObject("rdbNew.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.rdbNew.tbReadOnly = False
        Me.rdbNew.tbShadow = False
        Me.rdbNew.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbNew.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbNew.tbShowBadgeNumber = True
        Me.rdbNew.tbShowBadgeNumberWhenSelection = True
        Me.rdbNew.tbShowNew = False
        Me.rdbNew.tbSplit = "3,3,3,3"
        Me.rdbNew.tbTag = Nothing
        Me.rdbNew.tbToolTip = ""
        Me.rdbNew.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.rdbNew.Text = "新建备份"
        Me.rdbNew.UseVisualStyleBackColor = False
        '
        'txtiBackupFolder
        '
        Me.txtiBackupFolder.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtiBackupFolder.BackColor = System.Drawing.Color.White
        Me.txtiBackupFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtiBackupFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtiBackupFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtiBackupFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtiBackupFolder.Enabled = False
        Me.txtiBackupFolder.ForeColor = System.Drawing.Color.Black
        Me.txtiBackupFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtiBackupFolder.Location = New System.Drawing.Point(29, 429)
        Me.txtiBackupFolder.MaxLength = 256
        Me.txtiBackupFolder.Name = "txtiBackupFolder"
        Me.txtiBackupFolder.Size = New System.Drawing.Size(636, 21)
        Me.txtiBackupFolder.TabIndex = 77
        Me.txtiBackupFolder.Tag = Nothing
        Me.txtiBackupFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtiBackupFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtiBackupFolder.tbSelMark = True
        Me.txtiBackupFolder.tbTextBind = ""
        Me.txtiBackupFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtiBackupFolder.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtiBackupFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtiBackupFolder.TextTip = ""
        '
        'btnBacbupFolder
        '
        Me.btnBacbupFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBacbupFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnBacbupFolder.BindingForm = Nothing
        Me.btnBacbupFolder.Enabled = False
        Me.btnBacbupFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBacbupFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnBacbupFolder.Location = New System.Drawing.Point(674, 428)
        Me.btnBacbupFolder.Name = "btnBacbupFolder"
        Me.btnBacbupFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnBacbupFolder.Selectable = True
        Me.btnBacbupFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnBacbupFolder.TabIndex = 76
        Me.btnBacbupFolder.tbAdriftIconWhenHover = False
        Me.btnBacbupFolder.tbAutoSize = False
        Me.btnBacbupFolder.tbAutoSizeEx = False
        Me.btnBacbupFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnBacbupFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBacbupFolder.tbBadgeNumber = 0
        Me.btnBacbupFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBacbupFolder.tbEnableEnter = True
        Me.btnBacbupFolder.tbEndEllipsis = False
        Me.btnBacbupFolder.tbIconHoldPlace = True
        Me.btnBacbupFolder.tbIconImage = Nothing
        Me.btnBacbupFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBacbupFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBacbupFolder.tbIconMore = False
        Me.btnBacbupFolder.tbIconMouseDown = Nothing
        Me.btnBacbupFolder.tbIconMouseHover = Nothing
        Me.btnBacbupFolder.tbIconMouseLeave = Nothing
        Me.btnBacbupFolder.tbIconPlaceText = 2
        Me.btnBacbupFolder.tbIconReadOnly = Nothing
        Me.btnBacbupFolder.tbIconSize = New System.Drawing.Size(0, 0)
        Me.btnBacbupFolder.tbImageMouseDown = Nothing
        Me.btnBacbupFolder.tbImageMouseHover = Nothing
        Me.btnBacbupFolder.tbImageMouseLeave = Nothing
        Me.btnBacbupFolder.tbProgressValue = 50
        Me.btnBacbupFolder.tbReadOnly = False
        Me.btnBacbupFolder.tbReadOnlyText = False
        Me.btnBacbupFolder.tbShadow = False
        Me.btnBacbupFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnBacbupFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnBacbupFolder.tbShowDot = False
        Me.btnBacbupFolder.tbShowMoreIconImg = CType(resources.GetObject("btnBacbupFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBacbupFolder.tbShowNew = False
        Me.btnBacbupFolder.tbShowProgress = False
        Me.btnBacbupFolder.tbShowTip = True
        Me.btnBacbupFolder.tbShowToolTipOnButton = False
        Me.btnBacbupFolder.tbSplit = "13,11,13,11"
        Me.btnBacbupFolder.tbText = "更换"
        Me.btnBacbupFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBacbupFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBacbupFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBacbupFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBacbupFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBacbupFolder.tbTextMouseDownPlace = 0
        Me.btnBacbupFolder.tbToolTip = ""
        Me.btnBacbupFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBacbupFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBacbupFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBacbupFolder.tbUpToDownWhenCenter = False
        Me.btnBacbupFolder.VisibleEx = True
        '
        'pnlContainer
        '
        Me.pnlContainer.BackColor = System.Drawing.Color.Silver
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(0, 0)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(1)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Padding = New System.Windows.Forms.Padding(1)
        Me.pnlContainer.Size = New System.Drawing.Size(710, 273)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 78
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlCount
        '
        Me.pnlCount.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.pnlCount.Controls.Add(Me.lblOpenTutorial)
        Me.pnlCount.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlCount.Location = New System.Drawing.Point(0, 273)
        Me.pnlCount.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlCount.Name = "pnlCount"
        Me.pnlCount.Size = New System.Drawing.Size(710, 24)
        Me.pnlCount.TabIndex = 8
        '
        'lblOpenTutorial
        '
        Me.lblOpenTutorial.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblOpenTutorial.AutoSize = True
        Me.lblOpenTutorial.BackColor = System.Drawing.Color.Transparent
        Me.lblOpenTutorial.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblOpenTutorial.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblOpenTutorial.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblOpenTutorial.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblOpenTutorial.LinkColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblOpenTutorial.Location = New System.Drawing.Point(3, 4)
        Me.lblOpenTutorial.Name = "lblOpenTutorial"
        Me.lblOpenTutorial.Size = New System.Drawing.Size(221, 12)
        Me.lblOpenTutorial.TabIndex = 37
        Me.lblOpenTutorial.TabStop = True
        Me.lblOpenTutorial.Text = "备份失败？备份卡在某一处不动？戳我>>"
        Me.lblOpenTutorial.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lblOpenTutorial.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        '
        'munOperator
        '
        Me.munOperator.AccessibleDescription = "179x48"
        Me.munOperator.DropShadowEnabled = False
        Me.munOperator.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munOperator.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenFolder})
        Me.munOperator.Name = "munSearch"
        Me.munOperator.Size = New System.Drawing.Size(179, 26)
        Me.munOperator.tbBackColor = System.Drawing.Color.White
        Me.munOperator.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munOperator.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munOperator.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenFolder
        '
        Me.tsmiOpenFolder.Name = "tsmiOpenFolder"
        Me.tsmiOpenFolder.Size = New System.Drawing.Size(178, 22)
        Me.tsmiOpenFolder.Text = "打开文件所在的目录"
        '
        'pbarProgress
        '
        Me.pbarProgress.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.pbarProgress.BackColor = System.Drawing.Color.Transparent
        Me.pbarProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.pbarProgress.Location = New System.Drawing.Point(30, 502)
        Me.pbarProgress.Name = "pbarProgress"
        Me.pbarProgress.ProgressFormat = "0.##"
        Me.pbarProgress.Size = New System.Drawing.Size(110, 15)
        Me.pbarProgress.TabIndex = 79
        Me.pbarProgress.tbBackgroundImage = CType(resources.GetObject("pbarProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.pbarProgress.tbCanDragValue = False
        Me.pbarProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pbarProgress.tbDotImage = CType(resources.GetObject("pbarProgress.tbDotImage"), System.Drawing.Image)
        Me.pbarProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pbarProgress.tbDotMouseDown = Nothing
        Me.pbarProgress.tbDotMouseHover = Nothing
        Me.pbarProgress.tbDotMouseLeave = Nothing
        Me.pbarProgress.tbDownloadImage = Global.iTong.My.Resources.Resources.app_progress_background
        Me.pbarProgress.tbDownMax = 100
        Me.pbarProgress.tbDownMin = 0
        Me.pbarProgress.tbDownValue = 0
        Me.pbarProgress.tbIsWaiting = False
        Me.pbarProgress.tbPlayImage = Global.iTong.My.Resources.Resources.app_progress_value
        Me.pbarProgress.tbPlayMax = 100
        Me.pbarProgress.tbPlayMin = 0
        Me.pbarProgress.tbPlayValue = 50.0R
        Me.pbarProgress.tbShowDot = False
        Me.pbarProgress.tbShowText = True
        Me.pbarProgress.tbSplit = "4,0,4,0"
        Me.pbarProgress.Text = "TbControlBar1"
        Me.pbarProgress.Visible = False
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblMessage.AutoEllipsis = True
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(146, 500)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(418, 18)
        Me.lblMessage.TabIndex = 80
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = True
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnSplitCommon
        '
        Me.btnSplitCommon.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSplitCommon.BackColor = System.Drawing.Color.Transparent
        Me.btnSplitCommon.BindingForm = Nothing
        Me.btnSplitCommon.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSplitCommon.Location = New System.Drawing.Point(136, 54)
        Me.btnSplitCommon.Name = "btnSplitCommon"
        Me.btnSplitCommon.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSplitCommon.Selectable = True
        Me.btnSplitCommon.Size = New System.Drawing.Size(602, 2)
        Me.btnSplitCommon.TabIndex = 81
        Me.btnSplitCommon.tbAdriftIconWhenHover = False
        Me.btnSplitCommon.tbAutoSize = False
        Me.btnSplitCommon.tbAutoSizeEx = False
        Me.btnSplitCommon.tbBackgroundImage = Global.iTong.My.Resources.Resources.welcome_btn_split
        Me.btnSplitCommon.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSplitCommon.tbBadgeNumber = 0
        Me.btnSplitCommon.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSplitCommon.tbEnableEnter = True
        Me.btnSplitCommon.tbEndEllipsis = False
        Me.btnSplitCommon.tbIconHoldPlace = True
        Me.btnSplitCommon.tbIconImage = Nothing
        Me.btnSplitCommon.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplitCommon.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSplitCommon.tbIconMore = False
        Me.btnSplitCommon.tbIconMouseDown = Nothing
        Me.btnSplitCommon.tbIconMouseHover = Nothing
        Me.btnSplitCommon.tbIconMouseLeave = Nothing
        Me.btnSplitCommon.tbIconPlaceText = 2
        Me.btnSplitCommon.tbIconReadOnly = Nothing
        Me.btnSplitCommon.tbIconSize = New System.Drawing.Size(0, 0)
        Me.btnSplitCommon.tbImageMouseDown = Nothing
        Me.btnSplitCommon.tbImageMouseHover = Nothing
        Me.btnSplitCommon.tbImageMouseLeave = Nothing
        Me.btnSplitCommon.tbProgressValue = 50
        Me.btnSplitCommon.tbReadOnly = False
        Me.btnSplitCommon.tbReadOnlyText = False
        Me.btnSplitCommon.tbShadow = False
        Me.btnSplitCommon.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSplitCommon.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSplitCommon.tbShowDot = False
        Me.btnSplitCommon.tbShowMoreIconImg = CType(resources.GetObject("btnSplitCommon.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSplitCommon.tbShowNew = False
        Me.btnSplitCommon.tbShowProgress = False
        Me.btnSplitCommon.tbShowTip = True
        Me.btnSplitCommon.tbShowToolTipOnButton = False
        Me.btnSplitCommon.tbSplit = "35,0,35,0"
        Me.btnSplitCommon.tbText = ""
        Me.btnSplitCommon.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplitCommon.tbTextColor = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextColorDown = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextColorHover = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextMouseDownPlace = 0
        Me.btnSplitCommon.tbToolTip = ""
        Me.btnSplitCommon.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSplitCommon.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSplitCommon.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplitCommon.tbUpToDownWhenCenter = False
        Me.btnSplitCommon.VisibleEx = True
        '
        'pnlContainerEx
        '
        Me.pnlContainerEx.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainerEx.BackColor = System.Drawing.Color.Silver
        Me.pnlContainerEx.Controls.Add(Me.pnlContainer)
        Me.pnlContainerEx.Controls.Add(Me.pnlCount)
        Me.pnlContainerEx.Location = New System.Drawing.Point(29, 99)
        Me.pnlContainerEx.Name = "pnlContainerEx"
        Me.pnlContainerEx.Size = New System.Drawing.Size(710, 297)
        Me.pnlContainerEx.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainerEx.TabIndex = 82
        Me.pnlContainerEx.tbBackgroundImage = Nothing
        Me.pnlContainerEx.tbShowWatermark = False
        Me.pnlContainerEx.tbSplit = "0,0,0,0"
        Me.pnlContainerEx.tbWatermark = Nothing
        Me.pnlContainerEx.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainerEx.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblBackupPwdMsg
        '
        Me.lblBackupPwdMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblBackupPwdMsg.AutoSize = True
        Me.lblBackupPwdMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblBackupPwdMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.lblBackupPwdMsg.Location = New System.Drawing.Point(28, 462)
        Me.lblBackupPwdMsg.Name = "lblBackupPwdMsg"
        Me.lblBackupPwdMsg.Size = New System.Drawing.Size(263, 12)
        Me.lblBackupPwdMsg.TabIndex = 84
        Me.lblBackupPwdMsg.tbAdriftWhenHover = False
        Me.lblBackupPwdMsg.tbAutoEllipsis = False
        Me.lblBackupPwdMsg.tbAutoSize = True
        Me.lblBackupPwdMsg.tbHideImage = False
        Me.lblBackupPwdMsg.tbIconImage = Nothing
        Me.lblBackupPwdMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblBackupPwdMsg.tbIconPlaceText = 5
        Me.lblBackupPwdMsg.tbShadow = False
        Me.lblBackupPwdMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblBackupPwdMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblBackupPwdMsg.tbShowScrolling = False
        Me.lblBackupPwdMsg.Text = "此设备已设置备份密码， 备份的文件无法查看。"
        Me.lblBackupPwdMsg.Visible = False
        '
        'llblDeletePwd
        '
        Me.llblDeletePwd.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblDeletePwd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.llblDeletePwd.AutoSize = True
        Me.llblDeletePwd.BackColor = System.Drawing.Color.Transparent
        Me.llblDeletePwd.Cursor = System.Windows.Forms.Cursors.Hand
        Me.llblDeletePwd.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.llblDeletePwd.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblDeletePwd.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.llblDeletePwd.LinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblDeletePwd.Location = New System.Drawing.Point(297, 462)
        Me.llblDeletePwd.Name = "llblDeletePwd"
        Me.llblDeletePwd.Size = New System.Drawing.Size(77, 12)
        Me.llblDeletePwd.TabIndex = 83
        Me.llblDeletePwd.TabStop = True
        Me.llblDeletePwd.Text = "删除备份密码"
        Me.llblDeletePwd.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.llblDeletePwd.Visible = False
        Me.llblDeletePwd.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        '
        'lblPath
        '
        Me.lblPath.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblPath.AutoEllipsis = True
        Me.lblPath.BackColor = System.Drawing.Color.Transparent
        Me.lblPath.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblPath.Location = New System.Drawing.Point(32, 524)
        Me.lblPath.Name = "lblPath"
        Me.lblPath.Size = New System.Drawing.Size(706, 18)
        Me.lblPath.TabIndex = 85
        Me.lblPath.tbAdriftWhenHover = False
        Me.lblPath.tbAutoEllipsis = True
        Me.lblPath.tbAutoSize = False
        Me.lblPath.tbHideImage = False
        Me.lblPath.tbIconImage = Nothing
        Me.lblPath.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblPath.tbIconPlaceText = 5
        Me.lblPath.tbShadow = False
        Me.lblPath.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblPath.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblPath.tbShowScrolling = False
        Me.lblPath.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmBackupCreate
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(768, 545)
        Me.Controls.Add(Me.lblPath)
        Me.Controls.Add(Me.lblBackupPwdMsg)
        Me.Controls.Add(Me.llblDeletePwd)
        Me.Controls.Add(Me.pnlContainerEx)
        Me.Controls.Add(Me.pbarProgress)
        Me.Controls.Add(Me.txtiBackupFolder)
        Me.Controls.Add(Me.btnBacbupFolder)
        Me.Controls.Add(Me.rdbNew)
        Me.Controls.Add(Me.lblBackupType)
        Me.Controls.Add(Me.rdbIncrem)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.btnSplitCommon)
        Me.MinimumSize = New System.Drawing.Size(16, 110)
        Me.Name = "frmBackupCreate"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "8,55,8,55"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "创建备份"
        Me.Controls.SetChildIndex(Me.btnSplitCommon, 0)
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.btnOK, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.rdbIncrem, 0)
        Me.Controls.SetChildIndex(Me.lblBackupType, 0)
        Me.Controls.SetChildIndex(Me.rdbNew, 0)
        Me.Controls.SetChildIndex(Me.btnBacbupFolder, 0)
        Me.Controls.SetChildIndex(Me.txtiBackupFolder, 0)
        Me.Controls.SetChildIndex(Me.pbarProgress, 0)
        Me.Controls.SetChildIndex(Me.pnlContainerEx, 0)
        Me.Controls.SetChildIndex(Me.llblDeletePwd, 0)
        Me.Controls.SetChildIndex(Me.lblBackupPwdMsg, 0)
        Me.Controls.SetChildIndex(Me.lblPath, 0)
        Me.pnlCount.ResumeLayout(False)
        Me.pnlCount.PerformLayout()
        Me.munOperator.ResumeLayout(False)
        Me.pnlContainerEx.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnCancel As tbButton
    Friend WithEvents btnOK As tbButton
    Friend WithEvents rdbIncrem As tbRadioButton
    Friend WithEvents lblBackupType As System.Windows.Forms.Label
    Friend WithEvents rdbNew As tbRadioButton
    Friend WithEvents txtiBackupFolder As tbTextBox
    Friend WithEvents btnBacbupFolder As tbButton
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents munOperator As tbContextMenuStrip
    Friend WithEvents tsmiOpenFolder As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pbarProgress As tbControlBar
    Friend WithEvents lblMessage As tbLabel
    Friend WithEvents btnSplitCommon As tbButton
    Friend WithEvents pnlCount As System.Windows.Forms.Panel
    Friend WithEvents lblOpenTutorial As System.Windows.Forms.LinkLabel
    Friend WithEvents pnlContainerEx As iTong.Components.tbPanel
    Friend WithEvents lblBackupPwdMsg As iTong.Components.tbLabel
    Friend WithEvents llblDeletePwd As System.Windows.Forms.LinkLabel
    Friend WithEvents lblPath As iTong.Components.tbLabel
End Class
