﻿Public Class frmMSMSetting

#Region "--- 初始化 ---"

    Public Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.cbxSaveDB.Checked = IniSetting.GetSaveSMSDB()

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("Common.Button.Setting")
        Me.lblMSMSetting.Text = Me.Language.GetString("Common.Lable.SMSSetting") '"短信设置"
        Me.cbxSaveDB.Text = Me.Language.GetString("Common.Lable.SaveSMSInPC") ' "默认保存短信数据库到电脑"

        Me.btnOK.Text = Me.Language.GetString("Common.Button.Save")
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")

    End Sub

#End Region

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Try
            IniSetting.SetSaveSMSDB(Me.cbxSaveDB.Checked)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnOK_Click")
        End Try
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub
End Class