﻿Imports System.IO
Imports System.Data
Imports System.Data.SQLite3
Imports iTong.CoreFoundation
Imports iTong.CoreModule

Public Class BackupCallHistoryDB
    Implements IDisposable

    Private Shared mLocker As New Object
    Private Shared mDictDB As New Dictionary(Of String, BackupCallHistoryDB)
    Private mConn As SQLiteConnection = Nothing
    Private mTempFolder As String = ""
    Private mDbPathOnBackupFolder As String = ""
    Private mDbPathOnTempFolder As String = ""
    Private mBackObject As BackupInfo

    Public Sub New(ByVal strDBFilePath As String, ByVal bakObject As BackupInfo)
        Me.mTempFolder = Folder.GetTempFilePath()
        Folder.CheckFolder(Me.mTempFolder)
        Me.mDbPathOnBackupFolder = strDBFilePath
        Me.mBackObject = bakObject
    End Sub

    Public Shared Function Instance(ByVal strDBFilePath As String, ByVal bakObject As BackupInfo) As BackupCallHistoryDB
        Dim strID As String = strDBFilePath.Replace("/", "").Replace("\", "").Replace(" ", "").Replace(".", "").Replace(":", "")
        Try
            SyncLock mLocker
                If Not String.IsNullOrEmpty(strID) AndAlso mDictDB.ContainsKey(strID) Then
                    Dim db As BackupCallHistoryDB = mDictDB(strID)
                    If db Is Nothing Then
                        mDictDB.Remove(strID)

                        db = New BackupCallHistoryDB(strDBFilePath, bakObject)
                        mDictDB.Add(strID, db)
                    End If

                Else
                    Dim db As New BackupCallHistoryDB(strDBFilePath, bakObject)
                    mDictDB.Add(strID, db)

                End If
            End SyncLock
        Catch ex As Exception
        End Try
        Return mDictDB(strID)
    End Function

    Private Function Reload() As Boolean
        Dim blnResult As Boolean = False

        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            Me.mDbPathOnTempFolder = Path.Combine(Me.mTempFolder, "call_history.db")

            Try
                File.Delete(Me.mDbPathOnTempFolder)
                File.Copy(Me.mDbPathOnBackupFolder, Me.mDbPathOnTempFolder)
            Catch ex As Exception
            End Try
            If File.Exists(Me.mDbPathOnTempFolder) Then
                Me.mConn = SQLiteClass3.CreateConnectionFromFile(Me.mDbPathOnTempFolder)
                If Me.mConn IsNot Nothing Then
                    blnResult = True
                End If
            End If
        Catch ex As Exception
        End Try
        Return blnResult
    End Function

    Public Function GetAll() As List(Of CallHistory)
        '加载数据文件
        Me.Reload()
        '取得所有短信信息

        Dim listInfo As List(Of CallHistory) = Me.LoadCallHistory()

        Me.Dispose()
        Return listInfo
    End Function

    Private Function LoadCallHistory() As List(Of CallHistory)
        Dim listInfo As New List(Of CallHistory)
        Try
            If Me.mConn IsNot Nothing Then
                Dim overiOS8 As Boolean = False
                Dim strSQL As String = "SELECT * FROM call ORDER BY date desc"
                If Me.mBackObject.VersionNumber >= 800 Then
                    overiOS8 = True
                    strSQL = "SELECT Z_PK as ROWID,ZADDRESS as address,ZDATE as date,ZDURATION as duration,ZORIGINATED as flags,ZISO_COUNTRY_CODE as country_code FROM ZCALLRECORD ORDER BY date desc"
                End If
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim callInfo As New CallHistory
                        callInfo.CallHistoryROWID = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        If TypeOf row("address") Is Byte() Then
                            Dim bufferAddress() As Byte = Common.GetValue(Of Byte())(row("address"), Nothing)
                            If bufferAddress IsNot Nothing Then
                                callInfo.PhoneNumber = System.Text.Encoding.Default.GetString(bufferAddress)
                            End If
                        Else
                            callInfo.PhoneNumber = Common.GetValue(Of String)(row("address"), String.Empty)
                        End If

                        If row("date") IsNot Nothing AndAlso Not TypeOf row("date") Is System.DBNull Then
                            Dim longDate As Long = 0
                            Dim dateStr As String = CType(row("date"), String)
                            If Not String.IsNullOrEmpty(dateStr) Then
                                Dim index As Integer = dateStr.IndexOf(".")
                                If index > -1 Then
                                    dateStr = dateStr.Substring(0, index)
                                End If
                                If Long.TryParse(dateStr, longDate) Then
                                    If overiOS8 Then
                                        callInfo.CreateDate = Common.ConvertToPcTime(longDate, Date.Parse("2001-01-01 08:00"))
                                    Else
                                        callInfo.CreateDate = Common.ConvertToPcTime(longDate, Date.Parse("1970-01-01 08:00"))
                                    End If
                                End If
                            End If
                        End If
                        callInfo.Duration = Common.GetValue(Of Integer)(row("duration"), 0)

                        Dim intValue As Integer = Common.GetValue(Of Integer)(row("flags"), 0)
                        If intValue = 4 OrElse intValue = 0 OrElse intValue = 1769472 Then
                            callInfo.CallInOut = CallInOut.CallIn
                        Else
                            callInfo.CallInOut = CallInOut.CallOut
                        End If

                        callInfo.CountryCode = Common.GetValue(Of Integer)(row("country_code"), 0)

                        listInfo.Add(callInfo)
                    Next
                End If
            End If
        Catch ex As Exception
        End Try
        Return listInfo
    End Function

    Public Function Delete(ByVal lstCallHistoryIds As List(Of String)) As Integer
        Dim intReturn As String = -1
        If Me.mConn IsNot Nothing Then
            intReturn = 0
            Try
                Dim ids As String = ""
                For Each Item As String In lstCallHistoryIds
                    If ids.Length > 0 Then
                        ids &= "," & Item
                    Else
                        ids = Item
                    End If
                Next
                ids = String.Format("({0})", ids)

                Dim a As String = ""
                Dim strSQL As String = "DELETE FROM call WHERE ROWID in " & ids
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, Nothing, a)

                a = ""
            Catch ex As Exception
                intReturn = -1
            End Try
        End If
        Return intReturn
    End Function

    Public Function Save() As Boolean
        Dim blnReturn As Boolean = True
        Try
            Me.mConn.Close()
            File.Copy(Me.mDbPathOnTempFolder, Me.mDbPathOnBackupFolder, True)
            Me.Reload()
        Catch ex As Exception
            blnReturn = False
        End Try

        Return blnReturn
    End Function

    Public Sub Close()
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If
        Catch ex As Exception
        End Try
    End Sub

#Region " IDisposable Support "

    Private disposedValue As Boolean = False        ' 检测冗余的调用
    ' IDisposable
    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If

            ' TODO: 释放共享的非托管资源
        End If
        Me.disposedValue = True
    End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub
#End Region

End Class

Public Class CallHistory
    Public CallHistoryROWID As String = ""
    Public PhoneNumber As String = ""
    Public Name As String = ""
    Public CreateDate As DateTime
    Public Duration As Integer = 0
    'Public CallFlags As Integer = 0
    Public CallInOut As CallInOut = CallInOut.CallIn
    Public CountryCode As Integer = 460
    Public IsFind As Boolean = False
End Class

Public Enum CallInOut
    CallIn
    CallOut
End Enum

