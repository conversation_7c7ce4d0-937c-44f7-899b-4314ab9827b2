﻿'移到CoreModuleCS
Public Class BackupInfo

    Private mFolderPath As String = ""
    Private mDeviceName As String = ""
    Private mDisplayName As String = ""
    Private mLastBackupDate As Date = Now
    Private mProductVersion As String = ""
    Private mVersionNumber As Integer = -1
    Private mBuildVersion As String = ""
    Private mSize As ULong = 0
    Private mProductType As String = ""
    Private mSerialNumber As String = ""
    Private mIdentifier As String = ""
    Private mBackupDate As DateTime = Now
    Private mIsEncrypted As Boolean = False
    Private mDate1970 As New Date(1970, 1, 1)

    Private mIntType As Integer = 0 '0：官方   1：同步分身   2：VIP分身
    Public Property IntType() As Integer
        Get
            Return Me.mIntType
        End Get
        Set(ByVal value As Integer)
            Me.mIntType = value
        End Set
    End Property

    Public ReadOnly Property IsEncrypted() As Boolean
        Get
            Return Me.mIsEncrypted
        End Get
    End Property

    Public Property FolderPath() As String
        Get
            Return Me.mFolderPath
        End Get
        Set(ByVal value As String)
            Me.mFolderPath = value
        End Set
    End Property

    Public Property DeviceName() As String
        Get
            Return Me.mDeviceName
        End Get
        Set(ByVal value As String)
            Me.mDeviceName = value
        End Set
    End Property

    Public Property DisplayName() As String
        Get
            Return Me.mDisplayName
        End Get
        Set(ByVal value As String)
            Me.mDisplayName = value
        End Set
    End Property

    Public Property LastBackupDate() As Date
        Get
            Return Me.mLastBackupDate
        End Get
        Set(ByVal value As Date)
            Me.mLastBackupDate = value
        End Set
    End Property

    Public Property ProductVersion() As String
        Get
            Return Me.mProductVersion
        End Get
        Set(ByVal value As String)
            Me.mProductVersion = value
        End Set
    End Property

    Public Property VersionNumber() As Integer
        Get
            If Me.mVersionNumber < 0 Then
                Try
                    Dim arrVer() As String = mProductVersion.Split(".")
                    If arrVer.Length > 0 Then
                        mVersionNumber = arrVer(0) * 100
                    End If

                    If arrVer.Length > 1 Then
                        mVersionNumber += arrVer(1) * 10
                    End If

                    If arrVer.Length > 2 Then
                        mVersionNumber += arrVer(2)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "BackupInfo_VersionNumber")
                End Try
            End If
            Return Me.mVersionNumber
        End Get
        Set(ByVal value As Integer)
            Me.mVersionNumber = value
        End Set
    End Property

    Public Property BuildVersion() As String
        Get
            Return Me.mBuildVersion
        End Get
        Set(ByVal value As String)
            Me.mBuildVersion = value
        End Set
    End Property

    Public Property Size() As ULong
        Get
            Return Me.mSize
        End Get
        Set(ByVal value As ULong)
            Me.mSize = value
        End Set
    End Property

    Public Property ProductType() As String
        Get
            Return Me.mProductType
        End Get
        Set(ByVal value As String)
            Me.mProductType = value
        End Set
    End Property

    Public Property SerialNumber() As String
        Get
            Return Me.mSerialNumber
        End Get
        Set(ByVal value As String)
            Me.mSerialNumber = value
        End Set
    End Property

    Public Property Identifier() As String
        Get
            Return Me.mIdentifier
        End Get
        Set(ByVal value As String)
            Me.mIdentifier = value
        End Set
    End Property

    Public Property BackupDate() As DateTime
        Get
            Return Me.mBackupDate
        End Get
        Set(ByVal value As DateTime)
            Me.mBackupDate = value
        End Set
    End Property

    Sub New(ByVal strBackupFolder As String)
        Try
            Dim strPlistPath As String = Path.Combine(strBackupFolder, "Info.plist")
            Dim strStatePath As String = Path.Combine(strBackupFolder, "Status.plist")
            Dim strSnapPath As String = Path.Combine(strBackupFolder, "Snapshot")
            Dim strManiFestPath As String = Path.Combine(strBackupFolder, "Manifest.plist")
            Me.FolderPath = strBackupFolder
            'OrElse Directory.Exists(strSnapPath)
            If Not File.Exists(strPlistPath) OrElse Not File.Exists(strStatePath) OrElse Not File.Exists(strManiFestPath) Then
                Return
            End If

            Me.mIsEncrypted = mbdb.CheckIsEncrypted(strBackupFolder)

            Dim dic As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strPlistPath)
            If dic IsNot Nothing AndAlso dic.Count > 0 Then

                If dic.ContainsKey("Device Name") Then
                    Me.DeviceName = dic("Device Name")
                End If

                If dic.ContainsKey("Display Name") Then
                    Me.DisplayName = dic("Display Name")
                End If

                'dateTime
                If dic.ContainsKey("Last Backup Date") Then
                    Try
                        Me.LastBackupDate = dic("Last Backup Date")

                        '如果读取不到，再获取Status.plist里面的Date字段
                        If Date.Compare(mDate1970, Me.LastBackupDate) = 0 Then
                            Dim dictStatus As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strStatePath)
                            If dictStatus IsNot Nothing AndAlso dictStatus.ContainsKey("Date") Then
                                Me.LastBackupDate = dictStatus("Date")
                            End If
                        End If

                    Catch ex As Exception
                        Common.LogException(ex.ToString, "BackupInfo init 1")
                        Me.LastBackupDate = Date.MinValue.ToString("yyyy-MM-dd  HH:mm:ss")
                    End Try
                End If

                'Size
                Me.Size = 0  'Me.GetFileSize(strBackupFolder)

                'Version
                If dic.ContainsKey("Product Version") Then
                    Me.ProductVersion = dic("Product Version")
                End If

                If dic.ContainsKey("Build Version") Then
                    Me.BuildVersion &= dic("Build Version")
                End If

                '设备类型
                If dic.ContainsKey("Product Type") Then
                    Me.ProductType = SummaryInfo.FormatProduct(dic("Product Type"))
                End If

                '序列号

                If dic.ContainsKey("Serial Number") Then
                    Me.SerialNumber = dic("Serial Number")
                End If

                'Identifier
                If dic.ContainsKey("Target Identifier") Then
                    Me.Identifier = dic("Target Identifier")
                End If

                Dim info As New FileInfo(strPlistPath)

                Me.mBackupDate = info.LastWriteTime

                Dim diffDays As Integer = Me.BackupDate.Date.Subtract(Me.LastBackupDate.Date).TotalDays
                If Math.Abs(diffDays) > 1 Then
                    Me.BackupDate = Me.LastBackupDate
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "BackupInfo init")
        End Try
    End Sub

    '备份的文件解密时，需要大于或等于其固件的设备解密
    Public Shared Function GetHighProductVersionDevice(ByVal strVer As String) As iPhoneDevice
        Dim device As iPhoneDevice = Nothing

        Try
            Dim intVer As Integer = Val(strVer.Replace(".", "").PadRight(3, "0"c).Substring(0, 3)) \ 100
            Dim mgrDevice As MobileDeviceManager = MobileDeviceManager.Instance()
            For Each Item As iPhoneDevice In mgrDevice.ConnectedDevices
                If Item.VersionNumber \ 100 >= intVer Then
                    device = Item
                    Exit For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetHighProductVersionDevice")
        End Try

        Return device
    End Function

End Class
