﻿Imports System.IO
Imports System.Data
Imports System.Data.SQLite3
Imports iTong.CoreFoundation

Public Class BackupContactDB
    Implements IDisposable

    Private Shared mLocker As New Object
    Private Shared mDictDB As New Dictionary(Of String, BackupContactDB)
    Private mConn As SQLiteConnection = Nothing
    Private mConnImage As SQLiteConnection = Nothing
    Private mTempFolder As String = ""
    Private mDbPathOnBackupFolder As String = ""
    Private mImageDbPathOnBackupFolder As String = ""
    Private mDbPathOnTempFolder As String = ""
    Private mImageDbPathOnTempFolder As String = ""

    Public Sub New(ByVal strDBFilePath As String, ByVal strImageDBFile As String)
        Me.mTempFolder = Folder.GetTempFilePath()
        Folder.CheckFolder(Me.mTempFolder)
        Me.mDbPathOnBackupFolder = strDBFilePath
        Me.mImageDbPathOnBackupFolder = strImageDBFile
    End Sub

    Public Shared Function Instance(ByVal strDBFilePath As String, ByVal strImageDBFile As String) As BackupContactDB
        Dim strID As String = strDBFilePath.Replace("/", "").Replace("\", "").Replace(" ", "").Replace(".", "").Replace(":", "")
        Try
            SyncLock mLocker
                If Not String.IsNullOrEmpty(strID) AndAlso mDictDB.ContainsKey(strID) Then
                    Dim db As BackupContactDB = mDictDB(strID)
                    If db Is Nothing Then
                        mDictDB.Remove(strID)

                        db = New BackupContactDB(strDBFilePath, strImageDBFile)
                        mDictDB.Add(strID, db)
                    End If

                Else
                    Dim db As New BackupContactDB(strDBFilePath, strImageDBFile)
                    mDictDB.Add(strID, db)

                End If
            End SyncLock
        Catch ex As Exception
        End Try
        Return mDictDB(strID)
    End Function

    '加载数据库

    Private Function Reload() As Boolean
        Dim blnResult As Boolean = False
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            If Me.mConnImage IsNot Nothing AndAlso Me.mConnImage.State = ConnectionState.Open Then
                Me.mConnImage.Close()
            End If

            Me.mDbPathOnTempFolder = Path.Combine(Me.mTempFolder, "AddressBook.sqlitedb")
            Me.mImageDbPathOnTempFolder = Path.Combine(Me.mTempFolder, "AddressBookImages.sqlitedb")
           
            Try
                If File.Exists(Me.mDbPathOnTempFolder) Then
                    File.Delete(Me.mDbPathOnTempFolder)
                End If

                If File.Exists(Me.mDbPathOnBackupFolder) Then
                    File.Copy(Me.mDbPathOnBackupFolder, Me.mDbPathOnTempFolder)
                End If

                If File.Exists(Me.mDbPathOnTempFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-shm")) Then
                    File.Delete(Me.mDbPathOnTempFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-shm"))
                End If

                If File.Exists(Me.mDbPathOnBackupFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-shm")) Then
                    File.Copy(Me.mDbPathOnBackupFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-shm"), Me.mDbPathOnTempFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-shm"))
                End If

                If File.Exists(Me.mDbPathOnTempFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-wal")) Then
                    File.Delete(Me.mDbPathOnTempFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-wal"))
                End If

                If File.Exists(Me.mDbPathOnBackupFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-wal")) Then
                    File.Copy(Me.mDbPathOnBackupFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-wal"), Me.mDbPathOnTempFolder.Replace("AddressBook.sqlitedb", "AddressBook.sqlitedb-wal"))
                End If

                If File.Exists(Me.mImageDbPathOnTempFolder) Then
                    File.Delete(Me.mImageDbPathOnTempFolder)
                End If

                If File.Exists(Me.mImageDbPathOnBackupFolder) Then
                    File.Copy(Me.mImageDbPathOnBackupFolder, Me.mImageDbPathOnTempFolder)
                End If

                If File.Exists(Me.mImageDbPathOnTempFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-shm")) Then
                    File.Delete(Me.mImageDbPathOnTempFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-shm"))
                End If

                If File.Exists(Me.mImageDbPathOnBackupFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-shm")) Then
                    File.Copy(Me.mImageDbPathOnBackupFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-shm"), Me.mImageDbPathOnTempFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-shm"))
                End If

                If File.Exists(Me.mImageDbPathOnTempFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-wal")) Then
                    File.Delete(Me.mImageDbPathOnTempFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-wal"))
                End If

                If File.Exists(Me.mImageDbPathOnBackupFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-wal")) Then
                    File.Copy(Me.mImageDbPathOnBackupFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-wal"), Me.mImageDbPathOnTempFolder.Replace("AddressBookImages.sqlitedb", "AddressBookImages.sqlitedb-wal"))
                End If

            Catch ex As Exception
            End Try
            If File.Exists(Me.mDbPathOnTempFolder) Then

                Me.mConn = SQLiteClass3.CreateConnectionFromFile(Me.mDbPathOnTempFolder)
                If Me.mConn IsNot Nothing Then
                    blnResult = True
                End If
            End If

            If File.Exists(Me.mImageDbPathOnTempFolder) Then

                Me.mConnImage = SQLiteClass3.CreateConnectionFromFile(Me.mImageDbPathOnTempFolder)
                If Me.mConnImage IsNot Nothing Then
                    blnResult = True
                End If
            End If
        Catch ex As Exception
        End Try
        Return blnResult
    End Function

    Public Sub Close()
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            If Me.mConnImage IsNot Nothing AndAlso Me.mConnImage.State = ConnectionState.Open Then
                Me.mConnImage.Close()
            End If
        Catch ex As Exception
        End Try
    End Sub

#Region " Contacts 操作 "

    Public Function GetContacts() As List(Of Contact)
        '加载数据文件
        Me.Reload()
        '取得所有联系人基本信息
        Dim listInfo As List(Of Contact) = Me.LoadContact()

        Me.Dispose()
        Return listInfo
    End Function

    '加载联系人

    Private Function LoadContact() As List(Of Contact)
        Dim listInfo As New List(Of Contact)
        Dim dictInfo As New Dictionary(Of String, Contact)
        Try
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = "SELECT * FROM ABPerson "
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim strID As String = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        '添加基本信息
                        Dim info As Contact
                        If strID.Trim().Length > 0 Then
                            If Not dictInfo.ContainsKey(strID) Then
                                info = New Contact()
                                info.Identifier = strID
                                info.FirstName = Common.GetValue(Of String)(row("First"), String.Empty)
                                info.FirstNamePinyin = Common.GetValue(Of String)(row("FirstPhonetic"), String.Empty)
                                info.LastName = Common.GetValue(Of String)(row("Last"), String.Empty)
                                info.LastNamePinyin = Common.GetValue(Of String)(row("LastPhonetic"), String.Empty)
                                info.MiddleName = Common.GetValue(Of String)(row("Middle"), String.Empty)
                                info.Notes = Common.GetValue(Of String)(row("Note"), String.Empty)
                                info.Prefix = Common.GetValue(Of String)(row("Prefix"), String.Empty)
                                info.Suffix = Common.GetValue(Of String)(row("Suffix"), String.Empty)

                                If row("Birthday") IsNot Nothing AndAlso Not TypeOf row("Birthday") Is System.DBNull Then
                                    Dim longDate As Long = 0
                                    Dim dateStr As String = CType(row("Birthday"), String)
                                    If Not String.IsNullOrEmpty(dateStr) Then
                                        Dim index As Integer = dateStr.IndexOf(".")
                                        If index > -1 Then
                                            dateStr = dateStr.Substring(0, index)
                                            If Long.TryParse(dateStr, longDate) Then
                                                info.Birthday = Common.ConvertToPcTime(longDate)
                                            End If
                                        End If
                                    End If
                                End If

                                info.CompanyName = Common.GetValue(Of String)(row("Organization"), String.Empty)
                                info.Department = Common.GetValue(Of String)(row("Department"), String.Empty)
                                info.JobTitle = Common.GetValue(Of String)(row("JobTitle"), String.Empty)

                                '加载ABMulitValue  Property 3 4 22
                                Me.LoadMulitValue(info)

                                '加载地址  Property 5
                                Me.LoadMulitValueEntry(info)

                                '加载图片
                                Me.LoadPhoto(info)

                                dictInfo.Add(strID, info)
                            End If
                        End If
                    Next
                End If
            End If

            If dictInfo.Count > 0 Then
                listInfo.AddRange(dictInfo.Values())
            End If

            dictInfo.Clear()
            dictInfo = Nothing

        Catch ex As Exception
        End Try
        Return listInfo
    End Function

    Private Sub LoadMulitValue(ByRef info As Contact)
        If Me.mConn IsNot Nothing Then
            Dim strSQL As String = "SELECT A.*,B.value labelName From ABMultiValue A Left Join ABMultiValueLabel B ON A.label=B.ROWID WHERE A.property in (3,4,12,22,23) AND A.record_id={0}"
            strSQL = String.Format(strSQL, info.Identifier)

            Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
            For Each row As DataRow In dt.Rows
                Dim strProperty As String = Common.GetValue(Of String)(row("property"), String.Empty)
                Dim strIdentify As String = Common.GetValue(Of String)(row("identifier"), String.Empty)
                Dim strLabel As String = Common.GetValue(Of String)(row("label"), String.Empty)
                Dim strLabelName As String = Common.GetValue(Of String)(row("labelName"), String.Empty)
                Dim strValue As String = Common.GetValue(Of String)(row("value"), String.Empty)
                Dim strID As String = Common.GetValue(Of String)(row("UID"), String.Empty)

                Dim item As New ContactItem()
                item.ID = strID
                item.Key = String.Format("{0}/{1}/{2}", strProperty, info.Identifier, strIdentify)
                item.Label = strLabelName
                item.Value = strValue
                item.LabelType = Me.LabelToTextType(strLabelName)
                Select Case strProperty
                    Case "3"  '为3的是电话号码
                        info.PhoneNumbers.Add(item)

                    Case "4" '4为Email
                        info.Emails.Add(item)

                    Case "12" 'Dates
                        Dim longDate As Long = 0
                        Dim dateStr As String = strValue
                        If Not String.IsNullOrEmpty(dateStr) Then
                            Dim index As Integer = dateStr.IndexOf(".")
                            If index > -1 Then
                                dateStr = dateStr.Substring(0, index)
                                If Long.TryParse(dateStr, longDate) Then
                                    item.Value = Common.ConvertToPcTime(longDate)
                                End If
                            End If
                        End If

                        info.Dates.Add(item)

                    Case "22" 'HomePage
                        info.URLs.Add(item)

                    Case "23" 'Relateds
                        info.Relateds.Add(item)

                End Select
            Next
        End If
    End Sub

    Private Sub LoadMulitValueEntry(ByRef info As Contact)
        If Me.mConn IsNot Nothing Then
            Dim strSQL As String = "SELECT A.*,B.value labelName FROM ABMultiValue A Left Join ABMultiValueLabel B ON A.label=B.ROWID WHERE A.property in (5,13) AND A.record_id={0}"
            strSQL = String.Format(strSQL, info.Identifier)
            Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
            For Each row As DataRow In dt.Rows
                Dim strProperty As String = Common.GetValue(Of String)(row("property"), String.Empty)
                Dim strIdentify As String = Common.GetValue(Of String)(row("identifier"), String.Empty)
                Dim strLabelName As String = Common.GetValue(Of String)(row("labelName"), String.Empty)
                Dim strMultiValueID As String = Common.GetValue(Of String)(row("UID"), String.Empty)
               
                If strProperty = "5" Then
                    Dim adds As New AddressItem()
                    adds.Key = String.Format("{0}/{1}/{2}", strProperty, info.Identifier, strIdentify)
                    adds.Label = strLabelName
                    adds.LabelType = Me.LabelToTextType(strLabelName)
                    adds.ID = strMultiValueID
                    Me.LoadMulitValueEntryAddress(strMultiValueID, adds, info)
                    info.Addresses.Add(adds)
                Else
                    Dim aims As New IMItem
                    aims.Key = String.Format("{0}/{1}/{2}", strProperty, info.Identifier, strIdentify)
                    aims.Label = strLabelName
                    aims.LabelType = Me.LabelToTextType(strLabelName)
                    aims.ID = strMultiValueID
                    Me.LoadMulitValueEntryIM(strMultiValueID, aims, info)
                End If
            Next
        End If
    End Sub

    Private Sub LoadMulitValueEntryAddress(ByVal strMultiValueID As String, ByRef adds As AddressItem, ByRef info As Contact)
        If Me.mConn IsNot Nothing Then
            Dim strSQL As String = String.Format("SELECT A.*,B.value keyvalue FROM ABMultiValueEntry A Left Join ABMultiValueEntryKey B ON A.key=B.ROWID WHERE parent_id={0}", strMultiValueID)
            Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
            For Each row As DataRow In dt.Rows
                Dim strKeyValue As String = Common.GetValue(Of String)(row("keyvalue"), String.Empty)
                Dim strValue As String = Common.GetValue(Of String)(row("value"), String.Empty)
                Dim strKey As String = Common.GetValue(Of String)(row("key"), String.Empty)

                Select Case strKeyValue
                    Case "Country"
                        adds.Country = strValue

                    Case "CountryCode"
                        adds.CountryCode = strValue

                    Case "State"
                        adds.Province = strValue

                    Case "City"
                        adds.City = strValue

                    Case "Street"
                        adds.Street = strValue

                    Case "ZIP"
                        adds.PostalCode = strValue

                End Select
            Next
        End If
    End Sub

    Private Sub LoadMulitValueEntryIM(ByVal strMultiValueID As String, ByRef aims As IMItem, ByRef info As Contact)
        If Me.mConn IsNot Nothing Then
            Dim strSQL As String = String.Format("SELECT A.*,B.value keyvalue FROM ABMultiValueEntry A Left Join ABMultiValueEntryKey B ON A.key=B.ROWID WHERE parent_id={0}", strMultiValueID)
            Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
            Dim strServer As String = ""
            Dim strUserName As String = ""
            For Each row As DataRow In dt.Rows
                Dim strKeyValue As String = Common.GetValue(Of String)(row("keyvalue"), String.Empty)
                Dim strValue As String = Common.GetValue(Of String)(row("value"), String.Empty)

                Select Case strKeyValue
                    Case "service"
                        strServer = strValue

                    Case "username"
                        strUserName = strValue

                End Select
            Next
            If strServer.Length > 0 AndAlso strUserName.Length > 0 Then
                aims.Value = strUserName
                aims.ID = strMultiValueID
                aims.Label = strServer
                info.IMs.Add(aims)
            End If

        End If
    End Sub

    '加载图片
    Private Sub LoadPhoto(ByRef info As Contact)
        Try
            If Me.mConnImage IsNot Nothing Then
                Dim strSQL As String = "SELECT * From ABFullSizeImage WHERE record_id={0}"
                strSQL = String.Format(strSQL, info.Identifier)
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConnImage)
                For Each row As DataRow In dt.Rows
                    Dim buffer() As Byte = Common.GetValue(Of Byte())(row("data"), Nothing)
                    If buffer IsNot Nothing Then
                        Dim ms As MemoryStream = New MemoryStream(buffer)
                        info.Photo = Utility.AdjustImage(Image.FromStream(ms, True))
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException("BackUp Contact loadPhont:" & ex.ToString)
        End Try
    End Sub

    Public Function SaveContact(ByVal listContact As List(Of Contact), ByVal listGroup As List(Of ContactGroup)) As Boolean
        Return Me.SaveContact(listContact, listGroup, Nothing)
    End Function

    Public Function SaveContact(ByVal listContact As List(Of Contact), ByVal listGroup As List(Of ContactGroup), ByVal exitCallBack As SyncConflictEventHandler) As Boolean
        Me.Reload()
        Dim lstContact As List(Of Contact) = Me.LoadContact
        Dim connectBySelf As Boolean = False

        Dim args As New SyncConflictEventArgs()
        Dim listCover As New List(Of Contact)
        Dim listSkip As New List(Of Contact)

        For Each item As Contact In listContact
            If item.ChangeType = ChangeType.Add Then
                Dim existItem As Contact = Nothing

                For Each subItem As Contact In lstContact
                    If subItem.ChangeType = ChangeType.Add Then
                        Continue For
                    End If

                    If (subItem.LastName = item.LastName AndAlso subItem.FirstName = item.FirstName _
                            OrElse subItem.LastName & subItem.FirstName = item.LastName & item.FirstName) _
                            AndAlso Not Me.CheckCoverContactExist(listCover, subItem) Then

                        existItem = subItem
                        GoTo D0_EXIST
                    End If
                Next
D0_EXIST:
                If existItem IsNot Nothing Then
                    If Not args.UnPrompt Then
                        args.SelectType = SelectType.Skip
                        args.SrcData = existItem
                        args.DesData = item

                        If exitCallBack IsNot Nothing Then
                            exitCallBack(args)
                        End If
                    End If

                    Select Case args.SelectType
                        Case SelectType.Cover    ' 覆盖 
                            listCover.Add(existItem)

                        Case SelectType.CreateNew   ' 新建 

                        Case SelectType.Skip     ' 跳过 
                            listSkip.Add(item)

                        Case SelectType.Cancel
                            Return False

                    End Select
                End If
            End If
            System.Windows.Forms.Application.DoEvents()
        Next

        For Each item As Contact In listSkip
            listContact.Remove(item)
            For Each group As ContactGroup In listGroup
                If group.Members.ContainsKey(item.Identifier) Then
                    'group.Members.Remove(item.Identifier)
                    group.Members(item.Identifier).ChangeType = ChangeType.Del
                End If
            Next
        Next

        For Each item As Contact In listCover
            item.ChangeType = ChangeType.Del

            If Not listContact.Contains(item) Then
                listContact.Add(item)
            End If

            For Each group As ContactGroup In Me.LoadGroup()
                If group.Members.ContainsKey(item.Identifier) AndAlso Not listGroup.Contains(group) Then
                    'group.Members.Remove(item.Identifier)
                    group.Members(item.Identifier).ChangeType = ChangeType.Del
                    listGroup.Add(group)
                End If
            Next
        Next

        If listContact.Count = 0 AndAlso listGroup.Count = 0 Then
            Return True
        End If

        'If Me.mDevice.mSocketMobileSync <= 0 Then
        '    connectBySelf = True
        '    Me.mDevice.StartMobileSync()
        'End If

        'If Me.mDevice.mSocketMobileSync <= 0 Then
        '    Return False
        'End If

        'Dim inSocket As Integer = Me.mDevice.mSocketMobileSync
        'Me.mDevice.SendMessageSyncDataClassWithDevice(inSocket, DomainName)
        'Me.mDevice.SendMessageAcknowledgeChangesFromDevice(inSocket, DomainName)
        'Me.mDevice.SendMessageGetChangesFromDevice(inSocket, DomainName)
        'Me.mDevice.SendMessagePing(inSocket, DomainName)
        'Me.SendMessageProcessChanges(inSocket, listContact, listGroup)
        'Me.mDevice.SendMessageFinishSessionOnDevice(inSocket, Me.DomainName)

        Dim listAdd As New List(Of Contact)
        Dim listMod As New List(Of Contact)
        Dim listDel As New List(Of Contact)

        For Each item As Contact In listContact
            Select Case item.ChangeType
                Case ChangeType.Add
                    listAdd.Add(item)

                Case ChangeType.Mod
                    listMod.Add(item)

                Case ChangeType.Del
                    listDel.Add(item)

            End Select
        Next

        Dim lst As New List(Of String)
        For Each item As Contact In listDel
            If item.Photo IsNot Nothing Then
                item.Photo.Dispose()
                item.Photo = Nothing
            End If

            lst.Add(item.Identifier)
            'Me.mListContact.Remove(item)
            For Each group As ContactGroup In Me.LoadGroup
                If group.Members.ContainsKey(item.Identifier) Then
                    'group.Members.Remove(item.Identifier)
                    group.Members(item.Identifier).ChangeType = ChangeType.Del
                    'Exit For
                End If
            Next
        Next
        If lst.Count > 0 Then
            Me.Delete(lst)
        End If
        lst.Clear()
        lst = Nothing

        Me.SaveContactToDB(listMod, listGroup, False)
        For Each item As Contact In listMod
            item.ChangeType = ChangeType.None
        Next

        Me.SaveContactToDB(listAdd, listGroup, True)
        For Each item As Contact In listAdd
            item.ChangeType = ChangeType.None
        Next

        Me.SaveToBackupFile()
        Return True
    End Function

    Private Function SaveContactToDB(ByVal lstContactAdd As List(Of Contact), ByVal listGroup As List(Of ContactGroup), Optional ByVal IsInsert As Boolean = False)
        Dim blnReturn As Boolean = True
        If lstContactAdd IsNot Nothing AndAlso lstContactAdd.Count > 0 Then
            If IsInsert Then
                Me.InsertABPerson(lstContactAdd, listGroup)
            Else
                Me.UpdateABPerson(lstContactAdd)
            End If
        End If
        Return blnReturn
    End Function

    'Insert
    Public Function InsertABPerson(ByVal lstContactAdd As List(Of Contact), ByVal listGroup As List(Of ContactGroup)) As Integer
        Dim intReturn As String = -1
        If Me.mConn IsNot Nothing Then
            Try
                Dim strSQL As String = ""
                For Each Item As Contact In lstContactAdd
                    Dim a As String = ""
                    strSQL = "DELETE FROM ABMultiValueEntry WHERE parent_id IN (SELECT UID FROM ABMultiValue WHERE record_id =" & Item.Identifier & ")" & vbCrLf
                    intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, Nothing, a)
                    strSQL = "DELETE FROM ABMultiValue WHERE record_id =" & Item.Identifier & vbCrLf
                    intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, Nothing, a)
                    strSQL = "DELETE FROM ABPerson WHERE ROWID =" & Item.Identifier & vbCrLf
                    intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, Nothing, a)

                    strSQL = Me.GetInsertSQLContact(Item)
                    intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)

                    Dim rowId As Integer = 0
                    If intReturn > 0 Then
                        Dim dt As DataTable = SQLiteClass3.ExecuteSQL("SELECT seq FROM sqlite_sequence WHERE name = 'ABPerson'", Me.mConn)
                        If dt.Rows.Count > 0 Then
                            rowId = Convert.ToInt32(dt.Rows(0)(0))
                            Item.Identifier = rowId
                        End If
                    End If

                    '加入GROUP
                    If rowId > 0 AndAlso listGroup IsNot Nothing AndAlso listGroup.Count > 0 Then
                        For Each ItemGroup As ContactGroup In listGroup
                            Me.InsertGroupMembers(ItemGroup, rowId)
                        Next
                    End If

                    If rowId > 0 Then
                        intReturn = Me.UpdateABMulitValue(Item, rowId)
                    End If

                    intReturn = Me.InsertPhoto(Item)
                Next

                If intReturn <= 0 Then
                    intReturn = -1
                End If
            Catch ex As Exception
                intReturn = -1
            End Try
        End If
        Return intReturn
    End Function

    Public Function InsertABMulitValueEntry(ByVal contact As Contact, ByVal strROWID As String) As Integer
        Dim intReturn As Integer = -1

        Dim strInsertABMulitValue As String = "INSERT INTO ABMultiValue({0}) VALUES({1})"
        Dim strInsertABMulitValueEntry As String = "INSERT INTO ABMultiValueEntry({0}) VALUES({1})"
        Dim strSQL As String = ""

        '先删除当前的记录再添加进去


        Dim rowId As Integer = 0
        '5Address
        For Each Item As AddressItem In contact.Addresses
            rowId = Me.InsertOrUpdateABMulitValue(strROWID, 5, Item.ID, Me.GetABMultiValueLabelKey(Item.Label), "", Item.ID)

            strSQL = String.Format("DELETE FROM ABMultiValueEntry WHERE parent_id={0}", rowId)
            intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)

            If Item.Country.Length > 0 Then
                strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("Country"), Item.Country)) & vbCrLf
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
            End If

            If Item.CountryCode.Length > 0 Then
                strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("CountryCode"), Item.CountryCode)) & vbCrLf
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
            End If

            If Item.Province.Length > 0 Then
                strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("State"), Item.Province)) & vbCrLf
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
            End If

            If Item.City.Length > 0 Then
                strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("City"), Item.City)) & vbCrLf
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
            End If

            If Item.Street.Length > 0 Then
                strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("Street"), Item.Street)) & vbCrLf
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
            End If

            If Item.PostalCode.Length > 0 Then
                strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("ZIP"), Item.PostalCode)) & vbCrLf
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
            End If
        Next

        '13IMs
        For Each Item As IMItem In contact.IMs

            strSQL = String.Format("DELETE FROM ABMultiValueEntry WHERE parent_id={0}", rowId)
            intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)

            rowId = Me.InsertOrUpdateABMulitValue(strROWID, 13, Item.ID, Me.GetABMultiValueLabelKey(Item.Label), "", Item.ID)

            strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("service"), Item.IMType.ToString)) & vbCrLf
            intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)

            If Item.Value.Length > 0 Then
                strSQL = String.Format(strInsertABMulitValueEntry, "parent_id,[key],value", String.Format("{0},{1},'{2}'", rowId, Me.GetABMultiValueEntryKey("username"), Item.Value)) & vbCrLf
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
            End If
        Next

        Return intReturn
    End Function

    Private Function GetABMultiValueEntryKey(ByVal strValue As String) As String
        Dim strReturn As String = "1"
        Dim dt As DataTable = SQLiteClass3.ExecuteSQL(String.Format("SELECT ROWID FROM ABMultiValueEntryKey WHERE value='{0}' ", strValue), Me.mConn)
        If dt.Rows.Count > 0 Then
            strReturn = Convert.ToInt32(dt.Rows(0)(0))
        End If
        Return strReturn
    End Function

    Private Function GetABMultiValueLabelKey(ByVal strValue As String) As String
        Dim strReturn As String = "1"
        Dim dt As DataTable = SQLiteClass3.ExecuteSQL(String.Format("SELECT ROWID FROM ABMultiValueLabel WHERE value='{0}' ", strValue), Me.mConn)
        If dt.Rows.Count > 0 Then
            strReturn = Convert.ToInt32(dt.Rows(0)(0))
        End If
        Return strReturn
    End Function

    Private Function GetInsertSQLContact(ByVal contact As Contact) As String
        Dim strInsertABPerson As String = "INSERT INTO ABPerson({0}) VALUES({1})"
        Dim strColum As String = ""
        Dim strValue As String = ""
        'ABPerson
        strColum &= "First,"
        strValue &= String.Format("'{0}',", contact.FirstName)
        strColum &= "Last,"
        strValue &= String.Format("'{0}',", contact.LastName)
        strColum &= "Middle,"
        strValue &= String.Format("'{0}',", contact.MiddleName)
        strColum &= "FirstPhonetic,"
        strValue &= String.Format("'{0}',", contact.FirstNamePinyin)
        'strColum &= "MiddlePhonetic,"
        'strValue &= String.Format("'{0}',", contact.FirstNamePinyin)
        strColum &= "LastPhonetic,"
        strValue &= String.Format("'{0}',", contact.LastNamePinyin)
        strColum &= "Organization,"
        strValue &= String.Format("'{0}',", contact.CompanyName)
        strColum &= "Department,"
        strValue &= String.Format("'{0}',", contact.Department)
        strColum &= "Note,"
        strValue &= String.Format("'{0}',", contact.Notes)
        strColum &= "Kind,"
        strValue &= String.Format("{0},", 0)
        '在转换

        strColum &= "Birthday,"
        strValue &= String.Format("'{0}',", contact.Birthday)
        strColum &= "JobTitle,"
        strValue &= String.Format("'{0}',", contact.JobTitle)
        strColum &= "Nickname,"
        strValue &= String.Format("'{0}',", contact.NickName)
        strColum &= "Prefix,"
        strValue &= String.Format("'{0}',", contact.Prefix)
        strColum &= "Suffix,"
        strValue &= String.Format("'{0}',", contact.Suffix)
        '查找在

        strColum &= "FirstSort,"
        strValue &= String.Format("'{0}',", "")
        strColum &= "LastSort,"
        strValue &= String.Format("'{0}',", "")
        strColum &= "CreationDate,"
        strValue &= String.Format("{0},", Common.ConvertToPhoneTime(DateTime.Now))
        strColum &= "ModificationDate,"
        strValue &= String.Format("{0},", Common.ConvertToPhoneTime(DateTime.Now))

        'strColum &= "CompositeNameFallback,"
        'strValue &= String.Format("'{0}',", contact.FirstName)
        'strColum &= "ExternalIdentifier,"
        'strValue &= String.Format("'{0}',", contact.FirstName)
        'strColum &= "ExternalModificationTag,"
        'strValue &= String.Format("'{0}',", contact.FirstName)
        'strColum &= "ExternalUUID,"
        'strValue &= String.Format("'{0}',", contact.FirstName)

        strColum &= "StoreID,"
        strValue &= String.Format("{0},", 0)
        'strColum &= "DisplayName,"
        'strValue &= String.Format("'{0}',", contact.Identifier)
        'strColum &= "ExternalRepresentation,"
        'strValue &= String.Format("{0},", contact.FirstName)
        Dim strFirstSortSection As String = Me.GetSortSection(contact.FirstNamePinyin & contact.FirstName & contact.LastNamePinyin & contact.LastName)
        Dim strLastSortSection As String = Me.GetSortSection(contact.LastNamePinyin & contact.LastName & contact.FirstNamePinyin & contact.FirstName)
        If contact.FirstNamePinyin & contact.FirstName & contact.LastNamePinyin & contact.LastName = "" Then
            strFirstSortSection = "~"
            strLastSortSection = "~"
        End If
        strColum &= "FirstSortSection,"
        strValue &= String.Format("'{0}',", strFirstSortSection)
        strColum &= "LastSortSection,"
        strValue &= String.Format("'{0}',", strLastSortSection)
        strColum &= "FirstSortLanguageIndex,"
        strValue &= String.Format("{0},", 0)
        strColum &= "LastSortLanguageIndex,"
        strValue &= String.Format("{0},", 0)
        strColum &= "PersonLink,"
        strValue &= String.Format("{0},", -1)
        'strColum &= "ImageURI,"
        'strValue &= String.Format("'{0}',", contact.FirstName)
        strColum &= "IsPreferredName"
        strValue &= String.Format("{0}", 1)

        strInsertABPerson = String.Format(strInsertABPerson, strColum, strValue)
        Return strInsertABPerson
    End Function

    Private Function CheckCoverContactExist(ByVal listCover As List(Of Contact), ByVal cat As Contact) As Boolean
        Dim blnReturn As Boolean = False
        If listCover IsNot Nothing Then
            For Each Item As Contact In listCover
                If cat.Identifier = Item.Identifier Then
                    blnReturn = True
                    Exit For
                End If
            Next
        End If

        Return blnReturn
    End Function

    'Update
    Public Function UpdateABPerson(ByVal lstContactAdd As List(Of Contact)) As Integer
        Dim intReturn As String = -1
        If Me.mConn IsNot Nothing Then
            Try
                Dim strSQL As String = ""
                For Each Item As Contact In lstContactAdd
                    strSQL = Me.GetUpdateSQLContact(Item)
                    intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)

                    If intReturn >= 0 Then
                        intReturn = Me.UpdateABMulitValue(Item, Item.Identifier)
                    End If
                Next

                If intReturn <= 0 Then
                    intReturn = -1
                End If
            Catch ex As Exception
                intReturn = -1
            End Try
        End If
        Return intReturn
    End Function

    Private Function GetUpdateSQLContact(ByVal contact As Contact) As String
        Dim strUpdateABPerson As String = "UPDATE ABPerson SET {0} WHERE ROWID={1}"
        Dim strSetValue As String = ""

        strSetValue &= String.Format("{0}='{1}',", "First", contact.FirstName)
        strSetValue &= String.Format("{0}='{1}',", "Last", contact.LastName)
        strSetValue &= String.Format("{0}='{1}',", "Middle", contact.MiddleName)
        strSetValue &= String.Format("{0}='{1}',", "FirstPhonetic", contact.FirstNamePinyin)
        strSetValue &= String.Format("{0}='{1}',", "LastPhonetic", contact.LastNamePinyin)
        strSetValue &= String.Format("{0}='{1}',", "Organization", contact.CompanyName)
        strSetValue &= String.Format("{0}='{1}',", "Department", contact.Department)
        strSetValue &= String.Format("{0}='{1}',", "Note", contact.Notes)
        strSetValue &= String.Format("{0}='{1}',", "Birthday", contact.Birthday)
        strSetValue &= String.Format("{0}='{1}',", "JobTitle", contact.JobTitle)
        strSetValue &= String.Format("{0}='{1}',", "Nickname", contact.NickName)
        strSetValue &= String.Format("{0}='{1}',", "Prefix", contact.Prefix)
        strSetValue &= String.Format("{0}='{1}',", "Suffix", contact.Suffix)

        'strSetValue &= String.Format("{0}={1},", "CreationDate", Common.ConvertToPhoneTime(DateTime.Now))
        strSetValue &= String.Format("{0}={1},", "ModificationDate", 0)

        Dim strFirstSortSection As String = Me.GetSortSection(contact.FirstNamePinyin & contact.FirstName & contact.LastNamePinyin & contact.LastName)
        Dim strLastSortSection As String = Me.GetSortSection(contact.LastNamePinyin & contact.LastName & contact.FirstNamePinyin & contact.FirstName)
        If contact.FirstNamePinyin & contact.FirstName & contact.LastNamePinyin & contact.LastName = "" Then
            strFirstSortSection = "~"
            strLastSortSection = "~"
        End If

        strSetValue &= String.Format("{0}='{1}',", "FirstSortSection", strFirstSortSection)
        strSetValue &= String.Format("{0}='{1}'", "LastSortSection", strLastSortSection)
        strUpdateABPerson = String.Format(strUpdateABPerson, strSetValue, contact.Identifier)
        Return strUpdateABPerson
    End Function

    Public Function UpdateABMulitValue(ByVal contact As Contact, ByVal strROWID As String) As Integer
        Dim intReturn As String = -1
        If Me.mConn IsNot Nothing Then
            Try
                '3
                For Each Item As ContactItem In contact.PhoneNumbers
                    Me.InsertOrUpdateABMulitValue(strROWID, 3, Item.ID, Me.GetABMultiValueLabelKey(Item.Label), Item.Value, Item.ID)
                Next
                '4
                For Each Item As ContactItem In contact.Emails
                    Me.InsertOrUpdateABMulitValue(strROWID, 4, Item.ID, Me.GetABMultiValueLabelKey(Item.Label), Item.Value, Item.ID)
                Next
                '12
                For Each Item As ContactItem In contact.Dates
                    Me.InsertOrUpdateABMulitValue(strROWID, 12, Item.ID, Me.GetABMultiValueLabelKey(Item.Label), Item.Value, Item.ID)
                Next
                '22
                For Each Item As ContactItem In contact.URLs
                    Me.InsertOrUpdateABMulitValue(strROWID, 22, Item.ID, Me.GetABMultiValueLabelKey(Item.Label), Item.Value, Item.ID)
                Next
                '23
                For Each Item As ContactItem In contact.Relateds
                    Me.InsertOrUpdateABMulitValue(strROWID, 23, Item.ID, Me.GetABMultiValueLabelKey(Item.Label), Item.Value, Item.ID)
                Next
                '插入address 和 ims
                '5 13
                intReturn = Me.InsertABMulitValueEntry(contact, strROWID)

                intReturn = Me.InsertPhoto(contact)

            Catch ex As Exception
            End Try
        End If
        Return intReturn
    End Function

    Private Function InsertOrUpdateABMulitValue(ByVal strROWID As String, ByVal strProperty As String, ByVal strId As String, ByVal strlabel As String, ByVal strValue As String, ByVal strUIDMultiValue As String) As Integer
        Dim intUID As Integer = -1
        Dim isExist As Boolean = False
        Dim strSQL As String = String.Format("SELECT UID FROM ABMultiValue WHERE UID={0}", strUIDMultiValue)

        Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
        If dt.Rows.Count > 0 Then
            isExist = True
        End If
        If isExist Then
            strSQL = String.Format("UPDATE ABMultiValue SET record_id={0},property={1},identifier={2},label={3},value='{4}' WHERE UID={5}", strROWID, strProperty, strId, strlabel, strValue, strUIDMultiValue)
        Else
            Dim strInsertABMulitValue As String = "INSERT INTO ABMultiValue({0}) VALUES({1})"
            strSQL = String.Format(strInsertABMulitValue, "record_id,property,identifier,label,value", String.Format("{0},{1},{2},{3},'{4}'", strROWID, strProperty, strId, strlabel, strValue))
        End If

        Dim intUpdate As Integer = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)

        If intUpdate > 0 Then
            If isExist Then
                intUID = strUIDMultiValue
            Else
                dt = SQLiteClass3.ExecuteSQL("SELECT Max(UID) FROM ABMultiValue ", Me.mConn)
                If dt.Rows.Count > 0 Then
                    intUID = Convert.ToInt32(dt.Rows(0)(0))
                End If
            End If
        End If

        If strProperty = "3" Then
            strSQL = String.Format("DELETE FROM ABPhoneLastFour WHERE multivalue_id ={0}", intUID)
            intUpdate = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)

            Dim strForMumber As String = strValue.Trim().Replace("-", "")
            If strForMumber.Length > 4 Then
                strForMumber = strForMumber.Substring(strForMumber.Length - 4, 4)
            End If
            strSQL = String.Format("INSERT INTO ABPhoneLastFour (multivalue_id,value) VALUES({0},'{1}')", intUID, strForMumber)
            intUpdate = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn)
        End If

        Return intUID
    End Function

    Private Function InsertPhoto(ByVal info As Contact) As Boolean
        Dim blnReturn As Boolean = True
        Try
            If Me.mConnImage IsNot Nothing Then
                Dim intResult As Integer = 0
                Dim strSQL As String = String.Format("DELETE FROM ABFullSizeImage WHERE record_id={0}", info.Identifier)
                intResult = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConnImage)

                strSQL = String.Format("DELETE FROM ABThumbnailImage WHERE record_id={0}", info.Identifier)
                intResult = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConnImage)

                If info IsNot Nothing AndAlso info.Photo IsNot Nothing Then

                    Dim ms As MemoryStream = New MemoryStream()
                    '图片大小为320X320
                    Dim imgTemp As Image = Utility.GetThumbnail(info.Photo, New Size(320, 320))
                    imgTemp.Save(ms, System.Drawing.Imaging.ImageFormat.Png)
                    Dim buffer(ms.Length) As Byte

                    ms.Position = 0
                    ms.Read(buffer, 0, buffer.Length)
                    ms.Close()

                    Dim paraImage As New SQLite3.SQLiteParameter
                    paraImage.DbType = DbType.Binary
                    paraImage.ParameterName = "@image"
                    paraImage.Value = buffer
                    Dim lstPara As New List(Of SQLite3.SQLiteParameter)
                    lstPara.Add(paraImage)

                    strSQL = String.Format("INSERT INTO ABFullSizeImage({0}) VALUES({1})", "record_id,crop_x,crop_y,crop_width,data", _
                                                                                                          String.Format("{0},{1},{2},{3},{4}", info.Identifier, 0, 0, 320, "@image"))
                    intResult = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConnImage, lstPara.ToArray, Nothing, strSQL)

                    strSQL = String.Format("INSERT INTO ABThumbnailImage({0}) VALUES({1})", "record_id,format,data", String.Format("{0},{1},{2}", info.Identifier, 0, "@image"))
                    intResult = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConnImage, lstPara.ToArray, Nothing, strSQL)

                    strSQL = String.Format("INSERT INTO ABThumbnailImage({0}) VALUES({1})", "record_id,format,data", String.Format("{0},{1},{2}", info.Identifier, 5, "@image"))
                    intResult = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConnImage, lstPara.ToArray, Nothing, strSQL)

                    lstPara.Clear()
                    lstPara = Nothing
                    imgTemp = Nothing
                    buffer = Nothing
                End If
            End If
        Catch ex As Exception
        End Try
        Return blnReturn
    End Function

#End Region

#Region " Groups 操作 "

    Public Function GetGroups() As List(Of ContactGroup)
        '加载数据文件
        Me.Reload()
        '取得所有联系人基本信息
        Dim listGroup As List(Of ContactGroup) = Me.LoadGroup()

        Me.Dispose()
        Return listGroup
    End Function

    Public Function GetGroups(ByVal strName As String) As ContactGroup
        '加载数据文件
        Me.Reload()
        '取得所有联系人基本信息
        Dim group As ContactGroup = Me.LoadGroup(strName)

        Me.Dispose()
        Return group
    End Function

    Private Function LoadGroup() As List(Of ContactGroup)
        Dim listInfo As New List(Of ContactGroup)
        Dim dictInfo As New Dictionary(Of String, ContactGroup)
        Try
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = "SELECT A.ROWID,A.Name,B.member_id FROM ABGroup A Left Join ABGroupMembers B ON A.ROWID=B.group_id "
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim strGroupID As String = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        Dim strGroupName As String = Common.GetValue(Of String)(row("Name"), String.Empty)
                        Dim strMember As String = Common.GetValue(Of String)(row("member_id"), String.Empty)
                        Dim info As ContactGroup = Nothing
                        If Not dictInfo.ContainsKey(strGroupID) Then
                            info = New ContactGroup()
                            info.ID = strGroupID
                            info.Label = strGroupName
                            info.Name = strGroupName
                            info.Members = New Dictionary(Of String, SubItem)
                            dictInfo.Add(strGroupID, info)
                        Else
                            info = dictInfo(strGroupID)
                        End If
                        info.Members.Add(strMember, New SubItem(strMember))

                    Next
                End If
            End If

            If dictInfo.Count > 0 Then
                listInfo.AddRange(dictInfo.Values())
            End If

            dictInfo.Clear()
            dictInfo = Nothing
        Catch ex As Exception
        End Try

        Return listInfo
    End Function

    Private Function LoadGroup(ByVal strName As String) As ContactGroup
        Dim group As ContactGroup = Nothing
        Dim dictInfo As New Dictionary(Of String, ContactGroup)
        Try
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = "SELECT A.ROWID,A.Name,B.member_id FROM ABGroup A Left Join ABGroupMembers B ON A.ROWID=B.group_id WHERE A.Name='{0}'"
                strSQL = String.Format(strSQL, strName)

                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim strGroupID As String = Common.GetValue(Of String)(row("ROWID"), String.Empty)
                        Dim strGroupName As String = Common.GetValue(Of String)(row("Name"), String.Empty)
                        Dim strMember As String = Common.GetValue(Of String)(row("member_id"), String.Empty)

                        If Not dictInfo.ContainsKey(strGroupID) Then
                            group = New ContactGroup()
                            group.ID = strGroupID
                            group.Label = strGroupName
                            group.Name = strGroupName
                            group.Members = New Dictionary(Of String, SubItem)
                            dictInfo.Add(strGroupID, group)
                        Else
                            group = dictInfo(strGroupID)
                        End If
                        group.Members.Add(strMember, New SubItem(strMember))

                    Next
                End If
            End If

            If dictInfo.Count > 0 Then
                For Each Item As ContactGroup In dictInfo.Values
                    group = Item
                    Exit For
                Next
            End If

            dictInfo.Clear()
            dictInfo = Nothing
        Catch ex As Exception
        End Try

        Return group
    End Function

    Private Function InsertGroupMembers(ByVal group As ContactGroup, ByVal strMemberID As String)
        Dim blnReturn As Boolean = False
        Dim strSQL As String = "INSERT INTO ABGroupMembers({0}) VALUES({1})"
        Dim intReturn As Integer = SQLiteClass3.ExecuteNoneQuery(String.Format(strSQL, "UID,group_id,member_type,member_id", String.Format("{0},{1},{2},{3}", 0, group.ID, 0, strMemberID)), Me.mConn)
        Return blnReturn
    End Function

#End Region

#Region " Delete操作 "

    Public Function Delete(ByVal strID As String) As Integer
        Dim lstID As New List(Of String)
        lstID.Add(strID)
        Return Me.Delete(lstID)
    End Function

    Public Function Delete(ByVal lstContactIDs As List(Of String)) As Integer
        Dim intReturn As String = -1

        If Me.mConn IsNot Nothing Then
            intReturn = 0
            Try
                Dim blnSucc As Boolean = True
                Dim ids As String = ""
                For Each Item As String In lstContactIDs
                    If ids.Length > 0 Then
                        ids &= "," & Item
                    Else
                        ids = Item
                    End If
                Next
                ids = String.Format("({0})", ids)
                Dim tran As IDbTransaction = Nothing
                tran = Me.mConn.BeginTransaction

                Dim strSQL As String = "DELETE FROM ABMultiValueEntry WHERE parent_id IN (SELECT UID FROM ABMultiValue WHERE record_id in  " & ids & ")"
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, tran, "")
                If intReturn < 0 Then
                    blnSucc = False
                End If

                strSQL = "DELETE FROM ABMultiValue WHERE record_id in " + ids
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, tran, "")
                If intReturn < 0 Then
                    blnSucc = False
                End If

                strSQL = "DELETE FROM ABPerson WHERE ROWID in " + ids
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, tran, "")
                If intReturn < 0 Then
                    blnSucc = False
                End If

                strSQL = "DELETE FROM ABGroupMembers WHERE member_id in " + ids
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, tran, "")
                If intReturn < 0 Then
                    blnSucc = False
                End If

                strSQL = "DELETE FROM ABPhoneLastFour WHERE multivalue_id in (SELECT UID FROM ABMultiValue WHERE record_id in  " & ids & ")"
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, tran, "")
                If intReturn < 0 Then
                    blnSucc = False
                End If

                strSQL = "DELETE FROM ABPersonSearchKey WHERE person_id in " + ids
                intReturn = SQLiteClass3.ExecuteNoneQuery(strSQL, Me.mConn, Nothing, tran, "")
                If intReturn < 0 Then
                    blnSucc = False
                End If

                If Not blnSucc Then
                    intReturn = -1
                    tran.Rollback()
                Else
                    tran.Commit()
                End If

            Catch ex As Exception
                intReturn = -1
            End Try
        End If

        If intReturn >= 0 Then
            Me.SaveToBackupFile()
        End If
        Return intReturn
    End Function

#End Region

    Public Function LabelToTextType(ByVal strValue As String) As LabelType
        Dim type As LabelType = LabelType.Other
        Select Case strValue
            Case "_$!<Mobile>!$_"
                type = LabelType.Mobile
            Case "iPhone"
                type = LabelType.iPhone
            Case "_$!<Other>!$_"
                type = LabelType.Other
            Case "_$!<Work>!$_"
                type = LabelType.Work
            Case "_$!<Home>!$_"
                type = LabelType.Home
            Case "_$!<HomePage>!$_"
                type = LabelType.HomePage
            Case "_$!<Main>!$_"
                type = LabelType.Main
            Case "_$!HomePage!$_"
                type = LabelType.HomePage
            Case "_$!Other!$_"
                type = LabelType.Other
            Case "_$!<HomeFAX>!$_"
                type = LabelType.HomeFax
            Case "_$!<Parent>!$_"
                type = LabelType.Parent
            Case "_$!<Anniversary>!$_"
                type = LabelType.Anniversary
            Case "_$!<WorkFAX>!$_"
                type = LabelType.WorkFax
            Case "_$!<Pager>!$_"
                type = LabelType.Pager

        End Select
        Return type
    End Function

    Private Function GetPercentByID(ByVal listInfo As List(Of Contact), ByVal strID As String) As Contact
        Dim percent As Contact = Nothing
        For Each Item As Contact In listInfo
            If Item.Identifier = strID Then
                percent = Item
                Exit For
            End If
        Next
        Return percent
    End Function

    '保存到备份文件中
    Public Function SaveToBackupFile() As Boolean
        Dim blnReturn As Boolean = True

        Try
            If Me.mConn IsNot Nothing Then
                Me.mConn.Close()
            End If
            'File.Copy("C:\Documents and Settings\zhoudaohui\桌面\NewSNS\AddressBook.sqlitedb", Me.mDbPathOnBackupFolder, True)
            File.Copy(Me.mDbPathOnTempFolder, Me.mDbPathOnBackupFolder, True)
            If Me.mConnImage IsNot Nothing Then
                Me.mConnImage.Close()
            End If
            Try
                File.Copy(Me.mImageDbPathOnTempFolder, Me.mImageDbPathOnBackupFolder, True)
            Catch ex As Exception
            End Try
        Catch ex As Exception
            blnReturn = False
        End Try
        Me.Reload()

        Return blnReturn
    End Function

    Private Function GetSortSection(ByVal str As String) As String
        Dim strReturn As String = ""
        Dim strPinYin As String = PinYinClass.MakePinYin(str, PinYinOptions.FirstCharacterOnly)
        If strPinYin.Length > 0 Then
            Dim strFirst As String = strPinYin.Substring(0, 1)
            'Dim buffer() As Byte = System.Text.Encoding.ASCII.GetBytes("W")
            'If buffer.Length > 0 Then
            '    buffer(0) = buffer(0) + 24
            '    strReturn = System.Text.Encoding.ASCII.GetString(buffer)
            'End If

            Select Case strFirst
                Case "A"
                    strReturn = "''"
                Case "B"
                    strReturn = ")"
                Case "C"
                    strReturn = "+"
                Case "D"
                    strReturn = "-"
                Case "E"
                    strReturn = "/"
                Case "F"
                    strReturn = "1"
                Case "G"
                    strReturn = "3"
                Case "H"
                    strReturn = "5"
                Case "i"
                    strReturn = "7"
                Case "J"
                    strReturn = "9"
                Case "K"
                    strReturn = ";"
                Case "L"
                    strReturn = "="
                Case "M"
                    strReturn = "?"
                Case "N"
                    strReturn = "A"
                Case "O"
                    strReturn = "C"
                Case "P"
                    strReturn = "E"
                Case "Q"
                    strReturn = "G"
                Case "R"
                    strReturn = "I"
                Case "S"
                    strReturn = "K"
                Case "T"
                    strReturn = "M"
                Case "U"
                    strReturn = "O"
                Case "V"
                    strReturn = "Q"
                Case "W"
                    strReturn = "S"
                Case "X"
                    strReturn = "U"
                Case "Y"
                    strReturn = "W"
                Case "Z"
                    strReturn = "Y"
                Case ""
                    strReturn = ""
                Case Else
                    strReturn = ""
            End Select
        End If
        Return strReturn 
    End Function

#Region "--- 备份文件恢复删除联系人 ---"

    Private Sub LoadDeleteContactDBThread(ByVal strImport As String, ByVal strOutPut As String)
        Dim thr As New Threading.Thread(New Threading.ParameterizedThreadStart(AddressOf LoadDeleteContactDB))
        Try
            With thr
                .IsBackground = True
                .Start(New Object() {strImport, strOutPut, "contact"})
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadDeleteContactDBThread")
        End Try
    End Sub

    Private Sub LoadDeleteContactDB(ByVal paraArg As Object)
        Try
            Dim strImport As String = paraArg(0)
            Dim strOutPut As String = paraArg(1)
            Dim strType As String = paraArg(2)
            Dim strArguments As String = String.Format(" -i ""{0}"" -o ""{1}"" -t {2}", strImport, strOutPut, strType)
            Dim strUndark As String = "C:\Users\<USER>\Desktop\Addressbook\sqliteRecovery.exe"  'Path.Combine(Folder.AppFolder, "WeChat\WeChatHelper.exe")

            Dim info As ProcessStartInfo = New ProcessStartInfo()
            info.FileName = strUndark
            info.CreateNoWindow = True
            info.UseShellExecute = False
            info.Arguments = strArguments

            Dim process As Process = New Process()
            process.StartInfo = info
            process.Start()
            process.WaitForExit()
            process.Close()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadDeleteContactDB")
        End Try
    End Sub

#End Region

#Region "--- 解析删除掉的联系人信息 ---"

    Public Function GetDeleteContacts() As List(Of Contact)
        Dim listInfo As List(Of Contact) = New List(Of Contact)

        Return listInfo
    End Function

    Private Function ReadDeleteContactDB(ByVal strFile As String) As List(Of Contact)
        strFile = "C:\Users\<USER>\Desktop\Addressbook\ContactValue.txt"
        Dim lstReturn As New List(Of Contact)
        If Not File.Exists(strFile) Then
            Return lstReturn
        End If

        Dim strContact As String = File.ReadAllText(strFile)
        Dim chars() As Char = New Char() {Convert.ToChar(&HA), Convert.ToChar(&H1), Convert.ToChar(&H2), Convert.ToChar(&H1), Convert.ToChar(&HA)}
        Dim strLines As String() = strContact.Split(chars, StringSplitOptions.RemoveEmptyEntries)
        For Each item As String In strLines
            Dim values As String() = item.Split(";")
            Dim str_record_id As String = ""
            Dim str_property As String = ""
            Dim str_identifier As String = ""
            Dim str_label As String = ""
            Dim str_value As String = ""
            Dim str_labelName As String = ""

            For Each contactKey As String In values
                If contactKey.StartsWith("record_id") Then
                    str_record_id = contactKey.Replace("record_id:", "")
                ElseIf contactKey.StartsWith("property") Then
                    str_property = contactKey.Replace("property:", "")
                ElseIf contactKey.StartsWith("identifier") Then
                    str_identifier = contactKey.Replace("identifier:", "")
                ElseIf contactKey.StartsWith("label") Then
                    str_label = contactKey.Replace("label:", "")
                ElseIf contactKey.StartsWith("value") Then
                    str_value = contactKey.Replace("value:", "")
                End If
            Next

            Dim cItem As New ContactItem()
            cItem.ID = 0
            cItem.Key = String.Format("{0}/{1}/{2}", str_property, str_record_id, str_identifier)
            cItem.Label = Me.LoadMulitValueLabel(str_value)
            cItem.Value = str_value
            cItem.LabelType = Me.LabelToTextType(cItem.Label)
            If str_property = "" Then

            End If
        Next
        Return lstReturn
    End Function

    Private Function LoadMulitValueLabel(ByVal strValue As String) As String
        Dim strLabelValue As String = ""
        If Me.mConn IsNot Nothing Then
            Dim strSQL As String = "SELECT * From ABMultiValueLabel WHERE ROWID={0}"
            strSQL = String.Format(strSQL, strValue)
            Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
            For Each row As DataRow In dt.Rows
                strLabelValue = Common.GetValue(Of String)(row("value"), String.Empty)
            Next
        End If
        Return strLabelValue
    End Function

#End Region

#Region " IDisposable Support "

    Private disposedValue As Boolean = False        ' 检测冗余的调用

    ' IDisposable
    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If

            ' TODO: 释放共享的非托管资源
        End If
        Me.disposedValue = True
    End Sub
    ' Visual Basic 添加此代码是为了正确实现可处置模式。

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

#End Region

End Class
