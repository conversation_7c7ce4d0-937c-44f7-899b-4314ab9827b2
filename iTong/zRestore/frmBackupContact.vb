﻿Imports System.Threading
Imports iTong.CoreModule
Public Class frmBackupContact

    Private mBackObject As BackupInfo
    Private mStrDBFilePath As String = ""
    Private mStrImageDBFilePath As String = ""
    Private mDgvContact As New tbDataGridViewEx
    Private mThrLoadData As Thread
    Private mContacts As List(Of Contact)
    Private mBackupdb As BackupContactDB

#Region "--- 初始化 ---"

    Sub New()
        MyBase.New(Nothing, Nothing)
        ' 此调用是 Windows 窗体设计器所必需的。


        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。


    End Sub

    Public Sub New(ByVal application As IApplication, ByVal strDBFilePath As String, ByVal strImageDBFilePath As String, ByVal backupObj As BackupInfo)
        MyBase.New(application, Nothing)
        ' 此调用是 Windows 窗体设计器所必需的。


        ' 在 InitializeComponent() 调用之后添加任何初始化。

        InitializeComponent()
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.Icon = My.Resources.iTong
        Me.tbAutoSetFormSize = False
        Me.FilletRadius = 5

        Me.mBackObject = backupObj
        Me.mStrDBFilePath = strDBFilePath
        Me.mStrImageDBFilePath = strImageDBFilePath
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Dim size As Size = Me.tlpMain.Size

        Me.btnNewGroup.Enabled = False
        Me.tlpMain.Size = New Size(Me.Width - 3, Me.Height - 59)

        Me.btnDataRecovery.Visible = ServerIniSetting.IsShowTBGuide()
        '人工找回数据 红点
        If IniSetting.GetShowContactDataRecoveryNew() Then
            Me.btnDataRecovery.tbShowDot = True
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.pnliCloudNotify.Visible = False
        Me.pnlCloseiCloud.Visible = False
    End Sub

#End Region

    Protected Overrides Sub CheckShowiCloudInfo(Optional ByVal showInfo As Boolean = False)
        Me.pnliCloudNotify.Visible = False
        Me.pnliCloudNotify.SendToBack()
    End Sub

    Protected Overrides Sub SetCloseiCloud()
        Me.btnNewGroup.Visible = False
        Me.pnlCloseiCloud.Visible = False
        Me.pnlCloseiCloud.SendToBack()
        Me.btnNewContact.Visible = False
        Me.btnDelete.Visible = False
        Me.btnImport.Visible = False
        Me.pnlAddField.Visible = False
        Me.tlpMain.RowStyles(4).SizeType = SizeType.Absolute
        Me.tlpMain.RowStyles(4).Height = 0
        Me.lblModifyPic.Enabled = False
        Me.lblDeletePic.Enabled = False
        Me.picImage.Enabled = False
        Me.btnNewGroup.Visible = False
        Me.btnToRepeat.Visible = False
        Me.btnExport.Location = Me.btnNewGroup.Location
        Me.btnRefresh.Location = New Point(Me.pnlTop.Width - Me.btnRefresh.Width - 10, Me.btnRefresh.Top)

        Me.btnDataRecovery.Location = New Point(Me.btnRefresh.Left - Me.btnDataRecovery.Size.Width - 10, Me.btnRefresh.Top)

        'Me.txtLastName.ReadOnly = True
        'Me.txtFirstName.ReadOnly = True
        'Me.txtJob.ReadOnly = True
        'Me.txtDepartment.ReadOnly = True
        'Me.txtCompany.ReadOnly = True
        ''Me.cboGroup
    End Sub

    Protected Overrides Sub OnLoadContactDBDoWork(ByVal e As System.ComponentModel.DoWorkEventArgs)
        Me.mBackupdb = BackupContactDB.Instance(Me.mStrDBFilePath, Me.mStrImageDBFilePath)
        Me.mContacts = Me.mBackupdb.GetContacts()
        If Me.mContacts IsNot Nothing Then
            Me.mContactDB.Contacts = Me.mContacts
            Me.mContactDB.SortMode = SortMode.LastName Or SortMode.FirstName
        End If

        Dim lstGroup As List(Of ContactGroup) = Me.mBackupdb.GetGroups()
        If lstGroup IsNot Nothing AndAlso lstGroup.Count > 0 Then
            Me.mContactDB.Groups = lstGroup
        End If
    End Sub

    Protected Overrides Function GetGroupByName(ByVal strGroupName As String) As ContactGroup
        Return Me.mBackupdb.GetGroups(strGroupName)
    End Function

    Protected Overrides Sub ChangeGroup(ByVal group As ContactGroup)

    End Sub

    '设置group不可修改
    Protected Overrides Function CreateNode(ByVal group As ContactGroup) As Components.tbTreeNode
        Dim newNode As tbTreeNode = MyBase.CreateNode(group)
        newNode.ShowDischarge = False
        newNode.RenameEnable = False
        Return newNode
    End Function

    '设置备份到DB的文件名
    Public Overrides Sub ExportToDB(ByVal strDeviceName As String)
        If Me.mDevice Is Nothing Then
            strDeviceName = Me.mBackObject.DisplayName & "_Backup"
        End If
        MyBase.ExportToDB(strDeviceName)

    End Sub

    Protected Overrides Sub OnUpdateDoWork(ByVal e As Object)
        Select Case Me.mAction
            Case Action.GroupRename, Action.GroupDel
                'Me.mContactDB.Changes(CType(e.Argument, ContactGroup))

            Case Action.ContactDel
                '删除联系人


                Dim lstContace As List(Of Contact) = CType(e.Argument, List(Of Contact))
                If lstContace IsNot Nothing Then
                    Dim lstID As New List(Of String)
                    For Each item As Contact In lstContace
                        lstID.Add(item.Identifier)
                    Next
                    Me.mBackupdb.Delete(lstID)
                    Me.RemoveContactByID(lstID)
                End If
            Case Action.GroupRemoveContact
                'Me.mContactDB.Changes(CType(e.Argument, ContactGroup))

            Case Action.GroupAddContact
                'Dim arrObject() As Object = e.Argument
                'Me.mContactDB.Changes(CType(arrObject(0), List(Of Contact)), CType(arrObject(1), ContactGroup))

        End Select

    End Sub

    Protected Overrides Sub OnSaveDoWork(ByVal e As Object)
        'Select Case Me.mAction
        '    Case Action.SaveClick
        '        e.Result = Me.SaveToBackupDB(False, Nothing)

        '    Case Action.SaveClickSimple
        '        'e.Result = Me.SaveClickSimple(Nothing)

        '    Case Action.SaveChanged
        '        Dim args() As Object = e.Argument
        '        e.Result = Me.SaveToBackupDB(CBool(args(0)), CType(args(1), Contact))

        '    Case Action.ImportFromvCard
        '        '取出当前选中的Group的名称


        '        'Dim strDefaultGroup As String = ""
        '        'If Me.tvwDevice.SelectedNode.Tag IsNot Nothing AndAlso TypeOf Me.tvwDevice.SelectedNode.Tag Is ContactGroup Then
        '        '    Dim group As ContactGroup = Me.tvwDevice.SelectedNode.Tag
        '        '    strDefaultGroup = group.Name
        '        'End If
        '        'e.Result = ContactHelper.ImportFromvCard(e.Argument, Me.mContactDB, New SyncConflictEventHandler(AddressOf ShowConfirm), New ContactHelper.StartSaveDBHandler(AddressOf StartSaveDB), strDefaultGroup)

        '    Case Action.ImportFromOutlook
        '        'e.Result = ContactHelper.ImportFromOutlook(Me.mContactDB, New SyncEventHandler(AddressOf ShowProgress), New SyncConflictEventHandler(AddressOf ShowConfirm), New ContactHelper.StartSaveDBHandler(AddressOf StartSaveDB))

        '    Case Action.ImportFromDB
        '        'Me.StartSaveContactDB(e.Argument)
        '        'e.Result = Me.mContactDB.LoadFromDB(e.Argument, New SyncConflictEventHandler(AddressOf ShowConfirm))

        'End Select
    End Sub

    Protected Overrides Sub OnOperateMenuOpening()
        Me.tsmiAddToGroup.Enabled = False
        Me.tsmiRemoveFromGroup.Enabled = False
        Me.tsmiAdd.Enabled = False
        Me.tsmiImport.Enabled = False
        Me.tsmiDelete.Enabled = False

        Me.tsmiAddToGroup.Visible = False
        Me.tsmiRemoveFromGroup.Visible = False
        Me.tsmiAdd.Visible = False
        Me.tsmiImport.Visible = False
        Me.tsmiDelete.Visible = False
        Me.tss1.Visible = False
        Me.tss2.Visible = False
        Me.SetMenuForVCardExport(Me.tmsiTovCardSelected)
    End Sub

    Private Sub RemoveContactByID(ByVal lstMemberID As List(Of String))
        For Each Item As tbTreeNode In Me.tvwDevice.Nodes
            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is ContactGroup Then
                Dim group As ContactGroup = Item.Tag
                For Each Item1 As String In lstMemberID
                    'group.Members.Remove(Item1)
                    group.Members(Item1).ChangeType = ChangeType.Del
                Next
                Item.NodeNumber = group.Members.Count

            ElseIf Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is List(Of String) Then
                Dim lstMembers As List(Of String) = CType(Item.Tag, List(Of String))
                For Each Item1 As String In lstMemberID
                    lstMembers.Remove(Item1)
                Next
                Item.NodeNumber = lstMembers.Count
            Else
                Dim lstContact As New List(Of Contact)
                For Each itemContact As Contact In Me.mContactDB.Contacts
                    For Each strContactID As String In lstMemberID
                        If strContactID = itemContact.Identifier Then
                            lstContact.Add(itemContact)
                        End If
                    Next
                Next

                For Each itemContact As Contact In lstContact
                    Me.mContactDB.Contacts.Remove(itemContact)
                Next

                Item.NodeNumber = IIf(Me.mContactDB.Contacts.Count > 0, Me.mContactDB.Contacts.Count, 0)

                lstContact.Clear()
                lstContact = Nothing
            End If
        Next

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        If pblnCancelClose Then
            Return
        End If

        If Me.mBackupdb IsNot Nothing Then
            Me.mBackupdb.Close()
        End If

        Try
            If Me.mThrLoadData IsNot Nothing AndAlso Me.mThrLoadData.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadData.Abort()
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Function SaveToBackupDB(ByVal blnGroupChange As Boolean, ByVal item As Contact) As Boolean
        Dim blnReturn As Boolean = False
        If item Is Nothing Then
            Dim curRow As DataGridViewRow = Me.dgvContacts.CurrentRow
            If curRow Is Nothing Then
                Return False
            End If

            item = Me.GetCurrentContact()
        End If

        Dim group As ContactGroup = Me.GetCurrentGroup(blnGroupChange)
        Dim listGroup As List(Of ContactGroup) = Me.SaveContact(item, group)
        Dim listContact As New List(Of Contact)

        listContact.Add(item)

        If item.ChangeType = ChangeType.Add Then
            blnReturn = Me.mBackupdb.SaveContact(listContact, listGroup, New SyncConflictEventHandler(AddressOf ShowConfirm))
            Me.DeleteRow(listContact)
            Return blnReturn
        Else
            Return Me.mBackupdb.SaveContact(listContact, listGroup)
        End If
    End Function

    Protected Overrides Sub SetContralReadonly()
        MyBase.SetContralReadonly()
        'For Each ctl As Control In Me.pnl_Container.Controls
        '    For Each ctl1 As Control In ctl.Controls
        '        If TypeOf ctl1 Is tbTextBase Then
        '            CType(ctl1, tbTextBox).ReadOnly = True
        '        ElseIf Not TypeOf ctl1 Is Label Then
        '            ctl1.Enabled = False
        '        End If
        '    Next
        'Next
    End Sub

    Protected Overrides Sub CheckSaveChange(Optional ByVal blnGroupChange As Boolean = False, Optional ByVal contact As Contact = Nothing)
        'MyBase.CheckSaveChange(blnGroupChange, contact)
    End Sub

End Class

