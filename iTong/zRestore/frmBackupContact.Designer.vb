﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmBackupContact
    Inherits frmContact

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBackupContact))
        Me.cmsExport = New tbContextMenuStrip(Me.components)
        Me.tsmiExportTovCard = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiExportTovCardSelected = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiExportTovCardTotal = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiExportToOutlook = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiExportToDB = New System.Windows.Forms.ToolStripMenuItem
        Me.cmsExport.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'cmsExport
        '
        Me.cmsExport.AccessibleDescription = "161x92"
        Me.cmsExport.DropShadowEnabled = False
        Me.cmsExport.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsExport.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExportTovCard, Me.tsmiExportToOutlook, Me.tsmiExportToDB})
        Me.cmsExport.Name = "munOperate"
        Me.cmsExport.Size = New System.Drawing.Size(161, 70)
        Me.cmsExport.tbBackColor = System.Drawing.Color.White
        Me.cmsExport.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExport.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExport.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiExportTovCard
        '
        Me.tsmiExportTovCard.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExportTovCardSelected, Me.tsmiExportTovCardTotal})
        Me.tsmiExportTovCard.Name = "tsmiExportTovCard"
        Me.tsmiExportTovCard.Size = New System.Drawing.Size(160, 22)
        Me.tsmiExportTovCard.Text = "导出为vCard文件"
        '
        'tsmiExportTovCardSelected
        '
        Me.tsmiExportTovCardSelected.Name = "tsmiExportTovCardSelected"
        Me.tsmiExportTovCardSelected.Size = New System.Drawing.Size(124, 22)
        Me.tsmiExportTovCardSelected.Text = "导出""XXX"""
        '
        'tsmiExportTovCardTotal
        '
        Me.tsmiExportTovCardTotal.Name = "tsmiExportTovCardTotal"
        Me.tsmiExportTovCardTotal.Size = New System.Drawing.Size(124, 22)
        Me.tsmiExportTovCardTotal.Text = "导出全部"
        '
        'tsmiExportToOutlook
        '
        Me.tsmiExportToOutlook.Name = "tsmiExportToOutlook"
        Me.tsmiExportToOutlook.Size = New System.Drawing.Size(160, 22)
        Me.tsmiExportToOutlook.Text = "导出到Outlook中"
        Me.tsmiExportToOutlook.Visible = False
        '
        'tsmiExportToDB
        '
        Me.tsmiExportToDB.Name = "tsmiExportToDB"
        Me.tsmiExportToDB.Size = New System.Drawing.Size(160, 22)
        Me.tsmiExportToDB.Text = "导出为资料库"
        Me.tsmiExportToDB.Visible = False
        '
        'frmBackupContact
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        'Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Name = "frmBackupContact"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowTitleOnForm = True
        Me.Text = "联系人"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.cmsExport.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents cmsExport As tbContextMenuStrip
    Friend WithEvents tsmiExportTovCard As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportTovCardSelected As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportTovCardTotal As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToOutlook As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToDB As System.Windows.Forms.ToolStripMenuItem
End Class
