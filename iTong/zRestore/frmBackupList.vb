﻿Imports System.Threading
Imports iTong.CoreModule

Public Class frmBackupList

    Private mDgvBackupList As New tbDataGridViewEx
    Private mblnDeleting As Boolean = False
    Private mBackupXml As String = Path.Combine(Folder.BackupFolder, "BackupPathList.xml")
    Private mCreateBackupShow As Boolean = False
    Private mBackupHelper As BackupHelper = Nothing
    Private mCurrentRestoreRow As tbDataGridViewRow = Nothing

    Private mBackupListDeleteHelper As BackupListDeleteHelper = Nothing
    Private mTDImportBackup As Thread = Nothing

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal iPhone As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.mDevice = iPhone
        Me.Icon = My.Resources.iTong
        Me.tbAutoSetFormSize = True
        Me.Size = New Size(900, 600)
        Me.FunctionMappingKey = FunctionKey.Backup
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.InitGridView()
        Me.InitDescription()
        Me.InitBackupHelper()

        If Me.Parent IsNot Nothing Then
            Me.pnlControl.Top = 0
            Me.pnlContainer.Size = New Size(Me.Width, Me.Height - Me.pnlControl.Bottom)
            Me.pnlContainer.Location = New Point(0, Me.pnlControl.Bottom)

            Me.pnlDescription.Size = Me.pnlContainer.Size
            Me.pnlDescription.Location = Me.pnlContainer.Location
            Me.lblMessage.Visible = False
        End If

        If Me.mDevice Is Nothing Then
            Me.SetBackupRestoreBtnEnable(False)
        End If


        'Try
        '    Dim thr As New Thread(AddressOf LoadBackUpList)
        '    With thr
        '        .IsBackground = True
        '        .SetApartmentState(ApartmentState.STA) '线程中要弹窗体 所以需要设置改属性
        '        .Start()
        '    End With
        'Catch ex As Exception
        'End Try

        MainForm.SetButtonStatus(Me.btnImport)
        MainForm.SetButtonStatus(Me.btnDelete)
        MainForm.SetButtonStatus(Me.btnBackup)
        MainForm.SetButtonStatus(Me.btnRestore)
        MainForm.SetButtonStatus(Me.btnFoundFailure)
        MainForm.SetButtonStatus(Me.btnRefresh)

        Me.IniBackupListDeleteHelper()

    End Sub

    Private Sub SetBackupRestoreBtnEnable(ByVal blnEnable As Boolean)
        Me.btnBackup.Enabled = blnEnable
        Me.btnRestore.Enabled = blnEnable
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("Backup.Text.Title")                            '"备份还原"
        Me.lblTitle.Text = Me.Language.GetString("Backup.Text.Title")                   '"备份还原"
        Me.lblDescription.Text = Me.Language.GetString("Backup.Label.Description")      '"将设备的重要数据进行备份，以免丢失，或从以往的备份中恢复数据。"
        Me.lblSMS.Text = Me.Language.GetString("Backup.Label.SMS")                      '"短信"
        Me.lblPhoto.Text = Me.Language.GetString("Backup.Label.Photo")                  '"照片（相机胶卷）"
        Me.lblContact.Text = Me.Language.GetString("Main.Button.Contact")               '"联系人"
        Me.lblNote.Text = Me.Language.GetString("Main.Button.Note")                     '"备忘录"
        Me.lblAppData.Text = Me.Language.GetString("Backup.Label.AppData")              '"应用程序数据"
        Me.lblSetting.Text = Me.Language.GetString("Backup.Label.Setting")              '"配制信息"
        Me.btnNext.Text = Me.Language.GetString("Tools.Carrier.Button.Next")            '"下一步"

        Me.btnImport.Text = Me.Language.GetString("Media.Button.Import")                '"导入"
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")                      '"删除"
        Me.btnBackup.Text = Me.Language.GetString("Backup.Button.CreateBackup")   '"创建备份"
        Me.btnRestore.Text = Me.Language.GetString("Backup.Button.Restore")             '"从备份恢复"
        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh")        '"刷新"
        Me.tsmiDelete.Text = Me.Language.GetString("Common.Delete")
        Me.tsmiOpen.Text = Me.Language.GetString("Photo.Menu.View")                     '"查看"
        Me.tsmiOpenFolder.Text = Me.Language.GetString("Common.Button.OpenLocalFolder")
        Me.btnFoundFailure.Text = Me.Language.GetString("Backup.Button.FoundFailureData")   '"查看备份失败记录"
        'Me.btnOpen.Location = New Point(Me.btnImport.Right + 10, Me.btnOpen.Top)

        If Folder.LangType = LanguageType.th_TH Then
            Me.btnBackup.tbAutoSizeEx = False
            Me.btnBackup.Width += 25
        End If

        Me.btnDelete.Left = Me.btnImport.Right + 10
        Me.btnBackup.Left = Me.btnDelete.Right + 10
        Me.btnFoundFailure.Left = Me.btnBackup.Right + 10
        Me.btnCopyBackFiles.Left = Me.btnFoundFailure.Right + 10
        Me.btnRestoreSMS.Left = Me.btnFoundFailure.Right + 10

        Me.btnRefresh.Left = Me.pnlControl.Width - Me.btnRefresh.Width - 10
        Me.btnRestoreSMS.Text = Me.Language.GetString("Backup.Button.RestoreSMSFromBackup")   '从备份恢复短信

    End Sub


    Private Sub InitGridView()
        Me.mDgvBackupList = New tbDataGridViewEx
        With Me.mDgvBackupList
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 38
            .AllowDrop = True
            .MultiSelect = True
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .ShowCellToolTips = True
            .Columns.Clear()

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", _
                                                                   26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Common.Label.Name"), _
                                                                   IIf(Me.Parent Is Nothing, 180, 220), True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewMediaColumn), "colStatus", Me.Language.GetString("Backup.Label.Encypt"), _
                                                                   60, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colDateTime", Me.Language.GetString("App.Button.TimeSort"), _
                                                                   115, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"时间"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colSize", Me.Language.GetString("Common.Columns.Size"), _
                                                                   80, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"Size"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colVersion", Me.Language.GetString("App.Column.Version"), _
                                                                   65, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"当前版本"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colDeviceType", Me.Language.GetString("App.Button.DeviceType"), _
                                                                   IIf(Me.Parent Is Nothing, 130, 190), True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)) '"设备类型"
            '.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colSeriNumber", Me.Language.GetString("Welcome.Label.SerialNumber"), _
            '                                                       105, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)) '"序列号"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", 210, True, False, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
            '.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgressEx", "", 160, True, False, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))
            .ContextMenuStrip = Me.munOperator
            .GroupRowTemplateArray = Me.GetGroupTemplate()
            .ResumeLayout()
        End With

        RemoveHandler mDgvBackupList.CellDoubleClick, AddressOf mDgvBackupList_CellDoubleClick
        AddHandler mDgvBackupList.CellDoubleClick, AddressOf mDgvBackupList_CellDoubleClick

        RemoveHandler mDgvBackupList.CellButtonClick, AddressOf mDgvBackupList_CellButtonClick
        AddHandler mDgvBackupList.CellButtonClick, AddressOf mDgvBackupList_CellButtonClick

        RemoveHandler mDgvBackupList.SelectionChanged, AddressOf mDgvBackupList_SelectionChanged
        AddHandler mDgvBackupList.SelectionChanged, AddressOf mDgvBackupList_SelectionChanged

        RemoveHandler mDgvBackupList.ShowToolTip, AddressOf mDgvBackupList_ShowToolTip
        AddHandler mDgvBackupList.ShowToolTip, AddressOf mDgvBackupList_ShowToolTip

        RemoveHandler mDgvBackupList.MouseDown, AddressOf mDgvBackupList_MouseDown
        AddHandler mDgvBackupList.MouseDown, AddressOf mDgvBackupList_MouseDown

        Me.pnlContainer.Controls.Add(Me.mDgvBackupList)

        Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FailureBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"损坏备份"
        Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FullBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"整机备份"

    End Sub

    Private Function GetGroupTemplate() As Object()
        Return New Object() {True, _
                            "", _
                            True, _
                            "", _
                            "", _
                            "", _
                            "", _
                            "", _
                            ""}
    End Function

    Private Delegate Sub DgvClearHandler()
    Private Sub DgvClear()
        If Me.InvokeRequired Then
            Me.Invoke(New DgvClearHandler(AddressOf DgvClear))
        Else
            Try
                Me.mDgvBackupList.DictGroupKeys.Clear()
                Me.mDgvBackupList.Rows.Clear()
                Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FailureBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"损坏备份"
                Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FullBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"整机备份"
            Catch ex As Exception
                Common.LogException(ex.ToString(), "MainForm_SetBuyButtonStatus")
            End Try
        End If
    End Sub

    Private Sub LoadBackUpList()
        DgvClear()
        Try
            Me.SetContralEnable(False)

            If Not BackupHelper.IsLoadFullBackupList Then
                'Dim frm As frmWait = New frmWait(Me.mApplication, Me.Language.GetString("WeiBo.Message.Loading"), WaitState.FullBackupList) '正在获取信息...
                'frm.ShowDialog(Me)
                Me.SetLabelMsg(Me.Language.GetString("WeiBo.Message.Loading"))
            End If

            Dim intIndex As Integer = 0
            For Each bInfo As BackupInfo In BackupHelper.LstFullBackupList
                intIndex += 1
                Me.AddRow(intIndex, bInfo, False)
            Next

            If BackupHelper.LstFailureBackupList.Count > 0 Then
                intIndex += 1
            End If
            For Each bInfo As BackupInfo In BackupHelper.LstFailureBackupList
                intIndex += 1
                Me.AddRow(intIndex, bInfo, True)
            Next

            Me.SetLabelMsg("")
            '如果统计大小的时候出错再进行统计一次
            If Not Me.LoadSize() Then
                Me.LoadSize()
            End If

            Me.SetContralEnable(True)
            Me.CountTotalInfo()
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Function LoadSize() As Boolean
        Dim blnRetuern As Boolean = True
        Try
            For Each Item As BackupInfo In BackupHelper.LstFullBackupList
                Dim longSize As ULong = BackupHelper.GetFileSize(Item.FolderPath, False)
                For Each row As tbDataGridViewRow In Me.mDgvBackupList.Rows
                    If row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is BackupInfo AndAlso CType(row.Tag, BackupInfo).FolderPath = Item.FolderPath Then
                        row.Cells("colSize").Value = Utility.FormatFileSize(longSize)
                        Item.Size = longSize
                    End If
                Next
            Next
        Catch ex As Exception
            Common.Log("Load Size Error:" & ex.ToString())
            blnRetuern = False
        End Try
        Return blnRetuern
    End Function

    Private Delegate Sub AddRowHandler(ByVal intIndex As Integer, ByVal bakObject As BackupInfo, ByVal isFailure As Boolean)
    Public Sub AddRow(ByVal intIndex As Integer, ByVal bakObject As BackupInfo, ByVal isFailure As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New AddRowHandler(AddressOf AddRow), intIndex, bakObject, isFailure)
        Else
            Try
                Dim strSize As String = Me.Language.GetString("App.Cell.Loading")           '"正在加载..." 'Utility.FormatFileSize(bakObject.Size)
                Dim strDeviceName As String = bakObject.DeviceName
                If isFailure Then
                    strSize = Me.Language.GetString("Common.Label.Unknow")
                    strDeviceName = Path.GetFileName(bakObject.FolderPath)
                End If
                'Dim strVersion As String = String.Format("{0}({1})", bakObject.ProductVersion, bakObject.BuildVersion)

                Dim aryParameters As Object() = New Object() {False, strDeviceName, _
                                                                  True, _
                                                                  bakObject.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute)), _
                                                                  strSize, _
                                                                  bakObject.ProductVersion, _
                                                                  bakObject.ProductType, _
                                                                  "", "", ""}

                Dim row As tbDataGridViewRow = Me.SetDataGridViewGroup(isFailure, aryParameters, intIndex)

                'If Me.mDgvBackupList.Rows.Count = 0 Then
                '    Me.mDgvBackupList.Rows.Add(False, bakObject.DeviceName, True, bakObject.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute)), strSize, bakObject.ProductVersion, bakObject.ProductType, "", "", "")
                'Else
                '    If intIndex > Me.mDgvBackupList.Rows.Count Then
                '        intIndex = Me.mDgvBackupList.Rows.Count
                '    End If
                '    Me.mDgvBackupList.Rows.Insert(intIndex, False, bakObject.DeviceName, True, bakObject.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute)), strSize, bakObject.ProductVersion, bakObject.ProductType, "", "", "")
                'End If

                'Dim row As tbDataGridViewRow = Me.mDgvBackupList.Rows(intIndex)

                row.Tag = bakObject
                Dim cellProgressEx As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                cellProgressEx.tbProgressStyle = ProgressStyle.Button
                'cellProgressEx.tbText = Me.Language.GetString("Photo.Menu.View") '"查看"

                cellProgressEx.tbShowButtonFirst = True
                cellProgressEx.tbTextBackup = Me.Language.GetString("Photo.Menu.View")              '"查看"
                cellProgressEx.tbShowButtonSecond = True
                cellProgressEx.tbShowButtonThird = False
                cellProgressEx.tbTextUnInstall = Me.Language.GetString("Common.Button.Restore")     '"还原"

                If Not frmBackupCreate.CheckCanRestore(Me.mDevice, row.Tag) Then
                    cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                Else
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall
                End If

                If isFailure Then
                    cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                End If

                'Dim celStatus As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")
                Dim celStatus As tbDataGridViewMediaCell = row.Cells("colStatus")

                If mbdb.CheckIsEncrypted(bakObject.FolderPath) Then
                    'celStatus.Value = Me.Language.GetString("Backup.Label.Encypted")                '"已加密"
                    celStatus.tbCellStatus = CellButtonStyle.BackupLock
                    celStatus.ToolTipText = Me.Language.GetString("Backup.Label.EncryptionBackup")          '"加密备份"

                Else
                    'celStatus.Value = Me.Language.GetString("Backup.Label.UnEncypt")                '"未加密"
                    celStatus.tbCellStatus = CellButtonStyle.BackupUnlock

                End If

                'Me.mDgvBackupList.Refresh()
            Catch ex As Exception
                Common.LogException("Add List Error" & ex.ToString)
            End Try
        End If
    End Sub

    Private Function SetDataGridViewGroup(ByVal isFailure As Boolean, ByVal aryParameters As Object(), ByVal intIndex As Integer) As tbDataGridViewRow
        Dim row As New tbDataGridViewRow
        Try
            Dim strGroupText As String = ""
            If isFailure Then
                strGroupText = Me.Language.GetString("Weixin.Button.FailureBackup") '"损坏备份"
            Else
                strGroupText = Me.Language.GetString("Weixin.Button.FullBackup") '"整机备份"
            End If
            row = Me.mDgvBackupList.AddGroupItem(strGroupText, aryParameters, False, "", intIndex)
            row.Group.ShowIconMore = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetDataGridViewGroup")
        End Try
        Return row
    End Function

    Private Sub InitDescription()
        If Not IniSetting.GetRestoreDescription Then
            Return
        End If
        Me.lblTitle.Font = Common.CreateFont("微软雅黑", 25.0!, FontStyle.Bold)

        Me.picDevice.Image = My.Resources.device_default
        If Me.mDevice IsNot Nothing Then
            Dim strFilePath As String = SummaryInfo.GetIconPathByDevice(Me.mDevice.ProductType, Me.mDevice.DeviceColor, Me.mDevice.DeviceColorBg)

            If File.Exists(strFilePath) Then
                Me.picDevice.Image = Common.ImageFromFile(strFilePath)
            End If
        End If

        Me.pnlDescription.Size = New Size(Me.pnlControl.Width, Me.pnlContainer.Bottom - Me.pnlControl.Top)
        Me.pnlDescription.Location = New Point(Me.pnlControl.Left, Me.pnlControl.Top)
        Me.pnlDescription.Visible = True
        Me.pnlDescription.BringToFront()
    End Sub

    Protected Overrides Sub OnFormClosing(ByVal e As System.Windows.Forms.FormClosingEventArgs)
        MyBase.OnFormClosing(e)
        Try
            If Me.mTDImportBackup IsNot Nothing AndAlso Me.mTDImportBackup.ThreadState <> ThreadState.Stopped Then
                Me.mTDImportBackup.Abort()
            End If
        Catch ex As Exception

        End Try
        If Not e.Cancel Then
            Dim thd As New Threading.Thread(AddressOf DeleteDecrypt)
            thd.IsBackground = True
            thd.Start()
            Application.DoEvents()
        End If
    End Sub

    Private Sub DeleteDecrypt()
        Try
            For Each info As BackupInfo In BackupHelper.LstFullBackupList
                iPhoneDevice.mb2_Delete_Decrypted(info.FolderPath)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteDecrypt")
        End Try
    End Sub

    Private Sub InitBackupHelper(Optional ByVal isGetFull As Boolean = True)
        Try
            Me.mBackupHelper = BackupHelper.Instance(Me.mDevice)

            AddHandler Me.mBackupHelper.BackupProgressChanged, AddressOf Me.OnBackupProgressChanged
            AddHandler Me.mBackupHelper.DecryptInputPwd, AddressOf Me.OnDecryptInputPwd
            AddHandler Me.mBackupHelper.LoadBackupList, AddressOf OnLoadBackupList

            If isGetFull Then
                Me.mBackupHelper.GetFullBackupList(False)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitBackupHelper")
        End Try
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            If Me.mBackupHelper IsNot Nothing Then
                RemoveHandler Me.mBackupHelper.BackupProgressChanged, AddressOf Me.OnBackupProgressChanged
                RemoveHandler Me.mBackupHelper.DecryptInputPwd, AddressOf Me.OnDecryptInputPwd
                RemoveHandler Me.mBackupHelper.LoadBackupList, AddressOf OnLoadBackupList

                Me.mBackupHelper.Cancel()
            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub IniBackupListDeleteHelper()
        Try
            Me.mBackupListDeleteHelper = BackupListDeleteHelper.GetInstance()
            RemoveHandler Me.mBackupListDeleteHelper.BackupListDeleteEventHandler, AddressOf BackupListDeleteHelper_BackupListDeleteEventHandler
            AddHandler Me.mBackupListDeleteHelper.BackupListDeleteEventHandler, AddressOf BackupListDeleteHelper_BackupListDeleteEventHandler
        Catch ex As Exception
            Common.LogException(ex.ToString(), "IniBackupListDeleteHelper")
        End Try
    End Sub

#End Region

#Region "--- 连接事件 ---"

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
        Me.SetBackupRestoreBtnEnable(True)
        Me.SetRowCanRestore()
        Me.InitBackupHelper(False)
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.SetBackupRestoreBtnEnable(False)
        Me.SetRowCanRestore()
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Public Shared Sub ShowDetail(ByVal frm As Form, ByVal app As IApplication, ByVal backupObj As BackupInfo, lang As LanguageInterface, deviceForDecrypt As iPhoneDevice)
        Dim isNeedDecrypt As Boolean = False
        Dim strPwdForDecrypt As String = String.Empty

        If backupObj.Identifier.Length = 0 Then
            Common.OpenExplorer(backupObj.FolderPath)
            Return
        End If

        '1、判断是否需要解密
        If Not mbdb.CheckIsEncrypted(backupObj.FolderPath) Then
            GoTo DO_EXIT
        End If

        '4、是否已成功解密
        If iPhoneDevice.mb2_Check_Decrypted(backupObj.FolderPath) Then
            GoTo DO_EXIT
        End If

        '"您曾经在iTunes中备份加密，请输入设置的备份密码。"
DO_CONTINUE:
        If tbInputBox.Show(frm, lang.GetString("Backup.Message.TypeInBackupPwd"), _
                                    lang.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, _
                                    MessageBoxDefaultButton.Button1, strPwdForDecrypt, True, My.Resources.backup_password) = Windows.Forms.DialogResult.OK Then ', lang.GetString("Backup.Message.iTunesTutorial"), "http://news.tongbu.com/92502.html""iTunes备份密码相关教程"
            '密码不能为空
            If String.IsNullOrEmpty(strPwdForDecrypt) Then
                GoTo DO_CONTINUE
            End If

            isNeedDecrypt = True
        Else
            Return
        End If

DO_EXIT:
        Dim frmBP As New frmBackupPreview(app, backupObj, isNeedDecrypt, strPwdForDecrypt, deviceForDecrypt)
        If frm IsNot Nothing Then
            frmBP.Show(frm)
        Else
            frmBP.Show()
        End If
        'frm.ShowDialog(Me)
    End Sub

    Private Sub btnImport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.Click
        If Me.mTDImportBackup IsNot Nothing AndAlso Me.mTDImportBackup.ThreadState <> ThreadState.Stopped Then
            Return
        End If
        Dim folderDialog As New FolderBrowserDialog()
        Dim folderPath As String = ""
        If folderDialog.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
            folderPath = folderDialog.SelectedPath
        End If
        Me.mTDImportBackup = New Thread(AddressOf DoImportBackup)
        Me.mTDImportBackup.IsBackground = True
        Me.mTDImportBackup.Start(folderPath)
    End Sub

    Private Sub DoImportBackup(ByVal obj As Object)
        Try
            Dim lstPath As New List(Of String)
            If BackupHelper.CheckPathIsBackupPath(obj.ToString()) Then
                Me.LoadBackInfo(obj.ToString())
                lstPath.Add(obj.ToString())
            Else
                '如果当前目录下面有备份的目录也导入进来

                For Each folder As String In Directory.GetDirectories(obj.ToString())
                    If BackupHelper.CheckPathIsBackupPath(folder) Then
                        Me.LoadBackInfo(folder)
                        lstPath.Add(folder)
                    End If
                    Application.DoEvents()
                Next
            End If

            If lstPath.Count <= 0 Then
                '"您导入的文件夹不可用。可以尝试在""查找备份失败记录""中查看或联系客服QQ:{0}"
                Dim strMsg As String = String.Format(Me.Language.GetString("Backup.Message.LoadFolderError"), Utility.TongbuContact)
                tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            End If
            '用户添加的目录记录到xml文件里面。
            XmlHelper.InsertXMForBackupPath(lstPath)

            Try
                Me.LoadSize()
            Catch ex As Exception

            End Try
            Me.CountTotalInfo()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoImportBackup")
        End Try
    End Sub

    Private Sub btnOpen_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiOpen.Click
        If Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing Then
            ShowDetail(Me, Me.mApplication, Me.mDgvBackupList.SelectedRows(0).Tag, Me.Language, Me.mDevice)
        End If
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click, tsmiDelete.Click
        If Me.mDgvBackupList.Rows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows.Count > 0 Then
            '"您确定删除所选的备份项目以及备份数据？"
            If tbMessageBox.Show(Me, Me.Language.GetString("Backup.Message.DeleteBackupFiles"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                Dim strPath As String = ""
                Dim obj As BackupInfo = Nothing
                Dim blnSucc As Boolean = True
                Me.mblnDeleting = True
                Try
                    Dim lstRows As New List(Of tbDataGridViewRow)
                    For Each Item As tbDataGridViewRow In Me.mDgvBackupList.SelectedRows
                        lstRows.Add(Item)
                    Next
                    If lstRows.Count > 0 Then
                        Me.mBackupListDeleteHelper.DeleteByRows(lstRows)
                        Me.SetDeleteEnable(False)
                    End If
                Catch ex As Exception
                End Try
            End If
        End If
    End Sub

    Private Sub btnBackup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBackup.Click
        If Me.mDevice Is Nothing Then
            Return
        End If

        Me.SetBackupRestoreBtnEnable(False)
        Me.OpenBackupRestoreForm(BackupRestore.Backup, Nothing)
        Me.SetBackupRestoreBtnEnable(True)
    End Sub

    Private Sub btnRestore_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRestore.Click
        If Me.mDevice Is Nothing Then
            Return
        End If

        Me.SetBackupRestoreBtnEnable(False)
        Me.OpenBackupRestoreForm(BackupRestore.Restore, Nothing)
        Me.SetBackupRestoreBtnEnable(True)
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        Me.mBackupHelper.GetFullBackupList(True)
    End Sub


    Private Sub tsmiOpenFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiOpenFolder.Click
        If Me.mDgvBackupList.Rows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing Then
            Dim path As String = CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo).FolderPath & ""
            If Directory.Exists(path) Then
                Try
                    Common.OpenExplorer(path)
                Catch ex As Exception
                End Try
            End If
        End If
    End Sub

    Public Sub mDgvBackupList_CellDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs)
        '双击查看还原列不显示详细信息
        If e.RowIndex < 0 OrElse e.ColumnIndex >= 7 Then
            Return
        End If

        If Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing Then
            ShowDetail(Me, Me.mApplication, Me.mDgvBackupList.SelectedRows(0).Tag, Me.Language, Me.mDevice)
        End If
    End Sub

    Private Sub mDgvBackupList_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Dim row As DataGridViewRow = Me.mDgvBackupList.Rows(e.Cell.RowIndex)
        If e.CellButton = CellButtonStyle.Backup AndAlso row.Tag IsNot Nothing Then
            ShowDetail(Me, Me.mApplication, row.Tag, Me.Language, Me.mDevice)

        ElseIf e.CellButton = CellButtonStyle.Uninstall AndAlso row.Tag IsNot Nothing Then

            Dim info As BackupInfo = row.Tag
            Me.mCurrentRestoreRow = row
            If info IsNot Nothing Then
Do_RetryCheckiCloud:
                'icloud 提示
                Try
                    Dim frmCC As frmCheckiCloud = Nothing
                    Dim objValue As Object = Me.mDevice.GetDeviceValue("com.apple.fmip", "IsAssociated")
                    Dim blnValue As Boolean = CType(objValue, Boolean)
                    If blnValue Then
                        frmCC = New frmCheckiCloud(iCloudType.FindMyIphoneCloseBackup)
                    End If

                    If frmCC IsNot Nothing Then
                        If frmCC.ShowDialog <> Windows.Forms.DialogResult.OK Then
                            Return
                        End If
                        GoTo Do_RetryCheckiCloud
                    End If
                Catch
                End Try

                Dim strMessage As String = String.Format(Me.Language.GetString("Backup.Message.BeforeRestoreWaning"), Me.mDevice.OriginalDeviceName)
                '"确认将所选备份文件恢复到 {0}？" & vbCrLf & _
                '"该操作会抹掉当前设备的照片和系统配制等数据，请谨慎操作！" & vbCrLf & _
                '"恢复过程中请不要断开连接，否则会造成数据丢失或设备故障。" & vbCrLf & _
                '"恢复完成后，设备将会自动重启。"
                If tbMessageBox.Show(Me, strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo) = Windows.Forms.DialogResult.No Then
                    Return
                End If

                Me.SetOtherRowCanRestore(info)
                Dim cellProgressEx As tbDataGridViewProgressCellEx = Me.mCurrentRestoreRow.Cells("colProgress")
                cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                cellProgressEx.tbText = ""
                cellProgressEx.tbShowCancelButton = False
                cellProgressEx.tbProgressValue = 0
                Me.SetBackupRestoreBtnEnable(False)

                Me.mCurrentRestoreRow = Nothing
                Me.lblMessage.Text = Me.Language.GetString("Backup.Message.RestoreMessage")
                Me.mBackupHelper.StartRestore(info.FolderPath)
            End If
        ElseIf e.CellButton = CellButtonStyle.Cancel Then
            Me.mBackupHelper.CancelRestore()

        End If

    End Sub

    Public Sub mDgvBackupList_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If Me.mblnDeleting OrElse Me.mDgvBackupList.Rows.Count <= 0 OrElse Me.mDgvBackupList.SelectedRows.Count = 0 Then
            Me.SetDeleteEnable(False)
        Else
            Me.SetDeleteEnable(True)
        End If
    End Sub

    Private Function GetCanRestoreInfo() As BackupInfo
        Dim info As BackupInfo = Nothing
        Try
            '如果创建备份还原窗体打开了就不用再打开了。
            For Each row As tbDataGridViewRow In Me.mDgvBackupList.SelectedRows
                If frmBackupCreate.CheckCanRestore(Me.mDevice, row.Tag) Then
                    info = row.Tag
                    Exit For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmBackupList_GetCanRestoreInfo")
        End Try
        Return info
    End Function

    Private intRowIndex As Integer = -1
    Private Sub mDgvBackupList_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Dim row As DataGridViewRow = Me.mDgvBackupList.Rows(e.Cell.RowIndex)
        Dim pos As Point = Windows.Forms.Cursor.Position
        Dim secPos As Point = Me.PointToClient(pos)
        Dim newPos As New Point(secPos.X + 10, secPos.Y + 10)
        Dim cellProgressEx As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        Try
            Select Case e.CellButton
                Case CellButtonStyle.Uninstall
                    Dim info As BackupInfo = CType(row.Tag, BackupInfo)

                    If cellProgressEx.tbCellButtonEnable = CellButtonStyle.CrakedInfo Then
                        Return
                    End If
                    If Me.mDevice IsNot Nothing Then
                        Me.tipDgvCell.Show(String.Format(Me.Language.GetString("BackupList.Column.HintVersion"), info.ProductVersion, Me.mDevice.ProductVersion), Me, newPos) '备份固件版本：{0} 无法还原到您设备固件版本：{1}
                    Else
                        Me.tipDgvCell.Show(Me.Language.GetString("Welcome.Label.AccessDevice"), Me, newPos) '"请连接设备"
                    End If

                Case CellButtonStyle.None
                    Me.tipDgvCell.Hide(Me)

            End Select
        Catch ex As Exception
        End Try

        If TypeOf e.Cell Is tbDataGridViewProgressCellEx AndAlso e.CellButton = CellButtonStyle.OneButton Then

        ElseIf TypeOf e.Cell Is tbDataGridViewTextBoxCellEx Then
            Try
                If e.Cell.RowIndex <> intRowIndex AndAlso Me.munOperator.Visible = False AndAlso row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is BackupInfo Then
                    intRowIndex = e.Cell.RowIndex
                    Dim bakObject As BackupInfo = row.Tag
                    Dim strTip As New StringBuilder

                    strTip.AppendLine(Me.Language.GetString("Common.Label.Name") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.DisplayName)        '"名称："
                    strTip.AppendLine(Me.Language.GetString("App.Button.TimeSort") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute)))   '"时间："
                    strTip.AppendLine(Me.Language.GetString("Common.Columns.Size") & Me.Language.GetString("Common.Symbol.Colon") & " " & Utility.FormatFileSize(bakObject.Size)) '"大小："
                    strTip.AppendLine(Me.Language.GetString("App.Column.Version") & Me.Language.GetString("Common.Symbol.Colon") & " " & String.Format("{0}({1})", bakObject.ProductVersion, bakObject.BuildVersion)) '"版本："
                    strTip.AppendLine(Me.Language.GetString("Backup.Label.Key") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.Identifier)      '"标识符："
                    strTip.AppendLine(Me.Language.GetString("Welcome.Label.SerialNumber") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.SerialNumber)      '"序列号："
                    Dim strFolderPath As String = bakObject.FolderPath
                    If strFolderPath.Length > 60 Then
                        Dim intIndex As Integer = strFolderPath.Substring(0, 60).LastIndexOf("\")
                        strFolderPath = strFolderPath.Substring(0, intIndex) & vbCrLf & strFolderPath.Substring(intIndex, strFolderPath.Length - intIndex)
                    End If
                    strTip.AppendLine(Me.Language.GetString("Components.ImageView.Label.Location") & Me.Language.GetString("Common.Symbol.Colon") & " " & strFolderPath)   '"路径："

                    Me.tipDgvCell.Show(strTip.ToString(), Me, newPos)
                Else
                    Me.tipDgvCell.Hide(Me)
                End If
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub btnNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNext.Click
        Me.pnlDescription.SendToBack()
        Me.pnlDescription.Visible = False
        IniSetting.SetRestoreDescription(False)
    End Sub

    Public Sub mDgvBackupList_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs)
        Me.tipDgvCell.Hide(Me)
        Me.intRowIndex = -1
    End Sub

    Public Sub frmItem_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs)
        Try
            Me.SetBackupRestoreBtnEnable(True)
            Me.mCreateBackupShow = False
        Catch ex As Exception
        End Try
    End Sub

    Private Sub frmBackupList_MouseLeave(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.MouseLeave
        Me.tipDgvCell.Hide(Me)
        Me.intRowIndex = -1
    End Sub

    Private Sub btnFoundFailure_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFoundFailure.Click
        Dim dlg As New FolderBrowserDialog()
        If Directory.Exists(BackupHelper.BackupFolderEx) Then
            dlg.SelectedPath = BackupHelper.BackupFolderEx
        End If

        If dlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Dim strPath As String = dlg.SelectedPath
            If Directory.Exists(Path.Combine(strPath, "Snapshot")) Then
                strPath = Path.Combine(strPath, "Snapshot")
            End If
            Dim bakObject As New BackupInfo(strPath)
            bakObject = New BackupInfo(strPath)
            bakObject.Identifier = Path.GetFileName(strPath)
            bakObject.DeviceName = bakObject.Identifier
            Me.LoadBackUpInfo(dlg.SelectedPath, bakObject)
            Dim frm As New frmBackupPreview(Me.mApplication, bakObject, False, "", Nothing, strPath)
            frm.Show(Me)
        End If

    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Delegate Sub SetContralEnableHandelr(ByVal blnEnable As Boolean)
    Private Sub SetContralEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetContralEnableHandelr(AddressOf SetContralEnable), blnEnable)
        Else
            Me.btnRefresh.Enabled = blnEnable
        End If
    End Sub

    Private Delegate Sub CountTotalInfoHandler()
    Private Sub CountTotalInfo()
        If Me.InvokeRequired Then
            Me.Invoke(New CountTotalInfoHandler(AddressOf CountTotalInfo))
        Else
            Dim longSize As Long = 0
            Dim intCount As Integer = 0
            For Each row As tbDataGridViewRow In Me.mDgvBackupList.Rows
                If row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is BackupInfo Then
                    longSize += CType(row.Tag, BackupInfo).Size
                    intCount += 1
                End If
            Next
            '"共{0}个备份 {1}"          
            Me.SetLabelMsg(String.Format(Me.Language.GetString("Backup.Label.BackUpFileCount"), intCount, Utility.FormatFileSize(longSize)))
        End If

    End Sub

    Private Function GetXMRoot(ByVal doc As System.Xml.XmlDocument) As System.Xml.XmlNode
        If doc.ChildNodes.Count = 0 OrElse doc.ChildNodes(0).Name <> "Backup" Then
            If doc.ChildNodes.Count > 0 Then
                doc.RemoveAll()
            End If
            doc.AppendChild(doc.CreateElement("Backup"))
        End If
        Return doc.ChildNodes(0)
    End Function

    Public Sub OpenBackupRestoreForm(ByVal type As BackupRestore, ByVal info As BackupInfo)
        'Dim strSelectedSN As String = ""
        'If Me.mDgvBackupList.SelectedRows.Count > 0 Then
        '    strSelectedSN = Me.mDgvBackupList.SelectedRows(0).Cells("colSeriNumber").Value
        'End If
        Dim strName As String = "frmBacbup"
        If type = BackupRestore.Restore Then
            strName = "frmRestore"
        End If

        Dim frmItem As frmBackupCreate = Nothing
        For Each ctl As Form In Application.OpenForms
            If TypeOf ctl Is Form AndAlso ctl.GetType().FullName = GetType(frmBackupCreate).ToString Then
                If iPhone IsNot Nothing And TypeOf ctl Is frmDeviceBase AndAlso (Not TypeOf ctl Is frmAndroidBase) AndAlso CType(ctl, frmDeviceBase).iPhone IsNot iPhone Then
                    Continue For
                End If
                frmItem = ctl
                Exit For
            End If
        Next

        If frmItem Is Nothing Then
            frmItem = New frmBackupCreate(Me.mApplication, Me.mDevice, type, Me)
            frmItem.Name = strName
            frmItem.ShowDialog(Me)
            RemoveHandler frmItem.FormClosed, AddressOf frmItem_FormClosed
            AddHandler frmItem.FormClosed, AddressOf frmItem_FormClosed
            If Not String.IsNullOrEmpty(frmItem.BackupPath) Then
                Me.mBackupHelper.GetFullBackupList(True)
            End If
        Else
            frmItem.Activate()
            RemoveHandler frmItem.FormClosed, AddressOf frmItem_FormClosed
            AddHandler frmItem.FormClosed, AddressOf frmItem_FormClosed
        End If
        frmItem.mAutoClose = False
        Me.mCreateBackupShow = True
        If info IsNot Nothing AndAlso type = BackupRestore.Restore Then
            CType(frmItem, frmBackupCreate).StartRestore(info.FolderPath)
        End If
    End Sub

    Private Sub SetDeleteEnable(ByVal blnEnable As Boolean)
        If blnEnable AndAlso Me.mDgvBackupList.Rows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows.Count > 0 Then
            Me.btnDelete.Enabled = True
            Me.tsmiDelete.Enabled = True
            'Me.btnWhatsApp.Enabled = True
        Else
            Me.btnDelete.Enabled = False
            Me.tsmiDelete.Enabled = False
            'Me.btnWhatsApp.Enabled = False
        End If
    End Sub

    Private Sub SetLabelMsg(ByVal strText As String)
        If Me.Parent IsNot Nothing Then
            Me.mApplication.SetLabelText(FunctionKey.Backup, strText)
        Else
            Me.lblMessage.Text = strText
        End If
    End Sub

    Private Sub LoadBackUpInfo(ByVal strPath As String, ByVal bakObject As BackupInfo)
        Try
            Dim strPlistPath As String = Path.Combine(strPath, "Info.plist")
            If Not File.Exists(strPlistPath) Then
                strPlistPath = Path.Combine(Path.GetDirectoryName(strPath), "Info.plist")
                If Not File.Exists(strPlistPath) Then
                    Return
                End If
            End If
            Dim dic As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strPlistPath)
            If dic IsNot Nothing AndAlso dic.Count > 0 Then

                If dic.ContainsKey("Device Name") Then
                    bakObject.DeviceName = dic("Device Name")
                End If

                If dic.ContainsKey("Display Name") Then
                    bakObject.DisplayName = dic("Display Name")
                End If

                'dateTime
                If dic.ContainsKey("Last Backup Date") Then
                    bakObject.LastBackupDate = dic("Last Backup Date")
                End If

                'Size
                bakObject.Size = 0  'Me.GetFileSize(strBackupFolder)

                'Version
                If dic.ContainsKey("Product Version") Then
                    bakObject.ProductVersion = dic("Product Version")
                End If

                If dic.ContainsKey("Build Version") Then
                    bakObject.BuildVersion &= dic("Build Version")
                End If

                '设备类型
                If dic.ContainsKey("Product Type") Then
                    bakObject.ProductType = SummaryInfo.FormatProduct(dic("Product Type"))
                End If

                '序列号

                If dic.ContainsKey("Serial Number") Then
                    bakObject.SerialNumber = dic("Serial Number")
                End If

                'Identifier
                If dic.ContainsKey("Target Identifier") Then
                    bakObject.Identifier = dic("Target Identifier")
                    If bakObject.Identifier.Length > 40 Then
                        bakObject.Identifier = bakObject.Identifier.Substring(0, 40)
                    End If
                End If

                Dim info As New FileInfo(strPlistPath)

                bakObject.BackupDate = info.LastWriteTime

                Dim diffDays As Integer = bakObject.BackupDate.Date.Subtract(bakObject.LastBackupDate.Date).TotalDays
                If Math.Abs(diffDays) > 1 Then
                    bakObject.BackupDate = bakObject.LastBackupDate
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub SetRowCanRestoreHandler()
    Private Sub SetRowCanRestore()
        If Me.InvokeRequired Then
            Me.Invoke(New SetRowCanRestoreHandler(AddressOf SetRowCanRestore))
        Else
            For Each Item As tbDataGridViewRow In Me.mDgvBackupList.Rows
                Dim cellProgressEx As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                If Not frmBackupCreate.CheckCanRestore(Me.mDevice, Item.Tag) Then
                    cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                Else
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall
                End If
            Next
        End If
    End Sub

    Private Delegate Sub SetOtherRowCanRestoreHandler(ByVal bakObject As BackupInfo)
    Private Sub SetOtherRowCanRestore(ByVal bakObject As BackupInfo)
        If Me.InvokeRequired Then
            Me.Invoke(New SetRowCanRestoreHandler(AddressOf SetRowCanRestore), bakObject)
        Else
            For Each Item As tbDataGridViewRow In Me.mDgvBackupList.Rows
                Try
                    Dim bInfo As BackupInfo = CType(Item.Tag, BackupInfo)
                    If bInfo Is Nothing Then
                        Continue For
                    End If
                    If bInfo.FolderPath = bakObject.FolderPath AndAlso bInfo.DisplayName = bakObject.DisplayName Then
                        Continue For
                    End If

                    Dim cellProgressEx As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                    cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "SetOtherRowCanRestore")
                End Try
            Next
        End If
    End Sub

#End Region

#Region "--- 公共方法 ---"

    '加载备份信息，如果界面上不包含则添加到界面。
    Public Function LoadBackInfo(ByVal strPath As String) As BackupInfo
        Dim bakObject As BackupInfo = New BackupInfo(strPath)
        Try
            Dim intIndex As Integer = 0
            Dim isExist As Boolean = False
            bakObject = Me.mBackupHelper.AddLstFullBackupList(strPath, intIndex, isExist)
            If Not isExist Then
                Me.AddRow(intIndex, bakObject, False)
            End If

            Me.SetRowCanRestore()
        Catch ex As Exception
            Common.LogException(ex.ToString, "rmBackupList.LoadBackInfo")
        End Try
        Return bakObject
    End Function

    Private Function RemoveRow(ByVal strFilePath As String) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim row As tbDataGridViewRow = Nothing
            Dim obj As BackupInfo = Nothing
            For Each Item As tbDataGridViewRow In Me.mDgvBackupList.Rows
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is BackupInfo Then
                    obj = Item.Tag
                    If String.Compare(obj.FolderPath, strFilePath, True) = 0 Then
                        row = Item
                        Exit For
                    End If
                End If
            Next
            If row IsNot Nothing AndAlso obj IsNot Nothing Then
                'Me.mDgvBackupList.Rows.Remove(row)
                Me.mDgvBackupList.RemoveGroupItem(row)
                BackupHelper.LstFullBackupList.Remove(obj)
                isRelust = True
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "RemoveRow")
        End Try
        Return isRelust
    End Function

    '更新备份文件大小。
    Public Function UpdateRowFileSize(ByVal strFilePath As String) As Long
        Dim size As Long = 0
        If strFilePath.Length <= 0 Then
            Return size
        End If
        Try
            For Each Item As tbDataGridViewRow In Me.mDgvBackupList.Rows
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is BackupInfo Then
                    Dim obj As BackupInfo = Item.Tag
                    If String.Compare(obj.FolderPath, strFilePath, True) = 0 Then
                        size = BackupHelper.GetFileSize(strFilePath, True)
                        obj.Size = size

                        Item.Cells("colSize").Value = Utility.FormatFileSize(obj.Size)
                        Exit For
                    End If
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateRowFileSize")
        End Try

        Return size
    End Function

#End Region

#Region "--- 备份还原委托事件 ---"

    Public Sub OnBackupProgressChanged(ByVal sender As Object, ByVal args As BackupArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupArgs)(AddressOf OnBackupProgressChanged), sender, args)
        Else
            If Me.mCurrentRestoreRow Is Nothing Then
                For Each item As tbDataGridViewRow In mDgvBackupList.Rows
                    If item.Tag Is Nothing Then
                        Continue For
                    End If
                    Dim bakInfo As BackupInfo = item.Tag
                    If bakInfo.FolderPath = args.FolderPath Then
                        mCurrentRestoreRow = item
                        Exit For
                    End If
                Next
            End If

            If Me.mCurrentRestoreRow Is Nothing Then
                If args.Status = BackupStatus.Succeed AndAlso args.IsNewBackup Then
                    'Dim intIndex As Integer = 1
                    'Dim bakObject As BackupInfo = Me.mBackupHelper.AddLstFullBackupList(args.FolderPath, intIndex)
                    'Me.AddRow(intIndex, bakObject)
                    'If Not Me.LoadSize() Then
                    '    Me.LoadSize()
                    'End If
                    XmlHelper.InsertXMForBackupPath(args.FolderPath)
                    Me.btnRefresh_Click(Nothing, Nothing)
                    Me.SetBackupRestoreBtnEnable(True)
                End If
                Return
            End If

            Dim cellProgressEx As tbDataGridViewProgressCellEx = Me.mCurrentRestoreRow.Cells("colProgress")
            Dim celStatus As tbDataGridViewMediaCell = Me.mCurrentRestoreRow.Cells("colStatus")
            If args.Status = BackupStatus.BackupOrRestore Then
                cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                cellProgressEx.tbText = ""
                cellProgressEx.tbShowCancelButton = True
                If args.Progress = 100 Then
                    cellProgressEx.tbProgressValue = 99
                Else
                    cellProgressEx.tbProgressValue = args.Progress
                End If
                Me.SetBackupRestoreBtnEnable(False)
                Me.lblMessage.Text = IIf(args.BackupType = BackupRestore.Backup, Me.Language.GetString("Backup.Message.BackupMessage"), Me.Language.GetString("Backup.Message.RestoreMessage"))

            ElseIf args.Status = BackupStatus.Succeed Then
                cellProgressEx.tbProgressStyle = ProgressStyle.Text
                cellProgressEx.tbShowCancelButton = False
                cellProgressEx.tbText = IIf(args.BackupType = BackupRestore.Backup, Me.Language.GetString("App.Cell.BackupSucceed"), Me.Language.GetString("Backup.Message.RestoreSucceed"))
                Me.SetBackupRestoreBtnEnable(True)
                Me.mCurrentRestoreRow = Nothing
                Me.lblMessage.Text = IIf(args.BackupType = BackupRestore.Backup, Me.Language.GetString("App.Cell.BackupSucceed"), Me.Language.GetString("Backup.Message.RestoreSucceed"))

                If args.BackupType = BackupRestore.Backup AndAlso Not args.IsNewBackup Then

                    If Me.RemoveRow(args.FolderPath) Then
                        Dim intIndex As Integer = 0
                        Dim bakObject As BackupInfo = Me.mBackupHelper.AddLstFullBackupList(args.FolderPath, intIndex)

                        If intIndex = 0 AndAlso BackupHelper.LstFailureBackupList.Count > 0 Then
                            intIndex = 1
                        End If

                        Me.AddRow(intIndex, bakObject, False)
                        If Not Me.LoadSize() Then
                            Me.LoadSize()
                        End If
                    End If
                End If

                If mbdb.CheckIsEncrypted(args.FolderPath) Then
                    celStatus.tbCellStatus = CellButtonStyle.BackupLock
                    celStatus.ToolTipText = Me.Language.GetString("Backup.Label.EncryptionBackup")          '"加密备份"
                Else
                    celStatus.tbCellStatus = CellButtonStyle.BackupUnlock
                End If


            ElseIf args.Status = BackupStatus.None Then
                cellProgressEx.tbProgressStyle = ProgressStyle.Text
                cellProgressEx.tbShowCancelButton = False
                cellProgressEx.tbText = ""
                Me.SetBackupRestoreBtnEnable(True)
                Me.SetRestoreBtnEnable()
                Me.mCurrentRestoreRow = Nothing
                Me.lblMessage.Text = ""

            ElseIf args.Status = BackupStatus.Cancel Then
                cellProgressEx.tbProgressStyle = ProgressStyle.Text
                cellProgressEx.tbShowCancelButton = False
                cellProgressEx.tbText = ""
                Me.SetBackupRestoreBtnEnable(True)
                Me.SetRestoreBtnEnable()
                Me.mCurrentRestoreRow = Nothing
                Me.lblMessage.Text = ""
                Me.SetRowCanRestore()
            ElseIf args.Status = BackupStatus.ReadyRestore Then

            Else
                cellProgressEx.tbProgressStyle = ProgressStyle.Failed
                cellProgressEx.tbShowCancelButton = False
                cellProgressEx.tbText = ""
                cellProgressEx.tbTextFail = args.ErrorMsg
                Me.SetBackupRestoreBtnEnable(True)
                Me.mCurrentRestoreRow = Nothing
                Me.lblMessage.Text = ""
                Me.SetRowCanRestore()

            End If

            If args.Cancel Then
                cellProgressEx.tbProgressStyle = ProgressStyle.Button
                cellProgressEx.tbShowButtonFirst = True
                cellProgressEx.tbTextBackup = Me.Language.GetString("Photo.Menu.View")              '"查看"
                cellProgressEx.tbShowButtonSecond = True
                cellProgressEx.tbShowButtonThird = False
                cellProgressEx.tbTextUnInstall = Me.Language.GetString("Common.Button.Restore")     '"还原"
                Me.mCurrentRestoreRow = Nothing
            End If

            '''''''''''''''''''''''''''行为数据收集'''''''''''''''''''''''''
            Dim mode As ModelKey = ModelKey.BackupRestore
            If args.BackupType = BackupRestore.Backup Then
                mode = ModelKey.BackupBackup
            End If
            If args.Status = BackupStatus.Succeed Then
                ActionCollectHelper.BackupRestoreAccition(Me.mDevice, ModelKey.Backup, mode, FunctionSucceed.Succeed, "")
            ElseIf args.Status = BackupStatus.Failure Then
                ActionCollectHelper.BackupRestoreAccition(Me.mDevice, ModelKey.Backup, mode, FunctionSucceed.Failure, args.ErrorMsg)
            End If
            ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
        End If
    End Sub

    Public Sub OnDecryptInputPwd(ByVal sender As Object, ByVal args As BackupPwdArgs)
        Dim strPwd As String = String.Empty
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupPwdArgs)(AddressOf OnDecryptInputPwd), sender, args)
        Else
            '"您曾经在iTunes中备份加密，请输入设置的备份密码。"           '"iTunes备份密码相关教程"
            If tbInputBox.Show(Me, Me.Language.GetString("Backup.Message.TypeInBackupPwd"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, MessageBoxDefaultButton.Button1, strPwd, True, My.Resources.backup_password) = Windows.Forms.DialogResult.OK Then ', Me.Language.GetString("Backup.Message.iTunesTutorial"), "http://news.tongbu.com/92502.html"
                args.Password = strPwd
            Else
                args.Password = ""
                args.IsCancel = True
            End If

        End If
    End Sub

    Private Sub OnLoadBackupList(sender As Object, args As EventArgs)
        Me.LoadBackUpList()
    End Sub


    Private Sub SetRestoreBtnEnable()
        If Me.mCurrentRestoreRow Is Nothing Then
            Return
        End If
        Dim cellProgressEx As tbDataGridViewProgressCellEx = Me.mCurrentRestoreRow.Cells("colProgress")
        cellProgressEx.tbProgressStyle = ProgressStyle.Button
        'cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall
    End Sub

#End Region

#Region "--- ios10备份降级 ---"

    Private Sub btnCopyBackFiles_Click(sender As Object, e As EventArgs) Handles btnCopyBackFiles.Click
        '判断选中的备份文件是ios10及以上
        If Me.mDgvBackupList.SelectedRows Is Nothing OrElse _
           Me.mDgvBackupList.SelectedRows.Count <= 0 OrElse _
           Me.mDgvBackupList.SelectedRows(0).Tag Is Nothing OrElse _
           Not TypeOf Me.mDgvBackupList.SelectedRows(0).Tag Is BackupInfo OrElse _
           Val(CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo).ProductVersion) < 10 Then
            tbMessageBox.Show(Me, "请选择一个iOS10及以上的备份进行降级！", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        '判断空间够不够
        Dim bakInfo As BackupInfo = CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo)
        Dim info As New DirectoryInfo(bakInfo.FolderPath)
        Dim divInfo As New DriveInfo(info.Root.ToString)
        If divInfo.TotalFreeSpace < bakInfo.Size Then
            tbMessageBox.Show(Me, Me.Language.GetString("Flash.Progress.DiskLack"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Me.BackupDowngradeiOS10(Me.mDgvBackupList.SelectedRows(0).Tag)
    End Sub

    Private Sub BackupDowngradeiOS10(ByVal info As BackupInfo)
        Try
            Dim thr As New Threading.Thread(New Threading.ParameterizedThreadStart(AddressOf BackupDowngradeiOS10Thread))
            With thr
                .IsBackground = True
                .Start(info)
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmBackupList_BackupDowngradeiOS10")
        End Try
    End Sub

    Private Sub BackupDowngradeiOS10Thread(ByVal infoObj As Object)
        Me.SetCopyBackFilesEnable(False)
        Dim blnSucceed As Boolean = True
        Dim strErrMsg As String = ""
        Dim info As BackupInfo = infoObj
        Dim strBackupDowngradeFolder As String = Me.GetDowngradeFolder(info.FolderPath)
        Try
            Dim lstRecords As List(Of MBFileRecord) = mbdb.ReloadDB(Path.Combine(info.FolderPath, "Manifest.db"), info.FolderPath, True)
            Dim lstUpdateRecords As New List(Of MBFileRecord)
            Dim index As Integer = 0
            '1加载所有的信息
            Dim lstDomain As New List(Of String)
            Dim strDomain As String = ""
            For Each item As MBFileRecord In lstRecords
                index += 1

                '去掉这一类的记录不作还原
                If Not Me.CheckBackupItem(item) Then
                    Continue For
                End If
                item.LoadInfoFromPlist()
                lstUpdateRecords.Add(item)

                Me.SetCopyBackFilesProgress((index * 100 / lstRecords.Count) * 0.5, "")
                '把降级的domain输出后面可以追查。
                If Not lstDomain.Contains(item.Domain) Then
                    lstDomain.Add(item.Domain)
                    strDomain &= vbCrLf & item.Domain
                End If
            Next

            Common.LogException(strDomain, "降级Domain:")
            '2创建备份数据库文件
            mbdb.Write(strBackupDowngradeFolder, lstUpdateRecords)

            '3copy文件
            index = 0
            For Each item As MBFileRecord In lstUpdateRecords
                Try
                    If File.Exists(item.PathOnPC) Then
                        Dim strFileDes As String = Path.Combine(strBackupDowngradeFolder, Path.GetFileName(item.PathOnPC))
                        File.Copy(item.PathOnPC, strFileDes)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "frmBackupList_BackupDowngradeiOS10Thread_CopyFile")
                    blnSucceed = False
                    strErrMsg = "拷贝文件失败，磁盘空间不足或文件占用！"
                    Exit For
                End Try
                index += 1
                Me.SetCopyBackFilesProgress((index * 100 / lstRecords.Count) * 0.5 + 50, "")
            Next

            '4copy其它相关文件
            Try
                File.Copy(Path.Combine(info.FolderPath, "Info.Plist"), Path.Combine(strBackupDowngradeFolder, "Info.Plist"))
                File.Copy(Path.Combine(info.FolderPath, "Manifest.plist"), Path.Combine(strBackupDowngradeFolder, "Manifest.plist"))
                File.Copy(Path.Combine(info.FolderPath, "Status.plist"), Path.Combine(strBackupDowngradeFolder, "Status.plist"))
                If Not File.Exists(Path.Combine(strBackupDowngradeFolder, "Info.Plist")) OrElse _
                   Not File.Exists(Path.Combine(strBackupDowngradeFolder, "Manifest.plist")) OrElse _
                   Not File.Exists(Path.Combine(strBackupDowngradeFolder, "Status.plist")) OrElse _
                   Not File.Exists(Path.Combine(strBackupDowngradeFolder, "Manifest.mbdb")) Then
                    blnSucceed = False
                    strErrMsg = "拷贝备份数据库失败！"
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmBackupList_BackupDowngradeiOS10Thread_CopyStatus")
            End Try

            '5修改版本号
            If blnSucceed Then
                blnSucceed = mbdb.UpdateBackupVersion(strBackupDowngradeFolder, "9.3.3")
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmBackupList_BackupDowngradeiOS10Thread")
        End Try

        If blnSucceed Then
            Me.SetCopyBackFilesProgress(0, "降级成功！")
            Me.LoadBackInfo(strBackupDowngradeFolder)
            Dim lstPath As New List(Of String)
            lstPath.Add(strBackupDowngradeFolder)
            '用户添加的目录记录到xml文件里面。
            XmlHelper.InsertXMForBackupPath(lstPath)
            Me.LoadSize()
        Else
            Common.LogException("BackupDowngradeiOS10ThreadError:", strErrMsg)
            Me.SetCopyBackFilesProgress(0, "降级失败！" & strErrMsg)
            Try
                Directory.Delete(strBackupDowngradeFolder, True)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmBackupList_BackupDowngradeiOS10Thread_Delete")
            End Try
        End If
        Me.SetCopyBackFilesEnable(True)
    End Sub

    Private Function CheckBackupItem(ByVal item As MBFileRecord) As Boolean
        Dim blnReturn As Boolean = False
        If item.domain.Contains("AppDomain-") OrElse
           item.domain = "CameraRollDomain" OrElse
           item.domain = "KeyboardDomain" OrElse
          (item.domain = "HomeDomain" AndAlso item.relativePath = "") OrElse
          (item.domain = "HomeDomain" AndAlso item.relativePath = "Library") OrElse
          (item.domain = "HomeDomain" AndAlso item.relativePath.Contains("Library/SMS")) OrElse
          (item.domain = "HomeDomain" AndAlso item.relativePath.Contains("Library/Notes")) OrElse
          (item.domain = "HomeDomain" AndAlso item.relativePath.Contains("Library/AddressBook")) OrElse
          (item.domain = "HomeDomain" AndAlso item.relativePath.Contains("Library/CallHistory")) Then
            blnReturn = True
        End If
        Return blnReturn
    End Function

    '显示进度
    Private Delegate Sub SetCopyBackFilesProgressHandler(ByVal intProgress As Integer, ByVal strMsg As String)
    Private Sub SetCopyBackFilesProgress(ByVal intProgress As Integer, ByVal strMsg As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetCopyBackFilesProgressHandler(AddressOf SetCopyBackFilesProgress), intProgress, strMsg)
        Else
            If String.IsNullOrEmpty(strMsg) Then
                Me.lblMessage.Text = String.Format("正在生成降级备份文件 {0}%", intProgress)
            Else
                Me.lblMessage.Text = strMsg
            End If
        End If
    End Sub

    '设置button状态
    Private Delegate Sub SetCopyBackFilesEnableHandler(ByVal blnEnable As Boolean)
    Private Sub SetCopyBackFilesEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetCopyBackFilesEnableHandler(AddressOf SetCopyBackFilesEnable), blnEnable)
        Else
            Me.btnCopyBackFiles.Enabled = blnEnable
        End If
    End Sub

    Private Function GetDowngradeFolder(ByVal strBackPath As String) As String
        Dim strFolder As String = ""
        Dim strFolderName As String = Path.GetFileNameWithoutExtension(strBackPath)
        Dim strRoot As String = Path.GetDirectoryName(strBackPath)
        If strFolderName.Contains("(") Then
            Dim intIndex As Integer = strFolderName.IndexOf("(")
            strFolderName = strFolderName.Substring(0, intIndex)
        End If
        Dim index As Integer = 0
        While True
            index += 1
            strFolder = Path.Combine(strRoot, String.Format("{0}({1})", strFolderName, index))
            If Not Directory.Exists(strFolder) Then
                Exit While
            End If
        End While
        Folder.CheckFolder(strFolder)
        Return strFolder
    End Function

#End Region

    Private Sub BackupListDeleteHelper_BackupListDeleteEventHandler(sender As Object, e As BackupListDeleteEventArgs)
        Try
            Dim cRow As tbDataGridViewRow = Nothing
            For Each item As tbDataGridViewRow In Me.mDgvBackupList.Rows
                If item.Tag Is Nothing Then
                    Continue For
                End If
                Dim bakInfo As BackupInfo = item.Tag
                If bakInfo.FolderPath = e.StrFolderPath Then
                    cRow = item
                    Exit For
                End If
            Next
            If cRow Is Nothing AndAlso e.DelStatus = DeleteStatus.DeleteFinish Then
                Me.CountTotalInfo()
                Me.SetDeleteEnable(True)
                Me.mblnDeleting = False
                Return
            End If

            Dim cellProgressEx As tbDataGridViewProgressCellEx = cRow.Cells("colProgress")

            Select Case e.DelStatus

                Case DeleteStatus.WaitDelete
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = Me.Language.GetString("WeChat.Button.WaitingDelete") '"等待删除"

                Case DeleteStatus.Deleteing
                    cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                    cellProgressEx.tbText = Me.Language.GetString("WeChat.Button.Deleting") '"正在删除"
                    cellProgressEx.tbShowCancelButton = True
                    If e.IntProgress = 100 Then
                        cellProgressEx.tbProgressValue = 99
                    Else
                        cellProgressEx.tbProgressValue = e.IntProgress
                    End If

                Case DeleteStatus.Success
                    XmlHelper.DeleteXMLForBackupPath(e.StrFolderPath)
                    'Me.mDgvBackupList.Rows.Remove(cRow)
                    Me.mDgvBackupList.RemoveGroupItem(cRow)
                    Dim info As BackupInfo = BackupHelper.CheckBackObjExist(e.StrFolderPath, 0)
                    If info IsNot Nothing Then
                        BackupHelper.LstFullBackupList.Remove(info)
                    End If
                    Me.CountTotalInfo()

                Case DeleteStatus.Failure
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = Me.Language.GetString("Backup.Message.DeleteFailure") '删除失败

            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString(), "BackupListDeleteHelper_BackupListDeleteEventHandler")
        End Try
    End Sub

    Private Sub btnRestoreSMS_Click(sender As Object, e As EventArgs) Handles btnRestoreSMS.Click
        If Me.mDgvBackupList.SelectedRows.Count <= 0 Then
            '"请选择要还原的备份文件。"
            tbMessageBox.Show(Me, Me.Language.GetString("Backup.Message.ChooseBackupFile"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Try
            Dim info As BackupInfo = Me.mDgvBackupList.SelectedRows(0).Tag
            Me.mCurrentRestoreRow = Me.mDgvBackupList.SelectedRows(0)
            If info IsNot Nothing Then
                Dim strMessage As String = "此功能只单独还原备份文件中的短信数据，其它手机上的信息会被抹掉！" & vbCrLf & String.Format(Me.Language.GetString("Backup.Message.BeforeRestoreWaning"), Me.mDevice.OriginalDeviceName)
                If tbMessageBox.Show(Me, strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo) = Windows.Forms.DialogResult.No Then
                    Return
                End If
                Me.SetOtherRowCanRestore(info)
                Dim cellProgressEx As tbDataGridViewProgressCellEx = Me.mCurrentRestoreRow.Cells("colProgress")
                cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                cellProgressEx.tbText = ""
                cellProgressEx.tbShowCancelButton = False
                cellProgressEx.tbProgressValue = 0
                Me.SetBackupRestoreBtnEnable(False)

                Me.mCurrentRestoreRow = Nothing
                Me.lblMessage.Text = Me.Language.GetString("Backup.Message.RestoreMessage")
                Me.mBackupHelper.StartRestore(info.FolderPath, True, "", "", "", True)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmBackupList_btnRestoreSMS_Click")
        End Try
    End Sub

    'Private Sub btnWhatsApp_Click(sender As Object, e As EventArgs)
    '    Try
    '        Dim strBackupPath As String = String.Empty
    '        If Me.mDgvBackupList.SelectedRows IsNot Nothing AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing AndAlso TypeOf Me.mDgvBackupList.SelectedRows(0).Tag Is BackupInfo Then
    '            strBackupPath = CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo).FolderPath
    '        End If
    '        Dim frm As New frmWhatsApp(Me.mApplication, strBackupPath)
    '        frm.ShowDialog(Me)
    '    Catch ex As Exception

    '    End Try
    'End Sub

End Class