﻿Imports System.IO
Imports System.Data
Imports System.Data.SQLite3
Imports iTong.CoreFoundation

Public Class BackupNotesDB
    Implements IDisposable
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private Shared mLocker As New Object
    Private Shared mDictDB As New Dictionary(Of String, BackupNotesDB)
    Private mConn As SQLiteConnection = Nothing
    Private mTempFolder As String = ""
    Private mDbPathOnBackupFolder As String = ""
    Private mDbPathOnTempFolder As String = ""

    Public Sub New(ByVal strDBFilePath As String)
        Me.mTempFolder = Folder.GetTempFilePath()
        Folder.CheckFolder(Me.mTempFolder)
        Me.mDbPathOnBackupFolder = strDBFilePath
    End Sub

    Public Shared Function Instance(ByVal strDBFilePath As String) As BackupNotesDB
        Dim strID As String = strDBFilePath.Replace("/", "").Replace("\", "").Replace(" ", "").Replace(".", "").Replace(":", "")
        Try
            SyncLock mLocker
                If Not String.IsNullOrEmpty(strID) AndAlso mDictDB.ContainsKey(strID) Then
                    Dim db As BackupNotesDB = mDictDB(strID)
                    If db Is Nothing Then
                        mDictDB.Remove(strID)

                        db = New BackupNotesDB(strDBFilePath)
                        mDictDB.Add(strID, db)
                    End If

                Else
                    Dim db As New BackupNotesDB(strDBFilePath)
                    mDictDB.Add(strID, db)

                End If
            End SyncLock
        Catch ex As Exception
        End Try
        Return mDictDB(strID)
    End Function

    '加载数据库
    Private Function Reload() As Boolean
        Dim blnResult As Boolean = False
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            Me.mDbPathOnTempFolder = Path.Combine(Me.mTempFolder, "notes.sqlite")

            Try
                If File.Exists(Me.mDbPathOnTempFolder) Then
                    File.Delete(Me.mDbPathOnTempFolder)
                End If

                If File.Exists(Me.mDbPathOnBackupFolder) Then
                    File.Copy(Me.mDbPathOnBackupFolder, Me.mDbPathOnTempFolder)
                End If

                'If File.Exists(Me.mDbPathOnTempFolder.Replace("notes.sqlite", "notes.sqlite-shm")) Then
                '    File.Delete(Me.mDbPathOnTempFolder.Replace("notes.sqlite", "notes.sqlite-shm"))
                'End If

                'If File.Exists(Me.mDbPathOnBackupFolder.Replace("notes.sqlite", "notes.sqlite-shm")) Then
                '    File.Copy(Me.mDbPathOnBackupFolder.Replace("notes.sqlite", "notes.sqlite-shm"), Me.mDbPathOnTempFolder.Replace("notes.sqlite", "notes.sqlite-shm"))
                'End If

                'If File.Exists(Me.mDbPathOnTempFolder.Replace("notes.sqlite", "notes.sqlite-wal")) Then
                '    File.Delete(Me.mDbPathOnTempFolder.Replace("notes.sqlite", "notes.sqlite-wal"))
                'End If

                'If File.Exists(Me.mDbPathOnBackupFolder.Replace("notes.sqlite", "notes.sqlite-wal")) Then
                '    File.Copy(Me.mDbPathOnBackupFolder.Replace("notes.sqlite", "notes.sqlite-wal"), Me.mDbPathOnTempFolder.Replace("notes.sqlite", "notes.sqlite-wal"))
                'End If
            Catch ex As Exception
            End Try
            If File.Exists(Me.mDbPathOnTempFolder) Then

                Me.mConn = SQLiteClass3.CreateConnectionFromFile(Me.mDbPathOnTempFolder)
                If Me.mConn IsNot Nothing Then
                    blnResult = True
                End If
            End If
        Catch ex As Exception
        End Try
        Return blnResult
    End Function

    Public Sub Close()
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If
        Catch ex As Exception
        End Try
    End Sub

#Region " Notes 操作 "

    Public Function GetNotes() As List(Of Note)
        '加载数据文件
        Me.Reload()
        '取得所有联系人基本信息
        Dim listInfo As List(Of Note) = Me.LoadNotes()

        Me.Dispose()
        Return listInfo
    End Function

    '加载联系人
    Private Function LoadNotes() As List(Of Note)
        Dim listInfo As New List(Of Note)
        Try
            If Me.mConn IsNot Nothing Then
                Dim strSQL As String = "SELECT A.ZAUTHOR,A.ZCONTENTTYPE,A.ZCREATIONDATE,A.ZMODIFICATIONDATE,A.ZTITLE,B.ZCONTENT From ZNOTE A Left Join ZNOTEBODY B ON A.ZBODY=B.Z_PK;"
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(strSQL, Me.mConn)
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim info As New Note
                        info.Author = Common.GetValue(Of String)(row("ZAUTHOR"), String.Empty)
                        info.Content = Common.GetValue(Of String)(row("ZCONTENT"), String.Empty)

                        Dim contactType As String = Common.GetValue(Of String)(row("ZCONTENTTYPE"), String.Empty)
                        Dim type As ContentType = ContentType.TextPlain
                        If contactType <> "1" Then
                            type = ContentType.TextHtml
                        End If
                        info.ContentType = contactType

                        info.DateCreated = Me.GetDataFormString(Common.GetValue(Of String)(row("ZCREATIONDATE"), String.Empty))
                        info.DateModified = Me.GetDataFormString(Common.GetValue(Of String)(row("ZMODIFICATIONDATE"), String.Empty))
                        info.Subject = Common.GetValue(Of String)(row("ZTITLE"), String.Empty)
                        listInfo.Add(info)
                    Next
                End If
            End If
        Catch ex As Exception
        End Try
        Return listInfo
    End Function

    Private Function GetDataFormString(ByVal strValue As String) As Date
        Dim dateReturn As Date
        Dim longDate As Long = 0
        Dim dateStr As String = strValue
        If Not String.IsNullOrEmpty(dateStr) Then
            Dim index As Integer = dateStr.IndexOf(".")
            If index > -1 Then
                dateStr = dateStr.Substring(0, index)
                If Long.TryParse(dateStr, longDate) Then
                    dateReturn = Common.ConvertToPcTime(longDate)
                End If
            Else
                dateReturn = Common.ConvertToPcTime(strValue)
            End If
        End If
        Return dateReturn
    End Function

#End Region

#Region " IDisposable Support "

    ' IDisposable
    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If

            ' TODO: 释放共享的非托管资源
        End If
        Me.disposedValue = True
    End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

#End Region

End Class
