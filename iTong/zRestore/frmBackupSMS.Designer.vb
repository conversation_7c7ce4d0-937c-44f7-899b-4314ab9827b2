﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmBackupSMS
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBackupSMS))
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.pnlControl = New iTong.Components.tbPanel()
        Me.btnDataRecovery = New iTong.Components.tbButton()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.txtSearchFile = New iTong.Components.tbSearch()
        Me.btnImport = New iTong.Components.tbButton()
        Me.btnExport = New iTong.Components.tbButton()
        Me.btnSave = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.chkSelectAll = New iTong.Components.tbCheckBox()
        Me.tlpMain = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlFromBackup = New iTong.Components.tbPanel()
        Me.llblRefresh = New System.Windows.Forms.LinkLabel()
        Me.lblFromBackupMsg = New iTong.Components.tbLabel()
        Me.pnlTitle = New iTong.Components.tbPanel()
        Me.lblTitle = New iTong.Components.tbLabel()
        Me.Spliter1 = New System.Windows.Forms.Panel()
        Me.pnlLeft = New iTong.Components.tbPanel()
        Me.pnlGroups = New System.Windows.Forms.Panel()
        Me.pnlDatagridHeader = New System.Windows.Forms.Panel()
        Me.btnSearch = New iTong.Components.tbButton()
        Me.txtSearchContract = New iTong.Components.tbSearch()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.pnlCount = New System.Windows.Forms.Panel()
        Me.lblPrompt = New System.Windows.Forms.Label()
        Me.munSearch = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiSearchCurrentGroup = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSearchAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSearchContact = New System.Windows.Forms.ToolStripMenuItem()
        Me.munExport = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiExportToTxt = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToTxtSelected = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToTxtAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToExcel = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToExcelSelected = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToExcelAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToCSV = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToCSVSelected = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToCSVAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlFileEmpty = New iTong.Components.tbPanel()
        Me.btnDataRecoveryEx = New iTong.Components.tbButton()
        Me.picFileEmpty = New System.Windows.Forms.PictureBox()
        Me.lblFileEmpty = New iTong.Components.tbLabel()
        Me.pnlLoading = New System.Windows.Forms.Panel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.pnlDescription = New iTong.Components.tbPanel()
        Me.lblCancelPwdBackup = New iTong.Components.tbLabel()
        Me.lblStep2Description = New System.Windows.Forms.Label()
        Me.picClosePwd1 = New System.Windows.Forms.PictureBox()
        Me.btnReCheckEx1 = New iTong.Components.tbButton()
        Me.lblStep1Description = New System.Windows.Forms.Label()
        Me.picClosePwd = New System.Windows.Forms.PictureBox()
        Me.btn_Setting = New iTong.Components.tbButton()
        Me.pnlControl.SuspendLayout()
        Me.tlpMain.SuspendLayout()
        Me.pnlFromBackup.SuspendLayout()
        Me.pnlTitle.SuspendLayout()
        Me.pnlLeft.SuspendLayout()
        Me.pnlDatagridHeader.SuspendLayout()
        Me.pnlContainer.SuspendLayout()
        Me.pnlCount.SuspendLayout()
        Me.munSearch.SuspendLayout()
        Me.munExport.SuspendLayout()
        Me.pnlFileEmpty.SuspendLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDescription.SuspendLayout()
        CType(Me.picClosePwd1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picClosePwd, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(13, 577)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(775, 18)
        Me.lblMessage.TabIndex = 33
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = False
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlControl
        '
        Me.pnlControl.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlControl.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlControl.Controls.Add(Me.btnDataRecovery)
        Me.pnlControl.Controls.Add(Me.btnRefresh)
        Me.pnlControl.Controls.Add(Me.txtSearchFile)
        Me.pnlControl.Controls.Add(Me.btnImport)
        Me.pnlControl.Controls.Add(Me.btnExport)
        Me.pnlControl.Controls.Add(Me.btnSave)
        Me.pnlControl.Controls.Add(Me.btnDelete)
        Me.pnlControl.Location = New System.Drawing.Point(1, 32)
        Me.pnlControl.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlControl.Name = "pnlControl"
        Me.pnlControl.Size = New System.Drawing.Size(798, 33)
        Me.pnlControl.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlControl.TabIndex = 31
        Me.pnlControl.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlControl.tbShowWatermark = False
        Me.pnlControl.tbSplit = "3,3,3,3"
        Me.pnlControl.tbWatermark = Nothing
        Me.pnlControl.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlControl.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnDataRecovery
        '
        Me.btnDataRecovery.BackColor = System.Drawing.Color.Transparent
        Me.btnDataRecovery.BindingForm = Nothing
        Me.btnDataRecovery.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDataRecovery.Location = New System.Drawing.Point(292, 5)
        Me.btnDataRecovery.Name = "btnDataRecovery"
        Me.btnDataRecovery.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDataRecovery.Selectable = True
        Me.btnDataRecovery.Size = New System.Drawing.Size(105, 23)
        Me.btnDataRecovery.TabIndex = 43
        Me.btnDataRecovery.tbAdriftIconWhenHover = False
        Me.btnDataRecovery.tbAutoSize = False
        Me.btnDataRecovery.tbAutoSizeEx = True
        Me.btnDataRecovery.tbBackgroundImage = Nothing
        Me.btnDataRecovery.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDataRecovery.tbBadgeNumber = 0
        Me.btnDataRecovery.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDataRecovery.tbEndEllipsis = False
        Me.btnDataRecovery.tbIconHoldPlace = True
        Me.btnDataRecovery.tbIconImage = Global.iTong.My.Resources.Resources.btn_datarecovery_4
        Me.btnDataRecovery.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDataRecovery.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDataRecovery.tbIconMore = False
        Me.btnDataRecovery.tbIconMouseDown = Nothing
        Me.btnDataRecovery.tbIconMouseHover = Nothing
        Me.btnDataRecovery.tbIconMouseLeave = Nothing
        Me.btnDataRecovery.tbIconPlaceText = 2
        Me.btnDataRecovery.tbIconReadOnly = Nothing
        Me.btnDataRecovery.tbImageMouseDown = Nothing
        Me.btnDataRecovery.tbImageMouseHover = Nothing
        Me.btnDataRecovery.tbImageMouseLeave = Nothing
        Me.btnDataRecovery.tbProgressValue = 50
        Me.btnDataRecovery.tbReadOnly = False
        Me.btnDataRecovery.tbReadOnlyText = False
        Me.btnDataRecovery.tbShadow = False
        Me.btnDataRecovery.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDataRecovery.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDataRecovery.tbShowDot = False
        Me.btnDataRecovery.tbShowMoreIconImg = CType(resources.GetObject("btnDataRecovery.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDataRecovery.tbShowNew = False
        Me.btnDataRecovery.tbShowProgress = False
        Me.btnDataRecovery.tbShowTip = True
        Me.btnDataRecovery.tbShowToolTipOnButton = False
        Me.btnDataRecovery.tbSplit = "13,11,13,11"
        Me.btnDataRecovery.tbText = "人工数据恢复"
        Me.btnDataRecovery.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecovery.tbTextColor = System.Drawing.Color.Red
        Me.btnDataRecovery.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDataRecovery.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDataRecovery.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDataRecovery.tbTextMouseDownPlace = 2
        Me.btnDataRecovery.tbToolTip = ""
        Me.btnDataRecovery.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDataRecovery.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDataRecovery.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecovery.VisibleEx = True
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(444, 5)
        Me.btnRefresh.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(63, 23)
        Me.btnRefresh.TabIndex = 23
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = " 刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 2
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'txtSearchFile
        '
        Me.txtSearchFile.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.txtSearchFile.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchFile.Location = New System.Drawing.Point(624, 5)
        Me.txtSearchFile.MaxLength = 32767
        Me.txtSearchFile.Name = "txtSearchFile"
        Me.txtSearchFile.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchFile.ReadOnly = False
        Me.txtSearchFile.SearchText = ""
        Me.txtSearchFile.SearchTipText = "Search"
        Me.txtSearchFile.ShowClear = True
        Me.txtSearchFile.ShowClearAlways = False
        Me.txtSearchFile.ShowMore = False
        Me.txtSearchFile.ShowSearch = True
        Me.txtSearchFile.Size = New System.Drawing.Size(166, 23)
        Me.txtSearchFile.TabIndex = 13
        Me.txtSearchFile.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchFile.Timer = True
        Me.txtSearchFile.TimerInterval = 0.5R
        '
        'btnImport
        '
        Me.btnImport.BackColor = System.Drawing.Color.Transparent
        Me.btnImport.BindingForm = Nothing
        Me.btnImport.Enabled = False
        Me.btnImport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImport.Location = New System.Drawing.Point(11, 5)
        Me.btnImport.Name = "btnImport"
        Me.btnImport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImport.Selectable = True
        Me.btnImport.Size = New System.Drawing.Size(69, 23)
        Me.btnImport.TabIndex = 11
        Me.btnImport.tbAdriftIconWhenHover = False
        Me.btnImport.tbAutoSize = False
        Me.btnImport.tbAutoSizeEx = True
        Me.btnImport.tbBackgroundImage = Nothing
        Me.btnImport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbBadgeNumber = 0
        Me.btnImport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImport.tbEndEllipsis = False
        Me.btnImport.tbIconHoldPlace = True
        Me.btnImport.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.btnImport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbIconMore = False
        Me.btnImport.tbIconMouseDown = Nothing
        Me.btnImport.tbIconMouseHover = Nothing
        Me.btnImport.tbIconMouseLeave = Nothing
        Me.btnImport.tbIconPlaceText = 2
        Me.btnImport.tbIconReadOnly = Nothing
        Me.btnImport.tbImageMouseDown = Nothing
        Me.btnImport.tbImageMouseHover = Nothing
        Me.btnImport.tbImageMouseLeave = Nothing
        Me.btnImport.tbProgressValue = 50
        Me.btnImport.tbReadOnly = False
        Me.btnImport.tbReadOnlyText = False
        Me.btnImport.tbShadow = False
        Me.btnImport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImport.tbShowDot = False
        Me.btnImport.tbShowMoreIconImg = CType(resources.GetObject("btnImport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImport.tbShowNew = False
        Me.btnImport.tbShowProgress = False
        Me.btnImport.tbShowTip = True
        Me.btnImport.tbShowToolTipOnButton = False
        Me.btnImport.tbSplit = "13,11,13,11"
        Me.btnImport.tbText = "Import"
        Me.btnImport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnImport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextMouseDownPlace = 2
        Me.btnImport.tbToolTip = ""
        Me.btnImport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.VisibleEx = True
        '
        'btnExport
        '
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExport.Location = New System.Drawing.Point(85, 5)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(69, 23)
        Me.btnExport.TabIndex = 10
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = True
        Me.btnExport.tbBackgroundImage = Nothing
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = False
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Global.iTong.My.Resources.Resources.btn_export_4
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 2
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = CType(resources.GetObject("btnExport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "Export"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnExport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextMouseDownPlace = 2
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'btnSave
        '
        Me.btnSave.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnSave.BackColor = System.Drawing.Color.Transparent
        Me.btnSave.BindingForm = Nothing
        Me.btnSave.Enabled = False
        Me.btnSave.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSave.Location = New System.Drawing.Point(236, 5)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSave.Selectable = True
        Me.btnSave.Size = New System.Drawing.Size(57, 23)
        Me.btnSave.TabIndex = 7
        Me.btnSave.tbAdriftIconWhenHover = False
        Me.btnSave.tbAutoSize = False
        Me.btnSave.tbAutoSizeEx = True
        Me.btnSave.tbBackgroundImage = Nothing
        Me.btnSave.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSave.tbBadgeNumber = 0
        Me.btnSave.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSave.tbEndEllipsis = False
        Me.btnSave.tbIconHoldPlace = True
        Me.btnSave.tbIconImage = Global.iTong.My.Resources.Resources.btn_save_4
        Me.btnSave.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSave.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnSave.tbIconMore = False
        Me.btnSave.tbIconMouseDown = Nothing
        Me.btnSave.tbIconMouseHover = Nothing
        Me.btnSave.tbIconMouseLeave = Nothing
        Me.btnSave.tbIconPlaceText = 2
        Me.btnSave.tbIconReadOnly = Nothing
        Me.btnSave.tbImageMouseDown = Nothing
        Me.btnSave.tbImageMouseHover = Nothing
        Me.btnSave.tbImageMouseLeave = Nothing
        Me.btnSave.tbProgressValue = 50
        Me.btnSave.tbReadOnly = False
        Me.btnSave.tbReadOnlyText = False
        Me.btnSave.tbShadow = False
        Me.btnSave.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSave.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSave.tbShowDot = False
        Me.btnSave.tbShowMoreIconImg = CType(resources.GetObject("btnSave.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSave.tbShowNew = False
        Me.btnSave.tbShowProgress = False
        Me.btnSave.tbShowTip = True
        Me.btnSave.tbShowToolTipOnButton = False
        Me.btnSave.tbSplit = "13,11,13,11"
        Me.btnSave.tbText = "保存"
        Me.btnSave.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSave.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSave.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnSave.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSave.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSave.tbTextMouseDownPlace = 2
        Me.btnSave.tbToolTip = ""
        Me.btnSave.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSave.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSave.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSave.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(175, 5)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(57, 23)
        Me.btnDelete.TabIndex = 5
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "13,11,13,11"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'chkSelectAll
        '
        Me.chkSelectAll.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkSelectAll.BackColor = System.Drawing.Color.Transparent
        Me.chkSelectAll.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkSelectAll.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkSelectAll.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkSelectAll.Location = New System.Drawing.Point(4, 4)
        Me.chkSelectAll.Name = "chkSelectAll"
        Me.chkSelectAll.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkSelectAll.Size = New System.Drawing.Size(56, 18)
        Me.chkSelectAll.TabIndex = 51
        Me.chkSelectAll.tbAdriftIconWhenHover = False
        Me.chkSelectAll.tbAutoSize = False
        Me.chkSelectAll.tbAutoSizeEx = True
        Me.chkSelectAll.tbIconChecked = CType(resources.GetObject("chkSelectAll.tbIconChecked"), System.Drawing.Image)
        Me.chkSelectAll.tbIconCheckedMouseDown = Nothing
        Me.chkSelectAll.tbIconCheckedMouseHover = Nothing
        Me.chkSelectAll.tbIconCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkSelectAll.tbIconHoldPlace = True
        Me.chkSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkSelectAll.tbIconIndeterminate = CType(resources.GetObject("chkSelectAll.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkSelectAll.tbIconIndeterminateMouseDown = Nothing
        Me.chkSelectAll.tbIconIndeterminateMouseHover = Nothing
        Me.chkSelectAll.tbIconIndeterminateMouseLeave = Nothing
        Me.chkSelectAll.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkSelectAll.tbIconPlaceText = 1
        Me.chkSelectAll.tbIconUnChecked = CType(resources.GetObject("chkSelectAll.tbIconUnChecked"), System.Drawing.Image)
        Me.chkSelectAll.tbIconUnCheckedMouseDown = Nothing
        Me.chkSelectAll.tbIconUnCheckedMouseHover = Nothing
        Me.chkSelectAll.tbIconUnCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkSelectAll.tbImageBackground = Nothing
        Me.chkSelectAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkSelectAll.tbImageCheckedMouseDown = Nothing
        Me.chkSelectAll.tbImageCheckedMouseHover = Nothing
        Me.chkSelectAll.tbImageCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbImageUnCheckedMouseDown = Nothing
        Me.chkSelectAll.tbImageUnCheckedMouseHover = Nothing
        Me.chkSelectAll.tbImageUnCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbReadOnly = False
        Me.chkSelectAll.tbShadow = False
        Me.chkSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkSelectAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkSelectAll.tbSplit = "3,3,3,3"
        Me.chkSelectAll.tbToolTip = ""
        Me.chkSelectAll.Text = "全选"
        Me.chkSelectAll.UseVisualStyleBackColor = False
        '
        'tlpMain
        '
        Me.tlpMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tlpMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.tlpMain.ColumnCount = 3
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 215.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.Controls.Add(Me.pnlFromBackup, 0, 2)
        Me.tlpMain.Controls.Add(Me.pnlTitle, 2, 0)
        Me.tlpMain.Controls.Add(Me.Spliter1, 1, 0)
        Me.tlpMain.Controls.Add(Me.pnlLeft, 0, 0)
        Me.tlpMain.Controls.Add(Me.pnlContainer, 2, 1)
        Me.tlpMain.Location = New System.Drawing.Point(1, 65)
        Me.tlpMain.Margin = New System.Windows.Forms.Padding(0)
        Me.tlpMain.Name = "tlpMain"
        Me.tlpMain.RowCount = 3
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 24.0!))
        Me.tlpMain.Size = New System.Drawing.Size(798, 508)
        Me.tlpMain.TabIndex = 39
        '
        'pnlFromBackup
        '
        Me.pnlFromBackup.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.tlpMain.SetColumnSpan(Me.pnlFromBackup, 3)
        Me.pnlFromBackup.Controls.Add(Me.llblRefresh)
        Me.pnlFromBackup.Controls.Add(Me.lblFromBackupMsg)
        Me.pnlFromBackup.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlFromBackup.Location = New System.Drawing.Point(0, 484)
        Me.pnlFromBackup.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFromBackup.Name = "pnlFromBackup"
        Me.pnlFromBackup.Size = New System.Drawing.Size(798, 24)
        Me.pnlFromBackup.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFromBackup.TabIndex = 41
        Me.pnlFromBackup.tbBackgroundImage = Nothing
        Me.pnlFromBackup.tbShowWatermark = False
        Me.pnlFromBackup.tbSplit = "0,0,0,0"
        Me.pnlFromBackup.tbWatermark = Nothing
        Me.pnlFromBackup.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFromBackup.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'llblRefresh
        '
        Me.llblRefresh.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.llblRefresh.AutoSize = True
        Me.llblRefresh.BackColor = System.Drawing.Color.Transparent
        Me.llblRefresh.Cursor = System.Windows.Forms.Cursors.Hand
        Me.llblRefresh.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.llblRefresh.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.llblRefresh.LinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Location = New System.Drawing.Point(396, 6)
        Me.llblRefresh.Name = "llblRefresh"
        Me.llblRefresh.Size = New System.Drawing.Size(65, 12)
        Me.llblRefresh.TabIndex = 39
        Me.llblRefresh.TabStop = True
        Me.llblRefresh.Text = "获取最新>>"
        Me.llblRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.llblRefresh.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        '
        'lblFromBackupMsg
        '
        Me.lblFromBackupMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblFromBackupMsg.AutoSize = True
        Me.lblFromBackupMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFromBackupMsg.Location = New System.Drawing.Point(337, 6)
        Me.lblFromBackupMsg.Name = "lblFromBackupMsg"
        Me.lblFromBackupMsg.Size = New System.Drawing.Size(53, 12)
        Me.lblFromBackupMsg.TabIndex = 0
        Me.lblFromBackupMsg.tbAdriftWhenHover = False
        Me.lblFromBackupMsg.tbAutoEllipsis = False
        Me.lblFromBackupMsg.tbAutoSize = True
        Me.lblFromBackupMsg.tbHideImage = False
        Me.lblFromBackupMsg.tbIconImage = Nothing
        Me.lblFromBackupMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFromBackupMsg.tbIconPlaceText = 5
        Me.lblFromBackupMsg.tbShadow = False
        Me.lblFromBackupMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFromBackupMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFromBackupMsg.tbShowScrolling = False
        Me.lblFromBackupMsg.Text = "TbLabel1"
        '
        'pnlTitle
        '
        Me.pnlTitle.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlTitle.Controls.Add(Me.lblTitle)
        Me.pnlTitle.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTitle.Location = New System.Drawing.Point(216, 0)
        Me.pnlTitle.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTitle.Name = "pnlTitle"
        Me.pnlTitle.Size = New System.Drawing.Size(582, 32)
        Me.pnlTitle.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTitle.TabIndex = 40
        Me.pnlTitle.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTitle.tbShowWatermark = False
        Me.pnlTitle.tbSplit = "3,3,3,3"
        Me.pnlTitle.tbWatermark = Nothing
        Me.pnlTitle.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTitle.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblTitle.Location = New System.Drawing.Point(7, 5)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(568, 22)
        Me.lblTitle.TabIndex = 34
        Me.lblTitle.tbAdriftWhenHover = False
        Me.lblTitle.tbAutoEllipsis = False
        Me.lblTitle.tbAutoSize = False
        Me.lblTitle.tbHideImage = False
        Me.lblTitle.tbIconImage = Nothing
        Me.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTitle.tbIconPlaceText = 5
        Me.lblTitle.tbShadow = False
        Me.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTitle.tbShowScrolling = False
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Spliter1
        '
        Me.Spliter1.BackColor = System.Drawing.Color.FromArgb(CType(CType(214, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.Spliter1.Cursor = System.Windows.Forms.Cursors.SizeWE
        Me.Spliter1.Dock = System.Windows.Forms.DockStyle.Left
        Me.Spliter1.Location = New System.Drawing.Point(215, 0)
        Me.Spliter1.Margin = New System.Windows.Forms.Padding(0)
        Me.Spliter1.Name = "Spliter1"
        Me.tlpMain.SetRowSpan(Me.Spliter1, 2)
        Me.Spliter1.Size = New System.Drawing.Size(1, 484)
        Me.Spliter1.TabIndex = 37
        '
        'pnlLeft
        '
        Me.pnlLeft.BackColor = System.Drawing.SystemColors.Control
        Me.pnlLeft.Controls.Add(Me.pnlGroups)
        Me.pnlLeft.Controls.Add(Me.pnlDatagridHeader)
        Me.pnlLeft.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlLeft.Location = New System.Drawing.Point(0, 0)
        Me.pnlLeft.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLeft.Name = "pnlLeft"
        Me.tlpMain.SetRowSpan(Me.pnlLeft, 2)
        Me.pnlLeft.Size = New System.Drawing.Size(215, 484)
        Me.pnlLeft.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLeft.TabIndex = 38
        Me.pnlLeft.tbBackgroundImage = Nothing
        Me.pnlLeft.tbShowWatermark = False
        Me.pnlLeft.tbSplit = "0,0,0,0"
        Me.pnlLeft.tbWatermark = Nothing
        Me.pnlLeft.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLeft.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlGroups
        '
        Me.pnlGroups.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlGroups.Location = New System.Drawing.Point(0, 27)
        Me.pnlGroups.Name = "pnlGroups"
        Me.pnlGroups.Size = New System.Drawing.Size(215, 457)
        Me.pnlGroups.TabIndex = 2
        '
        'pnlDatagridHeader
        '
        Me.pnlDatagridHeader.BackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlDatagridHeader.Controls.Add(Me.btnSearch)
        Me.pnlDatagridHeader.Controls.Add(Me.chkSelectAll)
        Me.pnlDatagridHeader.Controls.Add(Me.txtSearchContract)
        Me.pnlDatagridHeader.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlDatagridHeader.Location = New System.Drawing.Point(0, 0)
        Me.pnlDatagridHeader.Name = "pnlDatagridHeader"
        Me.pnlDatagridHeader.Size = New System.Drawing.Size(215, 27)
        Me.pnlDatagridHeader.TabIndex = 1
        '
        'btnSearch
        '
        Me.btnSearch.BackColor = System.Drawing.Color.Transparent
        Me.btnSearch.BindingForm = Nothing
        Me.btnSearch.Location = New System.Drawing.Point(185, 4)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSearch.Selectable = True
        Me.btnSearch.Size = New System.Drawing.Size(27, 18)
        Me.btnSearch.TabIndex = 52
        Me.btnSearch.tbAdriftIconWhenHover = False
        Me.btnSearch.tbAutoSize = False
        Me.btnSearch.tbAutoSizeEx = False
        Me.btnSearch.tbBackgroundImage = Nothing
        Me.btnSearch.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSearch.tbBadgeNumber = 0
        Me.btnSearch.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSearch.tbEndEllipsis = False
        Me.btnSearch.tbIconHoldPlace = True
        Me.btnSearch.tbIconImage = Global.iTong.My.Resources.Resources.tvw_musicsearch_2
        Me.btnSearch.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSearch.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnSearch.tbIconMore = False
        Me.btnSearch.tbIconMouseDown = Nothing
        Me.btnSearch.tbIconMouseHover = Nothing
        Me.btnSearch.tbIconMouseLeave = Nothing
        Me.btnSearch.tbIconPlaceText = 2
        Me.btnSearch.tbIconReadOnly = Nothing
        Me.btnSearch.tbImageMouseDown = Nothing
        Me.btnSearch.tbImageMouseHover = Nothing
        Me.btnSearch.tbImageMouseLeave = Nothing
        Me.btnSearch.tbProgressValue = 50
        Me.btnSearch.tbReadOnly = False
        Me.btnSearch.tbReadOnlyText = False
        Me.btnSearch.tbShadow = False
        Me.btnSearch.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSearch.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSearch.tbShowDot = False
        Me.btnSearch.tbShowMoreIconImg = CType(resources.GetObject("btnSearch.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSearch.tbShowNew = False
        Me.btnSearch.tbShowProgress = False
        Me.btnSearch.tbShowTip = True
        Me.btnSearch.tbShowToolTipOnButton = False
        Me.btnSearch.tbSplit = "3,3,3,3"
        Me.btnSearch.tbText = ""
        Me.btnSearch.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearch.tbTextColor = System.Drawing.Color.White
        Me.btnSearch.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSearch.tbTextColorDown = System.Drawing.Color.White
        Me.btnSearch.tbTextColorHover = System.Drawing.Color.White
        Me.btnSearch.tbTextMouseDownPlace = 0
        Me.btnSearch.tbToolTip = ""
        Me.btnSearch.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSearch.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSearch.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearch.VisibleEx = True
        '
        'txtSearchContract
        '
        Me.txtSearchContract.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.txtSearchContract.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchContract.Location = New System.Drawing.Point(60, 2)
        Me.txtSearchContract.MaxLength = 32767
        Me.txtSearchContract.Name = "txtSearchContract"
        Me.txtSearchContract.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchContract.ReadOnly = False
        Me.txtSearchContract.SearchText = ""
        Me.txtSearchContract.SearchTipText = "Search"
        Me.txtSearchContract.ShowClear = True
        Me.txtSearchContract.ShowClearAlways = True
        Me.txtSearchContract.ShowMore = False
        Me.txtSearchContract.ShowSearch = True
        Me.txtSearchContract.Size = New System.Drawing.Size(119, 23)
        Me.txtSearchContract.TabIndex = 19
        Me.txtSearchContract.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchContract.Timer = True
        Me.txtSearchContract.TimerInterval = 0.5R
        Me.txtSearchContract.Visible = False
        '
        'pnlContainer
        '
        Me.pnlContainer.BackColor = System.Drawing.SystemColors.Control
        Me.pnlContainer.Controls.Add(Me.pnlCount)
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(216, 32)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(582, 452)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 36
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlCount
        '
        Me.pnlCount.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlCount.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.pnlCount.Controls.Add(Me.lblPrompt)
        Me.pnlCount.Location = New System.Drawing.Point(0, 428)
        Me.pnlCount.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlCount.Name = "pnlCount"
        Me.pnlCount.Size = New System.Drawing.Size(582, 24)
        Me.pnlCount.TabIndex = 7
        Me.pnlCount.Visible = False
        '
        'lblPrompt
        '
        Me.lblPrompt.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblPrompt.Location = New System.Drawing.Point(31, 3)
        Me.lblPrompt.Name = "lblPrompt"
        Me.lblPrompt.Size = New System.Drawing.Size(520, 18)
        Me.lblPrompt.TabIndex = 0
        Me.lblPrompt.Text = "Label1"
        Me.lblPrompt.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'munSearch
        '
        Me.munSearch.AccessibleDescription = "153x92"
        Me.munSearch.DropShadowEnabled = False
        Me.munSearch.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munSearch.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiSearchCurrentGroup, Me.tsmiSearchAll, Me.tsmiSearchContact})
        Me.munSearch.Name = "munSearch"
        Me.munSearch.Size = New System.Drawing.Size(119, 70)
        Me.munSearch.tbBackColor = System.Drawing.Color.White
        Me.munSearch.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munSearch.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munSearch.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiSearchCurrentGroup
        '
        Me.tsmiSearchCurrentGroup.Checked = True
        Me.tsmiSearchCurrentGroup.CheckOnClick = True
        Me.tsmiSearchCurrentGroup.CheckState = System.Windows.Forms.CheckState.Checked
        Me.tsmiSearchCurrentGroup.Name = "tsmiSearchCurrentGroup"
        Me.tsmiSearchCurrentGroup.Size = New System.Drawing.Size(118, 22)
        Me.tsmiSearchCurrentGroup.Text = "当前群组"
        '
        'tsmiSearchAll
        '
        Me.tsmiSearchAll.CheckOnClick = True
        Me.tsmiSearchAll.Name = "tsmiSearchAll"
        Me.tsmiSearchAll.Size = New System.Drawing.Size(118, 22)
        Me.tsmiSearchAll.Text = "全部"
        '
        'tsmiSearchContact
        '
        Me.tsmiSearchContact.CheckOnClick = True
        Me.tsmiSearchContact.Name = "tsmiSearchContact"
        Me.tsmiSearchContact.Size = New System.Drawing.Size(118, 22)
        Me.tsmiSearchContact.Text = "联系人"
        '
        'munExport
        '
        Me.munExport.AccessibleDescription = "153x92"
        Me.munExport.DropShadowEnabled = False
        Me.munExport.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munExport.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExportToTxt, Me.tsmiExportToExcel, Me.tsmiExportToCSV})
        Me.munExport.Name = "munSearch"
        Me.munExport.Size = New System.Drawing.Size(137, 70)
        Me.munExport.tbBackColor = System.Drawing.Color.White
        Me.munExport.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munExport.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munExport.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiExportToTxt
        '
        Me.tsmiExportToTxt.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExportToTxtSelected, Me.tsmiExportToTxtAll})
        Me.tsmiExportToTxt.Name = "tsmiExportToTxt"
        Me.tsmiExportToTxt.Size = New System.Drawing.Size(136, 22)
        Me.tsmiExportToTxt.Text = "导出到Txt"
        '
        'tsmiExportToTxtSelected
        '
        Me.tsmiExportToTxtSelected.Name = "tsmiExportToTxtSelected"
        Me.tsmiExportToTxtSelected.Size = New System.Drawing.Size(148, 22)
        Me.tsmiExportToTxtSelected.Text = "导出选中的XXX"
        '
        'tsmiExportToTxtAll
        '
        Me.tsmiExportToTxtAll.Name = "tsmiExportToTxtAll"
        Me.tsmiExportToTxtAll.Size = New System.Drawing.Size(148, 22)
        Me.tsmiExportToTxtAll.Text = "导出全部"
        '
        'tsmiExportToExcel
        '
        Me.tsmiExportToExcel.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExportToExcelSelected, Me.tsmiExportToExcelAll})
        Me.tsmiExportToExcel.Name = "tsmiExportToExcel"
        Me.tsmiExportToExcel.Size = New System.Drawing.Size(136, 22)
        Me.tsmiExportToExcel.Text = "导出到Excel"
        '
        'tsmiExportToExcelSelected
        '
        Me.tsmiExportToExcelSelected.Name = "tsmiExportToExcelSelected"
        Me.tsmiExportToExcelSelected.Size = New System.Drawing.Size(148, 22)
        Me.tsmiExportToExcelSelected.Text = "导出选中的XXX"
        '
        'tsmiExportToExcelAll
        '
        Me.tsmiExportToExcelAll.Name = "tsmiExportToExcelAll"
        Me.tsmiExportToExcelAll.Size = New System.Drawing.Size(148, 22)
        Me.tsmiExportToExcelAll.Text = "导出全部"
        '
        'tsmiExportToCSV
        '
        Me.tsmiExportToCSV.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExportToCSVSelected, Me.tsmiExportToCSVAll})
        Me.tsmiExportToCSV.Name = "tsmiExportToCSV"
        Me.tsmiExportToCSV.Size = New System.Drawing.Size(136, 22)
        Me.tsmiExportToCSV.Text = "导出到CSV"
        '
        'tsmiExportToCSVSelected
        '
        Me.tsmiExportToCSVSelected.Name = "tsmiExportToCSVSelected"
        Me.tsmiExportToCSVSelected.Size = New System.Drawing.Size(142, 22)
        Me.tsmiExportToCSVSelected.Text = "导出选中的XX"
        '
        'tsmiExportToCSVAll
        '
        Me.tsmiExportToCSVAll.Name = "tsmiExportToCSVAll"
        Me.tsmiExportToCSVAll.Size = New System.Drawing.Size(142, 22)
        Me.tsmiExportToCSVAll.Text = "导出全部"
        '
        'pnlFileEmpty
        '
        Me.pnlFileEmpty.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlFileEmpty.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFileEmpty.Controls.Add(Me.btnDataRecoveryEx)
        Me.pnlFileEmpty.Controls.Add(Me.picFileEmpty)
        Me.pnlFileEmpty.Controls.Add(Me.lblFileEmpty)
        Me.pnlFileEmpty.Location = New System.Drawing.Point(278, 108)
        Me.pnlFileEmpty.Name = "pnlFileEmpty"
        Me.pnlFileEmpty.Size = New System.Drawing.Size(492, 151)
        Me.pnlFileEmpty.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFileEmpty.TabIndex = 40
        Me.pnlFileEmpty.tbBackgroundImage = Nothing
        Me.pnlFileEmpty.tbShowWatermark = False
        Me.pnlFileEmpty.tbSplit = "0,0,0,0"
        Me.pnlFileEmpty.tbWatermark = Nothing
        Me.pnlFileEmpty.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFileEmpty.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlFileEmpty.Visible = False
        '
        'btnDataRecoveryEx
        '
        Me.btnDataRecoveryEx.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDataRecoveryEx.BackColor = System.Drawing.Color.Transparent
        Me.btnDataRecoveryEx.BindingForm = Nothing
        Me.btnDataRecoveryEx.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDataRecoveryEx.Location = New System.Drawing.Point(240, 70)
        Me.btnDataRecoveryEx.Name = "btnDataRecoveryEx"
        Me.btnDataRecoveryEx.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDataRecoveryEx.Selectable = True
        Me.btnDataRecoveryEx.Size = New System.Drawing.Size(105, 23)
        Me.btnDataRecoveryEx.TabIndex = 44
        Me.btnDataRecoveryEx.tbAdriftIconWhenHover = False
        Me.btnDataRecoveryEx.tbAutoSize = False
        Me.btnDataRecoveryEx.tbAutoSizeEx = True
        Me.btnDataRecoveryEx.tbBackgroundImage = Nothing
        Me.btnDataRecoveryEx.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDataRecoveryEx.tbBadgeNumber = 0
        Me.btnDataRecoveryEx.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDataRecoveryEx.tbEndEllipsis = False
        Me.btnDataRecoveryEx.tbIconHoldPlace = True
        Me.btnDataRecoveryEx.tbIconImage = Global.iTong.My.Resources.Resources.btn_datarecovery_4
        Me.btnDataRecoveryEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDataRecoveryEx.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDataRecoveryEx.tbIconMore = False
        Me.btnDataRecoveryEx.tbIconMouseDown = Nothing
        Me.btnDataRecoveryEx.tbIconMouseHover = Nothing
        Me.btnDataRecoveryEx.tbIconMouseLeave = Nothing
        Me.btnDataRecoveryEx.tbIconPlaceText = 2
        Me.btnDataRecoveryEx.tbIconReadOnly = Nothing
        Me.btnDataRecoveryEx.tbImageMouseDown = Nothing
        Me.btnDataRecoveryEx.tbImageMouseHover = Nothing
        Me.btnDataRecoveryEx.tbImageMouseLeave = Nothing
        Me.btnDataRecoveryEx.tbProgressValue = 50
        Me.btnDataRecoveryEx.tbReadOnly = False
        Me.btnDataRecoveryEx.tbReadOnlyText = False
        Me.btnDataRecoveryEx.tbShadow = False
        Me.btnDataRecoveryEx.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDataRecoveryEx.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDataRecoveryEx.tbShowDot = False
        Me.btnDataRecoveryEx.tbShowMoreIconImg = CType(resources.GetObject("btnDataRecoveryEx.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDataRecoveryEx.tbShowNew = False
        Me.btnDataRecoveryEx.tbShowProgress = False
        Me.btnDataRecoveryEx.tbShowTip = True
        Me.btnDataRecoveryEx.tbShowToolTipOnButton = False
        Me.btnDataRecoveryEx.tbSplit = "13,11,13,11"
        Me.btnDataRecoveryEx.tbText = "人工数据恢复"
        Me.btnDataRecoveryEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryEx.tbTextColor = System.Drawing.Color.Red
        Me.btnDataRecoveryEx.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDataRecoveryEx.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDataRecoveryEx.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDataRecoveryEx.tbTextMouseDownPlace = 2
        Me.btnDataRecoveryEx.tbToolTip = ""
        Me.btnDataRecoveryEx.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDataRecoveryEx.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDataRecoveryEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryEx.VisibleEx = True
        '
        'picFileEmpty
        '
        Me.picFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picFileEmpty.Image = Global.iTong.My.Resources.Resources.file_fileempty
        Me.picFileEmpty.Location = New System.Drawing.Point(145, 28)
        Me.picFileEmpty.Name = "picFileEmpty"
        Me.picFileEmpty.Size = New System.Drawing.Size(75, 75)
        Me.picFileEmpty.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picFileEmpty.TabIndex = 3
        Me.picFileEmpty.TabStop = False
        '
        'lblFileEmpty
        '
        Me.lblFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFileEmpty.AutoSize = True
        Me.lblFileEmpty.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFileEmpty.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblFileEmpty.Location = New System.Drawing.Point(237, 42)
        Me.lblFileEmpty.Name = "lblFileEmpty"
        Me.lblFileEmpty.Size = New System.Drawing.Size(110, 16)
        Me.lblFileEmpty.TabIndex = 2
        Me.lblFileEmpty.tbAdriftWhenHover = False
        Me.lblFileEmpty.tbAutoEllipsis = False
        Me.lblFileEmpty.tbAutoSize = True
        Me.lblFileEmpty.tbHideImage = False
        Me.lblFileEmpty.tbIconImage = Nothing
        Me.lblFileEmpty.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileEmpty.tbIconPlaceText = 5
        Me.lblFileEmpty.tbShadow = False
        Me.lblFileEmpty.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileEmpty.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileEmpty.tbShowScrolling = False
        Me.lblFileEmpty.Text = "这里没有内容"
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(277, 270)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(441, 138)
        Me.pnlLoading.TabIndex = 41
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(187, 47)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(163, 45)
        Me.lblLoading.TabIndex = 10
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(91, 22)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(91, 95)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 0
        Me.pbLoading.TabStop = False
        '
        'pnlDescription
        '
        Me.pnlDescription.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlDescription.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlDescription.Controls.Add(Me.lblCancelPwdBackup)
        Me.pnlDescription.Controls.Add(Me.lblStep2Description)
        Me.pnlDescription.Controls.Add(Me.picClosePwd1)
        Me.pnlDescription.Controls.Add(Me.btnReCheckEx1)
        Me.pnlDescription.Controls.Add(Me.lblStep1Description)
        Me.pnlDescription.Controls.Add(Me.picClosePwd)
        Me.pnlDescription.Location = New System.Drawing.Point(2, 429)
        Me.pnlDescription.Name = "pnlDescription"
        Me.pnlDescription.Size = New System.Drawing.Size(797, 109)
        Me.pnlDescription.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescription.TabIndex = 42
        Me.pnlDescription.tbBackgroundImage = Nothing
        Me.pnlDescription.tbShowWatermark = False
        Me.pnlDescription.tbSplit = "0,0,0,0"
        Me.pnlDescription.tbWatermark = Nothing
        Me.pnlDescription.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescription.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblCancelPwdBackup
        '
        Me.lblCancelPwdBackup.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCancelPwdBackup.Location = New System.Drawing.Point(75, -157)
        Me.lblCancelPwdBackup.Name = "lblCancelPwdBackup"
        Me.lblCancelPwdBackup.Size = New System.Drawing.Size(708, 54)
        Me.lblCancelPwdBackup.TabIndex = 62
        Me.lblCancelPwdBackup.tbAdriftWhenHover = False
        Me.lblCancelPwdBackup.tbAutoEllipsis = False
        Me.lblCancelPwdBackup.tbAutoSize = False
        Me.lblCancelPwdBackup.tbHideImage = False
        Me.lblCancelPwdBackup.tbIconImage = Global.iTong.My.Resources.Resources.icon_attention1
        Me.lblCancelPwdBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCancelPwdBackup.tbIconPlaceText = 5
        Me.lblCancelPwdBackup.tbShadow = False
        Me.lblCancelPwdBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCancelPwdBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCancelPwdBackup.tbShowScrolling = False
        Me.lblCancelPwdBackup.Text = "TbLabel1"
        Me.lblCancelPwdBackup.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblStep2Description
        '
        Me.lblStep2Description.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStep2Description.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStep2Description.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblStep2Description.Location = New System.Drawing.Point(411, 151)
        Me.lblStep2Description.Name = "lblStep2Description"
        Me.lblStep2Description.Size = New System.Drawing.Size(322, 24)
        Me.lblStep2Description.TabIndex = 59
        Me.lblStep2Description.Text = "取消该设备的备份加密"
        Me.lblStep2Description.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picClosePwd1
        '
        Me.picClosePwd1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picClosePwd1.Image = Global.iTong.My.Resources.Resources.weixin_close_pwd2
        Me.picClosePwd1.Location = New System.Drawing.Point(422, -91)
        Me.picClosePwd1.Name = "picClosePwd1"
        Me.picClosePwd1.Size = New System.Drawing.Size(300, 231)
        Me.picClosePwd1.TabIndex = 58
        Me.picClosePwd1.TabStop = False
        '
        'btnReCheckEx1
        '
        Me.btnReCheckEx1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnReCheckEx1.BackColor = System.Drawing.Color.Transparent
        Me.btnReCheckEx1.BindingForm = Nothing
        Me.btnReCheckEx1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReCheckEx1.Location = New System.Drawing.Point(291, 212)
        Me.btnReCheckEx1.Name = "btnReCheckEx1"
        Me.btnReCheckEx1.Padding = New System.Windows.Forms.Padding(10, 1, 10, 0)
        Me.btnReCheckEx1.Selectable = True
        Me.btnReCheckEx1.Size = New System.Drawing.Size(215, 47)
        Me.btnReCheckEx1.TabIndex = 57
        Me.btnReCheckEx1.tbAdriftIconWhenHover = False
        Me.btnReCheckEx1.tbAutoSize = False
        Me.btnReCheckEx1.tbAutoSizeEx = False
        Me.btnReCheckEx1.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnReCheckEx1.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReCheckEx1.tbBadgeNumber = 0
        Me.btnReCheckEx1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReCheckEx1.tbEndEllipsis = False
        Me.btnReCheckEx1.tbIconHoldPlace = True
        Me.btnReCheckEx1.tbIconImage = Nothing
        Me.btnReCheckEx1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReCheckEx1.tbIconMore = False
        Me.btnReCheckEx1.tbIconMouseDown = Nothing
        Me.btnReCheckEx1.tbIconMouseHover = Nothing
        Me.btnReCheckEx1.tbIconMouseLeave = Nothing
        Me.btnReCheckEx1.tbIconPlaceText = 2
        Me.btnReCheckEx1.tbIconReadOnly = Nothing
        Me.btnReCheckEx1.tbImageMouseDown = Nothing
        Me.btnReCheckEx1.tbImageMouseHover = Nothing
        Me.btnReCheckEx1.tbImageMouseLeave = Nothing
        Me.btnReCheckEx1.tbProgressValue = 50
        Me.btnReCheckEx1.tbReadOnly = False
        Me.btnReCheckEx1.tbReadOnlyText = False
        Me.btnReCheckEx1.tbShadow = False
        Me.btnReCheckEx1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnReCheckEx1.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnReCheckEx1.tbShowDot = False
        Me.btnReCheckEx1.tbShowMoreIconImg = CType(resources.GetObject("btnReCheckEx1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReCheckEx1.tbShowNew = False
        Me.btnReCheckEx1.tbShowProgress = False
        Me.btnReCheckEx1.tbShowTip = True
        Me.btnReCheckEx1.tbShowToolTipOnButton = False
        Me.btnReCheckEx1.tbSplit = "13,11,13,11"
        Me.btnReCheckEx1.tbText = "重新检查"
        Me.btnReCheckEx1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx1.tbTextColor = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextColorDown = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextColorHover = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextMouseDownPlace = 0
        Me.btnReCheckEx1.tbToolTip = ""
        Me.btnReCheckEx1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReCheckEx1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReCheckEx1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx1.VisibleEx = True
        '
        'lblStep1Description
        '
        Me.lblStep1Description.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStep1Description.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStep1Description.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblStep1Description.Location = New System.Drawing.Point(64, 151)
        Me.lblStep1Description.Name = "lblStep1Description"
        Me.lblStep1Description.Size = New System.Drawing.Size(322, 24)
        Me.lblStep1Description.TabIndex = 55
        Me.lblStep1Description.Text = "打开iTunes,选择设备"
        Me.lblStep1Description.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picClosePwd
        '
        Me.picClosePwd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picClosePwd.Image = Global.iTong.My.Resources.Resources.weixin_close_pwd1
        Me.picClosePwd.Location = New System.Drawing.Point(75, -91)
        Me.picClosePwd.Name = "picClosePwd"
        Me.picClosePwd.Size = New System.Drawing.Size(300, 231)
        Me.picClosePwd.TabIndex = 56
        Me.picClosePwd.TabStop = False
        '
        'btn_Setting
        '
        Me.btn_Setting.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Setting.BackColor = System.Drawing.Color.Transparent
        Me.btn_Setting.BindingForm = Nothing
        Me.btn_Setting.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btn_Setting.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.Location = New System.Drawing.Point(698, 2)
        Me.btn_Setting.Name = "btn_Setting"
        Me.btn_Setting.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Setting.Selectable = True
        Me.btn_Setting.Size = New System.Drawing.Size(24, 24)
        Me.btn_Setting.TabIndex = 43
        Me.btn_Setting.tbAdriftIconWhenHover = False
        Me.btn_Setting.tbAutoSize = True
        Me.btn_Setting.tbAutoSizeEx = True
        Me.btn_Setting.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_setting
        Me.btn_Setting.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_Setting.tbBadgeNumber = 0
        Me.btn_Setting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Setting.tbEndEllipsis = False
        Me.btn_Setting.tbIconHoldPlace = True
        Me.btn_Setting.tbIconImage = Nothing
        Me.btn_Setting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_Setting.tbIconMore = False
        Me.btn_Setting.tbIconMouseDown = Nothing
        Me.btn_Setting.tbIconMouseHover = Nothing
        Me.btn_Setting.tbIconMouseLeave = Nothing
        Me.btn_Setting.tbIconPlaceText = 2
        Me.btn_Setting.tbIconReadOnly = Nothing
        Me.btn_Setting.tbImageMouseDown = Nothing
        Me.btn_Setting.tbImageMouseHover = Nothing
        Me.btn_Setting.tbImageMouseLeave = Nothing
        Me.btn_Setting.tbProgressValue = 50
        Me.btn_Setting.tbReadOnly = False
        Me.btn_Setting.tbReadOnlyText = False
        Me.btn_Setting.tbShadow = False
        Me.btn_Setting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Setting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Setting.tbShowDot = False
        Me.btn_Setting.tbShowMoreIconImg = CType(resources.GetObject("btn_Setting.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_Setting.tbShowNew = False
        Me.btn_Setting.tbShowProgress = False
        Me.btn_Setting.tbShowTip = True
        Me.btn_Setting.tbShowToolTipOnButton = False
        Me.btn_Setting.tbSplit = "3,3,3,3"
        Me.btn_Setting.tbText = ""
        Me.btn_Setting.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btn_Setting.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.btn_Setting.tbTextColorDisable = System.Drawing.Color.Gray
        Me.btn_Setting.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextMouseDownPlace = 0
        Me.btn_Setting.tbToolTip = ""
        Me.btn_Setting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Setting.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.VisibleEx = True
        '
        'frmBackupSMS
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.btn_Setting)
        Me.Controls.Add(Me.tlpMain)
        Me.Controls.Add(Me.pnlDescription)
        Me.Controls.Add(Me.pnlLoading)
        Me.Controls.Add(Me.pnlFileEmpty)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.pnlControl)
        Me.Name = "frmBackupSMS"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "短信"
        Me.Controls.SetChildIndex(Me.pnlControl, 0)
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlFileEmpty, 0)
        Me.Controls.SetChildIndex(Me.pnlLoading, 0)
        Me.Controls.SetChildIndex(Me.pnlDescription, 0)
        Me.Controls.SetChildIndex(Me.tlpMain, 0)
        Me.Controls.SetChildIndex(Me.btn_Setting, 0)
        Me.pnlControl.ResumeLayout(False)
        Me.tlpMain.ResumeLayout(False)
        Me.pnlFromBackup.ResumeLayout(False)
        Me.pnlFromBackup.PerformLayout()
        Me.pnlTitle.ResumeLayout(False)
        Me.pnlLeft.ResumeLayout(False)
        Me.pnlDatagridHeader.ResumeLayout(False)
        Me.pnlContainer.ResumeLayout(False)
        Me.pnlCount.ResumeLayout(False)
        Me.munSearch.ResumeLayout(False)
        Me.munExport.ResumeLayout(False)
        Me.pnlFileEmpty.ResumeLayout(False)
        Me.pnlFileEmpty.PerformLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDescription.ResumeLayout(False)
        CType(Me.picClosePwd1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picClosePwd, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents lblMessage As tbLabel
    Friend WithEvents pnlControl As tbPanel
    Friend WithEvents btnExport As tbButton
    Friend WithEvents btnSave As tbButton
    Friend WithEvents btnDelete As tbButton
    Friend WithEvents btnImport As tbButton
    Friend WithEvents tlpMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Spliter1 As System.Windows.Forms.Panel
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents pnlLeft As tbPanel
    Friend WithEvents txtSearchFile As tbSearch
    Friend WithEvents munSearch As tbContextMenuStrip
    Friend WithEvents tsmiSearchCurrentGroup As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiSearchAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiSearchContact As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents munExport As tbContextMenuStrip
    Friend WithEvents tsmiExportToTxt As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToExcel As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToTxtSelected As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToTxtAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToExcelSelected As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToExcelAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlTitle As tbPanel
    Friend WithEvents pnlFileEmpty As tbPanel
    Friend WithEvents picFileEmpty As System.Windows.Forms.PictureBox
    Friend WithEvents lblFileEmpty As tbLabel
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblTitle As tbLabel
    Friend WithEvents chkSelectAll As tbCheckBox
    Friend WithEvents pnlDatagridHeader As System.Windows.Forms.Panel
    Friend WithEvents txtSearchContract As tbSearch
    Friend WithEvents pnlGroups As System.Windows.Forms.Panel
    Friend WithEvents btnSearch As tbButton
    Friend WithEvents tsmiExportToCSV As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToCSVSelected As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToCSVAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlDescription As iTong.Components.tbPanel
    Friend WithEvents lblStep2Description As System.Windows.Forms.Label
    Friend WithEvents picClosePwd1 As System.Windows.Forms.PictureBox
    Friend WithEvents btnReCheckEx1 As iTong.Components.tbButton
    Friend WithEvents lblStep1Description As System.Windows.Forms.Label
    Friend WithEvents picClosePwd As System.Windows.Forms.PictureBox
    Friend WithEvents lblCancelPwdBackup As iTong.Components.tbLabel
    Friend WithEvents btnRefresh As iTong.Components.tbButton
    Friend WithEvents pnlCount As System.Windows.Forms.Panel
    Friend WithEvents lblPrompt As System.Windows.Forms.Label
    Friend WithEvents btnDataRecovery As iTong.Components.tbButton
    Friend WithEvents btnDataRecoveryEx As iTong.Components.tbButton
    Friend WithEvents pnlFromBackup As iTong.Components.tbPanel
    Friend WithEvents llblRefresh As System.Windows.Forms.LinkLabel
    Friend WithEvents lblFromBackupMsg As iTong.Components.tbLabel
    Friend WithEvents btn_Setting As iTong.Components.tbButton
End Class
