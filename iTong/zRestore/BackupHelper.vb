﻿'移到CoreModuleCS
Imports System.Threading
Imports System.Security.Cryptography

Public Class BackupHelper
    Private mDevice As iPhoneDevice = Nothing
    Private mApplication As IApplication = Nothing
    Private Language As LanguageInterface = Nothing
    Private mBackupType As BackupRestore = BackupRestore.Backup
    Private mIsNewBackup As Boolean = False
    Private mIsAutoShowWeChat As Boolean = False

    Public Event BackupHandler(ByVal sender As Object, ByVal args As BackupArgs)
    Public Event BackupPwdHandler(ByVal sender As Object, ByVal args As BackupPwdArgs)

    Public Event MigrationHandler(ByVal sender As Object, ByVal args As DataMigrationArgs)

    Public Event BackupDecipherHandler(ByVal sender As Object, ByVal args As BackupDecipherArgs)

    Public Shared LstFullBackupList As New List(Of BackupInfo)
    Public Shared IsLoadFullBackupList = False
    Private mTDFullBackupList As Thread = Nothing

    Public Shared LstQuickBackupList As New List(Of BackupInfo)


    Public Shared StrSpareSku = "com.tencent.xin.mjuruangong"


#Region "--- 属性 ---"

    'Public Shared BackupList As New List(Of BackupInfo)
    Private mStrCurrentBackupFolder As String = ""

    Public Property StrCurrentBackupFolder() As String
        Get
            Return Me.mStrCurrentBackupFolder
        End Get
        Set(ByVal value As String)
            Me.mStrCurrentBackupFolder = value
        End Set
    End Property

    Public Shared ReadOnly Property BackupFolder() As String
        Get
            Dim strBackupFolder = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData).TrimEnd("\"c), "Apple Computer\MobileSync\Backup\")
            Folder.CheckFolder(strBackupFolder)
            Return strBackupFolder
        End Get
    End Property

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

#End Region

#Region "--- 单例 ---"

    Private Shared mLocker As New Object
    Private Shared mBackupHelper As BackupHelper
    Private Shared mDictInstances As New Dictionary(Of String, BackupHelper)(StringComparer.InvariantCultureIgnoreCase)

    Public Sub New(ByVal dev As iPhoneDevice, ByVal app As IApplication)
        Me.mDevice = dev
        Me.mApplication = app
        Me.Language = app.Language

        Me.GetFullBackupList()
    End Sub

    Public Sub New(ByVal dev As iPhoneDevice)
        Me.mDevice = dev

        Me.GetFullBackupList()
    End Sub

    Public Shared Function Instance(ByVal dev As iPhoneDevice, ByVal app As IApplication) As BackupHelper
        Dim helper As BackupHelper = Nothing
        Try
            If dev Is Nothing Then
                If Not mDictInstances.ContainsKey(NoDevcieIdentifier) Then
                    mDictInstances.Add(NoDevcieIdentifier, New BackupHelper(dev))
                End If
                helper = mDictInstances.Item(NoDevcieIdentifier)

            Else
                If mDictInstances.ContainsKey(dev.Identifier) Then
                    If mDictInstances(dev.Identifier).mDevice.IsConnected = False Then
                        mDictInstances.Remove(dev.Identifier)
                        mDictInstances.Add(dev.Identifier, New BackupHelper(dev, app))
                    End If
                Else
                    SyncLock mLocker
                        If Not mDictInstances.ContainsKey(dev.Identifier) Then
                            mDictInstances.Add(dev.Identifier, New BackupHelper(dev, app))
                        End If
                    End SyncLock
                End If
                helper = mDictInstances(dev.Identifier)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "BackupHelper_Instance")
        End Try
        Return helper
    End Function

#End Region

#Region "--- 备份与还原 ---"

    Public Sub StartBackup(ByVal isNewBackup As Boolean, ByVal isAutoShowWeChat As Boolean, Optional ByVal isAloneBackup As Boolean = False, Optional ByVal strSku As String = "")
        Me.mBackupType = BackupRestore.Backup
        Me.mIsNewBackup = isNewBackup
        Me.mIsAutoShowWeChat = isAutoShowWeChat
        Me.StartBackupRestoreInThread(BackupRestore.Backup, Me.StrCurrentBackupFolder, isNewBackup, isAutoShowWeChat, isAloneBackup, strSku)
    End Sub

    Public Sub StartRestore(ByVal strBackupFolder As String, Optional ByVal isFullBack As Boolean = True, Optional ByVal strSKU As String = "", Optional ByVal strUnbackDir As String = "", Optional ByVal strUnBackupPwd As String = "", Optional ByVal isGetSMSBackup As Boolean = False)
        Me.mBackupType = BackupRestore.Restore
        Me.StrCurrentBackupFolder = strBackupFolder
        Me.StartBackupRestoreInThread(BackupRestore.Restore, strBackupFolder, False, False, isFullBack, strSKU, strUnbackDir, strUnBackupPwd, isGetSMSBackup)
    End Sub

    Private mThrBackupRestore As Threading.Thread
    Private Sub StartBackupRestoreInThread(ByVal type As BackupRestore, ByVal strBackupFolder As String, ByVal isNewBackup As Boolean, ByVal isAutoShowWeChat As Boolean, Optional ByVal isFullBack As Boolean = True, Optional ByVal strSKU As String = "", Optional ByVal strUnbackDir As String = "", Optional ByVal strUnBackupPwd As String = "", Optional ByVal isGetSMSBackup As Boolean = False)

        Try
            Try
                If mThrBackupRestore IsNot Nothing AndAlso mThrBackupRestore.ThreadState <> ThreadState.Stopped Then
                    Return
                End If
            Catch
            End Try

            Dim objPara As Object = New Object() {type, strBackupFolder, isNewBackup, isAutoShowWeChat, isFullBack, strSKU, strUnbackDir, strUnBackupPwd, isGetSMSBackup}
            Me.mThrBackupRestore = New Thread(AddressOf StartBackupRestore)
            Me.mThrBackupRestore.SetApartmentState(ApartmentState.STA)
            Me.mThrBackupRestore.IsBackground = True
            Me.mThrBackupRestore.Start(objPara)

            '启动start的时候可能线程的状态来不及改变，会同时启动两个线程进去
            Utility.WaitSeconds(0.5)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "StartBackupRestoreInThread")
        End Try

    End Sub

    Private Sub StartBackupRestore(ByVal paraObj As Object)
        If paraObj Is Nothing OrElse Not TypeOf paraObj Is Object() OrElse CType(paraObj, Object()).Length < 5 Then
            Return
        End If
        Try
            Dim type As BackupRestore = paraObj(0)
            Dim strBackupFolder As String = paraObj(1)
            Dim blnNewBackup As Boolean = paraObj(2)
            Dim isAutoShowWeChat As Boolean = paraObj(3)
            Dim isFullBack As Boolean = paraObj(4)
            Dim strSKU As String = paraObj(5)
            Dim strUnbackDir As String = paraObj(6)
            Dim strUnBackupPwd As String = paraObj(7)
            Dim isGetSMSBackup As Boolean = paraObj(8)

            Dim strPwd As String = String.Empty

            Dim blnSuccess As Boolean = True
            Dim handler As New BackupRestoreHandler(AddressOf mb2_BackupHandler)
            Dim args As New BackupArgs()
            args.BackupType = type
            args.FolderPath = strBackupFolder
            args.IsNewBackup = blnNewBackup
            args.IsOpenWeChat = isAutoShowWeChat

            args.Progress = 0
            RaiseEvent BackupHandler(Me, args)

            Dim isAloneBackup As Boolean = IIf(isFullBack, False, True)
            '如果是还原 且 为整机备份 单独还原
            If type = BackupRestore.Restore AndAlso isFullBack AndAlso (strSKU.Length > 0 OrElse isGetSMSBackup) Then
                If strUnbackDir.Length > 0 AndAlso Directory.Exists(strUnbackDir) Then
                    strBackupFolder = strUnbackDir
                End If
                strPwd = strUnBackupPwd

                Dim strTempPath As String = ""
                If Not CopyBackupFilesByIdentify(strBackupFolder, strSKU, strTempPath, False, isGetSMSBackup) Then
                    Return
                End If
                isAloneBackup = True
                strBackupFolder = strTempPath
            End If
            If type = BackupRestore.Backup Then
                Folder.CheckFolder(strBackupFolder)

                blnSuccess = Me.mDevice.mb2_Backup(strBackupFolder, handler, blnNewBackup)

                '备份完成后判断文件是否正确显示错误信息
                If Not Me.CheckBackUpSucceed(strBackupFolder) Then
                    args.ErrorType = BackupRestoreErrorType.Other
                    args.ErrorMsg = BackupHelper.GetErrorMessage(type, args.ErrorType)
                    RaiseEvent BackupHandler(Me, args)
                    Return
                End If
            Else
                '密码错误
                If mbdb.CheckIsEncrypted(strBackupFolder) Then
                    Dim isCancel As Boolean = False
                    strPwd = Me.ShowPwdMessagebox(isCancel)
                    If String.IsNullOrEmpty(strPwd) Then
                        If isCancel Then
                            args.Status = BackupStatus.Cancel
                        Else
                            args.Status = BackupStatus.Failure
                        End If
                        args.ErrorType = BackupRestoreErrorType.PwdError
                        args.ErrorMsg = BackupHelper.GetErrorMessage(type, args.ErrorType)
                        RaiseEvent BackupHandler(Me, args)
                        Return
                    End If
                End If

                If isAloneBackup Then
                    blnSuccess = Me.mDevice.mb2_Restore(strBackupFolder, handler, strPwd, False)
                Else
                    blnSuccess = Me.mDevice.mb2_Restore(strBackupFolder, handler, strPwd, True)
                End If

            End If

            '已完成（展示成功的情况）
            If blnSuccess Then
                If type = BackupRestore.Backup Then
                    If Not blnNewBackup Then
                        Dim bakObject As BackupInfo = BackupHelper.CheckBackObjExist(args.FolderPath, 0)
                        If bakObject IsNot Nothing Then
                            BackupHelper.LstFullBackupList.Remove(bakObject)
                        End If
                    End If
                    '备份成功加入到全局
                    AddLstFullBackupList(strBackupFolder)
                End If

                args.Status = BackupStatus.Succeed
                RaiseEvent BackupHandler(Me, args)
                'Else
                '    args.ErrorMsg = Me.Language.GetString("Backup.Message.RestoreFailure")
                '    args.Status = BackupStatus.Failure
                '    RaiseEvent BackupHandler(Me, args)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "BackupHelper_StartBackupRestore")
        End Try
    End Sub

    Private Function CopyBackupFilesByIdentify(ByVal strBackupFolder As String, ByVal strSKU As String, ByRef strTempPath As String, Optional ByVal isMigration As Boolean = False, Optional ByVal isGetSMSBack As Boolean = False) As Boolean
        Dim isRelust As Boolean = False
        Try
            '需要先把整机备份 生成一份单独备份 然后在还原
            strTempPath = Path.Combine(Folder.TempFolder, "Backup\" & Path.GetFileName(strBackupFolder))
            Try
                If Directory.Exists(strTempPath) Then
                    Directory.Delete(strTempPath, True)
                End If
            Catch
            End Try
            Dim callback As BackupRestoreHandler
            If isMigration Then
                callback = New BackupRestoreHandler(AddressOf mb2_CopyBackupAloneFiles_Migration)
            Else
                callback = New BackupRestoreHandler(AddressOf mb2_CopyBackupAloneFiles)
            End If
            Dim bInfo As BackupInfo = New BackupInfo(strBackupFolder)
            Dim statueReslue As CopyAppBackupStatus = CopyAppBackupStatus.None
            If isGetSMSBack Then
                statueReslue = iPhoneDevice.mb2_CopyBackupFilesBySMS(strBackupFolder, strTempPath, callback)   '短信还原提取
            Else
                statueReslue = iPhoneDevice.mb2_CopyBackupFilesByIdentify(strBackupFolder, strTempPath, callback, ServerIniSetting.GetCopyAloneWeChatInterval(), bInfo.ProductVersion, strSKU)
            End If

            If statueReslue <> CopyAppBackupStatus.Succeed Then

                Dim strErrorMsg As String = ""
                Select Case statueReslue
                    Case CopyAppBackupStatus.CopyFileFaileue            '拷贝文件失败，重试= -1,
                        strErrorMsg = "准备数据失败 -1；拷贝文件失败"
                    Case CopyAppBackupStatus.CopyBackupPlistFailure      '拷贝Plist文件失败，重试= -2,
                        strErrorMsg = "准备数据失败 -2；Plist文件失败"
                    Case CopyAppBackupStatus.CopyBackupDBFailure        '拷贝数据库错误 = -3,
                        strErrorMsg = "准备数据失败 -3；拷贝数据库错误"
                    Case CopyAppBackupStatus.CreateBackupDBFailure      '删除多余的数据库记录= -4, 
                        strErrorMsg = "准备数据失败 -4；删除多余的数据库记录"
                    Case CopyAppBackupStatus.UninstalledWeChat        '没有安装 = -5,  
                        strErrorMsg = "准备数据失败 -5；没有安装"
                    Case CopyAppBackupStatus.BackupDBReadFailure         '数据文件无法读取= -6,
                        strErrorMsg = "准备数据失败 -6；数据文件无法读取"
                    Case CopyAppBackupStatus.WeixinNoInstall             '没有安装微信= -7 
                        strErrorMsg = "准备数据失败 -7；没有安装"
                End Select

                If isMigration Then
                    Dim args As New DataMigrationArgs()
                    args.FolderPath = Me.strDataMigrationPath
                    args.Status = DataMigrationStatus.Failure
                    args.Progress = 100
                    args.ErrorMsg = strErrorMsg
                    If args.ErrorMsg.Length > 0 Then
                        Common.LogException(args.ErrorMsg, "数据迁移 转换错误信息")
                    End If
                    RaiseEvent MigrationHandler(Me, args)
                Else
                    Dim argsA = New BackupArgs()
                    argsA.BackupType = Me.mBackupType
                    argsA.FolderPath = Me.StrCurrentBackupFolder
                    argsA.Progress = 100
                    argsA.IsConvert = True
                    argsA.Status = BackupStatus.Failure
                    argsA.ErrorMsg = strErrorMsg
                    RaiseEvent BackupHandler(Me, argsA)
                End If

            Else
                isRelust = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CopyBackupFilesByIdentify")
        End Try
        Return isRelust
    End Function

    Private Sub mb2_CopyBackupAloneFiles(ByVal e As BackupRestoreEventArgs)
        Dim args As New BackupArgs()
        args.BackupType = Me.mBackupType
        args.FolderPath = Me.StrCurrentBackupFolder
        args.Status = BackupStatus.BackupOrRestore
        args.Progress = e.Progress
        args.IsConvert = True
        RaiseEvent BackupHandler(Me, args)
    End Sub

    Private Sub mb2_CopyBackupAloneFiles_Migration(ByVal e As BackupRestoreEventArgs)
        Dim args As New DataMigrationArgs()
        args.FolderPath = Me.strDataMigrationPath
        args.Status = DataMigrationStatus.Convert
        args.Progress = e.Progress
        RaiseEvent MigrationHandler(Me, args)
    End Sub

    Private Delegate Function ShowPwdMessageboxHandler() As String
    Private Function ShowPwdMessagebox(ByRef isCancel As Boolean) As String
        Dim strPwd As String = String.Empty
        Dim args As New BackupPwdArgs
        RaiseEvent BackupPwdHandler(Me, args)
        strPwd = args.Password
        isCancel = args.IsCancel
        Return strPwd
    End Function

    Private Delegate Sub BackupRestoreProgressHandler(ByVal e As BackupRestoreEventArgs)
    Private Sub mb2_BackupHandler(ByVal e As BackupRestoreEventArgs)
        Dim args As New BackupArgs()
        args.BackupType = Me.mBackupType
        args.FolderPath = Me.StrCurrentBackupFolder
        args.Status = BackupStatus.BackupOrRestore
        args.Progress = e.Progress
        args.IsNewBackup = Me.mIsNewBackup
        args.IsOpenWeChat = Me.mIsAutoShowWeChat
        args.Cancel = e.Cancel

        '备份的时候空间不足。
        If args.BackupType = BackupRestore.Backup Then
            If Not e.Completed Then
                If Me.CheckDiskFull(1000, Me.StrCurrentBackupFolder) Then
                    args.ErrorType = BackupRestoreErrorType.DiskSpaceNotEnough
                End If
            End If
        End If

        If Not String.IsNullOrEmpty(e.ErrorMsg) Then
            args.Status = BackupStatus.Failure
            Dim type As BackupRestoreErrorType = GetErrotType(Me.mBackupType, e)
            args.ErrorType = type
        End If

        If args.Status = BackupStatus.Failure Then
            args.ErrorMsg = BackupHelper.GetErrorMessage(Me.mBackupType, args.ErrorType)
        End If

        '新增备份失败的时候自动删除备份文件
        If args.Status = BackupStatus.Failure AndAlso args.BackupType = BackupRestore.Backup AndAlso mIsNewBackup Then
            Me.DeleteFolder()
        End If

        RaiseEvent BackupHandler(Me, args)
    End Sub

    Public Shared Function GetErrorMessage(ByVal backType As BackupRestore, ByVal errortype As BackupRestoreErrorType) As String
        Dim Language As LanguageInterface = LanguageInterface.Instance()
        Dim strReturn As String = IIf(backType = BackupRestore.Backup, Language.GetString("App.Cell.BackupFail"), Language.GetString("Backup.Message.RestoreFailure"))
        Select Case errortype
            Case BackupRestoreErrorType.CloseBackupPwd
                strReturn &= String.Format("({0})", Language.GetString("Backup.Message.UnlockScreen"))                           '"如果您设置了密码锁定，请取消密码锁定"

            Case BackupRestoreErrorType.PwdError
                strReturn &= String.Format("({0})", Language.GetString("Backup.Message.PwdIsError"))                             '"密码错误。"

            Case BackupRestoreErrorType.FileError
                strReturn &= String.Format("({0})", Language.GetString("Backup.Message.FileError"))                              '"备份文件有问题。"

            Case BackupRestoreErrorType.CloseFindMyiPhone
                strReturn &= String.Format("({0})", Language.GetString("Backup.Message.CloseFindMyPhone"))                       '"请关闭iCloud中的""Find My iPhone"""

            Case BackupRestoreErrorType.DeviceSpaceNotEnough
                strReturn &= String.Format("({0})", Language.GetString("App.Cell.NotEnough"))                                    '"设备剩余空间不足"

            Case BackupRestoreErrorType.DiskSpaceNotEnough
                strReturn &= String.Format("({0})", Language.GetString("Common.Message.DiskSpaceNotEnough"))                      '"磁盘剩余空间不足"

            Case BackupRestoreErrorType.Other

        End Select
        Return strReturn
    End Function

    Public Shared Function GetErrotType(ByVal backOrRestore As BackupRestore, ByVal e As BackupRestoreEventArgs) As BackupRestoreErrorType
        Dim typeReturn As BackupRestoreErrorType = BackupRestoreErrorType.Other
        If e.ErrorMsg.Contains("keychain") OrElse e.ErrorMsg.Contains("Error reading backup properties") Then
            typeReturn = BackupRestoreErrorType.CloseBackupPwd

        ElseIf backOrRestore = BackupRestore.Restore AndAlso e.ErrorMsg.Contains("Invalid password when restoring encrypted backup") Then
            typeReturn = BackupRestoreErrorType.PwdError

        ElseIf backOrRestore = BackupRestore.Restore AndAlso e.ErrorMsg.Contains("a file record which is not a regular file") Then
            typeReturn = BackupRestoreErrorType.FileError

        ElseIf backOrRestore = BackupRestore.Restore AndAlso e.ErrorMsg.Contains("encrypted backup") Then
            typeReturn = BackupRestoreErrorType.FileError

        ElseIf backOrRestore = BackupRestore.Restore AndAlso e.ErrorMsg.Contains("Find My iPhone") Then
            typeReturn = BackupRestoreErrorType.CloseFindMyiPhone

        ElseIf backOrRestore = BackupRestore.Restore AndAlso e.ErrorMsg.Contains("Insufficient free disk space") Then
            typeReturn = BackupRestoreErrorType.DeviceSpaceNotEnough

        ElseIf backOrRestore = BackupRestore.Backup AndAlso e.ErrorMsg.Contains("磁盘空间不足") Then
            typeReturn = BackupRestoreErrorType.DiskSpaceNotEnough

        Else
            typeReturn = BackupRestoreErrorType.Other

        End If

        Return typeReturn
    End Function

#End Region

#Region "--- 数据迁移 同一程序 不同sku ---"
    Private mTDDataMigration As Threading.Thread
    Private strDataMigrationPath As String = ""
    Public Sub DataMigration(ByVal strSku As String, ByVal strNewSku As String, ByVal strFolderPath As String, ByVal isFullBack As Boolean)
        Try
            Try
                If mTDDataMigration IsNot Nothing AndAlso mTDDataMigration.ThreadState <> ThreadState.Stopped Then
                    Return
                End If
            Catch
            End Try

            Dim objPara As Object = New Object() {strSku, strNewSku, strFolderPath, isFullBack}
            Me.mTDDataMigration = New Thread(AddressOf DoDataMigration)
            Me.mTDDataMigration.SetApartmentState(ApartmentState.STA)
            Me.mTDDataMigration.IsBackground = True
            Me.mTDDataMigration.Start(objPara)

            Utility.WaitSeconds(0.5)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DataMigration")
        End Try
    End Sub

    Public Sub DoDataMigration(ByVal obj As Object)
        Try

            Dim strSku As String = obj(0)
            Dim strNewSku As String = obj(1)
            Dim strFolderPath As String = obj(2)
            Dim isFullBack As Boolean = obj(3)
            Me.strDataMigrationPath = strFolderPath
            Dim args As New DataMigrationArgs
            args.FolderPath = strFolderPath

            Dim strPwdForDecrypt As String = String.Empty

            Common.LogException("判断是否需要解密", "****WeChatSpare****")
            '1.判断是否需要解密
            If Not Me.ICheckIsEncrypted(strPwdForDecrypt, strFolderPath, args) Then
                Return
            End If

            Common.LogException("分析迁移的SKU", "****WeChatSpare****")
            '2.分析迁移的SKU
            If Not Me.GetSKU(strSku, strNewSku, strFolderPath, args) Then
                Return
            End If

            Common.LogException(String.Format("From:{0} To:{1}", strSku, strNewSku), "****WeChatSpare****")
            If strSku.Length = 0 AndAlso strNewSku.Length = 0 Then
                args.Status = DataMigrationStatus.Failure
                RaiseEvent MigrationHandler(Me, args)
                Return
            Else
                args.Status = DataMigrationStatus.Start
                RaiseEvent MigrationHandler(Me, args)
            End If

            '3.如果是整机备份 先把整机备份转成单独备份
            If isFullBack Then
                '需要先把整机备份 生成一份单独备份 然后在还原
                Dim strTempPath As String = ""
                args.Status = DataMigrationStatus.Convert
                RaiseEvent MigrationHandler(Me, args)
                Common.LogException("开始转换成单独备份", "****WeChatSpare****")
                If Not CopyBackupFilesByIdentify(strFolderPath, strSku, strTempPath, True) Then
                    args.ErrorMsg = "转换成单独备份失败"
                    args.Status = DataMigrationStatus.Failure
                    RaiseEvent MigrationHandler(Me, args)
                    Return
                End If
                strFolderPath = strTempPath
            End If
            Common.LogException("转换成单独备份成功", "****WeChatSpare****")

            args.Status = DataMigrationStatus.Modify
            RaiseEvent MigrationHandler(Me, args)

            '4.修改SKU 
            Dim strNewTempPath As String = Path.Combine(Folder.TempFolder, "Backup\Change\" & Path.GetFileName(strFolderPath))
            Try
                If Directory.Exists(strNewTempPath) Then
                    Directory.Delete(strNewTempPath, True)
                End If
            Catch
            End Try
            Dim callback = New DataMigrationHandler(AddressOf ChangeBackupIdentify)
            Dim isChange As Boolean = False

            Dim bInfo As New BackupInfo(strFolderPath)
            If Common.IOSStrVersionToIntVersion(bInfo.ProductVersion) >= 1000 Then
                Common.LogException("10.X", "****WeChatSpare****")
                isChange = iPhoneDevice.ChangeWeixinBackupIdentifyiOS10(strFolderPath, strNewTempPath, strSku, strNewSku, callback)
            Else
                Common.LogException("10.X以下", "****WeChatSpare****")
                isChange = iPhoneDevice.ChangeWeixinBackupIdentify(strFolderPath, strNewTempPath, strSku, strNewSku, callback)
            End If

            If Not isChange Then
                args.ErrorMsg = "修改SKU失败"
                args.Status = DataMigrationStatus.Failure
                RaiseEvent MigrationHandler(Me, args)
                Return
            End If
            Common.LogException("修改SKU成功", "****WeChatSpare****")

            strFolderPath = strNewTempPath
            args.Status = DataMigrationStatus.Restore
            RaiseEvent MigrationHandler(Me, args)

            '5.执行还原
            Dim strPwd As String = strPwdForDecrypt
            '密码错误
            If mbdb.CheckIsEncrypted(strFolderPath) Then
                Dim isCa As Boolean = False
                strPwd = Me.ShowPwdMessagebox(isCa)
                If String.IsNullOrEmpty(strPwd) Then
                    args.ErrorMsg = BackupHelper.GetErrorMessage(BackupRestore.Restore, BackupRestoreErrorType.PwdError)
                    args.Status = DataMigrationStatus.Failure
                    RaiseEvent MigrationHandler(Me, args)
                    Return
                End If
            End If
            Dim handler As New BackupRestoreHandler(AddressOf mb2_BackupHandlerEx)
            Dim blnSuccess As Boolean = Me.mDevice.mb2_Restore(strFolderPath, handler, strPwd, False)
            If blnSuccess Then
                args.Status = DataMigrationStatus.Succeed
                RaiseEvent MigrationHandler(Me, args)
            Else
                args.ErrorMsg = "还原失败"
                args.Status = DataMigrationStatus.Failure
                RaiseEvent MigrationHandler(Me, args)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoDataMigration")
        End Try
    End Sub

    Private Function ICheckIsEncrypted(ByRef strPwdForDecrypt As String, ByRef strFolderPath As String, ByRef args As DataMigrationArgs) As Boolean
        Dim isRelust As Boolean = True
        Try
            If mbdb.CheckIsEncrypted(strFolderPath) Then
DO_CONTINUE:
                If tbInputBox.Show(Me, Me.Language.GetString("Backup.Message.TypeInBackupPwd"), _
                                        Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, _
                                        MessageBoxDefaultButton.Button1, strPwdForDecrypt, True, My.Resources.backup_password, Me.Language.GetString("Backup.Message.iTunesTutorial"), "http://news.tongbu.com/92502.html") = Windows.Forms.DialogResult.OK Then ' "iTunes备份密码相关教程"’
                    '密码不能为空
                    If String.IsNullOrEmpty(strPwdForDecrypt) Then
                        GoTo DO_CONTINUE
                    End If
                    If Not LoadRecords(strFolderPath, "", strPwdForDecrypt) Then
                        args.Status = DataMigrationStatus.Failure
                        RaiseEvent MigrationHandler(Me, args)
                        isRelust = False
                        GoTo Do_Exit
                    Else
                        strFolderPath = Path.Combine(strFolderPath, iPhoneDevice.Unback_Dir)
                        If Not Directory.Exists(strFolderPath) Then
                            args.Status = DataMigrationStatus.Failure
                            RaiseEvent MigrationHandler(Me, args)
                            isRelust = False
                            GoTo Do_Exit
                        End If
                    End If
                Else
                    args.Status = DataMigrationStatus.Failure
                    RaiseEvent MigrationHandler(Me, args)
                    isRelust = False
                    GoTo Do_Exit
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ICheckIsEncrypted")
        End Try
Do_Exit:
        Return isRelust
    End Function

    Public Shared Function GetSkuByDevice(ByVal dev As iPhoneDevice, ByVal ssState As ScreenSkuState, Optional ByRef strSku As String = "") As Dictionary(Of String, String)
        Dim dictSku As New Dictionary(Of String, String)
        Try
            '1.获取设备里面已安装的同步分身版
            Dim dict As Dictionary(Of String, FileSharingPackageInfo) = dev.InstalledApplications(ApplicationType.User, False)
            If dict.Count > 0 Then
                For Each item As FileSharingPackageInfo In dict.Values
                    If item.Identifier.StartsWith(BackupHelper.StrSpareSku) AndAlso Not dictSku.ContainsKey(item.Identifier) AndAlso BackupHelper.ScreenSku(item.Identifier, ssState) Then
                        dictSku.Add(item.Identifier, item.DisplayName)
                        strSku = String.Format("{0},{1}", strSku, item.Identifier)
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSkuByDevice")
        End Try
        Return dictSku
    End Function

    Public Shared Function GetSkuByBackup(ByVal strFolderPath As String, ByVal ssState As ScreenSkuState, Optional ByRef strSku As String = "") As Dictionary(Of String, String)
        Dim dictSku As New Dictionary(Of String, String)
        Try
            '2.获取备份文件里面的同步分身版
            Dim baInfo As New BackupInfo(strFolderPath)
            If Common.IOSStrVersionToIntVersion(baInfo.ProductVersion) >= 1000 Then
                Dim conn As System.Data.SQLite3.SQLiteConnection = SQLiteClass3.CreateConnectionFromFile(Path.Combine(strFolderPath, "Manifest.db"))
                Dim sql As String = "SELECT DISTINCT(domain) FROM Files;"
                Dim dt As DataTable = SQLiteClass3.ExecuteSQL(sql, conn)
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    For Each row As DataRow In dt.Rows
                        Dim strValue As String = row(0).ToString().Replace("AppDomain-", "")
                        If strValue.StartsWith(BackupHelper.StrSpareSku) AndAlso Not dictSku.ContainsKey(strValue) AndAlso BackupHelper.ScreenSku(strValue, ssState) Then
                            dictSku.Add(strValue, strValue)
                            strSku = String.Format("{0},{1}", strValue, strSku)
                        End If
                    Next
                End If
            Else
                Dim lstFileRecord As List(Of MBFileRecord) = mbdb.Reload(strFolderPath) 'Path.Combine(strFolderPath, "Manifest.mbdb")
                If lstFileRecord.Count > 0 Then
                    For Each item As MBFileRecord In lstFileRecord
                        Dim strValue As String = item.Domain.Replace("AppDomain-", "")

                        If strValue.Contains(StrSpareSku) AndAlso Not dictSku.ContainsKey(strValue) AndAlso BackupHelper.ScreenSku(strValue, ssState) Then
                            dictSku.Add(strValue, strValue)
                            strSku = String.Format("{0},{1}", strValue, strSku)
                        End If
                    Next
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSkuByBackup")
        End Try
        Return dictSku
    End Function

    Private Function GetSKU(ByRef strSku As String, ByRef strNewSku As String, ByRef strFolderPath As String, ByRef args As DataMigrationArgs) As Boolean
        Dim isRelust As Boolean = True
        Try
            If strSku.Length = 0 AndAlso strNewSku.Length = 0 Then
                Dim intProgress As Integer = 0

                args.Progress = intProgress
                args.Status = DataMigrationStatus.DetectionSku
                RaiseEvent MigrationHandler(Me, args)

                '1.获取设备里面已安装的同步分身版
                Dim strToSku As String = ""
                Dim dictToSku As Dictionary(Of String, String) = GetSkuByDevice(Me.mDevice, ScreenSkuState.UnForm, strToSku)

                args.Progress = 50
                args.Status = DataMigrationStatus.DetectionSku
                RaiseEvent MigrationHandler(Me, args)

                '2.获取备份文件里面的同步分身版
                Dim strFormSku As String = ""
                Dim dictFormSku As Dictionary(Of String, String) = GetSkuByBackup(strFolderPath, ScreenSkuState.Form, strFormSku)

                If IniSetting.GetIsMigrationWeChatSpare Then
                    Common.LogException("从ini配置里面获取sku", "****WeChatSpare****")
                    '从ini配置里面获取sku 
                    strSku = IniSetting.GetMigrationWeChatSpareFromSKU()
                    strNewSku = IniSetting.GetMigrationWeChatSpareToSKU()
                    If Not dictFormSku.ContainsKey(strSku) OrElse Not dictToSku.ContainsKey(strNewSku) Then
                        args.Status = DataMigrationStatus.Failure
                        RaiseEvent MigrationHandler(Me, args)
                        isRelust = False
                        GoTo Do_Exit
                    End If
                Else
                    If dictToSku.Count > 1 OrElse dictFormSku.Count > 1 Then
                        Common.LogException(String.Format("SKU 多对多,From:{0} To:{1}", strFormSku, strToSku), "****WeChatSpare****")
                        args.Status = DataMigrationStatus.FailureSku
                        RaiseEvent MigrationHandler(Me, args)
                        isRelust = False
                        GoTo Do_Exit
                    End If
                    Common.LogException("SKU 一对一", "****WeChatSpare****")
                    strSku = dictFormSku(0)
                    strNewSku = dictToSku(0)
                End If

                args.Progress = 100
                args.Status = DataMigrationStatus.DetectionSku
                RaiseEvent MigrationHandler(Me, args)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSKU")
        End Try
Do_Exit:
        Return isRelust
    End Function

    Private Sub ChangeBackupIdentify(ByVal e As DataMigrationArgs)
        Dim args As New DataMigrationArgs()
        args.FolderPath = Me.strDataMigrationPath
        args.Status = DataMigrationStatus.Modify
        args.Progress = e.Progress

        If args.ErrorMsg.Length > 0 Then
            Common.LogException(args.ErrorMsg, "数据迁移 修改SKU错误信息")
        End If

        RaiseEvent MigrationHandler(Me, args)
    End Sub

    Private Sub mb2_BackupHandlerEx(ByVal e As BackupRestoreEventArgs)
        Dim args As New DataMigrationArgs()
        args.FolderPath = Me.strDataMigrationPath
        args.Status = DataMigrationStatus.Restore
        args.Progress = e.Progress
        args.ErrorMsg = e.ErrorMsg

        If args.ErrorMsg.Length > 0 Then
            Common.LogException(args.ErrorMsg, "数据迁移 备份错误信息")
        End If

        RaiseEvent MigrationHandler(Me, args)
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function CheckBackUpSucceed(ByVal strBackFolder As String) As Boolean
        Dim blnReturn As Boolean = True
        If Not File.Exists(Path.Combine(strBackFolder, "Info.Plist")) OrElse _
           (Not File.Exists(Path.Combine(strBackFolder, "Manifest.mbdb")) AndAlso Not File.Exists(Path.Combine(strBackFolder, "Manifest.db"))) OrElse _
           Not File.Exists(Path.Combine(strBackFolder, "Manifest.plist")) OrElse _
           Not File.Exists(Path.Combine(strBackFolder, "Status.plist")) Then
            blnReturn = False
        End If
        Return blnReturn
    End Function

    Private Function CheckDiskFull(ByRef intRemainSpace As Long, ByVal strFolderPath As String) As Boolean
        Dim isFull As Boolean = False
        Try
            Dim info As New DirectoryInfo(strFolderPath)
            Dim divInfo As New DriveInfo(info.Root.ToString)
            If divInfo.TotalFreeSpace < intRemainSpace Then
                isFull = True
                intRemainSpace = divInfo.TotalFreeSpace
            End If
        Catch ex As Exception
            Common.Log(ex.ToString)
        End Try

        Return isFull
    End Function

    Public Function CheckCanClose() As CloseStatus
        If Me.mThrBackupRestore IsNot Nothing AndAlso Me.mThrBackupRestore.ThreadState <> Threading.ThreadState.Stopped Then
            Return CloseStatus.UnClose
        End If
        Return CloseStatus.Close
    End Function

    Public Sub CancelBackup()
        Me.Cancel()
        Try
            '新增备份失败的时候自动删除备份文件
            If Me.mBackupType = BackupRestore.Backup AndAlso mIsNewBackup Then
                Me.DeleteFolder()
            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub CancelRestore()
        Me.Cancel()
        Dim args As New BackupArgs()
        args.BackupType = Me.mBackupType
        args.FolderPath = Me.StrCurrentBackupFolder
        args.Status = BackupStatus.None
        args.IsNewBackup = Me.mIsNewBackup
        args.IsOpenWeChat = Me.mIsAutoShowWeChat
        RaiseEvent BackupHandler(Me, args)
    End Sub

    Private Sub DeleteFolder()
        Try
            '删除所有文件和文件夹。
            For Each strFolder As String In Directory.GetDirectories(Me.StrCurrentBackupFolder, "*", SearchOption.AllDirectories)
                For Each strFile As String In Directory.GetFiles(strFolder)
                    File.Delete(strFile)
                    Application.DoEvents()
                Next
                Try
                    Directory.Delete(strFolder)
                Catch ex As Exception
                End Try
                Application.DoEvents()
            Next

            For Each strFile As String In Directory.GetFiles(Me.StrCurrentBackupFolder)
                File.Delete(strFile)
                Application.DoEvents()
            Next
            Try
                Directory.Delete(Me.StrCurrentBackupFolder)
            Catch ex As Exception
            End Try
        Catch ex As Exception
            Common.Log(ex.ToString)
        End Try
    End Sub

    Public Sub Cancel()
        Try
            If Me.mThrBackupRestore IsNot Nothing AndAlso Me.mThrBackupRestore.ThreadState <> Threading.ThreadState.Stopped Then
                Me.mThrBackupRestore.Abort()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "CancelBackup")
        End Try

        Try
            If Me.mTDDataMigration IsNot Nothing AndAlso Me.mTDDataMigration.ThreadState <> Threading.ThreadState.Stopped Then
                Me.mTDDataMigration.Abort()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "CancelDataMigration")
        End Try


        Try
            If Me.mDevice IsNot Nothing Then
                Me.mDevice.mb2_SvrClose()
            End If

            mDictInstances.Clear()
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 获取所以整机备份的数据 ---"
    Public Sub GetFullBackupList(Optional ByVal isRefresh As Boolean = False)
        Try
            If Me.mTDFullBackupList IsNot Nothing AndAlso Me.mTDFullBackupList.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mTDFullBackupList.Abort()
                Catch
                End Try
            End If

            Me.mTDFullBackupList = New Thread(New ParameterizedThreadStart(AddressOf DoGetFullBackupList))
            IsLoadFullBackupList = False
            With Me.mTDFullBackupList
                .IsBackground = True
                .SetApartmentState(ApartmentState.STA)
                .Start(isRefresh)
            End With
            Utility.WaitSeconds(0.5)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetFullBackupList")
        End Try
    End Sub

    Private Sub DoGetFullBackupList(ByVal obj As Object)
        Try
            Dim isRefresh As Boolean = CType(obj, Boolean)
            If isRefresh Then
                LstFullBackupList = New List(Of BackupInfo)
            End If

            If LstFullBackupList IsNot Nothing AndAlso LstFullBackupList.Count > 0 Then
                IsLoadFullBackupList = True
                Return
            End If

            Try
                '加载ITUNES备份目录里面的文件
                If Directory.Exists(BackupHelper.BackupFolder) Then
                    Dim arrDir As String() = Directory.GetDirectories(BackupHelper.BackupFolder)
                    If arrDir.Length > 0 Then
                        For Each Item As String In arrDir
                            AddLstFullBackupList(Item)
                        Next
                    End If

                    '加载xml文件里面的记录
                    Dim lstXML As List(Of String) = XmlHelper.LoadBackUpListFromXML()
                    If lstXML IsNot Nothing AndAlso lstXML.Count > 0 Then
                        For Each Item As String In lstXML
                            AddLstFullBackupList(Item)
                        Next
                    End If
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "GetBackup2AloneBackup_LoadDirectory")
            End Try

            LstQuickBackupList.Clear()
            Try
                '获取单独微信备份 LstQuickBackupList
                If Directory.Exists(Folder.AloneWeChatBackupsFolder) Then
                    Dim arrDir As String() = Directory.GetDirectories(Folder.AloneWeChatBackupsFolder)
                    Dim info As BackupInfo = Nothing
                    For Each item As String In arrDir
                        info = New BackupInfo(item)
                        If Me.CheckBackEnable(info) AndAlso Not LstQuickBackupList.Contains(info) Then
                            LstQuickBackupList.Add(info)
                        End If
                    Next
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "GetBackup2AloneBackup_QuickBackupList")
            End Try

            Try
                '获取单独 同步微信分身备份 LstQuickBackupList
                If Directory.Exists(Folder.AloneWeChatSpareBackupsFolder) Then
                    Dim arrDir As String() = Directory.GetDirectories(Folder.AloneWeChatSpareBackupsFolder)
                    Dim info As BackupInfo = Nothing
                    For Each item As String In arrDir
                        info = New BackupInfo(item)
                        info.IntType = 1
                        If Me.CheckBackEnable(info) AndAlso Not LstQuickBackupList.Contains(info) Then
                            LstQuickBackupList.Add(info)
                        End If
                    Next
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "GetBackup2AloneBackup_QuickBackupList")
            End Try

            Try
                '获取单独 VIP微信分身备份 LstQuickBackupList
                If Directory.Exists(Folder.AloneWeChatVIPSpareBackupsFolder) Then
                    Dim arrDir As String() = Directory.GetDirectories(Folder.AloneWeChatVIPSpareBackupsFolder)
                    Dim info As BackupInfo = Nothing
                    For Each item As String In arrDir
                        info = New BackupInfo(item)
                        info.IntType = 2
                        If Me.CheckBackEnable(info) AndAlso Not LstQuickBackupList.Contains(info) Then
                            LstQuickBackupList.Add(info)
                        End If
                    Next
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "GetBackup2AloneBackup_QuickBackupList")
            End Try

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoGetFullBackupList")
        End Try
        IsLoadFullBackupList = True

        Try
            If LstFullBackupList IsNot Nothing Then
                For Each info As BackupInfo In LstFullBackupList
                    Try
                        GetFileSize(info.FolderPath, True)
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "DoGetFullBackupList_GetFileSize1")
                    End Try
                Next
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoGetFullBackupList_GetFileSize")
        End Try

        Try
            '删除 解密的备份垃圾文件
            Dim arrDelete As String() = IniClass.GetIniSectionAllKey("MobileSyncBackup_Delete", Folder.MobileSyncBackupIniFile)
            If arrDelete.Length > 0 Then
                For Each strPath As String In arrDelete
                    Try
                        If Directory.Exists(strPath) Then
                            Directory.Delete(strPath, True)
                        End If
                    Catch
                    End Try
                    IniClass.DeleteSectionKey("MobileSyncBackup_Delete", strPath, Folder.MobileSyncBackupIniFile)
                Next
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoGetFullBackupList_Delete")
        End Try
    End Sub

    Public Function AddLstFullBackupList(ByVal strPath As String, Optional ByRef intIndex As Integer = 0, Optional ByRef isExist As Boolean = False) As BackupInfo
        Dim bakObject As BackupInfo = CheckBackObjExist(strPath, intIndex)
        Try
            If bakObject Is Nothing Then
                bakObject = New BackupInfo(strPath)
            Else
                isExist = True
                GoTo Do_Exit
            End If

            If LstFullBackupList.Contains(bakObject) Then
                isExist = True
                GoTo Do_Exit
            End If
            If Not CheckBackEnable(bakObject) Then
                isExist = True
                GoTo Do_Exit
            End If

            If LstFullBackupList.Count > 0 Then
                For Each Item As BackupInfo In LstFullBackupList
                    If Item.BackupDate <= bakObject.BackupDate Then
                        Continue For
                    End If
                    intIndex += 1
                Next
            End If
            LstFullBackupList.Insert(intIndex, bakObject)
            'Common.LogException(bakObject.FolderPath, "AddLstFullBackupList—Add")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddLstFullBackupList")
        End Try
Do_Exit:
        Return bakObject
    End Function

    Public Function CheckBackEnable(ByVal obj As BackupInfo) As Boolean
        Dim blnReturn As Boolean = True
        Try
            If obj.BuildVersion.Length = 0 AndAlso obj.DeviceName.Length = 0 AndAlso obj.DisplayName.Length = 0 AndAlso obj.ProductType.Length = 0 AndAlso obj.ProductVersion.Length = 0 AndAlso obj.SerialNumber.Length = 0 Then
                blnReturn = False
            End If
        Catch ex As Exception
            blnReturn = False
            Common.LogException(ex.ToString(), "CheckBackEnable")
        End Try
        Return blnReturn
    End Function

    Private Function GetInserIndex(ByVal strPath As String, ByVal listTemp As List(Of String)) As Integer
        Dim intIndex As Integer = listTemp.Count
        Try
            If listTemp.Count > 0 Then
                intIndex = 0
                For Each Item As String In listTemp

                    If Not File.Exists(Path.Combine(Item, "Status.Plist")) OrElse Not File.Exists(Path.Combine(strPath, "Status.Plist")) Then
                        Continue For
                    End If
                    If New FileInfo(Path.Combine(Item, "Status.Plist")).LastWriteTime <= New FileInfo(Path.Combine(strPath, "Status.Plist")).LastWriteTime Then
                        Exit For
                    End If
                    intIndex += 1
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "GetIndexInList")
        End Try
        Return intIndex
    End Function

    Public Shared Function CheckBackObjExist(ByVal strPath As String, ByRef intIndex As Integer) As BackupInfo
        Dim obj As BackupInfo = Nothing
        If LstFullBackupList Is Nothing OrElse LstFullBackupList.Count <= 0 Then
            Return obj
        End If
        intIndex = 0
        For Each Item As BackupInfo In LstFullBackupList
            If Item.FolderPath = strPath Then
                obj = Item
                Exit For
            End If
            intIndex += 1
        Next
        If obj Is Nothing Then
            intIndex = 0
        End If
        Return obj
    End Function

    Public Shared Function GetBackUpFolder(ByVal isNewBackup As Boolean, ByVal strIdentifier As String) As String
        Dim strBackupFolder As String = ""
        Try
            If Not isNewBackup Then
                Dim arrDirectoryInfo As DirectoryInfo() = New DirectoryInfo(BackupHelper.BackupFolder).GetDirectories()
                If arrDirectoryInfo.Length > 0 Then
                    Dim dtTime As DateTime = DateTime.MinValue
                    For Each item As DirectoryInfo In arrDirectoryInfo
                        If item.Name.Contains(strIdentifier) Then
                            Dim strStatusPath As String = Path.Combine(item.FullName, "Status.Plist")
                            If Not File.Exists(strStatusPath) Then
                                Continue For
                            End If
                            If DateTime.Compare(dtTime, New FileInfo(strStatusPath).LastWriteTime) > 0 Then
                                Continue For
                            End If
                            dtTime = New FileInfo(strStatusPath).LastWriteTime
                            strBackupFolder = item.FullName
                        End If
                    Next
                End If
                If strBackupFolder.Length = 0 Then
                    strBackupFolder = Path.Combine(BackupHelper.BackupFolder, strIdentifier)
                End If
            Else
                Dim strTempFolder As String = Path.Combine(BackupHelper.BackupFolder, strIdentifier)
                Dim intIndex As Integer = 0
                While True
                    If Directory.Exists(strTempFolder) Then
                        intIndex += 1
                        strTempFolder = Path.Combine(BackupHelper.BackupFolder, strIdentifier & String.Format("({0})", intIndex))
                    Else
                        Exit While
                    End If
                End While
                strBackupFolder = strTempFolder
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetBackUpFolder")
        End Try
        Return strBackupFolder
    End Function

#End Region

    Public Shared Function ExistBackup(ByVal strIdentifier As String) As Boolean
        Dim isRelust As Boolean = False
        Try
            If LstFullBackupList.Count > 0 Then
                For Each info As BackupInfo In LstFullBackupList
                    If info.Identifier = strIdentifier Then
                        isRelust = True
                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExistBackup")
        End Try
        Return isRelust
    End Function

    Public Function LoadRecords(ByVal strFolderPath As String, ByVal strIdentifier As String, ByVal strPwdForDecrypt As String) As Boolean
        Dim blnReturn As Boolean = True
        Try
            If Not Me.Decrypt(strFolderPath, strIdentifier, strPwdForDecrypt) Then
                Dim args As New BackupDecipherArgs
                args.strPath = strFolderPath
                args.strMsg = Me.Language.GetString("Backup.Message.DecryptedFailure")       '"解密失败！"
                args.strIdentifier = IIf(Me.mDevice Is Nothing, "", Me.mDevice.Identifier)
                args.isDecryptedFailure = True
                RaiseEvent BackupDecipherHandler(Me, args)
                blnReturn = False
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadRecords")
        End Try
        Return blnReturn
    End Function

    '如果需要解密，则解密
    Public Function Decrypt(ByVal strFolderPath As String, ByVal strIdentifier As String, ByVal strPwdForDecrypt As String) As Boolean
        Dim blnReturn As Boolean = True

        Try
            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then

                Dim args As New BackupDecipherArgs
                args.strPath = strFolderPath
                args.strMsg = Me.Language.GetString("Backup.Message.Decrypteding")           '"正在解密..."
                args.strIdentifier = IIf(Me.mDevice Is Nothing, "", Me.mDevice.Identifier)
                RaiseEvent BackupDecipherHandler(Me, args)

                If Not iPhoneDevice.mb2_Unback_New(strFolderPath, strPwdForDecrypt, New BackupRestoreHandler(AddressOf tmpDevice_BackupRestoreHandler)) Then
                    blnReturn = False
                End If

                args.intProgress = 100
                args.strPath = strFolderPath
                args.isFinish = True
                args.strIdentifier = IIf(Me.mDevice Is Nothing, "", Me.mDevice.Identifier)
                RaiseEvent BackupDecipherHandler(Me, args)

                'If Not Me.mDevice.mb2_Unback(strFolderPath, strIdentifier, strPwdForDecrypt, New BackupRestoreHandler(AddressOf tmpDevice_BackupRestoreHandler)) Then
                '    blnReturn = False
                'End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Decrypted")
        End Try

        Return blnReturn
    End Function

    Public Sub tmpDevice_BackupRestoreHandler(ByVal args As BackupRestoreEventArgs)
        Dim intProgreaa As Integer = args.Progress
        If intProgreaa <= 0 Then
            Return
        End If
        Dim argsD As New BackupDecipherArgs
        argsD.intProgress = intProgreaa
        argsD.strPath = args.StrPath
        argsD.strMsg = Me.Language.GetString("Backup.Message.Decrypteding")          '"正在解密..."
        argsD.strIdentifier = IIf(Me.mDevice Is Nothing, "", Me.mDevice.Identifier)
        RaiseEvent BackupDecipherHandler(Me, argsD)
    End Sub


    Public Shared Function GetFileSize(ByVal strFilePath As String, isReflesh As Boolean) As Long
        Dim result As ULong = 0

        Try
            If Not isReflesh Then
                'Dim arrKey As String() = IniClass.GetIniSectionAllKey("MobileSyncBackup", Folder.MobileSyncBackupIniFile)
                'If arrKey.Length > 0 Then
                '    Dim strValue As String = IniClass.GetIniSectionKey("MobileSyncBackup", strFilePath.ToLower(), Folder.MobileSyncBackupIniFile)
                '    Long.TryParse(strValue, result)
                '    If result > 0 Then
                '        GoTo Do_Exit
                '    End If
                'End If

                Dim strValue As String = IniClass.GetIniSectionKey("MobileSyncBackup", strFilePath.ToLower(), Folder.MobileSyncBackupIniFile)
                ULong.TryParse(strValue, result)
                If result > 0 Then
                    GoTo Do_Exit
                End If
            End If
        Catch ex As Exception
            Common.LogException("strFilePath:" & strFilePath & ex.ToString(), "GetFileSize1")
        End Try

        Try
            'ios10的备份文件是放在文件夹里面
            For Each Item As String In Directory.GetFiles(strFilePath, "*.*", SearchOption.AllDirectories)
                Try
                    If Item.Contains(iPhoneDevice.Unback_Dir) Then
                        Continue For
                    End If
                    If File.Exists(Item) Then
                        Dim info As New FileInfo(Item)
                        If info IsNot Nothing Then
                            result += info.Length
                        End If
                    End If
                Catch
                End Try
            Next
            IniClass.SetIniSectionKey("MobileSyncBackup", strFilePath.ToLower(), result, Folder.MobileSyncBackupIniFile)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetFileSize2")
        End Try

Do_Exit:
        Return result
    End Function

    Public Shared Function ScreenSku(ByVal strSku As String, ByVal sFormState As ScreenSkuState) As Boolean
        Dim blnRelust As Boolean = False
        Try
            Select Case sFormState
                Case ScreenSkuState.All
                    blnRelust = True
                    GoTo Do_Exit

                Case ScreenSkuState.Form
                    If strSku = BackupHelper.StrSpareSku OrElse strSku = BackupHelper.StrSpareSku & "1" OrElse strSku = BackupHelper.StrSpareSku & "2" Then
                        blnRelust = True
                        GoTo Do_Exit
                    End If

                Case ScreenSkuState.UnForm
                    If strSku <> BackupHelper.StrSpareSku AndAlso strSku <> BackupHelper.StrSpareSku & "1" AndAlso strSku <> BackupHelper.StrSpareSku & "2" Then
                        blnRelust = True
                        GoTo Do_Exit
                    End If

            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ScreenSku")
        End Try
Do_Exit:
        Return blnRelust
    End Function

End Class

Public Enum ScreenSkuState
    All
    Form
    UnForm
End Enum

Public Enum BackupRestore
    Backup
    Restore
End Enum

Public Enum BackupRestoreErrorType
    None
    CloseBackupPwd
    CloseFindMyiPhone
    PwdError
    FileError
    DiskSpaceNotEnough              'PC空间
    DeviceSpaceNotEnough            '设备空间
    Other
    ConvertBackup                   '整机备份转换为单独备份
End Enum

Public Enum BackupStatus
    None
    BackupOrRestore
    Succeed
    Failure
    Cancel
End Enum

Public Class BackupArgs
    Inherits EventArgs

    Public BackupType As BackupRestore = BackupRestore.Backup
    Public Status As BackupStatus = BackupStatus.None
    Public FolderPath As String = String.Empty
    Public Progress As Integer = 0
    Public Cancel As Boolean = False
    Public ErrorType As BackupRestoreErrorType = BackupRestoreErrorType.None
    Public ErrorMsg As String = String.Empty
    Public IsNewBackup As Boolean = False
    Public IsOpenWeChat As Boolean = False
    Public IsConvert As Boolean = False

End Class

Public Class BackupPwdArgs
    Inherits EventArgs
    Public Password As String = String.Empty
    Public IsCancel As Boolean = False
End Class

Public Class BackupDecipherArgs
    Inherits EventArgs
    Public strMsg As String = String.Empty
    Public strIdentifier As String = ""
    Public isDecryptedFailure As Boolean = False
    Public strPath As String = ""
    Public intProgress As Integer = 0
    Public isFinish As Boolean = False
End Class