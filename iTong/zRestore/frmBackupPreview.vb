﻿Imports System.Threading
Imports iTong.CoreModule

Public Class frmBackupPreview

    Private Enum BackupNodeType
        Normal
        Search
    End Enum

    Protected mDataObject As DataObject = Nothing
    Private mBackObject As BackupInfo
    Private mLstMBFileRecord As List(Of MBFileRecord)                       '数据源

    Private mListView As tbListView = Nothing
    Private mListViewSearch As tbListView = Nothing
    Private mSearchPath As String = ""
    Private mCurrentNodeType As BackupNodeType = BackupNodeType.Normal
    Private mCopyFiles As New List(Of String)
    Private mIsNeedDecrypt As Boolean = False
    Private mPwdForDecrypt As String = String.Empty
    Private mDeviceForDecrypt As iPhoneDevice = Nothing
    Private mtdForDecrypt As Thread = Nothing
    Private mstrBackupPath As String = ""

    Private Const PathDCIM As String = "/var/mobile/Media/DCIM/"
    Private Const PathWeixin As String = "/var/mobile/Applications/com.tencent.xin/"
    Private Const PathWeixinDB As String = "/var/mobile/Applications/com.tencent.xin/Weixin.db"
    Private Const PathSMS As String = "/var/mobile/Library/SMS/"
    Private Const PathSMSDB As String = "/var/mobile/Library/SMS/sms.db"
    Private Const PathAddressBook As String = "/var/mobile/Library/AddressBook/"
    Private Const PathAddressBookDB As String = "/var/mobile/Library/AddressBook/AddressBook.sqlitedb"
    Private Const PathAddressBookImagesDB As String = "/var/mobile/Library/AddressBook/AddressBookImages.sqlitedb"
    Private Const PathNote As String = "/var/mobile/Library/Notes/"
    Private Const PathNoteDB As String = "/var/mobile/Library/Notes/notes.sqlite"
    Private Const PathNoteIDX As String = "/var/mobile/Library/Notes/notes.idx"
    Private Const PathQQ As String = "/var/mobile/Applications/com.tencent.mqq/"
    Private Const PathQQDB As String = "/var/mobile/Applications/com.tencent.mqq/QQ.db"
    Private Const PathWhatsApp As String = "/var/mobile/Applications/net.whatsapp.WhatsApp/"
    Private Const PathWhatsAppDB As String = "/var/mobile/Applications/net.whatsapp.WhatsApp/WhatsApp.db"

    Private PathCallHistory As String = "/var/wireless/Library/CallHistory/"
    Private PathCallHistoryDB As String = "/var/wireless/Library/CallHistory/call_history.db"

    Private mFolderPathUnbackDir As String = ""
    Private StrEncryptedBackup As String = ""

#Region "--- 初始化 ---"

    Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。


    End Sub

    Public Sub New(ByVal application As IApplication, ByVal backupObj As BackupInfo, ByVal isNeedDecrypt As Boolean, _
                             ByVal strPwd As String, ByVal device As iPhoneDevice, Optional ByVal strBackupPath As String = "")
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.Icon = My.Resources.iTong
        Me.tbAutoSetFormSize = True
        Me.Size = New Size(840, 600)
        Me.CanResize = True
        Me.FilletRadius = 5
        Me.mBackObject = backupObj
        Me.mFolderPathUnbackDir = Me.mBackObject.FolderPath
        Me.mIsNeedDecrypt = isNeedDecrypt
        Me.mPwdForDecrypt = strPwd
        Me.mDeviceForDecrypt = device
        Me.mstrBackupPath = strBackupPath
        If backupObj.VersionNumber >= 800 Then
            PathCallHistory = "/var/mobile/Library/CallHistoryDB/"
            PathCallHistoryDB = "/var/mobile/Library/CallHistoryDB/CallHistory.storedata"
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.mBackObject.DeviceName
        Me.btnExport.Text = Me.Language.GetString("File.Button.DownloadToPC")                           '"导出"
        Me.btnRefresh.Text = Me.Language.GetString("File.Button.Reload")                           '"刷新"
        Me.tsmiCopyPath.Text = Me.Language.GetString("File.Menu.CopyPath")                              '"复制路径"
        Me.tsmiExport.Text = Me.Language.GetString("File.Button.DownloadToPC")                          '"导出"
        Me.tsmiOpen.Text = Me.Language.GetString("Common.Button.Open")                                  '"打开"
        Me.txtSearchFile.SearchTipText = Me.Language.GetString("File.Label.Search")                     '"查找"
        Me.btnView.Text = Me.Language.GetString("Photo.Menu.View") '查看
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete") '删除
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        'Try
        '    '设置父窗体隐藏
        '    If Me.Owner IsNot Nothing Then
        '        Me.Owner.Visible = False
        '    End If
        'Catch
        'End Try

        '初始化状态栏和loading
        Me.ShowProgressStatus(False)
        Me.SetLoadding(True, Me.Language.GetString("App.Cell.Loading"))

        '初始化ListView
        Me.mListView = Me.CreateListView(Me.imlSmall, Me.imlLarge)
        Me.pnlContainer.Controls.Add(Me.mListView)

        Me.mListViewSearch = Me.CreateListView(Me.imlSmall, Me.imlLarge)
        Me.pnlContainer.Controls.Add(Me.mListViewSearch)

        '加载数据
        Me.mtdForDecrypt = New Thread(AddressOf LoadRecords)

        With Me.mtdForDecrypt
            .IsBackground = True
            .Start()
        End With

        MainForm.SetButtonStatus(Me.btnExport)
        MainForm.SetButtonStatus(Me.btnView)

        Me.btnViewLargeIcon.Width = My.Resources.btn_viewlist_checked.Width
        Me.btnViewList.Width = My.Resources.btn_viewlist_checked.Width
        Me.btnViewLargeIcon.Location = New Point(Me.txtSearchFile.Left - Me.btnViewLargeIcon.Width - 10, Me.btnViewLargeIcon.Top)
        Me.btnViewList.Location = New Point(Me.btnViewLargeIcon.Left - Me.btnViewList.Width, Me.btnViewList.Top)
        Me.btnRefresh.Location = New Point(Me.btnViewList.Left - Me.btnRefresh.Width - 15, Me.btnRefresh.Top)

        If IniSetting.GetFileManagerViewMode() = View.LargeIcon Then
            Me.btnViewLargeIcon.Checked = True
        Else
            Me.btnViewList.Checked = True
        End If
        Me.btnDelete.Visible = False
    End Sub

    '    Private Function Decrypted() As Boolean
    '        Dim blnReturn As Boolean = True

    '        Try
    '            If mbdb.CheckIsEncrypted(Me.mBackObject.FolderPath) AndAlso Not iPhoneDevice.mb2_Check_Decrypted(Me.mBackObject.FolderPath) Then
    '                '"请输入密码："
    '                Dim strPwd As String = String.Empty
    '                tbInputBox.Show(Me, Me.Language.GetString("App.Lable.EnterPassword") & Me.Language.GetString("Common.Symbol.Colon"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, MessageBoxDefaultButton.Button1, strPwd, True)
    '                If String.IsNullOrEmpty(strPwd) Then
    '                    blnReturn = False
    '                    GoTo DO_EXIT
    '                End If

    '                Dim tmpDevice As iPhoneDevice = BackupInfo.GetHighProductVersionDevice(Me.mBackObject.ProductVersion)
    '                If tmpDevice Is Nothing Then
    '                    blnReturn = False
    '                    GoTo DO_EXIT
    '                End If

    '                Me.SetLoadding(True, Me.Language.GetString("Backup.Message.Decrypteding") & "0%")          '"正在解密..."

    '                If Not tmpDevice.mb2_Unback(Me.mBackObject.FolderPath, Me.mBackObject.Identifier, strPwd, New BackupRestoreHandler(AddressOf tmpDevice_BackupRestoreHandler)) Then
    '                    blnReturn = False
    '                End If
    '            End If
    '        Catch ex As Exception
    '            Common.LogException(ex.ToString(), "Decrypted")
    '        End Try
    'DO_EXIT:
    '        Return blnReturn
    '    End Function

    Public Sub tmpDevice_BackupRestoreHandler(ByVal args As BackupRestoreEventArgs)
        Dim intProgreaa As Integer = args.Progress
        If intProgreaa <= 0 Then
            Return
        End If

        Me.SetLoadding(True, Me.Language.GetString("Backup.Message.Decrypteding") & String.Format("{0}%", intProgreaa))          '"正在解密..."
    End Sub

    Private Delegate Sub SetLoaddingHandler(ByVal isShow As Boolean, ByVal strMessage As String, ByVal blnDecryptedFailure As Boolean)
    Private Sub SetLoadding(ByVal isShow As Boolean, ByVal strMessage As String, Optional ByVal blnDecryptedFailure As Boolean = True)
        If Me.InvokeRequired Then
            Me.Invoke(New SetLoaddingHandler(AddressOf SetLoadding), isShow, strMessage, blnDecryptedFailure)
        Else
            If isShow Then
                If Not Folder.LangType = LanguageType.en_US Then
                    Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
                End If
                Me.pnlLoading.Location = Me.tlpMain.Location
                Me.pnlLoading.Size = Me.tlpMain.Size
                Me.pnlLoading.BringToFront()
                Me.pnlLoading.Visible = True
            Else
                Me.pnlLoading.SendToBack()
                Me.pnlLoading.Visible = False
            End If

            Me.lblLoading.Text = strMessage
            Me.lblLoading.Refresh()

            If Not blnDecryptedFailure Then
                Me.pbLoading.Size = My.Resources.file_fileempty.Size
                Me.pbLoading.Image = My.Resources.file_fileempty
                Me.pbLoading.Location = New Point(Me.lblLoading.Left - Me.pbLoading.Width - 3, Me.lblLoading.Top + (Me.lblLoading.Height - Me.pbLoading.Height) / 2)
                Me.lblLoading.ForeColor = Color.Red
            End If
        End If
    End Sub

    Private Delegate Sub InitLeftTreeViewHandler()
    Private Sub InitLeftTreeView()
        If Me.InvokeRequired Then
            Me.Invoke(New InitLeftTreeViewHandler(AddressOf InitLeftTreeView))
        Else
            Me.InitImageList()
            With Me.tvwLeft
                .BeginUpdate()
                .Nodes.Clear()
                .ImageList = Me.imlSmall
                .ShowRootLines = True
                Dim gpNode As TreeNode = Nothing
                Dim childNode As TreeNode = Nothing
                Dim strDeviceName As String = Me.Language.GetString("App.TreeNode.Device")          '"设备"
                If Me.mBackObject IsNot Nothing Then
                    strDeviceName = Me.mBackObject.DeviceName
                End If
                gpNode = .Nodes.Add("/", strDeviceName, "Device")
                gpNode.SelectedImageKey = "Device"

                childNode = gpNode.Nodes.Add("/var/", "var", "Folder")
                childNode.SelectedImageKey = "Folder"
                childNode.Nodes.Add("")
                'Me.InitVarSubNode()

                gpNode.Expand()

                gpNode = .Nodes.Add("normal", Me.Language.GetString("File.TreeNode.CommonDirectory"), "Normal")                                 '"常用目录"
                gpNode.SelectedImageKey = "Normal"

                childNode = gpNode.Nodes.Add(PathDCIM, Me.Language.GetString("Backup.Label.Photo"), "Photo")         '"照片"
                childNode.SelectedImageKey = "Photo"
                childNode.Nodes.Add("")

                childNode = gpNode.Nodes.Add(PathSMS, Me.Language.GetString("Backup.Label.SMS"), "Message")                   '"短信"
                childNode.SelectedImageKey = "Message"
                childNode.Nodes.Add("")

                childNode = gpNode.Nodes.Add(PathAddressBook, Me.Language.GetString("Main.Button.Contact"), "Contact")        '"联系人"
                childNode.SelectedImageKey = "Contact"
                childNode.Nodes.Add("")

                childNode = gpNode.Nodes.Add(PathCallHistory, Me.Language.GetString("Main.Button.CallHistory"), "CallHistory")  '"通话记录"
                childNode.SelectedImageKey = "CallHistory"
                childNode.Nodes.Add("")

                childNode = gpNode.Nodes.Add(PathNote, Me.Language.GetString("Main.Button.Note"), "Notes")  '"备忘录"
                childNode.SelectedImageKey = "Notes"
                childNode.Nodes.Add("")

                If Folder.LangType = LanguageType.zh_CN Then
                    childNode = gpNode.Nodes.Add(PathWeixin, Me.Language.GetString("Weixin.Text.Title"), "Weixin")  '"微信"
                    childNode.SelectedImageKey = "Weixin"
                    childNode.Nodes.Add("")

                    childNode = gpNode.Nodes.Add(PathQQ, "QQ", "QQ")  '"微信"
                    childNode.SelectedImageKey = "QQ"
                    childNode.Nodes.Add("")
                Else
                    childNode = gpNode.Nodes.Add(PathWhatsApp, "WhatsApp", "WhatsApp")  '"微信"
                    childNode.SelectedImageKey = "WhatsApp"
                    childNode.Nodes.Add("")
                End If

                gpNode.Expand()

                .SelectedNode = .Nodes(0)
                .EndUpdate()
            End With
        End If
    End Sub

    Private Sub InitImageList()
        With Me.imlLarge
            .Images.Clear()
            .Images.Add("shortcut_FolderLargeIcon", My.Resources.ResourceManager.GetObject("file_foldershortcut_large"))
            .Images.Add("FileLargeIcon", My.Resources.ResourceManager.GetObject("file_file_large"))
            .Images.Add("CydiaLargeIcon", My.Resources.ResourceManager.GetObject("file_cydia_large"))
            .Images.Add("FolderLargeIcon", My.Resources.ResourceManager.GetObject("file_folder_large"))
            .Images.Add("UDiskLargeIcon", My.Resources.ResourceManager.GetObject("file_documents_large"))
            .Images.Add("AppLargeIcon", My.Resources.ResourceManager.GetObject("file_app_large"))
            .Images.Add("InstallousLargeIcon", My.Resources.ResourceManager.GetObject("file_installous_large"))
            .Images.Add("FileSearchLargeIcon", My.Resources.ResourceManager.GetObject("file_filesearch_large"))
            .Images.Add("shortcut_FileLargeIcon", My.Resources.ResourceManager.GetObject("file_fileshortcut_large"))
            .Images.Add("PlistLargeIcon", My.Resources.ResourceManager.GetObject("file_plist_large"))
            .Images.Add("PicLargeIcon", My.Resources.ResourceManager.GetObject("file_pic_large"))
            .Images.Add("MyFavoriteLargeIcon", My.Resources.ResourceManager.GetObject("file_myfavorite_largel"))
            .Images.Add("UnfindLargeIcon", My.Resources.ResourceManager.GetObject("file_unfind_large"))
            .Images.Add("UnfindFileLargeIcon", My.Resources.ResourceManager.GetObject("file_unfindfile_large"))
            .Images.Add("tbTuiLargeIcon", My.Resources.ResourceManager.GetObject("file_tbtui_large"))
            .Images.Add("ipaLargeIcon", My.Resources.ResourceManager.GetObject("file_ipa_large"))
            .Images.Add("ExcelLargeIcon", My.Resources.ResourceManager.GetObject("file_excel_75"))
            .Images.Add("PdfLargeIcon", My.Resources.ResourceManager.GetObject("file_pdf_75"))
            .Images.Add("PptLargeIcon", My.Resources.ResourceManager.GetObject("file_ppt_75"))
            .Images.Add("TxtLargeIcon", My.Resources.ResourceManager.GetObject("file_txt_75"))
            .Images.Add("WordLargeIcon", My.Resources.ResourceManager.GetObject("file_word_75"))
            .Images.Add("VideoLargeIcon", My.Resources.ResourceManager.GetObject("file_video_75"))

            .Images.Add("PhotoLargeIcon", My.Resources.ResourceManager.GetObject("backup_photo_75"))
            .Images.Add("MessageLargeIcon", My.Resources.ResourceManager.GetObject("backup_message_75"))
            .Images.Add("ContactLargeIcon", My.Resources.ResourceManager.GetObject("backup_contact_75"))
            .Images.Add("CallHistoryLargeIcon", My.Resources.ResourceManager.GetObject("backup_callhistory_75"))
            .Images.Add("NotesLargeIcon", My.Resources.ResourceManager.GetObject("backup_notes_75"))
            .Images.Add("WeixinLargeIcon", My.Resources.ResourceManager.GetObject("backup_weixin_75"))
        End With

        With Me.imlSmall
            .Images.Clear()
            .Images.Add("shortcut_Folder", My.Resources.ResourceManager.GetObject("file_foldershortcut_small"))
            .Images.Add("File", My.Resources.ResourceManager.GetObject("file_file_small"))
            .Images.Add("Cydia", My.Resources.ResourceManager.GetObject("file_cydia_small"))
            .Images.Add("Folder", My.Resources.ResourceManager.GetObject("file_folder_small"))
            .Images.Add("UDisk", My.Resources.ResourceManager.GetObject("file_documents_small"))
            .Images.Add("App", My.Resources.ResourceManager.GetObject("file_app_small"))
            .Images.Add("Installous", My.Resources.ResourceManager.GetObject("file_installous_small"))
            .Images.Add("FileSearch", My.Resources.ResourceManager.GetObject("file_filesearch_small"))
            .Images.Add("shortcut_File", My.Resources.ResourceManager.GetObject("file_fileshortcut_small"))
            .Images.Add("Plist", My.Resources.ResourceManager.GetObject("file_plist_small"))
            .Images.Add("Pic", My.Resources.ResourceManager.GetObject("file_pic_small"))
            .Images.Add("MyFavorite", My.Resources.ResourceManager.GetObject("file_myfavorite_small"))
            .Images.Add("Unfind", My.Resources.ResourceManager.GetObject("file_unfind_small"))
            .Images.Add("UnfindFile", My.Resources.ResourceManager.GetObject("file_unfindfile_small"))
            .Images.Add("tbTui", My.Resources.ResourceManager.GetObject("file_tbtui_small"))
            .Images.Add("ipa", My.Resources.ResourceManager.GetObject("file_ipa_large"))
            .Images.Add("Excel", My.Resources.ResourceManager.GetObject("file_excel_16"))
            .Images.Add("Pdf", My.Resources.ResourceManager.GetObject("file_pdf_16"))
            .Images.Add("Ppt", My.Resources.ResourceManager.GetObject("file_ppt_16"))
            .Images.Add("Txt", My.Resources.ResourceManager.GetObject("file_txt_16"))
            .Images.Add("Word", My.Resources.ResourceManager.GetObject("file_word_16"))
            .Images.Add("Video", My.Resources.ResourceManager.GetObject("file_video_16"))
            .Images.Add("FileSystem", My.Resources.ResourceManager.GetObject("file_filesystem_16"))
            .Images.Add("Device", My.Resources.ResourceManager.GetObject("backup_save_16"))
            .Images.Add("Normal", My.Resources.ResourceManager.GetObject("backup_normal_16"))
            .Images.Add("Photo", My.Resources.ResourceManager.GetObject("backup_photo_16"))
            .Images.Add("Message", My.Resources.ResourceManager.GetObject("backup_message_16"))
            .Images.Add("Contact", My.Resources.ResourceManager.GetObject("backup_contact_16"))
            .Images.Add("CallHistory", My.Resources.ResourceManager.GetObject("backup_callhistory_16"))
            .Images.Add("Notes", My.Resources.ResourceManager.GetObject("backup_notes_16"))
            .Images.Add("Weixin", My.Resources.ResourceManager.GetObject("backup_weixin_16"))
            .Images.Add("QQ", My.Resources.ResourceManager.GetObject("backup_weixin_16"))
            .Images.Add("WhatsApp", My.Resources.ResourceManager.GetObject("backup_weixin_16"))
        End With

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        Try
            If Me.mtdForDecrypt IsNot Nothing AndAlso Me.mtdForDecrypt.ThreadState <> ThreadState.Stopped Then
                Me.mtdForDecrypt.Abort()
            End If

            'If pblnCancelClose = False AndAlso Me.Owner IsNot Nothing AndAlso Me.Owner.Visible = False Then
            '    Me.Owner.Visible = True
            'End If
        Catch
        End Try

        If Me.mDeviceForDecrypt IsNot Nothing Then
            Me.mDeviceForDecrypt.mb2_SvrClose()
        End If

        Try
            iPhoneDevice.DoDecryptCancel()
        Catch
        End Try

        Try
            Me.DeleteEncryptedBackup()
        Catch ex As Exception
        End Try

        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

    Private Sub DeleteEncryptedBackup()
        Try
            If Me.StrEncryptedBackup.Length > 0 Then
                MainForm.DeleteEncryptedBackup(Me.StrEncryptedBackup)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteEncryptedBackup")
        End Try
    End Sub

#End Region

#Region "--- TreeView 操作 ---"

    Private Sub tvwLeft_AfterExpand(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles tvwLeft.AfterExpand
        If e.Node Is Nothing Then
            Return
        End If
        If e.Node.Nodes.Count = 1 AndAlso e.Node.Nodes(0).Text = "" AndAlso e.Node.Nodes(0).Tag Is Nothing Then
            Me.LoadTreeNode(e.Node, False, True)
        End If

    End Sub

    Private Sub SetCurrentNodeType(ByVal node As TreeNode)
        If node.Name = "Search" Then
            Me.mCurrentNodeType = BackupNodeType.Search
        Else
            Me.mCurrentNodeType = BackupNodeType.Normal
        End If
    End Sub

    Private Sub tvwLeft_AfterSelect(ByVal sender As System.Object, ByVal e As System.Windows.Forms.TreeViewEventArgs) Handles tvwLeft.AfterSelect
        Me.SetGoUpEnable()
        If e.Node Is Nothing Then
            Return
        End If

        Me.SetCurrentNodeType(e.Node)

        Me.btnExport.Enabled = IIf(Me.GetCurrentListView.SelectedItems.Count > 0, True, False)
        Me.btnView.Enabled = IIf(Me.GetCurrentListView.SelectedItems.Count > 0, True, False)

        Me.GetCurrentListView.BringToFront()

        Dim blnLoadTreeNode As Boolean = False
        If e.Node.Nodes.Count = 1 AndAlso e.Node.Nodes(0).Text = "" AndAlso e.Node.Nodes(0).Tag Is Nothing Then
            blnLoadTreeNode = True
        End If
        Me.LoadTreeNode(e.Node, True, blnLoadTreeNode)

        Me.UpdateThumbnailOtherThread(Me.GetCurrentListView)

        Me.txtFilePath.Text = e.Node.Name
        Me.OpenLoadDBForm(e.Node.Name, "")
    End Sub

    Private Sub LoadTreeNode(ByVal node As TreeNode, ByVal blnLoadListView As Boolean, ByVal blnLoadTreeNode As Boolean)
        If node.Name = "/" Then
            Me.LoadRootData()

        ElseIf node.Name = "normal" Then
            Me.LoadNormalData()

        Else
            Me.LoadPathData(node, blnLoadListView, blnLoadTreeNode)

        End If
    End Sub

    Private Sub CheckSubTreeNode(ByVal objNode As TreeNode)
        objNode.Nodes.Add("")
    End Sub

    Private Function GetCurrentListView() As ListView
        Dim lvw As ListView = Me.mListView
        If Me.mCurrentNodeType = BackupNodeType.Search Then
            lvw = Me.mListViewSearch
        End If
        Return lvw
    End Function

#End Region

#Region "--- 加载数据逻辑 ---"

    Private Sub LoadRecords()
        Try
            If Me.Decrypt() Then
                Me.SetLoadding(True, Me.Language.GetString("App.Cell.Loading"))
                If String.IsNullOrEmpty(Me.mstrBackupPath) Then
                    Dim strFoler As String = Me.mBackObject.FolderPath
                    If Me.mFolderPathUnbackDir.Length > 0 AndAlso Directory.Exists(Me.mFolderPathUnbackDir) Then
                        strFoler = Me.mFolderPathUnbackDir
                    End If

                    Me.mLstMBFileRecord = mbdb.Reload(strFoler)
                Else
                    Me.LoadFailuerBackUp()
                End If

                Me.InitLeftTreeView()
                Me.SetLoadding(False, Me.Language.GetString("App.Cell.Loading"))
            Else
                Me.SetLoadding(True, Me.Language.GetString("Backup.Message.DecryptedFailure"), False)       '"解密失败！"

                If ServerIniSetting.GetShowWechatBackupTool Then
                    ''解密失败！
                    ''可以试试【微信备份助手】，可以在不用数据线的情况下导出微信的消息。
                    ''是否需要了解？
                    'Dim strMsg As String = String.Format("{0}{1}{2}{1}{3}", _
                    '                                 Me.Language.GetString("Backup.Message.DecryptedFailure"), _
                    '                                 vbCrLf, _
                    '                                 Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistant"), _
                    '                                 Me.Language.GetString("WechatAssistant.Message.NeedToKnow"))
                    'If tbMessageBox.Show(Me, _
                    '                     strMsg, _
                    '                     Me.Language.GetString("Common.Info"), _
                    '                     MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Yes Then

                    '    Common.OpenExplorer(ServerIniSetting.GetUrlWechatBackupTooliOSEncrypt)
                    'End If

                    Dim strMsg As String = String.Format("{1}{0}{2}{3}", _
                                                               vbCrLf, _
                                                               Me.Language.GetString("Backup.Message.DecryptedFailure"), _
                                                               Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistantEx"), _
                                                               Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistantEx1"))

                    If tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2, "", False, New String() {Me.Language.GetString("WechatAssistant.Button.Specific"), Me.Language.GetString("WechatAssistant.Button.iWechatAssistant")}) = Windows.Forms.DialogResult.Yes Then
                        Common.OpenExplorer("http://news.tongbu.com/92201.html")
                    Else
                        Common.OpenExplorer(ServerIniSetting.GetUrlWechatBackupTooliOSBackupFailed)
                    End If

                End If

            End If
        Catch ex As Exception
        End Try
    End Sub

    '如果需要解密，则解密
    Private Function Decrypt() As Boolean
        Dim blnReturn As Boolean = True

        Try
            If Me.mIsNeedDecrypt Then ' AndAlso Me.mDeviceForDecrypt IsNot Nothing AndAlso Me.mDeviceForDecrypt.IsConnected

                Me.SetLoadding(True, Me.Language.GetString("Backup.Message.Decrypteding") & "0%")          '"正在解密..."

                'If Not Me.mDeviceForDecrypt.mb2_Unback(Me.mBackObject.FolderPath, Me.mBackObject.Identifier, Me.mPwdForDecrypt,  New BackupRestoreHandler(AddressOf tmpDevice_BackupRestoreHandler)) Then
                '    blnReturn = False
                'End If

                If Not iPhoneDevice.mb2_Unback_New(Me.mBackObject.FolderPath, Me.mPwdForDecrypt, New BackupRestoreHandler(AddressOf tmpDevice_BackupRestoreHandler)) Then
                    blnReturn = False
                Else
                    Me.mFolderPathUnbackDir = Path.Combine(Me.mBackObject.FolderPath, iPhoneDevice.Unback_Dir)
                    Me.StrEncryptedBackup = Me.mFolderPathUnbackDir
                    'Me.mBackObject.FolderPath = Me.mFolderPathUnbackDir
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Decrypted")
        End Try

        Return blnReturn
    End Function

    Private Sub LoadRootData()
        Dim lstFolders As New List(Of ListViewItem)

        Dim lvwItem As ListViewItem = Me.CreateListViewItem("var", "Folder", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, "/var/", ListViewItemType.Folder)
        lstFolders.Add(lvwItem)

        Dim lvw As ListView = Me.GetCurrentListView()
        lvw.Items.Clear()
        Me.AddListViewItem(lvw, lstFolders)
        lstFolders.Clear()
        lstFolders = Nothing

        Me.SetFileCount(0, 1)
    End Sub

    Private Sub LoadNormalData()
        Dim lstFolders As New List(Of ListViewItem)
        '照片（相机胶卷）
        Dim lvwItem As ListViewItem = Me.CreateListViewItem(Me.Language.GetString("Backup.Label.Photo"), "Photo", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, PathDCIM, ListViewItemType.Folder)
        lstFolders.Add(lvwItem)
        '"短信"
        lvwItem = Me.CreateListViewItem(Me.Language.GetString("Backup.Label.SMS"), "Message", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, PathSMS, ListViewItemType.Folder)
        lstFolders.Add(lvwItem)
        '"联系人"
        lvwItem = Me.CreateListViewItem(Me.Language.GetString("Main.Button.Contact"), "Contact", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, PathAddressBook, ListViewItemType.Folder)
        lstFolders.Add(lvwItem)
        '"通话记录"
        lvwItem = Me.CreateListViewItem(Me.Language.GetString("Main.Button.CallHistory"), "CallHistory", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, PathCallHistory, ListViewItemType.Folder)
        lstFolders.Add(lvwItem)

        '"备忘录"
        lvwItem = Me.CreateListViewItem(Me.Language.GetString("Main.Button.Note"), "Notes", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, PathNote, ListViewItemType.Folder)
        lstFolders.Add(lvwItem)

        If Folder.LangType = LanguageType.zh_CN Then
            '"微信"
            lvwItem = Me.CreateListViewItem(Me.Language.GetString("Weixin.Text.Title"), "Weixin", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, PathWeixin, ListViewItemType.Folder)
            lstFolders.Add(lvwItem)

            '"QQ"
            lvwItem = Me.CreateListViewItem("QQ", "QQ", New String() {"", Me.Language.GetString("Common.Folder"), "", "", ""}, PathQQ, ListViewItemType.Folder)
            lstFolders.Add(lvwItem)
        End If

        Dim lvw As ListView = Me.GetCurrentListView
        lvw.Items.Clear()
        Me.AddListViewItem(lvw, lstFolders)

        lstFolders.Clear()
        lstFolders = Nothing
        Me.SetFileCount(0, 4)
    End Sub

    Private Sub LoadPathData(ByVal node As TreeNode, ByVal blnLoadListView As Boolean, ByVal blnLoadTreeNode As Boolean)
        Dim strDir As String = node.Name
        Dim lstFolders As New List(Of ListViewItem)
        Dim lstFiles As New List(Of ListViewItem)
        Dim lstNodes As New List(Of TreeNode)
        Dim lstExistDir As New List(Of String)

        For Each item As MBFileRecord In Me.mLstMBFileRecord
            '/var/mobile/ 与 /var/mobile
            If Not item.PathOnPhone.StartsWith(strDir) OrElse item.PathOnPhone.TrimEnd("/").Length <= strDir.TrimEnd("/").Length Then
                Continue For
            End If

            If Me.CheckInFolder(strDir, item.PathOnPhone) Then
                '文件夹mode:16877 16895 16872 16832
                If Me.IsFolder(item) Then
                    Dim strNewDir As String = item.PathOnPhone.TrimEnd("/"c) & "/"
                    If lstExistDir.Contains(strNewDir) Then
                        Continue For
                    End If

                    lstExistDir.Add(strNewDir)

                    Dim strText As String = Path.GetFileName(item.relativePath)
                    If blnLoadListView Then
                        Dim lvwItem As ListViewItem = Me.CreateListViewItem(strText, "Folder", New String() {"", Me.Language.GetString("Common.Folder"), item.CreateTime.ToString(), "", ""}, strNewDir, ListViewItemType.Folder)
                        lstFolders.Add(lvwItem)
                    End If
                    If blnLoadTreeNode Then
                        lstNodes.Add(Me.CreateTreeNode(strNewDir, strText))
                    End If

                Else
                    If blnLoadListView Then
                        Dim strText As String = Path.GetFileName(item.relativePath)
                        Dim lvwItem As ListViewItem = Me.CreateListViewItem(strText, Me.GetFileType(item.PathOnPhone),
                                                      New String() {Utility.FormatFileSize(item.Size), Me.Language.GetString("Common.File"), item.CreateTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), "", ""}, item.PathOnPhone, item)
                        lstFiles.Add(lvwItem)
                    End If
                End If

            Else
                Dim arrDir() As String = item.PathOnPhone.Replace(strDir.TrimEnd("/") & "/", "").Split("/")
                If arrDir.Length = 0 Then
                    Continue For
                End If

                Dim strNewDir As String = strDir.TrimEnd("/"c) & "/" & arrDir(0) & "/"
                If lstExistDir.Contains(strNewDir) Then
                    Continue For
                End If

                lstExistDir.Add(strNewDir)

                Dim strText As String = arrDir(0)
                If blnLoadListView Then
                    Dim lvwItem As ListViewItem = Me.CreateListViewItem(strText, "Folder", New String() {"", Me.Language.GetString("Common.Folder"), item.CreateTime.ToString(), "", ""}, strNewDir, ListViewItemType.Folder)
                    lstFolders.Add(lvwItem)
                End If
                If blnLoadTreeNode Then
                    lstNodes.Add(Me.CreateTreeNode(strNewDir, strText))
                End If
            End If

        Next

        '添加listview
        If blnLoadListView Then
            Me.mListView.Items.Clear()
            Me.AddListViewItem(Me.mListView, lstFolders)

            If lstFiles.Count + lstFolders.Count > 0 Then
                Dim strDB As String = String.Empty

                Select Case strDir
                    Case PathWeixin
                        strDB = PathWeixinDB
                    Case PathQQ
                        strDB = PathQQDB
                    Case PathWhatsApp
                        strDB = PathWhatsAppDB

                End Select

                If Not String.IsNullOrEmpty(strDB) Then
                    Dim strText As String = Path.GetFileName(strDB)
                    Dim fRecord As New MBFileRecord
                    fRecord.PathOnPhone = strDB
                    Dim lvwItem As ListViewItem = Me.CreateListViewItem(strText, Me.GetFileType(strDB),
                                                  New String() {"", "Link", Now.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), "", ""}, strDB, fRecord)
                    lstFiles.Add(lvwItem)
                End If
            End If

            Me.AddListViewItem(Me.mListView, lstFiles)
        End If

        '添加到树节点上
        If blnLoadTreeNode Then
            Me.AddTreeNodeList(node, lstNodes)
            For Each Item As TreeNode In node.Nodes
                '添加节点上的+号
                Me.CheckSubTreeNode(Item)
            Next
        End If

        If Me.mCurrentNodeType = BackupNodeType.Search Then
            Me.SetFileCount(Me.GetCurrentListView.Items.Count, 0)
        Else
            Me.SetFileCount(lstFiles.Count, lstFolders.Count)
        End If

        lstFolders.Clear()
        lstFiles.Clear()
        lstNodes.Clear()
        lstExistDir.Clear()
    End Sub

    Private Function CheckInFolder(ByVal dirPath As String, ByVal subPath As String) As Boolean
        Dim isSubPath As String = False
        Dim strNewPath As String = subPath.TrimEnd("/").Replace(dirPath.TrimEnd("/") & "/", "")
        If Not strNewPath.Contains("/") Then
            isSubPath = True
        End If
        Return isSubPath
    End Function

    Protected Function CreateListViewItem(ByVal strText As String,
                                          ByVal imageKey As String,
                                          ByVal subItems As String(),
                                          ByVal strName As String, ByVal tagObj As Object) As ListViewItem
        Dim lvwItem As New ListViewItem
        With lvwItem
            .Name = strName
            .ImageKey = imageKey & IIf(Me.GetCurrentListView.View = View.LargeIcon, "LargeIcon", "")
            .Text = strText
            .SubItems.AddRange(subItems)
            .Tag = tagObj
        End With

        Return lvwItem
    End Function

    Public Function CreateTreeNode(ByVal strPath As String, ByVal strName As String)
        Dim objNodeNew As New TreeNode()
        objNodeNew.Name = strPath
        objNodeNew.Text = strName
        objNodeNew.ImageKey = "Folder"
        objNodeNew.SelectedImageKey = "Folder"

        Return objNodeNew
    End Function

    '添加TreeNode子节点

    Private Delegate Sub AddTrewViewListHandler(ByVal node As TreeNode, ByVal subNodeList As List(Of TreeNode))
    Public Sub AddTreeNodeList(ByVal node As TreeNode, ByVal subNodeList As List(Of TreeNode))
        If Me.InvokeRequired Then
            Me.Invoke(New AddTrewViewListHandler(AddressOf AddTreeNodeList), node, subNodeList)
        Else
            Me.tvwLeft.SuspendLayout()
            node.Nodes.Clear()
            node.Nodes.AddRange(subNodeList.ToArray())
            Me.tvwLeft.ResumeLayout()
            Application.DoEvents()
        End If
    End Sub

    '添加ListViewItem
    Private Delegate Sub AddListViewItemHandler(ByVal lvw As ListView, ByVal lstItem As List(Of ListViewItem))
    Public Sub AddListViewItem(ByVal lvw As ListView, ByVal lstItem As List(Of ListViewItem))
        If lvw.InvokeRequired Then
            lvw.Invoke(New AddListViewItemHandler(AddressOf AddListViewItem), lvw, lstItem)
        Else
            For Each item As ListViewItem In lstItem
                lvw.Items.Add(item)
            Next
        End If
    End Sub

#End Region

#Region "--- ListView 操作 ---"

    Private Function CreateListView(ByVal imlSmall As ImageList, ByVal imlLarge As ImageList, Optional ByVal lvw As tbListView = Nothing, Optional ByVal allowDrag As Boolean = True) As tbListView
        If lvw Is Nothing Then
            lvw = New tbListView
        End If

        With lvw
            .Items.Clear()
            .Columns.Clear()
            .View = View.LargeIcon
            .GridLines = False
            .FullRowSelect = True
            .HideSelection = False
            .MultiSelect = True
            .LabelEdit = True
            .SmallImageList = imlSmall
            .LargeImageList = imlLarge
            .Dock = DockStyle.Fill
            .BorderStyle = BorderStyle.None
            .AllowDrop = True
            .BackColor = Color.FromArgb(250, 251, 252)
            .ContextMenuStrip = Me.munOperator

            .Columns.Add("colName", Me.Language.GetString("Common.Label.Name"), 200, HorizontalAlignment.Left, -1) '"名称"
            .Columns.Add("colSize", Me.Language.GetString("Common.Columns.Size"), 80, HorizontalAlignment.Right, -1) '"大小"
            .Columns.Add("colType", Me.Language.GetString("Common.Label.ColumnType"), 80, HorizontalAlignment.Left, -1) '"类型"
            .Columns.Add("colModifyDate", Me.Language.GetString("Common.Label.ModifyDate"), 130, HorizontalAlignment.Left, -1) '"修改日期"

        End With

        RemoveHandler lvw.AfterLabelEdit, AddressOf lvw_AfterLabelEdit
        RemoveHandler lvw.DoubleClick, AddressOf lvw_DoubleClick
        RemoveHandler lvw.ItemDrag, AddressOf lvw_ItemDrag
        RemoveHandler lvw.SelectedIndexChanged, AddressOf lvw_SelectedIndexChanged
        RemoveHandler lvw.BeforeLabelEdit, AddressOf lvw_BeforeLabelEdit

        AddHandler lvw.AfterLabelEdit, AddressOf lvw_AfterLabelEdit
        AddHandler lvw.DoubleClick, AddressOf lvw_DoubleClick
        AddHandler lvw.ItemDrag, AddressOf lvw_ItemDrag
        AddHandler lvw.SelectedIndexChanged, AddressOf lvw_SelectedIndexChanged
        AddHandler lvw.BeforeLabelEdit, AddressOf lvw_BeforeLabelEdit

        Return lvw
    End Function

    Public Sub lvw_BeforeLabelEdit(ByVal sender As Object, ByVal e As System.Windows.Forms.LabelEditEventArgs)
        e.CancelEdit = True
    End Sub

    Private Sub lvw_AfterLabelEdit(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LabelEditEventArgs)

    End Sub

    Private Sub lvw_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.btnExport.Enabled = IIf(Me.GetCurrentListView.SelectedItems.Count > 0, True, False)
        Me.btnView.Enabled = IIf(Me.GetCurrentListView.SelectedItems.Count > 0, True, False)
        If Me.GetCurrentListView.SelectedItems.Count > 0 Then
            If Me.GetCurrentListView.Items(0).Tag IsNot Nothing AndAlso TypeOf Me.GetCurrentListView.SelectedItems(0).Tag Is MBFileRecord Then
                Dim record As MBFileRecord = Me.GetCurrentListView.SelectedItems(0).Tag
                '"文件大小：{0}，修改日期：{1}"
                Me.lblMessage.Text = String.Format(Me.Language.GetString("Backup.Label.FileInfo"), Utility.FormatFileSize(record.Size), record.CreateTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)))
            End If
        Else
            Dim intFileCount As Integer = 0
            Dim intFolderCount As Integer = 0
            For Each Item As ListViewItem In Me.GetCurrentListView.Items
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is ListViewItemType Then
                    intFolderCount += 1
                Else
                    intFileCount += 1
                End If
            Next
            Me.SetFileCount(intFileCount, intFolderCount)

        End If
    End Sub

    Private Sub lvw_ItemDrag(ByVal sender As System.Object, ByVal e As System.Windows.Forms.ItemDragEventArgs)
        Dim lvw As ListView = sender

        '取得当前拖拽的 Item
        Dim listItem As New List(Of ListViewItem)
        For Each Item As ListViewItem In lvw.SelectedItems
            listItem.Add(Item)
        Next

        If listItem.Count > 0 Then
            Try
                Me.mDataObject = New DataObject("FileDrop", listItem)
                Me.DoDragDrop(Me.mDataObject, DragDropEffects.Copy)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub lvw_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.OpenItem(sender)
    End Sub
#End Region

#Region "--- Spliter 拖动 ---"

    Private m_MousePoint As Point = New System.Drawing.Point(0, 0)
    Private m_MouseDown As Boolean = False
    Private m_MinWidth As Integer = 195

    Private Sub Spliter_MouseDown(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Spliter1.MouseDown, Spliter2.MouseDown
        If e.Button = System.Windows.Forms.MouseButtons.Left Then
            m_MousePoint = New Point(e.X, e.Y)
            m_MouseDown = True
        End If
    End Sub

    Private Sub Spliter_MouseMove(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Spliter1.MouseMove, Spliter2.MouseDown
        If Me.m_MouseDown AndAlso
           e.Button = System.Windows.Forms.MouseButtons.Left Then

            Dim intChanged As Integer = (e.X - Me.m_MousePoint.X)
            Dim intWidth As Integer = Me.tlpMain.ColumnStyles(0).Width + intChanged

            If intWidth < m_MinWidth Then
                intWidth = m_MinWidth
            ElseIf intWidth >= (Me.tlpMain.ClientRectangle.Width - Me.Spliter1.ClientRectangle.Width - Me.m_MinWidth) Then
                intWidth = (Me.tlpMain.ClientRectangle.Width - Me.Spliter1.ClientRectangle.Width - Me.m_MinWidth)
            End If

            Me.tlpMain.ColumnStyles(0).Width = intWidth

        End If
    End Sub

    Private Sub Spliter_MouseUp(ByVal sender As Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles Spliter1.MouseUp, Spliter2.MouseDown
        If e.Button = System.Windows.Forms.MouseButtons.Left Then
            Me.m_MouseDown = False
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Delegate Sub ShowProgressStatusHandler(ByVal isShow As Boolean)
    Private Sub ShowProgressStatus(ByVal isShow As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowProgressStatusHandler(AddressOf ShowProgressStatus), isShow)
        Else
            If isShow Then
                Me.tlpMain.RowStyles(3).Height = 30
            Else
                Me.tlpMain.RowStyles(3).Height = 0
            End If
        End If

    End Sub

    Private Sub OpenItem(ByVal lvw As ListView)
        If lvw.SelectedItems.Count = 0 OrElse lvw.SelectedItems.Count = 0 Then
            Return
        End If

        If lvw.SelectedItems(0).Tag IsNot Nothing AndAlso TypeOf lvw.SelectedItems(0).Tag Is ListViewItemType Then
            '打开文件夹
            If CType(lvw.SelectedItems(0).Tag, ListViewItemType) = ListViewItemType.Folder Then
                Dim strkey As String = lvw.SelectedItems(0).Name
                Dim nodesForSelect() As TreeNode = Me.tvwLeft.Nodes.Find(strkey, True)
                If nodesForSelect IsNot Nothing AndAlso nodesForSelect.Length > 0 Then
                    Me.tvwLeft.SelectedNode = nodesForSelect(0)
                End If
            End If

        ElseIf lvw.SelectedItems(0).Tag IsNot Nothing AndAlso TypeOf lvw.SelectedItems(0).Tag Is MBFileRecord Then
            Dim item As MBFileRecord = lvw.SelectedItems(0).Tag
            '打开文件
            If Me.CheckIfImg(item.PathOnPhone) Then
                '如果是图片打开图片预览
                Dim frmImageView As New tbImageViewForm()
                frmImageView.HideButton = ToolBarButtonType.WeiBo Or ToolBarButtonType.Delete
                frmImageView.ListView.SuspendLayout()
                frmImageView.Icon = My.Resources.iTong

                For Each lvItem As ListViewItem In Me.GetCurrentListView.Items
                    If Me.CheckIfImg(lvItem.Name) Then
                        Dim newItem As New ListViewItem()
                        newItem.Name = lvItem.Name
                        newItem.ImageKey = lvItem.ImageKey
                        If lvItem.Tag IsNot Nothing AndAlso TypeOf lvItem.Tag Is MBFileRecord Then
                            newItem.Tag = lvItem.Tag
                        End If
                        frmImageView.ListView.Items.Add(newItem)
                    End If
                Next
                frmImageView.ListView.ResumeLayout()

                frmImageView.ViewType = ViewType.FromDevice
                frmImageView.ListView.LargeImageList = lvw.LargeImageList
                frmImageView.SelectItemName = item.PathOnPhone

                Me.UpdateThumbnailOtherThread(frmImageView.ListView)

                RemoveHandler frmImageView.SelectItemChanged, AddressOf ImageView_SelectItemChanged
                AddHandler frmImageView.SelectItemChanged, AddressOf ImageView_SelectItemChanged
                frmImageView.Show()

            ElseIf Me.CheckIfText(item.PathOnPhone) Then
                Dim frmPlistEdit As New frmPlistEdit(Me.mApplication)
                frmPlistEdit.Show(Me, "", Nothing, 0, item.PathOnPC)

            Else
                Me.OpenLoadDBForm(item.PathOnPhone, item.PathOnPC)

            End If

        End If
    End Sub

    Private Sub OpenLoadDBForm(ByVal strPathOnPhone As String, ByVal strPathOnPC As String)
        If strPathOnPhone = PathSMSDB OrElse strPathOnPhone = PathSMS Then
            Dim strFilePath As String = Me.GetFilePathOnPC("sms.db")
            '短信
            If File.Exists(strFilePath) Then
                Dim frmSMSForDB As New frmBackupSMS(Me.mApplication, strFilePath, Me.mLstMBFileRecord, Me.mBackObject)
                frmSMSForDB.Show(Me)
            Else
                tbMessageBox.Show(Me.Language.GetString("Media.Message.FileNoExist"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        ElseIf strPathOnPhone = PathAddressBookDB OrElse strPathOnPhone = PathAddressBookImagesDB OrElse strPathOnPhone = PathAddressBook Then
            Dim strFilePath As String = Me.GetFilePathOnPC("AddressBook.sqlitedb")
            Dim strImageFilePath As String = Me.GetFilePathOnPC("AddressBookImages.sqlitedb")

            '联系人
            If File.Exists(strFilePath) Then
                Dim frmContactForDB As New frmBackupContact(Me.mApplication, strFilePath, strImageFilePath, Me.mBackObject)
                frmContactForDB.Show(Me)
            Else
                tbMessageBox.Show(Me.Language.GetString("Media.Message.FileNoExist"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        ElseIf strPathOnPhone = PathCallHistoryDB OrElse strPathOnPhone = PathCallHistory Then
            Dim strFilePath As String = Me.GetFilePathOnPC("CallHistory.storedata")

            If String.IsNullOrEmpty(strFilePath) Then
                strFilePath = Me.GetFilePathOnPC("call_history.db")
            End If

            '通话记录
            If File.Exists(strFilePath) Then
                Dim frmCallHistoryForDB As New frmBackupCallHistory(Me.mApplication, Me.mBackObject, strFilePath, Me.mLstMBFileRecord)
                frmCallHistoryForDB.Show(Me)
            Else
                tbMessageBox.Show(Me.Language.GetString("Media.Message.FileNoExist"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

        ElseIf strPathOnPhone = PathNoteDB OrElse strPathOnPhone = PathNoteIDX OrElse strPathOnPhone = PathNote Then
            '备忘录
            Dim strFilePath As String = Me.GetFilePathOnPC("notes.sqlite")
            If File.Exists(strFilePath) Then
                Dim db As BackupNotesDB = BackupNotesDB.Instance(strFilePath)
                Dim frmnote As New frmNote(Me.mApplication, Nothing, db)
                frmnote.Show(Me)
            Else
                tbMessageBox.Show(Me.Language.GetString("Media.Message.FileNoExist"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
        ElseIf strPathOnPhone = PathWeixinDB OrElse strPathOnPhone = PathWeixin Then
            If Not Directory.Exists(Me.mFolderPathUnbackDir) Then
                Me.mFolderPathUnbackDir = Me.mBackObject.FolderPath
            End If

            Dim backinfo As New BackupInfo(Me.mFolderPathUnbackDir)
            Dim frmM As New frmWeixin(Me.mApplication, Nothing, backinfo, IMAppType.WeChat)
            frmM.StartPosition = FormStartPosition.CenterParent
            frmM.ShowDialog()

        ElseIf strPathOnPhone = PathQQDB OrElse strPathOnPhone = PathQQ Then
            If Not Directory.Exists(Me.mFolderPathUnbackDir) Then
                Me.mFolderPathUnbackDir = Me.mBackObject.FolderPath
            End If

            Dim backinfo As New BackupInfo(Me.mFolderPathUnbackDir)
            Dim frmM As New frmWeixin(Me.mApplication, Nothing, backinfo, IMAppType.QQ)
            frmM.StartPosition = FormStartPosition.CenterParent
            frmM.ShowDialog()

        ElseIf strPathOnPhone = PathWhatsApp OrElse strPathOnPhone = PathWhatsAppDB Then
            If Not Directory.Exists(Me.mFolderPathUnbackDir) Then
                Me.mFolderPathUnbackDir = Me.mBackObject.FolderPath
            End If

            Dim backinfo As New BackupInfo(Me.mFolderPathUnbackDir)
            Dim frmM As New frmWeixin(Me.mApplication, Nothing, backinfo, IMAppType.WhatsApp)
            frmM.StartPosition = FormStartPosition.CenterParent
            frmM.ShowDialog()

        ElseIf Not String.IsNullOrEmpty(strPathOnPC) Then
            Common.OpenExplorer(strPathOnPC)

        End If
    End Sub

    Private Function GetFilePathOnPC(ByVal strFileName As String)
        Dim strFilePath As String = ""
        For Each contactsItem As ListViewItem In Me.GetCurrentListView.Items
            If contactsItem.Text = strFileName AndAlso contactsItem.Tag IsNot Nothing AndAlso TypeOf contactsItem.Tag Is MBFileRecord Then
                strFilePath = CType(contactsItem.Tag, MBFileRecord).PathOnPC

                Exit For
            End If
        Next
        Return strFilePath
    End Function

    Private Sub ImageView_SelectItemChanged(ByVal sender As Object, ByVal e As ImageEventArgs)
        Dim lvw As ListView = sender
        If lvw IsNot Nothing AndAlso lvw.SelectedItems.Count > 0 AndAlso lvw.SelectedItems(0).Tag IsNot Nothing AndAlso TypeOf lvw.SelectedItems(0).Tag Is MBFileRecord Then
            e.Image = Utility.GetImageFormFile(CType(lvw.SelectedItems(0).Tag, MBFileRecord).PathOnPC)
        End If
    End Sub

    Private Function CheckIfImg(ByVal strFileName As String) As Boolean
        If Not strFileName.StartsWith("/") Then
            Return False
        End If

        Dim blnReturn As Boolean = False

        strFileName = strFileName.ToLower()

        If strFileName.EndsWith(".bmp") OrElse
           strFileName.EndsWith(".png") OrElse
           strFileName.EndsWith(".jpeg") OrElse
           strFileName.EndsWith(".jpg") OrElse
           strFileName.EndsWith(".gif") OrElse
           strFileName.EndsWith(".thm") Then

            blnReturn = True
        End If

        Return blnReturn
    End Function

    Private Function CheckIfText(ByVal strFileName As String) As Boolean
        If Not strFileName.StartsWith("/") Then
            Return False
        End If

        Dim blnReturn As Boolean = False

        strFileName = strFileName.ToLower()

        If strFileName.EndsWith(".plist") OrElse
           strFileName.EndsWith(".strings") OrElse
           strFileName.EndsWith(".txt") OrElse
           strFileName.EndsWith(".xml") OrElse
           strFileName.EndsWith(".ini") OrElse
           strFileName.EndsWith(".log") OrElse
           strFileName.EndsWith(".cs") OrElse
           strFileName.EndsWith(".vb") OrElse
           strFileName.EndsWith(".js") OrElse
           strFileName.EndsWith(".c") OrElse
           strFileName.EndsWith(".h") OrElse
           strFileName.EndsWith(".m") OrElse
           strFileName.EndsWith(".mm") OrElse
           strFileName.EndsWith(".htm") OrElse
           strFileName.EndsWith(".html") Then

            blnReturn = True
        End If

        Return blnReturn
    End Function

    Private Function GetFileType(ByVal strFilePathOnPhone As String) As String
        Dim strFileType As String = "File"
        If Me.CheckIfImg(strFilePathOnPhone) Then
            strFileType = "Pic"
        ElseIf strFilePathOnPhone.ToLower.EndsWith(".plist") Then
            strFileType = "Plist"
        ElseIf Me.CheckIfText(strFilePathOnPhone) Then
            strFileType = "Txt"
        ElseIf strFilePathOnPhone.TrimEnd.ToLower.EndsWith(".doc") OrElse strFilePathOnPhone.TrimEnd.ToLower.EndsWith(".docx") Then
            strFileType = "Word"
        ElseIf strFilePathOnPhone.TrimEnd.ToLower.EndsWith(".ppt") OrElse strFilePathOnPhone.TrimEnd.ToLower.EndsWith(".pptx") Then
            strFileType = "Ppt"
        ElseIf strFilePathOnPhone.TrimEnd.ToLower.EndsWith(".pdf") Then
            strFileType = "Pdf"
        ElseIf strFilePathOnPhone.TrimEnd.ToLower.EndsWith(".xls") OrElse strFilePathOnPhone.TrimEnd.ToLower.EndsWith(".xlsx") Then
            strFileType = "Excel"

        End If
        Return strFileType
    End Function

    Private Sub SetGoUpEnable()
        If Me.tvwLeft IsNot Nothing AndAlso Me.tvwLeft.SelectedNode IsNot Nothing AndAlso Me.tvwLeft.SelectedNode.Parent IsNot Nothing Then
            Me.btnGoUp.Enabled = True
        Else
            Me.btnGoUp.Enabled = False
        End If
    End Sub

    Public Sub SwitchThumbnail(ByVal lvw As ListView)
        If lvw.View = View.Details Then
            Try
                If Me.mThrUpdateThumbnailOther IsNot Nothing AndAlso Me.mThrUpdateThumbnailOther.ThreadState <> ThreadState.Stopped Then
                    Me.mThrUpdateThumbnailOther.Abort()
                End If
            Catch ex As Exception
            End Try


            For Each Item As ListViewItem In lvw.Items
                Application.DoEvents()
                If Me.CheckIfImg(Item.Name) AndAlso (Item.ImageKey.EndsWith("Pic") = False AndAlso Item.ImageKey.EndsWith("LargeIcon") = False) Then
                    Item.ImageKey = "Pic"
                ElseIf Item.ImageKey.EndsWith("LargeIcon") Then
                    Item.ImageKey = Item.ImageKey.Replace("LargeIcon", "")
                End If

            Next
        ElseIf lvw.View = View.LargeIcon Then
            Me.UpdateThumbnailOtherThread(Me.GetCurrentListView)
            For Each Item As ListViewItem In lvw.Items
                Application.DoEvents()
                If Not Me.CheckIfImg(Item.Name) AndAlso Item.Name <> Item.ImageKey Then
                    Item.ImageKey = Item.ImageKey & "LargeIcon"
                End If
            Next

        End If
    End Sub

    Private mThrUpdateThumbnailOther As Thread

    Private Sub UpdateThumbnailOtherThread(ByVal lvw As ListView)
        Try
            Dim lvwGetThumbnai As ListView = lvw
            If lvw Is Nothing Then
                lvwGetThumbnai = Me.GetCurrentListView
            End If
            If Me.mThrUpdateThumbnailOther IsNot Nothing AndAlso Me.mThrUpdateThumbnailOther.ThreadState <> ThreadState.Stopped Then
                Me.mThrUpdateThumbnailOther.Abort()
            End If

            Me.mThrUpdateThumbnailOther = New Thread(New ParameterizedThreadStart(AddressOf UpdateThumbnailOther))
            Me.mThrUpdateThumbnailOther.IsBackground = True
            Me.mThrUpdateThumbnailOther.Start(lvwGetThumbnai)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmBackupPreview_UpdateThumbnailOtherThread")
        End Try
    End Sub

    Private Delegate Sub UpdateThumbnailHandler(ByVal objListView As Object)
    Protected Sub UpdateThumbnailOther(ByVal objListView As Object)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateThumbnailHandler(AddressOf UpdateThumbnailOther), objListView)
        Else
            Try
                Dim lvw As tbListView = objListView

                lvw.ScrollBar = True
                lvw.ResponseIndex += 1

                Dim intResponseIndex As Integer = lvw.ResponseIndex

                Dim blnReload As Boolean = False
                Dim blnLoadCompleted As Boolean = True
DO_RELOAD:
                For Each item As ListViewItem In lvw.Items
                    Application.DoEvents()

                    If intResponseIndex <> lvw.ResponseIndex Then
                        Exit Sub
                    End If

                    If lvw.ScrollBar Then
                        lvw.ScrollBar = False
                        blnReload = True
                        blnLoadCompleted = False

                        GoTo DO_RELOAD
                    End If

                    If blnReload Then
                        If lvw.ClientRectangle.IntersectsWith(item.Bounds) Then
                            blnReload = False
                        Else
                            Continue For
                        End If
                    End If

                    '解决用户程序和系统程序目录下切换到小图标的时候小图标会不见

                    If lvw.View = View.LargeIcon AndAlso lvw.LargeImageList.Images.ContainsKey(item.Name) Then
                        If item.Name <> item.ImageKey Then
                            item.ImageKey = item.Name
                        End If
                        Continue For
                    End If

                    '如果是图片就进行处理缩略图

                    If Me.CheckIfImg(item.Name) Then
                        Dim strPicPathInIphone As String = item.Name
                        Dim strPicPathInPC As String = ""
                        Dim imgLargeIcon As Image = Nothing
                        If item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is MBFileRecord Then
                            strPicPathInPC = CType(item.Tag, MBFileRecord).PathOnPC
                            imgLargeIcon = Common.ImageFromFile(strPicPathInPC)
                        End If

                        'If strPicPathInPC.ToUpper().EndsWith(".PNG") Then
                        '    imgLargeIcon = iPhonePNG.ImageFromFile(strPicPathInPC)
                        'Else
                        '    imgLargeIcon = Common.ImageFromFile(strPicPathInPC)
                        'End If

                        If imgLargeIcon IsNot Nothing Then
                            Dim imgSize As Size = lvw.LargeImageList.ImageSize
                            Dim imgCut As Image = Utility.GetThumbnail(imgLargeIcon, imgSize, False, False)

                            '线程操作，有可能切换到小图标的时候线程还没有结束，所的加一个判断

                            If lvw.View = View.Details Then
                                Return
                            End If

                            lvw.LargeImageList.Images.Add(item.Name, imgCut)
                            item.ImageKey = item.Name

                            imgLargeIcon.Dispose()
                            imgLargeIcon = Nothing
                        End If

                    End If
                    Application.DoEvents()
                Next

                If blnLoadCompleted = False Then
                    blnLoadCompleted = True
                    GoTo DO_RELOAD
                End If
            Catch ex As Exception
                Common.Log("UpdateThumbnailOther_" & vbTab & ex.ToString())
            End Try
        End If
    End Sub

    Private Function IsFolder(ByVal record As MBFileRecord)
        Dim blnIsFolder As Boolean = False
        If (record.Mode = 16877 OrElse record.Mode = 16895 OrElse record.Mode = 16895 OrElse record.Mode = 16872 OrElse record.Mode = 16832 OrElse record.Mode = 16841) AndAlso record.Size = 0 Then
            blnIsFolder = True
        End If
        Return blnIsFolder
    End Function

    Private Delegate Sub SetFileCountHandler(ByVal intFileCount As Integer, ByVal intFolderCount As Integer)
    Private Sub SetFileCount(ByVal intFileCount As Integer, ByVal intFolderCount As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New SetFileCountHandler(AddressOf SetFileCount), intFileCount, intFolderCount)
        Else
            If intFileCount > 0 OrElse intFolderCount > 0 Then
                '当前 {0} 个文件/文件夹

                Me.lblMessage.Text = String.Format(Me.Language.GetString("File.Message.FileCount"), intFileCount + intFolderCount)
            Else
                Me.lblMessage.Text = String.Format(Me.Language.GetString("File.Message.FileCount"), 0)
            End If

        End If
    End Sub

    Private Function GoToPath(ByVal strPath As String) As Boolean
        Dim success As Boolean = False
        Try
            Dim blnExist As Boolean = False
            For Each Item As MBFileRecord In mLstMBFileRecord
                If Item.PathOnPhone.StartsWith(strPath) Then
                    blnExist = True
                    Exit For
                End If
            Next
            If Not blnExist Then
                '路径不存在

                Return success
            End If
        Catch ex As Exception
        End Try
        Return success
    End Function

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnViewLargeIcon_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnViewLargeIcon.CheckedChanged
        If Me.mListView Is Nothing Then
            Return
        End If
        If Me.btnViewLargeIcon.Checked Then
            Me.mListView.View = View.LargeIcon
            Me.mListViewSearch.View = View.LargeIcon
        Else
            Me.mListView.View = View.Details
            Me.mListViewSearch.View = View.Details
        End If
        Me.SwitchThumbnail(Me.GetCurrentListView())
        Me.GetCurrentListView.Invalidate()
    End Sub

    Private Sub btnGoUp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGoUp.Click
        If Me.tvwLeft IsNot Nothing AndAlso Me.tvwLeft.SelectedNode IsNot Nothing AndAlso Me.tvwLeft.SelectedNode.Parent IsNot Nothing Then
            Me.tvwLeft.SelectedNode = Me.tvwLeft.SelectedNode.Parent
        End If
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        If Me.tvwLeft.SelectedNode IsNot Nothing Then
            Me.LoadTreeNode(Me.tvwLeft.SelectedNode, True, True)
            '刷新后重新更新缩略图。
            Me.UpdateThumbnailOtherThread(Me.GetCurrentListView)
        End If
    End Sub

    Private Sub txtSearchFile_ClearClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchFile.ClearClick
        Me.StopSrarch()
    End Sub

    Private Sub txtSearchFile_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtSearchFile.KeyDown
        If e.KeyCode = Keys.Enter AndAlso Me.txtSearchFile.Text.Length > 0 Then

            If Me.tvwLeft.SelectedNode.Name <> "Search" Then
                Me.mSearchPath = Me.tvwLeft.SelectedNode.Name
            End If

            Dim searchNode As TreeNode = Me.tvwLeft.Nodes("Search")
            If searchNode Is Nothing Then
                Me.tvwLeft.Nodes.Add(Me.CreateTreeNode("Search", Me.Language.GetString("File.TreeNode.FileSearchResult")))      '文件查找结果
                searchNode = Me.tvwLeft.Nodes("Search")
            End If
            Me.tvwLeft.SelectedNode = searchNode

            Me.GetCurrentListView.Items.Clear()
            'TODO Search
            Me.DoSearch()

        End If
    End Sub

    Private Sub tsmiOpen_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiOpen.Click
        Me.OpenItem(Me.GetCurrentListView)
    End Sub

    Private Sub tsmiCopyPath_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiCopyPath.Click
        If Me.GetCurrentListView.SelectedItems.Count > 0 Then
            Dim strPath As String = Me.GetCurrentListView.SelectedItems(0).Name
            Clipboard.SetText(strPath)
            tbMessageBox.Show(Me, strPath & vbCrLf & Me.Language.GetString("Welcome.Label.CopyToClip"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
        End If
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click, tsmiExport.Click
        Me.DoExport()
    End Sub

    Private Sub btnAbrot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAbrot.Click
        Try
            If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                Me.mThrExport.Abort()
            End If
        Catch ex As Exception
        End Try
        Me.SetContralStatus(True, False, "", 0)
    End Sub

    Private Sub frmBackupPreview_QueryContinueDrag(ByVal sender As System.Object, ByVal e As System.Windows.Forms.QueryContinueDragEventArgs) Handles Me.QueryContinueDrag
        If e.Action = DragAction.Drop Then
            Dim listItem As List(Of ListViewItem) = Me.mDataObject.GetData("FileDrop", True)
            Dim strTempDropFolder As String = Path.Combine(Folder.TempFolder, "DropBackUpFolder")
            Folder.CheckFolder(strTempDropFolder)
            Dim objPara As Object = New Object() {strTempDropFolder, listItem, False}

            Me.DoExportThread(objPara)

            If Me.mCopyFiles.Count > 0 Then
                Dim obj As Object = mDataObject.GetData("FileDrop")
                obj = Me.mCopyFiles.ToArray
                mDataObject.SetData("FileDrop", obj)
            End If

            e.Action = DragAction.Drop
        End If
    End Sub

    Private Sub btnView_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnView.Click
        Me.OpenItem(Me.GetCurrentListView())
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        Try
            Dim lvw As ListView = Me.GetCurrentListView()
            If lvw.SelectedItems.Count = 0 Then
                Return
            End If

            '您确定要删除选中的 {0} 个对象吗？
            Dim strMessage As String = Me.Language.GetString("File.Message.CheckIfDelete")
            If tbMessageBox.Show(IIf(Me.Parent Is Nothing, Me, Me.mApplication),
                          strMessage,
                          Me.Language.GetString("Common.Info"),
                          MessageBoxButtons.OKCancel,
                          MessageBoxIcon.Warning,
                          MessageBoxDefaultButton.Button2) <> Windows.Forms.DialogResult.OK Then  '"信息"
                Return
            End If

            Dim listDelItem As New List(Of String)
            Dim blnSucceed As Boolean = True
            Try
                '收集要删除的文件的信息
                Me.SetContralStatus(False, True, Me.Language.GetString("WeiBo.Message.Loading"), -1)

                Dim listDel As New List(Of ListViewItem)
                For Each item As ListViewItem In lvw.SelectedItems
                    listDel.Add(item)
                Next

                '收集用户选中的删除的文件信息
                Dim listFile As New List(Of MBFileRecord)
                Dim listDelFile As New List(Of MBFileRecord)

                For Each Item As ListViewItem In listDel
                    If Item.Tag Is Nothing Then
                        Continue For
                    End If

                    listDelItem.Add(Item.Name)

                    If TypeOf Item.Tag Is ListViewItemType AndAlso Item.Tag = ListViewItemType.Folder Then
                        For Each itemRecord As MBFileRecord In Me.mLstMBFileRecord
                            Application.DoEvents()

                            If itemRecord.PathOnPhone.StartsWith(Item.Name) AndAlso itemRecord.PathOnPhone.TrimEnd("/").Length > Item.Name.TrimEnd("/").Length Then
                                If Not Me.IsFolder(itemRecord) Then
                                    listFile.Add(itemRecord)
                                End If
                            End If
                        Next
                    ElseIf TypeOf Item.Tag Is MBFileRecord Then
                        Application.DoEvents()

                        listFile.Add(Item.Tag)
                    End If
                Next

                Me.SetContralStatus(False, True, String.Format(Me.Language.GetString("Backup.Message.ExportFiles"), ""), 0)

                Dim index As Integer = 0
                '开始删除文件
                For Each Item As MBFileRecord In listFile
                    Try
                        '"正在删除文件 {0}"
                        Me.SetContralStatus(True, True, String.Format(Me.Language.GetString("File.Label.DeletingFiles"), Item.PathOnPhone), index * 100 \ listFile.Count)
                        Application.DoEvents()

                        If File.Exists(Item.PathOnPC) Then
                            File.Delete(Item.PathOnPC)
                        End If
                        Me.mLstMBFileRecord.Remove(Item)
                    Catch ex As Exception
                    End Try
                Next

                For Each Item As ListViewItem In listDel
                    lvw.Items.Remove(Item)
                Next
                Me.SetFileCount(lvw.Items.Count, 0)
            Catch ex As Exception
                blnSucceed = False
                Me.SetContralStatus(True, True, Me.Language.GetString("Backup.Message.DeleteFailure"), 0)      '导出失败！

            End Try

            If blnSucceed Then
                Me.SetContralStatus(True, True, Me.Language.GetString("Music.Message.DeleteSuccessed"), 100)       '导出成功！

                mbdb.Write(mBackObject.FolderPath, listDelItem)

                Utility.WaitSeconds(3)

                Me.SetContralStatus(True, False, "", 0)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmBackupPreview.DoDelete")
        End Try
    End Sub

    Private Sub btnSizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.SizeChanged, btnExport.SizeChanged, btnView.SizeChanged
        Me.btnView.Location = New Point(Me.btnExport.Right + 10, Me.btnExport.Top)
        Me.btnDelete.Location = New Point(Me.btnView.Right + 10, Me.btnExport.Top)
        'Me.btnRefresh.Location = New Point(Me.btnDelete.Right + 10, Me.btnRefresh.Top)
    End Sub

#End Region

#Region "--- 查找功能 ---"

    Private mThrSearch As Thread

    Private Sub DoSearch()
        Try
            If Me.mThrSearch IsNot Nothing AndAlso Me.mThrSearch.ThreadState <> ThreadState.Stopped Then
                Me.mThrSearch.Abort()
            End If
            Me.mThrSearch = New Thread(AddressOf DoSearchThread)
            With Me.mThrSearch
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
        End Try
    End Sub

    Private Sub DoSearchThread()
        Try
            Dim lstItems As New List(Of ListViewItem)
            For Each Item As MBFileRecord In Me.mLstMBFileRecord
                If Item.PathOnPhone.StartsWith(Me.mSearchPath) AndAlso Item.PathOnPhone.TrimEnd("/").Length > Me.mSearchPath.TrimEnd("/").Length _
                   AndAlso Path.GetFileName(Item.PathOnPhone).ToLower.Contains(Me.txtSearchFile.Text.Trim().ToLower) Then

                    If Me.IsFolder(Item) Then
                        Dim strText As String = Path.GetFileName(Item.PathOnPhone)
                        Dim lvwItem As ListViewItem = Me.CreateListViewItem(strText, "Folder", New String() {"", Me.Language.GetString("Common.Folder"), Item.CreateTime.ToString(), "", ""}, Item.PathOnPhone, ListViewItemType.Folder)
                        lstItems.Clear()
                        lstItems.Add(lvwItem)
                        Me.AddListViewItem(Me.GetCurrentListView, lstItems)

                    Else
                        Dim strText As String = Path.GetFileName(Item.PathOnPhone)
                        Dim lvwItem As ListViewItem = Me.CreateListViewItem(strText, Me.GetFileType(Item.PathOnPhone),
                                                      New String() {Utility.FormatFileSize(Item.Size), Me.Language.GetString("Common.File"), Item.CreateTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), "", ""}, Item.PathOnPhone, Item)
                        lstItems.Clear()
                        lstItems.Add(lvwItem)
                        Me.AddListViewItem(Me.GetCurrentListView, lstItems)
                    End If
                    Me.SetFileCount(Me.GetCurrentListView.Items.Count, 0)
                    If Me.CheckIfImg(Item.PathOnPhone) Then
                        Application.DoEvents()
                        Me.UpdateThumbnailOther(Me.mListViewSearch)
                    End If
                End If
            Next
            lstItems.Clear()
            lstItems = Nothing
        Catch ex As Exception
        End Try
    End Sub

    Private Sub StopSrarch()
        Try
            If Me.mThrSearch IsNot Nothing AndAlso Me.mThrSearch.ThreadState <> ThreadState.Stopped Then
                Me.mThrSearch.Abort()
            End If
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 导出文件 ---"

    Private mThrExport As Thread
    Private Sub DoExport()
        If Me.GetCurrentListView.SelectedItems.Count <= 0 Then
            Return
        End If

        Dim dlg As New FolderBrowserDialog
        If dlg.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
            Dim path As String = dlg.SelectedPath
            Me.SetContralStatus(False, True, "", 0)
            Dim lstListviewItem As New List(Of ListViewItem)

            For Each Item As ListViewItem In Me.GetCurrentListView.SelectedItems
                lstListviewItem.Add(Item)
            Next

            Dim objPara As Object = New Object() {path, lstListviewItem, True}
            Try
                If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                    Me.mThrExport.Abort()
                End If
                Me.mThrExport = New Thread(New ParameterizedThreadStart(AddressOf DoExportThread))
                With Me.mThrExport
                    .IsBackground = True
                    .Start(objPara)
                End With
            Catch ex As Exception
                Me.SetContralStatus(True, True, Me.Language.GetString("Contact.Message.ExportFailure"), 0)      '导出失败！

            End Try
        End If
    End Sub

    Private Sub DoExportThread(ByVal objPara As Object)
        Try
            Me.mCopyFiles.Clear()
            Dim strExportPath As String = objPara(0)
            Dim lstListViewItem As List(Of ListViewItem) = objPara(1)
            Dim blnWaitWhenSucceed As Boolean = objPara(2)
            Dim blnSucceed As Boolean = True
            Try
                '收集要导出的文件的信息

                If lstListViewItem IsNot Nothing AndAlso lstListViewItem.Count >= 0 Then
                    Me.SetContralStatus(False, True, Me.Language.GetString("WeiBo.Message.Loading"), -1)
                    '收集用户选中的导出文件信息

                    Dim listFiles As New List(Of BackupExportObject)
                    Dim intFileCount As Integer = 0
                    Dim longFileLength As Long = 0
                    For Each Item As ListViewItem In lstListViewItem
                        If Item.Tag IsNot Nothing Then
                            If TypeOf Item.Tag Is ListViewItemType AndAlso Item.Tag = ListViewItemType.Folder Then
                                Me.mCopyFiles.Add(strExportPath.TrimEnd("\") & "\" & Path.GetFileName(Item.Name.TrimEnd("/")))
                                Application.DoEvents()
                                For Each itemRecord As MBFileRecord In Me.mLstMBFileRecord
                                    Application.DoEvents()
                                    If itemRecord.PathOnPhone.StartsWith(Item.Name) AndAlso itemRecord.PathOnPhone.TrimEnd("/").Length > Item.Name.TrimEnd("/").Length Then
                                        Dim obj As New BackupExportObject
                                        obj.FileRecord = itemRecord
                                        obj.FileType = IIf(Me.IsFolder(itemRecord), ListViewItemType.Folder, ListViewItemType.File)
                                        obj.FileSavePath = strExportPath.TrimEnd("\") & "\" & Path.GetFileName(Item.Name.TrimEnd("/")) & "\" & itemRecord.PathOnPhone.Replace(Item.Name.TrimEnd("/") & "/", "").Replace("/", "\")
                                        listFiles.Add(obj)
                                        If obj.FileType = ListViewItemType.File Then
                                            longFileLength += 1
                                            longFileLength += obj.FileRecord.Size
                                        End If
                                    End If
                                Next
                            ElseIf TypeOf Item.Tag Is MBFileRecord Then
                                Application.DoEvents()
                                Dim obj As New BackupExportObject
                                obj.FileRecord = Item.Tag
                                obj.FileType = ListViewItemType.File
                                obj.FileSavePath = strExportPath.TrimEnd("\") & "\" & Path.GetFileName(CType(Item.Tag, MBFileRecord).PathOnPhone)
                                listFiles.Add(obj)
                                longFileLength += 1
                                longFileLength += obj.FileRecord.Size
                                Me.mCopyFiles.Add(obj.FileSavePath)
                            End If
                        End If
                    Next
                    Me.SetContralStatus(False, True, String.Format(Me.Language.GetString("Backup.Message.ExportFiles"), ""), 0)
                    '开始导出文件

                    Dim longFileCopyLength As Long = 0
                    For Each Item As BackupExportObject In listFiles
                        Application.DoEvents()
                        If Item.FileType = ListViewItemType.Folder Then
                            Folder.CheckFolder(Item.FileSavePath)
                        Else
                            Try
                                '"正在导出文件 {0}"
                                Me.SetContralStatus(True, True, String.Format(Me.Language.GetString("Backup.Message.ExportFiles"), Item.FileRecord.PathOnPhone),
                                                    CType(longFileCopyLength * 100 / longFileLength, Integer))
                                Folder.CheckFolder(Path.GetDirectoryName(Item.FileSavePath))
                                If File.Exists(Item.FileRecord.PathOnPC) Then
                                    File.Copy(Item.FileRecord.PathOnPC, Item.FileSavePath, True)
                                End If
                                longFileCopyLength += Item.FileRecord.Size
                            Catch ex As Exception
                            End Try
                        End If
                    Next
                End If
            Catch ex As Exception
                blnSucceed = False
                Me.SetContralStatus(True, True, Me.Language.GetString("Contact.Message.ExportFailure"), 0)      '导出失败！

            End Try

            If blnSucceed = True Then
                Me.SetContralStatus(True, True, Me.Language.GetString("Note.Message.ExportSucceed"), 100)       '导出成功！

                If blnWaitWhenSucceed Then
                    Utility.WaitSeconds(3)
                End If
                Me.SetContralStatus(True, False, "", 0)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportThread")
        End Try
    End Sub

    Private Delegate Sub SetContralStatusHandler(ByVal blnContralEnable As Boolean, ByVal blnProgressViselble As Boolean, ByVal strMessage As String, ByVal intProgressValues As Integer)
    Private Sub SetContralStatus(ByVal blnEnabled As Boolean, ByVal blnProgressViselble As Boolean, ByVal strMsg As String, ByVal intProgress As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New SetContralStatusHandler(AddressOf SetContralStatus), blnEnabled, blnProgressViselble, strMsg, intProgress)
        Else
            Me.tvwLeft.Enabled = blnEnabled
            Me.pnlControl.Enabled = blnEnabled
            Me.pnlFilePath.Enabled = blnEnabled
            Me.btnExport.Enabled = blnEnabled
            Me.btnView.Enabled = blnEnabled
            Me.btnDelete.Enabled = blnEnabled

            Me.ShowProgressStatus(blnProgressViselble)

            Me.lblProgress.Text = strMsg
            If intProgress = -1 Then
                Me.pbarProgress.tbPlayValue = 0
                Me.pbarProgress.tbIsWaiting = True
            Else
                Me.pbarProgress.tbIsWaiting = False
                Me.pbarProgress.tbPlayValue = intProgress
            End If

        End If
    End Sub

#End Region

#Region "--- 查找备份失败记录 ---"

    Private Sub LoadFailuerBackUp()
        If Me.mLstMBFileRecord Is Nothing Then
            Me.mLstMBFileRecord = New List(Of MBFileRecord)
        End If

        If Me.CheckFolderEmpty(Me.mBackObject.FolderPath) Then
            Return
        End If

        '图片/var/mobile/ 可能存在的域有三个MediaDomain  CameraRollDomain HomeDomain
        Me.mLstMBFileRecord.AddRange(Me.CreatePicPath(Me.mBackObject.FolderPath))
      
        '短信
        Me.mLstMBFileRecord.AddRange(Me.CreateSMSPath("MediaDomain", Me.mBackObject.FolderPath))
        Me.mLstMBFileRecord.AddRange(Me.CreateSMSPath("CameraRollDomain", Me.mBackObject.FolderPath))
        Me.mLstMBFileRecord.AddRange(Me.CreateSMSPath("HomeDomain", Me.mBackObject.FolderPath))

        '联系人
        Me.mLstMBFileRecord.AddRange(Me.CreateAddressofBookPath("MediaDomain", Me.mBackObject.FolderPath))
        Me.mLstMBFileRecord.AddRange(Me.CreateAddressofBookPath("CameraRollDomain", Me.mBackObject.FolderPath))
        Me.mLstMBFileRecord.AddRange(Me.CreateAddressofBookPath("HomeDomain", Me.mBackObject.FolderPath))

        '备忘录
        Me.mLstMBFileRecord.AddRange(Me.CreateNotesPath("MediaDomain", Me.mBackObject.FolderPath))
        Me.mLstMBFileRecord.AddRange(Me.CreateNotesPath("CameraRollDomain", Me.mBackObject.FolderPath))
        Me.mLstMBFileRecord.AddRange(Me.CreateNotesPath("HomeDomain", Me.mBackObject.FolderPath))

        '通话记录
        Me.mLstMBFileRecord.AddRange(Me.CreateCallHistoryPath("WirelessDomain", Me.mBackObject.FolderPath))
    End Sub

    Private Function CheckFolderEmpty(ByVal strPath As String) As Boolean
        Dim blnReturn As Boolean = False
        Try
            If Directory.GetFiles(strPath).Length <= 0 AndAlso Directory.GetDirectories(strPath).Length <= 0 Then
                blnReturn = True
            End If
        Catch ex As Exception
        End Try

        Return blnReturn
    End Function



    Private Function CreatePicPath(ByVal dirBackup As String) As List(Of MBFileRecord)
        Dim lstReturn As New List(Of MBFileRecord)

        Dim sha1 As New System.Security.Cryptography.SHA1CryptoServiceProvider()

        Dim listDomain As New List(Of String)
        listDomain.Add("CameraRollDomain")
        listDomain.Add("MediaDomain")
        listDomain.Add("HomeDomain")

        Dim listFormat As New List(Of String)
        listFormat.Add("{0}Media/DCIM/{1}APPLE/IMG_{2}.JPG")
        listFormat.Add("{0}Media/DCIM/{1}APPLE/IMG_{2}.PNG")
        listFormat.Add("{0}Media/DCIM/{1}APPLE/IMG_{2}.MOV")
        listFormat.Add("{0}Media/DCIM/{1}APPLE/IMG_{2}.GIF")

        listFormat.Add("{0}Media/PhotoData/Sync/{1}SYNCD/IMG_{2}.JPG")
        listFormat.Add("{0}Media/PhotoData/Sync/{1}SYNCD/IMG_{2}.PNG")
        listFormat.Add("{0}Media/PhotoData/Sync/{1}SYNCD/IMG_{2}.MOV")
        listFormat.Add("{0}Media/PhotoData/Sync/{1}SYNCD/IMG_{2}.MP4")
        listFormat.Add("{0}Media/PhotoData/Sync/{1}SYNCD/IMG_{2}.M4V")
        listFormat.Add("{0}Media/PhotoData/Sync/{1}SYNCD/IMG_{2}.GIF")

        listFormat.Add("{0}Media/PhotoData/Thumbnails/V2/DCIM/{1}APPLE/IMG_{2}.JPG")
        listFormat.Add("{0}Media/PhotoData/Thumbnails/V2/DCIM/{1}APPLE/IMG_{2}.JPG/5005.JPG")
        listFormat.Add("{0}Media/PhotoData/Mutations/DCIM/{1}APPLE/IMG_{2}")
        listFormat.Add("{0}Media/PhotoData/Mutations/DCIM/{1}APPLE/IMG_{2}/Adjustments")
        listFormat.Add("{0}Media/PhotoData/Mutations/DCIM/{1}APPLE/IMG_{2}/Adjustments/IMG_{2}.JPG")
        listFormat.Add("{0}Media/PhotoData/Mutations/DCIM/{1}APPLE/IMG_{2}/Adjustments/FullSizeRender.jpg")
        listFormat.Add("{0}Media/PhotoData/Mutations/DCIM/{1}APPLE/IMG_{2}/Adjustments/SubstandardFullSizeRender.jpg")
        'listFormat.Add("{0}Media/PhotoData/Thumbnails/V2/PhotoStreamsData/1300381697/{1}APPLE/IMG_{2}.JPG")

        For Each strDomain As String In listDomain
            For intI As Integer = 100 To 109
                For intJ As Integer = 1 To 999
                    For Each strFormat As String In listFormat
                        Dim strPath As String = String.Format(strFormat, String.Empty, intI.ToString(), (intJ + (intI - 100) * 1000).ToString().PadLeft(4, "0"c))
                        GetFileRecord(strDomain, strPath, dirBackup, sha1, lstReturn)
                    Next
                Next
            Next
        Next


        'For index As Integer = 1 To 9999
        '    For index1 As Integer = 0 To 9
        '        Dim strKey As String = String.Format("Media/DCIM/1{1}APPLE/IMG_{0}.JPG", index.ToString.PadLeft(4, "0"), index1.ToString.PadLeft(2, "0"))
        '        GetFileRecord(strDomain, strPath, dirBackup, sha1, lstReturn)
        '    Next
        'Next

        'For index As Integer = 1 To 9999
        '    For index1 As Integer = 0 To 9
        '        Dim strKey As String = String.Format("Media/PhotoData/1{1}APPLE/IMG_{0}.JPG", index.ToString.PadLeft(4, "0"), index1.ToString.PadLeft(2, "0"))
        '        GetFileRecord(strDomain, strPath, dirBackup, sha1, lstReturn)
        '    Next
        'Next

        'For index As Integer = 1 To 9999
        '    For index1 As Integer = 0 To 9
        '        Dim strKey As String = String.Format("Media/DCIM/1{1}APPLE/IMG_{0}.PNG", index.ToString.PadLeft(4, "0"), index1.ToString.PadLeft(2, "0"))
        '        GetFileRecord(strDomain, strPath, dirBackup, sha1, lstReturn)
        '    Next
        'Next

        'For index As Integer = 1 To 9999
        '    For index1 As Integer = 0 To 9
        '        Dim strKey As String = String.Format("Media/PhotoData/1{1}APPLE/IMG_{0}.PNG", index.ToString.PadLeft(4, "0"), index1.ToString.PadLeft(2, "0"))
        '        GetFileRecord(strDomain, strPath, dirBackup, sha1, lstReturn)
        '    Next
        'Next

        'For index As Integer = 1 To 9999
        '    For index1 As Integer = 0 To 9
        '        Dim strKey As String = String.Format("Media/DCIM/1{1}APPLE/IMG_{0}.MOV", index.ToString.PadLeft(4, "0"), index1.ToString.PadLeft(2, "0"))
        '        GetFileRecord(strDomain, strPath, dirBackup, sha1, lstReturn)
        '    Next
        'Next

        'For index As Integer = 1 To 9999
        '    For index1 As Integer = 0 To 9
        '        Dim strKey As String = String.Format("Media/PhotoData/1{1}APPLE/IMG_{0}.MOV", index.ToString.PadLeft(4, "0"), index1.ToString.PadLeft(2, "0"))
        '        GetFileRecord(strDomain, strPath, dirBackup, sha1, lstReturn)
        '    Next
        'Next

        Return lstReturn
    End Function

    Private Function CreateSMSPath(ByVal strDomain As String, ByVal dirBackup As String) As List(Of MBFileRecord)
        Dim lstReturn As New List(Of MBFileRecord)
        Dim sha1 As New System.Security.Cryptography.SHA1CryptoServiceProvider()

        Dim strKey As String = "Library/SMS/sms.db"
        Me.GetFileRecord(strDomain, strKey, dirBackup, sha1, lstReturn)
        Return lstReturn
    End Function

    Private Function CreateAddressofBookPath(ByVal strDomain As String, ByVal dirBackup As String) As List(Of MBFileRecord)
        Dim lstReturn As New List(Of MBFileRecord)
        Dim sha1 As New System.Security.Cryptography.SHA1CryptoServiceProvider()

        Dim strKey As String = "Library/AddressBook/AddressBook.sqlitedb"
        Me.GetFileRecord(strDomain, strKey, dirBackup, sha1, lstReturn)

        strKey = "Library/AddressBook/AddressBookImages.sqlitedb"
        Me.GetFileRecord(strDomain, strKey, dirBackup, sha1, lstReturn)

        Return lstReturn
    End Function

    Private Function CreateNotesPath(ByVal strDomain As String, ByVal dirBackup As String) As List(Of MBFileRecord)
        Dim lstReturn As New List(Of MBFileRecord)
        Dim sha1 As New System.Security.Cryptography.SHA1CryptoServiceProvider()

        Dim strKey As String = "Library/Notes/notes.idx"
        Me.GetFileRecord(strDomain, strKey, dirBackup, sha1, lstReturn)

        strKey = "Library/Notes/notes.sqlite"
        Me.GetFileRecord(strDomain, strKey, dirBackup, sha1, lstReturn)

        Return lstReturn
    End Function

    Private Function CreateCallHistoryPath(ByVal strDomain As String, ByVal dirBackup As String) As List(Of MBFileRecord)
        Dim lstReturn As New List(Of MBFileRecord)
        Dim sha1 As New System.Security.Cryptography.SHA1CryptoServiceProvider()

        Dim strKey As String = "Library/CallHistory/call_history.db"
        Me.GetFileRecord(strDomain, strKey, dirBackup, sha1, lstReturn)
        Return lstReturn
    End Function

    Private Sub GetFileRecord(ByVal strDomain As String, ByVal strRelativePath As String, ByVal dirBackup As String, ByVal sha1 As System.Security.Cryptography.SHA1CryptoServiceProvider, ByVal recorde As List(Of MBFileRecord))
        Dim strKey As String = strDomain & "-" & strRelativePath     '"MediaDomain-Media/Library/AddressBook/AddressBook.sqlitedb"
        Dim arrData() As Byte = sha1.ComputeHash(System.Text.Encoding.UTF8.GetBytes(strKey))
        Dim strValue As String = Common.ToHexString(arrData)
        Dim strPathOnPC As String = Path.Combine(dirBackup, strValue)

        If File.Exists(strPathOnPC) Then
            Dim obj As New MBFileRecord()
            obj.Domain = strDomain
            obj.PathOnPC = strPathOnPC
            obj.PathOnPhone = Me.FormatPath(strDomain, strRelativePath)
            obj.relativePath = Path.GetFileName(obj.PathOnPhone)
            If recorde IsNot Nothing Then
                recorde.Add(obj)
            End If
        End If
    End Sub

    Private Function FormatPath(ByVal strDomain As String, ByVal strRelativePath As String) As String
        Dim pathOnPhone As String = strRelativePath

        Select Case strDomain
            Case "WirelessDomain"
                pathOnPhone = String.Format("/var/wireless/{0}", strRelativePath)
            Case "ManagedPreferencesDomain"
                pathOnPhone = String.Format("/var/Managed Preferences/{0}", strRelativePath)
            Case "MediaDomain"
                pathOnPhone = String.Format("/var/mobile/{0}", strRelativePath)
            Case "SystemPreferencesDomain"
                pathOnPhone = String.Format("/var/preferences/{0}", strRelativePath)
            Case "CameraRollDomain"
                pathOnPhone = String.Format("/var/mobile/{0}", strRelativePath)
            Case "RootDomain"
                pathOnPhone = String.Format("/var/root/{0}", strRelativePath)
            Case "MobileDeviceDomain"
                pathOnPhone = String.Format("/var/MobileDevice/{0}", strRelativePath)
            Case "KeychainDomain"
                pathOnPhone = String.Format("/var/Keychains/{0}", strRelativePath)
            Case "HomeDomain"
                pathOnPhone = String.Format("/var/mobile/{0}", strRelativePath)
            Case "DatabaseDomain"
                pathOnPhone = String.Format("/var/db/{0}", strRelativePath)
            Case Else
                If strDomain.StartsWith("AppDomain-") Then
                    pathOnPhone = String.Format("/var/mobile/Applications/{0}/{1}", strDomain.Replace("AppDomain-", ""), strRelativePath)
                End If
        End Select
        Return pathOnPhone
    End Function

#End Region

End Class

Public Class BackupExportObject
    Public FileRecord As MBFileRecord
    Public FileSavePath As String = ""
    Public FileType As ListViewItemType = ListViewItemType.File
End Class


