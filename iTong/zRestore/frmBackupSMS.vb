﻿Imports System.Threading
Imports System.Text.RegularExpressions
Public Class frmBackupSMS

    Private mStrDBFilePath As String = ""
    Private mDgvGroup As New tbDataGridViewEx
    Private mThrLoadData As Thread
    Private mSMSGroup As List(Of SMSGroup)
    Private mLstMBFileRecord As List(Of MBFileRecord)
    Private mBackupdb As BackupSMSDB
    Private mSMSDBHelper As SMSDBHelper
    Private mBackupContactdb As BackupContactDB
    Private mThrExport As Thread
    Private mExportHelper As SmsExportHelper

    Private wbsChat As tbWebBrowserEx
    Private mPathHtml As String = String.Empty
    Private mstrIconPath As String = ""
    Private mLstContact As List(Of Contact)
    Private mBackObject As BackupInfo
    Private mblnLoadFirst As Boolean = True
    Private mblnCheckBoxSelected As Boolean = False
    Private Shared blnLoaded As Boolean = False
    Private mCurrentGroup As SMSGroup = Nothing

    Private mintBackProgress As Integer = 0
    Private mShowTime As Integer = 0
    Private tmrShowCount As New System.Timers.Timer(1000)

    Private mIsCheckLoadAloneBackup As Boolean = True

    Private mIsLoadAloneBackup As Boolean = False
    Private mStrAloneBackupPath As String = ""

    Private mIsUnsaved As Boolean = False

    'Private mblnIsPwdBackUp As Boolean = False

    '聊天数据的来源

    Private Enum DataType
        Normal = 0              '普通展示
        GetNews = 1             '查看更多消息
        Search = 2              '搜索
        TimeLocate = 3          '时间定位
        GetContextNews = 4      '查看前后消息
    End Enum

    Private Enum SetViewType
        None
        Loading
        Empty
        PwdBackup
    End Enum

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal iPhone As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.Icon = My.Resources.iTong
        Me.tbAutoSetFormSize = True
        Me.Size = New Size(840, 600)
        Me.CanResize = True
        Me.FilletRadius = 5
        Me.mDevice = iPhone
    End Sub

    Public Sub New(ByVal application As IApplication, ByVal strDBFilePath As String, ByVal lstMBFileRecord As List(Of MBFileRecord), ByVal backupObj As BackupInfo)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.Icon = My.Resources.iTong
        Me.tbAutoSetFormSize = True
        Me.Size = New Size(840, 600)
        Me.CanResize = True
        Me.FilletRadius = 5
        Me.mStrDBFilePath = strDBFilePath
        Me.mLstMBFileRecord = lstMBFileRecord
        Me.mBackObject = backupObj
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.mstrIconPath = Path.Combine(Folder.TempFolder, String.Format("SMSIcon\{0}\", Guid.NewGuid.ToString()))
        Folder.CheckFolder(Me.mstrIconPath)

        Me.InitGridView()
        Me.InitWabBrowser()
        Me.LoadDataThread(Not blnLoaded)
        'Me.mExportHelper = New SmsExportHelper(Me.Language)

        Me.InitOtherControls()
        Me.SetPanelView(SetViewType.Loading)
        Me.SetCheckView()

        MainForm.SetButtonStatus(Me.btnImport)
        MainForm.SetButtonStatus(Me.btnExport)
        MainForm.SetButtonStatus(Me.btnDelete)
        MainForm.SetButtonStatus(Me.btnSave)
        MainForm.SetButtonStatus(Me.btnRefresh)

        Me.btnDataRecovery.Visible = ServerIniSetting.IsShowTBGuide()
        Me.btnDataRecoveryEx.Visible = ServerIniSetting.IsShowTBGuide()
        AddHandler Me.tmrShowCount.Elapsed, AddressOf tmrShowCount_Elapsed

        '人工找回数据 红点
        If IniSetting.GetShowSMSDataRecoveryNew() Then
            Me.btnDataRecovery.tbShowDot = True
        End If
        Me.SetLoadFromBackup(False, "")
    End Sub

    Private Sub InitOtherControls()
        Me.btnDelete.Visible = False
        Me.btnSave.Visible = False

        Me.pnlFileEmpty.SendToBack()
        Me.pnlFileEmpty.Size = New Size(Me.Width - 2, Me.tlpMain.Height + Me.pnlControl.Height)
        Me.pnlFileEmpty.Location = New Point(Me.pnlControl.Left, Me.pnlControl.Top)

        Me.pnlDescription.SendToBack()
        Me.pnlDescription.Size = New Size(Me.Width - 2, Me.tlpMain.Height + Me.pnlControl.Height)
        Me.pnlDescription.Location = New Point(Me.pnlControl.Left, Me.pnlControl.Top)

        Me.pnlLoading.Location = Me.pnlControl.Location
        Me.pnlLoading.Size = New Size(Me.pnlControl.Width, Me.tlpMain.Bottom - Me.pnlControl.Top)
        Me.pnlLoading.BringToFront()
        Me.pnlLoading.Visible = True

        If Not Folder.LangType = LanguageType.en_US Then
            Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lblTitle.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.lblCancelPwdBackup.Font = Common.CreateFont("微软雅黑", 16.0!, FontStyle.Regular)
        Me.lblStep1Description.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.lblStep2Description.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.btnReCheckEx1.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)

        Me.Text = Me.Language.GetString("Backup.Label.SMS")                                         '"短信"
        Me.btnImport.Text = Me.Language.GetString("Media.Button.Import")                            '"导入"
        Me.btnExport.Text = Me.Language.GetString("Media.Button.Export")                            '"导出"
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")                                  '"删除"
        Me.btnSave.Text = Me.Language.GetString("Common.Button.Save")                               '"保存"
        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh")                    '"刷新"
        Me.tsmiExportToTxt.Text = Me.Language.GetString("Backup.Button.ExportToTxt")                '"导出到Txt"
        Me.tsmiExportToTxtAll.Text = Me.Language.GetString("Contact.Menu.ExportAll")                '"导出全部"
        Me.tsmiExportToExcel.Text = Me.Language.GetString("Backup.Button.ExportToExcel")            '"导出到Excel"
        Me.tsmiExportToExcelAll.Text = Me.Language.GetString("Contact.Menu.ExportAll")              '"导出全部"
        Me.lblFileEmpty.Text = Me.Language.GetString("Media.Label.Empty")                           '"这里没有内容"
        Me.txtSearchFile.SearchTipText = Me.Language.GetString("File.Label.Search")                 '"查找"
        Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading")                              '"正在载入..."
        Me.chkSelectAll.Text = Me.Language.GetString("Common.SelectAll")                            '"全选"
        Me.tsmiExportToCSV.Text = Me.Language.GetString("Backup.Button.ExportToCSV")                '"导出到CSV"
        Me.tsmiExportToCSVAll.Text = Me.Language.GetString("Contact.Menu.ExportAll")              '"导出全部"

        Me.btnDataRecovery.Text = Me.Language.GetString("Button.ArtificialDataRecovery") '“人工数据恢复”
        Me.btnDataRecoveryEx.Text = Me.Language.GetString("Button.ArtificialDataRecovery") '“人工数据恢复”
        'Me.btnDataRecovery.Visible = False

        Me.llblRefresh.Text = String.Format("{0}>>", Me.Language.GetString("Weixin.Button.GetLatest"))

        Me.btnImport.Location = New Point(10, Me.btnImport.Top)
        Me.btnImport.Visible = False

        Me.chkSelectAll.Location = New Point(1, Me.chkSelectAll.Top)
        Me.btnExport.Location = New Point(Me.chkSelectAll.Left, Me.btnImport.Top)
        'Me.btnExport.Location = New Point(Me.chkSelectAll.Right + 20, Me.btnImport.Top)

        Me.btnDataRecovery.Location = New Point(Me.btnExport.Right + 10, Me.btnImport.Top)

        Me.btnDelete.Location = New Point(Me.btnDataRecovery.Right + 10, Me.btnImport.Top)
        Me.btnSave.Location = New Point(Me.btnDelete.Right + 10, Me.btnImport.Top)
        Me.btnRefresh.Location = New Point(Me.txtSearchFile.Left - Me.btnRefresh.Width - 10, Me.btnRefresh.Top)

        Me.tsmiExportToTxtAll.Visible = False
        Me.tsmiExportToExcelAll.Visible = False
        Me.tsmiExportToCSVAll.Visible = False

        Me.lblCancelPwdBackup.Text = Me.Language.GetString("Backup.Message.LoadFailureByEncryption")        '"加载短信失败，请取消iTunes加密备份"
        Me.lblStep1Description.Text = Me.Language.GetString("Weixin.Lable.OpeniTunesSelectDevice")          '"打开iTunes,选择设备"
        Me.lblStep2Description.Text = Me.Language.GetString("Weixin.Lable.CancelDeviceEncryptionBackup")    '"取消该设备的备份加密"
        Me.btnReCheckEx1.Text = Me.Language.GetString("Weixin.Button.Continue")                             '"已取消，继续"
    End Sub

    Private Sub InitWabBrowser()
        If Me.wbsChat Is Nothing Then
            Me.wbsChat = New tbWebBrowserEx()
        End If
        With Me.wbsChat
            .AllowWebBrowserDrop = False
            .Dock = System.Windows.Forms.DockStyle.Fill
            .IsWebBrowserContextMenuEnabled = False
            .Jailbreaked = False
            .LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
            .Location = New System.Drawing.Point(0, 0)
            .MinimumSize = New System.Drawing.Size(20, 20)
            .Name = "wbsChat"
            .ScriptErrorsSuppressed = True
            .ShowLoadingWait = False
            .ShowNavigateErrorPage = True
            .ShowProgress = True
            .TabIndex = 0
            .WebBrowserShortcutsEnabled = True
            .ScrollBarsEnabled = True
        End With

        RemoveHandler wbsChat.Navigating, AddressOf wbChat_Navigating
        AddHandler wbsChat.Navigating, AddressOf wbChat_Navigating

        Me.pnlContainer.Controls.Add(Me.wbsChat)
        Me.wbsChat.BringToFront()
        Me.pnlCount.BringToFront()
    End Sub

    Private Sub InitGridView()
        Me.mDgvGroup = New tbDataGridViewEx
        With Me.mDgvGroup
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 32
            .AllowDrop = True
            .MultiSelect = True
            .tbNoDataText = "" 'Me.Language.GetString("Media.Label.Empty")
            .tbShowNoData = False
            .ColumnHeadersVisible = False
            .Columns.Clear()

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", _
                                                                   26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colPhoneNumber", Me.Language.GetString("Welcome.Label.PhoneNumber"), _
                                                                   165, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.True)) '电话号码
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))

            .ResumeLayout()
        End With
        RemoveHandler mDgvGroup.SelectionChanged, AddressOf mDgvGroup_SelectionChanged
        AddHandler mDgvGroup.SelectionChanged, AddressOf mDgvGroup_SelectionChanged
        Me.pnlGroups.Controls.Add(Me.mDgvGroup)
    End Sub

    Private Sub LoadDataThread(Optional ByVal blnRefresh As Boolean = False)
        Me.mintBackProgress = 0
        Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading")

        If Me.mblnLoadFirst Then
            ChatWebPage.InitChatWebPage(True)
            Me.wbsChat.Navigate(ChatWebPage.WebPagePath)
            'Me.wbsChat.Navigate("http://local.tongbu.com/wx/")
            'http://**************/tongbuwx/
            Me.mblnLoadFirst = False
        End If

        Me.mThrLoadData = New Thread(New ParameterizedThreadStart(AddressOf LoadData))
        With Me.mThrLoadData
            .IsBackground = True
            .Start(blnRefresh)
        End With
    End Sub

    Private Sub LoadData(ByVal objPara As Object)
        Try
            Dim blnRefresh As Boolean = objPara
            'Me.mblnIsPwdBackUp = False
            Me.SetButtonEnable(False)
            If Me.mSMSGroup Is Nothing Then
                Me.mSMSGroup = New List(Of SMSGroup)
            End If

            '如果用户是密码备份的手机提示用户关闭后再重新加载。
            '8.X以上的设备没越狱用备份还原的机制取得数据库。
            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 800 AndAlso Not Me.mDevice.Jailbreaked AndAlso Me.mDevice.mb2_CheckEncrypt() Then
                If Not frmWeixin.SetPwdBackup(Me, Me.mDevice, Me.Language) Then
                    Me.SetPanelView(SetViewType.PwdBackup)
                    Return
                End If
            End If

            If Me.mDevice IsNot Nothing Then
                If Me.mIsCheckLoadAloneBackup Then
                    mIsLoadAloneBackup = BackupSMSDB.GetAloneBackupMSM(Me.mDevice, mStrAloneBackupPath, mIsUnsaved)
                End If

                Dim handler As New BackupRestoreHandler(AddressOf OnBackupDBHandler)
                Me.mSMSDBHelper = SMSDBHelper.Instance(Me.mDevice, handler, blnRefresh, mIsLoadAloneBackup, mStrAloneBackupPath)
                If Not Me.mIsLoadAloneBackup Then
                    Me.mSMSDBHelper.LoadDB(Me.mDevice, handler)
                End If

                Me.mBackupdb = BackupSMSDB.Instance(Me.mSMSDBHelper.SMSDBPath, Me.mBackObject, Me.mDevice)

                Me.SetLoadFromBackup(mIsLoadAloneBackup, Me.mSMSDBHelper.SMSDBPath)
            Else
                Me.mBackupdb = BackupSMSDB.Instance(Me.mStrDBFilePath, Me.mBackObject)
            End If

            blnLoaded = True
            Me.mSMSGroup = Me.mBackupdb.GetAll()

            If mIsUnsaved AndAlso Me.mSMSGroup.Count > 0 Then
                Me.ShowSaveMSMDB(Me.mSMSDBHelper.SMSDBPath)
            End If

            Me.Invoke(New System.Threading.ThreadStart(AddressOf ClearRow))

            For Each Item As SMSGroup In Me.mSMSGroup
                Me.AddGroupRow(Item)
            Next

            Me.SetGroupSelected()
            Me.LoadName()
            Me.UpdateName()

            If Me.mSMSGroup.Count > 0 Then
                Me.SetButtonEnable(True)
                Me.SetPanelView(SetViewType.None)
            Else
                Me.SetPanelView(SetViewType.Empty)
            End If
            ''锁屏的情况文件是不存在的 sms.db。
            'If Me.mSMSGroup.Count = 0 AndAlso Not File.Exists(Me.mSMSDBHelper.SMSDBPath) Then
            '    '"读取失败，请保持屏幕处于解锁的状态再重试。"
            '    tbMessageBox.Show(Me, _
            '                      Me.Language.GetString("Backup.Message.ReadErrorUnlockScreen"), _
            '                      Me.Language.GetString("Common.Info"), _
            '                      MessageBoxButtons.OK, MessageBoxIcon.Information)
            'End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "SMS LoadData")
        End Try
    End Sub

    Private Delegate Sub SetLoadFromBackupHandler(ByVal isShow As Boolean, ByVal strPath As String)
    Public Sub SetLoadFromBackup(ByVal isShow As Boolean, ByVal strPath As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetLoadFromBackupHandler(AddressOf SetLoadFromBackup), isShow, strPath)
            Else
                If isShow Then
                    Me.tlpMain.RowStyles(2).SizeType = SizeType.Absolute
                    Me.tlpMain.RowStyles(2).Height = 24
                Else
                    Me.tlpMain.RowStyles(2).SizeType = SizeType.Percent
                    Me.tlpMain.RowStyles(2).Height = 0
                End If
                If strPath.Length = 0 OrElse Not File.Exists(strPath) Then
                    Return
                End If


                Me.lblFromBackupMsg.Text = String.Format(Me.Language.GetString("Common.Backup.DataExplain"), Path.GetFileName(strPath), String.Format("{1}：{0}", New FileInfo(strPath).LastWriteTime, Me.Language.GetString("App.Button.TimeSort"))) '当前数据不是最新，是读取【{0}-{1}】的备份文件

                Dim iFBLX As Integer = (Me.Width - Me.lblFromBackupMsg.Width - Me.llblRefresh.Width) / 2
                Me.lblFromBackupMsg.Location = New Point(iFBLX, Me.lblFromBackupMsg.Location.Y)
                Me.llblRefresh.Location = New Point(Me.lblFromBackupMsg.Left + Me.lblFromBackupMsg.Size.Width, Me.lblFromBackupMsg.Location.Y)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "SetLoadFromBackup")
        End Try
    End Sub

    Private Delegate Sub ShowSaveMSMDBHandler(ByVal strPath As String)
    Private Sub ShowSaveMSMDB(ByVal strPath As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowSaveMSMDBHandler(AddressOf ShowSaveMSMDB), strPath)
        Else
            If Not IniSetting.GetSaveSMSDB() Then
                If tbMessageBox.Show(Me, Me.Language.GetString("Common.Backup.SMSBackupInfo"), Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo) = DialogResult.No Then '"是否把短信备份保存到电脑上， 以便您需要时可以单独把短信的记录还原到设备上。 建议仅在您自己的电脑上操作。"
                    Return
                Else
                    IniSetting.SetSaveSMSDB(True)
                End If
            End If

            If Not IniSetting.GetSaveSMSDB Then
                Return
            End If

            Try
                Dim strMSMFolser As String = Path.Combine(Folder.AloneSMSBackupsFolder, Me.mDevice.Identifier)
                Folder.CheckFolder(strMSMFolser)
                Dim strDBNewName As String = String.Format("{0}-{1}.db", Path.GetFileNameWithoutExtension(strPath), Date.Now.ToString("yyyyMMddHHmmss"))
                Dim strDB As String = Path.Combine(strMSMFolser, strDBNewName)
                Dim strDBSHM As String = Path.Combine(strMSMFolser, String.Format("{0}-shm", strDBNewName))
                Dim strDBWAL As String = Path.Combine(strMSMFolser, String.Format("{0}-wal", strDBNewName))

                If File.Exists(strPath) Then
                    File.Copy(strPath, strDB)
                End If

                If File.Exists(String.Format("{0}-shm", strPath)) Then
                    File.Copy(String.Format("{0}-shm", strPath), strDBSHM)
                End If

                If File.Exists(String.Format("{0}-wal", strPath)) Then
                    File.Copy(String.Format("{0}-wal", strPath), strDBWAL)
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "ShowSaveMSMDB")
            End Try
        End If
    End Sub

    Private Delegate Sub BackupRestoreProgressHandler(ByVal e As BackupRestoreEventArgs)
    Private Sub OnBackupDBHandler(ByVal e As BackupRestoreEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New BackupRestoreProgressHandler(AddressOf OnBackupDBHandler), e)
        Else
            If e.Progress > Me.mintBackProgress Then
                'Debug.Print(e.Progress & "," & Me.mintBackProgress)
                Me.mintBackProgress = e.Progress
                Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading") & String.Format("{0}%", e.Progress.ToString("##.#"))   '"正在载入..."
                Application.DoEvents()
            End If

            'Dim type As BackupRestoreErrorType = frmBackupCreate.GetErrotType(BackupRestore.Backup, e)
            'If type = BackupRestoreErrorType.CloseBackupPwd Then
            '    Me.mblnIsPwdBackUp = True
            'End If

            If Not String.IsNullOrEmpty(e.ErrorMsg) Then
                Common.LogException("SMSBackupDB: " & e.ErrorMsg)
            End If
        End If
    End Sub

    Private Delegate Sub UpdateNameHanlder()
    Private Sub UpdateName()
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateNameHanlder(AddressOf UpdateName))
        Else
            If Me.mCurrentGroup IsNot Nothing Then
                Me.lblTitle.Text = IIf(Me.mCurrentGroup.GroupName.Length > 0, String.Format("{0}({1})", Me.mCurrentGroup.GroupName, Me.mCurrentGroup.PhoneNumber), Me.mCurrentGroup.PhoneNumber)
            End If
        End If

    End Sub

    Private Sub LoadName()
        If Me.mDevice IsNot Nothing Then
            Me.LoadNameDevice()
        Else
            Me.LoadNameBackup()
        End If
    End Sub

    'Private Sub LoadNameDevice()
    '    If Me.mDevice IsNot Nothing Then
    '        If Me.mLstContact Is Nothing OrElse Me.mLstContact.Count <= 0 Then
    '            '加载联系人信息

    '            Dim contactDB As New iPhoneContact(Me.mDevice)
    '            contactDB.Load()
    '            Me.mLstContact = contactDB.Contacts
    '        End If

    '        For Each Item As DataGridViewRow In Me.mDgvGroup.Rows
    '            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is SMSGroup Then
    '                Dim group As SMSGroup = Item.Tag
    '                Dim strName As String = Me.GetContactNameByPhoneNumber(group.PhoneNumber, Me.mLstContact)
    '                If group.GroupName.Length <= 0 AndAlso strName.Length > 0 Then
    '                    group.GroupName = strName
    '                    Item.Cells("colPhoneNumber").Value = strName
    '                End If
    '                Item.Cells("colPhoneNumber").Value = Item.Cells("colPhoneNumber").Value & String.Format("({0})", group.Messages.Count)
    '            End If
    '        Next

    '    End If
    'End Sub

    Private Sub LoadNameDevice()
        Try
            '取得联系人数据
            Dim strContactFilePath As String = String.Empty
            Dim strContactImageFilePath As String = String.Empty

            If Me.mDevice.VersionNumber >= 800 Then
                strContactFilePath = Path.Combine(Me.mSMSDBHelper.DevicePath, "AddressBook.sqlitedb")
                strContactImageFilePath = Path.Combine(Me.mSMSDBHelper.DevicePath, "AddressBookImages.sqlitedb")
            Else
                Dim strDir As String = Path.GetDirectoryName(Me.mSMSDBHelper.SMSDBPath).Replace("SMS", "AddressBook")
                strContactFilePath = Path.Combine(strDir, "AddressBook.sqlitedb")
                strContactImageFilePath = Path.Combine(strDir, "AddressBookImages.sqlitedb")

            End If

            Me.LoadNameFormDB(strContactFilePath, strContactImageFilePath)
        Catch ex As Exception
        End Try
    End Sub

    Private Sub LoadNameBackup()
        Try
            '取得联系人数据
            If mLstMBFileRecord IsNot Nothing Then
                Dim strContactFilePath As String = ""
                Dim strContactImageFilePath As String = ""
                For Each Item As MBFileRecord In Me.mLstMBFileRecord
                    If Item.PathOnPhone = "/var/mobile/Library/AddressBook/AddressBook.sqlitedb" Then
                        strContactFilePath = Item.PathOnPC
                    ElseIf Item.PathOnPhone = "/var/mobile/Library/AddressBook/AddressBookImages.sqlitedb" Then
                        strContactImageFilePath = Item.PathOnPC
                    End If
                Next
                Me.LoadNameFormDB(strContactFilePath, strContactImageFilePath)
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Sub LoadNameFormDB(ByVal strContactFilePath As String, ByVal strContactImageFilePath As String)
        Try
            If strContactFilePath.Length > 0 Then
                Me.mBackupContactdb = BackupContactDB.Instance(strContactFilePath, strContactImageFilePath)
                Me.mLstContact = Me.mBackupContactdb.GetContacts()
                For Each Item As DataGridViewRow In Me.mDgvGroup.Rows
                    If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is SMSGroup Then
                        Dim group As SMSGroup = Item.Tag
                        Dim strName As String = Me.GetContactNameByPhoneNumber(group.PhoneNumber, Me.mLstContact)
                        If group.GroupName.Length <= 0 AndAlso strName.Length > 0 Then
                            group.GroupName = strName
                            Item.Cells("colPhoneNumber").Value = strName
                        End If
                        Item.Cells("colPhoneNumber").Value = Item.Cells("colPhoneNumber").Value & String.Format("({0})", group.Messages.Count)
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException("SMS.LoadNameFormDB : " & ex.ToString)
        End Try
    End Sub

#End Region

#Region "--- webBrowser处理事件 ---"

    Private Function GetChatJson(ByVal lstMessage As List(Of SMSMessage), ByVal group As SMSGroup, ByVal type As DataType)
        Dim dicChat As JsonObject
        Dim dicChats As New JsonObject
        Dim arrChats As New JsonArray
        Dim strFrom As String = String.Empty   '0对方；1自己
        Dim strType As String = String.Empty   '0文本；3图片；49网页；43视频；34语音；1000系统消息
        Dim startTime As DateTime = DateTime.MinValue

        '倒叙
        For Each Item As SMSMessage In lstMessage
            Dim iconPath As String = String.Empty
            Dim strUsrName As String = group.PhoneNumber
            Dim strNickName As String = IIf(group.GroupName.Length = 0, group.PhoneNumber, group.GroupName)
            Dim strMsg As String = Item.Content
            Dim strTime As String = Item.CreateDate

            If Item.IsSend Then
                strFrom = "1"
                If Me.mBackObject IsNot Nothing Then
                    strUsrName = Me.mBackObject.SerialNumber
                    strNickName = Me.mBackObject.DisplayName
                ElseIf Me.mDevice IsNot Nothing Then
                    strUsrName = Me.mDevice.SerialNumber
                    strNickName = Me.mDevice.DeviceName
                End If

                iconPath = Path.Combine(Me.mstrIconPath, "default.png")
                Me.GetContactImage(Nothing, Nothing)
            Else
                strFrom = "0"
                iconPath = Me.GetContactImage(group, Me.mLstContact)
                If Not File.Exists(iconPath) Then
                    iconPath = Path.Combine(Me.mstrIconPath, "default.png")
                End If
            End If

            strType = "0"
            strMsg = GetMessagePic(Item)

            Dim strSearchText As String = Me.txtSearchFile.Text.Trim
            If strSearchText.Length > 0 Then
                'strMsg = strMsg.Replace(strSearchText, "<span  style=""background-color:#ffff00;"">" & strSearchText & "</span>")
                strMsg = strMsg.Replace(strSearchText, "~" & strSearchText & "~(background-color:#ffff00;color:#fffff;)")
            End If

            dicChat = New JsonObject()
            dicChat.Add("from", strFrom)
            dicChat.Add("type", strType)
            dicChat.Add("user", strNickName)
            dicChat.Add("icon", iconPath)
            dicChat.Add("content", strMsg)
            dicChat.Add("time", strTime)
            dicChat.Add("id", Item.MessageROWID)
            dicChat.Add("friend", group.GroupID)

            arrChats.Add(dicChat)
        Next
        dicChats.Add("data", arrChats)

        '语言包

        Dim dicLanguage As New JsonObject
        dicLanguage.Add("weixincopy", Language.GetString("Common.Button.Copy"))
        dicLanguage.Add("weixinexport", Language.GetString("Media.Button.Export"))
        dicLanguage.Add("weixingetmore", Language.GetString("Weixin.Button.GetMoreNews"))
        dicLanguage.Add("weixingetcontext", Language.GetString("Weixin.Button.GetContextNews"))

        '功能（"weixincopy","weixinexport"）
        Dim dicOption As New JsonArray
        dicOption.Add("weixincopy")

        dicChats.Add("type", type.GetHashCode.ToString())
        dicChats.Add("language", dicLanguage)
        dicChats.Add("msgOption", dicOption)

        Dim writer As New JsonWriter()
        dicChats.Write(writer)

        Return writer.ToString()
    End Function

    Private Function GetMessagePic(ByRef Item As SMSMessage) As String
        Dim strReturn As String = ""
        Try
            strReturn = Item.Content
            If Item.Pics IsNot Nothing AndAlso Item.Pics.Count > 0 Then
                For Each pic As SMSMessagePic In Item.Pics

                    If pic.Class = ImageVideo.Video Then
                        strReturn = strReturn.Replace(String.Format("#Video{0}#", pic.PicPath), String.Format("【{0}】", Me.Language.GetString("Media.Type.Video")))
                        Return strReturn
                    End If

                    Dim pathPicPC As String = Path.Combine(Me.mBackupdb.TempPath, "SMSPic\" & pic.PicPath)

                    If pic.PartID = "-1" Then
                        If Me.mDevice IsNot Nothing Then
                            pathPicPC = pic.PicPath
                        Else
                            Folder.CheckFolder(Path.GetDirectoryName(pic.PicPathOnPC))
                            Me.GetPicFormBackUp(pic.PicPathOnPC, pic.PicPath)
                            pathPicPC = pic.PicPathOnPC
                        End If

                    Else
                        Folder.CheckFolder(Path.GetDirectoryName(pathPicPC))
                        If Me.mDevice IsNot Nothing Then
                            Me.GetPicFormDevice(pathPicPC, pic.PicPath)
                        Else
                            Me.GetPicFormBackUp(pathPicPC, pic.PicPath)
                        End If
                        If Not File.Exists(pathPicPC) AndAlso pic.Picture IsNot Nothing Then
                            Try
                                pic.Picture = Utility.AdjustImage(pic.Picture)
                                pic.Picture.Save(pathPicPC, Drawing.Imaging.ImageFormat.Jpeg)
                            Catch ex As Exception
                            End Try
                        End If
                    End If

                    If File.Exists(pathPicPC) Then
                        Try
                            Dim img As Image = Common.ImageFromFile(pathPicPC)
                            Try
                                img = Utility.AdjustImage(img)
                                img.Save(pathPicPC, Drawing.Imaging.ImageFormat.Jpeg)
                            Catch ex As Exception
                            End Try

                            Dim sizeCurrent As New Size(200, 100)
                            If img IsNot Nothing Then
                                If img.Width > 200 Then
                                    sizeCurrent = New Size(200, img.Height * (200 / img.Width))
                                Else
                                    sizeCurrent = img.Size
                                End If
                            End If
                            pic.PicPathOnPC = pathPicPC
                            If pic.PartID = "-1" Then
                                'strReturn = strReturn.Replace(String.Format("#Pic{0}#", 1), String.Format("<img src=""{0}"" width=""{1}px"" height=""{2}px""/>", pathPicPC, sizeCurrent.Width, sizeCurrent.Height))
                                strReturn = strReturn.Replace(String.Format("#Pic{0}#", 1), String.Format("^{0}^({1},{2})", pathPicPC, sizeCurrent.Width, sizeCurrent.Height))
                            Else
                                strReturn = strReturn.Replace(String.Format("#Pic{0}#", pic.PartID), String.Format("^{0}^({1},{2})", pathPicPC, sizeCurrent.Width, sizeCurrent.Height))
                            End If
                        Catch ex As Exception
                        End Try
                    Else
                        strReturn = strReturn.Replace(String.Format("#Pic{0}#", pic.PartID), String.Format("【{0}】", Me.Language.GetString("Components.ImageView.Label.Current")))
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException("Get Message Picture Error:" & ex.ToString)
        End Try
        Return strReturn
    End Function

    Private Sub GetPicFormBackUp(ByVal pathPicPC As String, ByVal strPath As String)
        If Not File.Exists(pathPicPC) AndAlso Me.mLstMBFileRecord IsNot Nothing Then
            For Each recorde As MBFileRecord In mLstMBFileRecord
                Application.DoEvents()
                If recorde.PathOnPhone.StartsWith("/var/mobile/Library/SMS/") AndAlso recorde.PathOnPhone.EndsWith(strPath) Then
                    Try
                        File.Copy(recorde.PathOnPC, pathPicPC)
                        Exit For
                    Catch ex As Exception
                    End Try
                End If
            Next
        End If
    End Sub

    Private Sub GetPicFormDevice(ByVal pathPicPC As String, ByVal strPath As String)
        If Me.mDevice IsNot Nothing Then
            Dim downloadPicPath As String = Path.Combine(Folder.TempFolder, Me.mDevice.Identifier & "\UserDatabases\var\mobile\Library\SMS\Parts")
            Dim intIndex As Integer = 0
            For Each picPath As String In Directory.GetFiles(downloadPicPath, "*.*", SearchOption.AllDirectories)
                intIndex += 1
                If intIndex Mod 5 = 0 Then
                    Application.DoEvents()
                End If

                If picPath.EndsWith(strPath) AndAlso Not File.Exists(pathPicPC) Then
                    Try
                        File.Copy(picPath, pathPicPC)
                        '矫正手机上的图片
                        Dim img As Image = Common.ImageFromFile(pathPicPC)
                        Common.ImageToCPBitmap(img, pathPicPC)
                        Exit For
                    Catch ex As Exception
                    End Try
                End If
            Next
        End If
    End Sub

    Private Function GetContactImage(ByVal group As SMSGroup, ByVal lstContact As List(Of Contact)) As String
        Dim strPath As String = Path.Combine(Me.mstrIconPath, "default.png")
        If Not File.Exists(strPath) Then
            Try
                Dim img As Image = My.Resources.weixin_icon_default.Clone
                img.Save(strPath, Imaging.ImageFormat.Png)
                img = Nothing
            Catch ex As Exception
            End Try
        End If

        If group Is Nothing OrElse lstContact Is Nothing Then
            Return strPath
        End If

        For Each Item As Contact In lstContact
            If Item.PhoneNumbers.Count > 0 Then
                For Each phone As ContactItem In Item.PhoneNumbers
                    If phone.Value.Replace("+86", "").Replace("-", "").Trim = group.PhoneNumber.Replace("+86", "").Replace("-", "").Trim AndAlso Item.Photo IsNot Nothing Then
                        strPath = Path.Combine(Me.mstrIconPath, String.Format("{0}_Img.png", group.GroupID))

                        If Not File.Exists(strPath) Then
                            Try
                                Dim img As Image = Utility.GetThumbnail(Item.Photo.Clone, New Size(50, 50), True)
                                img.Save(strPath, Imaging.ImageFormat.Png)
                                img = Nothing
                            Catch ex As Exception
                            End Try
                        End If
                    End If
                Next
            End If
        Next
        Return strPath
    End Function

    Private Function CheckIsContainUrl(ByVal strContent As String) As String
        Dim result As String = strContent
        Try
            '(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&amp;:/~\+#]*[\w\-\@?^=%&amp;/~\+#])?
            '用正则表达式识别URL超链接
            Dim UrlRegex As New Regex("((http|ftp|https)://)?(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,4})*(/[a-zA-Z0-9\&%_\./-~-]*)?", RegexOptions.IgnoreCase Or RegexOptions.Compiled)
            Dim matches As MatchCollection = UrlRegex.Matches(strContent)

            For Each match As Match In matches
                'result = result.Replace(match.Value, String.Format("<a href=""{0}"" target=""_blank""><font><u>{1}</u></font></a>", match.Value, match.Value))
                result = result.Replace(match.Value, String.Format("<span class=""title left""><a href=""weixin://navigate?url={0}""><font><u>{1}</u></font></a></span>", match.Value, match.Value))
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckIsContainUrl")
            result = strContent
        End Try

        Return result
    End Function


#End Region

#Region "--- 私有方法 ---"

    Private Delegate Sub AddGroupRowHandler(ByVal Item As SMSGroup)
    Private Sub AddGroupRow(ByVal Item As SMSGroup)
        If Me.InvokeRequired Then
            Me.Invoke(New AddGroupRowHandler(AddressOf AddGroupRow), Item)
        Else
            If Item IsNot Nothing Then
                Dim intIndex As Integer = Me.mDgvGroup.Rows.Add(False, _
                                                                  Item.PhoneNumber, _
                                                                  "")
                Dim row As DataGridViewRow = Me.mDgvGroup.Rows(intIndex)
                If row IsNot Nothing Then
                    row.Tag = Item
                End If
            End If
        End If
    End Sub

    Private Function GetContactNameByPhoneNumber(ByVal strPhoneNumber As String, ByVal lstContact As List(Of Contact))
        Dim strName As String = ""
        If strPhoneNumber.Length > 0 AndAlso lstContact IsNot Nothing AndAlso lstContact.Count > 0 Then
            For Each Item As Contact In lstContact
                If Item.PhoneNumbers IsNot Nothing AndAlso Item.PhoneNumbers.Count > 0 Then
                    For Each conItem As ContactItem In Item.PhoneNumbers
                        'If strPhoneNumber.Contains("4221") AndAlso conItem.Value.Contains("4221") Then
                        '    Dim a As String = ""
                        'End If
                        If conItem.Value = strPhoneNumber OrElse Me.FormatPhoneNumber(conItem.Value) = Me.FormatPhoneNumber(strPhoneNumber) Then
                            strName = Item.LastName & Item.FirstName
                            Exit For
                        End If
                    Next

                    '添加email比较
                    For Each conItem As ContactItem In Item.Emails
                        If conItem.Value = strPhoneNumber Then
                            strName = Item.LastName & Item.FirstName
                            Exit For
                        End If
                    Next

                End If
            Next

        End If

        Return strName
    End Function

    Private Function FormatPhoneNumber(ByVal strPhoneNumber As String) As String
        Dim strReturn As String = strPhoneNumber.Replace("+86", "").Replace("-", "").Replace(" ", "").Replace(" ", "")
        Return strReturn
    End Function

    Private Delegate Sub SetGroupSelectedHandler()
    Private Sub SetGroupSelected()
        If Me.InvokeRequired Then
            Me.Invoke(New SetGroupSelectedHandler(AddressOf SetGroupSelected))
        Else
            If Me.mDgvGroup.RowCount > 0 Then
                Me.mDgvGroup.Rows(0).Selected = True
            End If
        End If
    End Sub

    Private Sub ClearRow()
        Try
            Me.mDgvGroup.Rows.Clear()
        Catch ex As Exception
        End Try
    End Sub

    Private Sub FilterSMSGroup(ByVal strText As String)
        If strText.Trim.Length <= 0 Then
            For Each row As tbDataGridViewRow In Me.mDgvGroup.Rows
                row.Visible = True
            Next
        Else
            For Each row As tbDataGridViewRow In Me.mDgvGroup.Rows
                If row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is SMSGroup Then
                    Dim group As SMSGroup = row.Tag
                    Dim strPinYin As String = PinYinClass.MakePinYin(group.GroupName, PinYinOptions.FirstCharacterOnly)
                    If group.GroupName.StartsWith(strText.Trim) OrElse group.PhoneNumber.Contains(strText.Trim) OrElse strPinYin.ToLower.StartsWith(strText.Trim.ToLower) Then
                        row.Visible = True
                        Continue For
                    Else
                        Dim blnFound As Boolean = False
                        For Each messsage As SMSMessage In group.Messages
                            strPinYin = PinYinClass.MakePinYin(messsage.Content, PinYinOptions.FirstCharacterOnly)
                            If messsage.Content.Contains(strText.Trim) OrElse strPinYin.ToLower.Contains(strText.ToLower.Trim) Then
                                blnFound = True
                                Exit For
                            End If
                        Next
                        If blnFound Then
                            row.Visible = True
                        Else
                            row.Visible = False
                        End If
                    End If
                End If
            Next
        End If

        Dim isSelected As Boolean = False
        For Each row As tbDataGridViewRow In Me.mDgvGroup.Rows
            If row.Visible = True Then
                If Not isSelected Then
                    row.Selected = True
                    Me.FilterSMSRow(row.Tag, Me.txtSearchFile.Text, "-1", DataType.Normal)
                    isSelected = True
                Else
                    row.Selected = False
                End If
            Else
                row.Selected = False
            End If
        Next

        If Not isSelected Then
            Me.FilterSMSRow(Nothing, Me.txtSearchFile.Text, 0, DataType.Normal)
            Me.lblTitle.Text = ""
            Me.lblMessage.Text = String.Format(Me.Language.GetString("Backup.Message.SMSCount"), 0)      '"共 {0} 条短信"
        End If
    End Sub

    Private Sub FilterSMSRow(ByVal group As SMSGroup, ByVal strFilderText As String, ByVal intLastId As String, ByVal type As DataType)
        If intLastId = "-1" Then
            ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        End If

        If group Is Nothing Then
            Return
        End If

        Dim strPinYin As String = PinYinClass.MakePinYin(group.GroupName, PinYinOptions.FirstCharacterOnly)
        Dim lstMessage As New List(Of SMSMessage)
        If group.GroupName.StartsWith(strFilderText.Trim) OrElse group.PhoneNumber.Contains(strFilderText.Trim) OrElse strPinYin.ToLower.StartsWith(strFilderText.Trim.ToLower) Then
            lstMessage = group.Messages
        Else
            Dim intIndex As Integer = 0
            For Each messsage As SMSMessage In group.Messages
                intIndex += 1
                If intIndex Mod 50 = 0 Then
                    Application.DoEvents()
                End If
                strPinYin = PinYinClass.MakePinYin(messsage.Content, PinYinOptions.FirstCharacterOnly)
                If messsage.Content.Contains(strFilderText.Trim) OrElse strPinYin.ToLower.Contains(strFilderText.ToLower.Trim) Then
                    lstMessage.Add(messsage)
                End If
            Next
        End If

        Dim blnShowMore As Boolean = False
        Dim strJason As String = Me.GetChatJson(Me.GetSubMessageById(lstMessage, intLastId, blnShowMore), group, type)
        ChatWebPage.ShowChatOnWebPage(Me.wbsChat, strJason)
        ChatWebPage.ShowMoreButton(Me.wbsChat, False)
        ChatWebPage.ShowMoreButton(Me.wbsChat, blnShowMore)
    End Sub

    Private Function GetSubMessageById(ByVal lstMessage As List(Of SMSMessage), ByVal intLastId As String, ByRef blnShowMore As Boolean) As List(Of SMSMessage)
        Dim subMessages As New List(Of SMSMessage)
        Dim intEnd As Integer = 0
        For Each Item As SMSMessage In lstMessage
            intEnd += 1
            If Item.MessageROWID = intLastId Then
                intEnd -= 1
                Exit For
            End If
        Next

        Dim intStart As Integer = intEnd - 100
        If intStart <= 1 Then
            blnShowMore = False
            intStart = 1
        Else
            blnShowMore = True
        End If

        For index As Integer = intStart To intEnd
            subMessages.Add(lstMessage(index - 1))
        Next

        Return subMessages
    End Function

    Private Sub tmrShowCount_Elapsed(sender As Object, e As Timers.ElapsedEventArgs)
        If Me.mShowTime < 5 Then
            Me.mShowTime = Me.mShowTime + 1
            Return
        End If
        Me.tmrShowCount.Stop()
        Utility.WaitSeconds(1)
        Me.SetPnlCount(False)
    End Sub

    Private Sub SetPnlCount(ByVal isShow As Boolean)
        Me.mShowTime = 0
        If isShow Then
            Me.tmrShowCount.Start()
        Else
            Me.tmrShowCount.Close()
        End If
        Try
            Me.pnlCount.Visible = isShow
        Catch ex As Exception
            Me.pnlCount.Visible = isShow
            Common.LogException(ex.ToString(), "frmBackupSMS_SetPnlCount")
        End Try
    End Sub

    Private Sub Export(ByVal type As ExportType, Optional ByVal blnExportAll As Boolean = True)
        If type = ExportType.Excel AndAlso Not Me.mExportHelper.Enabled Then
            Return
        End If

        Dim intCount As Integer = Me.mDgvGroup.Rows.Count
        If Not blnExportAll Then
            intCount = Me.mDgvGroup.SelectedRows.Count
        End If

        '短信只能导出三个联系人的聊天记录
        If Not ChargeHelper.Instance(ChargeProjectType.Assistant_Abroad).CheckIsChargeApp(Me, FunctionKey.SMS, intCount) Then
            Return
        End If

        Me.lblPrompt.Text = Me.Language.GetString("Common.Info.SystemRelatedEx") '"温馨提示：导出的文件无法导入设备，备份的才可以还原。"
        Me.SetPnlCount(True)
        'Dim isShowBackupSMS As Boolean = IniSetting.GetShowBackupSMS()
        'If isShowBackupSMS Then
        '    Dim isNoArgenShow As Boolean = False
        '    '’因为系统限制导出的文件无法导入， 是否继续导出？
        '    If tbMessageBox.Show(Me, _
        '                     Me.Language.GetString("Common.Info.SystemRelated"), _
        '                     Me.Language.GetString("Common.Info"), _
        '                     MessageBoxButtons.YesNo, _
        '                     MessageBoxIcon.Warning, _
        '                     MessageBoxDefaultButton.Button1, _
        '                     Me.Language.GetString("App.Button.IgnoreNumber"), _
        '                     isNoArgenShow, True, False, False) = Windows.Forms.DialogResult.Yes Then
        '        If isNoArgenShow Then
        '            IniSetting.SetShowBackupSMS()
        '        End If
        '    Else
        '        If isNoArgenShow Then
        '            IniSetting.SetShowBackupSMS()
        '        End If
        '        Return
        '    End If
        'End If

        Dim fileDlg As New SaveFileDialog
        fileDlg.Title = Me.Language.GetString("Media.Button.Export")                '"导出"
        fileDlg.Filter = Me.Language.GetString("Note.Label.TxtFile") & String.Format(" (*.{0})|*.{0}", Me.GetFileType(type))         ' "文本文件"
        fileDlg.FileName = String.Format("SMS_{0}", DateTime.Now.ToString("yyyy_MM_dd_hhmmss"))

        If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Dim strFilePath As String = fileDlg.FileName

            Dim objPara(3) As Object
            objPara(0) = strFilePath
            objPara(1) = type
            objPara(2) = blnExportAll
            Try
                If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                    Me.mThrExport.Abort()
                End If

                Me.mThrExport = New Thread(New ParameterizedThreadStart(AddressOf ExportExcelThread))
                With Me.mThrExport
                    .IsBackground = True
                    .Start(objPara)
                End With
            Catch ex As Exception
            End Try
            'Me.ExportExcelThread(objPara)
        End If
    End Sub

    Private Function GetFileType(ByVal type As ExportType) As String
        Dim strReturn As String = "xls"
        Select Case type
            Case ExportType.Txt
                strReturn = "txt"

            Case ExportType.CSV
                strReturn = "CSV"

        End Select
        Return strReturn
    End Function

    Private Sub ExportExcelThread(ByVal para As Object)
        Try
            If para Is Nothing Then
                Return
            End If
            Dim strPath As String = para(0)
            Dim type As ExportType = para(1)
            Dim blnExportAll As Boolean = para(2)

            ''"正在导出到 {0} 文件中..."
            Me.SetExportMessage(String.Format(Me.Language.GetString("Backup.Message.Exporting"), Me.GetFileType(type)))

            If Me.mExportHelper Is Nothing Then
                Me.mExportHelper = New SmsExportHelper(Me.Language)
            End If
            Dim lstExport As New List(Of SMSGroup)
            If blnExportAll Then
                lstExport = Me.mSMSGroup
            Else
                For Each Item As tbDataGridViewRow In Me.mDgvGroup.SelectedRows
                    If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is SMSGroup Then
                        lstExport.Add(Item.Tag)
                    End If
                Next
            End If
            Dim blnSucceed As Boolean = False
            If type = ExportType.Txt Then
                blnSucceed = Me.mExportHelper.ExportToTxt(lstExport, strPath)
            ElseIf type = ExportType.Excel Then
                blnSucceed = Me.mExportHelper.ExportToExcel(lstExport, strPath, False, Me.Language.GetString("Weixin.Text.Me"))
            Else
                blnSucceed = Me.mExportHelper.ExportToCSV(lstExport, strPath)
            End If

            '导出成功后才记录数量
            If blnSucceed Then
                ChargeHelper.Instance(ChargeProjectType.Assistant_Abroad).SendDataToServer(FunctionKey.SMS, lstExport.Count)
            End If

            Me.SetExportMessage(Me.Language.GetString("Note.Message.ExportSucceed"))            '"导出成功！"
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub SetExportMessageHandler(ByVal strMessage As String)
    Private Sub SetExportMessage(ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetExportMessageHandler(AddressOf SetExportMessage), strMessage)
        Else
            Me.lblMessage.Text = strMessage
        End If

    End Sub

    Private Delegate Sub SetButtonEnableHandler(ByVal blnEnable As Boolean)
    Private Sub SetButtonEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetButtonEnableHandler(AddressOf SetButtonEnable), blnEnable)
        Else
            Me.btnExport.Enabled = blnEnable
        End If

    End Sub

    Private Delegate Sub SetPanelViewHandler(ByVal type As SetViewType)
    Private Sub SetPanelView(ByVal type As SetViewType)
        If Me.InvokeRequired Then
            Me.Invoke(New SetPanelViewHandler(AddressOf SetPanelView), type)
        Else
            'type = SetViewType.PwdBackup
            Select Case type
                Case SetViewType.Empty
                    Me.pnlFileEmpty.BringToFront()
                    Me.pnlFileEmpty.Visible = True

                Case SetViewType.Loading
                    Me.pnlFileEmpty.Visible = False
                    Me.pnlLoading.BringToFront()
                    Me.pnlLoading.Visible = True

                Case SetViewType.PwdBackup
                    Me.pnlFileEmpty.Visible = False
                    Me.pnlDescription.BringToFront()

                Case SetViewType.None
                    Me.pnlLoading.SendToBack()
                    Me.pnlDescription.SendToBack()
                    Me.pnlFileEmpty.SendToBack()
                    Me.pnlFileEmpty.Visible = False

            End Select
        End If
    End Sub

    Private Function GetChatForWeb(ByVal url As String) As SMSMessage
        Dim result As SMSMessage = Nothing
        Dim strGroupID As String = Utility.GetParamValueFromQuery("friend", url)
        Dim strID As String = Utility.GetParamValueFromQuery("id", url)

        If String.IsNullOrEmpty(strGroupID) OrElse String.IsNullOrEmpty(strID) Then
            Return result
        End If

        For Each group As SMSGroup In Me.mSMSGroup
            Application.DoEvents()
            If group.GroupID = strGroupID Then
                Application.DoEvents()
                For Each message As SMSMessage In group.Messages
                    If message.MessageROWID = strID Then
                        result = message
                        Exit For
                    End If
                Next
                Exit For
            End If
        Next
        Return result
    End Function

    '短信内容的换行符是vbLf，没有vbCr。
    Private Function GetMessageForCopy(ByVal message As String) As String
        Dim strText As String = message
        Dim arr As String() = strText.Split(New Char() {vbCr, vbLf})

        If arr.Length > 1 Then
            Dim strMsgTemp As String = String.Empty
            For index As Integer = 0 To arr.Length - 1
                strMsgTemp = strMsgTemp & vbCrLf & arr(index)
            Next

            strText = strMsgTemp.TrimStart(vbCrLf)
        End If

        Return strText
    End Function

#End Region

#Region "--- 窗体事件 ---"

    Public Sub mDgvGroup_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If Me.mblnCheckBoxSelected = True Then
            Return
        End If
        If Me.mDgvGroup.RowCount > 0 AndAlso Me.mDgvGroup.SelectedRows.Count > 0 AndAlso Me.mDgvGroup.SelectedRows(0).Tag IsNot Nothing AndAlso TypeOf Me.mDgvGroup.SelectedRows(0).Tag Is SMSGroup Then
            Dim group As SMSGroup = CType(Me.mDgvGroup.SelectedRows(0).Tag, SMSGroup)
            Me.mCurrentGroup = group
            Me.FilterSMSRow(group, Me.txtSearchFile.Text, "-1", DataType.Normal)
            Me.lblTitle.Text = IIf(group.GroupName.Length > 0, String.Format("{0}({1})", group.GroupName, group.PhoneNumber), group.PhoneNumber)
            Me.lblMessage.Text = String.Format(Me.Language.GetString("Backup.Message.SMSCount"), group.Messages.Count)      '"共 {0} 条短信"
        End If

        If Me.mDgvGroup.Rows.Count <> Me.mDgvGroup.SelectedRows.Count Then
            Me.chkSelectAll.Checked = False
        ElseIf Me.mDgvGroup.Rows.Count = Me.mDgvGroup.SelectedRows.Count AndAlso Me.mDgvGroup.Rows.Count > 0 Then
            Me.chkSelectAll.Checked = True
        End If

    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click

    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click

        Me.DoRefresh()
    End Sub

    Private Sub DoRefresh()
        Me.mIsCheckLoadAloneBackup = False
        Me.mIsLoadAloneBackup = False
        Me.mStrAloneBackupPath = ""
        Me.mIsUnsaved = True
        '设备上短信刷新显示loading 设备上刷新会比较漫
        If Me.mDevice IsNot Nothing Then
            Me.SetPanelView(SetViewType.Loading)
        End If
        Me.LoadDataThread(True)
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If Not Me.mBackupdb.Save() Then
            '"保存失败！"
            tbMessageBox.Show(Me, Me.Language.GetString("File.Message.SaveToPhoneFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)
        Try
            If Me.mBackupdb IsNot Nothing Then
                Me.mBackupdb.Close()
            End If
            If Me.mBackupContactdb IsNot Nothing Then
                Me.mBackupContactdb.Close()
                Me.mBackupContactdb.Dispose()
            End If
        Catch ex As Exception
        End Try

        Try
            If Me.mSMSDBHelper IsNot Nothing Then
                Me.mSMSDBHelper.Dispose()
            End If

            If Me.mBackupdb IsNot Nothing Then
                Me.mBackupdb.Dispose()
            End If
        Catch ex As Exception

        End Try

        Try
            If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                Me.mThrExport.Abort()
            End If
            Me.mThrExport = Nothing
        Catch ex As Exception
        End Try
    End Sub

    Private Sub txtSearchFile_SearchTypeClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchFile.SearchTypeClick
        'Me.munSearch.Show(Me.txtSearchFile, New Point(10, Me.txtSearchFile.Height - 4))
    End Sub

    Private Sub txtSearchFile_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchFile.TextChangedByTimer
        Me.FilterSMSGroup(Me.txtSearchFile.Text)
    End Sub

    Private Sub tsmiSearchAll_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tsmiSearchAll.Click
        Me.tsmiSearchAll.Checked = True
        Me.tsmiSearchCurrentGroup.Checked = False
        Me.tsmiSearchContact.Checked = False
    End Sub

    Private Sub tsmiSearchCurrentGroup_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles tsmiSearchCurrentGroup.Click
        Me.tsmiSearchAll.Checked = False
        Me.tsmiSearchCurrentGroup.Checked = True
        Me.tsmiSearchContact.Checked = False
    End Sub

    Private Sub tsmiSearchContact_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiSearchContact.Click
        Me.tsmiSearchAll.Checked = False
        Me.tsmiSearchCurrentGroup.Checked = False
        Me.tsmiSearchContact.Checked = True
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        Me.munExport.Show(Me.btnExport, New Point(10, Me.txtSearchFile.Height - 4))
    End Sub

    Private Sub munExport_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles munExport.Opening
        If Me.mExportHelper Is Nothing Then
            Me.mExportHelper = New SmsExportHelper(Me.Language)
        End If

        If Not Me.mExportHelper.Enabled Then
            Me.tsmiExportToExcel.Enabled = False
            Me.tsmiExportToExcelSelected.Visible = False
        Else
            Me.tsmiExportToExcel.Enabled = True
            Me.tsmiExportToExcelSelected.Visible = True
        End If

        Dim strText As String = ""
        Dim blnEnable As Boolean = True
        If Me.mDgvGroup.SelectedRows.Count = 0 Then
            strText = Me.Language.GetString("Backup.Button.ExportSelected")             '"导出选中"
            blnEnable = False

        ElseIf Me.mDgvGroup.SelectedRows.Count = 1 Then
            If Me.mDgvGroup.SelectedRows(0).Tag IsNot Nothing AndAlso TypeOf Me.mDgvGroup.SelectedRows(0).Tag Is SMSGroup Then
                Dim group As SMSGroup = Me.mDgvGroup.SelectedRows(0).Tag
                Dim name As String = IIf(group.GroupName.Trim.Length > 0, group.GroupName, group.PhoneNumber)
                strText = String.Format(Me.Language.GetString("Backup.Button.ExportSelectedRecord"), name, Me.mDgvGroup.SelectedRows.Count)         '"导出 ""{0} 的记录"""
            End If

        Else
            If Me.mDgvGroup.SelectedRows(0).Tag IsNot Nothing AndAlso TypeOf Me.mDgvGroup.SelectedRows(0).Tag Is SMSGroup Then
                Dim group As SMSGroup = Me.mDgvGroup.SelectedRows(0).Tag
                Dim name As String = IIf(group.GroupName.Trim.Length > 0, group.GroupName, group.PhoneNumber)
                strText = String.Format(Me.Language.GetString("Backup.Button.ExportSelectedRecordAndCount"), name, Me.mDgvGroup.SelectedRows.Count) '"导出 ""{0} 等 {1} 人的记录"""
            End If

        End If

        Me.tsmiExportToExcelSelected.Text = strText
        Me.tsmiExportToTxtSelected.Text = strText
        Me.tsmiExportToCSVSelected.Text = strText
        Me.tsmiExportToExcelSelected.Enabled = blnEnable
        Me.tsmiExportToTxtSelected.Enabled = blnEnable
        Me.tsmiExportToCSVSelected.Enabled = blnEnable
    End Sub

    Private Sub tsmiExportToTxtSelected_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToTxtSelected.Click
        Me.Export(ExportType.Txt, False)
    End Sub

    Private Sub tsmiExportToTxtAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToTxtAll.Click
        Me.Export(ExportType.Txt, True)
    End Sub

    Private Sub tsmiExportToExcelSelected_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToExcelSelected.Click
        Me.Export(ExportType.Excel, False)
    End Sub

    Private Sub tsmiExportToExcelAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToExcelAll.Click
        Me.Export(ExportType.Excel, True)
    End Sub

    Private Sub wbChat_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs)
        Dim url As String = e.Url.ToString
        If url.StartsWith("weixin://copy", StringComparison.OrdinalIgnoreCase) Then
            Dim message As SMSMessage = Me.GetChatForWeb(url)
            If message IsNot Nothing Then
                Try
                    '把图片的标记转换成【图片】

                    Dim str As String = message.Content
                    If message.Pics IsNot Nothing AndAlso message.Pics.Count > 0 Then
                        For Each pic As SMSMessagePic In message.Pics
                            If pic.Class = ImageVideo.Image Then
                                str = str.Replace(String.Format("#Pic{0}#", pic.PicPath), String.Format("【{0}】", Me.Language.GetString("Components.ImageView.Label.Current")))
                            Else
                                str = str.Replace(String.Format("#Video{0}#", pic.PicPath), String.Format("【{0}】", Me.Language.GetString("Media.Type.Video")))
                            End If

                        Next
                    End If

                    '复制
                    str = Me.GetMessageForCopy(str)
                    Clipboard.SetText(str)

                    '复制结果提示
                    Dim strMessage As String = message.Content.Replace(vbCr, "").Replace(vbLf, "").Trim
                    If strMessage.Length > 10 Then
                        strMessage = String.Format("""{0}..."" {1}", strMessage.Substring(0, 10), Me.Language.GetString("Welcome.Label.CopyToClip"))
                    Else
                        strMessage = String.Format("""{0}"" {1}", strMessage, Me.Language.GetString("Welcome.Label.CopyToClip"))
                    End If
                    Me.lblMessage.Text = strMessage
                Catch
                    Me.lblMessage.Text = Me.Language.GetString("Weixin.Message.CopyFail")       '"复制失败！"
                End Try
                e.Cancel = True
            End If
        ElseIf url.StartsWith("weixin://getnews/") Then
            Dim LastId As String = Utility.GetParamValueFromQuery("id", url)
            Dim group As SMSGroup = CType(Me.mDgvGroup.SelectedRows(0).Tag, SMSGroup)
            Me.FilterSMSRow(group, Me.txtSearchFile.Text, LastId, DataType.GetNews)
            e.Cancel = True
        ElseIf url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase) Then
            Me.Navigate(url)
            e.Cancel = True

        End If
    End Sub

    '跳转
    Private Sub Navigate(ByVal url As String)
        Dim startText As String = "?url="
        Dim startIndex As Integer = url.IndexOf(startText)
        Dim strUrl As String = url.Substring(startIndex + startText.Length).Trim()

        If Not String.IsNullOrEmpty(strUrl) Then
            Dim strPrefix As String = "http://"
            If Not strUrl.StartsWith(strPrefix) Then
                strUrl = strPrefix & strUrl
            End If

            Common.OpenExplorer(strUrl)
        End If
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            RemoveHandler Me.tmrShowCount.Elapsed, AddressOf tmrShowCount_Elapsed
        Catch
        End Try
        Try
            RemoveHandler Me.tmrShowCount.Elapsed, AddressOf tmrShowCount_Elapsed
            Me.tmrShowCount.Dispose()
        Catch
        End Try

        If pblnCancelClose Then
            Return
        End If

        Try
            If Me.mExportHelper IsNot Nothing Then
                Me.mExportHelper.Close()
            End If
        Catch ex As Exception
        End Try

        Try
            Me.mExportHelper = Nothing
        Catch ex As Exception
        End Try

        Try
            If Me.mThrLoadData IsNot Nothing AndAlso Me.mThrLoadData.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadData.Abort()
            End If
        Catch ex As Exception
        End Try

        Try
            If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                Me.mThrExport.Abort()
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub chkSelectAll_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles chkSelectAll.CheckedChanged
        Dim intInsex As Integer = 0
        Me.mblnCheckBoxSelected = True
        For Each Item As DataGridViewRow In Me.mDgvGroup.Rows
            intInsex += 1
            Item.Selected = Me.chkSelectAll.Checked
            If intInsex Mod 50 = 0 Then
                Application.DoEvents()
            End If
        Next
        Me.mblnCheckBoxSelected = False

    End Sub

    Private Sub llblRefresh_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles llblRefresh.LinkClicked
        
        Me.DoRefresh()
    End Sub

#End Region

#Region "--- 连接事件 ---"

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        If Me.mBackObject Is Nothing Then
            Me.Close()
        End If
    End Sub

#End Region

    'Private Sub chkSelectAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkSelectAll.CheckedChanged
    '    ' Me.mInserting = True
    '    For Each row As DataGridViewRow In Me.mDgvGroup.Rows
    '        If row.Visible Then
    '            row.Selected = Me.chkSelectAll.Checked
    '        End If
    '    Next
    '    '  Me.mInserting = False
    'End Sub

    Private Sub btnSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSearch.Click
        Me.mIsCheckLoadAloneBackup = True
        Me.SetSearchView()
    End Sub

    Private Sub txtSearchContract_ClearClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchContract.ClearClick
        Me.SetCheckView()
        For Each row As DataGridViewRow In Me.mDgvGroup.Rows
            row.Visible = True
        Next
    End Sub

    Private Sub txtSearchContract_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchContract.TextChanged
        Me.FilterGroups()
    End Sub

    Private Sub FilterGroups()
        Dim isDigit As Boolean
        Dim strText As String = Me.txtSearchContract.Text

        'Dim regex As New System.Text.RegularExpressions.Regex()
        isDigit = System.Text.RegularExpressions.Regex.IsMatch(strText, "[0-9]+")

        If Not String.IsNullOrEmpty(strText) Then
            For Each row As DataGridViewRow In Me.mDgvGroup.Rows
                Dim group As SMSGroup = CType(row.Tag, SMSGroup)
                Dim strPinyin As String = PinYinClass.MakePinYin(group.GroupName, PinYinOptions.Default)
                Dim strFirstChar As String = PinYinClass.MakePinYin(group.GroupName, PinYinOptions.FirstCharacterOnly)


                If group.GroupName.StartsWith(strText, StringComparison.InvariantCultureIgnoreCase) OrElse strPinyin.StartsWith(strText, StringComparison.InvariantCultureIgnoreCase) OrElse strFirstChar.StartsWith(strText, StringComparison.InvariantCultureIgnoreCase) Then
                    row.Visible = True
                Else
                    row.Visible = False
                End If

                If isDigit Then
                    If group.PhoneNumber.StartsWith(strText) Then
                        row.Visible = row.Visible Or True
                    Else
                        row.Visible = row.Visible Or False
                    End If
                End If

            Next
        Else
            For Each row As DataGridViewRow In Me.mDgvGroup.Rows
                row.Visible = True
            Next
        End If
    End Sub

    Private Sub SetSearchView()
        Me.chkSelectAll.Visible = False
        Me.btnSearch.Visible = False
        Me.txtSearchContract.Visible = True
        Me.txtSearchContract.Dock = DockStyle.Fill
        Me.txtSearchContract.Focus()
        'Me.r()
    End Sub

    Private Sub SetCheckView()
        Me.txtSearchContract.Visible = False
        Me.btnSearch.Visible = True
        Me.chkSelectAll.Visible = True
    End Sub

    Private Sub tsmiExportToCSVSelected_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToCSVSelected.Click
        Me.Export(ExportType.CSV, False)
    End Sub

    Private Sub tsmiExportToCSVAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToCSVAll.Click
        Me.Export(ExportType.CSV, True)
    End Sub

    Private Sub btnReCheckEx1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReCheckEx1.Click
        Me.DoRefresh()
    End Sub

    'Private Sub btnReCheckEx1_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Me.btnReCheckEx1.Location = New Point(Me.picClosePwd1.Right - Me.btnReCheckEx1.Width, Me.btnReCheckEx1.Top)
    'End Sub

    Private Sub btnDataRecovery_Click(sender As Object, e As EventArgs) Handles btnDataRecovery.Click, btnDataRecoveryEx.Click
        Try
            If ServerIniSetting.GetDataRecovery_SMS.Length > 0 Then
                Common.OpenExplorer(ServerIniSetting.GetDataRecovery_SMS)
                Return
            End If
            Me.btnDataRecovery.tbShowDot = False
            TBGuideHelper.OpenTBGuide(Me, TBGuideType.Msg)
            IniSetting.SetShowSMSDataRecoveryNew(False)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnDataRecovery_Click")
        End Try
    End Sub


    Private Sub btn_Setting_Click(sender As Object, e As EventArgs) Handles btn_Setting.Click
        Dim frm As New frmMSMSetting()
        frm.Show(Me)
    End Sub
End Class


