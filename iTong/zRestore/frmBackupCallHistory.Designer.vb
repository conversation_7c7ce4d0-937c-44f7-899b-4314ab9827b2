﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmBackupCallHistory
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBackupCallHistory))
        Me.pnlControl = New iTong.Components.tbPanel()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.txtSearchFile = New iTong.Components.tbSearch()
        Me.btnImport = New iTong.Components.tbButton()
        Me.btnExport = New iTong.Components.tbButton()
        Me.btnSave = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.tlpMain = New System.Windows.Forms.TableLayoutPanel()
        Me.Spliter1 = New System.Windows.Forms.Panel()
        Me.tvwLeft = New iTong.Components.tbTreeView()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.munExport = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiExportToTxt = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExportToExcel = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlLoading = New System.Windows.Forms.Panel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.pnlControl.SuspendLayout()
        Me.tlpMain.SuspendLayout()
        Me.munExport.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'pnlControl
        '
        Me.pnlControl.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlControl.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlControl.Controls.Add(Me.btnRefresh)
        Me.pnlControl.Controls.Add(Me.txtSearchFile)
        Me.pnlControl.Controls.Add(Me.btnImport)
        Me.pnlControl.Controls.Add(Me.btnExport)
        Me.pnlControl.Controls.Add(Me.btnSave)
        Me.pnlControl.Controls.Add(Me.btnDelete)
        Me.pnlControl.Location = New System.Drawing.Point(1, 32)
        Me.pnlControl.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlControl.Name = "pnlControl"
        Me.pnlControl.Size = New System.Drawing.Size(798, 33)
        Me.pnlControl.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlControl.TabIndex = 32
        Me.pnlControl.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlControl.tbShowWatermark = False
        Me.pnlControl.tbSplit = "3,3,3,3"
        Me.pnlControl.tbWatermark = Nothing
        Me.pnlControl.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlControl.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(306, 5)
        Me.btnRefresh.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(63, 23)
        Me.btnRefresh.TabIndex = 23
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = " 刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 2
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'txtSearchFile
        '
        Me.txtSearchFile.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.txtSearchFile.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchFile.Location = New System.Drawing.Point(624, 5)
        Me.txtSearchFile.MaxLength = 32767
        Me.txtSearchFile.Name = "txtSearchFile"
        Me.txtSearchFile.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchFile.ReadOnly = False
        Me.txtSearchFile.SearchText = ""
        Me.txtSearchFile.SearchTipText = "Search"
        Me.txtSearchFile.ShowClear = True
        Me.txtSearchFile.ShowClearAlways = False
        Me.txtSearchFile.ShowMore = False
        Me.txtSearchFile.ShowSearch = True
        Me.txtSearchFile.Size = New System.Drawing.Size(166, 23)
        Me.txtSearchFile.TabIndex = 12
        Me.txtSearchFile.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchFile.Timer = True
        Me.txtSearchFile.TimerInterval = 0.5R
        '
        'btnImport
        '
        Me.btnImport.BackColor = System.Drawing.Color.Transparent
        Me.btnImport.BindingForm = Nothing
        Me.btnImport.Enabled = False
        Me.btnImport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImport.Location = New System.Drawing.Point(11, 5)
        Me.btnImport.Name = "btnImport"
        Me.btnImport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImport.Selectable = True
        Me.btnImport.Size = New System.Drawing.Size(69, 23)
        Me.btnImport.TabIndex = 11
        Me.btnImport.tbAdriftIconWhenHover = False
        Me.btnImport.tbAutoSize = False
        Me.btnImport.tbAutoSizeEx = True
        Me.btnImport.tbBackgroundImage = Nothing
        Me.btnImport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbBadgeNumber = 0
        Me.btnImport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImport.tbEndEllipsis = False
        Me.btnImport.tbIconHoldPlace = True
        Me.btnImport.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.btnImport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbIconMore = False
        Me.btnImport.tbIconMouseDown = Nothing
        Me.btnImport.tbIconMouseHover = Nothing
        Me.btnImport.tbIconMouseLeave = Nothing
        Me.btnImport.tbIconPlaceText = 2
        Me.btnImport.tbIconReadOnly = Nothing
        Me.btnImport.tbImageMouseDown = Nothing
        Me.btnImport.tbImageMouseHover = Nothing
        Me.btnImport.tbImageMouseLeave = Nothing
        Me.btnImport.tbProgressValue = 50
        Me.btnImport.tbReadOnly = False
        Me.btnImport.tbReadOnlyText = False
        Me.btnImport.tbShadow = False
        Me.btnImport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImport.tbShowDot = False
        Me.btnImport.tbShowMoreIconImg = CType(resources.GetObject("btnImport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImport.tbShowNew = False
        Me.btnImport.tbShowProgress = False
        Me.btnImport.tbShowTip = True
        Me.btnImport.tbShowToolTipOnButton = False
        Me.btnImport.tbSplit = "13,11,13,11"
        Me.btnImport.tbText = "Import"
        Me.btnImport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnImport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextMouseDownPlace = 2
        Me.btnImport.tbToolTip = ""
        Me.btnImport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.VisibleEx = True
        '
        'btnExport
        '
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExport.Location = New System.Drawing.Point(85, 5)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(69, 23)
        Me.btnExport.TabIndex = 10
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = True
        Me.btnExport.tbBackgroundImage = Nothing
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = False
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Global.iTong.My.Resources.Resources.btn_export_4
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 2
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = CType(resources.GetObject("btnExport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "Export"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnExport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextMouseDownPlace = 2
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'btnSave
        '
        Me.btnSave.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnSave.BackColor = System.Drawing.Color.Transparent
        Me.btnSave.BindingForm = Nothing
        Me.btnSave.Enabled = False
        Me.btnSave.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSave.Location = New System.Drawing.Point(236, 5)
        Me.btnSave.Name = "btnSave"
        Me.btnSave.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSave.Selectable = True
        Me.btnSave.Size = New System.Drawing.Size(57, 23)
        Me.btnSave.TabIndex = 7
        Me.btnSave.tbAdriftIconWhenHover = False
        Me.btnSave.tbAutoSize = False
        Me.btnSave.tbAutoSizeEx = True
        Me.btnSave.tbBackgroundImage = Nothing
        Me.btnSave.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSave.tbBadgeNumber = 0
        Me.btnSave.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSave.tbEndEllipsis = False
        Me.btnSave.tbIconHoldPlace = True
        Me.btnSave.tbIconImage = Global.iTong.My.Resources.Resources.btn_save_4
        Me.btnSave.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSave.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnSave.tbIconMore = False
        Me.btnSave.tbIconMouseDown = Nothing
        Me.btnSave.tbIconMouseHover = Nothing
        Me.btnSave.tbIconMouseLeave = Nothing
        Me.btnSave.tbIconPlaceText = 2
        Me.btnSave.tbIconReadOnly = Nothing
        Me.btnSave.tbImageMouseDown = Nothing
        Me.btnSave.tbImageMouseHover = Nothing
        Me.btnSave.tbImageMouseLeave = Nothing
        Me.btnSave.tbProgressValue = 50
        Me.btnSave.tbReadOnly = False
        Me.btnSave.tbReadOnlyText = False
        Me.btnSave.tbShadow = False
        Me.btnSave.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSave.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSave.tbShowDot = False
        Me.btnSave.tbShowMoreIconImg = CType(resources.GetObject("btnSave.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSave.tbShowNew = False
        Me.btnSave.tbShowProgress = False
        Me.btnSave.tbShowTip = True
        Me.btnSave.tbShowToolTipOnButton = False
        Me.btnSave.tbSplit = "13,11,13,11"
        Me.btnSave.tbText = "保存"
        Me.btnSave.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSave.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSave.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnSave.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSave.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSave.tbTextMouseDownPlace = 2
        Me.btnSave.tbToolTip = ""
        Me.btnSave.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSave.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSave.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSave.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(175, 5)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(57, 23)
        Me.btnDelete.TabIndex = 5
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "13,11,13,11"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(13, 577)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(775, 18)
        Me.lblMessage.TabIndex = 37
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = False
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'tlpMain
        '
        Me.tlpMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tlpMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.tlpMain.ColumnCount = 3
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 215.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.Controls.Add(Me.Spliter1, 0, 0)
        Me.tlpMain.Controls.Add(Me.tvwLeft, 0, 0)
        Me.tlpMain.Controls.Add(Me.pnlContainer, 2, 0)
        Me.tlpMain.Location = New System.Drawing.Point(1, 65)
        Me.tlpMain.Margin = New System.Windows.Forms.Padding(0)
        Me.tlpMain.Name = "tlpMain"
        Me.tlpMain.RowCount = 1
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.Size = New System.Drawing.Size(798, 508)
        Me.tlpMain.TabIndex = 38
        '
        'Spliter1
        '
        Me.Spliter1.BackColor = System.Drawing.Color.FromArgb(CType(CType(214, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.Spliter1.Cursor = System.Windows.Forms.Cursors.SizeWE
        Me.Spliter1.Dock = System.Windows.Forms.DockStyle.Left
        Me.Spliter1.Location = New System.Drawing.Point(215, 0)
        Me.Spliter1.Margin = New System.Windows.Forms.Padding(0)
        Me.Spliter1.Name = "Spliter1"
        Me.Spliter1.Size = New System.Drawing.Size(1, 508)
        Me.Spliter1.TabIndex = 37
        '
        'tvwLeft
        '
        Me.tvwLeft.AutoScroll = True
        Me.tvwLeft.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tvwLeft.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tvwLeft.ImageList = Nothing
        Me.tvwLeft.Indent = 19
        Me.tvwLeft.ItemHeight = 28
        Me.tvwLeft.Location = New System.Drawing.Point(0, 0)
        Me.tvwLeft.Margin = New System.Windows.Forms.Padding(0)
        Me.tvwLeft.Name = "tvwLeft"
        Me.tvwLeft.Padding = New System.Windows.Forms.Padding(0, 3, 0, 0)
        Me.tvwLeft.PathSeparator = "\"
        Me.tvwLeft.SelectedNode = Nothing
        Me.tvwLeft.Size = New System.Drawing.Size(215, 508)
        Me.tvwLeft.TabIndex = 2
        Me.tvwLeft.TabStop = True
        Me.tvwLeft.tbBackgroundImage = Nothing
        Me.tvwLeft.tbNodeCountSelectedColor = System.Drawing.Color.Black
        Me.tvwLeft.tbNodeDiff = 4
        Me.tvwLeft.tbNodeFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold)
        Me.tvwLeft.tbNodeForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.tvwLeft.tbNodeLabelFont = New System.Drawing.Font("宋体", 9.0!)
        Me.tvwLeft.tbNodeLabelForeColor = System.Drawing.Color.FromArgb(CType(CType(119, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(178, Byte), Integer))
        Me.tvwLeft.tbNodePadding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.tvwLeft.tbNodeShadowColor = System.Drawing.Color.Empty
        Me.tvwLeft.tbSplit = "1,27,3,3"
        Me.tvwLeft.WrapContents = False
        '
        'pnlContainer
        '
        Me.pnlContainer.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(216, 0)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(582, 508)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 36
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'munExport
        '
        Me.munExport.AccessibleDescription = "153x70"
        Me.munExport.DropShadowEnabled = False
        Me.munExport.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munExport.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExportToTxt, Me.tsmiExportToExcel})
        Me.munExport.Name = "munSearch"
        Me.munExport.Size = New System.Drawing.Size(137, 48)
        Me.munExport.tbBackColor = System.Drawing.Color.White
        Me.munExport.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munExport.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munExport.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiExportToTxt
        '
        Me.tsmiExportToTxt.Name = "tsmiExportToTxt"
        Me.tsmiExportToTxt.Size = New System.Drawing.Size(136, 22)
        Me.tsmiExportToTxt.Text = "导出到Txt"
        '
        'tsmiExportToExcel
        '
        Me.tsmiExportToExcel.Name = "tsmiExportToExcel"
        Me.tsmiExportToExcel.Size = New System.Drawing.Size(136, 22)
        Me.tsmiExportToExcel.Text = "导出到Excel"
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(118, 5)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(319, 24)
        Me.pnlLoading.TabIndex = 39
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(147, -10)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(120, 45)
        Me.lblLoading.TabIndex = 10
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(51, -35)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(91, 95)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 0
        Me.pbLoading.TabStop = False
        '
        'frmBackupCallHistory
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.pnlLoading)
        Me.Controls.Add(Me.tlpMain)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.pnlControl)
        Me.Name = "frmBackupCallHistory"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "通话记录"
        Me.Controls.SetChildIndex(Me.pnlControl, 0)
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Controls.SetChildIndex(Me.tlpMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlLoading, 0)
        Me.pnlControl.ResumeLayout(False)
        Me.tlpMain.ResumeLayout(False)
        Me.munExport.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlControl As tbPanel
    Friend WithEvents btnImport As tbButton
    Friend WithEvents btnExport As tbButton
    Friend WithEvents btnSave As tbButton
    Friend WithEvents btnDelete As tbButton
    Friend WithEvents lblMessage As tbLabel
    Friend WithEvents tlpMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents tvwLeft As tbTreeView
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents Spliter1 As System.Windows.Forms.Panel
    Friend WithEvents txtSearchFile As tbSearch
    Friend WithEvents munExport As tbContextMenuStrip
    Friend WithEvents tsmiExportToTxt As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExportToExcel As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents btnRefresh As iTong.Components.tbButton
End Class
