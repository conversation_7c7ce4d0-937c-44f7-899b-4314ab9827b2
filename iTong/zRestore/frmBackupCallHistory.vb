﻿Imports System.Threading
Imports iTong.CoreModule
Public Class frmBackupCallHistory

    Private mStrDBFilePath As String = ""
    Private mDgvCallHistory As New tbDataGridViewEx
    Private mThrLoadData As Thread
    Private mCallHistory As List(Of CallHistory)
    Private mLstMBFileRecord As List(Of MBFileRecord)
    Private mBackupdb As BackupCallHistoryDB
    Private mBackupContactdb As BackupContactDB
    Private mThrExport As Thread
    Private mExportHelper As CallHistoryExportHelper
    Private mBackObject As BackupInfo

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal backupObj As BackupInfo, ByVal strDBFilePath As String, ByVal lstMBFileRecord As List(Of MBFileRecord))

        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.Icon = My.Resources.iTong
        Me.tbAutoSetFormSize = True
        Me.Size = New Size(840, 600)
        Me.CanResize = True
        Me.FilletRadius = 5
        Me.mStrDBFilePath = strDBFilePath
        Me.mLstMBFileRecord = lstMBFileRecord
        Me.mBackObject = backupObj
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitGridView()
        Me.LoadDataThread()

        'Me.mExportHelper = New CallHistoryExportHelper(Me.Language)

        Me.btnDelete.Enabled = False
        Me.btnDelete.Visible = False
        Me.btnSave.Visible = False

        Me.pnlLoading.Location = Me.pnlControl.Location
        Me.pnlLoading.Size = New Size(Me.pnlControl.Width, Me.tlpMain.Bottom - Me.pnlControl.Top)
        Me.pnlLoading.BringToFront()
        Me.pnlLoading.Visible = True
        If Not Folder.LangType = LanguageType.en_US Then
            Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        End If

        MainForm.SetButtonStatus(Me.btnImport)
        MainForm.SetButtonStatus(Me.btnExport)
        MainForm.SetButtonStatus(Me.btnDelete)
        MainForm.SetButtonStatus(Me.btnSave)
        MainForm.SetButtonStatus(Me.btnRefresh)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Main.Button.CallHistory")                          '"通话记录"
        Me.btnImport.Text = Me.Language.GetString("Media.Button.Import")                    '"导入"
        Me.btnExport.Text = Me.Language.GetString("Media.Button.Export")                    '"导出"
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")                          '"删除"
        Me.btnSave.Text = Me.Language.GetString("Common.Button.Save")                       '"保存"
        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh")            '"刷新"
        Me.tsmiExportToTxt.Text = Me.Language.GetString("Backup.Button.ExportToTxt")        '"导出到Txt"
        Me.tsmiExportToExcel.Text = Me.Language.GetString("Backup.Button.ExportToExcel")    '"导出到Excel"
        Me.txtSearchFile.SearchTipText = Me.Language.GetString("File.Label.Search")         '"查找"
        Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading")                      '"正在载入..."

        Me.btnImport.Location = New Point(10, Me.btnImport.Top)
        Me.btnImport.Visible = False
        Me.btnExport.Location = New Point(10, Me.btnImport.Top)
        Me.btnDelete.Location = New Point(Me.btnExport.Right + 10, Me.btnImport.Top)
        Me.btnSave.Location = New Point(Me.btnDelete.Right + 10, Me.btnImport.Top)
        Me.btnRefresh.Location = New Point(Me.txtSearchFile.Left - Me.btnRefresh.Width - 10, Me.btnRefresh.Top)
    End Sub

    Protected Overrides Sub InitTreeView()
        MyBase.InitTreeView()
        Me.CreateNode("All", Me.Language.GetString("Backup.Label.CallHistoryAll"))              '"全部电话"
        Me.CreateNode("Accept", Me.Language.GetString("Backup.Label.CallHistoryAccept"))        '"已接电话"
        Me.CreateNode("UnAccept", Me.Language.GetString("Backup.Label.CallHistoryUnAccept"))    '"未接电话"
        Me.CreateNode("Out", Me.Language.GetString("Backup.Label.CallHistoryOut"))              '"已拨电话"
    End Sub

    Private Sub InitGridView()
        Me.mDgvCallHistory = New tbDataGridViewEx
        With Me.mDgvCallHistory
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 32
            .AllowDrop = True
            .MultiSelect = True
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .Columns.Clear()

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Main.Button.Contact"), _
                                                               140, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.True)) '"联系人"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colPhoneNumber", Me.Language.GetString("Welcome.Label.PhoneNumber"), _
                                                                130, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.True)) '电话号码
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colCreateDate", Me.Language.GetString("Backup.Label.CallTime"), _
                                                                   140, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)) '"通话时间"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colDuration", Me.Language.GetString("Backup.Label.CallDuration"), _
                                                                   80, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"通话时长"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colInOrOut", Me.Language.GetString("Common.Label.ColumnType"), _
                                                                   80, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)) '"类型"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))

            .ResumeLayout()
        End With
        RemoveHandler mDgvCallHistory.SelectionChanged, AddressOf mDgvCallHistory_SelectionChanged
        AddHandler mDgvCallHistory.SelectionChanged, AddressOf mDgvCallHistory_SelectionChanged
        Me.pnlContainer.Controls.Add(Me.mDgvCallHistory)
    End Sub

    Private Sub LoadDataThread()
        Me.mDgvCallHistory.Rows.Clear()
        If Me.mThrLoadData IsNot Nothing AndAlso Me.mThrLoadData.ThreadState <> ThreadState.Stopped Then
            Return
        End If
        Me.mThrLoadData = New Thread(AddressOf LoadData)
        With Me.mThrLoadData
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub LoadData()
        Try
            Me.mBackupdb = BackupCallHistoryDB.Instance(Me.mStrDBFilePath, Me.mBackObject)
            Me.mCallHistory = Me.mBackupdb.GetAll()
            Me.LoadName()
            Me.UpdateCallHistoryCount()
            Me.SetNodeSelected()
        Catch ex As Exception
        End Try
    End Sub

    Private Sub LoadName()
        '取得联系人数据


        Dim strContactFilePath As String = ""
        For Each Item As MBFileRecord In Me.mLstMBFileRecord
            If Item.PathOnPhone = "/var/mobile/Library/AddressBook/AddressBook.sqlitedb" Then
                strContactFilePath = Item.PathOnPC
            End If
        Next
        If strContactFilePath.Length > 0 Then
            Me.mBackupContactdb = BackupContactDB.Instance(strContactFilePath, "")
            Dim lstContact As List(Of Contact) = Me.mBackupContactdb.GetContacts()
            For Each Item As CallHistory In Me.mCallHistory
                Item.Name = Me.GetContactNameByPhoneNumber(Item.PhoneNumber, lstContact)
            Next
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function GetContactNameByPhoneNumber(ByVal strPhoneNumber As String, ByVal lstContact As List(Of Contact))
        Dim strName As String = ""
        If strPhoneNumber.Length > 0 AndAlso lstContact IsNot Nothing AndAlso lstContact.Count > 0 Then
            For Each Item As Contact In lstContact
                If Item.PhoneNumbers IsNot Nothing AndAlso Item.PhoneNumbers.Count > 0 Then
                    For Each conItem As ContactItem In Item.PhoneNumbers
                        If conItem.Value = strPhoneNumber OrElse conItem.Value.Replace("+86", "").Replace("-", "") = strPhoneNumber.Replace("+86", "").Replace("-", "") Then
                            strName = Item.LastName & Item.FirstName
                            Exit For
                        End If
                    Next

                End If
            Next

        End If
        If strName.Trim.Length = 0 Then
            strName = strPhoneNumber
        End If
        Return strName
    End Function

    Private Sub UpdateCallHistoryCount()
        For Each Item As tbTreeNode In Me.tvwLeft.Nodes
            Dim intCount As Integer = 0
            Select Case Item.Name
                Case "All"
                    intCount = Me.mCallHistory.Count
                    Application.DoEvents()
                Case "Accept"
                    For Each historyItem As CallHistory In Me.mCallHistory
                        If historyItem.Duration > 0 AndAlso historyItem.CallInOut = CallInOut.CallIn Then
                            intCount += 1
                        End If
                        Application.DoEvents()
                    Next

                Case "UnAccept"
                    For Each historyItem As CallHistory In Me.mCallHistory
                        If historyItem.Duration <= 0 AndAlso historyItem.CallInOut = CallInOut.CallIn Then
                            intCount += 1
                        End If
                        Application.DoEvents()
                    Next

                Case "Out"
                    For Each historyItem As CallHistory In Me.mCallHistory
                        If historyItem.CallInOut = CallInOut.CallOut Then
                            intCount += 1
                        End If
                        Application.DoEvents()
                    Next
            End Select
            Item.NodeNumber = intCount
        Next

    End Sub

    Protected Overridable Function CreateNode(ByVal strName As String, ByVal strTest As String) As tbTreeNode
        Dim newNode As tbTreeNode = Me.tvwLeft.Nodes.Add(strName, strTest)
        newNode.ShowDischarge = False
        newNode.NodeDischargeStyle = tbTreeNode.DischargeStyle.Delete
        newNode.NodeDischargePadding = New Padding(0, 0, 15, 0)
        newNode.RenameEnable = False
        newNode.Tag = Nothing
        If strName = "All" Then
            newNode.NodeIcon = My.Resources.tvw_callall_2
        ElseIf strName = "Accept" Then
            newNode.NodeIcon = My.Resources.tvw_callin_2
        ElseIf strName = "UnAccept" Then
            newNode.NodeIcon = My.Resources.tvw_callunaccept_2
        ElseIf strName = "Out" Then
            newNode.NodeIcon = My.Resources.tvw_callout_2
        End If
        newNode.NodeIconState = ImageState.TwoState
        Return newNode
    End Function

    Private Delegate Sub AddCallHistoryRowHandler(ByVal Item As CallHistory, ByVal strType As String)
    Private Sub AddCallHistoryRow(ByVal Item As CallHistory, ByVal strType As String)
        If Me.InvokeRequired Then
            Me.Invoke(New AddCallHistoryRowHandler(AddressOf AddCallHistoryRow), Item, strType)
        Else
            If Item IsNot Nothing Then
                Select Case strType
                    Case "All"
                    Case "Accept"
                        If Not (Item.Duration > 0 AndAlso Item.CallInOut = CallInOut.CallIn) Then
                            Return
                        End If

                    Case "UnAccept"
                        If Not (Item.Duration <= 0 AndAlso Item.CallInOut = CallInOut.CallIn) Then
                            Return
                        End If
                    Case "Out"
                        If Item.CallInOut = CallInOut.CallIn Then
                            Return
                        End If
                End Select
                '"呼出"    "呼入"   "-未接"
                Dim strCallStatus As String = IIf(Item.CallInOut = CallInOut.CallIn, Me.Language.GetString("Backup.Label.CallIn"), Me.Language.GetString("Backup.Label.CallOut")) & IIf(Item.Duration = 0, "-" & Me.Language.GetString("Backup.Label.CallUnConnect"), "")

                Dim intIndex As Integer = Me.mDgvCallHistory.Rows.Add(Item.Name, _
                                                                      Item.PhoneNumber, _
                                                                      Item.CreateDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), _
                                                                      Utility.FormatDuration(Item.Duration), _
                                                                      strCallStatus, _
                                                                      "")

                Dim row As DataGridViewRow = Me.mDgvCallHistory.Rows(intIndex)
                Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                cellName.tbIconSize = New Size(20, 20)
                cellName.tbIcon = GuiHelper.GetMouseStateImage(Me.GetCallIcon(Item), 1, 2)
                cellName.tbIconShadow = False

                If row IsNot Nothing Then
                    row.Tag = Item
                End If

            End If
        End If
    End Sub

    Private Function GetCallIcon(ByVal Item As CallHistory) As Image
        Dim img As Image = Nothing
        If Item.Duration > 0 AndAlso Item.CallInOut = CallInOut.CallIn Then
            'Accept
            img = My.Resources.tvw_callin_2

        ElseIf Item.Duration <= 0 AndAlso Item.CallInOut = CallInOut.CallIn Then
            'UnAccept
            img = My.Resources.tvw_callunaccept_2

        Else
            'Out
            img = My.Resources.tvw_callout_2

        End If
        Return img
    End Function

    Private Delegate Sub SetNodeSelectedHandler()
    Private Sub SetNodeSelected()
        If Me.InvokeRequired Then
            Me.Invoke(New SetNodeSelectedHandler(AddressOf SetNodeSelected))
        Else
            If Me.tvwLeft.SelectedNode IsNot Nothing Then
                Me.tvwLeft_SelectedIndexChanged(Me.tvwLeft.SelectedNode, Nothing)
            Else
                Me.tvwLeft.SelectedNode = Me.tvwLeft.Nodes(0)
            End If
            Me.pnlLoading.SendToBack()
            Me.pnlLoading.Visible = False
        End If
    End Sub

    Private Sub FilterCallHistory(ByVal strText As String)
        Dim intIndex As Integer = 0
        For Each Item As tbDataGridViewRow In Me.mDgvCallHistory.Rows
            intIndex += 1
            If intIndex Mod 50 = 0 Then
                Application.DoEvents()
            End If
            If strText.Trim.Length = 0 Then
                Item.Visible = True
                Continue For
            End If

            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is CallHistory Then
                Dim history As CallHistory = Item.Tag
                If history.PhoneNumber.StartsWith(strText, StringComparison.OrdinalIgnoreCase) Then
                    Item.Visible = True
                    Continue For
                End If

                If history.Name.StartsWith(strText, StringComparison.OrdinalIgnoreCase) Then
                    Item.Visible = True
                    Continue For
                End If

                Dim strPinYin As String = PinYinClass.MakePinYin(history.Name, PinYinOptions.FirstCharacterOnly)
                If strPinYin.StartsWith(strText, StringComparison.OrdinalIgnoreCase) Then
                    Item.Visible = True
                    Continue For
                End If

                Item.Visible = False
            End If

        Next
    End Sub

    Private Sub Export(ByVal type As ExportType)
        If type = ExportType.Excel AndAlso Not Me.mExportHelper.Enabled Then
            Return
        End If

        Dim fileDlg As New SaveFileDialog
        fileDlg.Title = Me.Language.GetString("Main.Button.CallHistory")        '"通话记录"
        fileDlg.Filter = Me.Language.GetString("Note.Label.TxtFile") & String.Format(" (*.{0})|*.{0}", IIf(type = ExportType.Txt, "txt", "xls")) '"文本文件"
        fileDlg.FileName = String.Format("call_history_{0}", DateTime.Now.ToString("yyyy_MM_dd_hhmmss"))

        If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Try
                Dim strFilePath As String = fileDlg.FileName
                If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                    Me.mThrExport.Abort()
                End If

                Dim objPara(2) As Object
                objPara(0) = strFilePath
                objPara(1) = type
                Me.mThrExport = New Thread(New ParameterizedThreadStart(AddressOf ExportExcelThread))
                With Me.mThrExport
                    .IsBackground = True
                    .Start(objPara)
                End With
                'Me.ExportExcelThread(objPara)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub ExportExcelThread(ByVal objPara As Object)
        Try
            If objPara Is Nothing Then
                Return
            End If
            Dim strPath As String = objPara(0)
            Dim type As ExportType = objPara(1)

            '"正在导出到 {0} 文件中..."
            Me.SetExportMessage(String.Format(Me.Language.GetString("Backup.Message.Exporting"), IIf(type = ExportType.Excel, "Excel", "txt")))

            If Me.mExportHelper Is Nothing Then
                Me.mExportHelper = New CallHistoryExportHelper(Me.Language)
            End If

            If type = ExportType.Txt Then
                Me.mExportHelper.ExportToTxt(Me.mCallHistory, strPath)
            Else
                Me.mExportHelper.ExportToExcel(Me.mCallHistory, strPath)
            End If

            Me.SetExportMessage(Me.Language.GetString("Note.Message.ExportSucceed"))        '"导出成功！"
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub SetExportMessageHandler(ByVal strMessage As String)
    Private Sub SetExportMessage(ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetExportMessageHandler(AddressOf SetExportMessage), strMessage)
        Else
            Me.lblMessage.Text = strMessage
        End If

    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Public Sub mDgvCallHistory_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If Me.mDgvCallHistory.SelectedRows.Count > 0 Then
            Me.btnDelete.Enabled = True
        Else
            Me.btnDelete.Enabled = False
        End If
    End Sub

    Private Sub tvwLeft_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As tbTreeView_SelectedIndexChanged_EventArgs) Handles tvwLeft.SelectedIndexChanged
        If sender IsNot Nothing Then
            Dim node As tbTreeNode = sender
            If Not node.IsSelected Then
                Return
            End If
            Me.mDgvCallHistory.Rows.Clear()
            Dim intIndex As Integer = 0
            For Each Item As CallHistory In Me.mCallHistory
                intIndex += 1
                Me.AddCallHistoryRow(Item, node.Name)
                If intIndex Mod 50 = 0 Then
                    Application.DoEvents()
                End If
            Next
        End If

    End Sub

    Private Sub btnRefresh_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        Me.LoadDataThread()
        Me.btnSave.Enabled = False
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        If pblnCancelClose Then
            Return
        End If

        Try
            If Me.mBackupdb IsNot Nothing Then
                Me.mBackupdb.Close()
            End If

            If Me.mBackupContactdb IsNot Nothing Then
                Me.mBackupContactdb.Close()
            End If

            Try
                If Me.mThrLoadData IsNot Nothing AndAlso Me.mThrLoadData.ThreadState <> ThreadState.Stopped Then
                    Me.mThrLoadData.Abort()
                End If
            Catch ex As Exception
            End Try

            Try
                If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                    Me.mThrExport.Abort()
                End If
            Catch ex As Exception
            End Try

        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        If Me.mBackupdb IsNot Nothing Then
            Dim lstID As New List(Of String)
            Dim lstRow As New List(Of tbDataGridViewRow)
            Dim lstHistory As New List(Of CallHistory)
            For Each Item As tbDataGridViewRow In Me.mDgvCallHistory.SelectedRows()
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is CallHistory Then
                    lstID.Add(CType(Item.Tag, CallHistory).CallHistoryROWID)
                    lstHistory.Add(Item.Tag)
                End If
                lstRow.Add(Item)
            Next

            If Me.mBackupdb.Delete(lstID) > 0 Then
                Application.DoEvents()
                For Each Item As CallHistory In lstHistory
                    Me.mCallHistory.Remove(Item)
                Next
                Application.DoEvents()
                For Each Item As tbDataGridViewRow In lstRow
                    Me.mDgvCallHistory.Rows.Remove(Item)
                Next
                Me.btnSave.Enabled = True
            Else
                '"删除失败！"
                tbMessageBox.Show(Me, Me.Language.GetString("Backup.Message.DeleteFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If

            Application.DoEvents()
            lstID.Clear()
            lstID = Nothing
            lstRow.Clear()
            lstRow = Nothing
            lstHistory.Clear()
            lstHistory = Nothing
            Me.UpdateCallHistoryCount()
        End If
    End Sub

    Private Sub btnSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSave.Click
        If Me.mBackupdb.Save() Then
            Me.btnSave.Enabled = False
        Else
            ' "保存失败！"
            tbMessageBox.Show(Me, Me.Language.GetString("File.Message.SaveToPhoneFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub txtSearchFile_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchFile.TextChangedByTimer
        Me.FilterCallHistory(Me.txtSearchFile.Text.Trim)
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        Me.munExport.Show(Me.btnExport, New Point(10, Me.txtSearchFile.Height - 4))
    End Sub

    Private Sub tsmiExportToTxt_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToTxt.Click
        Me.Export(ExportType.Txt)
    End Sub

    Private Sub tsmiExportToExcel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExportToExcel.Click
        Me.Export(ExportType.Excel)
    End Sub

    Private Sub munExport_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles munExport.Opening
        If Me.mExportHelper Is Nothing Then
            Me.mExportHelper = New CallHistoryExportHelper(Me.Language)
        End If

        If Not Me.mExportHelper.Enabled Then
            Me.tsmiExportToExcel.Enabled = False
        Else
            Me.tsmiExportToExcel.Enabled = True
        End If
    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)
        Try
            If Me.mThrExport IsNot Nothing AndAlso Me.mThrExport.ThreadState <> ThreadState.Stopped Then
                Me.mThrExport.Abort()
            End If
            Me.mThrExport = Nothing
        Catch ex As Exception
        End Try

        Try
            If Me.mExportHelper IsNot Nothing Then
                Me.mExportHelper.Close()
            End If
        Catch ex As Exception
        End Try

        Try
            Me.mExportHelper = Nothing
        Catch ex As Exception
        End Try
    End Sub
#End Region

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.tvwLeft.tbNodeSelectedBackground = My.Resources.tvw_node_bg_select
    End Sub

End Class

Public Class CallHistoryExportHelper
    Inherits Disposable

    Private xlApp As Object = Nothing
    Private xlBook As Object = Nothing
    Private xlSheet As Object = Nothing
    Private m_enabled As Boolean = False
    Protected Language As LanguageInterface = Nothing

    Public Sub New(ByVal lang As LanguageInterface)
        Me.Language = lang
        Try
            xlApp = CreateObject("Excel.Application")
            xlBook = xlApp.Workbooks.Add
            xlSheet = xlBook.Worksheets(1)
            m_enabled = True
        Catch
            m_enabled = False
        End Try
    End Sub

    Public ReadOnly Property Enabled()
        Get
            Return m_enabled
        End Get
    End Property

    Public Function ExportToExcel(ByVal lstCallHistory As List(Of CallHistory), ByVal strOutputFile As String) As Boolean
        If Not m_enabled OrElse lstCallHistory Is Nothing OrElse lstCallHistory.Count <= 0 Then
            Return False
        End If

        Try
            xlBook = xlApp.Workbooks.Add
            xlSheet = xlBook.Worksheets(1)
            With xlSheet
                '***************************************************************************************
                .Cells(1, 1) = "Date" '时间
                .Cells(1, 2) = "Contact Person" '姓名
                .Cells(1, 3) = "PhoneNumber" '电话号码
                .Cells(1, 4) = "Type" '类型
                .Cells(1, 5) = "Duration" '时长

                '=== 输出详细内容 ======================================================== 
                Dim rowIndex As Integer = 2

                '设置表格中各列的类型
                With .Range(.Cells(2, 1), .Cells(rowIndex + lstCallHistory.Count, 5))
                    .NumberFormatLocal = "@"                '文本格式
                End With

                With .Range(.Cells(2, 1), .Cells(rowIndex + lstCallHistory.Count, 1))
                    .NumberFormatLocal = Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)  '日期格式
                End With

                For Each item As CallHistory In lstCallHistory
                    Application.DoEvents()
                    .Cells(rowIndex, 1) = item.CreateDate
                    .Cells(rowIndex, 2) = item.Name & ""
                    .Cells(rowIndex, 3) = item.PhoneNumber & ""
                    .Cells(rowIndex, 4) = Me.GetCallType(item) & ""
                    .Cells(rowIndex, 5) = Common.FormatTime(item.Duration) & ""
                    rowIndex += 1
                Next

                '========================================================================= 
                .Columns(1).ColumnWidth = 18
                .Columns(2).ColumnWidth = 18
                .Columns(3).ColumnWidth = 18
                .Columns(4).ColumnWidth = 15
                .Columns(5).ColumnWidth = 50

                '***** 格式设定 ******************************************************************************

                '标题
                .Rows(1).RowHeight = 25                 '行高

                '标题栏


                With .Range(.Cells(1, 1), .Cells(1, 5))
                    .Font.Size = 12
                    .Interior.ColorIndex = 45           '设置标题背景色为 浅橙色


                    .Font.Bold = True                   '标题字体加粗
                End With

                '表格格式（含标题栏）
                With .Range(.Cells(1, 1), .Cells(rowIndex, 5))
                    .Borders.LineStyle = 1              '设表格边框样式


                    .Font.Size = 10                     '设置表格内容的字体为 10 号字
                End With

                With .Range(.Cells(1, 1), .Cells(rowIndex, 1))
                    .HorizontalAlignment = -4152        '水平居右
                    .VerticalAlignment = -4108          '垂直居中
                End With
            End With

            If Not (xlSheet Is Nothing) Then
                xlSheet.SaveAs(strOutputFile)           '文件另存为

                'Me.xlBook.Close()
                'xlApp.Visible = True                    '显示导出后的 Excel 内容
                'xlApp.Quit()                           '退出 Excel
            End If

            'If xlApp IsNot Nothing Then
            '    xlApp.Quit()
            'End If

            Return True
        Catch ex As Exception
            If xlApp IsNot Nothing Then
                xlApp.Quit()
            End If
            Common.LogException(ex.ToString())

            Return False
        End Try
    End Function

    Public Function ExportToTxt(ByVal lstCallHistory As List(Of CallHistory), ByVal strOutputFile As String) As Boolean
        If lstCallHistory Is Nothing OrElse lstCallHistory.Count <= 0 Then
            Return False
        End If

        Try
            Using sw As New System.IO.StreamWriter(strOutputFile, True, System.Text.Encoding.UTF8)
                '"通话记录"   "导出时间"
                sw.WriteLine(String.Format("{0}     {1}:{2}", Me.Language.GetString("Main.Button.CallHistory"), Me.Language.GetString("Backup.Label.ExportDate"), DateTime.Now.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))))
                sw.WriteLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")
                '"时间"   "联络人"   "电话号码"  "类型"  "通话时长"
                sw.WriteLine(String.Format("{0}{1}{2}{3}{4}", _
                                                  Common.StringFormat(Me.Language.GetString("App.Button.TimeSort"), 25, False, True, True, False), _
                                                  Common.StringFormat(Me.Language.GetString("Main.Button.Contact"), 20, False, True, True, False), _
                                                  Common.StringFormat(Me.Language.GetString("Welcome.Label.PhoneNumber"), 20, False, True, True, False), _
                                                  Common.StringFormat(Me.Language.GetString("Common.Label.ColumnType"), 20, False, True, True, False), _
                                                  Common.StringFormat(Me.Language.GetString("Backup.Label.CallDuration"), 20, False, True, True, False)))
                sw.WriteLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")

                For Each Item As CallHistory In lstCallHistory
                    Application.DoEvents()
                    sw.WriteLine(String.Format("{0}{1}{2}{3}{4}", _
                                                    Common.StringFormat(Item.CreateDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), 25, False, True, True, False), _
                                                    Common.StringFormat(Item.Name, 20, False, True, True, False), _
                                                    Common.StringFormat(Item.PhoneNumber, 20, False, True, True, False), _
                                                    Common.StringFormat(Me.GetCallType(Item), 20, False, True, True, False), _
                                                    Common.StringFormat(Common.FormatTime(Item.Duration), 20, False, True, True, False)))
                Next
                sw.WriteLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")
            End Using
            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function

    Private Function GetCallType(ByVal item As CallHistory) As String
        Dim strText As String = ""

        If item.Duration > 0 AndAlso item.CallInOut = CallInOut.CallIn Then
            'Accept
            strText = Me.Language.GetString("Backup.Label.CallHistoryAccept")           '"已接电话"

        ElseIf item.Duration <= 0 AndAlso item.CallInOut = CallInOut.CallIn Then
            'UnAccept
            strText = Me.Language.GetString("Backup.Label.CallHistoryUnAccept")         '"未接电话"

        Else
            'Out
            strText = Me.Language.GetString("Backup.Label.CallHistoryOut")              '"已拨电话"

        End If
        Return strText
    End Function

    Public Sub Close()
        Try
            Dim intGeneration As Integer = 0
            intGeneration = System.GC.GetGeneration(Me.xlApp)
            Me.xlSheet = Nothing
            Me.xlBook = Nothing
            Me.xlApp.Quit()
            Me.xlApp = Nothing
            System.GC.Collect(intGeneration)
        Catch ex As Exception
        End Try
    End Sub

End Class