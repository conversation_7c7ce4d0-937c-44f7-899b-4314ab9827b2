﻿Imports System.Threading
Imports iTong.CoreModule

Public Class BackupListDeleteHelper
    Implements IDisposable

    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private mLanguage As LanguageInterface = Nothing
    Private mTdDelete As Thread = Nothing

    Public Event BackupListDeleteEventHandler As EventHandler(Of BackupListDeleteEventArgs)

#Region "--- 单例 ---"

    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New()
        Me.mLanguage = LanguageInterface.Instance()

    End Sub

    Private Shared mHelper As BackupListDeleteHelper = Nothing

    Public Shared Function GetInstance() As BackupListDeleteHelper
        If mHelper Is Nothing Then
            mHelper = New BackupListDeleteHelper()
        End If
        Return mHelper
    End Function


#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then

                Try
                    If Me.mTdDelete IsNot Nothing AndAlso Me.mTdDelete.ThreadState <> ThreadState.Stopped Then
                        Me.mTdDelete.Abort()
                    End If
                Catch
                End Try
            End If
        End If
        disposedValue = True
    End Sub

#End Region

    Public Sub DeleteByRows(lstRows As List(Of tbDataGridViewRow))
        Try
            If Me.mTdDelete IsNot Nothing AndAlso Me.mTdDelete.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mTdDelete = New Thread(AddressOf DoDeleteByRows)
            Me.mTdDelete.IsBackground = True
            Me.mTdDelete.Start(lstRows)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteByRows")
        End Try
    End Sub

    Public Sub DoDeleteByRows(ByVal obj As Object)
        Try
            Dim args As BackupListDeleteEventArgs = Nothing
            Dim lstRows As List(Of tbDataGridViewRow) = CType(obj, List(Of tbDataGridViewRow))
            Dim lstFolderPath As New List(Of String)

            For Each Item As tbDataGridViewRow In lstRows
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is BackupInfo Then
                    lstFolderPath.Add(CType(Item.Tag, BackupInfo).FolderPath)
                    args = New BackupListDeleteEventArgs()
                    args.DelStatus = DeleteStatus.WaitDelete
                    args.StrFolderPath = CType(Item.Tag, BackupInfo).FolderPath
                    RaiseEvent BackupListDeleteEventHandler(Me, args)
                End If
            Next

            For Each strFolder As String In lstFolderPath
                Common.LogException(String.Format("使用者操作了删除备份：{0} ", strFolder), "DoDeleteByRows")
                Try
                    If Directory.Exists(strFolder) Then
                        Try
                            Dim intIndex As Integer = 0
                            Dim arrFolder As String() = Directory.GetDirectories(strFolder)

                            args = New BackupListDeleteEventArgs()
                            args.DelStatus = DeleteStatus.Deleteing
                            args.IntProgress = 1
                            args.StrFolderPath = strFolder
                            RaiseEvent BackupListDeleteEventHandler(Me, args)

                            For Each item As String In arrFolder
                                intIndex = intIndex + 1
                                Directory.Delete(item, True)

                                args = New BackupListDeleteEventArgs()
                                args.DelStatus = DeleteStatus.Deleteing
                                Dim intPro As Integer = intIndex / arrFolder.Length * 100%
                                If intPro > 1 AndAlso intPro <= 95 Then
                                    args.IntProgress = intPro
                                    args.StrFolderPath = strFolder
                                    RaiseEvent BackupListDeleteEventHandler(Me, args)
                                End If

                            Next

                            args = New BackupListDeleteEventArgs()
                            args.DelStatus = DeleteStatus.Deleteing
                            args.IntProgress = 95
                            args.StrFolderPath = strFolder
                            RaiseEvent BackupListDeleteEventHandler(Me, args)

                            Directory.Delete(strFolder, True)
                            Dim isSuccess As Boolean = Not Directory.Exists(strFolder)
                            args = New BackupListDeleteEventArgs()
                            args.DelStatus = IIf(isSuccess, DeleteStatus.Success, DeleteStatus.Failure)
                            args.IntProgress = 100
                            args.StrFolderPath = strFolder
                            RaiseEvent BackupListDeleteEventHandler(Me, args)

                        Catch ex As Exception
                            args = New BackupListDeleteEventArgs()
                            args.DelStatus = DeleteStatus.Failure
                            args.StrFolderPath = strFolder
                            RaiseEvent BackupListDeleteEventHandler(Me, args)
                        End Try
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoDeleteByRows_Delete")
                End Try
            Next
            args = New BackupListDeleteEventArgs()
            args.DelStatus = DeleteStatus.DeleteFinish
            RaiseEvent BackupListDeleteEventHandler(Me, args)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoDeleteByRows")
        End Try
    End Sub

    Public Function DeleteiTunesBackupPassword(language As LanguageInterface, device As iPhoneDevice) As Boolean
        '’"删除密码后备份的文件将不再加密，您确定删除吗？(以前加密备份的文件仍需要数据备份时的密码才能查看和恢复)"
        If tbMessageBox.Show(Me, language.GetString("Common.DeleteBackupPwd"), language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2, "", False, New String() {language.GetString("Common.Delete"), language.GetString("Common.Cancel")}) <> Windows.Forms.DialogResult.Yes Then
            Return False
        End If

        Dim isRelust As Boolean = False
        Dim strPwdForDecrypt As String = String.Empty
        Dim strPwdMsg As String = language.GetString("Common.VerifyPwd") '"请输入密码验证。"

DO_CONTINUE:
        If tbInputBox.Show(Me, strPwdMsg, _
                            language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, _
                            MessageBoxDefaultButton.Button1, strPwdForDecrypt, True) = Windows.Forms.DialogResult.OK Then
            '密码不能为空
            If String.IsNullOrEmpty(strPwdForDecrypt) Then
                GoTo DO_CONTINUE
            End If
            If Not device.mb2_ChangePW(String.Empty, strPwdForDecrypt) Then
                strPwdMsg = language.GetString("Common.AgainVerifyPwd") '"删除失败，请重新输入密码验证"
                GoTo DO_CONTINUE
            Else
                'SetCheckEncrypt(False)
                tbMessageBox.Show(Me, language.GetString("Music.Message.DeleteSuccessed"), language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
                isRelust = True
            End If
        Else

            If ServerIniSetting.GetShowWechatBackupTool Then
                '可以试试【微信备份助手】，可以在不用数据线的情况下导出微信的消息。
                '是否需要了解？
                Dim strMsg As String = String.Format("{0}{1}{2}", _
                                                 language.GetString("WechatAssistant.Message.TryUseWechatAssistant"), _
                                                 vbCrLf, _
                                                 language.GetString("WechatAssistant.Message.NeedToKnow"))
                If tbMessageBox.Show(Me, _
                                     strMsg, _
                                     language.GetString("Common.Info"), _
                                     MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Yes Then

                    Common.OpenExplorer(ServerIniSetting.GetUrlWechatBackupTooliOSBackupFailed)
                End If
            End If
        End If


        '        '"删除密码后备份的文件将不再加密，您确定删除吗？(以前加密备份的文件仍需要数据备份时的密码才能查看和恢复)"
        '        If tbMessageBox.Show(Me, language.GetString("Common.DeleteBackupPwd"), language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2, "", False, New String() {Me.Language.GetString("Common.Delete"), Me.Language.GetString("Common.Cancel")}) <> Windows.Forms.DialogResult.Yes Then
        '            Return False
        '        End If

        '        Dim isRelust As Boolean = False

        '        Dim strPwdForDecrypt As String = String.Empty
        '        Dim strPwdMsg As String = language.GetString("Common.VerifyPwd") '"请输入密码验证。"
        'DO_CONTINUE:
        '        If tbInputBox.Show(Me, strPwdMsg, _
        '                                     language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, _
        '                                     MessageBoxDefaultButton.Button1, strPwdForDecrypt, True) = Windows.Forms.DialogResult.OK Then
        '            '密码不能为空
        '            If String.IsNullOrEmpty(strPwdForDecrypt) Then
        '                GoTo DO_CONTINUE
        '            End If
        '            If Not device.mb2_ChangePW(String.Empty, strPwdForDecrypt) Then
        '                strPwdMsg = language.GetString("Common.AgainVerifyPwd") '"删除失败，请重新输入密码验证"
        '                GoTo DO_CONTINUE
        '            Else
        '                SetBackupPwd(False)
        '                tbMessageBox.Show(Me, language.GetString("Music.Message.DeleteSuccessed"), language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
        '                isRelust = True
        '            End If
        '        End If

        Return isRelust
    End Function

End Class

Public Class BackupListDeleteEventArgs
    Inherits EventArgs

    Private mStrFolderPath As String = ""
    Public Property StrFolderPath() As String
        Get
            Return Me.mStrFolderPath
        End Get
        Set(ByVal value As String)
            Me.mStrFolderPath = value
        End Set
    End Property

    Private mDelStatus As DeleteStatus = DeleteStatus.WaitDelete
    Public Property DelStatus() As DeleteStatus
        Get
            Return Me.mDelStatus
        End Get
        Set(ByVal value As DeleteStatus)
            Me.mDelStatus = value
        End Set
    End Property

    Private mIntProgress As Integer = 0
    Public Property IntProgress() As Integer
        Get
            Return Me.mIntProgress
        End Get
        Set(ByVal value As Integer)
            Me.mIntProgress = value
        End Set
    End Property

End Class

Public Enum DeleteStatus
    WaitDelete
    Deleteing
    Success
    Failure
    DeleteFinish
End Enum
