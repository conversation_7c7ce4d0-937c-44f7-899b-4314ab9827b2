﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmBackupList
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBackupList))
        Me.pnlControl = New iTong.Components.tbPanel()
        Me.btnRestoreSMS = New iTong.Components.tbButton()
        Me.btnCopyBackFiles = New iTong.Components.tbButton()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnFoundFailure = New iTong.Components.tbButton()
        Me.btnRestore = New iTong.Components.tbButton()
        Me.btnBackup = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.btnImport = New iTong.Components.tbButton()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.munOperator = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpenFolder = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator1 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiOpen = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlDescription = New iTong.Components.tbPanel()
        Me.btnNext = New iTong.Components.tbButton()
        Me.lblSetting = New iTong.Components.tbLabel()
        Me.lblAppData = New iTong.Components.tbLabel()
        Me.lblNote = New iTong.Components.tbLabel()
        Me.lblContact = New iTong.Components.tbLabel()
        Me.lblPhoto = New iTong.Components.tbLabel()
        Me.lblSMS = New iTong.Components.tbLabel()
        Me.lblDescription = New System.Windows.Forms.Label()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.picDevice = New System.Windows.Forms.PictureBox()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.pnlControl.SuspendLayout()
        Me.munOperator.SuspendLayout()
        Me.pnlDescription.SuspendLayout()
        CType(Me.picDevice, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'pnlControl
        '
        Me.pnlControl.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlControl.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlControl.Controls.Add(Me.btnRestoreSMS)
        Me.pnlControl.Controls.Add(Me.btnCopyBackFiles)
        Me.pnlControl.Controls.Add(Me.btnRefresh)
        Me.pnlControl.Controls.Add(Me.btnFoundFailure)
        Me.pnlControl.Controls.Add(Me.btnRestore)
        Me.pnlControl.Controls.Add(Me.btnBackup)
        Me.pnlControl.Controls.Add(Me.btnDelete)
        Me.pnlControl.Controls.Add(Me.btnImport)
        Me.pnlControl.Location = New System.Drawing.Point(1, 32)
        Me.pnlControl.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlControl.Name = "pnlControl"
        Me.pnlControl.Size = New System.Drawing.Size(798, 33)
        Me.pnlControl.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlControl.TabIndex = 22
        Me.pnlControl.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlControl.tbShowWatermark = False
        Me.pnlControl.tbSplit = "3,3,3,3"
        Me.pnlControl.tbWatermark = Nothing
        Me.pnlControl.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlControl.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRestoreSMS
        '
        Me.btnRestoreSMS.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRestoreSMS.BackColor = System.Drawing.Color.Transparent
        Me.btnRestoreSMS.BindingForm = Nothing
        Me.btnRestoreSMS.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRestoreSMS.Location = New System.Drawing.Point(582, 5)
        Me.btnRestoreSMS.Name = "btnRestoreSMS"
        Me.btnRestoreSMS.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRestoreSMS.Selectable = True
        Me.btnRestoreSMS.Size = New System.Drawing.Size(117, 23)
        Me.btnRestoreSMS.TabIndex = 23
        Me.btnRestoreSMS.tbAdriftIconWhenHover = False
        Me.btnRestoreSMS.tbAutoSize = False
        Me.btnRestoreSMS.tbAutoSizeEx = True
        Me.btnRestoreSMS.tbBackgroundImage = Nothing
        Me.btnRestoreSMS.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRestoreSMS.tbBadgeNumber = 0
        Me.btnRestoreSMS.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRestoreSMS.tbEndEllipsis = False
        Me.btnRestoreSMS.tbIconHoldPlace = True
        Me.btnRestoreSMS.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_restore
        Me.btnRestoreSMS.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRestoreSMS.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRestoreSMS.tbIconMore = False
        Me.btnRestoreSMS.tbIconMouseDown = Nothing
        Me.btnRestoreSMS.tbIconMouseHover = Nothing
        Me.btnRestoreSMS.tbIconMouseLeave = Nothing
        Me.btnRestoreSMS.tbIconPlaceText = 2
        Me.btnRestoreSMS.tbIconReadOnly = Nothing
        Me.btnRestoreSMS.tbImageMouseDown = Nothing
        Me.btnRestoreSMS.tbImageMouseHover = Nothing
        Me.btnRestoreSMS.tbImageMouseLeave = Nothing
        Me.btnRestoreSMS.tbProgressValue = 50
        Me.btnRestoreSMS.tbReadOnly = False
        Me.btnRestoreSMS.tbReadOnlyText = False
        Me.btnRestoreSMS.tbShadow = False
        Me.btnRestoreSMS.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRestoreSMS.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRestoreSMS.tbShowDot = False
        Me.btnRestoreSMS.tbShowMoreIconImg = CType(resources.GetObject("btnRestoreSMS.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRestoreSMS.tbShowNew = False
        Me.btnRestoreSMS.tbShowProgress = False
        Me.btnRestoreSMS.tbShowTip = True
        Me.btnRestoreSMS.tbShowToolTipOnButton = False
        Me.btnRestoreSMS.tbSplit = "13,11,13,11"
        Me.btnRestoreSMS.tbText = "从备份恢复短信"
        Me.btnRestoreSMS.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRestoreSMS.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnRestoreSMS.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRestoreSMS.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRestoreSMS.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRestoreSMS.tbTextMouseDownPlace = 2
        Me.btnRestoreSMS.tbToolTip = ""
        Me.btnRestoreSMS.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRestoreSMS.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRestoreSMS.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestoreSMS.Visible = False
        Me.btnRestoreSMS.VisibleEx = True
        '
        'btnCopyBackFiles
        '
        Me.btnCopyBackFiles.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnCopyBackFiles.BackColor = System.Drawing.Color.Transparent
        Me.btnCopyBackFiles.BindingForm = Nothing
        Me.btnCopyBackFiles.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCopyBackFiles.Location = New System.Drawing.Point(474, 5)
        Me.btnCopyBackFiles.Name = "btnCopyBackFiles"
        Me.btnCopyBackFiles.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCopyBackFiles.Selectable = True
        Me.btnCopyBackFiles.Size = New System.Drawing.Size(111, 23)
        Me.btnCopyBackFiles.TabIndex = 22
        Me.btnCopyBackFiles.tbAdriftIconWhenHover = False
        Me.btnCopyBackFiles.tbAutoSize = False
        Me.btnCopyBackFiles.tbAutoSizeEx = True
        Me.btnCopyBackFiles.tbBackgroundImage = Nothing
        Me.btnCopyBackFiles.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCopyBackFiles.tbBadgeNumber = 0
        Me.btnCopyBackFiles.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopyBackFiles.tbEndEllipsis = False
        Me.btnCopyBackFiles.tbIconHoldPlace = True
        Me.btnCopyBackFiles.tbIconImage = Global.iTong.My.Resources.Resources.btn_backup_4
        Me.btnCopyBackFiles.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyBackFiles.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnCopyBackFiles.tbIconMore = False
        Me.btnCopyBackFiles.tbIconMouseDown = Nothing
        Me.btnCopyBackFiles.tbIconMouseHover = Nothing
        Me.btnCopyBackFiles.tbIconMouseLeave = Nothing
        Me.btnCopyBackFiles.tbIconPlaceText = 2
        Me.btnCopyBackFiles.tbIconReadOnly = Nothing
        Me.btnCopyBackFiles.tbImageMouseDown = Nothing
        Me.btnCopyBackFiles.tbImageMouseHover = Nothing
        Me.btnCopyBackFiles.tbImageMouseLeave = Nothing
        Me.btnCopyBackFiles.tbProgressValue = 50
        Me.btnCopyBackFiles.tbReadOnly = False
        Me.btnCopyBackFiles.tbReadOnlyText = False
        Me.btnCopyBackFiles.tbShadow = False
        Me.btnCopyBackFiles.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopyBackFiles.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopyBackFiles.tbShowDot = False
        Me.btnCopyBackFiles.tbShowMoreIconImg = CType(resources.GetObject("btnCopyBackFiles.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCopyBackFiles.tbShowNew = False
        Me.btnCopyBackFiles.tbShowProgress = False
        Me.btnCopyBackFiles.tbShowTip = True
        Me.btnCopyBackFiles.tbShowToolTipOnButton = False
        Me.btnCopyBackFiles.tbSplit = "13,11,13,11"
        Me.btnCopyBackFiles.tbText = "iOS10备份降级"
        Me.btnCopyBackFiles.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyBackFiles.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnCopyBackFiles.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnCopyBackFiles.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnCopyBackFiles.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnCopyBackFiles.tbTextMouseDownPlace = 2
        Me.btnCopyBackFiles.tbToolTip = ""
        Me.btnCopyBackFiles.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCopyBackFiles.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopyBackFiles.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyBackFiles.Visible = False
        Me.btnCopyBackFiles.VisibleEx = True
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(722, 5)
        Me.btnRefresh.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(63, 23)
        Me.btnRefresh.TabIndex = 21
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = " 刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 2
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnFoundFailure
        '
        Me.btnFoundFailure.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnFoundFailure.BackColor = System.Drawing.Color.Transparent
        Me.btnFoundFailure.BindingForm = Nothing
        Me.btnFoundFailure.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFoundFailure.Location = New System.Drawing.Point(333, 5)
        Me.btnFoundFailure.Name = "btnFoundFailure"
        Me.btnFoundFailure.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFoundFailure.Selectable = True
        Me.btnFoundFailure.Size = New System.Drawing.Size(129, 23)
        Me.btnFoundFailure.TabIndex = 11
        Me.btnFoundFailure.tbAdriftIconWhenHover = False
        Me.btnFoundFailure.tbAutoSize = False
        Me.btnFoundFailure.tbAutoSizeEx = True
        Me.btnFoundFailure.tbBackgroundImage = Nothing
        Me.btnFoundFailure.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFoundFailure.tbBadgeNumber = 0
        Me.btnFoundFailure.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFoundFailure.tbEndEllipsis = False
        Me.btnFoundFailure.tbIconHoldPlace = True
        Me.btnFoundFailure.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_failurerestore
        Me.btnFoundFailure.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFoundFailure.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnFoundFailure.tbIconMore = False
        Me.btnFoundFailure.tbIconMouseDown = Nothing
        Me.btnFoundFailure.tbIconMouseHover = Nothing
        Me.btnFoundFailure.tbIconMouseLeave = Nothing
        Me.btnFoundFailure.tbIconPlaceText = 2
        Me.btnFoundFailure.tbIconReadOnly = Nothing
        Me.btnFoundFailure.tbImageMouseDown = Nothing
        Me.btnFoundFailure.tbImageMouseHover = Nothing
        Me.btnFoundFailure.tbImageMouseLeave = Nothing
        Me.btnFoundFailure.tbProgressValue = 50
        Me.btnFoundFailure.tbReadOnly = False
        Me.btnFoundFailure.tbReadOnlyText = False
        Me.btnFoundFailure.tbShadow = False
        Me.btnFoundFailure.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFoundFailure.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFoundFailure.tbShowDot = False
        Me.btnFoundFailure.tbShowMoreIconImg = CType(resources.GetObject("btnFoundFailure.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFoundFailure.tbShowNew = False
        Me.btnFoundFailure.tbShowProgress = False
        Me.btnFoundFailure.tbShowTip = True
        Me.btnFoundFailure.tbShowToolTipOnButton = False
        Me.btnFoundFailure.tbSplit = "13,11,13,11"
        Me.btnFoundFailure.tbText = "查找备份失败记录"
        Me.btnFoundFailure.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFoundFailure.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFoundFailure.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnFoundFailure.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnFoundFailure.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnFoundFailure.tbTextMouseDownPlace = 2
        Me.btnFoundFailure.tbToolTip = ""
        Me.btnFoundFailure.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFoundFailure.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFoundFailure.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFoundFailure.VisibleEx = True
        '
        'btnRestore
        '
        Me.btnRestore.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRestore.BackColor = System.Drawing.Color.Transparent
        Me.btnRestore.BindingForm = Nothing
        Me.btnRestore.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRestore.Location = New System.Drawing.Point(231, 5)
        Me.btnRestore.Name = "btnRestore"
        Me.btnRestore.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRestore.Selectable = True
        Me.btnRestore.Size = New System.Drawing.Size(93, 23)
        Me.btnRestore.TabIndex = 8
        Me.btnRestore.tbAdriftIconWhenHover = False
        Me.btnRestore.tbAutoSize = False
        Me.btnRestore.tbAutoSizeEx = True
        Me.btnRestore.tbBackgroundImage = Nothing
        Me.btnRestore.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRestore.tbBadgeNumber = 0
        Me.btnRestore.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRestore.tbEndEllipsis = False
        Me.btnRestore.tbIconHoldPlace = True
        Me.btnRestore.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_restore
        Me.btnRestore.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRestore.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRestore.tbIconMore = False
        Me.btnRestore.tbIconMouseDown = Nothing
        Me.btnRestore.tbIconMouseHover = Nothing
        Me.btnRestore.tbIconMouseLeave = Nothing
        Me.btnRestore.tbIconPlaceText = 2
        Me.btnRestore.tbIconReadOnly = Nothing
        Me.btnRestore.tbImageMouseDown = Nothing
        Me.btnRestore.tbImageMouseHover = Nothing
        Me.btnRestore.tbImageMouseLeave = Nothing
        Me.btnRestore.tbProgressValue = 50
        Me.btnRestore.tbReadOnly = False
        Me.btnRestore.tbReadOnlyText = False
        Me.btnRestore.tbShadow = False
        Me.btnRestore.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRestore.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRestore.tbShowDot = False
        Me.btnRestore.tbShowMoreIconImg = CType(resources.GetObject("btnRestore.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRestore.tbShowNew = False
        Me.btnRestore.tbShowProgress = False
        Me.btnRestore.tbShowTip = True
        Me.btnRestore.tbShowToolTipOnButton = False
        Me.btnRestore.tbSplit = "13,11,13,11"
        Me.btnRestore.tbText = "从备份恢复"
        Me.btnRestore.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRestore.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnRestore.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRestore.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRestore.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRestore.tbTextMouseDownPlace = 2
        Me.btnRestore.tbToolTip = ""
        Me.btnRestore.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRestore.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRestore.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestore.Visible = False
        Me.btnRestore.VisibleEx = True
        '
        'btnBackup
        '
        Me.btnBackup.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnBackup.BackColor = System.Drawing.Color.Transparent
        Me.btnBackup.BindingForm = Nothing
        Me.btnBackup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBackup.Location = New System.Drawing.Point(133, 5)
        Me.btnBackup.Name = "btnBackup"
        Me.btnBackup.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBackup.Selectable = True
        Me.btnBackup.Size = New System.Drawing.Size(81, 23)
        Me.btnBackup.TabIndex = 7
        Me.btnBackup.tbAdriftIconWhenHover = False
        Me.btnBackup.tbAutoSize = False
        Me.btnBackup.tbAutoSizeEx = True
        Me.btnBackup.tbBackgroundImage = Nothing
        Me.btnBackup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBackup.tbBadgeNumber = 0
        Me.btnBackup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackup.tbEndEllipsis = False
        Me.btnBackup.tbIconHoldPlace = True
        Me.btnBackup.tbIconImage = Global.iTong.My.Resources.Resources.btn_backup_4
        Me.btnBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnBackup.tbIconMore = False
        Me.btnBackup.tbIconMouseDown = Nothing
        Me.btnBackup.tbIconMouseHover = Nothing
        Me.btnBackup.tbIconMouseLeave = Nothing
        Me.btnBackup.tbIconPlaceText = 2
        Me.btnBackup.tbIconReadOnly = Nothing
        Me.btnBackup.tbImageMouseDown = Nothing
        Me.btnBackup.tbImageMouseHover = Nothing
        Me.btnBackup.tbImageMouseLeave = Nothing
        Me.btnBackup.tbProgressValue = 50
        Me.btnBackup.tbReadOnly = False
        Me.btnBackup.tbReadOnlyText = False
        Me.btnBackup.tbShadow = False
        Me.btnBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackup.tbShowDot = False
        Me.btnBackup.tbShowMoreIconImg = CType(resources.GetObject("btnBackup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBackup.tbShowNew = False
        Me.btnBackup.tbShowProgress = False
        Me.btnBackup.tbShowTip = True
        Me.btnBackup.tbShowToolTipOnButton = False
        Me.btnBackup.tbSplit = "13,11,13,11"
        Me.btnBackup.tbText = "创建备份"
        Me.btnBackup.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnBackup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnBackup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackup.tbTextMouseDownPlace = 2
        Me.btnBackup.tbToolTip = ""
        Me.btnBackup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackup.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(72, 5)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(57, 23)
        Me.btnDelete.TabIndex = 5
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "13,11,13,11"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'btnImport
        '
        Me.btnImport.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnImport.BackColor = System.Drawing.Color.Transparent
        Me.btnImport.BindingForm = Nothing
        Me.btnImport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImport.Location = New System.Drawing.Point(8, 5)
        Me.btnImport.Name = "btnImport"
        Me.btnImport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImport.Selectable = True
        Me.btnImport.Size = New System.Drawing.Size(57, 23)
        Me.btnImport.TabIndex = 3
        Me.btnImport.tbAdriftIconWhenHover = False
        Me.btnImport.tbAutoSize = False
        Me.btnImport.tbAutoSizeEx = True
        Me.btnImport.tbBackgroundImage = Nothing
        Me.btnImport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbBadgeNumber = 0
        Me.btnImport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImport.tbEndEllipsis = False
        Me.btnImport.tbIconHoldPlace = True
        Me.btnImport.tbIconImage = Global.iTong.My.Resources.Resources.btn_addfolder_4
        Me.btnImport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbIconMore = False
        Me.btnImport.tbIconMouseDown = Nothing
        Me.btnImport.tbIconMouseHover = Nothing
        Me.btnImport.tbIconMouseLeave = Nothing
        Me.btnImport.tbIconPlaceText = 2
        Me.btnImport.tbIconReadOnly = Nothing
        Me.btnImport.tbImageMouseDown = Nothing
        Me.btnImport.tbImageMouseHover = Nothing
        Me.btnImport.tbImageMouseLeave = Nothing
        Me.btnImport.tbProgressValue = 50
        Me.btnImport.tbReadOnly = False
        Me.btnImport.tbReadOnlyText = False
        Me.btnImport.tbShadow = False
        Me.btnImport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImport.tbShowDot = False
        Me.btnImport.tbShowMoreIconImg = CType(resources.GetObject("btnImport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImport.tbShowNew = False
        Me.btnImport.tbShowProgress = False
        Me.btnImport.tbShowTip = True
        Me.btnImport.tbShowToolTipOnButton = False
        Me.btnImport.tbSplit = "13,11,13,11"
        Me.btnImport.tbText = "导入"
        Me.btnImport.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnImport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextMouseDownPlace = 2
        Me.btnImport.tbToolTip = ""
        Me.btnImport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.VisibleEx = True
        '
        'pnlContainer
        '
        Me.pnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainer.BackColor = System.Drawing.SystemColors.Control
        Me.pnlContainer.Location = New System.Drawing.Point(1, 65)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(798, 508)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 23
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(87, 577)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(627, 18)
        Me.lblMessage.TabIndex = 30
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = False
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'munOperator
        '
        Me.munOperator.AccessibleDescription = "179x98"
        Me.munOperator.DropShadowEnabled = False
        Me.munOperator.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munOperator.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenFolder, Me.ToolStripSeparator1, Me.tsmiOpen, Me.tsmiDelete})
        Me.munOperator.Name = "munSearch"
        Me.munOperator.Size = New System.Drawing.Size(179, 76)
        Me.munOperator.tbBackColor = System.Drawing.Color.White
        Me.munOperator.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munOperator.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munOperator.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenFolder
        '
        Me.tsmiOpenFolder.Name = "tsmiOpenFolder"
        Me.tsmiOpenFolder.Size = New System.Drawing.Size(178, 22)
        Me.tsmiOpenFolder.Text = "打开文件所在的目录"
        '
        'ToolStripSeparator1
        '
        Me.ToolStripSeparator1.Name = "ToolStripSeparator1"
        Me.ToolStripSeparator1.Size = New System.Drawing.Size(175, 6)
        '
        'tsmiOpen
        '
        Me.tsmiOpen.Name = "tsmiOpen"
        Me.tsmiOpen.Size = New System.Drawing.Size(178, 22)
        Me.tsmiOpen.Text = "打开"
        '
        'tsmiDelete
        '
        Me.tsmiDelete.Name = "tsmiDelete"
        Me.tsmiDelete.Size = New System.Drawing.Size(178, 22)
        Me.tsmiDelete.Text = "删除"
        '
        'pnlDescription
        '
        Me.pnlDescription.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlDescription.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlDescription.Controls.Add(Me.btnNext)
        Me.pnlDescription.Controls.Add(Me.lblSetting)
        Me.pnlDescription.Controls.Add(Me.lblAppData)
        Me.pnlDescription.Controls.Add(Me.lblNote)
        Me.pnlDescription.Controls.Add(Me.lblContact)
        Me.pnlDescription.Controls.Add(Me.lblPhoto)
        Me.pnlDescription.Controls.Add(Me.lblSMS)
        Me.pnlDescription.Controls.Add(Me.lblDescription)
        Me.pnlDescription.Controls.Add(Me.lblTitle)
        Me.pnlDescription.Controls.Add(Me.picDevice)
        Me.pnlDescription.Location = New System.Drawing.Point(0, 161)
        Me.pnlDescription.Name = "pnlDescription"
        Me.pnlDescription.Size = New System.Drawing.Size(800, 403)
        Me.pnlDescription.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescription.TabIndex = 35
        Me.pnlDescription.tbBackgroundImage = Nothing
        Me.pnlDescription.tbShowWatermark = False
        Me.pnlDescription.tbSplit = "0,0,0,0"
        Me.pnlDescription.tbWatermark = Nothing
        Me.pnlDescription.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescription.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlDescription.Visible = False
        '
        'btnNext
        '
        Me.btnNext.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnNext.BackColor = System.Drawing.Color.Transparent
        Me.btnNext.BindingForm = Nothing
        Me.btnNext.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnNext.Location = New System.Drawing.Point(639, 374)
        Me.btnNext.Name = "btnNext"
        Me.btnNext.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnNext.Selectable = True
        Me.btnNext.Size = New System.Drawing.Size(86, 27)
        Me.btnNext.TabIndex = 40
        Me.btnNext.tbAdriftIconWhenHover = False
        Me.btnNext.tbAutoSize = False
        Me.btnNext.tbAutoSizeEx = False
        Me.btnNext.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnNext.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnNext.tbBadgeNumber = 0
        Me.btnNext.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnNext.tbEndEllipsis = False
        Me.btnNext.tbIconHoldPlace = True
        Me.btnNext.tbIconImage = Nothing
        Me.btnNext.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNext.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnNext.tbIconMore = False
        Me.btnNext.tbIconMouseDown = Nothing
        Me.btnNext.tbIconMouseHover = Nothing
        Me.btnNext.tbIconMouseLeave = Nothing
        Me.btnNext.tbIconPlaceText = 2
        Me.btnNext.tbIconReadOnly = Nothing
        Me.btnNext.tbImageMouseDown = Nothing
        Me.btnNext.tbImageMouseHover = Nothing
        Me.btnNext.tbImageMouseLeave = Nothing
        Me.btnNext.tbProgressValue = 50
        Me.btnNext.tbReadOnly = False
        Me.btnNext.tbReadOnlyText = False
        Me.btnNext.tbShadow = False
        Me.btnNext.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnNext.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnNext.tbShowDot = False
        Me.btnNext.tbShowMoreIconImg = CType(resources.GetObject("btnNext.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnNext.tbShowNew = False
        Me.btnNext.tbShowProgress = False
        Me.btnNext.tbShowTip = True
        Me.btnNext.tbShowToolTipOnButton = False
        Me.btnNext.tbSplit = "13,11,13,11"
        Me.btnNext.tbText = "下一步"
        Me.btnNext.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNext.tbTextColor = System.Drawing.Color.White
        Me.btnNext.tbTextColorDisable = System.Drawing.Color.White
        Me.btnNext.tbTextColorDown = System.Drawing.Color.White
        Me.btnNext.tbTextColorHover = System.Drawing.Color.White
        Me.btnNext.tbTextMouseDownPlace = 0
        Me.btnNext.tbToolTip = ""
        Me.btnNext.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnNext.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnNext.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNext.VisibleEx = True
        '
        'lblSetting
        '
        Me.lblSetting.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetting.Location = New System.Drawing.Point(538, 275)
        Me.lblSetting.Name = "lblSetting"
        Me.lblSetting.Size = New System.Drawing.Size(187, 44)
        Me.lblSetting.TabIndex = 35
        Me.lblSetting.tbAdriftWhenHover = False
        Me.lblSetting.tbAutoEllipsis = False
        Me.lblSetting.tbAutoSize = False
        Me.lblSetting.tbHideImage = False
        Me.lblSetting.tbIconImage = Global.iTong.My.Resources.Resources.icon_setting
        Me.lblSetting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSetting.tbIconPlaceText = 5
        Me.lblSetting.tbShadow = False
        Me.lblSetting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSetting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSetting.tbShowScrolling = False
        Me.lblSetting.Text = "配制信息"
        Me.lblSetting.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblAppData
        '
        Me.lblAppData.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblAppData.Location = New System.Drawing.Point(330, 275)
        Me.lblAppData.Name = "lblAppData"
        Me.lblAppData.Size = New System.Drawing.Size(187, 44)
        Me.lblAppData.TabIndex = 34
        Me.lblAppData.tbAdriftWhenHover = False
        Me.lblAppData.tbAutoEllipsis = False
        Me.lblAppData.tbAutoSize = False
        Me.lblAppData.tbHideImage = False
        Me.lblAppData.tbIconImage = Global.iTong.My.Resources.Resources.icon_appdata
        Me.lblAppData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblAppData.tbIconPlaceText = 5
        Me.lblAppData.tbShadow = False
        Me.lblAppData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblAppData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblAppData.tbShowScrolling = False
        Me.lblAppData.Text = "应用程序数据"
        Me.lblAppData.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblNote
        '
        Me.lblNote.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNote.Location = New System.Drawing.Point(538, 207)
        Me.lblNote.Name = "lblNote"
        Me.lblNote.Size = New System.Drawing.Size(187, 44)
        Me.lblNote.TabIndex = 33
        Me.lblNote.tbAdriftWhenHover = False
        Me.lblNote.tbAutoEllipsis = False
        Me.lblNote.tbAutoSize = False
        Me.lblNote.tbHideImage = False
        Me.lblNote.tbIconImage = Global.iTong.My.Resources.Resources.icon_note
        Me.lblNote.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNote.tbIconPlaceText = 5
        Me.lblNote.tbShadow = False
        Me.lblNote.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNote.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNote.tbShowScrolling = False
        Me.lblNote.Text = "备忘录"
        Me.lblNote.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblContact
        '
        Me.lblContact.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblContact.Location = New System.Drawing.Point(330, 207)
        Me.lblContact.Name = "lblContact"
        Me.lblContact.Size = New System.Drawing.Size(187, 44)
        Me.lblContact.TabIndex = 32
        Me.lblContact.tbAdriftWhenHover = False
        Me.lblContact.tbAutoEllipsis = False
        Me.lblContact.tbAutoSize = False
        Me.lblContact.tbHideImage = False
        Me.lblContact.tbIconImage = Global.iTong.My.Resources.Resources.icon_contact
        Me.lblContact.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblContact.tbIconPlaceText = 5
        Me.lblContact.tbShadow = False
        Me.lblContact.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblContact.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblContact.tbShowScrolling = False
        Me.lblContact.Text = "联系人"
        Me.lblContact.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblPhoto
        '
        Me.lblPhoto.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblPhoto.Location = New System.Drawing.Point(538, 139)
        Me.lblPhoto.Name = "lblPhoto"
        Me.lblPhoto.Size = New System.Drawing.Size(187, 44)
        Me.lblPhoto.TabIndex = 31
        Me.lblPhoto.tbAdriftWhenHover = False
        Me.lblPhoto.tbAutoEllipsis = False
        Me.lblPhoto.tbAutoSize = False
        Me.lblPhoto.tbHideImage = False
        Me.lblPhoto.tbIconImage = Global.iTong.My.Resources.Resources.icon_photo
        Me.lblPhoto.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblPhoto.tbIconPlaceText = 5
        Me.lblPhoto.tbShadow = False
        Me.lblPhoto.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblPhoto.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblPhoto.tbShowScrolling = False
        Me.lblPhoto.Text = "照片（相机胶卷）"
        Me.lblPhoto.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSMS
        '
        Me.lblSMS.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSMS.Location = New System.Drawing.Point(330, 139)
        Me.lblSMS.Name = "lblSMS"
        Me.lblSMS.Size = New System.Drawing.Size(187, 44)
        Me.lblSMS.TabIndex = 30
        Me.lblSMS.tbAdriftWhenHover = False
        Me.lblSMS.tbAutoEllipsis = False
        Me.lblSMS.tbAutoSize = False
        Me.lblSMS.tbHideImage = False
        Me.lblSMS.tbIconImage = Global.iTong.My.Resources.Resources.icon_sms
        Me.lblSMS.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSMS.tbIconPlaceText = 5
        Me.lblSMS.tbShadow = False
        Me.lblSMS.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSMS.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSMS.tbShowScrolling = False
        Me.lblSMS.Text = "短信"
        Me.lblSMS.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblDescription
        '
        Me.lblDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDescription.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.lblDescription.Location = New System.Drawing.Point(330, 73)
        Me.lblDescription.Name = "lblDescription"
        Me.lblDescription.Size = New System.Drawing.Size(410, 40)
        Me.lblDescription.TabIndex = 29
        Me.lblDescription.Text = "将设备的重要数据进行备份，以免丢失，或从以往的备份中恢复数据。"
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblTitle.AutoSize = True
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.lblTitle.Location = New System.Drawing.Point(322, 12)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(59, 13)
        Me.lblTitle.TabIndex = 28
        Me.lblTitle.Text = "备份还原"
        '
        'picDevice
        '
        Me.picDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picDevice.BackColor = System.Drawing.Color.Transparent
        Me.picDevice.Image = Global.iTong.My.Resources.Resources.device_default
        Me.picDevice.Location = New System.Drawing.Point(44, -16)
        Me.picDevice.Name = "picDevice"
        Me.picDevice.Size = New System.Drawing.Size(250, 412)
        Me.picDevice.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picDevice.TabIndex = 25
        Me.picDevice.TabStop = False
        '
        'frmBackupList
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.pnlDescription)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.pnlContainer)
        Me.Controls.Add(Me.pnlControl)
        Me.Name = "frmBackupList"
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "备份还原"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlControl, 0)
        Me.Controls.SetChildIndex(Me.pnlContainer, 0)
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Controls.SetChildIndex(Me.pnlDescription, 0)
        Me.pnlControl.ResumeLayout(False)
        Me.munOperator.ResumeLayout(False)
        Me.pnlDescription.ResumeLayout(False)
        Me.pnlDescription.PerformLayout()
        CType(Me.picDevice, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlControl As tbPanel
    Friend WithEvents btnDelete As tbButton
    Friend WithEvents btnImport As tbButton
    Friend WithEvents btnRestore As tbButton
    Friend WithEvents btnBackup As tbButton
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents lblMessage As tbLabel
    Friend WithEvents munOperator As tbContextMenuStrip
    Friend WithEvents tsmiOpen As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiOpenFolder As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlDescription As tbPanel
    Friend WithEvents picDevice As System.Windows.Forms.PictureBox
    Friend WithEvents lblTitle As System.Windows.Forms.Label
    Friend WithEvents lblDescription As System.Windows.Forms.Label
    Friend WithEvents lblSMS As tbLabel
    Friend WithEvents lblSetting As tbLabel
    Friend WithEvents lblAppData As tbLabel
    Friend WithEvents lblNote As tbLabel
    Friend WithEvents lblContact As tbLabel
    Friend WithEvents lblPhoto As tbLabel
    Friend WithEvents btnNext As tbButton
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents ToolStripSeparator1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents btnFoundFailure As tbButton
    Friend WithEvents btnRefresh As iTong.Components.tbButton
    Friend WithEvents btnCopyBackFiles As iTong.Components.tbButton
    Friend WithEvents btnRestoreSMS As iTong.Components.tbButton
End Class
