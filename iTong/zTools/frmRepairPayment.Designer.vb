﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmRepairPayment
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmRepairPayment))
        Me.pnlDescription = New iTong.Components.tbPanel()
        Me.btnDescription = New iTong.Components.tbButton()
        Me.txtDescripton = New iTong.Components.tbRichTextBox()
        Me.btnStartRepair = New iTong.Components.tbButton()
        Me.pnlNoRecord = New iTong.Components.tbPanel()
        Me.picReason2 = New System.Windows.Forms.PictureBox()
        Me.picReason1 = New System.Windows.Forms.PictureBox()
        Me.lblNoRecordReason2 = New iTong.Components.tbLabel()
        Me.lblNoRecordReason1 = New iTong.Components.tbLabel()
        Me.lblNoRecordReason = New iTong.Components.tbLabel()
        Me.lblNoRecordTitle = New iTong.Components.tbLabel()
        Me.picNoRecord = New System.Windows.Forms.PictureBox()
        Me.btnStartRepairEx = New iTong.Components.tbButton()
        Me.pnlRecord = New iTong.Components.tbPanel()
        Me.pnlContral = New iTong.Components.tbPanel()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.btnMyPayment = New iTong.Components.tbButton()
        Me.btnGetTwoYuan = New iTong.Components.tbButton()
        Me.btnRepair = New iTong.Components.tbButton()
        Me.btnStartRepairEx1 = New iTong.Components.tbButton()
        Me.pnlGrid = New iTong.Components.tbPanel()
        Me.pnlTui = New iTong.Components.tbPanel()
        Me.pbarShowProgress = New iTong.Components.tbControlBar()
        Me.lblTuiProgress = New iTong.Components.tbLabel()
        Me.btnInstallTui = New iTong.Components.tbButton()
        Me.picInstallTui2 = New iTong.Components.tbLabel()
        Me.lblInstallTui1 = New iTong.Components.tbLabel()
        Me.picInstallTui = New System.Windows.Forms.PictureBox()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.lblLoadingEx = New iTong.Components.tbLabel()
        Me.pnlRecordRepair = New iTong.Components.tbPanel()
        Me.pnlEmpty = New iTong.Components.tbPanel()
        Me.split = New iTong.Components.tbButton()
        Me.picFileEmpty = New System.Windows.Forms.PictureBox()
        Me.lblFileEmpty = New iTong.Components.tbLabel()
        Me.pnlBottom = New iTong.Components.tbPanel()
        Me.lblTipJaibreak = New iTong.Components.tbLabel()
        Me.lblJaiBreak = New iTong.Components.tbLabel()
        Me.btnAllRepair = New iTong.Components.tbButton()
        Me.txtSearch = New iTong.Components.tbSearch()
        Me.btnSearch = New iTong.Components.tbButton()
        Me.txtNameValue = New iTong.Components.tbTextBox()
        Me.pnlGridRepair = New iTong.Components.tbPanel()
        Me.pnlDescription.SuspendLayout()
        Me.pnlNoRecord.SuspendLayout()
        CType(Me.picReason2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picReason1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picNoRecord, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlRecord.SuspendLayout()
        Me.pnlContral.SuspendLayout()
        Me.pnlTui.SuspendLayout()
        CType(Me.picInstallTui, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlRecordRepair.SuspendLayout()
        Me.pnlEmpty.SuspendLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlBottom.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(1743, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(1719, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(1695, 0)
        '
        'pnlDescription
        '
        Me.pnlDescription.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlDescription.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlDescription.Controls.Add(Me.btnDescription)
        Me.pnlDescription.Controls.Add(Me.txtDescripton)
        Me.pnlDescription.Controls.Add(Me.btnStartRepair)
        Me.pnlDescription.Location = New System.Drawing.Point(1, 12)
        Me.pnlDescription.Name = "pnlDescription"
        Me.pnlDescription.Size = New System.Drawing.Size(340, 582)
        Me.pnlDescription.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescription.TabIndex = 23
        Me.pnlDescription.tbBackgroundImage = Global.iTong.My.Resources.Resources.repair_bg_payment
        Me.pnlDescription.tbShowWatermark = False
        Me.pnlDescription.tbSplit = "849,188,12,19"
        Me.pnlDescription.tbWatermark = Nothing
        Me.pnlDescription.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescription.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnDescription
        '
        Me.btnDescription.BackColor = System.Drawing.Color.Transparent
        Me.btnDescription.BindingForm = Nothing
        Me.btnDescription.Location = New System.Drawing.Point(44, 202)
        Me.btnDescription.Name = "btnDescription"
        Me.btnDescription.Padding = New System.Windows.Forms.Padding(15, 2, 15, 2)
        Me.btnDescription.Selectable = True
        Me.btnDescription.Size = New System.Drawing.Size(83, 40)
        Me.btnDescription.TabIndex = 42
        Me.btnDescription.tbAdriftIconWhenHover = False
        Me.btnDescription.tbAutoSize = True
        Me.btnDescription.tbAutoSizeEx = True
        Me.btnDescription.tbBackgroundImage = Global.iTong.My.Resources.Resources.repair_bg_title
        Me.btnDescription.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDescription.tbBadgeNumber = 0
        Me.btnDescription.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDescription.tbEndEllipsis = False
        Me.btnDescription.tbIconHoldPlace = True
        Me.btnDescription.tbIconImage = Nothing
        Me.btnDescription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDescription.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDescription.tbIconMore = False
        Me.btnDescription.tbIconMouseDown = Nothing
        Me.btnDescription.tbIconMouseHover = Nothing
        Me.btnDescription.tbIconMouseLeave = Nothing
        Me.btnDescription.tbIconPlaceText = 2
        Me.btnDescription.tbIconReadOnly = Nothing
        Me.btnDescription.tbImageMouseDown = Nothing
        Me.btnDescription.tbImageMouseHover = Nothing
        Me.btnDescription.tbImageMouseLeave = Nothing
        Me.btnDescription.tbProgressValue = 50
        Me.btnDescription.tbReadOnly = False
        Me.btnDescription.tbReadOnlyText = False
        Me.btnDescription.tbShadow = False
        Me.btnDescription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDescription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDescription.tbShowDot = False
        Me.btnDescription.tbShowMoreIconImg = CType(resources.GetObject("btnDescription.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDescription.tbShowNew = False
        Me.btnDescription.tbShowProgress = False
        Me.btnDescription.tbShowTip = True
        Me.btnDescription.tbShowToolTipOnButton = False
        Me.btnDescription.tbSplit = "31,19,31,19"
        Me.btnDescription.tbText = "活动说明"
        Me.btnDescription.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDescription.tbTextColor = System.Drawing.Color.White
        Me.btnDescription.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDescription.tbTextColorDown = System.Drawing.Color.White
        Me.btnDescription.tbTextColorHover = System.Drawing.Color.White
        Me.btnDescription.tbTextMouseDownPlace = 0
        Me.btnDescription.tbToolTip = ""
        Me.btnDescription.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDescription.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDescription.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDescription.VisibleEx = True
        '
        'txtDescripton
        '
        Me.txtDescripton.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtDescripton.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.txtDescripton.Location = New System.Drawing.Point(46, 248)
        Me.txtDescripton.Name = "txtDescripton"
        Me.txtDescripton.Size = New System.Drawing.Size(270, 256)
        Me.txtDescripton.TabIndex = 41
        Me.txtDescripton.Text = ""
        '
        'btnStartRepair
        '
        Me.btnStartRepair.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnStartRepair.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnStartRepair.BindingForm = Nothing
        Me.btnStartRepair.Location = New System.Drawing.Point(89, 508)
        Me.btnStartRepair.Margin = New System.Windows.Forms.Padding(0)
        Me.btnStartRepair.Name = "btnStartRepair"
        Me.btnStartRepair.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStartRepair.Selectable = True
        Me.btnStartRepair.Size = New System.Drawing.Size(223, 47)
        Me.btnStartRepair.TabIndex = 40
        Me.btnStartRepair.tbAdriftIconWhenHover = False
        Me.btnStartRepair.tbAutoSize = False
        Me.btnStartRepair.tbAutoSizeEx = False
        Me.btnStartRepair.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnStartRepair.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStartRepair.tbBadgeNumber = 0
        Me.btnStartRepair.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStartRepair.tbEndEllipsis = False
        Me.btnStartRepair.tbIconHoldPlace = True
        Me.btnStartRepair.tbIconImage = Nothing
        Me.btnStartRepair.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepair.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStartRepair.tbIconMore = False
        Me.btnStartRepair.tbIconMouseDown = Nothing
        Me.btnStartRepair.tbIconMouseHover = Nothing
        Me.btnStartRepair.tbIconMouseLeave = Nothing
        Me.btnStartRepair.tbIconPlaceText = 0
        Me.btnStartRepair.tbIconReadOnly = Nothing
        Me.btnStartRepair.tbImageMouseDown = Nothing
        Me.btnStartRepair.tbImageMouseHover = Nothing
        Me.btnStartRepair.tbImageMouseLeave = Nothing
        Me.btnStartRepair.tbProgressValue = 50
        Me.btnStartRepair.tbReadOnly = False
        Me.btnStartRepair.tbReadOnlyText = False
        Me.btnStartRepair.tbShadow = False
        Me.btnStartRepair.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnStartRepair.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStartRepair.tbShowDot = False
        Me.btnStartRepair.tbShowMoreIconImg = CType(resources.GetObject("btnStartRepair.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStartRepair.tbShowNew = False
        Me.btnStartRepair.tbShowProgress = False
        Me.btnStartRepair.tbShowTip = True
        Me.btnStartRepair.tbShowToolTipOnButton = False
        Me.btnStartRepair.tbSplit = "10,10,10,10"
        Me.btnStartRepair.tbText = "开始体验"
        Me.btnStartRepair.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepair.tbTextColor = System.Drawing.Color.White
        Me.btnStartRepair.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStartRepair.tbTextColorDown = System.Drawing.Color.White
        Me.btnStartRepair.tbTextColorHover = System.Drawing.Color.White
        Me.btnStartRepair.tbTextMouseDownPlace = 0
        Me.btnStartRepair.tbToolTip = ""
        Me.btnStartRepair.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStartRepair.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStartRepair.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepair.VisibleEx = True
        '
        'pnlNoRecord
        '
        Me.pnlNoRecord.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlNoRecord.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlNoRecord.Controls.Add(Me.picReason2)
        Me.pnlNoRecord.Controls.Add(Me.picReason1)
        Me.pnlNoRecord.Controls.Add(Me.lblNoRecordReason2)
        Me.pnlNoRecord.Controls.Add(Me.lblNoRecordReason1)
        Me.pnlNoRecord.Controls.Add(Me.lblNoRecordReason)
        Me.pnlNoRecord.Controls.Add(Me.lblNoRecordTitle)
        Me.pnlNoRecord.Controls.Add(Me.picNoRecord)
        Me.pnlNoRecord.Controls.Add(Me.btnStartRepairEx)
        Me.pnlNoRecord.Location = New System.Drawing.Point(351, 12)
        Me.pnlNoRecord.Name = "pnlNoRecord"
        Me.pnlNoRecord.Size = New System.Drawing.Size(340, 582)
        Me.pnlNoRecord.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlNoRecord.TabIndex = 24
        Me.pnlNoRecord.tbBackgroundImage = Nothing
        Me.pnlNoRecord.tbShowWatermark = False
        Me.pnlNoRecord.tbSplit = "0,0,0,0"
        Me.pnlNoRecord.tbWatermark = Nothing
        Me.pnlNoRecord.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlNoRecord.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'picReason2
        '
        Me.picReason2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picReason2.Image = Global.iTong.My.Resources.Resources.repair_setp2
        Me.picReason2.Location = New System.Drawing.Point(125, 287)
        Me.picReason2.Name = "picReason2"
        Me.picReason2.Size = New System.Drawing.Size(22, 22)
        Me.picReason2.TabIndex = 48
        Me.picReason2.TabStop = False
        '
        'picReason1
        '
        Me.picReason1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picReason1.Image = Global.iTong.My.Resources.Resources.repair_setp1
        Me.picReason1.Location = New System.Drawing.Point(125, 237)
        Me.picReason1.Name = "picReason1"
        Me.picReason1.Size = New System.Drawing.Size(22, 22)
        Me.picReason1.TabIndex = 47
        Me.picReason1.TabStop = False
        '
        'lblNoRecordReason2
        '
        Me.lblNoRecordReason2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNoRecordReason2.AutoSize = True
        Me.lblNoRecordReason2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblNoRecordReason2.Location = New System.Drawing.Point(157, 287)
        Me.lblNoRecordReason2.Name = "lblNoRecordReason2"
        Me.lblNoRecordReason2.Size = New System.Drawing.Size(305, 24)
        Me.lblNoRecordReason2.TabIndex = 46
        Me.lblNoRecordReason2.tbAdriftWhenHover = False
        Me.lblNoRecordReason2.tbAutoEllipsis = False
        Me.lblNoRecordReason2.tbAutoSize = True
        Me.lblNoRecordReason2.tbHideImage = False
        Me.lblNoRecordReason2.tbIconImage = Nothing
        Me.lblNoRecordReason2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNoRecordReason2.tbIconPlaceText = 5
        Me.lblNoRecordReason2.tbShadow = False
        Me.lblNoRecordReason2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNoRecordReason2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNoRecordReason2.tbShowScrolling = False
        Me.lblNoRecordReason2.Text = "如果您的应用确实在同步平台上下载且闪退，请联系客户" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "加入客服QQ群号：XXXXX"
        '
        'lblNoRecordReason1
        '
        Me.lblNoRecordReason1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNoRecordReason1.AutoSize = True
        Me.lblNoRecordReason1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblNoRecordReason1.Location = New System.Drawing.Point(157, 237)
        Me.lblNoRecordReason1.Name = "lblNoRecordReason1"
        Me.lblNoRecordReason1.Size = New System.Drawing.Size(317, 24)
        Me.lblNoRecordReason1.TabIndex = 45
        Me.lblNoRecordReason1.tbAdriftWhenHover = False
        Me.lblNoRecordReason1.tbAutoEllipsis = False
        Me.lblNoRecordReason1.tbAutoSize = True
        Me.lblNoRecordReason1.tbHideImage = False
        Me.lblNoRecordReason1.tbIconImage = Nothing
        Me.lblNoRecordReason1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNoRecordReason1.tbIconPlaceText = 5
        Me.lblNoRecordReason1.tbShadow = False
        Me.lblNoRecordReason1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNoRecordReason1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNoRecordReason1.tbShowScrolling = False
        Me.lblNoRecordReason1.Text = "闪退的应用并不是同步平台（同步助手、同步推等）下载；" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "建议从同步助手的应用商店中重新下载安装。"
        '
        'lblNoRecordReason
        '
        Me.lblNoRecordReason.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNoRecordReason.AutoSize = True
        Me.lblNoRecordReason.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblNoRecordReason.Location = New System.Drawing.Point(119, 193)
        Me.lblNoRecordReason.Name = "lblNoRecordReason"
        Me.lblNoRecordReason.Size = New System.Drawing.Size(125, 12)
        Me.lblNoRecordReason.TabIndex = 44
        Me.lblNoRecordReason.tbAdriftWhenHover = False
        Me.lblNoRecordReason.tbAutoEllipsis = False
        Me.lblNoRecordReason.tbAutoSize = True
        Me.lblNoRecordReason.tbHideImage = False
        Me.lblNoRecordReason.tbIconImage = Nothing
        Me.lblNoRecordReason.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNoRecordReason.tbIconPlaceText = 5
        Me.lblNoRecordReason.tbShadow = False
        Me.lblNoRecordReason.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNoRecordReason.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNoRecordReason.tbShowScrolling = False
        Me.lblNoRecordReason.Text = "可能有以下几个原因："
        '
        'lblNoRecordTitle
        '
        Me.lblNoRecordTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNoRecordTitle.AutoSize = True
        Me.lblNoRecordTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblNoRecordTitle.Location = New System.Drawing.Point(119, 141)
        Me.lblNoRecordTitle.Name = "lblNoRecordTitle"
        Me.lblNoRecordTitle.Size = New System.Drawing.Size(113, 12)
        Me.lblNoRecordTitle.TabIndex = 43
        Me.lblNoRecordTitle.tbAdriftWhenHover = False
        Me.lblNoRecordTitle.tbAutoEllipsis = False
        Me.lblNoRecordTitle.tbAutoSize = True
        Me.lblNoRecordTitle.tbHideImage = False
        Me.lblNoRecordTitle.tbIconImage = Nothing
        Me.lblNoRecordTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNoRecordTitle.tbIconPlaceText = 5
        Me.lblNoRecordTitle.tbShadow = False
        Me.lblNoRecordTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNoRecordTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNoRecordTitle.tbShowScrolling = False
        Me.lblNoRecordTitle.Text = "未检测到闪退的应用"
        '
        'picNoRecord
        '
        Me.picNoRecord.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picNoRecord.Image = Global.iTong.My.Resources.Resources.repair_icon_norecord
        Me.picNoRecord.Location = New System.Drawing.Point(-81, 142)
        Me.picNoRecord.Name = "picNoRecord"
        Me.picNoRecord.Size = New System.Drawing.Size(169, 169)
        Me.picNoRecord.TabIndex = 42
        Me.picNoRecord.TabStop = False
        '
        'btnStartRepairEx
        '
        Me.btnStartRepairEx.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStartRepairEx.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnStartRepairEx.BindingForm = Nothing
        Me.btnStartRepairEx.Location = New System.Drawing.Point(59, 411)
        Me.btnStartRepairEx.Margin = New System.Windows.Forms.Padding(0)
        Me.btnStartRepairEx.Name = "btnStartRepairEx"
        Me.btnStartRepairEx.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStartRepairEx.Selectable = True
        Me.btnStartRepairEx.Size = New System.Drawing.Size(223, 47)
        Me.btnStartRepairEx.TabIndex = 41
        Me.btnStartRepairEx.tbAdriftIconWhenHover = False
        Me.btnStartRepairEx.tbAutoSize = False
        Me.btnStartRepairEx.tbAutoSizeEx = False
        Me.btnStartRepairEx.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnStartRepairEx.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStartRepairEx.tbBadgeNumber = 0
        Me.btnStartRepairEx.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStartRepairEx.tbEndEllipsis = False
        Me.btnStartRepairEx.tbIconHoldPlace = True
        Me.btnStartRepairEx.tbIconImage = Nothing
        Me.btnStartRepairEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepairEx.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStartRepairEx.tbIconMore = False
        Me.btnStartRepairEx.tbIconMouseDown = Nothing
        Me.btnStartRepairEx.tbIconMouseHover = Nothing
        Me.btnStartRepairEx.tbIconMouseLeave = Nothing
        Me.btnStartRepairEx.tbIconPlaceText = 0
        Me.btnStartRepairEx.tbIconReadOnly = Nothing
        Me.btnStartRepairEx.tbImageMouseDown = Nothing
        Me.btnStartRepairEx.tbImageMouseHover = Nothing
        Me.btnStartRepairEx.tbImageMouseLeave = Nothing
        Me.btnStartRepairEx.tbProgressValue = 50
        Me.btnStartRepairEx.tbReadOnly = False
        Me.btnStartRepairEx.tbReadOnlyText = False
        Me.btnStartRepairEx.tbShadow = False
        Me.btnStartRepairEx.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnStartRepairEx.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStartRepairEx.tbShowDot = False
        Me.btnStartRepairEx.tbShowMoreIconImg = CType(resources.GetObject("btnStartRepairEx.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStartRepairEx.tbShowNew = False
        Me.btnStartRepairEx.tbShowProgress = False
        Me.btnStartRepairEx.tbShowTip = True
        Me.btnStartRepairEx.tbShowToolTipOnButton = False
        Me.btnStartRepairEx.tbSplit = "10,10,10,10"
        Me.btnStartRepairEx.tbText = "重新检测"
        Me.btnStartRepairEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepairEx.tbTextColor = System.Drawing.Color.White
        Me.btnStartRepairEx.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStartRepairEx.tbTextColorDown = System.Drawing.Color.White
        Me.btnStartRepairEx.tbTextColorHover = System.Drawing.Color.White
        Me.btnStartRepairEx.tbTextMouseDownPlace = 0
        Me.btnStartRepairEx.tbToolTip = ""
        Me.btnStartRepairEx.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStartRepairEx.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStartRepairEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepairEx.VisibleEx = True
        '
        'pnlRecord
        '
        Me.pnlRecord.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlRecord.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlRecord.Controls.Add(Me.pnlContral)
        Me.pnlRecord.Controls.Add(Me.pnlGrid)
        Me.pnlRecord.Location = New System.Drawing.Point(699, 12)
        Me.pnlRecord.Name = "pnlRecord"
        Me.pnlRecord.Size = New System.Drawing.Size(340, 582)
        Me.pnlRecord.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlRecord.TabIndex = 25
        Me.pnlRecord.tbBackgroundImage = Nothing
        Me.pnlRecord.tbShowWatermark = False
        Me.pnlRecord.tbSplit = "0,0,0,0"
        Me.pnlRecord.tbWatermark = Nothing
        Me.pnlRecord.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlRecord.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlContral
        '
        Me.pnlContral.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContral.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlContral.Controls.Add(Me.lblMessage)
        Me.pnlContral.Controls.Add(Me.btnMyPayment)
        Me.pnlContral.Controls.Add(Me.btnGetTwoYuan)
        Me.pnlContral.Controls.Add(Me.btnRepair)
        Me.pnlContral.Controls.Add(Me.btnStartRepairEx1)
        Me.pnlContral.Location = New System.Drawing.Point(0, 516)
        Me.pnlContral.Name = "pnlContral"
        Me.pnlContral.Size = New System.Drawing.Size(340, 65)
        Me.pnlContral.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContral.TabIndex = 28
        Me.pnlContral.tbBackgroundImage = Global.iTong.My.Resources.Resources.file_bg_applistbottom
        Me.pnlContral.tbShowWatermark = False
        Me.pnlContral.tbSplit = "3,3,3,3"
        Me.pnlContral.tbWatermark = Nothing
        Me.pnlContral.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContral.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblMessage
        '
        Me.lblMessage.AutoEllipsis = True
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(14, 22)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(222, 20)
        Me.lblMessage.TabIndex = 75
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = True
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = True
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnMyPayment
        '
        Me.btnMyPayment.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnMyPayment.BackColor = System.Drawing.Color.Transparent
        Me.btnMyPayment.BindingForm = Nothing
        Me.btnMyPayment.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnMyPayment.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnMyPayment.Location = New System.Drawing.Point(257, 19)
        Me.btnMyPayment.Margin = New System.Windows.Forms.Padding(0)
        Me.btnMyPayment.Name = "btnMyPayment"
        Me.btnMyPayment.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnMyPayment.Selectable = True
        Me.btnMyPayment.Size = New System.Drawing.Size(75, 27)
        Me.btnMyPayment.TabIndex = 43
        Me.btnMyPayment.tbAdriftIconWhenHover = False
        Me.btnMyPayment.tbAutoSize = True
        Me.btnMyPayment.tbAutoSizeEx = True
        Me.btnMyPayment.tbBackgroundImage = Nothing
        Me.btnMyPayment.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnMyPayment.tbBadgeNumber = 0
        Me.btnMyPayment.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMyPayment.tbEndEllipsis = False
        Me.btnMyPayment.tbIconHoldPlace = True
        Me.btnMyPayment.tbIconImage = Nothing
        Me.btnMyPayment.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMyPayment.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnMyPayment.tbIconMore = False
        Me.btnMyPayment.tbIconMouseDown = Nothing
        Me.btnMyPayment.tbIconMouseHover = Nothing
        Me.btnMyPayment.tbIconMouseLeave = Nothing
        Me.btnMyPayment.tbIconPlaceText = 0
        Me.btnMyPayment.tbIconReadOnly = Nothing
        Me.btnMyPayment.tbImageMouseDown = Nothing
        Me.btnMyPayment.tbImageMouseHover = Nothing
        Me.btnMyPayment.tbImageMouseLeave = Nothing
        Me.btnMyPayment.tbProgressValue = 50
        Me.btnMyPayment.tbReadOnly = False
        Me.btnMyPayment.tbReadOnlyText = False
        Me.btnMyPayment.tbShadow = False
        Me.btnMyPayment.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnMyPayment.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnMyPayment.tbShowDot = False
        Me.btnMyPayment.tbShowMoreIconImg = CType(resources.GetObject("btnMyPayment.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnMyPayment.tbShowNew = False
        Me.btnMyPayment.tbShowProgress = False
        Me.btnMyPayment.tbShowTip = True
        Me.btnMyPayment.tbShowToolTipOnButton = False
        Me.btnMyPayment.tbSplit = "10,10,10,10"
        Me.btnMyPayment.tbText = "我的赔付金"
        Me.btnMyPayment.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMyPayment.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnMyPayment.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnMyPayment.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnMyPayment.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnMyPayment.tbTextMouseDownPlace = 0
        Me.btnMyPayment.tbToolTip = ""
        Me.btnMyPayment.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMyPayment.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMyPayment.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMyPayment.VisibleEx = True
        '
        'btnGetTwoYuan
        '
        Me.btnGetTwoYuan.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnGetTwoYuan.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnGetTwoYuan.BindingForm = Nothing
        Me.btnGetTwoYuan.Location = New System.Drawing.Point(153, 15)
        Me.btnGetTwoYuan.Margin = New System.Windows.Forms.Padding(0)
        Me.btnGetTwoYuan.Name = "btnGetTwoYuan"
        Me.btnGetTwoYuan.Padding = New System.Windows.Forms.Padding(20, 9, 20, 9)
        Me.btnGetTwoYuan.Selectable = True
        Me.btnGetTwoYuan.Size = New System.Drawing.Size(87, 27)
        Me.btnGetTwoYuan.TabIndex = 42
        Me.btnGetTwoYuan.tbAdriftIconWhenHover = False
        Me.btnGetTwoYuan.tbAutoSize = False
        Me.btnGetTwoYuan.tbAutoSizeEx = True
        Me.btnGetTwoYuan.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_orange
        Me.btnGetTwoYuan.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnGetTwoYuan.tbBadgeNumber = 0
        Me.btnGetTwoYuan.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGetTwoYuan.tbEndEllipsis = False
        Me.btnGetTwoYuan.tbIconHoldPlace = True
        Me.btnGetTwoYuan.tbIconImage = Nothing
        Me.btnGetTwoYuan.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetTwoYuan.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGetTwoYuan.tbIconMore = False
        Me.btnGetTwoYuan.tbIconMouseDown = Nothing
        Me.btnGetTwoYuan.tbIconMouseHover = Nothing
        Me.btnGetTwoYuan.tbIconMouseLeave = Nothing
        Me.btnGetTwoYuan.tbIconPlaceText = 0
        Me.btnGetTwoYuan.tbIconReadOnly = Nothing
        Me.btnGetTwoYuan.tbImageMouseDown = Nothing
        Me.btnGetTwoYuan.tbImageMouseHover = Nothing
        Me.btnGetTwoYuan.tbImageMouseLeave = Nothing
        Me.btnGetTwoYuan.tbProgressValue = 50
        Me.btnGetTwoYuan.tbReadOnly = False
        Me.btnGetTwoYuan.tbReadOnlyText = False
        Me.btnGetTwoYuan.tbShadow = False
        Me.btnGetTwoYuan.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnGetTwoYuan.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGetTwoYuan.tbShowDot = False
        Me.btnGetTwoYuan.tbShowMoreIconImg = CType(resources.GetObject("btnGetTwoYuan.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGetTwoYuan.tbShowNew = False
        Me.btnGetTwoYuan.tbShowProgress = False
        Me.btnGetTwoYuan.tbShowTip = True
        Me.btnGetTwoYuan.tbShowToolTipOnButton = False
        Me.btnGetTwoYuan.tbSplit = "10,10,10,10"
        Me.btnGetTwoYuan.tbText = "领取2元"
        Me.btnGetTwoYuan.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetTwoYuan.tbTextColor = System.Drawing.Color.White
        Me.btnGetTwoYuan.tbTextColorDisable = System.Drawing.Color.White
        Me.btnGetTwoYuan.tbTextColorDown = System.Drawing.Color.White
        Me.btnGetTwoYuan.tbTextColorHover = System.Drawing.Color.White
        Me.btnGetTwoYuan.tbTextMouseDownPlace = 0
        Me.btnGetTwoYuan.tbToolTip = ""
        Me.btnGetTwoYuan.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGetTwoYuan.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGetTwoYuan.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetTwoYuan.VisibleEx = True
        '
        'btnRepair
        '
        Me.btnRepair.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRepair.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnRepair.BindingForm = Nothing
        Me.btnRepair.Location = New System.Drawing.Point(65, 15)
        Me.btnRepair.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRepair.Name = "btnRepair"
        Me.btnRepair.Padding = New System.Windows.Forms.Padding(20, 9, 20, 10)
        Me.btnRepair.Selectable = True
        Me.btnRepair.Size = New System.Drawing.Size(93, 21)
        Me.btnRepair.TabIndex = 41
        Me.btnRepair.tbAdriftIconWhenHover = False
        Me.btnRepair.tbAutoSize = False
        Me.btnRepair.tbAutoSizeEx = True
        Me.btnRepair.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_welcome_connect
        Me.btnRepair.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRepair.tbBadgeNumber = 0
        Me.btnRepair.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRepair.tbEndEllipsis = False
        Me.btnRepair.tbIconHoldPlace = True
        Me.btnRepair.tbIconImage = Nothing
        Me.btnRepair.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRepair.tbIconMore = False
        Me.btnRepair.tbIconMouseDown = Nothing
        Me.btnRepair.tbIconMouseHover = Nothing
        Me.btnRepair.tbIconMouseLeave = Nothing
        Me.btnRepair.tbIconPlaceText = 0
        Me.btnRepair.tbIconReadOnly = Nothing
        Me.btnRepair.tbImageMouseDown = Nothing
        Me.btnRepair.tbImageMouseHover = Nothing
        Me.btnRepair.tbImageMouseLeave = Nothing
        Me.btnRepair.tbProgressValue = 50
        Me.btnRepair.tbReadOnly = False
        Me.btnRepair.tbReadOnlyText = False
        Me.btnRepair.tbShadow = False
        Me.btnRepair.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnRepair.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRepair.tbShowDot = False
        Me.btnRepair.tbShowMoreIconImg = CType(resources.GetObject("btnRepair.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRepair.tbShowNew = False
        Me.btnRepair.tbShowProgress = False
        Me.btnRepair.tbShowTip = True
        Me.btnRepair.tbShowToolTipOnButton = False
        Me.btnRepair.tbSplit = "10,10,10,10"
        Me.btnRepair.tbText = "全部修复"
        Me.btnRepair.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.tbTextColor = System.Drawing.Color.White
        Me.btnRepair.tbTextColorDisable = System.Drawing.Color.White
        Me.btnRepair.tbTextColorDown = System.Drawing.Color.White
        Me.btnRepair.tbTextColorHover = System.Drawing.Color.White
        Me.btnRepair.tbTextMouseDownPlace = 0
        Me.btnRepair.tbToolTip = ""
        Me.btnRepair.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRepair.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepair.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.VisibleEx = True
        '
        'btnStartRepairEx1
        '
        Me.btnStartRepairEx1.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnStartRepairEx1.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnStartRepairEx1.BindingForm = Nothing
        Me.btnStartRepairEx1.Location = New System.Drawing.Point(-20, 15)
        Me.btnStartRepairEx1.Margin = New System.Windows.Forms.Padding(0)
        Me.btnStartRepairEx1.Name = "btnStartRepairEx1"
        Me.btnStartRepairEx1.Padding = New System.Windows.Forms.Padding(20, 9, 20, 9)
        Me.btnStartRepairEx1.Selectable = True
        Me.btnStartRepairEx1.Size = New System.Drawing.Size(93, 27)
        Me.btnStartRepairEx1.TabIndex = 40
        Me.btnStartRepairEx1.tbAdriftIconWhenHover = False
        Me.btnStartRepairEx1.tbAutoSize = False
        Me.btnStartRepairEx1.tbAutoSizeEx = True
        Me.btnStartRepairEx1.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnStartRepairEx1.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStartRepairEx1.tbBadgeNumber = 0
        Me.btnStartRepairEx1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStartRepairEx1.tbEndEllipsis = False
        Me.btnStartRepairEx1.tbIconHoldPlace = True
        Me.btnStartRepairEx1.tbIconImage = Nothing
        Me.btnStartRepairEx1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepairEx1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStartRepairEx1.tbIconMore = False
        Me.btnStartRepairEx1.tbIconMouseDown = Nothing
        Me.btnStartRepairEx1.tbIconMouseHover = Nothing
        Me.btnStartRepairEx1.tbIconMouseLeave = Nothing
        Me.btnStartRepairEx1.tbIconPlaceText = 0
        Me.btnStartRepairEx1.tbIconReadOnly = Nothing
        Me.btnStartRepairEx1.tbImageMouseDown = Nothing
        Me.btnStartRepairEx1.tbImageMouseHover = Nothing
        Me.btnStartRepairEx1.tbImageMouseLeave = Nothing
        Me.btnStartRepairEx1.tbProgressValue = 50
        Me.btnStartRepairEx1.tbReadOnly = False
        Me.btnStartRepairEx1.tbReadOnlyText = False
        Me.btnStartRepairEx1.tbShadow = False
        Me.btnStartRepairEx1.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnStartRepairEx1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStartRepairEx1.tbShowDot = False
        Me.btnStartRepairEx1.tbShowMoreIconImg = CType(resources.GetObject("btnStartRepairEx1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStartRepairEx1.tbShowNew = False
        Me.btnStartRepairEx1.tbShowProgress = False
        Me.btnStartRepairEx1.tbShowTip = True
        Me.btnStartRepairEx1.tbShowToolTipOnButton = False
        Me.btnStartRepairEx1.tbSplit = "10,10,10,10"
        Me.btnStartRepairEx1.tbText = "重新检测"
        Me.btnStartRepairEx1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepairEx1.tbTextColor = System.Drawing.Color.White
        Me.btnStartRepairEx1.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStartRepairEx1.tbTextColorDown = System.Drawing.Color.White
        Me.btnStartRepairEx1.tbTextColorHover = System.Drawing.Color.White
        Me.btnStartRepairEx1.tbTextMouseDownPlace = 0
        Me.btnStartRepairEx1.tbToolTip = ""
        Me.btnStartRepairEx1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStartRepairEx1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStartRepairEx1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartRepairEx1.VisibleEx = True
        '
        'pnlGrid
        '
        Me.pnlGrid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlGrid.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlGrid.Location = New System.Drawing.Point(0, 0)
        Me.pnlGrid.Name = "pnlGrid"
        Me.pnlGrid.Size = New System.Drawing.Size(340, 516)
        Me.pnlGrid.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlGrid.TabIndex = 27
        Me.pnlGrid.tbBackgroundImage = Nothing
        Me.pnlGrid.tbShowWatermark = False
        Me.pnlGrid.tbSplit = "0,0,0,0"
        Me.pnlGrid.tbWatermark = Nothing
        Me.pnlGrid.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlGrid.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlTui
        '
        Me.pnlTui.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTui.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlTui.Controls.Add(Me.pbarShowProgress)
        Me.pnlTui.Controls.Add(Me.lblTuiProgress)
        Me.pnlTui.Controls.Add(Me.btnInstallTui)
        Me.pnlTui.Controls.Add(Me.picInstallTui2)
        Me.pnlTui.Controls.Add(Me.lblInstallTui1)
        Me.pnlTui.Controls.Add(Me.picInstallTui)
        Me.pnlTui.Location = New System.Drawing.Point(1049, 12)
        Me.pnlTui.Name = "pnlTui"
        Me.pnlTui.Size = New System.Drawing.Size(340, 582)
        Me.pnlTui.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTui.TabIndex = 26
        Me.pnlTui.tbBackgroundImage = Nothing
        Me.pnlTui.tbShowWatermark = False
        Me.pnlTui.tbSplit = "0,0,0,0"
        Me.pnlTui.tbWatermark = Nothing
        Me.pnlTui.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTui.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pbarShowProgress
        '
        Me.pbarShowProgress.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbarShowProgress.BackColor = System.Drawing.Color.Transparent
        Me.pbarShowProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.pbarShowProgress.Location = New System.Drawing.Point(115, 446)
        Me.pbarShowProgress.Name = "pbarShowProgress"
        Me.pbarShowProgress.Size = New System.Drawing.Size(110, 10)
        Me.pbarShowProgress.TabIndex = 50
        Me.pbarShowProgress.tbBackgroundImage = CType(resources.GetObject("pbarShowProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbCanDragValue = False
        Me.pbarShowProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pbarShowProgress.tbDotImage = CType(resources.GetObject("pbarShowProgress.tbDotImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pbarShowProgress.tbDotMouseDown = Nothing
        Me.pbarShowProgress.tbDotMouseHover = Nothing
        Me.pbarShowProgress.tbDotMouseLeave = Nothing
        Me.pbarShowProgress.tbDownloadImage = Global.iTong.My.Resources.Resources.app_progress_background
        Me.pbarShowProgress.tbDownMax = 100
        Me.pbarShowProgress.tbDownMin = 0
        Me.pbarShowProgress.tbDownValue = 0
        Me.pbarShowProgress.tbIsWaiting = False
        Me.pbarShowProgress.tbPlayImage = Global.iTong.My.Resources.Resources.app_progress_value
        Me.pbarShowProgress.tbPlayMax = 100
        Me.pbarShowProgress.tbPlayMin = 0
        Me.pbarShowProgress.tbPlayValue = 50
        Me.pbarShowProgress.tbShowDot = False
        Me.pbarShowProgress.tbShowText = True
        Me.pbarShowProgress.tbSplit = "4,0,4,0"
        Me.pbarShowProgress.Text = "TbControlBar1"
        Me.pbarShowProgress.Visible = False
        '
        'lblTuiProgress
        '
        Me.lblTuiProgress.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTuiProgress.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblTuiProgress.Location = New System.Drawing.Point(38, 416)
        Me.lblTuiProgress.Name = "lblTuiProgress"
        Me.lblTuiProgress.Size = New System.Drawing.Size(263, 23)
        Me.lblTuiProgress.TabIndex = 49
        Me.lblTuiProgress.tbAdriftWhenHover = False
        Me.lblTuiProgress.tbAutoEllipsis = False
        Me.lblTuiProgress.tbAutoSize = False
        Me.lblTuiProgress.tbHideImage = False
        Me.lblTuiProgress.tbIconImage = Nothing
        Me.lblTuiProgress.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTuiProgress.tbIconPlaceText = 5
        Me.lblTuiProgress.tbShadow = False
        Me.lblTuiProgress.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTuiProgress.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTuiProgress.tbShowScrolling = False
        Me.lblTuiProgress.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnInstallTui
        '
        Me.btnInstallTui.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnInstallTui.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.btnInstallTui.BindingForm = Nothing
        Me.btnInstallTui.Location = New System.Drawing.Point(59, 353)
        Me.btnInstallTui.Margin = New System.Windows.Forms.Padding(0)
        Me.btnInstallTui.Name = "btnInstallTui"
        Me.btnInstallTui.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnInstallTui.Selectable = True
        Me.btnInstallTui.Size = New System.Drawing.Size(223, 47)
        Me.btnInstallTui.TabIndex = 48
        Me.btnInstallTui.tbAdriftIconWhenHover = False
        Me.btnInstallTui.tbAutoSize = False
        Me.btnInstallTui.tbAutoSizeEx = False
        Me.btnInstallTui.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnInstallTui.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstallTui.tbBadgeNumber = 0
        Me.btnInstallTui.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstallTui.tbEndEllipsis = False
        Me.btnInstallTui.tbIconHoldPlace = True
        Me.btnInstallTui.tbIconImage = Nothing
        Me.btnInstallTui.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstallTui.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInstallTui.tbIconMore = False
        Me.btnInstallTui.tbIconMouseDown = Nothing
        Me.btnInstallTui.tbIconMouseHover = Nothing
        Me.btnInstallTui.tbIconMouseLeave = Nothing
        Me.btnInstallTui.tbIconPlaceText = 0
        Me.btnInstallTui.tbIconReadOnly = Nothing
        Me.btnInstallTui.tbImageMouseDown = Nothing
        Me.btnInstallTui.tbImageMouseHover = Nothing
        Me.btnInstallTui.tbImageMouseLeave = Nothing
        Me.btnInstallTui.tbProgressValue = 50
        Me.btnInstallTui.tbReadOnly = False
        Me.btnInstallTui.tbReadOnlyText = False
        Me.btnInstallTui.tbShadow = False
        Me.btnInstallTui.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnInstallTui.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstallTui.tbShowDot = False
        Me.btnInstallTui.tbShowMoreIconImg = CType(resources.GetObject("btnInstallTui.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstallTui.tbShowNew = False
        Me.btnInstallTui.tbShowProgress = False
        Me.btnInstallTui.tbShowTip = True
        Me.btnInstallTui.tbShowToolTipOnButton = False
        Me.btnInstallTui.tbSplit = "10,10,10,10"
        Me.btnInstallTui.tbText = "立即安装"
        Me.btnInstallTui.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstallTui.tbTextColor = System.Drawing.Color.White
        Me.btnInstallTui.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInstallTui.tbTextColorDown = System.Drawing.Color.White
        Me.btnInstallTui.tbTextColorHover = System.Drawing.Color.White
        Me.btnInstallTui.tbTextMouseDownPlace = 0
        Me.btnInstallTui.tbToolTip = ""
        Me.btnInstallTui.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstallTui.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstallTui.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstallTui.VisibleEx = True
        '
        'picInstallTui2
        '
        Me.picInstallTui2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picInstallTui2.AutoSize = True
        Me.picInstallTui2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.picInstallTui2.Location = New System.Drawing.Point(132, 245)
        Me.picInstallTui2.Name = "picInstallTui2"
        Me.picInstallTui2.Size = New System.Drawing.Size(185, 12)
        Me.picInstallTui2.TabIndex = 46
        Me.picInstallTui2.tbAdriftWhenHover = False
        Me.picInstallTui2.tbAutoEllipsis = False
        Me.picInstallTui2.tbAutoSize = True
        Me.picInstallTui2.tbHideImage = False
        Me.picInstallTui2.tbIconImage = Nothing
        Me.picInstallTui2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.picInstallTui2.tbIconPlaceText = 5
        Me.picInstallTui2.tbShadow = False
        Me.picInstallTui2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.picInstallTui2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.picInstallTui2.tbShowScrolling = False
        Me.picInstallTui2.Text = "安装后即可参与你闪退我赔付活动"
        '
        'lblInstallTui1
        '
        Me.lblInstallTui1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblInstallTui1.AutoSize = True
        Me.lblInstallTui1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblInstallTui1.Location = New System.Drawing.Point(132, 206)
        Me.lblInstallTui1.Name = "lblInstallTui1"
        Me.lblInstallTui1.Size = New System.Drawing.Size(101, 12)
        Me.lblInstallTui1.TabIndex = 45
        Me.lblInstallTui1.tbAdriftWhenHover = False
        Me.lblInstallTui1.tbAutoEllipsis = False
        Me.lblInstallTui1.tbAutoSize = True
        Me.lblInstallTui1.tbHideImage = False
        Me.lblInstallTui1.tbIconImage = Nothing
        Me.lblInstallTui1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblInstallTui1.tbIconPlaceText = 5
        Me.lblInstallTui1.tbShadow = False
        Me.lblInstallTui1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblInstallTui1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblInstallTui1.tbShowScrolling = False
        Me.lblInstallTui1.Text = "您尚未安装同步推"
        '
        'picInstallTui
        '
        Me.picInstallTui.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picInstallTui.Image = Global.iTong.My.Resources.Resources.repair_icon_tui
        Me.picInstallTui.Location = New System.Drawing.Point(-4, 184)
        Me.picInstallTui.Name = "picInstallTui"
        Me.picInstallTui.Size = New System.Drawing.Size(126, 119)
        Me.picInstallTui.TabIndex = 43
        Me.picInstallTui.TabStop = False
        '
        'picLoading
        '
        Me.picLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.picLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_repair
        Me.picLoading.Location = New System.Drawing.Point(318, 211)
        Me.picLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(1008, 284)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picLoading.TabIndex = 13
        Me.picLoading.TabStop = False
        '
        'lblLoadingEx
        '
        Me.lblLoadingEx.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoadingEx.Location = New System.Drawing.Point(519, 129)
        Me.lblLoadingEx.Name = "lblLoadingEx"
        Me.lblLoadingEx.Size = New System.Drawing.Size(607, 45)
        Me.lblLoadingEx.TabIndex = 14
        Me.lblLoadingEx.tbAdriftWhenHover = False
        Me.lblLoadingEx.tbAutoEllipsis = False
        Me.lblLoadingEx.tbAutoSize = False
        Me.lblLoadingEx.tbHideImage = False
        Me.lblLoadingEx.tbIconImage = Nothing
        Me.lblLoadingEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoadingEx.tbIconPlaceText = 5
        Me.lblLoadingEx.tbShadow = False
        Me.lblLoadingEx.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLoadingEx.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLoadingEx.tbShowScrolling = False
        Me.lblLoadingEx.Text = "正在检测设备中闪退的应用"
        Me.lblLoadingEx.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlRecordRepair
        '
        Me.pnlRecordRepair.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlRecordRepair.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlRecordRepair.Controls.Add(Me.pnlEmpty)
        Me.pnlRecordRepair.Controls.Add(Me.pnlBottom)
        Me.pnlRecordRepair.Controls.Add(Me.txtSearch)
        Me.pnlRecordRepair.Controls.Add(Me.btnSearch)
        Me.pnlRecordRepair.Controls.Add(Me.txtNameValue)
        Me.pnlRecordRepair.Controls.Add(Me.pnlGridRepair)
        Me.pnlRecordRepair.Location = New System.Drawing.Point(1400, 12)
        Me.pnlRecordRepair.Name = "pnlRecordRepair"
        Me.pnlRecordRepair.Size = New System.Drawing.Size(355, 582)
        Me.pnlRecordRepair.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlRecordRepair.TabIndex = 27
        Me.pnlRecordRepair.tbBackgroundImage = Nothing
        Me.pnlRecordRepair.tbShowWatermark = False
        Me.pnlRecordRepair.tbSplit = "0,0,0,0"
        Me.pnlRecordRepair.tbWatermark = Nothing
        Me.pnlRecordRepair.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlRecordRepair.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlEmpty
        '
        Me.pnlEmpty.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlEmpty.Controls.Add(Me.split)
        Me.pnlEmpty.Controls.Add(Me.picFileEmpty)
        Me.pnlEmpty.Controls.Add(Me.lblFileEmpty)
        Me.pnlEmpty.Location = New System.Drawing.Point(0, 99)
        Me.pnlEmpty.Name = "pnlEmpty"
        Me.pnlEmpty.Size = New System.Drawing.Size(355, 106)
        Me.pnlEmpty.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlEmpty.TabIndex = 12
        Me.pnlEmpty.tbBackgroundImage = Nothing
        Me.pnlEmpty.tbShowWatermark = False
        Me.pnlEmpty.tbSplit = "0,0,0,0"
        Me.pnlEmpty.tbWatermark = Nothing
        Me.pnlEmpty.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlEmpty.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'split
        '
        Me.split.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.split.BackColor = System.Drawing.Color.Transparent
        Me.split.BindingForm = Nothing
        Me.split.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.split.Location = New System.Drawing.Point(12, 15)
        Me.split.Name = "split"
        Me.split.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.split.Selectable = True
        Me.split.Size = New System.Drawing.Size(331, 1)
        Me.split.TabIndex = 44
        Me.split.tbAdriftIconWhenHover = False
        Me.split.tbAutoSize = False
        Me.split.tbAutoSizeEx = False
        Me.split.tbBackgroundImage = Global.iTong.My.Resources.Resources.welcome_btn_split
        Me.split.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.split.tbBadgeNumber = 0
        Me.split.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.split.tbEndEllipsis = False
        Me.split.tbIconHoldPlace = True
        Me.split.tbIconImage = Nothing
        Me.split.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.split.tbIconImageState = iTong.Components.ImageState.OneState
        Me.split.tbIconMore = False
        Me.split.tbIconMouseDown = Nothing
        Me.split.tbIconMouseHover = Nothing
        Me.split.tbIconMouseLeave = Nothing
        Me.split.tbIconPlaceText = 2
        Me.split.tbIconReadOnly = Nothing
        Me.split.tbImageMouseDown = Nothing
        Me.split.tbImageMouseHover = Nothing
        Me.split.tbImageMouseLeave = Nothing
        Me.split.tbProgressValue = 50
        Me.split.tbReadOnly = False
        Me.split.tbReadOnlyText = False
        Me.split.tbShadow = False
        Me.split.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.split.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.split.tbShowDot = False
        Me.split.tbShowMoreIconImg = CType(resources.GetObject("split.tbShowMoreIconImg"), System.Drawing.Image)
        Me.split.tbShowNew = False
        Me.split.tbShowProgress = False
        Me.split.tbShowTip = True
        Me.split.tbShowToolTipOnButton = False
        Me.split.tbSplit = "35,0,35,0"
        Me.split.tbText = ""
        Me.split.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.split.tbTextColor = System.Drawing.Color.White
        Me.split.tbTextColorDisable = System.Drawing.Color.White
        Me.split.tbTextColorDown = System.Drawing.Color.White
        Me.split.tbTextColorHover = System.Drawing.Color.White
        Me.split.tbTextMouseDownPlace = 0
        Me.split.tbToolTip = ""
        Me.split.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.split.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.split.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.split.VisibleEx = True
        '
        'picFileEmpty
        '
        Me.picFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picFileEmpty.Image = Global.iTong.My.Resources.Resources.file_fileempty
        Me.picFileEmpty.Location = New System.Drawing.Point(76, 12)
        Me.picFileEmpty.Name = "picFileEmpty"
        Me.picFileEmpty.Size = New System.Drawing.Size(75, 75)
        Me.picFileEmpty.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picFileEmpty.TabIndex = 3
        Me.picFileEmpty.TabStop = False
        '
        'lblFileEmpty
        '
        Me.lblFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFileEmpty.AutoSize = True
        Me.lblFileEmpty.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFileEmpty.ForeColor = System.Drawing.Color.FromArgb(CType(CType(159, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(163, Byte), Integer))
        Me.lblFileEmpty.Location = New System.Drawing.Point(168, 47)
        Me.lblFileEmpty.Name = "lblFileEmpty"
        Me.lblFileEmpty.Size = New System.Drawing.Size(104, 16)
        Me.lblFileEmpty.TabIndex = 2
        Me.lblFileEmpty.tbAdriftWhenHover = False
        Me.lblFileEmpty.tbAutoEllipsis = False
        Me.lblFileEmpty.tbAutoSize = True
        Me.lblFileEmpty.tbHideImage = False
        Me.lblFileEmpty.tbIconImage = Nothing
        Me.lblFileEmpty.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileEmpty.tbIconPlaceText = 5
        Me.lblFileEmpty.tbShadow = False
        Me.lblFileEmpty.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileEmpty.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileEmpty.tbShowScrolling = False
        Me.lblFileEmpty.Text = "这里没有内容"
        '
        'pnlBottom
        '
        Me.pnlBottom.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlBottom.BackColor = System.Drawing.Color.FromArgb(CType(CType(204, Byte), Integer), CType(CType(216, Byte), Integer), CType(CType(237, Byte), Integer))
        Me.pnlBottom.Controls.Add(Me.lblTipJaibreak)
        Me.pnlBottom.Controls.Add(Me.lblJaiBreak)
        Me.pnlBottom.Controls.Add(Me.btnAllRepair)
        Me.pnlBottom.Location = New System.Drawing.Point(0, 546)
        Me.pnlBottom.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(355, 36)
        Me.pnlBottom.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBottom.TabIndex = 31
        Me.pnlBottom.tbBackgroundImage = Nothing
        Me.pnlBottom.tbShowWatermark = False
        Me.pnlBottom.tbSplit = "0,0,0,0"
        Me.pnlBottom.tbWatermark = Nothing
        Me.pnlBottom.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBottom.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblTipJaibreak
        '
        Me.lblTipJaibreak.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.lblTipJaibreak.AutoSize = True
        Me.lblTipJaibreak.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblTipJaibreak.Location = New System.Drawing.Point(10, 12)
        Me.lblTipJaibreak.Name = "lblTipJaibreak"
        Me.lblTipJaibreak.Size = New System.Drawing.Size(131, 12)
        Me.lblTipJaibreak.TabIndex = 22
        Me.lblTipJaibreak.tbAdriftWhenHover = False
        Me.lblTipJaibreak.tbAutoEllipsis = False
        Me.lblTipJaibreak.tbAutoSize = True
        Me.lblTipJaibreak.tbHideImage = False
        Me.lblTipJaibreak.tbIconImage = Nothing
        Me.lblTipJaibreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTipJaibreak.tbIconPlaceText = 5
        Me.lblTipJaibreak.tbShadow = False
        Me.lblTipJaibreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTipJaibreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTipJaibreak.tbShowScrolling = False
        Me.lblTipJaibreak.Text = "越狱后可完全杜绝闪退,"
        '
        'lblJaiBreak
        '
        Me.lblJaiBreak.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.lblJaiBreak.AutoSize = True
        Me.lblJaiBreak.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblJaiBreak.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblJaiBreak.Location = New System.Drawing.Point(142, 12)
        Me.lblJaiBreak.Name = "lblJaiBreak"
        Me.lblJaiBreak.Size = New System.Drawing.Size(59, 12)
        Me.lblJaiBreak.TabIndex = 22
        Me.lblJaiBreak.tbAdriftWhenHover = False
        Me.lblJaiBreak.tbAutoEllipsis = False
        Me.lblJaiBreak.tbAutoSize = True
        Me.lblJaiBreak.tbHideImage = False
        Me.lblJaiBreak.tbIconImage = Nothing
        Me.lblJaiBreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblJaiBreak.tbIconPlaceText = 5
        Me.lblJaiBreak.tbShadow = False
        Me.lblJaiBreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblJaiBreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblJaiBreak.tbShowScrolling = False
        Me.lblJaiBreak.Text = "如何越狱?"
        '
        'btnAllRepair
        '
        Me.btnAllRepair.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnAllRepair.BackColor = System.Drawing.Color.Transparent
        Me.btnAllRepair.BindingForm = Nothing
        Me.btnAllRepair.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAllRepair.Location = New System.Drawing.Point(268, 7)
        Me.btnAllRepair.Name = "btnAllRepair"
        Me.btnAllRepair.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnAllRepair.Selectable = True
        Me.btnAllRepair.Size = New System.Drawing.Size(73, 23)
        Me.btnAllRepair.TabIndex = 21
        Me.btnAllRepair.tbAdriftIconWhenHover = False
        Me.btnAllRepair.tbAutoSize = False
        Me.btnAllRepair.tbAutoSizeEx = True
        Me.btnAllRepair.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnAllRepair.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAllRepair.tbBadgeNumber = 0
        Me.btnAllRepair.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAllRepair.tbEndEllipsis = False
        Me.btnAllRepair.tbIconHoldPlace = True
        Me.btnAllRepair.tbIconImage = Nothing
        Me.btnAllRepair.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAllRepair.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAllRepair.tbIconMore = False
        Me.btnAllRepair.tbIconMouseDown = Nothing
        Me.btnAllRepair.tbIconMouseHover = Nothing
        Me.btnAllRepair.tbIconMouseLeave = Nothing
        Me.btnAllRepair.tbIconPlaceText = 2
        Me.btnAllRepair.tbIconReadOnly = Nothing
        Me.btnAllRepair.tbImageMouseDown = Nothing
        Me.btnAllRepair.tbImageMouseHover = Nothing
        Me.btnAllRepair.tbImageMouseLeave = Nothing
        Me.btnAllRepair.tbProgressValue = 50
        Me.btnAllRepair.tbReadOnly = False
        Me.btnAllRepair.tbReadOnlyText = False
        Me.btnAllRepair.tbShadow = False
        Me.btnAllRepair.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAllRepair.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAllRepair.tbShowDot = False
        Me.btnAllRepair.tbShowMoreIconImg = CType(resources.GetObject("btnAllRepair.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAllRepair.tbShowNew = False
        Me.btnAllRepair.tbShowProgress = False
        Me.btnAllRepair.tbShowTip = True
        Me.btnAllRepair.tbShowToolTipOnButton = False
        Me.btnAllRepair.tbSplit = "13,11,13,11"
        Me.btnAllRepair.tbText = "一键修复"
        Me.btnAllRepair.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAllRepair.tbTextColor = System.Drawing.Color.White
        Me.btnAllRepair.tbTextColorDisable = System.Drawing.Color.White
        Me.btnAllRepair.tbTextColorDown = System.Drawing.Color.White
        Me.btnAllRepair.tbTextColorHover = System.Drawing.Color.White
        Me.btnAllRepair.tbTextMouseDownPlace = 0
        Me.btnAllRepair.tbToolTip = ""
        Me.btnAllRepair.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAllRepair.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAllRepair.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAllRepair.VisibleEx = True
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.Location = New System.Drawing.Point(77, 17)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "Search"
        Me.txtSearch.ShowClear = True
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(65, 23)
        Me.txtSearch.TabIndex = 30
        Me.txtSearch.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.5R
        Me.txtSearch.Visible = False
        '
        'btnSearch
        '
        Me.btnSearch.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnSearch.BackColor = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSearch.BindingForm = Nothing
        Me.btnSearch.Location = New System.Drawing.Point(347, 41)
        Me.btnSearch.Margin = New System.Windows.Forms.Padding(0)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Padding = New System.Windows.Forms.Padding(18, 0, 0, 0)
        Me.btnSearch.Selectable = True
        Me.btnSearch.Size = New System.Drawing.Size(51, 38)
        Me.btnSearch.TabIndex = 29
        Me.btnSearch.tbAdriftIconWhenHover = False
        Me.btnSearch.tbAutoSize = False
        Me.btnSearch.tbAutoSizeEx = True
        Me.btnSearch.tbBackgroundImage = Nothing
        Me.btnSearch.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSearch.tbBadgeNumber = 0
        Me.btnSearch.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSearch.tbEndEllipsis = False
        Me.btnSearch.tbIconHoldPlace = True
        Me.btnSearch.tbIconImage = Global.iTong.My.Resources.Resources.icon_search
        Me.btnSearch.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSearch.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSearch.tbIconMore = False
        Me.btnSearch.tbIconMouseDown = Nothing
        Me.btnSearch.tbIconMouseHover = Nothing
        Me.btnSearch.tbIconMouseLeave = Nothing
        Me.btnSearch.tbIconPlaceText = 0
        Me.btnSearch.tbIconReadOnly = Nothing
        Me.btnSearch.tbImageMouseDown = Nothing
        Me.btnSearch.tbImageMouseHover = Nothing
        Me.btnSearch.tbImageMouseLeave = Nothing
        Me.btnSearch.tbProgressValue = 50
        Me.btnSearch.tbReadOnly = False
        Me.btnSearch.tbReadOnlyText = False
        Me.btnSearch.tbShadow = False
        Me.btnSearch.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSearch.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSearch.tbShowDot = False
        Me.btnSearch.tbShowMoreIconImg = CType(resources.GetObject("btnSearch.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSearch.tbShowNew = False
        Me.btnSearch.tbShowProgress = False
        Me.btnSearch.tbShowTip = True
        Me.btnSearch.tbShowToolTipOnButton = False
        Me.btnSearch.tbSplit = "3,3,3,3"
        Me.btnSearch.tbText = ""
        Me.btnSearch.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearch.tbTextColor = System.Drawing.Color.White
        Me.btnSearch.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSearch.tbTextColorDown = System.Drawing.Color.White
        Me.btnSearch.tbTextColorHover = System.Drawing.Color.White
        Me.btnSearch.tbTextMouseDownPlace = 0
        Me.btnSearch.tbToolTip = ""
        Me.btnSearch.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSearch.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSearch.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearch.VisibleEx = True
        '
        'txtNameValue
        '
        Me.txtNameValue.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.txtNameValue.BackColor = System.Drawing.Color.White
        Me.txtNameValue.BorderColor = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtNameValue.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtNameValue.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtNameValue.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtNameValue.ForeColor = System.Drawing.Color.Black
        Me.txtNameValue.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNameValue.Location = New System.Drawing.Point(-43, 41)
        Me.txtNameValue.Margin = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtNameValue.MaxLength = 50
        Me.txtNameValue.Name = "txtNameValue"
        Me.txtNameValue.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtNameValue.Size = New System.Drawing.Size(390, 37)
        Me.txtNameValue.TabIndex = 28
        Me.txtNameValue.Tag = Nothing
        Me.txtNameValue.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtNameValue.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtNameValue.tbSelMark = True
        Me.txtNameValue.tbTextBind = ""
        Me.txtNameValue.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtNameValue.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNameValue.TextPadding = New System.Windows.Forms.Padding(10, 3, 3, 3)
        Me.txtNameValue.TextTip = ""
        '
        'pnlGridRepair
        '
        Me.pnlGridRepair.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlGridRepair.BackColor = System.Drawing.Color.FromArgb(CType(CType(214, Byte), Integer), CType(CType(216, Byte), Integer), CType(CType(223, Byte), Integer))
        Me.pnlGridRepair.Location = New System.Drawing.Point(0, 114)
        Me.pnlGridRepair.Name = "pnlGridRepair"
        Me.pnlGridRepair.Size = New System.Drawing.Size(355, 432)
        Me.pnlGridRepair.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlGridRepair.TabIndex = 27
        Me.pnlGridRepair.tbBackgroundImage = Nothing
        Me.pnlGridRepair.tbShowWatermark = False
        Me.pnlGridRepair.tbSplit = "0,0,0,0"
        Me.pnlGridRepair.tbWatermark = Nothing
        Me.pnlGridRepair.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlGridRepair.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'frmRepairPayment
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(1767, 688)
        Me.Controls.Add(Me.pnlRecordRepair)
        Me.Controls.Add(Me.pnlDescription)
        Me.Controls.Add(Me.pnlNoRecord)
        Me.Controls.Add(Me.pnlRecord)
        Me.Controls.Add(Me.pnlTui)
        Me.Name = "frmRepairPayment"
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmRepairPayment"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlTui, 0)
        Me.Controls.SetChildIndex(Me.pnlRecord, 0)
        Me.Controls.SetChildIndex(Me.pnlNoRecord, 0)
        Me.Controls.SetChildIndex(Me.pnlDescription, 0)
        Me.Controls.SetChildIndex(Me.pnlRecordRepair, 0)
        Me.pnlDescription.ResumeLayout(False)
        Me.pnlNoRecord.ResumeLayout(False)
        CType(Me.picReason2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picReason1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picNoRecord, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlRecord.ResumeLayout(False)
        Me.pnlContral.ResumeLayout(False)
        Me.pnlTui.ResumeLayout(False)
        CType(Me.picInstallTui, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlRecordRepair.ResumeLayout(False)
        Me.pnlEmpty.ResumeLayout(False)
        Me.pnlEmpty.PerformLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlBottom.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlDescription As tbPanel
    Friend WithEvents pnlNoRecord As tbPanel
    Friend WithEvents pnlRecord As tbPanel
    Friend WithEvents pnlTui As tbPanel
    Friend WithEvents btnStartRepair As tbButton
    Friend WithEvents btnStartRepairEx As tbButton
    Friend WithEvents txtDescripton As tbRichTextBox
    Friend WithEvents btnDescription As tbButton
    Friend WithEvents picNoRecord As System.Windows.Forms.PictureBox
    Friend WithEvents lblNoRecordReason2 As tbLabel
    Friend WithEvents lblNoRecordReason1 As tbLabel
    Friend WithEvents lblNoRecordReason As tbLabel
    Friend WithEvents lblNoRecordTitle As tbLabel
    Friend WithEvents picReason2 As System.Windows.Forms.PictureBox
    Friend WithEvents picReason1 As System.Windows.Forms.PictureBox
    Friend WithEvents pnlGrid As tbPanel
    Friend WithEvents pnlContral As tbPanel
    Friend WithEvents btnStartRepairEx1 As tbButton
    Friend WithEvents btnGetTwoYuan As tbButton
    Friend WithEvents btnRepair As tbButton
    Friend WithEvents btnMyPayment As tbButton
    Friend WithEvents picInstallTui2 As tbLabel
    Friend WithEvents lblInstallTui1 As tbLabel
    Friend WithEvents picInstallTui As System.Windows.Forms.PictureBox
    Friend WithEvents btnInstallTui As tbButton
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblLoadingEx As tbLabel
    Friend WithEvents lblTuiProgress As tbLabel
    Friend WithEvents pbarShowProgress As tbControlBar
    Friend WithEvents lblMessage As tbLabel
    Friend WithEvents pnlRecordRepair As iTong.Components.tbPanel
    Friend WithEvents pnlGridRepair As iTong.Components.tbPanel
    Friend WithEvents btnSearch As iTong.Components.tbButton
    Friend WithEvents txtNameValue As iTong.Components.tbTextBox
    Friend WithEvents txtSearch As iTong.Components.tbSearch
    Friend WithEvents pnlBottom As iTong.Components.tbPanel
    Friend WithEvents btnAllRepair As iTong.Components.tbButton
    Friend WithEvents pnlEmpty As iTong.Components.tbPanel
    Friend WithEvents split As iTong.Components.tbButton
    Friend WithEvents picFileEmpty As System.Windows.Forms.PictureBox
    Friend WithEvents lblFileEmpty As iTong.Components.tbLabel
    Friend WithEvents lblTipJaibreak As iTong.Components.tbLabel
    Friend WithEvents lblJaiBreak As iTong.Components.tbLabel
End Class
