﻿Public Class ClearRubbishFileFoundControl
    Inherits System.Windows.Forms.Control

    Private mFileCount As Integer = 0
    Private mFileSize As Long = 0

    Private mFontText As Font = Nothing
    Private mFontNumber As Font = Nothing

    Private mColorText As Color = Nothing
    Private mColorNumber As Color = Nothing

    Private mBrashText As Brush = Nothing
    Private mBrushNumber As Brush = Nothing

    Private mStringHead As String = "已发现"
    Private mStringMiddle As String = "个垃圾，共占用"

    Public Property FileSize() As Long
        Get
            Return Me.mFileSize
        End Get
        Set(ByVal value As Long)
            Me.mFileSize = value
            Me.Invalidate()
        End Set
    End Property

    Public Property FileCount() As Integer
        Get
            Return Me.mFileCount
        End Get
        Set(ByVal value As Integer)
            Me.mFileCount = value
            Me.Invalidate()
        End Set
    End Property

    'Public Property FontText() As Font
    '    Get
    '        Return Me.mFontText
    '    End Get
    '    Set(ByVal value As Font)
    '        Me.mFontText = value
    '        Me.Invalidate()
    '    End Set
    'End Property

    'Public Property FontNumber() As Font
    '    Get
    '        Return Me.mFontNumber
    '    End Get
    '    Set(ByVal value As Font)
    '        Me.mFontNumber = value
    '        Me.Invalidate()
    '    End Set
    'End Property

    Public Property ColorText() As Color
        Get
            Return Me.mColorText
        End Get
        Set(ByVal value As Color)
            Me.mColorText = value
            If Me.mBrashText IsNot Nothing Then
                Me.mBrashText.Dispose()
            End If
            Me.mBrashText = New SolidBrush(value)
            Me.Invalidate()
        End Set
    End Property

    Public Property ColorNumber() As Color
        Get
            Return Me.mColorNumber
        End Get
        Set(ByVal value As Color)
            Me.mColorNumber = value
            If Me.mBrushNumber IsNot Nothing Then
                Me.mBrushNumber.Dispose()
            End If
            Me.mBrushNumber = New SolidBrush(value)
            Me.Invalidate()
        End Set
    End Property

    Public Property StringHead() As String
        Get
            Return Me.mStringHead
        End Get
        Set(ByVal value As String)
            Me.mStringHead = value
            Me.Invalidate()
        End Set
    End Property

    Public Property StringMiddle() As String
        Get
            Return Me.mStringMiddle
        End Get
        Set(ByVal value As String)
            Me.mStringMiddle = value
            Me.Invalidate()
        End Set
    End Property

    Public Sub New()

        Try
            Me.ColorText = Color.Black
            Me.ColorNumber = Color.FromArgb(214, 109, 77)
        Catch ex As Exception

        End Try

        Me.SetStyle(ControlStyles.UserPaint, True)
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)
        Me.SetStyle(ControlStyles.DoubleBuffer, True)
        Me.SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        Me.SetStyle(ControlStyles.ResizeRedraw, True)
        Me.UpdateStyles()

        Me.mFontText = CreateFontSafe("微软雅黑", 9, FontStyle.Regular)
        Me.mFontNumber = CreateFontSafe("微软雅黑", 11.5, FontStyle.Regular)
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        'MyBase.OnResize(e)
        'Me.Invalidate()
    End Sub

    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        Dim strMessage As String = String.Format(Me.StringHead, "#count#", "#size#")
        Dim strValues() As String = strMessage.Split("#")
        Dim intX As Integer = Me.Width
        Dim g As Graphics = e.Graphics
        Dim formatCenter As New StringFormat
        formatCenter.Alignment = StringAlignment.Center
        formatCenter.LineAlignment = StringAlignment.Center

        For index As Integer = 0 To strValues.Length - 1
            Dim strValue As String = strValues(strValues.Length - 1 - index)
            Dim size As SizeF = g.MeasureString(strValue, Me.mFontText)
            Dim font As Font = Me.mFontText
            Dim brash As Brush = Me.mBrashText

            If strValue = "count" Then
                strValue = Me.mFileCount.ToString
                size = g.MeasureString(strValue, Me.mFontNumber)
                font = Me.mFontNumber
                brash = Me.mBrushNumber
                intX = intX - size.Width - 2

            ElseIf strValue = "size" Then
                strValue = Me.FormatFileSize(Me.mFileSize)
                size = g.MeasureString(strValue, Me.mFontNumber)
                font = Me.mFontNumber
                brash = Me.mBrushNumber
                intX = intX - size.Width - 2

            Else
                intX = intX - size.Width - 2
            End If

            Dim rect As New Rectangle(intX, 0, size.Width + 2, Me.Height)

            g.DrawString(strValue, font, brash, rect, formatCenter)
        Next



        ''MyBase.OnPaint(e)
        'Dim size1 As SizeF
        'Dim size2 As SizeF
        'Dim size3 As SizeF
        'Dim size4 As SizeF
        'Dim size5 As SizeF

        'Dim rect1 As Rectangle
        'Dim rect2 As Rectangle
        'Dim rect3 As Rectangle
        'Dim rect4 As Rectangle
        'Dim rect5 As Rectangle

        'Dim string1 As String = "空间"
        'Dim string2 As String = Me.FormatFileSize(Me.mFileSize)

        'Dim string4 As String = Me.mFileCount.ToString


        'Dim formatCenter As New StringFormat
        'formatCenter.Alignment = StringAlignment.Center
        'formatCenter.LineAlignment = StringAlignment.Center

        'Dim g As Graphics = e.Graphics

        'size4 = g.MeasureString(string1, Me.mFontText)
        'rect4.Height = Me.Height
        'rect4.Width = size4.Width + 2
        'rect4.Location = New Point(Me.Width - rect4.Width, 0)
        'g.DrawString(string1, Me.mFontText, Me.mBrashText, rect4, formatCenter)

        'size3 = g.MeasureString(string2, Me.mFontNumber)
        'rect3.Height = Me.Height
        'rect3.Width = size3.Width + 1
        'rect3.Location = New Point(Me.Width - rect4.Width - rect3.Width, 0)
        'g.DrawString(string2, Me.mFontNumber, Me.mBrushNumber, rect3, formatCenter)

        'size2 = g.MeasureString(mStringMiddle, Me.mFontText)
        'rect2.Height = Me.Height
        'rect2.Width = size2.Width + 2
        'rect2.Location = New Point(Me.Width - rect4.Width - rect3.Width - rect2.Width, 0.0)
        'g.DrawString(mStringMiddle, Me.mFontText, Me.mBrashText, rect2, formatCenter)

        'size1 = g.MeasureString(string4, Me.mFontNumber)
        'rect1.Height = Me.Height
        'rect1.Width = size1.Width + 1
        'rect1.Location = New Point(Me.Width - rect4.Width - rect3.Width - rect2.Width - rect1.Width, 0)
        'g.DrawString(string4, Me.mFontNumber, Me.mBrushNumber, rect1, formatCenter)

        'size5 = g.MeasureString(Me.mStringHead, Me.mFontText)
        'rect5.Height = Me.Height
        'rect5.Width = size5.Width + 1
        'rect5.Location = New Point(Me.Width - rect4.Width - rect3.Width - rect2.Width - rect1.Width - rect5.Width, 0)
        'g.DrawString(mStringHead, Me.mFontText, Me.mBrashText, rect5, formatCenter)

    End Sub

    Public Function FormatFileSize(ByVal filesize As Long) As String
        Dim strReturn As String = ""
        If (filesize >= 1073741824) Then
            strReturn = (1.0 * filesize / 1073741824).ToString("f2") & " GB"
        ElseIf (filesize >= 1048576) Then
            strReturn = (1.0 * filesize / 1048576).ToString("f2") & " MB"
        ElseIf (filesize >= 1024) Then
            strReturn = (1.0 * filesize / 1024).ToString("f2") & " KB"
        Else
            strReturn = (1.0 * filesize).ToString("f2") & " Bytes"
        End If

        Return strReturn
    End Function

    '创建字体
    Private Function CreateFontSafe(ByVal fontFamaly As String, ByVal size As Integer, ByVal style As FontStyle) As Font
        Dim fontCreate As Font = Nothing
        Try
            fontCreate = New Font(fontFamaly, size, style)
        Catch ex As Exception
            fontCreate = Common.CreateFont("宋体", size, style)
        End Try
        Return fontCreate
    End Function

End Class
