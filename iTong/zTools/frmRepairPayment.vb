﻿Imports System.Threading
Imports ProgressStyle = iTong.Components.ProgressStyle

Public Class frmRepairPayment

    Private dgvSoft As New tbDataGridViewEx
    Private mThrLoad As Thread
    Private mThrDownlaod As Thread
    Private mThrCheclTuiInstall As Thread
    Private mThrGetMoney As Thread
    Private mQueueDownlaod As New Queue(Of RepairObj)

    Private mDownManage As MultiThreadDownload
    Private mInstallHelper As iPhoneInstallHelper = Nothing

    Private mDictCanRepaid As New Dictionary(Of String, RepairObj)
    Private mDictCanNotRepaid As New Dictionary(Of String, RepairObj)
    Private mDictNotNeedRepaid As New Dictionary(Of String, RepairObj)
    Private mDictGridRow As New Dictionary(Of String, tbDataGridViewRow)

    Private mRepairPaymentResult As RepairPaymentResult = New RepairPaymentResult()
    Private mPluginLogin As PluginLogin                 '登陆接口
    Private mType As RepairFormType = RepairFormType.RepairPayment
    Private mCoreUpdate As CoreUpdateHelper

#Region "----初始化、窗体闭关----"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice, Optional ByVal type As RepairFormType = RepairFormType.RepairPayment)
        MyBase.New(application, device)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.Language = application.Language
        Me.mApplication = application
        Me.mDevice = device
        Me.FunctionMappingKey = FunctionKey.None
        Me.FilletRadius = 5
        Me.Name = Me.mDevice.Identifier & "frmRepairPayment"
        Me.mType = type
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.mPluginLogin = PluginLogin.Instance()

        Me.Icon = My.Resources.iTong
        Me.tbGuiBackground = My.Resources.Resources.frm_bg_state
        Me.tbSplit = "10,33,10,33"
        Me.Size = New Size(870, 620)
        Me.StartPosition = FormStartPosition.CenterParent

        Me.InitPanel()
        Me.InitGrid()
        Me.InitDownloadAndInstall()

        Me.txtDescripton.ReadOnly = True
        Me.txtDescripton.ForeColor = Color.FromArgb(61, 61, 61, 61)
        Me.txtDescripton.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)

        Me.btnDescription.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)
        Me.btnInstallTui.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)
        Me.btnStartRepair.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)
        Me.btnStartRepairEx.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)

        Me.lblNoRecordTitle.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)
        Me.lblNoRecordReason.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.lblInstallTui1.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)
        Me.lblLoadingEx.Font = Common.CreateFont("微软雅黑", 20.0!, FontStyle.Regular)

        Me.InitRepair()
    End Sub

    Private Sub InitRepair()
        If Me.mType = RepairFormType.Repair Then
            Me.pnlGrid.Controls.Remove(Me.dgvSoft)
            Me.pnlGridRepair.Controls.Add(Me.dgvSoft)

            '设置为空的界面
            Me.pnlEmpty.Location = Me.pnlGridRepair.Location
            Me.pnlEmpty.Size = Me.pnlGridRepair.Size
            Me.pnlEmpty.Anchor = Me.pnlGridRepair.Anchor
            Me.pnlEmpty.SendToBack()

            Me.LoadData()
            Me.txtNameValue.Size = New Size(Me.txtNameValue.Width, Me.txtNameValue.Height + 1)
            Me.pnlGridRepair.Padding = New Padding(0, 1, 0, 0)
        End If
    End Sub

    Private Sub InitPanel()
        Dim pnlSize As New Size(Me.Width - 2, Me.Height - 32)
        Dim pnlLocation As New Point(1, 31)
        Me.pnlDescription.Size = pnlSize
        Me.pnlNoRecord.Size = pnlSize
        Me.pnlRecord.Size = pnlSize
        Me.pnlTui.Size = pnlSize
        Me.pnlRecordRepair.Size = New Size(pnlSize.Width, pnlSize.Height - 27)

        Me.pnlDescription.Location = pnlLocation
        Me.pnlNoRecord.Location = pnlLocation
        Me.pnlRecord.Location = pnlLocation
        Me.pnlTui.Location = pnlLocation
        Me.pnlRecordRepair.Location = pnlLocation

        Me.SetPnlLoading(pnlLocation, pnlSize)
        Me.SetView(RepairPaymentStatus.Description)
    End Sub

    Private Sub InitGrid()
        Me.dgvSoft = New tbDataGridViewEx
        With Me.dgvSoft
            .SuspendLayout()
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.ForeColor = Color.Black

            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Common.Label.Name"), _
                                                                                             310, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.True) '"Name"
            colName.DefaultCellStyle = cellStyle
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 58
            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(colName)
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewMediaColumn), "colStatus", "", 20, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colVersion", Me.Language.GetString("App.Column.Version"), 100, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) '"当前版本"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), 100, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) '"Size"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", Me.Language.GetString("File.Label.Status"), 280, True, False, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))
            .AllowDrop = True
            .GroupRowTemplateArray = Me.GetGroupTemplate
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .ResumeLayout()
        End With

        RemoveHandler dgvSoft.CellButtonClick, AddressOf dgvSoft_CellButtonClick
        AddHandler dgvSoft.CellButtonClick, AddressOf dgvSoft_CellButtonClick

        'RemoveHandler dgvSoft.ShowToolTip, AddressOf dgvSoft_ShowToolTip
        'AddHandler dgvSoft.ShowToolTip, AddressOf dgvSoft_ShowToolTip

        RemoveHandler dgvSoft.GroupTextClick, AddressOf dgvSoft_GroupTextClick
        AddHandler dgvSoft.GroupTextClick, AddressOf dgvSoft_GroupTextClick

        RemoveHandler dgvSoft.SelectionChanged, AddressOf dgvSoft_SelectionChanged
        AddHandler dgvSoft.SelectionChanged, AddressOf dgvSoft_SelectionChanged

        Me.pnlGrid.Controls.Add(Me.dgvSoft)
    End Sub

    Private Function GetGroupTemplate() As Object()
        Return New Object() {False, _
                                          "", _
                                          True, _
                                          "", _
                                          "", _
                                          "", _
                                          ""}
    End Function

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        If Me.mType = RepairFormType.Repair Then
            Me.Text = Me.Language.GetString("Tools.Button.StronRepair")             '强力修复
        Else
            Me.Text = Me.Language.GetString("Tools.Payment.Text.Payment")           '"闪退赔付"
        End If

        Me.txtDescripton.Text = String.Format(Me.Language.GetString("Tools.Payment.Text.PaymentDescription"), ServerIniSetting.GetRepairPaymentTime())
        '"活动时间：" & vbCrLf & _
        '                        ServerIniSetting.GetRepairPaymentTime() & vbCrLf & vbCrLf & _
        '                        "参与方式：" & vbCrLf & _
        '                        "1、打开电脑端同步助手" & vbCrLf & _
        '                        "2、连接设备" & vbCrLf & _
        '                        "3、点击【闪退赔付】即可！" & vbCrLf & vbCrLf & _
        '                        "1、下载安装最新版同步助手（V3.1.3）及最新版同步推（V3.3.0）；" & vbCrLf & _
        '                        "注意事项：" & vbCrLf & _
        '                        "   （1）电脑端同步助手（V3.1.3）与手机端同步推（V3.3.0及以上）必须是最新版本" & vbCrLf & _
        '                        "   （2）未安装同步推的用户无法参与本活动，点击安装同步推" & vbCrLf & _
        '                        "   （3）本活动仅限iPhone未越狱用户参与" & vbCrLf & _
        '                        "   （4）参与用户的设备固件必须在7.x以上" & vbCrLf & _
        '                        "   （5）非同步推与同步助手下载的闪退APP不属于赔付范围" & vbCrLf & _
        '                        "   （6）Apple ID未授权不属于赔付范围。请在运行软件时，输入在同步推中领用的Apple ID即可。" & vbCrLf & vbCrLf & _
        '                        "2、在电脑上连接您的苹果设备，打开同步助手，点击【检测闪退】，同步助手将检测您的设备中是否有闪退软件；" & vbCrLf & _
        '                        "3、如您是旧版同步推用户（同步推V3.3.0前的版本），则无论闪退软件数量多少，直接赔付2元；如您是新版同步推（V3.3.0）用户，则每个闪退软件赔付0.5元，即您所获得的赔偿=0.5*闪退软件个数，上限10元；" & vbCrLf & _
        '                        "4、赔付您可直接用于推币充值，用于推币充值可直接返利25%；也可直接转现，可将赔付转入支付宝内，每次转入需收取8%的手续费。"

        Me.btnDescription.Text = Me.Language.GetString("Tools.Payment.Label.Description")                   '"活动说明"
        Me.btnStartRepair.Text = Me.Language.GetString("Tools.Payment.Button.Start")                        '"开始体验"
        Me.btnStartRepairEx.Text = Me.Language.GetString("Welcome.Repair.Button.StartRepair")               '"重新检测"
        Me.btnInstallTui.Text = Me.Language.GetString("Download.Button.Install")                            '"立即安装"

        Me.btnStartRepairEx1.Text = Me.Language.GetString("Welcome.Repair.Button.StartRepair")              '"重新检测"
        Me.btnRepair.Text = Me.Language.GetString("Tools.Payment.Button.RepairApp")                         '"修复应用"
        Me.btnGetTwoYuan.Text = String.Format(Me.Language.GetString("Tools.Payment.Button.GetMoney"), 2)    '"领取2元"
        Me.btnMyPayment.Text = Me.Language.GetString("Tools.Payment.Button.MyPayment")                      '"我的赔付金"
        Me.btnStartRepairEx1.Height = 35
        Me.btnRepair.Height = 36
        Me.btnGetTwoYuan.Height = 35

        Me.lblLoadingEx.Text = Me.Language.GetString("Tools.Message.CheckingErrorApp")                      '"正在检测设备中闪退的应用..."

        Me.lblNoRecordTitle.Text = Me.Language.GetString("Tools.Payment.Label.NoRecordTitle")               '"未检测到闪退的应用"
        Me.lblNoRecordReason.Text = Me.Language.GetString("Tools.Payment.Label.NoRecordReason")             '"可能有以下几个原因："
        Me.lblNoRecordReason1.Text = Me.Language.GetString("Tools.Payment.Label.NoRecordReason1")              '"闪退的应用并不是同步平台（同步助手、同步推等）下载；" & vbCrLf & "建议从同步助手的应用商店中重新下载安装。"
        Me.lblNoRecordReason2.Text = String.Format(Me.Language.GetString("Tools.Payment.Label.NoRecordReason2"), Utility.TongbuContact)  '"如果您的应用确实在同步平台上下载且闪退，请联系客服" & vbCrLf & "加入客服QQ群号: {0}"

        Me.lblInstallTui1.Text = Me.Language.GetString("Tools.Payment.Label.InstallTui1")                   '"您尚未安装同步推"
        Me.picInstallTui2.Text = Me.Language.GetString("Tools.Payment.Label.InstallTui2")                   '"安装后即可参与你闪退我赔付活动"

        'Me.lblJailbreak.Text = Me.Language.GetString("Tools.Message.PreventFlashBack")      '"杜绝闪退"
        'Me.btnAllRepair.Text = Me.Language.GetString("Tools.Message.JailbreakImmediately")  '"立即越狱"
        Me.txtNameValue.TextTip = Me.Language.GetString("Tools.Message.SearchRepairApp")    '"搜索您要修复的应用"
        Me.lblFileEmpty.Text = Me.Language.GetString("Media.Label.Empty")                   '"这里没有数据"
        Me.lblTipJaibreak.Text = Me.Language.GetString("Tools.Message.PreventFlashBackAfterJaibreak") '越狱后可完全杜绝闪退
        Me.lblJaiBreak.Text = Me.Language.GetString("Tools.Message.HowtoJaibreak")  '如何越狱
        Me.btnAllRepair.Text = Me.Language.GetString("Tools.Message.OneKeyRepair") '一键修复
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Try
            RemoveHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload

            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            RemoveHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
            RemoveHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse

            RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
            RemoveHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_BeforeFormClose")
        End Try
        Try
            If Me.mthrLoad IsNot Nothing AndAlso Me.mthrLoad.ThreadState <> ThreadState.Stopped Then
                Me.mthrLoad.Abort()
            End If
        Catch ex As Exception
        End Try
        Try
            If Me.mThrDownlaod IsNot Nothing AndAlso Me.mThrDownlaod.ThreadState <> ThreadState.Stopped Then
                Me.mThrDownlaod.Abort()
            End If
        Catch ex As Exception
        End Try
        Try
            If Me.mThrCheclTuiInstall IsNot Nothing AndAlso Me.mThrCheclTuiInstall.ThreadState <> ThreadState.Stopped Then
                Me.mThrCheclTuiInstall.Abort()
            End If
        Catch ex As Exception
        End Try
        Try
            If Me.mThrGetMoney IsNot Nothing AndAlso Me.mThrGetMoney.ThreadState <> ThreadState.Stopped Then
                Me.mThrGetMoney.Abort()
            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Overrides Sub OnUsbDisconnect(ByVal usbDev As Android.UsbDevice)
        MyBase.OnUsbDisconnect(usbDev)
        Me.Close()
    End Sub

    Private Sub InitDownloadAndInstall()
        Try
            Me.mCoreUpdate = CoreUpdateHelper.Instance()
            RemoveHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload
            AddHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload

            Me.mDownManage = MultiThreadDownload.Instance
            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            RemoveHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
            AddHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
            RemoveHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
            AddHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse

            RemoveHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
            AddHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd

            Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)
            RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
            AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall

            RemoveHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
            AddHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_InitDownloadAndInstall")
        End Try

    End Sub

#End Region

#Region "----Grid事件----"

    Private Sub dgvSoft_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvSoft.Rows(e.Cell.RowIndex)

            Select Case e.CellButton
                Case CellButtonStyle.Backup, CellButtonStyle.Failed
                    Me.RepairRow(row)

                Case CellButtonStyle.Uninstall

                Case CellButtonStyle.Cancel
                    Me.DoCancelRepair(row)

                Case CellButtonStyle.BackupFolder


                Case CellButtonStyle.Reinstall


                Case CellButtonStyle.Document

                Case CellButtonStyle.OneRetryButton
                    Me.StarDownloadApp(row.Tag)

            End Select
        Catch ex As Exception

        End Try
    End Sub

    Private Sub dgvSoft_GroupTextClick(ByVal sender As Object, ByVal args As System.Windows.Forms.MouseEventArgs)
        For Each row As tbDataGridViewRow In Me.mDictGridRow.Values
            If Not row.Selected Then
                Continue For
            End If
            Me.RepairRow(row)
        Next
    End Sub

    Public Sub dgvSoft_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If Me.mType <> RepairFormType.Repair Then
            Return
        End If
        Dim intCoutnt As Integer = 0
        For Each row As tbDataGridViewRow In Me.mDictGridRow.Values
            If Not row.Selected Then
                Continue For
            End If
            intCoutnt += 1
            If intCoutnt Mod 30 = 0 Then
                Application.DoEvents()
            End If
        Next

        If Me.dgvSoft.DictGroupKeys.ContainsKey(Me.Language.GetString("Tools.Payment.Label.FlashApp")) Then
            Dim row As tbDataGridViewRow = Me.dgvSoft.DictGroupKeys(Me.Language.GetString("Tools.Payment.Label.FlashApp"))
            If row IsNot Nothing Then
                row.GroupClickText = String.Format("{0}({1})", Me.Language.GetString("Welcome.Repair.Title"), intCoutnt)
                Me.dgvSoft.Refresh()
            End If
        End If
    End Sub

    'Private Sub dgvSoft_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
    '    Try

    '    Catch ex As Exception

    '    End Try
    'End Sub

#End Region

#Region "----下载、安装事件----"

    Public Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If String.IsNullOrEmpty(item.ItemInfo.Url) Then
            Try
                If Not Me.mDictGridRow.ContainsKey(item.ItemInfo.Identifier) Then
                    Return
                End If
                '如果当前下载的任务url SourceUrl  为空就设置修复列表的状态。
                Dim row As tbDataGridViewRow = Me.mDictGridRow(item.ItemInfo.Identifier)
                If row IsNot Nothing AndAlso String.IsNullOrEmpty(item.ItemInfo.Url) AndAlso Not item.ItemInfo.Source = TaskSource.AppleDownload Then
                    Me.SetCurrentRowStatus(row, AppIconStatus.Failure, 0, Me.Language.GetString("Tools.Payment.Message.DownloadFailure"))
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "frmRepairPayment_OnTaskAdd")
            End Try
        End If
    End Sub

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of MultiThreadDownloadEventArgs)(AddressOf OnDownloading), sender, e)
            Else
                Dim item As MultiThreadDownloadItem = sender
                Dim strIdentify As String = item.ItemInfo.Identifier

                If item.ItemInfo.Class = ResourceClass.Software AndAlso Me.mDictGridRow.ContainsKey(strIdentify) Then
                    Dim row As tbDataGridViewRow = Me.mDictGridRow(strIdentify)

                    If row Is Nothing OrElse _
                      row.Tag Is Nothing OrElse _
                      CType(row.Tag, RepairObj).repairInfo Is Nothing OrElse _
                      CType(row.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair Then
                        Return
                    End If

                    Me.SetCurrentRowStatus(row, AppIconStatus.Repairing, e.Progress / 2, "")
                End If

                '同步推下载进度
                If item.ItemInfo.Type = ResourceType.pTui Then
                    Me.SetTuiProgressStatus(True, Me.Language.GetString("App.Cell.Downloading"), True, False, e.Progress)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_OnDownloading")
        End Try
    End Sub

    Private Sub OnTaskAppleStoreAnalyse(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.ItemInfo.Class = ResourceClass.Software AndAlso Me.mDictGridRow.ContainsKey(item.ItemInfo.Identifier) Then
            Dim row As tbDataGridViewRow = Me.mDictGridRow(item.ItemInfo.Identifier)
            If Not item.ItemInfo.IsDeleted AndAlso Not String.IsNullOrEmpty(item.ItemInfo.ErrorMsg) Then
                Me.SetCurrentRowStatus(row, AppIconStatus.Failure, 0, item.DownloadInfo.LastErrorMsg)
            End If
        End If
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloaded), sender, e)
            Else
                Dim item As MultiThreadDownloadItem = sender
                If item.ItemInfo.Class = ResourceClass.Software AndAlso Me.mDictGridRow.ContainsKey(item.ItemInfo.Identifier) Then
                    Dim row As tbDataGridViewRow = Me.mDictGridRow(item.ItemInfo.Identifier)

                    If row Is Nothing OrElse _
                       row.Tag Is Nothing OrElse _
                       CType(row.Tag, RepairObj).repairInfo Is Nothing OrElse _
                       CType(row.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair Then
                        Return
                    End If
                    If e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
                        Me.SetCurrentRowStatus(row, AppIconStatus.Repairing, 50, "")

                    ElseIf item.DownloadInfo.Cancel Then
                        Me.SetCurrentRowStatus(row, AppIconStatus.None, 0)

                    Else
                        Dim strErrorMessage As String = item.DownloadInfo.LastErrorMsg
                        If String.IsNullOrEmpty(strErrorMessage) Then
                            strErrorMessage = Me.Language.GetString("Tools.Payment.Message.DownloadFailure")            '"下载失败"
                        End If
                        Me.SetCurrentRowStatus(row, AppIconStatus.Failure, 0, strErrorMessage)

                    End If
                End If

                '同步推下载进度
                If item.ItemInfo.Type = ResourceType.pTui Then
                    Me.SetTuiProgressStatus(True, Me.Language.GetString("App.Cell.Downloading"), True, False, 0)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_OnDownloaded")
        End Try
    End Sub

    Private Sub OnTaskDelete(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.Status <> TaskState.Completed AndAlso item.ItemInfo.Class = ResourceClass.Software Then
                Dim row As tbDataGridViewRow = Me.mDictGridRow(item.ItemInfo.Identifier)

                If row Is Nothing OrElse _
                   row.Tag Is Nothing OrElse _
                   CType(row.Tag, RepairObj).repairInfo Is Nothing OrElse _
                   CType(row.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair Then
                    Return
                End If
                Utility.WaitSeconds(0.2)
                Me.SetCurrentRowStatus(row, AppIconStatus.Failure, 0, Me.Language.GetString("Tools.Payment.Message.DownloadFailure"))
            End If

            '同步推下载进度
            If item.ItemInfo.Type = ResourceType.pTui Then
                Me.SetTuiProgressStatus(True, "", False, False, 0)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_OnTaskDelete")
        End Try
    End Sub

    Private Sub OnCheckDownload(ByVal sender As Object, ByVal args As CheckDownloadArgs)
        Try
            Dim row As tbDataGridViewRow = Me.mDictGridRow(args.Identifier)
            If row Is Nothing Then
                Return
            End If

            Dim status As AppIconStatus = AppIconStatus.Failure
            Select Case args.State
                Case SoftDownloadErrorState.Logining, SoftDownloadErrorState.Succeed, SoftDownloadErrorState.Buying, AppIconStatus.WaitDownloading
                    status = AppIconStatus.Downloading
            End Select

            Me.SetCurrentRowStatus(row, status, 0, CoreUpdateHelper.GetErrorDescriptionByState(args.State, False))
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_OnCheckDownload")
        End Try
    End Sub

    Private Delegate Sub SetTuiProgressStatusHandler(ByVal blnIsTui As Boolean, ByVal strMessage As String, ByVal isVisable As Boolean, ByVal isWaiting As Boolean, ByVal intProgress As Integer)
    Private Sub SetTuiProgressStatus(ByVal blnIsTui As Boolean, ByVal strMessage As String, ByVal isVisable As Boolean, ByVal isWaiting As Boolean, ByVal intProgress As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New SetTuiProgressStatusHandler(AddressOf SetTuiProgressStatus), blnIsTui, strMessage, isVisable, isWaiting, intProgress)
        Else
            If Not blnIsTui Then
                Return
            End If
            Me.btnInstallTui.Enabled = Not isVisable
            Me.lblTuiProgress.Text = strMessage
            Me.pbarShowProgress.Visible = isVisable
            Me.pbarShowProgress.tbIsWaiting = isWaiting
            Me.pbarShowProgress.tbPlayValue = intProgress
            If strMessage.Contains("-402636802") Then
                '"请关闭设备上的访问限制。"
                tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.CloseAccessRestrictions"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, MessageBoxDefaultButton.Button1, My.Resources.webclip_note)
            ElseIf iPhoneDevice.IsCodesignOverdue(strMessage) Then
                '软件过期
                tbMessageBox.Show(Me, Me.Language.GetString("PkgInstaller.Label.InstallFailCompatible"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, MessageBoxDefaultButton.Button1, My.Resources.webclip_note)
            End If
        End If
    End Sub

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try

            Dim blnIsTui As Boolean = False
            Dim row As tbDataGridViewRow = Me.GetRowByID(args.PackageInfo.Identifier, args.PackageInfo.ItemId)

            If row Is Nothing Then
                '同步推安装进度
                If args.PackageInfo.IsTui Then
                    blnIsTui = True
                Else
                    Return
                End If
            ElseIf row.Tag Is Nothing OrElse _
               (CType(row.Tag, RepairObj).repairInfo IsNot Nothing AndAlso _
                CType(row.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair) Then
                '只有可修复的列表才有进度。
                Return
            End If

            Dim obj As RepairObj = Nothing
            If row IsNot Nothing Then
                obj = row.Tag
            End If

            '用户点修复的时候如果已经下载过的应用就从0开始走进度，如果没下载的应该 下载0-50% 上专50-80%，安装80-100%
            Dim intProgress As Integer = 0
            If obj IsNot Nothing AndAlso obj.hasdownload Then
                If args.InstallState = InstallState.Installing Then
                    intProgress = 60 + (args.TransferProgress * 0.4)
                Else
                    intProgress = args.TransferProgress * 0.6
                End If
            Else
                If args.InstallState = InstallState.Installing Then
                    intProgress = 80 + (args.TransferProgress * 0.2)
                Else
                    intProgress = 50 + (args.TransferProgress * 0.3)
                End If
            End If

            Select Case args.InstallState
                Case InstallState.AllInstallCompleted

                Case InstallState.Waiting
                    Me.SetTuiProgressStatus(blnIsTui, Me.Language.GetString("App.Cell.WaitingInstall"), True, True, 0)
                    Me.SetCurrentRowStatus(row, AppIconStatus.Repairing, intProgress, "")

                Case InstallState.AuthBegin
                    Me.SetTuiProgressStatus(blnIsTui, Me.Language.GetString("App.Message.Calculating"), True, True, 0)
                    Me.SetCurrentRowStatus(row, AppIconStatus.Repairing, intProgress, "")

                Case InstallState.Transfering
                    Me.SetTuiProgressStatus(blnIsTui, Me.Language.GetString("File.Label.Uploading"), True, False, args.TransferProgress)
                    Me.SetCurrentRowStatus(row, AppIconStatus.Repairing, intProgress, "")

                Case InstallState.Installing
                    Me.SetTuiProgressStatus(blnIsTui, Me.Language.GetString("App.Cell.Installing"), True, False, args.TransferProgress)
                    Me.SetCurrentRowStatus(row, AppIconStatus.Repairing, intProgress, "")

                Case InstallState.CancelInstall
                    Me.SetTuiProgressStatus(blnIsTui, args.ErrorMsg, True, False, 0)
                    Me.SetCurrentRowStatus(row, AppIconStatus.Failure, 0, Me.Language.GetString("PkgInstaller.Label.InstallFail"))

                Case InstallState.UnSucceed, InstallState.HaveNotInstall
                    Me.SetTuiProgressStatus(blnIsTui, args.ErrorMsg, True, False, 0)
                    Me.SetCurrentRowStatus(row, AppIconStatus.Failure, 0, args.ErrorMsg)

                Case InstallState.Succeed
                    If blnIsTui Then
                        Me.LoadData()
                    End If
                    Me.SetCurrentRowStatus(row, AppIconStatus.Succeed)

                Case InstallState.FailRetry, InstallState.UnSucceedCraked
                    Me.SetTuiProgressStatus(blnIsTui, args.ErrorMsg, True, False, 0)
                    Dim status As AppIconStatus = AppIconStatus.Failure
                    If args.InstallState = InstallState.FailRetry Then
                        status = InstallState.FailRetry
                    End If
                    Me.SetCurrentRowStatus(row, status, 0, args.ErrorMsg)


                Case Else
                    Me.SetTuiProgressStatus(blnIsTui, Me.Language.GetString("App.Cell.Installing"), True, False, args.TransferProgress)
                    Me.SetCurrentRowStatus(row, AppIconStatus.Repairing, intProgress, "")

            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmRepairPayment_mInstallHelper_OnInstall")
        End Try
    End Sub

    Private Sub OnDownloadEventHandler(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        If sender IsNot Nothing AndAlso TypeOf sender Is WebSiteHelperBase.AnalyseDownloadInfo Then
            Dim info As WebSiteHelperBase.AnalyseDownloadInfo = sender
            If info.Status = TaskState.Failed Then
                If Me.mDictCanRepaid.ContainsKey(info.Identifier) AndAlso Me.mDictGridRow.ContainsKey(info.Identifier) Then
                    Dim strErrorMsg As String = Me.Language.GetString("Tools.Payment.Message.DownloadFailure") & Me.Language.GetString("Common.Symbol.Colon") & Me.Language.GetString("App.Label.BindingAppleId")
                    Me.SetCurrentRowStatus(Me.mDictGridRow(info.Identifier), AppIconStatus.Failure, 0, strErrorMsg)
                    Dim obj As RepairObj = Me.mDictCanRepaid(info.Identifier)
                    If obj IsNot Nothing AndAlso obj.repairInfo IsNot Nothing Then
                        obj.repairInfo.ShowLogingAppleId = True
                    End If
                End If
            End If
        End If
    End Sub

#End Region

#Region "----窗体事件----"

    Private Sub btnStartRepair_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStartRepair.Click
        Me.CheckTuiInstall()
    End Sub

    Private Sub CheckTuiInstall()
        Try
            Me.SetView(RepairPaymentStatus.Loading)
            If Me.mThrCheclTuiInstall IsNot Nothing AndAlso Me.mThrCheclTuiInstall.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mThrCheclTuiInstall = New Thread(AddressOf CheckTuiInstallThread)
            With mThrCheclTuiInstall
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_CheckTuiInstall")
        End Try
    End Sub

    Private Sub CheckTuiInstallThread()
        Dim info As FileSharingPackageInfo = Me.mDevice.GetTuiInfo()
        If info Is Nothing Then
            Me.SetView(RepairPaymentStatus.InstallTui)
        Else
            Me.LoadData()
        End If
    End Sub

    Private Sub btnInstallTui_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnInstallTui.Click
        '先下载同步推并安装
        'Dim blnJailbresk As Boolean = False
        'If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
        '    blnJailbresk = True
        'End If
        'TuiInstallHelper.DownloadTbTuiHD(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, blnJailbresk)

        ''启一个线程加载数据
        'Me.LoadData()

        AuthorizeByAppleID.Instance(Me.mDevice).CheckTui()
    End Sub

    Private Sub btnGetTwoYuan_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGetTwoYuan.Click
        If Not Me.mPluginLogin.IsLogin Then
            frmLogin.ShowLogin(Me.mApplication)
            Return
        End If

        Me.GetMoney()
    End Sub

    Private Sub btnMyPayment_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMyPayment.Click
        If Not Me.mPluginLogin.CheckLogin Then
            frmLogin.ShowLogin(Me.mApplication, False)
            Return
        End If

        'Dim frm As frmPersonalCenter = GetPersonalCenter(Me.mDevice)

        'If frm IsNot Nothing Then
        '    frm.Activate()
        '    frm.BringToFront()
        '    frm.WindowState = FormWindowState.Normal
        '    frm.Show()
        'Else
        '    frm = New frmPersonalCenter(Me.mApplication, Me.mDevice)
        '    frm.Show(Me)
        'End If
        ShowPersonalCenter(Me, Me.mApplication, Me.mDevice)
    End Sub

    Private Sub btnRepair_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRepair.Click
        If Me.dgvSoft.SelectedRows.Count <= 0 Then
            '"请选择要修复的应用！"
            tbMessageBox.Show(Me, Me.Language.GetString("Tools.Payment.Message.SelectRepairApp"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            Return
        End If

        For Each Item As tbDataGridViewRow In Me.dgvSoft.SelectedRows
            Me.RepairRow(Item)
            Utility.WaitSeconds(0.1)
        Next
    End Sub

    Private Sub btnStartRepairEx_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStartRepairEx.Click, btnStartRepairEx1.Click
        Me.LoadData()
    End Sub

    Private Sub btnMyPayment_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMyPayment.SizeChanged, btnGetTwoYuan.SizeChanged, btnRepair.SizeChanged, btnStartRepairEx1.SizeChanged
        Me.btnMyPayment.Location = New Point(Me.Width - Me.btnMyPayment.Width - 25, Me.btnMyPayment.Top)
        Me.btnGetTwoYuan.Location = New Point(Me.btnMyPayment.Left - Me.btnGetTwoYuan.Width - 15, Me.btnGetTwoYuan.Top)
        Me.btnRepair.Location = New Point(Me.btnGetTwoYuan.Left - Me.btnRepair.Width - 20, Me.btnRepair.Top)
        Me.btnStartRepairEx1.Location = New Point(Me.btnRepair.Left - Me.btnStartRepairEx1.Width - 20, Me.btnStartRepairEx1.Top)
    End Sub

#End Region

#Region "----加载数据----"

    Private Sub LoadData()
        Try
            Me.SetView(RepairPaymentStatus.Loading)
            If Me.mthrLoad IsNot Nothing AndAlso Me.mthrLoad.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mthrLoad = New Thread(AddressOf LoadDataThread)
            With mthrLoad
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_LoadData")
        End Try
    End Sub

    Private Sub LoadDataThread()
        Dim dictApps As New Dictionary(Of String, FileSharingPackageInfo)
        Try
            '加载本手机上的软件信息
            dictApps = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            If dictApps Is Nothing OrElse dictApps.Count <= 0 Then
                Utility.WaitSeconds(0.5)
                dictApps = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            End If

            '从服务器取得要可以修复的软件信息
            iPhoneDeviceHelper.GetDeviceITunesMedataInfo(Me.mDevice, dictApps, False, True)
            Dim lstPkgInfo As New List(Of PackageInfo)
            For Each Item As FileSharingPackageInfo In dictApps.Values
                'iPhoneDeviceHelper.GetDeviceITunesMedataInfo(Me.mDevice, Item, True)
                lstPkgInfo.Add(Item)
            Next

            Dim blnJail As Boolean = False
            If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
                blnJail = True
            End If
            Dim blnAccount As Boolean = True
            If String.IsNullOrEmpty(IniSetting.GetDefaultUserAccount) Then
                blnAccount = False
            End If

            Dim tuiItem As FileSharingPackageInfo = Me.mDevice.GetTuiInfo()
            Dim tuiIdentify As String = ""
            Dim tuiVersion As String = ""
            If tuiItem IsNot Nothing Then
                tuiIdentify = tuiItem.Identifier
                tuiVersion = tuiItem.Version
            End If

            Dim isPayment As Boolean = True
            If Me.mType = RepairFormType.Repair Then
                isPayment = False
            End If
            Me.mRepairPaymentResult = HtmlHelper.CheckSoftRepairPayment(isPayment, _
                                                                        lstPkgInfo, _
                                                                        tuiIdentify, _
                                                                        tuiVersion, _
                                                                        IniSetting.GetDefaultUserAccount(), _
                                                                        My.Application.Info.Version.ToString(4), _
                                                                        Me.mDevice.ProductType, Me.mDevice.ProductVersion, _
                                                                        Me.mDevice.SerialNumber, blnJail, _
                                                                        DeviceIni.GetAuthVersion)

            Me.mDictCanRepaid.Clear()
            Me.mDictCanNotRepaid.Clear()
            Me.mDictNotNeedRepaid.Clear()

            For Each Item As PackageInfo In dictApps.Values
                'If Item.Name.Contains("龙枪") Then
                '    Dim a As String = ""
                'End If
                Dim obj As New RepairObj
                obj.pkgInfo = Item
                obj.repairInfo = Nothing

                If dictApps.ContainsKey(Item.Identifier) AndAlso _
                  (dictApps(Item.Identifier).CrackedInfo = CrakedInfo.Craked OrElse _
                   dictApps(Item.Identifier).AppleId.Length > 0 OrElse _
                   Item.TuiType = tbTuiType.TuiStore OrElse _
                   Item.TuiType = tbTuiType.TuiStoreHD) Then        '推壳显示在无需修改里面
                    Me.mDictNotNeedRepaid.Add(Item.Identifier, obj)
                    Continue For
                End If

                If Me.mRepairPaymentResult.RepairApps.ContainsKey(Item.Identifier) Then
                    obj.repairInfo = Me.mRepairPaymentResult.RepairApps(Item.Identifier)
                    If obj.repairInfo.Type = RepairType.CanRepair Then
                        Me.mDictCanRepaid.Add(Item.Identifier, obj)

                    ElseIf obj.repairInfo.Type = RepairType.NotRepair Then
                        Me.mDictCanNotRepaid.Add(Item.Identifier, obj)

                    Else
                        Me.mDictNotNeedRepaid.Add(Item.Identifier, obj)

                    End If

                Else
                    Me.mDictCanNotRepaid.Add(Item.Identifier, obj)

                End If
            Next

            '显示到界面
            Me.ShowDataToView()
        Catch ex As Exception
        End Try
    End Sub

    Private Sub SetRepairContralInit()
        Me.lblMessage.Text = ""
        Me.btnGetTwoYuan.Text = String.Format(Me.Language.GetString("Tools.Payment.Button.GetMoney"), Me.mRepairPaymentResult.Indemnification)
        If Me.mRepairPaymentResult.Indemnification <= 0 Then
            Me.btnGetTwoYuan.Enabled = False
        Else
            Me.btnGetTwoYuan.Enabled = True
        End If

        Me.dgvSoft.Rows.Clear()
        Me.dgvSoft.DictGroupKeys.Clear()
        Me.mDictGridRow.Clear()

        Try
            If Me.mThrGetMoney IsNot Nothing AndAlso Me.mThrGetMoney.ThreadState <> ThreadState.Stopped Then
                Me.mThrGetMoney.Abort()
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub ShowDataToViewHandler()
    Private Sub ShowDataToView()
        If Me.InvokeRequired Then
            Me.Invoke(New ShowDataToViewHandler(AddressOf ShowDataToView))
        Else
            Try
                If Me.mRepairPaymentResult Is Nothing OrElse Me.mRepairPaymentResult.Code <> 0 OrElse (Me.mDictCanRepaid.Count <= 0 AndAlso Me.mDictCanNotRepaid.Count <= 0) Then
                    Me.SetView(RepairPaymentStatus.NoRecord)
                Else
                    Me.SetRepairContralInit()
                    Dim strTitle As String = ""
                    Dim strRepairAll As String = ""
                    If Me.mType = RepairFormType.Repair Then
                        Me.pnlEmpty.SendToBack()
                        Me.SetView(RepairPaymentStatus.RecordRepair)
                        '无需修复
                        strTitle = Me.Language.GetString("Tools.Label.NotNeedRepair")                   '"无需修复"
                        Me.dgvSoft.AddGroup(strTitle, Me.dgvSoft.GroupRowTemplateArray, "")
                        For Each Item As RepairObj In Me.mDictNotNeedRepaid.Values
                            Dim strSize As String = Me.Language.GetString("Common.Label.Unknow")            '"未知"
                            If Not String.IsNullOrEmpty(Item.pkgInfo.Size) Then
                                strSize = Item.pkgInfo.Size
                            End If
                            Dim paraObj() As Object = New Object() {False, Item.pkgInfo.Name, False, Item.pkgInfo.Version, strSize, strTitle}
                            Dim row As tbDataGridViewRow = Me.dgvSoft.AddGroupItem(strTitle, paraObj)
                            row.Tag = Item
                            Me.SetRowRepairStatus(row, Item)
                            Me.SetCurrentRowStatus(row, AppIconStatus.Succeed, 0, strTitle)
                            Me.UpdateRowIcon(row, iPhoneDeviceHelper.GetDevicePNGData(Me.mDevice, Item.pkgInfo.Identifier, Item.pkgInfo.Version))
                        Next
                        strRepairAll = String.Format("{0}({1})", Me.Language.GetString("Welcome.Repair.Title"), 0)
                    Else
                        Me.SetView(RepairPaymentStatus.Record)
                    End If

                    '暂不能修复
                    strTitle = Me.Language.GetString("Tools.Label.NotRepair")   '"暂不能修复"
                    Me.dgvSoft.AddGroup(strTitle, Me.dgvSoft.GroupRowTemplateArray, "")
                    For Each Item As RepairObj In Me.mDictCanNotRepaid.Values
                        Dim strSize As String = Me.Language.GetString("Common.Label.Unknow")            '"未知"
                        If Not String.IsNullOrEmpty(Item.pkgInfo.Size) Then
                            strSize = Item.pkgInfo.Size
                        End If
                        Dim paraObj() As Object = New Object() {False, Item.pkgInfo.Name, False, Item.pkgInfo.Version, strSize, strTitle}
                        Dim row As tbDataGridViewRow = Me.dgvSoft.AddGroupItem(strTitle, paraObj)
                        row.Tag = Item
                        Me.SetRowRepairStatus(row, Item)
                        Me.SetCurrentRowStatus(row, AppIconStatus.Succeed, 0, strTitle)
                        Me.UpdateRowIcon(row, iPhoneDeviceHelper.GetDevicePNGData(Me.mDevice, Item.pkgInfo.Identifier, Item.pkgInfo.Version))
                    Next

                    strTitle = Me.Language.GetString("Tools.Payment.Label.FlashApp")                '"闪退的应用"
                    Me.dgvSoft.AddGroup(strTitle, Me.dgvSoft.GroupRowTemplateArray, strRepairAll)
                    For Each Item As RepairObj In Me.mDictCanRepaid.Values
                        Dim paraObj() As Object = New Object() {False, Item.repairInfo.Name, False, Item.repairInfo.Version, Utility.FormatFileSize(Item.repairInfo.Size), ""}
                        Dim row As tbDataGridViewRow = Me.dgvSoft.AddGroupItem(strTitle, paraObj)
                        row.Tag = Item

                        Me.SetRowRepairStatus(row, Item)
                        Me.mDictGridRow.Add(Item.repairInfo.Identifier, row)
                        Me.SetCurrentRowStatus(row, AppIconStatus.None)

                        Me.UpdateRowIcon(row, iPhoneDeviceHelper.GetDevicePNGData(Me.mDevice, Item.pkgInfo.Identifier, Item.pkgInfo.Version))
                        row.Selected = True  '默认选中
                    Next
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "frmRepairPayment_ShowDataToView")
            End Try
        End If
    End Sub

    Private Sub SetRowRepairStatus(ByVal row As tbDataGridViewRow, ByVal item As RepairObj)
        Dim cell As tbDataGridViewMediaCell = row.Cells("colStatus")
        If item.repairInfo IsNot Nothing AndAlso item.repairInfo.IsRepair Then
            cell.tbCellStatus = CellButtonStyle.Payment
        Else
            cell.tbCellStatus = CellButtonStyle.None
        End If
        cell.ToolTipText = ""
    End Sub

    Private Delegate Sub UpdateRowIconHandler(ByVal row As tbDataGridViewRow, ByVal appIcon As Image)
    Private Sub UpdateRowIcon(ByVal row As tbDataGridViewRow, ByVal appIcon As Image)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateRowIconHandler(AddressOf UpdateRowIcon), row, appIcon)
        Else
            Try
                Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                cellName.tbIconShadow = True
                If appIcon IsNot Nothing Then
                    cellName.tbIcon = appIcon.Clone()
                Else
                    cellName.tbIcon = My.Resources.app_icon_default.Clone
                End If
            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try
        End If

    End Sub

#End Region

#Region "----开始修复、暂停修复----"

    Private Sub DoRepair()
        Try
            For Each Item As tbDataGridViewRow In Me.dgvSoft.SelectedRows
                Me.RepairRow(Item)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmRepairPayment_Start")
        End Try
    End Sub

    Private Sub RepairRow(ByVal item As tbDataGridViewRow)
        If item.Tag Is Nothing OrElse _
           Not TypeOf item.Tag Is RepairObj OrElse _
           CType(item.Tag, RepairObj).repairInfo Is Nothing OrElse _
           CType(item.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair Then

            Return
        End If

        '执行修复操作，下载或者安装最新版的包
        Dim obj As RepairObj = item.Tag
        '设置状态
        '海外下载走苹果，如果用户点取消就不进行下载。
        '1.海外下载。
        '2.没有登陆苹果账号。
        Dim status As AppIconStatus = Me.GetCurrentRowStatus(item)
        If obj IsNot Nothing AndAlso obj.repairInfo IsNot Nothing AndAlso obj.repairInfo.IsAbroad AndAlso String.IsNullOrEmpty(IniSetting.GetDefaultUserAccount) Then
            Me.SetCurrentRowStatus(item, AppIconStatus.None)

        ElseIf status = AppIconStatus.None OrElse status = AppIconStatus.Failure Then
            Me.SetCurrentRowStatus(item, AppIconStatus.Waiting)

        End If

        If obj.repairInfo Is Nothing Then
            Return
        End If

        Dim itemDown As MultiThreadDownloadItem = Me.GetDownItem(obj.repairInfo.Identifier, obj.repairInfo.Itemid)

        If itemDown IsNot Nothing Then
            Dim pkgInfo As PackageInfo = PackageInfoReader.GetPackageInfo(itemDown.DownloadInfo.FilePath)
            If pkgInfo IsNot Nothing AndAlso File.Exists(itemDown.DownloadInfo.FilePath) Then
                Me.mInstallHelper.InstallApplication(pkgInfo)
            Else
                Me.mDownManage.Start(itemDown)
            End If
        Else
            Me.StarDownloadApp(obj)
        End If
    End Sub

    Private Sub StarDownloadApp(ByVal obj As RepairObj)
        Try
            Me.mQueueDownlaod.Enqueue(obj)

            If Me.mThrDownlaod IsNot Nothing AndAlso Me.mThrDownlaod.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mThrDownlaod = New Thread(AddressOf StarDownloadAppThread)

            With Me.mThrDownlaod
                .IsBackground = True
                .Start()
            End With

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_StarDownloadApp")
        End Try
    End Sub

    Private Sub StarDownloadAppThread()
        Try
            While Me.mQueueDownlaod.Count > 0
                Dim objDown As RepairObj = Me.mQueueDownlaod.Dequeue
                Me.DownloadApp(objDown.repairInfo.Url, objDown.repairInfo, objDown.pkgInfo)
                Utility.WaitSeconds(0.1)
            End While
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_StarDownloadAppThread")
        End Try
    End Sub

    '取得当前要下载的软件是不是在下载列表中
    Private Function GetDownItem(ByVal strIdentifier As String, ByVal strItemid As String)
        Dim lstDownload As List(Of MultiThreadDownloadItem) = Me.mDownManage.GetItems(ResourceClass.Software, ResourceType.IPA, TaskState.All)

        Dim itemDown As MultiThreadDownloadItem = Nothing
        For Each Item As MultiThreadDownloadItem In lstDownload
            If Item Is Nothing OrElse Item.ItemInfo Is Nothing OrElse Item.ItemInfo.Status = TaskState.Failed Then
                Continue For
            End If
            '苹果商店下载的loadid为空。所以无法比较
            If (strIdentifier.Length > 0 AndAlso Item.ItemInfo.Identifier = strIdentifier) OrElse _
                (strItemid.Length > 0 AndAlso Item.ItemInfo.ItemId = strItemid) Then
                itemDown = Item
                Exit For
            End If
        Next
        Return itemDown
    End Function

    Private Sub DoCancelRepair(ByVal item As tbDataGridViewRow)
        If item.Tag Is Nothing OrElse Not TypeOf item.Tag Is RepairObj Then
            Return
        End If
        Dim status As AppIconStatus = Me.GetCurrentRowStatus(item)
        Select Case status
            Case AppIconStatus.Repairing, AppIconStatus.Waiting
                Dim obj As RepairObj = item.Tag
                Dim itemDown As MultiThreadDownloadItem = Me.GetDownItem(obj.repairInfo.Identifier, obj.repairInfo.Itemid)
                If itemDown IsNot Nothing Then
                    Me.mDownManage.Stop(itemDown)
                    Dim pkgInfo As PackageInfo = PackageInfoReader.GetPackageInfo(itemDown.DownloadInfo.FilePath)
                    If pkgInfo IsNot Nothing Then
                        Me.mInstallHelper.CancelInstallApp(pkgInfo)
                    End If
                End If
        End Select

        Me.SetCurrentRowStatus(item, AppIconStatus.None)
    End Sub

#End Region

#Region "--- 下载ipa逻辑 ---"

    Private Sub DownloadApp(ByVal strUrl As String, ByVal repairInfo As RepairPackageInfo, ByVal pkgInfo As PackageInfo)
        Try
            Dim strItemId As String = repairInfo.Itemid
            If String.IsNullOrEmpty(strItemId) AndAlso pkgInfo IsNot Nothing Then
                strItemId = pkgInfo.ItemId
            End If
            WebSiteHelper.Instance().Start2AnalyseDownloadUrlByDownloadCenter(Me.mApplication, repairInfo.Url, repairInfo.Name, repairInfo.LoadId, strItemId, repairInfo.Version, False, repairInfo.IconUrl, Me.mDevice, False, "", repairInfo.Identifier, repairInfo.IsAbroad, repairInfo.ShowLogingAppleId)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_DownloadApp")
        End Try
    End Sub

    Private Sub DownloadIPA(ByVal url As String, _
                            ByVal repairInfo As RepairPackageInfo, _
                            Optional ByVal strItemId As String = "", _
                            Optional ByVal isNeedToBackInfo As Boolean = False)
        Try
            Dim info As New MultiThreadDownloadItemInfo
            With info
                .IsSingleThread = False

                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .Source = TaskSource.Tongbu
                .LoadId = repairInfo.LoadId
                .Url = url
                .ItemId = strItemId
                .IconUrl = repairInfo.IconUrl

                If isNeedToBackInfo Then
                    .UrlId = repairInfo.LoadId
                End If
                Dim strName As String = repairInfo.Name
                If strName.Length > 0 Then
                    If repairInfo.Version.Length > 0 Then
                        strName &= String.Format("_v{0}", repairInfo.Version)
                    End If
                    If strName.ToLower().EndsWith(".ipa") = False Then
                        strName &= ".ipa"
                    End If
                End If
                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If .Url.ToLower().Contains("tbtui") Then
                    .IsUpdate = True
                End If
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                    .Identifier = Me.mDevice.Identifier
                End If
                .Identifier = repairInfo.Identifier
            End With

            Me.mDownManage.NewTask(info)

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_DownloadIPA")
        End Try
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Delegate Sub SetViewHandler(ByVal status As RepairPaymentStatus)
    Private Sub SetView(ByVal status As RepairPaymentStatus)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewHandler(AddressOf SetView), status)
        Else
            Select Case status
                Case RepairPaymentStatus.Loading
                    Me.ShowPnlLoading()

                Case RepairPaymentStatus.Description
                    Me.pnlDescription.Visible = True
                    Me.pnlDescription.BringToFront()

                Case RepairPaymentStatus.NoRecord
                    Me.pnlNoRecord.Visible = True
                    Me.pnlNoRecord.BringToFront()

                Case RepairPaymentStatus.Record
                    Me.pnlRecord.Visible = True
                    Me.pnlRecord.BringToFront()

                Case RepairPaymentStatus.InstallTui
                    Me.pnlTui.Visible = True
                    Me.pnlTui.BringToFront()

                Case RepairPaymentStatus.RecordRepair
                    Me.pnlRecordRepair.Visible = True
                    Me.pnlRecordRepair.BringToFront()
                    Me.HidePnlLoading()
                    Me.pnlDescription.Visible = False
                    Me.pnlNoRecord.Visible = False
                    Me.pnlRecord.Visible = False
                    Me.pnlTui.Visible = False

            End Select
        End If
    End Sub

    Private Sub SetCurrentRowStatus(ByVal row As tbDataGridViewRow, ByVal status As AppIconStatus, Optional ByVal intProgress As Integer = 0, Optional ByVal strMessage As String = "")
        If row Is Nothing Then
            Return
        End If
        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        cellProgress.Tag = status
        Select Case status
            Case AppIconStatus.None
                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Button
                cellProgress.tbShowButtonFirst = True
                cellProgress.tbShowButtonSecond = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowCancelButton = False
                cellProgress.tbTextBackup = Me.Language.GetString("Tools.Button.Repair")        '"修复"

            Case AppIconStatus.Waiting
                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbShowButtonFirst = False
                cellProgress.tbShowButtonSecond = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowCancelButton = True
                cellProgress.tbText = Me.Language.GetString("Tools.Button.WaitingRepair")       '"等待修复"

            Case AppIconStatus.Downloading
                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbShowButtonFirst = False
                cellProgress.tbShowButtonSecond = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowCancelButton = True
                cellProgress.tbText = strMessage

            Case AppIconStatus.Repairing
                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Progress
                cellProgress.tbShowButtonFirst = False
                cellProgress.tbShowButtonSecond = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowCancelButton = True
                cellProgress.tbProgressValue = intProgress
                cellProgress.tbText = String.Format(Me.Language.GetString("Tools.Message.Repairing"), "")        '"修复中"

            Case AppIconStatus.Installing               '上传后正在安装的状态
                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.UnsureProgress
                cellProgress.tbShowButtonFirst = False
                cellProgress.tbShowButtonSecond = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowCancelButton = False
                cellProgress.tbProgressValue = intProgress
                cellProgress.tbText = String.Format(Me.Language.GetString("Tools.Message.Repairing"), "")        '"修复中"

            Case AppIconStatus.Succeed
                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbShowButtonFirst = False
                cellProgress.tbShowButtonSecond = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowCancelButton = False
                If String.IsNullOrEmpty(strMessage) Then
                    cellProgress.tbText = Me.Language.GetString("Tools.Button.RepairSucceed")               '"修复成功"
                Else
                    cellProgress.tbText = strMessage
                End If

            Case AppIconStatus.Failure
                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Failed
                cellProgress.tbShowButtonFirst = True
                cellProgress.tbShowButtonSecond = True
                cellProgress.tbShowButtonThird = True
                cellProgress.tbShowCancelButton = False
                cellProgress.tbText = Me.Language.GetString("Common.Retry")                                 '"重试"
                cellProgress.tbTextFail = strMessage

            Case InstallState.FailRetry, InstallState.UnSucceedCraked
                cellProgress.tbProgressStyle = ProgressStyle.OneRetryButton
                cellProgress.tbText = IIf(status = InstallState.FailRetry, Me.Language.GetString("Download.Button.Redownload"), Me.Language.GetString("Download.Button.Genuine")) '重新下载"下载正版"
                cellProgress.Enabled = True
                cellProgress.tbTextFail = IIf(status = InstallState.FailRetry, Me.Language.GetString("PkgInstaller.Label.InstallFailCompatible"), Me.Language.GetString("PkgInstaller.Label.UnSucceedCraked")) '"#安装失败,软件已过期" '"设备未越狱,无法安装此越狱包"args.ErrorMsg
                If cellProgress.tbText.Contains(cellProgress.tbTextFail) Then
                    cellProgress.tbTextFail = String.Empty
                End If
                cellProgress.tbShowCancelButton = False
                cellProgress.tbForColor = Color.Blue

        End Select
    End Sub

    Private Function GetCurrentRowStatus(ByVal row As tbDataGridViewRow) As AppIconStatus
        Dim status As AppIconStatus = AppIconStatus.None
        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        If cellProgress.Tag IsNot Nothing AndAlso TypeOf cellProgress.Tag Is AppIconStatus Then
            status = cellProgress.Tag
        End If
        Return status
    End Function

    Private Shared Function GetPersonalCenter(ByVal device As iPhoneDevice) As frmPersonalCenter
        Dim frm As frmPersonalCenter = Nothing
        For Each Item As Form In My.Application.OpenForms()
            If Item IsNot Nothing AndAlso TypeOf Item Is frmPersonalCenter AndAlso Item.Name.Contains("frmPersonalCenter") Then
                frm = Item
                Exit For
            End If
        Next
        Return frm
    End Function

    Public Shared Sub ShowPersonalCenter(ByVal owner As Form, ByVal app As IApplication, ByVal device As iPhoneDevice)
        Dim frm As frmPersonalCenter = GetPersonalCenter(device)

        If frm IsNot Nothing Then
            frm.Activate()
            frm.BringToFront()
            frm.WindowState = FormWindowState.Normal
            frm.Show()
        Else
            frm = New frmPersonalCenter(app, device)
            frm.Show(owner)
        End If
    End Sub

    Private Function GetRowByID(ByVal strIdnetify As String, ByVal strItemid As String) As tbDataGridViewRow
        Dim row As tbDataGridViewRow = Nothing
        Try
            Dim strTempIdnetify As String = ""
            For Each strKey As String In Me.mDictGridRow.Keys
                If strKey = strIdnetify OrElse strIdnetify.StartsWith(strKey) Then
                    strTempIdnetify = strKey
                    Exit For
                End If
            Next

            If Me.mDictGridRow.ContainsKey(strTempIdnetify) Then
                row = Me.mDictGridRow(strTempIdnetify)

            ElseIf strItemid.Length > 0 Then
                For Each Item As tbDataGridViewRow In Me.mDictGridRow.Values
                    If Item IsNot Nothing AndAlso Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is RepairObj Then
                        Dim obj As RepairObj = Item.Tag
                        If obj.repairInfo.Itemid = strItemid Then
                            row = Item
                            Exit For
                        ElseIf obj.repairInfo.PackageIdentifier = strIdnetify Then
                            row = Item
                            Exit For
                        End If
                    End If
                Next

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_GetRowByID")
        End Try
        Return row
    End Function

#End Region

#Region "--- 领取金额 ---"

    Private Sub GetMoney()
        Try
            If Me.mThrGetMoney IsNot Nothing AndAlso Me.mThrGetMoney.ThreadState <> ThreadState.Stopped Then
                Me.mThrGetMoney.Abort()
            End If

            Me.lblMessage.Text = Me.Language.GetString("Tools.Payment.Label.Receiving")         '"正在领取..."
            Me.btnGetTwoYuan.Enabled = False
            Me.mThrGetMoney = New Thread(AddressOf GetMoneyThread)
            With mThrGetMoney
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepairPayment_GetMoney")
        End Try
    End Sub

    Private Sub GetMoneyThread()
        Dim strResult As String = HtmlHelper.GetMoney(Me.mRepairPaymentResult.Session, Me.mDevice.SerialNumber, Me.mPluginLogin.Uid)
        Me.SetGetMoneyResult(strResult)
    End Sub

    Private Delegate Sub SetGetMoneyResultHandler(ByVal strResult As String)
    Private Sub SetGetMoneyResult(ByVal strResult As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetGetMoneyResultHandler(AddressOf SetGetMoneyResult), strResult)
        Else
            Select Case strResult
                Case "0"
                    Me.lblMessage.Text = Me.Language.GetString("Tools.Payment.Message.ReceiveSucceed")              '"领取成功，请在我的赔付金中查看。"
                    Me.btnGetTwoYuan.Enabled = False
                    Me.btnGetTwoYuan.Text = String.Format(Me.Language.GetString("Tools.Payment.Button.GetMoney"), 0)
                    Dim frm As frmPersonalCenter = GetPersonalCenter(Me.mDevice)
                    If frm IsNot Nothing Then
                        frm.RefreshUrl()
                    End If

                Case "10001"
                    Me.lblMessage.Text = Me.Language.GetString("Tools.Payment.Message.UserInfoError")               '"用户信息异常。"
                    Me.btnGetTwoYuan.Enabled = True

                Case "12001", "12002"
                    Me.lblMessage.Text = Me.Language.GetString("Tools.Payment.Message.ReceiveError")                '"领取失败，请重新检测后再领取。"
                    Me.btnGetTwoYuan.Enabled = True

                Case "12003"
                    Me.lblMessage.Text = Me.Language.GetString("Tools.Payment.Message.ReceiveZero")                 '"可领取金额为0"
                    Me.btnGetTwoYuan.Enabled = True

                Case Else
                    Me.lblMessage.Text = Me.Language.GetString("Tools.Payment.Message.ReceiveFailure")              '"领取失败"
                    Me.btnGetTwoYuan.Enabled = True

            End Select

        End If
    End Sub

#End Region

#Region "----软件查找----"

    Private Sub txtSearchApp_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChangedByTimer
        Dim ifShowIcon As Boolean = False

        For Each Item As tbDataGridViewRow In Me.dgvSoft.Rows
            If Item IsNot Nothing AndAlso _
               Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is RepairObj Then
                If Me.CheckInclude(Item, Me.txtSearch.Text) Then
                    Item.Visible = True
                    ifShowIcon = True
                Else
                    Item.Visible = False
                End If
            End If
        Next

        If ifShowIcon Then
            Me.pnlGridRepair.BringToFront()
        Else
            Me.pnlEmpty.BringToFront()
        End If
    End Sub

    Private Sub txtNameValue_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNameValue.TextChanged
        Me.txtSearch.Text = Me.txtNameValue.Text.Trim()
    End Sub

    Private Function CheckInclude(ByVal noteCurrent As tbDataGridViewRow, ByVal strSearch As String) As Boolean
        Dim blnReturn As Boolean = False

        strSearch = strSearch.Replace(" ", "").Replace("　", "").ToLower
        Dim strTipTest As String = CType(noteCurrent.Tag, RepairObj).pkgInfo.Name.ToLower().Replace(" ", "").Replace("　", "")

        '软件名称是不是是相符的
        If strTipTest.Contains(strSearch) Then
            blnReturn = True
            GoTo DO_EXIST

        End If

        '软件名称中拼音是不是是相符的
        Dim strPinYinSubject As String = PinYinClass.MakePinYin(strTipTest, PinYinOptions.FirstCharacterOnly)
        If strPinYinSubject.StartsWith(strSearch, StringComparison.OrdinalIgnoreCase) Then
            blnReturn = True
            GoTo DO_EXIST
        End If

        Dim strPinYin As String = PinYinClass.MakePinYin(strTipTest, PinYinOptions.Default)
        If strPinYin.StartsWith(strSearch, StringComparison.OrdinalIgnoreCase) Then
            blnReturn = True
            GoTo DO_EXIST
        End If

DO_EXIST:
        Return blnReturn
    End Function

#End Region

    Private Sub btnJailbreak_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAllRepair.Click
        For Each row As tbDataGridViewRow In Me.mDictGridRow.Values
            If Not row.Selected Then
                Continue For
            End If
            Me.RepairRow(row)
        Next
    End Sub

    Private Sub lblJaiBreak_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblJaiBreak.Click
        Try
            For Each Item As Form In Application.OpenForms
                If Item IsNot Nothing AndAlso TypeOf Item Is frmSummary AndAlso CType(Item, frmSummary).mDevice Is Me.mDevice Then
                    CType(Me.mApplication, MainForm).GotoItem(ActionFuncType.Welcome)
                    CType(Item, frmSummary).StartJailbreakTool()
                End If
            Next

            Me.WindowState = FormWindowState.Minimized
            If Me.Owner IsNot Nothing AndAlso TypeOf Me.Owner Is frmTools Then
                CType(Me.Owner, frmTools).WindowState = FormWindowState.Minimized
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_Jailbreak")
        End Try
    End Sub

    Private Sub lblJailbreak_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAllRepair.Resize, lblTipJaibreak.Resize, lblJaiBreak.Resize
        Me.btnAllRepair.Location = New Point(Me.pnlBottom.Width - Me.btnAllRepair.Width - 15, Me.btnAllRepair.Top)
        Me.lblTipJaibreak.Location = New Point(15, Me.lblTipJaibreak.Top)
        Me.lblJaiBreak.Location = New Point(Me.lblTipJaibreak.Right, Me.lblJaiBreak.Top)
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_state
        End If
    End Sub


End Class

Public Enum RepairPaymentStatus
    Loading
    Description
    NoRecord
    Record
    RecordRepair
    InstallTui
End Enum

Public Enum RepairFormType
    Repair
    RepairPayment
End Enum