﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSystemLog
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSystemLog))
        Me.tlpMain = New System.Windows.Forms.TableLayoutPanel
        Me.pnlContent = New tbPanel
        Me.rtbContent = New tbRichTextBox
        Me.cmsOperate = New tbContextMenuStrip(Me.components)
        Me.tsmiSelectAll = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiCopy = New System.Windows.Forms.ToolStripMenuItem
        Me.tss1 = New System.Windows.Forms.ToolStripSeparator
        Me.tsmiCleanAll = New System.Windows.Forms.ToolStripMenuItem
        Me.pnlTop = New tbPanel
        Me.btnCleanAll = New tbButton
        Me.btnExport = New tbButton
        Me.tlpMain.SuspendLayout()
        Me.pnlContent.SuspendLayout()
        Me.cmsOperate.SuspendLayout()
        Me.pnlTop.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'tlpMain
        '
        Me.tlpMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tlpMain.BackColor = System.Drawing.Color.Transparent
        Me.tlpMain.ColumnCount = 1
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.Controls.Add(Me.pnlContent, 0, 0)
        Me.tlpMain.Controls.Add(Me.pnlTop, 0, 1)
        Me.tlpMain.Location = New System.Drawing.Point(1, 32)
        Me.tlpMain.Margin = New System.Windows.Forms.Padding(0)
        Me.tlpMain.Name = "tlpMain"
        Me.tlpMain.RowCount = 2
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 54.0!))
        Me.tlpMain.Size = New System.Drawing.Size(797, 567)
        Me.tlpMain.TabIndex = 27
        '
        'pnlContent
        '
        Me.pnlContent.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(214, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.pnlContent.Controls.Add(Me.rtbContent)
        Me.pnlContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContent.Location = New System.Drawing.Point(0, 0)
        Me.pnlContent.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContent.Name = "pnlContent"
        Me.pnlContent.Size = New System.Drawing.Size(797, 513)
        Me.pnlContent.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlContent.TabIndex = 1
        Me.pnlContent.tbBackgroundImage = Nothing
        Me.pnlContent.tbShowWatermark = False
        Me.pnlContent.tbSplit = "0,0,0,0"
        Me.pnlContent.tbWatermark = Nothing
        Me.pnlContent.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContent.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'rtbContent
        '
        Me.rtbContent.BackColor = System.Drawing.Color.White
        Me.rtbContent.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtbContent.ContextMenuStrip = Me.cmsOperate
        Me.rtbContent.DetectUrls = False
        Me.rtbContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.rtbContent.Location = New System.Drawing.Point(0, 0)
        Me.rtbContent.Margin = New System.Windows.Forms.Padding(0)
        Me.rtbContent.Name = "rtbContent"
        Me.rtbContent.ReadOnly = True
        Me.rtbContent.ScrollBars = System.Windows.Forms.RichTextBoxScrollBars.ForcedVertical
        Me.rtbContent.Size = New System.Drawing.Size(797, 513)
        Me.rtbContent.TabIndex = 0
        Me.rtbContent.Text = ""
        '
        'cmsOperate
        '
        Me.cmsOperate.AccessibleDescription = "153x98"
        Me.cmsOperate.DropShadowEnabled = False
        Me.cmsOperate.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsOperate.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiSelectAll, Me.tsmiCopy, Me.tss1, Me.tsmiCleanAll})
        Me.cmsOperate.Name = "munOperate"
        Me.cmsOperate.Size = New System.Drawing.Size(95, 76)
        Me.cmsOperate.tbBackColor = System.Drawing.Color.White
        Me.cmsOperate.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsOperate.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsOperate.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiSelectAll
        '
        Me.tsmiSelectAll.Name = "tsmiSelectAll"
        Me.tsmiSelectAll.Size = New System.Drawing.Size(94, 22)
        Me.tsmiSelectAll.Text = "全选"
        '
        'tsmiCopy
        '
        Me.tsmiCopy.Name = "tsmiCopy"
        Me.tsmiCopy.Size = New System.Drawing.Size(94, 22)
        Me.tsmiCopy.Text = "复制"
        '
        'tss1
        '
        Me.tss1.Name = "tss1"
        Me.tss1.Size = New System.Drawing.Size(91, 6)
        '
        'tsmiCleanAll
        '
        Me.tsmiCleanAll.Name = "tsmiCleanAll"
        Me.tsmiCleanAll.Size = New System.Drawing.Size(94, 22)
        Me.tsmiCleanAll.Text = "清空"
        '
        'pnlTop
        '
        Me.pnlTop.BackColor = System.Drawing.Color.Transparent
        Me.pnlTop.Controls.Add(Me.btnCleanAll)
        Me.pnlTop.Controls.Add(Me.btnExport)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTop.Location = New System.Drawing.Point(0, 513)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(797, 54)
        Me.pnlTop.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 20
        Me.pnlTop.tbBackgroundImage = Nothing
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnCleanAll
        '
        Me.btnCleanAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnCleanAll.BackColor = System.Drawing.Color.Transparent
        Me.btnCleanAll.BindingForm = Nothing
        Me.btnCleanAll.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCleanAll.Location = New System.Drawing.Point(16, 16)
        Me.btnCleanAll.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnCleanAll.Name = "btnCleanAll"
        Me.btnCleanAll.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCleanAll.Selectable = True
        Me.btnCleanAll.Size = New System.Drawing.Size(70, 25)
        Me.btnCleanAll.TabIndex = 48
        Me.btnCleanAll.tbAdriftIconWhenHover = False
        Me.btnCleanAll.tbAutoSize = False
        Me.btnCleanAll.tbAutoSizeEx = True
        Me.btnCleanAll.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnCleanAll.tbBackgroundImageState = ImageState.FourState
        Me.btnCleanAll.tbBadgeNumber = 0
        Me.btnCleanAll.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCleanAll.tbEndEllipsis = False
        Me.btnCleanAll.tbIconHoldPlace = True
        Me.btnCleanAll.tbIconImage = Nothing
        Me.btnCleanAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCleanAll.tbIconImageState = ImageState.OneState
        Me.btnCleanAll.tbIconMore = False
        Me.btnCleanAll.tbIconMouseDown = Nothing
        Me.btnCleanAll.tbIconMouseHover = Nothing
        Me.btnCleanAll.tbIconMouseLeave = Nothing
        Me.btnCleanAll.tbIconPlaceText = 2
        Me.btnCleanAll.tbIconReadOnly = Nothing
        Me.btnCleanAll.tbImageMouseDown = Nothing
        Me.btnCleanAll.tbImageMouseHover = Nothing
        Me.btnCleanAll.tbImageMouseLeave = Nothing
        Me.btnCleanAll.tbReadOnly = False
        Me.btnCleanAll.tbReadOnlyText = False
        Me.btnCleanAll.tbShadow = False
        Me.btnCleanAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCleanAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCleanAll.tbShowNew = False
        Me.btnCleanAll.tbShowToolTipOnButton = False
        Me.btnCleanAll.tbSplit = "13,11,13,11"
        Me.btnCleanAll.tbText = "清除"
        Me.btnCleanAll.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCleanAll.tbTextColor = System.Drawing.Color.Black
        Me.btnCleanAll.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCleanAll.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCleanAll.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCleanAll.tbTextMouseDownPlace = 0
        Me.btnCleanAll.tbToolTip = ""
        Me.btnCleanAll.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCleanAll.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCleanAll.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnCleanAll.VisibleEx = True
        '
        'btnExport
        '
        Me.btnExport.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExport.Location = New System.Drawing.Point(707, 16)
        Me.btnExport.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(75, 25)
        Me.btnExport.TabIndex = 47
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = False
        Me.btnExport.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnExport.tbBackgroundImageState = ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = False
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Nothing
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbIconImageState = ImageState.OneState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 2
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "保存"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.White
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.White
        Me.btnExport.tbTextColorDown = System.Drawing.Color.White
        Me.btnExport.tbTextColorHover = System.Drawing.Color.White
        Me.btnExport.tbTextMouseDownPlace = 0
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnExport.VisibleEx = True
        '
        'frmSystemLog
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.tlpMain)
        Me.MinimumSize = New System.Drawing.Size(10, 85)
        Me.Name = "frmSystemLog"
        Me.tbGuiBackground = My.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,31,5,54"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "实时日志"
        Me.Controls.SetChildIndex(Me.tlpMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.tlpMain.ResumeLayout(False)
        Me.pnlContent.ResumeLayout(False)
        Me.cmsOperate.ResumeLayout(False)
        Me.pnlTop.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

    Protected WithEvents tlpMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents pnlContent As tbPanel
    Friend WithEvents rtbContent As tbRichTextBox
    Friend WithEvents cmsOperate As tbContextMenuStrip
    Friend WithEvents tsmiSelectAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiCopy As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tss1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiCleanAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnCleanAll As tbButton
    Friend WithEvents btnExport As tbButton
End Class
