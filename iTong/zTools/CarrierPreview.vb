﻿'Imports iTong.Components
Public Class CarrierPreview
    Inherits System.Windows.Forms.Control
    Private mText As String = ""
    Private mContextImage As Image = Nothing
    Private mIsFSO As Boolean = True
    Private mIsX2 As Boolean = True
    Private mTextFont As String = "黑体"
    Private mFsoBrush As SolidBrush = Nothing
    Private mTsBrush As SolidBrush = Nothing
    Private mFontFsoText As Font = Nothing
    Private mFontTsText As Font = Nothing
    Private mFontDefault As Font = Nothing

    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        Dim totalContextLength As Integer
        Dim offsetX2G As Integer
        Dim offsetY2G As Integer
        Dim offsetX3G As Integer
        Dim offsetY3G As Integer
        Dim offsetXContex As Integer
        Dim offsetYContex As Integer
        Dim rectContext As Rectangle
        'Dim fontText As Font = New Font(mTextFont, 9, FontStyle.Regular, GraphicsUnit.Point)
        Dim G2Image As Image
        Dim G3Image As Image


        If Me.mIsFSO Then
            G2Image = My.Resources.tool_2G_signal
            G3Image = My.Resources.tool_3G_signal
        Else
            G2Image = My.Resources.tool_2G_signal_light
            G3Image = My.Resources.tool_3G_signal_light
        End If

        offsetY2G = (Me.Height - G2Image.Height) / 2 + 2
        offsetY3G = (Me.Height - G3Image.Height) / 2

        MyBase.OnPaint(e)
        Me.DoubleBuffered = True
        e.Graphics.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        'e.Graphics.SmoothingMode = Drawing.Drawing2D.SmoothingMode.HighQuality
        e.Graphics.TextRenderingHint = Drawing.Text.TextRenderingHint.ClearTypeGridFit
        Dim sizeOfText As SizeF

        If mContextImage Is Nothing Then
            sizeOfText = e.Graphics.MeasureString(mText, mFontFsoText)
        Else
            sizeOfText = mContextImage.Size
            'sizeOfText.Width /= 2
            'sizeOfText.Height /= 2
            If Me.IsX2 Then
                sizeOfText.Width /= 2
                sizeOfText.Height /= 2
            End If
        End If

        totalContextLength = sizeOfText.Width + G2Image.Width + G3Image.Width + 12
        offsetX2G = 12 '(Me.Width - totalContextLength) / 2
        offsetXContex = offsetX2G + G2Image.Width + 3
        offsetYContex = (Me.Height - sizeOfText.Height) / 2
        offsetX3G = offsetXContex + sizeOfText.Width + 6

        rectContext = New Rectangle(offsetXContex, offsetYContex, sizeOfText.Width, sizeOfText.Height)
        GuiHelper.DrawImage(e.Graphics, New Rectangle(offsetX2G, offsetY2G, G2Image.Width, G2Image.Height), _
                New tbSplitStructure(1), G2Image, True, MouseStates.MouseLeave, ImageState.OneState)

        If Me.mContextImage Is Nothing Then
            If Me.mIsFSO Then
                e.Graphics.DrawString(Me.mText, mFontFsoText, mFsoBrush, rectContext.Left, rectContext.Top + 2)
            Else
                e.Graphics.DrawString(Me.mText, mFontTsText, mTsBrush, rectContext.Left, rectContext.Top + 2)
            End If
        Else
            e.Graphics.DrawImage(mContextImage, rectContext, New Rectangle(0, 0, mContextImage.Size.Width, mContextImage.Size.Height), GraphicsUnit.Pixel)
        End If

        GuiHelper.DrawImage(e.Graphics, New Rectangle(offsetX3G, offsetY3G, G3Image.Width, G3Image.Height), _
        New tbSplitStructure(1), G3Image, True, MouseStates.MouseLeave, ImageState.OneState)

    End Sub

    Public Property IsX2() As Boolean
        Get
            Return Me.mIsX2
        End Get
        Set(ByVal value As Boolean)
            mIsX2 = value
        End Set
    End Property

    Public Property CarrierText() As String
        Get
            Return mText
        End Get
        Set(ByVal value As String)
            mText = value
            Me.Invalidate()
        End Set
    End Property


    Public Property ContextImage() As Image
        Get
            Return mContextImage
        End Get
        Set(ByVal value As Image)
            mContextImage = value
            Me.Invalidate()
        End Set
    End Property

    Public Property IsFSO() As Boolean
        Get
            Return Me.mIsFSO
        End Get
        Set(ByVal value As Boolean)
            Me.mIsFSO = value
        End Set
    End Property

    Public Property TextFont() As String
        Get
            Return Me.mTextFont
        End Get
        Set(ByVal value As String)
            Me.mTextFont = value
            Me.mFontDefault.Dispose()
            Me.mFontFsoText.Dispose()
            Me.mFontTsText.Dispose()

            Try
                mFontDefault = New Font(Me.mTextFont, 11, System.Drawing.GraphicsUnit.Pixel)
                mFontFsoText = New Font(Me.mTextFont, 11, System.Drawing.GraphicsUnit.Pixel)
                mFontTsText = New Font(Me.mTextFont, 11, System.Drawing.GraphicsUnit.Pixel)
            Catch ex As Exception
                mFontDefault = New Font("宋体", 11, System.Drawing.GraphicsUnit.Pixel)
                mFontFsoText = New Font("宋体", 11, System.Drawing.GraphicsUnit.Pixel)
                mFontTsText = New Font("宋体", 11, System.Drawing.GraphicsUnit.Pixel)
            End Try
            Me.Invalidate()
        End Set
    End Property

    Public Sub New()
        mFsoBrush = New SolidBrush(Color.FromArgb(255, 255, 255, 255))
        mTsBrush = New SolidBrush(Color.FromArgb(255, 255, 254, 254))
        mFontFsoText = New Font("黑体", 11, System.Drawing.GraphicsUnit.Pixel)
        mFontTsText = New Font("黑体", 11, System.Drawing.GraphicsUnit.Pixel)
        mFontDefault = New Font("黑体", 11, System.Drawing.GraphicsUnit.Pixel)
    End Sub
End Class
