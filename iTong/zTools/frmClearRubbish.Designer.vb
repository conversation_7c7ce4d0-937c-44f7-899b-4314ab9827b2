﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmClearRubbish
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmClearRubbish))
        Me.btnScan = New iTong.Components.tbButton()
        Me.lblReScan = New System.Windows.Forms.LinkLabel()
        Me.pnlListData = New iTong.tbFlowLayoutPanel()
        Me.pnlHeader = New iTong.Components.tbPanel()
        Me.pnlScanFinish = New iTong.Components.tbPanel()
        Me.crlRubbishFound = New iTong.ClearRubbishFileSelectControl()
        Me.crlRubbishSelct = New iTong.ClearRubbishFileSelectControl()
        Me.picScanFinish = New iTong.Components.tbPictureBox()
        Me.btnClear = New iTong.Components.tbButton()
        Me.lblScanFinish_Describ = New iTong.Components.tbLabel()
        Me.pnlDescrib = New iTong.Components.tbPanel()
        Me.TbPictureBox2 = New iTong.Components.tbPictureBox()
        Me.lblDescrib = New iTong.Components.tbLabel()
        Me.lblSuguest = New iTong.Components.tbLabel()
        Me.pnlCleanning = New iTong.Components.tbPanel()
        Me.prgCleanProgress = New iTong.Components.tbControlBar()
        Me.btnStopClear = New iTong.Components.tbButton()
        Me.picCleanning = New iTong.Components.tbPictureBox()
        Me.lblCleanning = New iTong.Components.tbLabel()
        Me.pnlScanning = New iTong.Components.tbPanel()
        Me.prgScanProgress = New iTong.Components.tbControlBar()
        Me.ClearRubbishFileFoundControl1 = New iTong.ClearRubbishFileFoundControl()
        Me.btnStopScan = New iTong.Components.tbButton()
        Me.picScanning = New iTong.Components.tbPictureBox()
        Me.lblScanning = New iTong.Components.tbLabel()
        Me.pnlFunction = New iTong.Components.tbPanel()
        Me.pnlDataHeader = New iTong.Components.tbPanel()
        Me.lblRowHeaderDetail = New System.Windows.Forms.Label()
        Me.lblRowHeaderFileSize = New System.Windows.Forms.Label()
        Me.lblRowHeaderFileCount = New System.Windows.Forms.Label()
        Me.lblHeaderItemName = New System.Windows.Forms.Label()
        Me.chkSelectAll = New iTong.Components.tbCheckBox()
        Me.pnlReport = New iTong.Components.tbPanel()
        Me.TbPanel1 = New iTong.Components.tbPanel()
        Me.lblReportQQWeibo = New System.Windows.Forms.LinkLabel()
        Me.picQQWeiBo = New iTong.Components.tbPictureBox()
        Me.picReportResult = New iTong.Components.tbPictureBox()
        Me.lblReportWeibo = New System.Windows.Forms.LinkLabel()
        Me.picSinaWeiBo = New iTong.Components.tbPictureBox()
        Me.lblReportRescan = New System.Windows.Forms.LinkLabel()
        Me.btnResult = New iTong.Components.tbButton()
        Me.btnReportFinish = New iTong.Components.tbButton()
        Me.chkSharedWeibo = New iTong.Components.tbCheckBox()
        Me.TbPictureBox1 = New iTong.Components.tbPictureBox()
        Me.webFirstPage = New iTong.Components.tbWebBrowser()
        Me.pnlHeader.SuspendLayout()
        Me.pnlScanFinish.SuspendLayout()
        CType(Me.picScanFinish, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDescrib.SuspendLayout()
        CType(Me.TbPictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlCleanning.SuspendLayout()
        CType(Me.picCleanning, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlScanning.SuspendLayout()
        CType(Me.picScanning, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlFunction.SuspendLayout()
        Me.pnlDataHeader.SuspendLayout()
        Me.pnlReport.SuspendLayout()
        Me.TbPanel1.SuspendLayout()
        CType(Me.picQQWeiBo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picReportResult, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picSinaWeiBo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TbPictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(796, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(772, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(748, 0)
        '
        'btnScan
        '
        Me.btnScan.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScan.BackColor = System.Drawing.Color.Transparent
        Me.btnScan.BindingForm = Nothing
        Me.btnScan.Font = New System.Drawing.Font("宋体", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScan.Location = New System.Drawing.Point(639, 4)
        Me.btnScan.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnScan.Name = "btnScan"
        Me.btnScan.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScan.Selectable = True
        Me.btnScan.Size = New System.Drawing.Size(133, 47)
        Me.btnScan.TabIndex = 82
        Me.btnScan.tbAdriftIconWhenHover = False
        Me.btnScan.tbAutoSize = False
        Me.btnScan.tbAutoSizeEx = False
        Me.btnScan.tbBackgroundImage = Global.iTong.My.Resources.Resources.ljql_btn_03
        Me.btnScan.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnScan.tbBadgeNumber = 0
        Me.btnScan.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScan.tbEndEllipsis = False
        Me.btnScan.tbIconHoldPlace = True
        Me.btnScan.tbIconImage = Nothing
        Me.btnScan.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScan.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScan.tbIconMore = False
        Me.btnScan.tbIconMouseDown = Nothing
        Me.btnScan.tbIconMouseHover = Nothing
        Me.btnScan.tbIconMouseLeave = Nothing
        Me.btnScan.tbIconPlaceText = 2
        Me.btnScan.tbIconReadOnly = Nothing
        Me.btnScan.tbImageMouseDown = Nothing
        Me.btnScan.tbImageMouseHover = Nothing
        Me.btnScan.tbImageMouseLeave = Nothing
        Me.btnScan.tbProgressValue = 50
        Me.btnScan.tbReadOnly = False
        Me.btnScan.tbReadOnlyText = False
        Me.btnScan.tbShadow = False
        Me.btnScan.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnScan.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnScan.tbShowDot = False
        Me.btnScan.tbShowMoreIconImg = CType(resources.GetObject("btnScan.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnScan.tbShowNew = False
        Me.btnScan.tbShowProgress = False
        Me.btnScan.tbShowTip = True
        Me.btnScan.tbShowToolTipOnButton = False
        Me.btnScan.tbSplit = "13,11,13,11"
        Me.btnScan.tbText = "开始扫描"
        Me.btnScan.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScan.tbTextColor = System.Drawing.Color.White
        Me.btnScan.tbTextColorDisable = System.Drawing.Color.White
        Me.btnScan.tbTextColorDown = System.Drawing.Color.White
        Me.btnScan.tbTextColorHover = System.Drawing.Color.White
        Me.btnScan.tbTextMouseDownPlace = 0
        Me.btnScan.tbToolTip = ""
        Me.btnScan.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScan.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScan.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnScan.VisibleEx = True
        '
        'lblReScan
        '
        Me.lblReScan.AutoSize = True
        Me.lblReScan.BackColor = System.Drawing.Color.Transparent
        Me.lblReScan.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblReScan.LinkColor = System.Drawing.Color.FromArgb(CType(CType(4, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(203, Byte), Integer))
        Me.lblReScan.Location = New System.Drawing.Point(68, 34)
        Me.lblReScan.Name = "lblReScan"
        Me.lblReScan.Size = New System.Drawing.Size(53, 12)
        Me.lblReScan.TabIndex = 87
        Me.lblReScan.TabStop = True
        Me.lblReScan.Text = "重新扫描"
        '
        'pnlListData
        '
        Me.pnlListData.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlListData.AutoScroll = True
        Me.pnlListData.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlListData.FlowDirection = System.Windows.Forms.FlowDirection.TopDown
        Me.pnlListData.ForeColor = System.Drawing.Color.Black
        Me.pnlListData.Location = New System.Drawing.Point(0, 118)
        Me.pnlListData.Name = "pnlListData"
        Me.pnlListData.Size = New System.Drawing.Size(818, 440)
        Me.pnlListData.TabIndex = 88
        Me.pnlListData.WrapContents = False
        '
        'pnlHeader
        '
        Me.pnlHeader.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlHeader.Controls.Add(Me.pnlScanFinish)
        Me.pnlHeader.Controls.Add(Me.pnlDescrib)
        Me.pnlHeader.Controls.Add(Me.pnlCleanning)
        Me.pnlHeader.Controls.Add(Me.pnlScanning)
        Me.pnlHeader.Location = New System.Drawing.Point(0, 1)
        Me.pnlHeader.Name = "pnlHeader"
        Me.pnlHeader.Size = New System.Drawing.Size(817, 91)
        Me.pnlHeader.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlHeader.TabIndex = 89
        Me.pnlHeader.tbBackgroundImage = Nothing
        Me.pnlHeader.tbShowWatermark = False
        Me.pnlHeader.tbSplit = "0,0,0,0"
        Me.pnlHeader.tbWatermark = Nothing
        Me.pnlHeader.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlHeader.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlScanFinish
        '
        Me.pnlScanFinish.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlScanFinish.Controls.Add(Me.crlRubbishFound)
        Me.pnlScanFinish.Controls.Add(Me.crlRubbishSelct)
        Me.pnlScanFinish.Controls.Add(Me.picScanFinish)
        Me.pnlScanFinish.Controls.Add(Me.btnClear)
        Me.pnlScanFinish.Controls.Add(Me.lblReScan)
        Me.pnlScanFinish.Controls.Add(Me.lblScanFinish_Describ)
        Me.pnlScanFinish.Location = New System.Drawing.Point(11, 27)
        Me.pnlScanFinish.Name = "pnlScanFinish"
        Me.pnlScanFinish.Size = New System.Drawing.Size(798, 65)
        Me.pnlScanFinish.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlScanFinish.TabIndex = 95
        Me.pnlScanFinish.tbBackgroundImage = Nothing
        Me.pnlScanFinish.tbShowWatermark = False
        Me.pnlScanFinish.tbSplit = "0,0,0,0"
        Me.pnlScanFinish.tbWatermark = Nothing
        Me.pnlScanFinish.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlScanFinish.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'crlRubbishFound
        '
        Me.crlRubbishFound.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.crlRubbishFound.ColorNumber = System.Drawing.Color.FromArgb(CType(CType(214, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.crlRubbishFound.ColorText = System.Drawing.Color.Black
        Me.crlRubbishFound.FileSize = CType(0, Long)
        Me.crlRubbishFound.Location = New System.Drawing.Point(385, 7)
        Me.crlRubbishFound.Name = "crlRubbishFound"
        Me.crlRubbishFound.Size = New System.Drawing.Size(252, 21)
        Me.crlRubbishFound.StringHeader = "发现垃圾 :"
        Me.crlRubbishFound.TabIndex = 93
        Me.crlRubbishFound.Text = "ClearRubbishFileSelectControl1"
        '
        'crlRubbishSelct
        '
        Me.crlRubbishSelct.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.crlRubbishSelct.ColorNumber = System.Drawing.Color.FromArgb(CType(CType(214, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.crlRubbishSelct.ColorText = System.Drawing.Color.Black
        Me.crlRubbishSelct.FileSize = CType(1000, Long)
        Me.crlRubbishSelct.Location = New System.Drawing.Point(386, 30)
        Me.crlRubbishSelct.Name = "crlRubbishSelct"
        Me.crlRubbishSelct.Size = New System.Drawing.Size(252, 21)
        Me.crlRubbishSelct.StringHeader = "   已选择 :"
        Me.crlRubbishSelct.TabIndex = 92
        Me.crlRubbishSelct.Text = "ClearRubbishFileSelectControl1"
        '
        'picScanFinish
        '
        Me.picScanFinish.Image = Global.iTong.My.Resources.Resources.icon_warning1
        Me.picScanFinish.Location = New System.Drawing.Point(20, 10)
        Me.picScanFinish.Name = "picScanFinish"
        Me.picScanFinish.Size = New System.Drawing.Size(30, 35)
        Me.picScanFinish.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picScanFinish.TabIndex = 89
        Me.picScanFinish.TabStop = False
        Me.picScanFinish.tbAutoSize = False
        Me.picScanFinish.tbBackgroundImage = Nothing
        Me.picScanFinish.tbSplit = "0,0,0,0"
        '
        'btnClear
        '
        Me.btnClear.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClear.BackColor = System.Drawing.Color.Transparent
        Me.btnClear.BindingForm = Nothing
        Me.btnClear.Font = New System.Drawing.Font("宋体", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClear.Location = New System.Drawing.Point(639, 4)
        Me.btnClear.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnClear.Name = "btnClear"
        Me.btnClear.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnClear.Selectable = True
        Me.btnClear.Size = New System.Drawing.Size(133, 47)
        Me.btnClear.TabIndex = 82
        Me.btnClear.tbAdriftIconWhenHover = False
        Me.btnClear.tbAutoSize = False
        Me.btnClear.tbAutoSizeEx = False
        Me.btnClear.tbBackgroundImage = Global.iTong.My.Resources.Resources.ljql_btn_03
        Me.btnClear.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnClear.tbBadgeNumber = 0
        Me.btnClear.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClear.tbEndEllipsis = False
        Me.btnClear.tbIconHoldPlace = True
        Me.btnClear.tbIconImage = Nothing
        Me.btnClear.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClear.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClear.tbIconMore = False
        Me.btnClear.tbIconMouseDown = Nothing
        Me.btnClear.tbIconMouseHover = Nothing
        Me.btnClear.tbIconMouseLeave = Nothing
        Me.btnClear.tbIconPlaceText = 2
        Me.btnClear.tbIconReadOnly = Nothing
        Me.btnClear.tbImageMouseDown = Nothing
        Me.btnClear.tbImageMouseHover = Nothing
        Me.btnClear.tbImageMouseLeave = Nothing
        Me.btnClear.tbProgressValue = 50
        Me.btnClear.tbReadOnly = False
        Me.btnClear.tbReadOnlyText = False
        Me.btnClear.tbShadow = False
        Me.btnClear.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnClear.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnClear.tbShowDot = False
        Me.btnClear.tbShowMoreIconImg = CType(resources.GetObject("btnClear.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnClear.tbShowNew = False
        Me.btnClear.tbShowProgress = False
        Me.btnClear.tbShowTip = True
        Me.btnClear.tbShowToolTipOnButton = False
        Me.btnClear.tbSplit = "13,11,13,11"
        Me.btnClear.tbText = "开始清理"
        Me.btnClear.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClear.tbTextColor = System.Drawing.Color.White
        Me.btnClear.tbTextColorDisable = System.Drawing.Color.White
        Me.btnClear.tbTextColorDown = System.Drawing.Color.White
        Me.btnClear.tbTextColorHover = System.Drawing.Color.White
        Me.btnClear.tbTextMouseDownPlace = 0
        Me.btnClear.tbToolTip = ""
        Me.btnClear.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClear.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClear.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnClear.VisibleEx = True
        '
        'lblScanFinish_Describ
        '
        Me.lblScanFinish_Describ.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblScanFinish_Describ.Location = New System.Drawing.Point(66, 7)
        Me.lblScanFinish_Describ.Name = "lblScanFinish_Describ"
        Me.lblScanFinish_Describ.Size = New System.Drawing.Size(369, 23)
        Me.lblScanFinish_Describ.TabIndex = 88
        Me.lblScanFinish_Describ.tbAdriftWhenHover = False
        Me.lblScanFinish_Describ.tbAutoEllipsis = False
        Me.lblScanFinish_Describ.tbAutoSize = False
        Me.lblScanFinish_Describ.tbHideImage = False
        Me.lblScanFinish_Describ.tbIconImage = Nothing
        Me.lblScanFinish_Describ.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblScanFinish_Describ.tbIconPlaceText = 5
        Me.lblScanFinish_Describ.tbShadow = False
        Me.lblScanFinish_Describ.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblScanFinish_Describ.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblScanFinish_Describ.tbShowScrolling = False
        Me.lblScanFinish_Describ.Text = "扫描完成，共发现xxxMB垃圾"
        Me.lblScanFinish_Describ.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlDescrib
        '
        Me.pnlDescrib.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlDescrib.Controls.Add(Me.TbPictureBox2)
        Me.pnlDescrib.Controls.Add(Me.btnScan)
        Me.pnlDescrib.Controls.Add(Me.lblDescrib)
        Me.pnlDescrib.Controls.Add(Me.lblSuguest)
        Me.pnlDescrib.Location = New System.Drawing.Point(26, 16)
        Me.pnlDescrib.Name = "pnlDescrib"
        Me.pnlDescrib.Size = New System.Drawing.Size(798, 65)
        Me.pnlDescrib.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescrib.TabIndex = 91
        Me.pnlDescrib.tbBackgroundImage = Nothing
        Me.pnlDescrib.tbShowWatermark = False
        Me.pnlDescrib.tbSplit = "0,0,0,0"
        Me.pnlDescrib.tbWatermark = Nothing
        Me.pnlDescrib.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescrib.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPictureBox2
        '
        Me.TbPictureBox2.Image = Global.iTong.My.Resources.Resources.tool_clean_rubbish_icon
        Me.TbPictureBox2.Location = New System.Drawing.Point(20, 9)
        Me.TbPictureBox2.Name = "TbPictureBox2"
        Me.TbPictureBox2.Size = New System.Drawing.Size(30, 35)
        Me.TbPictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.TbPictureBox2.TabIndex = 89
        Me.TbPictureBox2.TabStop = False
        Me.TbPictureBox2.tbAutoSize = False
        Me.TbPictureBox2.tbBackgroundImage = Nothing
        Me.TbPictureBox2.tbSplit = "0,0,0,0"
        '
        'lblDescrib
        '
        Me.lblDescrib.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDescrib.Location = New System.Drawing.Point(65, 7)
        Me.lblDescrib.Name = "lblDescrib"
        Me.lblDescrib.Size = New System.Drawing.Size(500, 20)
        Me.lblDescrib.TabIndex = 88
        Me.lblDescrib.tbAdriftWhenHover = False
        Me.lblDescrib.tbAutoEllipsis = False
        Me.lblDescrib.tbAutoSize = False
        Me.lblDescrib.tbHideImage = False
        Me.lblDescrib.tbIconImage = Nothing
        Me.lblDescrib.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDescrib.tbIconPlaceText = 5
        Me.lblDescrib.tbShadow = False
        Me.lblDescrib.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDescrib.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDescrib.tbShowScrolling = False
        Me.lblDescrib.Text = "全面清理设备中的垃圾，合理利用有限的空间"
        Me.lblDescrib.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSuguest
        '
        Me.lblSuguest.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblSuguest.ForeColor = System.Drawing.Color.FromArgb(CType(CType(124, Byte), Integer), CType(CType(125, Byte), Integer), CType(CType(127, Byte), Integer))
        Me.lblSuguest.Location = New System.Drawing.Point(65, 30)
        Me.lblSuguest.Name = "lblSuguest"
        Me.lblSuguest.Size = New System.Drawing.Size(426, 16)
        Me.lblSuguest.TabIndex = 90
        Me.lblSuguest.tbAdriftWhenHover = False
        Me.lblSuguest.tbAutoEllipsis = False
        Me.lblSuguest.tbAutoSize = False
        Me.lblSuguest.tbHideImage = False
        Me.lblSuguest.tbIconImage = Nothing
        Me.lblSuguest.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSuguest.tbIconPlaceText = 5
        Me.lblSuguest.tbShadow = False
        Me.lblSuguest.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSuguest.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSuguest.tbShowScrolling = False
        Me.lblSuguest.Text = "每天清一清，手机更健康"
        Me.lblSuguest.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlCleanning
        '
        Me.pnlCleanning.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlCleanning.Controls.Add(Me.prgCleanProgress)
        Me.pnlCleanning.Controls.Add(Me.btnStopClear)
        Me.pnlCleanning.Controls.Add(Me.picCleanning)
        Me.pnlCleanning.Controls.Add(Me.lblCleanning)
        Me.pnlCleanning.Location = New System.Drawing.Point(71, 0)
        Me.pnlCleanning.Name = "pnlCleanning"
        Me.pnlCleanning.Size = New System.Drawing.Size(798, 65)
        Me.pnlCleanning.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlCleanning.TabIndex = 94
        Me.pnlCleanning.tbBackgroundImage = Nothing
        Me.pnlCleanning.tbShowWatermark = False
        Me.pnlCleanning.tbSplit = "0,0,0,0"
        Me.pnlCleanning.tbWatermark = Nothing
        Me.pnlCleanning.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlCleanning.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'prgCleanProgress
        '
        Me.prgCleanProgress.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.prgCleanProgress.BackColor = System.Drawing.Color.Transparent
        Me.prgCleanProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.prgCleanProgress.Location = New System.Drawing.Point(68, 31)
        Me.prgCleanProgress.Name = "prgCleanProgress"
        Me.prgCleanProgress.Size = New System.Drawing.Size(602, 23)
        Me.prgCleanProgress.TabIndex = 92
        Me.prgCleanProgress.tbBackgroundImage = CType(resources.GetObject("prgCleanProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.prgCleanProgress.tbCanDragValue = False
        Me.prgCleanProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.prgCleanProgress.tbDotImage = CType(resources.GetObject("prgCleanProgress.tbDotImage"), System.Drawing.Image)
        Me.prgCleanProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.prgCleanProgress.tbDotMouseDown = Nothing
        Me.prgCleanProgress.tbDotMouseHover = Nothing
        Me.prgCleanProgress.tbDotMouseLeave = Nothing
        Me.prgCleanProgress.tbDownloadImage = Global.iTong.My.Resources.Resources.jindu_bg_07
        Me.prgCleanProgress.tbDownMax = 100
        Me.prgCleanProgress.tbDownMin = 0
        Me.prgCleanProgress.tbDownValue = 0
        Me.prgCleanProgress.tbIsWaiting = False
        Me.prgCleanProgress.tbPlayImage = Global.iTong.My.Resources.Resources.jindu_07
        Me.prgCleanProgress.tbPlayMax = 100
        Me.prgCleanProgress.tbPlayMin = 0
        Me.prgCleanProgress.tbPlayValue = 50
        Me.prgCleanProgress.tbShowDot = False
        Me.prgCleanProgress.tbShowText = True
        Me.prgCleanProgress.tbSplit = "4,0,4,0"
        Me.prgCleanProgress.Text = "TbControlBar1"
        '
        'btnStopClear
        '
        Me.btnStopClear.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnStopClear.BackColor = System.Drawing.Color.Transparent
        Me.btnStopClear.BindingForm = Nothing
        Me.btnStopClear.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStopClear.Location = New System.Drawing.Point(681, 30)
        Me.btnStopClear.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnStopClear.Name = "btnStopClear"
        Me.btnStopClear.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStopClear.Selectable = True
        Me.btnStopClear.Size = New System.Drawing.Size(80, 25)
        Me.btnStopClear.TabIndex = 93
        Me.btnStopClear.tbAdriftIconWhenHover = False
        Me.btnStopClear.tbAutoSize = False
        Me.btnStopClear.tbAutoSizeEx = False
        Me.btnStopClear.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnStopClear.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStopClear.tbBadgeNumber = 0
        Me.btnStopClear.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStopClear.tbEndEllipsis = False
        Me.btnStopClear.tbIconHoldPlace = True
        Me.btnStopClear.tbIconImage = Nothing
        Me.btnStopClear.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStopClear.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStopClear.tbIconMore = False
        Me.btnStopClear.tbIconMouseDown = Nothing
        Me.btnStopClear.tbIconMouseHover = Nothing
        Me.btnStopClear.tbIconMouseLeave = Nothing
        Me.btnStopClear.tbIconPlaceText = 2
        Me.btnStopClear.tbIconReadOnly = Nothing
        Me.btnStopClear.tbImageMouseDown = Nothing
        Me.btnStopClear.tbImageMouseHover = Nothing
        Me.btnStopClear.tbImageMouseLeave = Nothing
        Me.btnStopClear.tbProgressValue = 50
        Me.btnStopClear.tbReadOnly = False
        Me.btnStopClear.tbReadOnlyText = False
        Me.btnStopClear.tbShadow = False
        Me.btnStopClear.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStopClear.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStopClear.tbShowDot = False
        Me.btnStopClear.tbShowMoreIconImg = CType(resources.GetObject("btnStopClear.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStopClear.tbShowNew = False
        Me.btnStopClear.tbShowProgress = False
        Me.btnStopClear.tbShowTip = True
        Me.btnStopClear.tbShowToolTipOnButton = False
        Me.btnStopClear.tbSplit = "13,11,13,11"
        Me.btnStopClear.tbText = "停止清理"
        Me.btnStopClear.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStopClear.tbTextColor = System.Drawing.Color.Black
        Me.btnStopClear.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnStopClear.tbTextColorDown = System.Drawing.Color.Black
        Me.btnStopClear.tbTextColorHover = System.Drawing.Color.Black
        Me.btnStopClear.tbTextMouseDownPlace = 0
        Me.btnStopClear.tbToolTip = ""
        Me.btnStopClear.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStopClear.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStopClear.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnStopClear.VisibleEx = True
        '
        'picCleanning
        '
        Me.picCleanning.Image = Global.iTong.My.Resources.Resources.tool_clean_rubbish_icon
        Me.picCleanning.Location = New System.Drawing.Point(20, 14)
        Me.picCleanning.Name = "picCleanning"
        Me.picCleanning.Size = New System.Drawing.Size(30, 35)
        Me.picCleanning.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picCleanning.TabIndex = 89
        Me.picCleanning.TabStop = False
        Me.picCleanning.tbAutoSize = False
        Me.picCleanning.tbBackgroundImage = Nothing
        Me.picCleanning.tbSplit = "0,0,0,0"
        '
        'lblCleanning
        '
        Me.lblCleanning.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblCleanning.ForeColor = System.Drawing.Color.Black
        Me.lblCleanning.Location = New System.Drawing.Point(67, 8)
        Me.lblCleanning.Name = "lblCleanning"
        Me.lblCleanning.Size = New System.Drawing.Size(586, 16)
        Me.lblCleanning.TabIndex = 90
        Me.lblCleanning.tbAdriftWhenHover = False
        Me.lblCleanning.tbAutoEllipsis = False
        Me.lblCleanning.tbAutoSize = False
        Me.lblCleanning.tbHideImage = False
        Me.lblCleanning.tbIconImage = Nothing
        Me.lblCleanning.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCleanning.tbIconPlaceText = 5
        Me.lblCleanning.tbShadow = False
        Me.lblCleanning.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCleanning.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCleanning.tbShowScrolling = False
        Me.lblCleanning.Text = "正在清理，请稍候..."
        Me.lblCleanning.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlScanning
        '
        Me.pnlScanning.Controls.Add(Me.prgScanProgress)
        Me.pnlScanning.Controls.Add(Me.ClearRubbishFileFoundControl1)
        Me.pnlScanning.Controls.Add(Me.btnStopScan)
        Me.pnlScanning.Controls.Add(Me.picScanning)
        Me.pnlScanning.Controls.Add(Me.lblScanning)
        Me.pnlScanning.Location = New System.Drawing.Point(54, 10)
        Me.pnlScanning.Name = "pnlScanning"
        Me.pnlScanning.Size = New System.Drawing.Size(798, 65)
        Me.pnlScanning.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlScanning.TabIndex = 93
        Me.pnlScanning.tbBackgroundImage = Nothing
        Me.pnlScanning.tbShowWatermark = False
        Me.pnlScanning.tbSplit = "0,0,0,0"
        Me.pnlScanning.tbWatermark = Nothing
        Me.pnlScanning.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlScanning.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'prgScanProgress
        '
        Me.prgScanProgress.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.prgScanProgress.BackColor = System.Drawing.Color.Transparent
        Me.prgScanProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.prgScanProgress.Location = New System.Drawing.Point(68, 31)
        Me.prgScanProgress.Name = "prgScanProgress"
        Me.prgScanProgress.Size = New System.Drawing.Size(602, 23)
        Me.prgScanProgress.TabIndex = 92
        Me.prgScanProgress.tbBackgroundImage = CType(resources.GetObject("prgScanProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.prgScanProgress.tbCanDragValue = False
        Me.prgScanProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.prgScanProgress.tbDotImage = CType(resources.GetObject("prgScanProgress.tbDotImage"), System.Drawing.Image)
        Me.prgScanProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.prgScanProgress.tbDotMouseDown = Nothing
        Me.prgScanProgress.tbDotMouseHover = Nothing
        Me.prgScanProgress.tbDotMouseLeave = Nothing
        Me.prgScanProgress.tbDownloadImage = Global.iTong.My.Resources.Resources.jindu_bg_07
        Me.prgScanProgress.tbDownMax = 100
        Me.prgScanProgress.tbDownMin = 0
        Me.prgScanProgress.tbDownValue = 0
        Me.prgScanProgress.tbIsWaiting = False
        Me.prgScanProgress.tbPlayImage = Global.iTong.My.Resources.Resources.jindu_07
        Me.prgScanProgress.tbPlayMax = 100
        Me.prgScanProgress.tbPlayMin = 0
        Me.prgScanProgress.tbPlayValue = 50
        Me.prgScanProgress.tbShowDot = False
        Me.prgScanProgress.tbShowText = True
        Me.prgScanProgress.tbSplit = "4,0,4,0"
        Me.prgScanProgress.Text = "TbControlBar1"
        '
        'ClearRubbishFileFoundControl1
        '
        Me.ClearRubbishFileFoundControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ClearRubbishFileFoundControl1.ColorNumber = System.Drawing.Color.FromArgb(CType(CType(214, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(77, Byte), Integer))
        Me.ClearRubbishFileFoundControl1.ColorText = System.Drawing.Color.Black
        Me.ClearRubbishFileFoundControl1.FileCount = 0
        Me.ClearRubbishFileFoundControl1.FileSize = CType(0, Long)
        Me.ClearRubbishFileFoundControl1.Location = New System.Drawing.Point(298, 6)
        Me.ClearRubbishFileFoundControl1.Name = "ClearRubbishFileFoundControl1"
        Me.ClearRubbishFileFoundControl1.Size = New System.Drawing.Size(449, 24)
        Me.ClearRubbishFileFoundControl1.StringHead = "已发现"
        Me.ClearRubbishFileFoundControl1.StringMiddle = "个垃圾文件，共占用"
        Me.ClearRubbishFileFoundControl1.TabIndex = 96
        Me.ClearRubbishFileFoundControl1.Text = "ClearRubbishFileFoundControl1"
        '
        'btnStopScan
        '
        Me.btnStopScan.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnStopScan.BackColor = System.Drawing.Color.Transparent
        Me.btnStopScan.BindingForm = Nothing
        Me.btnStopScan.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStopScan.Location = New System.Drawing.Point(681, 30)
        Me.btnStopScan.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnStopScan.Name = "btnStopScan"
        Me.btnStopScan.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStopScan.Selectable = True
        Me.btnStopScan.Size = New System.Drawing.Size(80, 25)
        Me.btnStopScan.TabIndex = 93
        Me.btnStopScan.tbAdriftIconWhenHover = False
        Me.btnStopScan.tbAutoSize = False
        Me.btnStopScan.tbAutoSizeEx = False
        Me.btnStopScan.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnStopScan.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStopScan.tbBadgeNumber = 0
        Me.btnStopScan.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStopScan.tbEndEllipsis = False
        Me.btnStopScan.tbIconHoldPlace = True
        Me.btnStopScan.tbIconImage = Nothing
        Me.btnStopScan.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStopScan.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStopScan.tbIconMore = False
        Me.btnStopScan.tbIconMouseDown = Nothing
        Me.btnStopScan.tbIconMouseHover = Nothing
        Me.btnStopScan.tbIconMouseLeave = Nothing
        Me.btnStopScan.tbIconPlaceText = 2
        Me.btnStopScan.tbIconReadOnly = Nothing
        Me.btnStopScan.tbImageMouseDown = Nothing
        Me.btnStopScan.tbImageMouseHover = Nothing
        Me.btnStopScan.tbImageMouseLeave = Nothing
        Me.btnStopScan.tbProgressValue = 50
        Me.btnStopScan.tbReadOnly = False
        Me.btnStopScan.tbReadOnlyText = False
        Me.btnStopScan.tbShadow = False
        Me.btnStopScan.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStopScan.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStopScan.tbShowDot = False
        Me.btnStopScan.tbShowMoreIconImg = CType(resources.GetObject("btnStopScan.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStopScan.tbShowNew = False
        Me.btnStopScan.tbShowProgress = False
        Me.btnStopScan.tbShowTip = True
        Me.btnStopScan.tbShowToolTipOnButton = False
        Me.btnStopScan.tbSplit = "13,11,13,11"
        Me.btnStopScan.tbText = "停止扫描"
        Me.btnStopScan.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStopScan.tbTextColor = System.Drawing.Color.Black
        Me.btnStopScan.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnStopScan.tbTextColorDown = System.Drawing.Color.Black
        Me.btnStopScan.tbTextColorHover = System.Drawing.Color.Black
        Me.btnStopScan.tbTextMouseDownPlace = 0
        Me.btnStopScan.tbToolTip = ""
        Me.btnStopScan.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStopScan.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStopScan.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnStopScan.VisibleEx = True
        '
        'picScanning
        '
        Me.picScanning.Image = Global.iTong.My.Resources.Resources.tool_clean_rubbish_icon
        Me.picScanning.Location = New System.Drawing.Point(20, 14)
        Me.picScanning.Name = "picScanning"
        Me.picScanning.Size = New System.Drawing.Size(30, 35)
        Me.picScanning.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picScanning.TabIndex = 89
        Me.picScanning.TabStop = False
        Me.picScanning.tbAutoSize = False
        Me.picScanning.tbBackgroundImage = Nothing
        Me.picScanning.tbSplit = "0,0,0,0"
        '
        'lblScanning
        '
        Me.lblScanning.Font = New System.Drawing.Font("宋体", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblScanning.ForeColor = System.Drawing.Color.Black
        Me.lblScanning.Location = New System.Drawing.Point(67, 10)
        Me.lblScanning.Name = "lblScanning"
        Me.lblScanning.Size = New System.Drawing.Size(225, 16)
        Me.lblScanning.TabIndex = 90
        Me.lblScanning.tbAdriftWhenHover = False
        Me.lblScanning.tbAutoEllipsis = False
        Me.lblScanning.tbAutoSize = False
        Me.lblScanning.tbHideImage = False
        Me.lblScanning.tbIconImage = Nothing
        Me.lblScanning.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblScanning.tbIconPlaceText = 5
        Me.lblScanning.tbShadow = False
        Me.lblScanning.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblScanning.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblScanning.tbShowScrolling = False
        Me.lblScanning.Text = "正在扫描，请稍候..."
        Me.lblScanning.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlFunction
        '
        Me.pnlFunction.Controls.Add(Me.pnlHeader)
        Me.pnlFunction.Controls.Add(Me.pnlDataHeader)
        Me.pnlFunction.Controls.Add(Me.pnlListData)
        Me.pnlFunction.Location = New System.Drawing.Point(1, 30)
        Me.pnlFunction.Name = "pnlFunction"
        Me.pnlFunction.Size = New System.Drawing.Size(818, 558)
        Me.pnlFunction.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFunction.TabIndex = 91
        Me.pnlFunction.tbBackgroundImage = Nothing
        Me.pnlFunction.tbShowWatermark = False
        Me.pnlFunction.tbSplit = "0,0,0,0"
        Me.pnlFunction.tbWatermark = Nothing
        Me.pnlFunction.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFunction.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlDataHeader
        '
        Me.pnlDataHeader.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlDataHeader.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlDataHeader.Controls.Add(Me.lblRowHeaderDetail)
        Me.pnlDataHeader.Controls.Add(Me.lblRowHeaderFileSize)
        Me.pnlDataHeader.Controls.Add(Me.lblRowHeaderFileCount)
        Me.pnlDataHeader.Controls.Add(Me.lblHeaderItemName)
        Me.pnlDataHeader.Controls.Add(Me.chkSelectAll)
        Me.pnlDataHeader.Location = New System.Drawing.Point(0, 92)
        Me.pnlDataHeader.Name = "pnlDataHeader"
        Me.pnlDataHeader.Size = New System.Drawing.Size(818, 27)
        Me.pnlDataHeader.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDataHeader.TabIndex = 92
        Me.pnlDataHeader.tbBackgroundImage = Global.iTong.My.Resources.Resources.tool_clean_rowheader
        Me.pnlDataHeader.tbShowWatermark = False
        Me.pnlDataHeader.tbSplit = "100,0,400,0"
        Me.pnlDataHeader.tbWatermark = Nothing
        Me.pnlDataHeader.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDataHeader.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblRowHeaderDetail
        '
        Me.lblRowHeaderDetail.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblRowHeaderDetail.BackColor = System.Drawing.Color.Transparent
        Me.lblRowHeaderDetail.Location = New System.Drawing.Point(728, 3)
        Me.lblRowHeaderDetail.Name = "lblRowHeaderDetail"
        Me.lblRowHeaderDetail.Size = New System.Drawing.Size(81, 20)
        Me.lblRowHeaderDetail.TabIndex = 95
        Me.lblRowHeaderDetail.Text = "详情"
        Me.lblRowHeaderDetail.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblRowHeaderFileSize
        '
        Me.lblRowHeaderFileSize.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblRowHeaderFileSize.BackColor = System.Drawing.Color.Transparent
        Me.lblRowHeaderFileSize.Location = New System.Drawing.Point(627, 3)
        Me.lblRowHeaderFileSize.Name = "lblRowHeaderFileSize"
        Me.lblRowHeaderFileSize.Size = New System.Drawing.Size(75, 20)
        Me.lblRowHeaderFileSize.TabIndex = 94
        Me.lblRowHeaderFileSize.Text = "文件大小"
        Me.lblRowHeaderFileSize.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblRowHeaderFileCount
        '
        Me.lblRowHeaderFileCount.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblRowHeaderFileCount.BackColor = System.Drawing.Color.Transparent
        Me.lblRowHeaderFileCount.Location = New System.Drawing.Point(509, 3)
        Me.lblRowHeaderFileCount.Name = "lblRowHeaderFileCount"
        Me.lblRowHeaderFileCount.Size = New System.Drawing.Size(98, 20)
        Me.lblRowHeaderFileCount.TabIndex = 93
        Me.lblRowHeaderFileCount.Text = "文件个数"
        Me.lblRowHeaderFileCount.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblHeaderItemName
        '
        Me.lblHeaderItemName.AutoSize = True
        Me.lblHeaderItemName.BackColor = System.Drawing.Color.Transparent
        Me.lblHeaderItemName.Location = New System.Drawing.Point(49, 7)
        Me.lblHeaderItemName.Name = "lblHeaderItemName"
        Me.lblHeaderItemName.Size = New System.Drawing.Size(53, 12)
        Me.lblHeaderItemName.TabIndex = 92
        Me.lblHeaderItemName.Text = "项目名称"
        '
        'chkSelectAll
        '
        Me.chkSelectAll.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkSelectAll.BackColor = System.Drawing.Color.FromArgb(CType(CType(204, Byte), Integer), CType(CType(214, Byte), Integer), CType(CType(229, Byte), Integer))
        Me.chkSelectAll.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.chkSelectAll.Location = New System.Drawing.Point(10, 4)
        Me.chkSelectAll.Name = "chkSelectAll"
        Me.chkSelectAll.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkSelectAll.Size = New System.Drawing.Size(22, 16)
        Me.chkSelectAll.TabIndex = 91
        Me.chkSelectAll.tbAdriftIconWhenHover = False
        Me.chkSelectAll.tbAutoSize = False
        Me.chkSelectAll.tbAutoSizeEx = False
        Me.chkSelectAll.tbIconChecked = CType(resources.GetObject("chkSelectAll.tbIconChecked"), System.Drawing.Image)
        Me.chkSelectAll.tbIconCheckedMouseDown = Nothing
        Me.chkSelectAll.tbIconCheckedMouseHover = Nothing
        Me.chkSelectAll.tbIconCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkSelectAll.tbIconHoldPlace = True
        Me.chkSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkSelectAll.tbIconIndeterminate = CType(resources.GetObject("chkSelectAll.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkSelectAll.tbIconIndeterminateMouseDown = Nothing
        Me.chkSelectAll.tbIconIndeterminateMouseHover = Nothing
        Me.chkSelectAll.tbIconIndeterminateMouseLeave = Nothing
        Me.chkSelectAll.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkSelectAll.tbIconPlaceText = 1
        Me.chkSelectAll.tbIconUnChecked = CType(resources.GetObject("chkSelectAll.tbIconUnChecked"), System.Drawing.Image)
        Me.chkSelectAll.tbIconUnCheckedMouseDown = Nothing
        Me.chkSelectAll.tbIconUnCheckedMouseHover = Nothing
        Me.chkSelectAll.tbIconUnCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkSelectAll.tbImageBackground = Nothing
        Me.chkSelectAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkSelectAll.tbImageCheckedMouseDown = Nothing
        Me.chkSelectAll.tbImageCheckedMouseHover = Nothing
        Me.chkSelectAll.tbImageCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbImageUnCheckedMouseDown = Nothing
        Me.chkSelectAll.tbImageUnCheckedMouseHover = Nothing
        Me.chkSelectAll.tbImageUnCheckedMouseLeave = Nothing
        Me.chkSelectAll.tbReadOnly = False
        Me.chkSelectAll.tbShadow = False
        Me.chkSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkSelectAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkSelectAll.tbSplit = "3,3,3,3"
        Me.chkSelectAll.tbToolTip = ""
        Me.chkSelectAll.Text = " "
        Me.chkSelectAll.UseVisualStyleBackColor = False
        '
        'pnlReport
        '
        Me.pnlReport.BackColor = System.Drawing.Color.FromArgb(CType(CType(238, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlReport.Controls.Add(Me.TbPanel1)
        Me.pnlReport.Controls.Add(Me.btnReportFinish)
        Me.pnlReport.Controls.Add(Me.chkSharedWeibo)
        Me.pnlReport.Controls.Add(Me.TbPictureBox1)
        Me.pnlReport.Location = New System.Drawing.Point(1, 30)
        Me.pnlReport.Name = "pnlReport"
        Me.pnlReport.Size = New System.Drawing.Size(818, 558)
        Me.pnlReport.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlReport.TabIndex = 92
        Me.pnlReport.tbBackgroundImage = Nothing
        Me.pnlReport.tbShowWatermark = False
        Me.pnlReport.tbSplit = "0,0,0,0"
        Me.pnlReport.tbWatermark = Nothing
        Me.pnlReport.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlReport.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel1
        '
        Me.TbPanel1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.TbPanel1.Controls.Add(Me.lblReportQQWeibo)
        Me.TbPanel1.Controls.Add(Me.picQQWeiBo)
        Me.TbPanel1.Controls.Add(Me.picReportResult)
        Me.TbPanel1.Controls.Add(Me.lblReportWeibo)
        Me.TbPanel1.Controls.Add(Me.picSinaWeiBo)
        Me.TbPanel1.Controls.Add(Me.lblReportRescan)
        Me.TbPanel1.Controls.Add(Me.btnResult)
        Me.TbPanel1.Location = New System.Drawing.Point(118, 68)
        Me.TbPanel1.Name = "TbPanel1"
        Me.TbPanel1.Size = New System.Drawing.Size(582, 422)
        Me.TbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel1.TabIndex = 110
        Me.TbPanel1.tbBackgroundImage = Nothing
        Me.TbPanel1.tbShowWatermark = False
        Me.TbPanel1.tbSplit = "0,0,0,0"
        Me.TbPanel1.tbWatermark = Nothing
        Me.TbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblReportQQWeibo
        '
        Me.lblReportQQWeibo.AutoSize = True
        Me.lblReportQQWeibo.BackColor = System.Drawing.Color.Transparent
        Me.lblReportQQWeibo.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblReportQQWeibo.LinkColor = System.Drawing.Color.FromArgb(CType(CType(4, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(203, Byte), Integer))
        Me.lblReportQQWeibo.Location = New System.Drawing.Point(387, 373)
        Me.lblReportQQWeibo.Name = "lblReportQQWeibo"
        Me.lblReportQQWeibo.Size = New System.Drawing.Size(89, 12)
        Me.lblReportQQWeibo.TabIndex = 112
        Me.lblReportQQWeibo.TabStop = True
        Me.lblReportQQWeibo.Text = "分享到新浪微博"
        '
        'picQQWeiBo
        '
        Me.picQQWeiBo.Image = Global.iTong.My.Resources.Resources.weibo_tecent32
        Me.picQQWeiBo.Location = New System.Drawing.Point(344, 362)
        Me.picQQWeiBo.Name = "picQQWeiBo"
        Me.picQQWeiBo.Size = New System.Drawing.Size(40, 35)
        Me.picQQWeiBo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picQQWeiBo.TabIndex = 111
        Me.picQQWeiBo.TabStop = False
        Me.picQQWeiBo.tbAutoSize = False
        Me.picQQWeiBo.tbBackgroundImage = Nothing
        Me.picQQWeiBo.tbSplit = "0,0,0,0"
        '
        'picReportResult
        '
        Me.picReportResult.Image = Global.iTong.My.Resources.Resources.weibo_sina_
        Me.picReportResult.Location = New System.Drawing.Point(91, 4)
        Me.picReportResult.Name = "picReportResult"
        Me.picReportResult.Size = New System.Drawing.Size(400, 240)
        Me.picReportResult.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picReportResult.TabIndex = 109
        Me.picReportResult.TabStop = False
        Me.picReportResult.tbAutoSize = False
        Me.picReportResult.tbBackgroundImage = Nothing
        Me.picReportResult.tbSplit = "0,0,0,0"
        '
        'lblReportWeibo
        '
        Me.lblReportWeibo.AutoSize = True
        Me.lblReportWeibo.BackColor = System.Drawing.Color.Transparent
        Me.lblReportWeibo.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblReportWeibo.LinkColor = System.Drawing.Color.FromArgb(CType(CType(4, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(203, Byte), Integer))
        Me.lblReportWeibo.Location = New System.Drawing.Point(150, 373)
        Me.lblReportWeibo.Name = "lblReportWeibo"
        Me.lblReportWeibo.Size = New System.Drawing.Size(89, 12)
        Me.lblReportWeibo.TabIndex = 108
        Me.lblReportWeibo.TabStop = True
        Me.lblReportWeibo.Text = "分享到新浪微博"
        '
        'picSinaWeiBo
        '
        Me.picSinaWeiBo.Image = Global.iTong.My.Resources.Resources.weibo_sina32
        Me.picSinaWeiBo.Location = New System.Drawing.Point(107, 362)
        Me.picSinaWeiBo.Name = "picSinaWeiBo"
        Me.picSinaWeiBo.Size = New System.Drawing.Size(40, 35)
        Me.picSinaWeiBo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picSinaWeiBo.TabIndex = 107
        Me.picSinaWeiBo.TabStop = False
        Me.picSinaWeiBo.tbAutoSize = False
        Me.picSinaWeiBo.tbBackgroundImage = Nothing
        Me.picSinaWeiBo.tbSplit = "0,0,0,0"
        '
        'lblReportRescan
        '
        Me.lblReportRescan.BackColor = System.Drawing.Color.Transparent
        Me.lblReportRescan.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblReportRescan.LinkColor = System.Drawing.Color.FromArgb(CType(CType(4, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(203, Byte), Integer))
        Me.lblReportRescan.Location = New System.Drawing.Point(308, 288)
        Me.lblReportRescan.Name = "lblReportRescan"
        Me.lblReportRescan.Size = New System.Drawing.Size(105, 23)
        Me.lblReportRescan.TabIndex = 93
        Me.lblReportRescan.TabStop = True
        Me.lblReportRescan.Text = "重新扫描"
        Me.lblReportRescan.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnResult
        '
        Me.btnResult.BackColor = System.Drawing.Color.Transparent
        Me.btnResult.BindingForm = Nothing
        Me.btnResult.Font = New System.Drawing.Font("宋体", 15.75!)
        Me.btnResult.Location = New System.Drawing.Point(144, 279)
        Me.btnResult.Name = "btnResult"
        Me.btnResult.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnResult.Selectable = True
        Me.btnResult.Size = New System.Drawing.Size(387, 72)
        Me.btnResult.TabIndex = 113
        Me.btnResult.tbAdriftIconWhenHover = False
        Me.btnResult.tbAutoSize = False
        Me.btnResult.tbAutoSizeEx = False
        Me.btnResult.tbBackgroundImage = Nothing
        Me.btnResult.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnResult.tbBadgeNumber = 0
        Me.btnResult.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnResult.tbEndEllipsis = False
        Me.btnResult.tbIconHoldPlace = True
        Me.btnResult.tbIconImage = Global.iTong.My.Resources.Resources.icon_ok
        Me.btnResult.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnResult.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnResult.tbIconMore = False
        Me.btnResult.tbIconMouseDown = Nothing
        Me.btnResult.tbIconMouseHover = Nothing
        Me.btnResult.tbIconMouseLeave = Nothing
        Me.btnResult.tbIconPlaceText = 2
        Me.btnResult.tbIconReadOnly = Nothing
        Me.btnResult.tbImageMouseDown = Nothing
        Me.btnResult.tbImageMouseHover = Nothing
        Me.btnResult.tbImageMouseLeave = Nothing
        Me.btnResult.tbProgressValue = 50
        Me.btnResult.tbReadOnly = False
        Me.btnResult.tbReadOnlyText = False
        Me.btnResult.tbShadow = False
        Me.btnResult.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnResult.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnResult.tbShowDot = False
        Me.btnResult.tbShowMoreIconImg = CType(resources.GetObject("btnResult.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnResult.tbShowNew = False
        Me.btnResult.tbShowProgress = False
        Me.btnResult.tbShowTip = True
        Me.btnResult.tbShowToolTipOnButton = True
        Me.btnResult.tbSplit = "3,3,3,3"
        Me.btnResult.tbText = "清理完成"
        Me.btnResult.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnResult.tbTextColor = System.Drawing.Color.Black
        Me.btnResult.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnResult.tbTextColorDown = System.Drawing.Color.Black
        Me.btnResult.tbTextColorHover = System.Drawing.Color.Black
        Me.btnResult.tbTextMouseDownPlace = 0
        Me.btnResult.tbToolTip = "建议您经常清理垃圾，保持设备运行顺畅。"
        Me.btnResult.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnResult.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnResult.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnResult.VisibleEx = True
        '
        'btnReportFinish
        '
        Me.btnReportFinish.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnReportFinish.BackColor = System.Drawing.Color.Transparent
        Me.btnReportFinish.BindingForm = Nothing
        Me.btnReportFinish.Font = New System.Drawing.Font("宋体", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReportFinish.Location = New System.Drawing.Point(665, 508)
        Me.btnReportFinish.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnReportFinish.Name = "btnReportFinish"
        Me.btnReportFinish.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnReportFinish.Selectable = True
        Me.btnReportFinish.Size = New System.Drawing.Size(118, 47)
        Me.btnReportFinish.TabIndex = 91
        Me.btnReportFinish.tbAdriftIconWhenHover = False
        Me.btnReportFinish.tbAutoSize = False
        Me.btnReportFinish.tbAutoSizeEx = False
        Me.btnReportFinish.tbBackgroundImage = Global.iTong.My.Resources.Resources.ljql_btn_03
        Me.btnReportFinish.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnReportFinish.tbBadgeNumber = 0
        Me.btnReportFinish.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReportFinish.tbEndEllipsis = False
        Me.btnReportFinish.tbIconHoldPlace = True
        Me.btnReportFinish.tbIconImage = Nothing
        Me.btnReportFinish.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReportFinish.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReportFinish.tbIconMore = False
        Me.btnReportFinish.tbIconMouseDown = Nothing
        Me.btnReportFinish.tbIconMouseHover = Nothing
        Me.btnReportFinish.tbIconMouseLeave = Nothing
        Me.btnReportFinish.tbIconPlaceText = 2
        Me.btnReportFinish.tbIconReadOnly = Nothing
        Me.btnReportFinish.tbImageMouseDown = Nothing
        Me.btnReportFinish.tbImageMouseHover = Nothing
        Me.btnReportFinish.tbImageMouseLeave = Nothing
        Me.btnReportFinish.tbProgressValue = 50
        Me.btnReportFinish.tbReadOnly = False
        Me.btnReportFinish.tbReadOnlyText = False
        Me.btnReportFinish.tbShadow = False
        Me.btnReportFinish.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReportFinish.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReportFinish.tbShowDot = False
        Me.btnReportFinish.tbShowMoreIconImg = CType(resources.GetObject("btnReportFinish.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReportFinish.tbShowNew = False
        Me.btnReportFinish.tbShowProgress = False
        Me.btnReportFinish.tbShowTip = True
        Me.btnReportFinish.tbShowToolTipOnButton = False
        Me.btnReportFinish.tbSplit = "13,11,13,11"
        Me.btnReportFinish.tbText = "完成"
        Me.btnReportFinish.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReportFinish.tbTextColor = System.Drawing.Color.White
        Me.btnReportFinish.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReportFinish.tbTextColorDown = System.Drawing.Color.White
        Me.btnReportFinish.tbTextColorHover = System.Drawing.Color.White
        Me.btnReportFinish.tbTextMouseDownPlace = 0
        Me.btnReportFinish.tbToolTip = ""
        Me.btnReportFinish.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReportFinish.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReportFinish.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnReportFinish.Visible = False
        Me.btnReportFinish.VisibleEx = True
        '
        'chkSharedWeibo
        '
        Me.chkSharedWeibo.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkSharedWeibo.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.chkSharedWeibo.Location = New System.Drawing.Point(752, 441)
        Me.chkSharedWeibo.Name = "chkSharedWeibo"
        Me.chkSharedWeibo.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkSharedWeibo.Size = New System.Drawing.Size(21, 22)
        Me.chkSharedWeibo.TabIndex = 110
        Me.chkSharedWeibo.tbAdriftIconWhenHover = False
        Me.chkSharedWeibo.tbAutoSize = False
        Me.chkSharedWeibo.tbAutoSizeEx = False
        Me.chkSharedWeibo.tbIconChecked = CType(resources.GetObject("chkSharedWeibo.tbIconChecked"), System.Drawing.Image)
        Me.chkSharedWeibo.tbIconCheckedMouseDown = Nothing
        Me.chkSharedWeibo.tbIconCheckedMouseHover = Nothing
        Me.chkSharedWeibo.tbIconCheckedMouseLeave = Nothing
        Me.chkSharedWeibo.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkSharedWeibo.tbIconHoldPlace = True
        Me.chkSharedWeibo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkSharedWeibo.tbIconIndeterminate = CType(resources.GetObject("chkSharedWeibo.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkSharedWeibo.tbIconIndeterminateMouseDown = Nothing
        Me.chkSharedWeibo.tbIconIndeterminateMouseHover = Nothing
        Me.chkSharedWeibo.tbIconIndeterminateMouseLeave = Nothing
        Me.chkSharedWeibo.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkSharedWeibo.tbIconPlaceText = 1
        Me.chkSharedWeibo.tbIconUnChecked = CType(resources.GetObject("chkSharedWeibo.tbIconUnChecked"), System.Drawing.Image)
        Me.chkSharedWeibo.tbIconUnCheckedMouseDown = Nothing
        Me.chkSharedWeibo.tbIconUnCheckedMouseHover = Nothing
        Me.chkSharedWeibo.tbIconUnCheckedMouseLeave = Nothing
        Me.chkSharedWeibo.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkSharedWeibo.tbImageBackground = Nothing
        Me.chkSharedWeibo.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkSharedWeibo.tbImageCheckedMouseDown = Nothing
        Me.chkSharedWeibo.tbImageCheckedMouseHover = Nothing
        Me.chkSharedWeibo.tbImageCheckedMouseLeave = Nothing
        Me.chkSharedWeibo.tbImageUnCheckedMouseDown = Nothing
        Me.chkSharedWeibo.tbImageUnCheckedMouseHover = Nothing
        Me.chkSharedWeibo.tbImageUnCheckedMouseLeave = Nothing
        Me.chkSharedWeibo.tbReadOnly = False
        Me.chkSharedWeibo.tbShadow = False
        Me.chkSharedWeibo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkSharedWeibo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkSharedWeibo.tbSplit = "3,3,3,3"
        Me.chkSharedWeibo.tbToolTip = ""
        Me.chkSharedWeibo.Text = " "
        Me.chkSharedWeibo.UseVisualStyleBackColor = True
        Me.chkSharedWeibo.Visible = False
        '
        'TbPictureBox1
        '
        Me.TbPictureBox1.Image = Global.iTong.My.Resources.Resources.verison_split
        Me.TbPictureBox1.Location = New System.Drawing.Point(749, 431)
        Me.TbPictureBox1.Name = "TbPictureBox1"
        Me.TbPictureBox1.Size = New System.Drawing.Size(2, 51)
        Me.TbPictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.TbPictureBox1.TabIndex = 109
        Me.TbPictureBox1.TabStop = False
        Me.TbPictureBox1.tbAutoSize = False
        Me.TbPictureBox1.tbBackgroundImage = Nothing
        Me.TbPictureBox1.tbSplit = "0,0,0,0"
        Me.TbPictureBox1.Visible = False
        '
        'webFirstPage
        '
        Me.webFirstPage.AllowWebBrowserDrop = False
        Me.webFirstPage.Client = ""
        Me.webFirstPage.DeviceList = Nothing
        Me.webFirstPage.IsConnected = False
        Me.webFirstPage.IsWebBrowserContextMenuEnabled = False
        Me.webFirstPage.Jailbreaked = False
        Me.webFirstPage.Location = New System.Drawing.Point(1, 30)
        Me.webFirstPage.MinimumSize = New System.Drawing.Size(20, 20)
        Me.webFirstPage.Name = "webFirstPage"
        Me.webFirstPage.ScrollBarsEnabled = False
        Me.webFirstPage.Size = New System.Drawing.Size(818, 557)
        Me.webFirstPage.SN = ""
        Me.webFirstPage.TabIndex = 111
        Me.webFirstPage.Url = New System.Uri("C:\Users\<USER>\Documents\Tongbu\Cache\RubbishClearWebPage\welcome2.html", System.UriKind.Absolute)
        Me.webFirstPage.UserInfo = ""
        Me.webFirstPage.UserInfoEncode = ""
        '
        'frmClearRubbish
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.AutoSetFont = False
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(820, 615)
        Me.Controls.Add(Me.pnlReport)
        Me.Controls.Add(Me.pnlFunction)
        Me.Controls.Add(Me.webFirstPage)
        Me.MinimumSize = New System.Drawing.Size(40, 66)
        Me.Name = "frmClearRubbish"
        Me.tbAutoSetFormSize = True
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_state
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "20,33,20,33"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "垃圾清理"
        Me.Controls.SetChildIndex(Me.webFirstPage, 0)
        Me.Controls.SetChildIndex(Me.pnlFunction, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlReport, 0)
        Me.pnlHeader.ResumeLayout(False)
        Me.pnlScanFinish.ResumeLayout(False)
        Me.pnlScanFinish.PerformLayout()
        CType(Me.picScanFinish, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDescrib.ResumeLayout(False)
        CType(Me.TbPictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlCleanning.ResumeLayout(False)
        CType(Me.picCleanning, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlScanning.ResumeLayout(False)
        CType(Me.picScanning, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlFunction.ResumeLayout(False)
        Me.pnlDataHeader.ResumeLayout(False)
        Me.pnlDataHeader.PerformLayout()
        Me.pnlReport.ResumeLayout(False)
        Me.pnlReport.PerformLayout()
        Me.TbPanel1.ResumeLayout(False)
        Me.TbPanel1.PerformLayout()
        CType(Me.picQQWeiBo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picReportResult, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picSinaWeiBo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TbPictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Public WithEvents btnScan As tbButton
    Friend WithEvents lblReScan As System.Windows.Forms.LinkLabel
    Friend WithEvents pnlListData As tbFlowLayoutPanel
    Friend WithEvents pnlHeader As tbPanel
    Friend WithEvents lblDescrib As tbLabel
    Friend WithEvents TbPictureBox2 As tbPictureBox
    Friend WithEvents lblSuguest As tbLabel
    Friend WithEvents pnlDescrib As tbPanel
    Friend WithEvents prgScanProgress As tbControlBar
    Friend WithEvents pnlScanning As tbPanel
    Friend WithEvents picScanning As tbPictureBox
    Friend WithEvents lblScanning As tbLabel
    Public WithEvents btnStopScan As tbButton
    Friend WithEvents pnlScanFinish As tbPanel
    Friend WithEvents picScanFinish As tbPictureBox
    Public WithEvents btnClear As tbButton
    Friend WithEvents lblScanFinish_Describ As tbLabel
    Friend WithEvents pnlCleanning As tbPanel
    Public WithEvents btnStopClear As tbButton
    Friend WithEvents picCleanning As tbPictureBox
    Friend WithEvents prgCleanProgress As tbControlBar
    Friend WithEvents lblCleanning As tbLabel
    Friend WithEvents pnlFunction As tbPanel
    Friend WithEvents pnlReport As tbPanel
    Public WithEvents btnReportFinish As tbButton
    Friend WithEvents lblReportRescan As System.Windows.Forms.LinkLabel
    Friend WithEvents lblReportWeibo As System.Windows.Forms.LinkLabel
    Friend WithEvents picSinaWeiBo As tbPictureBox
    Friend WithEvents chkSelectAll As tbCheckBox
    Friend WithEvents pnlDataHeader As tbPanel
    Friend WithEvents lblHeaderItemName As System.Windows.Forms.Label
    Friend WithEvents lblRowHeaderDetail As System.Windows.Forms.Label
    Friend WithEvents lblRowHeaderFileSize As System.Windows.Forms.Label
    Friend WithEvents lblRowHeaderFileCount As System.Windows.Forms.Label
    Friend WithEvents ClearRubbishFileFoundControl1 As iTong.ClearRubbishFileFoundControl
    Friend WithEvents crlRubbishSelct As iTong.ClearRubbishFileSelectControl
    Friend WithEvents picReportResult As tbPictureBox
    Friend WithEvents TbPanel1 As tbPanel
    Friend WithEvents TbPictureBox1 As tbPictureBox
    Friend WithEvents webFirstPage As tbWebBrowser
    Friend WithEvents chkSharedWeibo As tbCheckBox
    Friend WithEvents crlRubbishFound As iTong.ClearRubbishFileSelectControl
    Friend WithEvents lblReportQQWeibo As System.Windows.Forms.LinkLabel
    Friend WithEvents picQQWeiBo As tbPictureBox
    Friend WithEvents btnResult As tbButton
End Class
