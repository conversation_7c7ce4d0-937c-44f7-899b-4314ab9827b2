﻿Public Class tbFlowLayoutPanel
    Inherits FlowLayoutPanel

    Public Sub New()
        Me.SetStyle(ControlStyles.UserPaint, True)
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)

        Me.SetStyle(ControlStyles.DoubleBuffer, True)
        Me.SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        Me.SetStyle(ControlStyles.ResizeRedraw, True)
        Me.UpdateStyles()

        Me.FlowDirection = Windows.Forms.FlowDirection.LeftToRight
    End Sub

    Protected Overrides Sub OnControlAdded(ByVal e As System.Windows.Forms.ControlEventArgs)
        MyBase.OnControlAdded(e)

        Me.Invalidate()
    End Sub

    Protected Overrides Sub OnControlRemoved(ByVal e As System.Windows.Forms.ControlEventArgs)
        MyBase.OnControlRemoved(e)

        Me.Invalidate()
    End Sub

    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        MyBase.OnPaint(e)

        'Debug.Print(e.ClipRectangle.ToString())

        e.Graphics.Clear(Me.BackColor)
        For index As Integer = 0 To Me.Controls.Count - 1

            Dim ctl As Control = Me.Controls(index)
            Dim brushBack As New SolidBrush(ctl.BackColor)
            Dim rect As New Rectangle(0, ctl.Top, Me.ClientSize.Width, ctl.Height)

            e.Graphics.FillRectangle(brushBack, rect)
        Next

    End Sub

    Private Sub tbFlowLayoutPanel_Resize(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Resize
        Me.Invalidate()
    End Sub
End Class
