<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="chkIsSelect.tbIconChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAlwSFlzAAAK/wAA
        Cv8BNGKaggAABJhJREFUWEfVl21MU2cUx/thsinIkEsL9PJS3OZMsDUL0c0NMZDIMoFhFlgyI2rYTJYN
        SlgDlDnWdOliCAEEB/GlMrGgCGbZF/bCXICZuKCgQizZok2TfSNkmjC2qFty9/9f721uaxvt6Jed5Jf0
        ec753/uc85x771Pd+hO/6WACyAMloDwC9DFGgEYP2kEy9Xr7TD4YBH8AKQL0MSY/lvqz5y9gSbp4kAU2
        AUsE6GNMPDR60A6SMdalACZnBgwQI0CfOU7cWGn64vbN7K75CYyfwoKK9E1X7yYfvPDh0xsKN2jig6CP
        MYbm6d9NPX5frPQgAXDtOSADhNUD+nI2mS2veM6en+vrH1D1ui0gF7AaiWBtBBLj88qM2R3eG6buW38m
        FlQVSpLE3RtZ997wQfifTN/5q8/UffvvWOlh2cAEHqvf9uprxi/PDF5DAZZ3Fr9eqHSPrgywC8KJAqyx
        FAu4+aSp2ycJlc5hzMUpCdxflZWXFhofilZv+OjbfzAXEz2Mu89HOKxOZcvWlwUkPomkpQ9qrCcxF6cW
        oDy19txm/bvHXmRgONaYi4XMtvnRrI5bkui8fAVz+3lzJQEpWr1Q+wOksdHDLA6na7OtoSminsmfGRwa
        ZfK9x91cv7z7gQKIzqmuzFavN6nMzmflkQtkHJ4bzmz7RULMcnxe+S7MlWsTiFZPTaz0MMuJU6e7sLve
        vfsOhNX3D5wbZvJs/e0FO7h+S1ABjI6piYzD85Lomptclb4xiSIV0TnTTh9J2d/bijm2W1ABotWHFmAl
        epjF3dc/oSQ4mW0yBenxzPNtL5GGpmZ1/cEFSMivrhCdc/fEz25KxpZpN+ZkcXrTpWrOkbTGCbbOesUX
        VIBo9aEFWIkeZil7c3fF4NDIPSaJhAP6Yyf7qtXkldZX1x9cAGAQqo4fNTpmsRuzUrr9siPVOlpqdNx4
        II9bZpZWW0rZOvKFqdEmEK0+tAAr0cP4jTd8/MmnR9VkT532OI5095SiKA84RmcsKa2vrv+RAnDyBUPd
        xZm0Q9elh1xbVn+ve7tLbR31AqEFiEofpgD/WQ9jAWQ9dnlGLQKSX1Z/a1pf1oOgAvAz+CxY+0zurpLU
        xqmlVNxARV87pm0dwtgyTQL3cUiRL/4kesZSEys9jJ9B+fufv72ghLutJk5CWp8w1qwtAA9CzwE6haTd
        ba7UhquSjO3nJSxK2zqEF9uqSWBEqPm+SvE9Vi/UfLcHmq9ipYfxIKSeI4R6W4NLTT5M6xPGZmsLwEMQ
        g54HfIPmJJZ+7kp660hnwg6rFWO1dehjod4Aek0CRXr79B3BerF69UsVBvjC6uljoohdhGZnrPQwHoV5
        zmdi7M6c2rp6V2Pzoc49e/dp108fY3jqTQgUwGC7ovP7/b0ej6ddFMVKOPlO4I4cAO8o43L63G53q8/n
        66FGXQB/f+O9O/7+kF/KbLku/3ER6n6UhNoxKaX+p8CfGfqqB3zS17N3LsVSzyQWFxc7xsfH7bm5uexm
        vhMKQBHYpowt9I2NjdkWFhb4WZR1cgFouFAyGAR/gUhG3wjQKxqZ/6v+oel0/wJcY06vPti7twAAAABJ
        RU5ErkJggg==
</value>
  </data>
  <data name="chkIsSelect.tbIconIndeterminate" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0w0hl6ky4wgPQuIB0EURhmBhjKAMMMTWyIqEBEEREBRZCggAGjoUis
        iGIhKKhgD0gQUGIwiqioZEbWSnx5ee/l5ffHvd/aZ+9z99l7n7UuACRPHy4vBZYCIJkn4Ad6ONNXhUfQ
        sf0ABniAAaYAMFnpqb5B7sFAJC83F3q6yAn8i94MAUj8vmXo6U+ng/9P0qxUvgAAyF/E5mxOOkvE+SJO
        yhSkiu0zIqbGJIoZRomZL0pQxHJijlvkpZ99FtlRzOxkHlvE4pxT2clsMfeIeHuGkCNixEfEBRlcTqaI
        b4tYM0mYzBXxW3FsMoeZDgCKJLYLOKx4EZuImMQPDnQR8XIAcKS4LzjmCxZwsgTiQ7mkpGbzuXHxArou
        S49uam3NoHtyMpM4AoGhP5OVyOSz6S4pyalMXjYAi2f+LBlxbemiIluaWltaGpoZmX5RqP+6+Dcl7u0i
        vQr43DOI1veH7a/8UuoAYMyKarPrD1vMfgA6tgIgd/8Pm+YhACRFfWu/8cV5aOJ5iRcIUm2MjTMzM424
        HJaRuKC/6386/A198T0j8Xa/l4fuyollCpMEdHHdWClJKUI+PT2VyeLQDf88xP848K/zWBrIieXwOTxR
        RKhoyri8OFG7eWyugJvCo3N5/6mJ/zDsT1qca5Eo9Z8ANcoISN2gAuTnPoCiEAESeVDc9d/75oMPBeKb
        F6Y6sTj3nwX9+65wifiRzo37HOcSGExnCfkZi2viawnQgAAkARXIAxWgAXSBITADVsAWOAI3sAL4gWAQ
        DtYCFogHyYAPMkEu2AwKQBHYBfaCSlAD6kEjaAEnQAc4DS6Ay+A6uAnugAdgBIyD52AGvAHzEARhITJE
        geQhVUgLMoDMIAZkD7lBPlAgFA5FQ3EQDxJCudAWqAgqhSqhWqgR+hY6BV2ArkID0D1oFJqCfoXewwhM
        gqmwMqwNG8MM2An2hoPhNXAcnAbnwPnwTrgCroOPwe3wBfg6fAcegZ/DswhAiAgNUUMMEQbigvghEUgs
        wkc2IIVIOVKHtCBdSC9yCxlBppF3KAyKgqKjDFG2KE9UCIqFSkNtQBWjKlFHUe2oHtQt1ChqBvUJTUYr
        oQ3QNmgv9Cp0HDoTXYAuRzeg29CX0HfQ4+g3GAyGhtHBWGE8MeGYBMw6TDHmAKYVcx4zgBnDzGKxWHms
        AdYO64dlYgXYAux+7DHsOewgdhz7FkfEqeLMcO64CBwPl4crxzXhzuIGcRO4ebwUXgtvg/fDs/HZ+BJ8
        Pb4LfwM/jp8nSBN0CHaEYEICYTOhgtBCuER4SHhFJBLVidbEACKXuIlYQTxOvEIcJb4jyZD0SS6kSJKQ
        tJN0hHSedI/0ikwma5MdyRFkAXknuZF8kfyY/FaCImEk4SXBltgoUSXRLjEo8UISL6kl6SS5VjJHslzy
        pOQNyWkpvJS2lIsUU2qDVJXUKalhqVlpirSptJ90snSxdJP0VelJGayMtoybDFsmX+awzEWZMQpC0aC4
        UFiULZR6yiXKOBVD1aF6UROoRdRvqP3UGVkZ2WWyobJZslWyZ2RHaAhNm+ZFS6KV0E7QhmjvlygvcVrC
        WbJjScuSwSVzcopyjnIcuUK5Vrk7cu/l6fJu8onyu+U75B8poBT0FQIUMhUOKlxSmFakKtoqshQLFU8o
        3leClfSVApXWKR1W6lOaVVZR9lBOVd6vfFF5WoWm4qiSoFKmclZlSpWiaq/KVS1TPaf6jC5Ld6In0Svo
        PfQZNSU1TzWhWq1av9q8uo56iHqeeqv6Iw2CBkMjVqNMo1tjRlNV01czV7NZ874WXouhFa+1T6tXa05b
        RztMe5t2h/akjpyOl06OTrPOQ12yroNumm6d7m09jB5DL1HvgN5NfVjfQj9ev0r/hgFsYGnANThgMLAU
        vdR6KW9p3dJhQ5Khk2GGYbPhqBHNyMcoz6jD6IWxpnGE8W7jXuNPJhYmSSb1Jg9MZUxXmOaZdpn+aqZv
        xjKrMrttTjZ3N99o3mn+cpnBMs6yg8vuWlAsfC22WXRbfLS0suRbtlhOWWlaRVtVWw0zqAx/RjHjijXa
        2tl6o/Vp63c2ljYCmxM2v9ga2ibaNtlOLtdZzllev3zMTt2OaVdrN2JPt4+2P2Q/4qDmwHSoc3jiqOHI
        dmxwnHDSc0pwOub0wtnEme/c5jznYuOy3uW8K+Lq4Vro2u8m4xbiVun22F3dPc692X3Gw8Jjncd5T7Sn
        t+duz2EvZS+WV6PXzAqrFetX9HiTvIO8K72f+Oj78H26fGHfFb57fB+u1FrJW9nhB/y8/Pb4PfLX8U/z
        /z4AE+AfUBXwNNA0MDewN4gSFBXUFPQm2Dm4JPhBiG6IMKQ7VDI0MrQxdC7MNaw0bGSV8ar1q66HK4Rz
        wzsjsBGhEQ0Rs6vdVu9dPR5pEVkQObRGZ03WmqtrFdYmrT0TJRnFjDoZjY4Oi26K/sD0Y9YxZ2O8Yqpj
        ZlgurH2s52xHdhl7imPHKeVMxNrFlsZOxtnF7YmbineIL4+f5rpwK7kvEzwTahLmEv0SjyQuJIUltSbj
        kqOTT/FkeIm8nhSVlKyUgVSD1ILUkTSbtL1pM3xvfkM6lL4mvVNAFf1M9Ql1hVuFoxn2GVUZbzNDM09m
        SWfxsvqy9bN3ZE/kuOd8vQ61jrWuO1ctd3Pu6Hqn9bUboA0xG7o3amzM3zi+yWPT0c2EzYmbf8gzySvN
        e70lbEtXvnL+pvyxrR5bmwskCvgFw9tst9VsR23nbu/fYb5j/45PhezCa0UmReVFH4pZxde+Mv2q4quF
        nbE7+0ssSw7uwuzi7Rra7bD7aKl0aU7p2B7fPe1l9LLCstd7o/ZeLV9WXrOPsE+4b6TCp6Jzv+b+Xfs/
        VMZX3qlyrmqtVqreUT13gH1g8KDjwZYa5ZqimveHuIfu1nrUttdp15UfxhzOOPy0PrS+92vG140NCg1F
        DR+P8I6MHA082tNo1djYpNRU0gw3C5unjkUeu/mN6zedLYYtta201qLj4Ljw+LNvo78dOuF9ovsk42TL
        d1rfVbdR2grbofbs9pmO+I6RzvDOgVMrTnV32Xa1fW/0/ZHTaqerzsieKTlLOJt/duFczrnZ86nnpy/E
        XRjrjup+cHHVxds9AT39l7wvXbnsfvlir1PvuSt2V05ftbl66hrjWsd1y+vtfRZ9bT9Y/NDWb9nffsPq
        RudN65tdA8sHzg46DF645Xrr8m2v29fvrLwzMBQydHc4cnjkLvvu5L2key/vZ9yff7DpIfph4SOpR+WP
        lR7X/aj3Y+uI5ciZUdfRvidBTx6Mscae/5T+04fx/Kfkp+UTqhONk2aTp6fcp24+W/1s/Hnq8/npgp+l
        f65+ofviu18cf+mbWTUz/pL/cuHX4lfyr468Xva6e9Z/9vGb5Dfzc4Vv5d8efcd41/s+7P3EfOYH7IeK
        j3ofuz55f3q4kLyw8Bv3hPP74uYdwgAAAAlwSFlzAAALDgAACw4BQL7hQQAAAq9JREFUWEftl81rU0EU
        xd9GRGy0zYcIsa1fdBMrSNGVGwUXtZRCqxvBTTQgWFzUhYHQPyCBumtX2cVCsVJcSRuEQLrUIi5chqxK
        DJF2IQiuxnOGGZ0xM+SFvGUu/KDv3XPmzdy+N3MTqEiAGTAHFjwwRw21gRBCksofkNtgE/wEwgNz1FAb
        mV/FaTABroHrHpijhlrLnwRc3DSgIO2BOWrug5QeAJO5m3r16Tiee/f85NSdKaXtgjlqoP0Bz72o/IgR
        wHldAheA0w+Yo4aFGDH8wU2QAXFwBsQ8MEcNtbf0AJjM9tjTtzmV6+kfy249gWcnKj9iElxkDvT0AxZh
        0vAH84BvQSye21lMrtRbyZV9YVNvMUeN0s7rATCZ3ycmZs6H9VNLT1R+BP/7/CxjhUJhsVgstkqlkjDh
        PeaoUdppwy+/b1kl98M19ZbW0aMHwGQ4Sl9+eqLyI/h9y/uuxWuY0zp6DL9ZANeD/6F19OgB7AW4fRqt
        8xfA7dO4/Ii/BXAt3ETr6DH8wwIMC6BuDgvQYxP6rnX06AHsBYTz+wvQvx8RdhM0528VgMfgWcBjaIkP
        cjz8kDlqlNY6BtGkyGMojJ9aeqLyI3gMyvMfR90SF+pY/CFz1CitdQyyEboCdHV6cRlYjVBiee/xfxov
        ieXdR/BYjdAgfgQbIdlHhIRaqxFiY8P29ioYBS4TYY6FmgV2K5z/fJR48TF76saDc0rbBXNcKLQdeOxW
        eAA/gq0w21suTL7JHpijhp2s1QoHzWZzo1KprKXT6Ye45J7QBXPlcrnYaDTWcS3NBJMJPnw7rj3baorx
        1S+uHzES5rJvGuL916N9XEfmZ3Q6nde1Wi2fyWT4NnNP6IK5arX6st1ur+Ha8vOPONgEv4AvmNsGKeWR
        qMn05TcXMKh/sPmL4A9LsMSWC/9HNgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="chkIsSelect.tbIconUnChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAlwSFlzAAAK/wAA
        Cv8BNGKaggAAAjxJREFUWEftl79LW1EUx98ipWhbTUwppP5qi0uqUMROXSx0sCIuugguaYVCxcWhWfwD
        OuimU7YoSC2lk7ShkMHRltKhY8gcIjoIhU6v38/jPXg3uTcqeVPJgQ/k5ny/l/NO3nv3xAsjLabEnFhw
        QA4NWs/3/YBM4Qc8E/viQvgOyKFBm5g/jF4xLB6LSQfk0KA1/IOCi5sQCLIOyKF5KTLRBirmeebdyXlq
        9ePbG+Mz46G2BXJopD2V50VSfkWfoK4xcV9Y/YIcGhrRF/N70yInUuK2uOWAHBq0T6MNVMzhwOsPq2Hu
        Uv9A/uCVPJ+S8itGxCg5calf0ISRmN+bF9wFNpMNtPPRBirmb8/w1L0mjRO0eJLyK/j1eSytegtoJ2L+
        4Pm2CduxEG2gYtjFpnGCJym/gufbqmvDZMzfbUC3AeGX16HbgGiDbgP+gwZwDN4RNqENtMYxqCHlyscQ
        WjxJ+RUcg+3O/2bQGscgg9BDYRPbeCCMQSi99nWlSeMkvfZlWR5jEOrEr2AQuvIcIdAagxCDDePtI9Ev
        bCYgR6NmhTkKF76fpde/5W8+Wbwbalsgx4VK25DHHIU78CsYhRlvubB2dzI5NEyyxijs1Wq13VKptJXN
        Zpe05J3QArlisfi+Wq3uaB2YQcV4R7/PK28Oav7Q5k/bn5gAcvm9qv/519mx1on5iUajsV2pVAq5XI67
        mXdCC+TK5fJGvV7f0trw8yEl9sUf4QpyhyITegLCYq7lj19Ap/7O6ve9f0UMhJTNQlizAAAAAElFTkSu
        QmCC
</value>
  </data>
</root>