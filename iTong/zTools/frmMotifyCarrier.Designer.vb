﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmMotifyCarrier
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmMotifyCarrier))
        Me.btnSplit = New tbButton
        Me.pnlStep2 = New tbPanel
        Me.btnNotValid = New tbButton
        Me.lblNotValid = New System.Windows.Forms.LinkLabel
        Me.pnSelectPicture = New tbPanel
        Me.txtTSPath = New tbTextBox
        Me.txtFsoPath = New tbTextBox
        Me.picStep3 = New System.Windows.Forms.PictureBox
        Me.lblDownloadPicture = New System.Windows.Forms.LinkLabel
        Me.lblSelectPicture = New tbLabel
        Me.lblPictureSuggust = New System.Windows.Forms.Label
        Me.lblTSImage = New tbLabel
        Me.lblFSOImage = New tbLabel
        Me.btnBrowsePicture2 = New tbButton
        Me.btnBrowsePicture1 = New tbButton
        Me.btnReadTutorials = New tbButton
        Me.pnlSelectText = New tbPanel
        Me.lblFont = New tbLabel
        Me.cboTextFont = New System.Windows.Forms.ComboBox
        Me.lblTextTooLongWarning = New tbLabel
        Me.txtCarrierString = New tbTextBox
        Me.TbLabel11 = New tbLabel
        Me.PictureBox5 = New System.Windows.Forms.PictureBox
        Me.lblSelectDefinePicture = New tbLabel
        Me.cprFSO = New iTong.CarrierPreview
        Me.btnHorizonSplit = New tbButton
        Me.lblReadTutorials = New System.Windows.Forms.LinkLabel
        Me.pnlMessage = New tbPanel
        Me.picResultTip = New System.Windows.Forms.PictureBox
        Me.lblMessage = New System.Windows.Forms.Label
        Me.TbButton3 = New tbButton
        Me.btnFirstStep = New tbButton
        Me.picStep2 = New System.Windows.Forms.PictureBox
        Me.rdoSetPicture = New System.Windows.Forms.RadioButton
        Me.rdoSetText = New System.Windows.Forms.RadioButton
        Me.lblReplaceMethod = New tbLabel
        Me.lblTSPreview = New tbLabel
        Me.lblFSOPreview = New tbLabel
        Me.lblPreview = New tbLabel
        Me.cprTS = New iTong.CarrierPreview
        Me.btnOK = New tbButton
        Me.pnlStep1 = New tbPanel
        Me.cboCarrierName = New System.Windows.Forms.ComboBox
        Me.lblReplaceType = New tbLabel
        Me.TbPanel2 = New tbPanel
        Me.picTips = New System.Windows.Forms.PictureBox
        Me.picDescribtion = New System.Windows.Forms.PictureBox
        Me.txtCarrierVersion = New tbTextBox
        Me.picArraw = New System.Windows.Forms.PictureBox
        Me.PictureBox4 = New System.Windows.Forms.PictureBox
        Me.TbLabel9 = New tbLabel
        Me.lblErrorMessage = New tbLabel
        Me.TbLabel10 = New tbLabel
        Me.btnNextStep = New tbButton
        Me.btnToDefault = New tbButton
        Me.TbPanel1 = New tbPanel
        Me.lblDescribtion = New System.Windows.Forms.Label
        Me.pnlStep2.SuspendLayout()
        Me.pnSelectPicture.SuspendLayout()
        CType(Me.picStep3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlSelectText.SuspendLayout()
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlMessage.SuspendLayout()
        CType(Me.picResultTip, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picStep2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlStep1.SuspendLayout()
        Me.TbPanel2.SuspendLayout()
        CType(Me.picTips, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picDescribtion, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picArraw, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TbPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(534, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(510, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(486, 0)
        '
        'btnSplit
        '
        Me.btnSplit.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnSplit.BackColor = System.Drawing.Color.Transparent
        Me.btnSplit.BindingForm = Nothing
        Me.btnSplit.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSplit.Location = New System.Drawing.Point(29, 95)
        Me.btnSplit.Name = "btnSplit"
        Me.btnSplit.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSplit.Selectable = True
        Me.btnSplit.Size = New System.Drawing.Size(500, 2)
        Me.btnSplit.TabIndex = 62
        Me.btnSplit.tbAdriftIconWhenHover = False
        Me.btnSplit.tbAutoSize = False
        Me.btnSplit.tbAutoSizeEx = False
        Me.btnSplit.tbBackgroundImage = CType(resources.GetObject("btnSplit.tbBackgroundImage"), System.Drawing.Image)
        Me.btnSplit.tbBackgroundImageState = ImageState.OneState
        Me.btnSplit.tbBadgeNumber = 0
        Me.btnSplit.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSplit.tbEndEllipsis = False
        Me.btnSplit.tbIconHoldPlace = True
        Me.btnSplit.tbIconImage = Nothing
        Me.btnSplit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbIconImageState = ImageState.OneState
        Me.btnSplit.tbIconMore = False
        Me.btnSplit.tbIconMouseDown = Nothing
        Me.btnSplit.tbIconMouseHover = Nothing
        Me.btnSplit.tbIconMouseLeave = Nothing
        Me.btnSplit.tbIconPlaceText = 2
        Me.btnSplit.tbIconReadOnly = Nothing
        Me.btnSplit.tbImageMouseDown = Nothing
        Me.btnSplit.tbImageMouseHover = Nothing
        Me.btnSplit.tbImageMouseLeave = Nothing
        Me.btnSplit.tbReadOnly = False
        Me.btnSplit.tbReadOnlyText = False
        Me.btnSplit.tbShadow = False
        Me.btnSplit.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSplit.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSplit.tbShowNew = False
        Me.btnSplit.tbShowToolTipOnButton = False
        Me.btnSplit.tbSplit = "35,0,35,0"
        Me.btnSplit.tbText = ""
        Me.btnSplit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbTextColor = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDown = System.Drawing.Color.White
        Me.btnSplit.tbTextColorHover = System.Drawing.Color.White
        Me.btnSplit.tbTextMouseDownPlace = 0
        Me.btnSplit.tbToolTip = ""
        Me.btnSplit.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSplit.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSplit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.VisibleEx = True
        '
        'pnlStep2
        '
        Me.pnlStep2.BackColor = System.Drawing.Color.Transparent
        Me.pnlStep2.Controls.Add(Me.btnNotValid)
        Me.pnlStep2.Controls.Add(Me.lblNotValid)
        Me.pnlStep2.Controls.Add(Me.pnSelectPicture)
        Me.pnlStep2.Controls.Add(Me.btnReadTutorials)
        Me.pnlStep2.Controls.Add(Me.pnlSelectText)
        Me.pnlStep2.Controls.Add(Me.cprFSO)
        Me.pnlStep2.Controls.Add(Me.btnHorizonSplit)
        Me.pnlStep2.Controls.Add(Me.lblReadTutorials)
        Me.pnlStep2.Controls.Add(Me.pnlMessage)
        Me.pnlStep2.Controls.Add(Me.TbButton3)
        Me.pnlStep2.Controls.Add(Me.btnFirstStep)
        Me.pnlStep2.Controls.Add(Me.picStep2)
        Me.pnlStep2.Controls.Add(Me.rdoSetPicture)
        Me.pnlStep2.Controls.Add(Me.rdoSetText)
        Me.pnlStep2.Controls.Add(Me.lblReplaceMethod)
        Me.pnlStep2.Controls.Add(Me.lblTSPreview)
        Me.pnlStep2.Controls.Add(Me.lblFSOPreview)
        Me.pnlStep2.Controls.Add(Me.lblPreview)
        Me.pnlStep2.Controls.Add(Me.cprTS)
        Me.pnlStep2.Controls.Add(Me.btnOK)
        Me.pnlStep2.Location = New System.Drawing.Point(30, 109)
        Me.pnlStep2.Name = "pnlStep2"
        Me.pnlStep2.Size = New System.Drawing.Size(498, 397)
        Me.pnlStep2.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlStep2.TabIndex = 83
        Me.pnlStep2.tbBackgroundImage = Nothing
        Me.pnlStep2.tbShowWatermark = False
        Me.pnlStep2.tbSplit = "0,0,0,0"
        Me.pnlStep2.tbWatermark = Nothing
        Me.pnlStep2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlStep2.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnNotValid
        '
        Me.btnNotValid.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnNotValid.BackColor = System.Drawing.Color.Transparent
        Me.btnNotValid.BindingForm = Nothing
        Me.btnNotValid.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnNotValid.Location = New System.Drawing.Point(270, 352)
        Me.btnNotValid.Name = "btnNotValid"
        Me.btnNotValid.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnNotValid.Selectable = True
        Me.btnNotValid.Size = New System.Drawing.Size(12, 12)
        Me.btnNotValid.TabIndex = 105
        Me.btnNotValid.tbAdriftIconWhenHover = False
        Me.btnNotValid.tbAutoSize = True
        Me.btnNotValid.tbAutoSizeEx = False
        Me.btnNotValid.tbBackgroundImage = My.Resources.btn_3_goto
        Me.btnNotValid.tbBackgroundImageState = ImageState.ThreeState
        Me.btnNotValid.tbBadgeNumber = 0
        Me.btnNotValid.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnNotValid.tbEndEllipsis = False
        Me.btnNotValid.tbIconHoldPlace = True
        Me.btnNotValid.tbIconImage = Nothing
        Me.btnNotValid.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNotValid.tbIconImageState = ImageState.OneState
        Me.btnNotValid.tbIconMore = False
        Me.btnNotValid.tbIconMouseDown = Nothing
        Me.btnNotValid.tbIconMouseHover = Nothing
        Me.btnNotValid.tbIconMouseLeave = Nothing
        Me.btnNotValid.tbIconPlaceText = 2
        Me.btnNotValid.tbIconReadOnly = Nothing
        Me.btnNotValid.tbImageMouseDown = Nothing
        Me.btnNotValid.tbImageMouseHover = Nothing
        Me.btnNotValid.tbImageMouseLeave = Nothing
        Me.btnNotValid.tbReadOnly = False
        Me.btnNotValid.tbReadOnlyText = False
        Me.btnNotValid.tbShadow = False
        Me.btnNotValid.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnNotValid.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnNotValid.tbShowNew = False
        Me.btnNotValid.tbShowToolTipOnButton = False
        Me.btnNotValid.tbSplit = "3,3,3,3"
        Me.btnNotValid.tbText = ""
        Me.btnNotValid.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNotValid.tbTextColor = System.Drawing.Color.White
        Me.btnNotValid.tbTextColorDisable = System.Drawing.Color.White
        Me.btnNotValid.tbTextColorDown = System.Drawing.Color.White
        Me.btnNotValid.tbTextColorHover = System.Drawing.Color.White
        Me.btnNotValid.tbTextMouseDownPlace = 0
        Me.btnNotValid.tbToolTip = ""
        Me.btnNotValid.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnNotValid.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnNotValid.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNotValid.VisibleEx = True
        '
        'lblNotValid
        '
        Me.lblNotValid.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblNotValid.AutoEllipsis = True
        Me.lblNotValid.AutoSize = True
        Me.lblNotValid.DisabledLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblNotValid.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lblNotValid.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblNotValid.Location = New System.Drawing.Point(8, 351)
        Me.lblNotValid.MaximumSize = New System.Drawing.Size(280, 17)
        Me.lblNotValid.Name = "lblNotValid"
        Me.lblNotValid.Size = New System.Drawing.Size(257, 12)
        Me.lblNotValid.TabIndex = 104
        Me.lblNotValid.TabStop = True
        Me.lblNotValid.Text = "修改运营商图标成功，但设备上未生效怎么办？"
        Me.lblNotValid.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnSelectPicture
        '
        Me.pnSelectPicture.BackColor = System.Drawing.Color.Transparent
        Me.pnSelectPicture.Controls.Add(Me.txtTSPath)
        Me.pnSelectPicture.Controls.Add(Me.txtFsoPath)
        Me.pnSelectPicture.Controls.Add(Me.picStep3)
        Me.pnSelectPicture.Controls.Add(Me.lblDownloadPicture)
        Me.pnSelectPicture.Controls.Add(Me.lblSelectPicture)
        Me.pnSelectPicture.Controls.Add(Me.lblPictureSuggust)
        Me.pnSelectPicture.Controls.Add(Me.lblTSImage)
        Me.pnSelectPicture.Controls.Add(Me.lblFSOImage)
        Me.pnSelectPicture.Controls.Add(Me.btnBrowsePicture2)
        Me.pnSelectPicture.Controls.Add(Me.btnBrowsePicture1)
        Me.pnSelectPicture.Location = New System.Drawing.Point(0, 68)
        Me.pnSelectPicture.Name = "pnSelectPicture"
        Me.pnSelectPicture.Size = New System.Drawing.Size(499, 124)
        Me.pnSelectPicture.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnSelectPicture.TabIndex = 93
        Me.pnSelectPicture.tbBackgroundImage = Nothing
        Me.pnSelectPicture.tbShowWatermark = False
        Me.pnSelectPicture.tbSplit = "0,0,0,0"
        Me.pnSelectPicture.tbWatermark = Nothing
        Me.pnSelectPicture.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnSelectPicture.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'txtTSPath
        '
        Me.txtTSPath.BackColor = System.Drawing.Color.White
        Me.txtTSPath.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtTSPath.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtTSPath.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtTSPath.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtTSPath.ForeColor = System.Drawing.Color.Black
        Me.txtTSPath.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtTSPath.Location = New System.Drawing.Point(151, 94)
        Me.txtTSPath.MaxLength = 32767
        Me.txtTSPath.Name = "txtTSPath"
        Me.txtTSPath.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtTSPath.Size = New System.Drawing.Size(221, 23)
        Me.txtTSPath.TabIndex = 89
        Me.txtTSPath.Tag = Nothing
        Me.txtTSPath.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtTSPath.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtTSPath.tbSelMark = True
        Me.txtTSPath.tbTextBind = ""
        Me.txtTSPath.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtTSPath.TextFormat = tbTextFormat.Normal
        Me.txtTSPath.TextTip = ""
        '
        'txtFsoPath
        '
        Me.txtFsoPath.BackColor = System.Drawing.Color.White
        Me.txtFsoPath.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtFsoPath.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtFsoPath.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtFsoPath.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtFsoPath.ForeColor = System.Drawing.Color.Black
        Me.txtFsoPath.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtFsoPath.Location = New System.Drawing.Point(151, 32)
        Me.txtFsoPath.MaxLength = 32767
        Me.txtFsoPath.Name = "txtFsoPath"
        Me.txtFsoPath.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtFsoPath.Size = New System.Drawing.Size(221, 23)
        Me.txtFsoPath.TabIndex = 88
        Me.txtFsoPath.Tag = Nothing
        Me.txtFsoPath.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtFsoPath.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtFsoPath.tbSelMark = True
        Me.txtFsoPath.tbTextBind = ""
        Me.txtFsoPath.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtFsoPath.TextFormat = tbTextFormat.Normal
        Me.txtFsoPath.TextTip = ""
        '
        'picStep3
        '
        Me.picStep3.Image = CType(resources.GetObject("picStep3.Image"), System.Drawing.Image)
        Me.picStep3.Location = New System.Drawing.Point(0, -4)
        Me.picStep3.Name = "picStep3"
        Me.picStep3.Size = New System.Drawing.Size(37, 48)
        Me.picStep3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picStep3.TabIndex = 87
        Me.picStep3.TabStop = False
        '
        'lblDownloadPicture
        '
        Me.lblDownloadPicture.LinkBehavior = System.Windows.Forms.LinkBehavior.AlwaysUnderline
        Me.lblDownloadPicture.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblDownloadPicture.Location = New System.Drawing.Point(40, 36)
        Me.lblDownloadPicture.Name = "lblDownloadPicture"
        Me.lblDownloadPicture.Size = New System.Drawing.Size(102, 21)
        Me.lblDownloadPicture.TabIndex = 86
        Me.lblDownloadPicture.TabStop = True
        Me.lblDownloadPicture.Text = "点击下载图片资源"
        Me.lblDownloadPicture.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblSelectPicture
        '
        Me.lblSelectPicture.BackColor = System.Drawing.Color.Transparent
        Me.lblSelectPicture.Location = New System.Drawing.Point(31, 10)
        Me.lblSelectPicture.Name = "lblSelectPicture"
        Me.lblSelectPicture.Size = New System.Drawing.Size(115, 21)
        Me.lblSelectPicture.TabIndex = 66
        Me.lblSelectPicture.tbAdriftWhenHover = False
        Me.lblSelectPicture.tbAutoEllipsis = False
        Me.lblSelectPicture.tbAutoSize = False
        Me.lblSelectPicture.tbHideImage = False
        Me.lblSelectPicture.tbIconImage = Nothing
        Me.lblSelectPicture.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSelectPicture.tbIconPlaceText = 5
        Me.lblSelectPicture.tbShadow = False
        Me.lblSelectPicture.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSelectPicture.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSelectPicture.tbShowScrolling = False
        Me.lblSelectPicture.Text = "选择自定义图片："
        Me.lblSelectPicture.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblPictureSuggust
        '
        Me.lblPictureSuggust.AutoSize = True
        Me.lblPictureSuggust.Location = New System.Drawing.Point(140, 137)
        Me.lblPictureSuggust.Name = "lblPictureSuggust"
        Me.lblPictureSuggust.Size = New System.Drawing.Size(0, 12)
        Me.lblPictureSuggust.TabIndex = 65
        '
        'lblTSImage
        '
        Me.lblTSImage.BackColor = System.Drawing.Color.Transparent
        Me.lblTSImage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(141, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(149, Byte), Integer))
        Me.lblTSImage.Location = New System.Drawing.Point(151, 67)
        Me.lblTSImage.Name = "lblTSImage"
        Me.lblTSImage.Size = New System.Drawing.Size(326, 24)
        Me.lblTSImage.TabIndex = 64
        Me.lblTSImage.tbAdriftWhenHover = False
        Me.lblTSImage.tbAutoEllipsis = False
        Me.lblTSImage.tbAutoSize = False
        Me.lblTSImage.tbHideImage = False
        Me.lblTSImage.tbIconImage = Nothing
        Me.lblTSImage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTSImage.tbIconPlaceText = 5
        Me.lblTSImage.tbShadow = False
        Me.lblTSImage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTSImage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTSImage.tbShowScrolling = False
        Me.lblTSImage.Text = "通知中心图片(必填):"
        Me.lblTSImage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblFSOImage
        '
        Me.lblFSOImage.BackColor = System.Drawing.Color.Transparent
        Me.lblFSOImage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(141, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(149, Byte), Integer))
        Me.lblFSOImage.Location = New System.Drawing.Point(149, 12)
        Me.lblFSOImage.Name = "lblFSOImage"
        Me.lblFSOImage.Size = New System.Drawing.Size(328, 17)
        Me.lblFSOImage.TabIndex = 63
        Me.lblFSOImage.tbAdriftWhenHover = False
        Me.lblFSOImage.tbAutoEllipsis = False
        Me.lblFSOImage.tbAutoSize = False
        Me.lblFSOImage.tbHideImage = False
        Me.lblFSOImage.tbIconImage = Nothing
        Me.lblFSOImage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFSOImage.tbIconPlaceText = 5
        Me.lblFSOImage.tbShadow = False
        Me.lblFSOImage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFSOImage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFSOImage.tbShowScrolling = False
        Me.lblFSOImage.Text = "顶部状态栏图片(必填):"
        Me.lblFSOImage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnBrowsePicture2
        '
        Me.btnBrowsePicture2.BackColor = System.Drawing.Color.Transparent
        Me.btnBrowsePicture2.BindingForm = Nothing
        Me.btnBrowsePicture2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBrowsePicture2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture2.Location = New System.Drawing.Point(396, 94)
        Me.btnBrowsePicture2.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnBrowsePicture2.Name = "btnBrowsePicture2"
        Me.btnBrowsePicture2.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBrowsePicture2.Selectable = True
        Me.btnBrowsePicture2.Size = New System.Drawing.Size(74, 23)
        Me.btnBrowsePicture2.TabIndex = 60
        Me.btnBrowsePicture2.tbAdriftIconWhenHover = False
        Me.btnBrowsePicture2.tbAutoSize = False
        Me.btnBrowsePicture2.tbAutoSizeEx = False
        Me.btnBrowsePicture2.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnBrowsePicture2.tbBackgroundImageState = ImageState.FourState
        Me.btnBrowsePicture2.tbBadgeNumber = 0
        Me.btnBrowsePicture2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBrowsePicture2.tbEndEllipsis = False
        Me.btnBrowsePicture2.tbIconHoldPlace = True
        Me.btnBrowsePicture2.tbIconImage = Nothing
        Me.btnBrowsePicture2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBrowsePicture2.tbIconImageState = ImageState.OneState
        Me.btnBrowsePicture2.tbIconMore = False
        Me.btnBrowsePicture2.tbIconMouseDown = Nothing
        Me.btnBrowsePicture2.tbIconMouseHover = Nothing
        Me.btnBrowsePicture2.tbIconMouseLeave = Nothing
        Me.btnBrowsePicture2.tbIconPlaceText = 2
        Me.btnBrowsePicture2.tbIconReadOnly = Nothing
        Me.btnBrowsePicture2.tbImageMouseDown = Nothing
        Me.btnBrowsePicture2.tbImageMouseHover = Nothing
        Me.btnBrowsePicture2.tbImageMouseLeave = Nothing
        Me.btnBrowsePicture2.tbReadOnly = False
        Me.btnBrowsePicture2.tbReadOnlyText = False
        Me.btnBrowsePicture2.tbShadow = False
        Me.btnBrowsePicture2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBrowsePicture2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBrowsePicture2.tbShowNew = False
        Me.btnBrowsePicture2.tbShowToolTipOnButton = False
        Me.btnBrowsePicture2.tbSplit = "13,11,13,11"
        Me.btnBrowsePicture2.tbText = "浏览"
        Me.btnBrowsePicture2.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBrowsePicture2.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture2.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture2.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture2.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture2.tbTextMouseDownPlace = 0
        Me.btnBrowsePicture2.tbToolTip = ""
        Me.btnBrowsePicture2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBrowsePicture2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBrowsePicture2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnBrowsePicture2.VisibleEx = True
        '
        'btnBrowsePicture1
        '
        Me.btnBrowsePicture1.BackColor = System.Drawing.Color.Transparent
        Me.btnBrowsePicture1.BindingForm = Nothing
        Me.btnBrowsePicture1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBrowsePicture1.ForeColor = System.Drawing.Color.Black
        Me.btnBrowsePicture1.Location = New System.Drawing.Point(396, 32)
        Me.btnBrowsePicture1.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnBrowsePicture1.Name = "btnBrowsePicture1"
        Me.btnBrowsePicture1.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBrowsePicture1.Selectable = True
        Me.btnBrowsePicture1.Size = New System.Drawing.Size(74, 23)
        Me.btnBrowsePicture1.TabIndex = 59
        Me.btnBrowsePicture1.tbAdriftIconWhenHover = False
        Me.btnBrowsePicture1.tbAutoSize = False
        Me.btnBrowsePicture1.tbAutoSizeEx = False
        Me.btnBrowsePicture1.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnBrowsePicture1.tbBackgroundImageState = ImageState.FourState
        Me.btnBrowsePicture1.tbBadgeNumber = 0
        Me.btnBrowsePicture1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBrowsePicture1.tbEndEllipsis = False
        Me.btnBrowsePicture1.tbIconHoldPlace = True
        Me.btnBrowsePicture1.tbIconImage = Nothing
        Me.btnBrowsePicture1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBrowsePicture1.tbIconImageState = ImageState.OneState
        Me.btnBrowsePicture1.tbIconMore = False
        Me.btnBrowsePicture1.tbIconMouseDown = Nothing
        Me.btnBrowsePicture1.tbIconMouseHover = Nothing
        Me.btnBrowsePicture1.tbIconMouseLeave = Nothing
        Me.btnBrowsePicture1.tbIconPlaceText = 2
        Me.btnBrowsePicture1.tbIconReadOnly = Nothing
        Me.btnBrowsePicture1.tbImageMouseDown = Nothing
        Me.btnBrowsePicture1.tbImageMouseHover = Nothing
        Me.btnBrowsePicture1.tbImageMouseLeave = Nothing
        Me.btnBrowsePicture1.tbReadOnly = False
        Me.btnBrowsePicture1.tbReadOnlyText = False
        Me.btnBrowsePicture1.tbShadow = False
        Me.btnBrowsePicture1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBrowsePicture1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBrowsePicture1.tbShowNew = False
        Me.btnBrowsePicture1.tbShowToolTipOnButton = False
        Me.btnBrowsePicture1.tbSplit = "13,11,13,11"
        Me.btnBrowsePicture1.tbText = "浏览"
        Me.btnBrowsePicture1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBrowsePicture1.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture1.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture1.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture1.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(73, Byte), Integer), CType(CType(96, Byte), Integer), CType(CType(112, Byte), Integer))
        Me.btnBrowsePicture1.tbTextMouseDownPlace = 0
        Me.btnBrowsePicture1.tbToolTip = ""
        Me.btnBrowsePicture1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBrowsePicture1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBrowsePicture1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnBrowsePicture1.VisibleEx = True
        '
        'btnReadTutorials
        '
        Me.btnReadTutorials.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnReadTutorials.BackColor = System.Drawing.Color.Transparent
        Me.btnReadTutorials.BindingForm = Nothing
        Me.btnReadTutorials.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnReadTutorials.Location = New System.Drawing.Point(138, 373)
        Me.btnReadTutorials.Name = "btnReadTutorials"
        Me.btnReadTutorials.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnReadTutorials.Selectable = True
        Me.btnReadTutorials.Size = New System.Drawing.Size(12, 12)
        Me.btnReadTutorials.TabIndex = 103
        Me.btnReadTutorials.tbAdriftIconWhenHover = False
        Me.btnReadTutorials.tbAutoSize = True
        Me.btnReadTutorials.tbAutoSizeEx = False
        Me.btnReadTutorials.tbBackgroundImage = My.Resources.btn_3_goto
        Me.btnReadTutorials.tbBackgroundImageState = ImageState.ThreeState
        Me.btnReadTutorials.tbBadgeNumber = 0
        Me.btnReadTutorials.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReadTutorials.tbEndEllipsis = False
        Me.btnReadTutorials.tbIconHoldPlace = True
        Me.btnReadTutorials.tbIconImage = Nothing
        Me.btnReadTutorials.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReadTutorials.tbIconImageState = ImageState.OneState
        Me.btnReadTutorials.tbIconMore = False
        Me.btnReadTutorials.tbIconMouseDown = Nothing
        Me.btnReadTutorials.tbIconMouseHover = Nothing
        Me.btnReadTutorials.tbIconMouseLeave = Nothing
        Me.btnReadTutorials.tbIconPlaceText = 2
        Me.btnReadTutorials.tbIconReadOnly = Nothing
        Me.btnReadTutorials.tbImageMouseDown = Nothing
        Me.btnReadTutorials.tbImageMouseHover = Nothing
        Me.btnReadTutorials.tbImageMouseLeave = Nothing
        Me.btnReadTutorials.tbReadOnly = False
        Me.btnReadTutorials.tbReadOnlyText = False
        Me.btnReadTutorials.tbShadow = False
        Me.btnReadTutorials.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReadTutorials.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReadTutorials.tbShowNew = False
        Me.btnReadTutorials.tbShowToolTipOnButton = False
        Me.btnReadTutorials.tbSplit = "3,3,3,3"
        Me.btnReadTutorials.tbText = ""
        Me.btnReadTutorials.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReadTutorials.tbTextColor = System.Drawing.Color.White
        Me.btnReadTutorials.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReadTutorials.tbTextColorDown = System.Drawing.Color.White
        Me.btnReadTutorials.tbTextColorHover = System.Drawing.Color.White
        Me.btnReadTutorials.tbTextMouseDownPlace = 0
        Me.btnReadTutorials.tbToolTip = ""
        Me.btnReadTutorials.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReadTutorials.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReadTutorials.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReadTutorials.VisibleEx = True
        '
        'pnlSelectText
        '
        Me.pnlSelectText.BackColor = System.Drawing.Color.Transparent
        Me.pnlSelectText.Controls.Add(Me.lblFont)
        Me.pnlSelectText.Controls.Add(Me.cboTextFont)
        Me.pnlSelectText.Controls.Add(Me.lblTextTooLongWarning)
        Me.pnlSelectText.Controls.Add(Me.txtCarrierString)
        Me.pnlSelectText.Controls.Add(Me.TbLabel11)
        Me.pnlSelectText.Controls.Add(Me.PictureBox5)
        Me.pnlSelectText.Controls.Add(Me.lblSelectDefinePicture)
        Me.pnlSelectText.Location = New System.Drawing.Point(0, 68)
        Me.pnlSelectText.Name = "pnlSelectText"
        Me.pnlSelectText.Size = New System.Drawing.Size(499, 124)
        Me.pnlSelectText.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlSelectText.TabIndex = 94
        Me.pnlSelectText.tbBackgroundImage = Nothing
        Me.pnlSelectText.tbShowWatermark = False
        Me.pnlSelectText.tbSplit = "0,0,0,0"
        Me.pnlSelectText.tbWatermark = Nothing
        Me.pnlSelectText.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlSelectText.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblFont
        '
        Me.lblFont.BackColor = System.Drawing.Color.Transparent
        Me.lblFont.Location = New System.Drawing.Point(17, 59)
        Me.lblFont.Name = "lblFont"
        Me.lblFont.Size = New System.Drawing.Size(130, 21)
        Me.lblFont.TabIndex = 93
        Me.lblFont.tbAdriftWhenHover = False
        Me.lblFont.tbAutoEllipsis = False
        Me.lblFont.tbAutoSize = False
        Me.lblFont.tbHideImage = False
        Me.lblFont.tbIconImage = Nothing
        Me.lblFont.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFont.tbIconPlaceText = 5
        Me.lblFont.tbShadow = False
        Me.lblFont.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFont.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFont.tbShowScrolling = False
        Me.lblFont.Text = "字体："
        Me.lblFont.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cboTextFont
        '
        Me.cboTextFont.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cboTextFont.FormattingEnabled = True
        Me.cboTextFont.Location = New System.Drawing.Point(153, 59)
        Me.cboTextFont.Name = "cboTextFont"
        Me.cboTextFont.Size = New System.Drawing.Size(180, 20)
        Me.cboTextFont.TabIndex = 92
        '
        'lblTextTooLongWarning
        '
        Me.lblTextTooLongWarning.BackColor = System.Drawing.Color.Transparent
        Me.lblTextTooLongWarning.ForeColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.lblTextTooLongWarning.Location = New System.Drawing.Point(149, 87)
        Me.lblTextTooLongWarning.Name = "lblTextTooLongWarning"
        Me.lblTextTooLongWarning.Size = New System.Drawing.Size(345, 21)
        Me.lblTextTooLongWarning.TabIndex = 91
        Me.lblTextTooLongWarning.tbAdriftWhenHover = False
        Me.lblTextTooLongWarning.tbAutoEllipsis = False
        Me.lblTextTooLongWarning.tbAutoSize = False
        Me.lblTextTooLongWarning.tbHideImage = False
        Me.lblTextTooLongWarning.tbIconImage = Nothing
        Me.lblTextTooLongWarning.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTextTooLongWarning.tbIconPlaceText = 5
        Me.lblTextTooLongWarning.tbShadow = False
        Me.lblTextTooLongWarning.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTextTooLongWarning.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTextTooLongWarning.tbShowScrolling = False
        Me.lblTextTooLongWarning.Text = "警告：您输入的文字过长，可能无法正常显示"
        Me.lblTextTooLongWarning.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTextTooLongWarning.Visible = False
        '
        'txtCarrierString
        '
        Me.txtCarrierString.BackColor = System.Drawing.Color.White
        Me.txtCarrierString.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtCarrierString.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtCarrierString.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtCarrierString.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtCarrierString.ForeColor = System.Drawing.Color.Black
        Me.txtCarrierString.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtCarrierString.Location = New System.Drawing.Point(153, 10)
        Me.txtCarrierString.MaxLength = 32767
        Me.txtCarrierString.Name = "txtCarrierString"
        Me.txtCarrierString.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtCarrierString.Size = New System.Drawing.Size(221, 23)
        Me.txtCarrierString.TabIndex = 90
        Me.txtCarrierString.Tag = Nothing
        Me.txtCarrierString.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtCarrierString.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtCarrierString.tbSelMark = True
        Me.txtCarrierString.tbTextBind = ""
        Me.txtCarrierString.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtCarrierString.TextFormat = tbTextFormat.Normal
        Me.txtCarrierString.TextTip = ""
        '
        'TbLabel11
        '
        Me.TbLabel11.BackColor = System.Drawing.Color.Transparent
        Me.TbLabel11.ForeColor = System.Drawing.Color.FromArgb(CType(CType(141, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(149, Byte), Integer))
        Me.TbLabel11.Location = New System.Drawing.Point(151, 36)
        Me.TbLabel11.Name = "TbLabel11"
        Me.TbLabel11.Size = New System.Drawing.Size(295, 21)
        Me.TbLabel11.TabIndex = 89
        Me.TbLabel11.tbAdriftWhenHover = False
        Me.TbLabel11.tbAutoEllipsis = False
        Me.TbLabel11.tbAutoSize = False
        Me.TbLabel11.tbHideImage = False
        Me.TbLabel11.tbIconImage = Nothing
        Me.TbLabel11.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel11.tbIconPlaceText = 5
        Me.TbLabel11.tbShadow = False
        Me.TbLabel11.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel11.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel11.tbShowScrolling = False
        Me.TbLabel11.Text = "任意修改左上角运营商的文字内容"
        Me.TbLabel11.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'PictureBox5
        '
        Me.PictureBox5.Image = CType(resources.GetObject("PictureBox5.Image"), System.Drawing.Image)
        Me.PictureBox5.Location = New System.Drawing.Point(0, -4)
        Me.PictureBox5.Name = "PictureBox5"
        Me.PictureBox5.Size = New System.Drawing.Size(37, 48)
        Me.PictureBox5.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.PictureBox5.TabIndex = 88
        Me.PictureBox5.TabStop = False
        '
        'lblSelectDefinePicture
        '
        Me.lblSelectDefinePicture.BackColor = System.Drawing.Color.Transparent
        Me.lblSelectDefinePicture.Location = New System.Drawing.Point(39, 10)
        Me.lblSelectDefinePicture.Name = "lblSelectDefinePicture"
        Me.lblSelectDefinePicture.Size = New System.Drawing.Size(108, 21)
        Me.lblSelectDefinePicture.TabIndex = 63
        Me.lblSelectDefinePicture.tbAdriftWhenHover = False
        Me.lblSelectDefinePicture.tbAutoEllipsis = False
        Me.lblSelectDefinePicture.tbAutoSize = False
        Me.lblSelectDefinePicture.tbHideImage = False
        Me.lblSelectDefinePicture.tbIconImage = Nothing
        Me.lblSelectDefinePicture.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSelectDefinePicture.tbIconPlaceText = 5
        Me.lblSelectDefinePicture.tbShadow = False
        Me.lblSelectDefinePicture.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSelectDefinePicture.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSelectDefinePicture.tbShowScrolling = False
        Me.lblSelectDefinePicture.Text = "填写文字内容："
        Me.lblSelectDefinePicture.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'cprFSO
        '
        Me.cprFSO.BackgroundImage = My.Resources.tool_fso_background
        Me.cprFSO.CarrierText = ""
        Me.cprFSO.ContextImage = Nothing
        Me.cprFSO.IsFSO = True
        Me.cprFSO.IsX2 = True
        Me.cprFSO.Location = New System.Drawing.Point(151, 226)
        Me.cprFSO.Name = "cprFSO"
        Me.cprFSO.Size = New System.Drawing.Size(319, 20)
        Me.cprFSO.TabIndex = 102
        Me.cprFSO.Text = "CarrierPreview1"
        Me.cprFSO.TextFont = "黑体"
        '
        'btnHorizonSplit
        '
        Me.btnHorizonSplit.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnHorizonSplit.BackColor = System.Drawing.Color.Transparent
        Me.btnHorizonSplit.BindingForm = Nothing
        Me.btnHorizonSplit.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnHorizonSplit.Location = New System.Drawing.Point(-1, 196)
        Me.btnHorizonSplit.Name = "btnHorizonSplit"
        Me.btnHorizonSplit.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnHorizonSplit.Selectable = True
        Me.btnHorizonSplit.Size = New System.Drawing.Size(500, 2)
        Me.btnHorizonSplit.TabIndex = 101
        Me.btnHorizonSplit.tbAdriftIconWhenHover = False
        Me.btnHorizonSplit.tbAutoSize = False
        Me.btnHorizonSplit.tbAutoSizeEx = False
        Me.btnHorizonSplit.tbBackgroundImage = CType(resources.GetObject("btnHorizonSplit.tbBackgroundImage"), System.Drawing.Image)
        Me.btnHorizonSplit.tbBackgroundImageState = ImageState.OneState
        Me.btnHorizonSplit.tbBadgeNumber = 0
        Me.btnHorizonSplit.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnHorizonSplit.tbEndEllipsis = False
        Me.btnHorizonSplit.tbIconHoldPlace = True
        Me.btnHorizonSplit.tbIconImage = Nothing
        Me.btnHorizonSplit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnHorizonSplit.tbIconImageState = ImageState.OneState
        Me.btnHorizonSplit.tbIconMore = False
        Me.btnHorizonSplit.tbIconMouseDown = Nothing
        Me.btnHorizonSplit.tbIconMouseHover = Nothing
        Me.btnHorizonSplit.tbIconMouseLeave = Nothing
        Me.btnHorizonSplit.tbIconPlaceText = 2
        Me.btnHorizonSplit.tbIconReadOnly = Nothing
        Me.btnHorizonSplit.tbImageMouseDown = Nothing
        Me.btnHorizonSplit.tbImageMouseHover = Nothing
        Me.btnHorizonSplit.tbImageMouseLeave = Nothing
        Me.btnHorizonSplit.tbReadOnly = False
        Me.btnHorizonSplit.tbReadOnlyText = False
        Me.btnHorizonSplit.tbShadow = False
        Me.btnHorizonSplit.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnHorizonSplit.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnHorizonSplit.tbShowNew = False
        Me.btnHorizonSplit.tbShowToolTipOnButton = False
        Me.btnHorizonSplit.tbSplit = "35,0,35,0"
        Me.btnHorizonSplit.tbText = ""
        Me.btnHorizonSplit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnHorizonSplit.tbTextColor = System.Drawing.Color.White
        Me.btnHorizonSplit.tbTextColorDisable = System.Drawing.Color.White
        Me.btnHorizonSplit.tbTextColorDown = System.Drawing.Color.White
        Me.btnHorizonSplit.tbTextColorHover = System.Drawing.Color.White
        Me.btnHorizonSplit.tbTextMouseDownPlace = 0
        Me.btnHorizonSplit.tbToolTip = ""
        Me.btnHorizonSplit.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnHorizonSplit.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnHorizonSplit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnHorizonSplit.VisibleEx = True
        '
        'lblReadTutorials
        '
        Me.lblReadTutorials.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblReadTutorials.AutoSize = True
        Me.lblReadTutorials.DisabledLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblReadTutorials.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lblReadTutorials.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblReadTutorials.Location = New System.Drawing.Point(8, 372)
        Me.lblReadTutorials.Name = "lblReadTutorials"
        Me.lblReadTutorials.Size = New System.Drawing.Size(125, 12)
        Me.lblReadTutorials.TabIndex = 100
        Me.lblReadTutorials.TabStop = True
        Me.lblReadTutorials.Text = "自定义运营商图标教程"
        Me.lblReadTutorials.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlMessage
        '
        Me.pnlMessage.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.pnlMessage.Controls.Add(Me.picResultTip)
        Me.pnlMessage.Controls.Add(Me.lblMessage)
        Me.pnlMessage.Location = New System.Drawing.Point(4, 307)
        Me.pnlMessage.Name = "pnlMessage"
        Me.pnlMessage.Size = New System.Drawing.Size(478, 34)
        Me.pnlMessage.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlMessage.TabIndex = 99
        Me.pnlMessage.tbBackgroundImage = My.Resources.tool_label_message
        Me.pnlMessage.tbShowWatermark = False
        Me.pnlMessage.tbSplit = "9,9,9,9"
        Me.pnlMessage.tbWatermark = Nothing
        Me.pnlMessage.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMessage.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'picResultTip
        '
        Me.picResultTip.Image = My.Resources.tool_tip_success
        Me.picResultTip.Location = New System.Drawing.Point(4, 4)
        Me.picResultTip.Name = "picResultTip"
        Me.picResultTip.Size = New System.Drawing.Size(24, 26)
        Me.picResultTip.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picResultTip.TabIndex = 84
        Me.picResultTip.TabStop = False
        '
        'lblMessage
        '
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(116, Byte), Integer), CType(CType(91, Byte), Integer), CType(CType(1, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(34, 7)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(437, 21)
        Me.lblMessage.TabIndex = 83
        Me.lblMessage.Text = "保存成功，请先进入飞行模式后再关闭，以启用新图标。"
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'TbButton3
        '
        Me.TbButton3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.TbButton3.BackColor = System.Drawing.Color.Transparent
        Me.TbButton3.BindingForm = Nothing
        Me.TbButton3.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TbButton3.Location = New System.Drawing.Point(-51, -9)
        Me.TbButton3.Name = "TbButton3"
        Me.TbButton3.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton3.Selectable = True
        Me.TbButton3.Size = New System.Drawing.Size(600, 2)
        Me.TbButton3.TabIndex = 98
        Me.TbButton3.tbAdriftIconWhenHover = False
        Me.TbButton3.tbAutoSize = False
        Me.TbButton3.tbAutoSizeEx = False
        Me.TbButton3.tbBackgroundImage = CType(resources.GetObject("TbButton3.tbBackgroundImage"), System.Drawing.Image)
        Me.TbButton3.tbBackgroundImageState = ImageState.OneState
        Me.TbButton3.tbBadgeNumber = 0
        Me.TbButton3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton3.tbEndEllipsis = False
        Me.TbButton3.tbIconHoldPlace = True
        Me.TbButton3.tbIconImage = Nothing
        Me.TbButton3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton3.tbIconImageState = ImageState.OneState
        Me.TbButton3.tbIconMore = False
        Me.TbButton3.tbIconMouseDown = Nothing
        Me.TbButton3.tbIconMouseHover = Nothing
        Me.TbButton3.tbIconMouseLeave = Nothing
        Me.TbButton3.tbIconPlaceText = 2
        Me.TbButton3.tbIconReadOnly = Nothing
        Me.TbButton3.tbImageMouseDown = Nothing
        Me.TbButton3.tbImageMouseHover = Nothing
        Me.TbButton3.tbImageMouseLeave = Nothing
        Me.TbButton3.tbReadOnly = False
        Me.TbButton3.tbReadOnlyText = False
        Me.TbButton3.tbShadow = False
        Me.TbButton3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton3.tbShowNew = False
        Me.TbButton3.tbShowToolTipOnButton = False
        Me.TbButton3.tbSplit = "35,0,35,0"
        Me.TbButton3.tbText = ""
        Me.TbButton3.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton3.tbTextColor = System.Drawing.Color.White
        Me.TbButton3.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton3.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton3.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton3.tbTextMouseDownPlace = 0
        Me.TbButton3.tbToolTip = ""
        Me.TbButton3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton3.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton3.VisibleEx = True
        '
        'btnFirstStep
        '
        Me.btnFirstStep.BackColor = System.Drawing.Color.Transparent
        Me.btnFirstStep.BindingForm = Nothing
        Me.btnFirstStep.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFirstStep.ForeColor = System.Drawing.Color.Black
        Me.btnFirstStep.Location = New System.Drawing.Point(308, 367)
        Me.btnFirstStep.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnFirstStep.Name = "btnFirstStep"
        Me.btnFirstStep.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFirstStep.Selectable = True
        Me.btnFirstStep.Size = New System.Drawing.Size(74, 23)
        Me.btnFirstStep.TabIndex = 96
        Me.btnFirstStep.tbAdriftIconWhenHover = False
        Me.btnFirstStep.tbAutoSize = False
        Me.btnFirstStep.tbAutoSizeEx = False
        Me.btnFirstStep.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnFirstStep.tbBackgroundImageState = ImageState.FourState
        Me.btnFirstStep.tbBadgeNumber = 0
        Me.btnFirstStep.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFirstStep.tbEndEllipsis = False
        Me.btnFirstStep.tbIconHoldPlace = True
        Me.btnFirstStep.tbIconImage = Nothing
        Me.btnFirstStep.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFirstStep.tbIconImageState = ImageState.OneState
        Me.btnFirstStep.tbIconMore = False
        Me.btnFirstStep.tbIconMouseDown = Nothing
        Me.btnFirstStep.tbIconMouseHover = Nothing
        Me.btnFirstStep.tbIconMouseLeave = Nothing
        Me.btnFirstStep.tbIconPlaceText = 2
        Me.btnFirstStep.tbIconReadOnly = Nothing
        Me.btnFirstStep.tbImageMouseDown = Nothing
        Me.btnFirstStep.tbImageMouseHover = Nothing
        Me.btnFirstStep.tbImageMouseLeave = Nothing
        Me.btnFirstStep.tbReadOnly = False
        Me.btnFirstStep.tbReadOnlyText = False
        Me.btnFirstStep.tbShadow = False
        Me.btnFirstStep.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFirstStep.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFirstStep.tbShowNew = False
        Me.btnFirstStep.tbShowToolTipOnButton = False
        Me.btnFirstStep.tbSplit = "13,11,13,11"
        Me.btnFirstStep.tbText = "上一步"
        Me.btnFirstStep.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFirstStep.tbTextColor = System.Drawing.Color.Black
        Me.btnFirstStep.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnFirstStep.tbTextColorDown = System.Drawing.Color.Black
        Me.btnFirstStep.tbTextColorHover = System.Drawing.Color.Black
        Me.btnFirstStep.tbTextMouseDownPlace = 0
        Me.btnFirstStep.tbToolTip = ""
        Me.btnFirstStep.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFirstStep.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFirstStep.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnFirstStep.VisibleEx = True
        '
        'picStep2
        '
        Me.picStep2.Image = CType(resources.GetObject("picStep2.Image"), System.Drawing.Image)
        Me.picStep2.Location = New System.Drawing.Point(0, 4)
        Me.picStep2.Name = "picStep2"
        Me.picStep2.Size = New System.Drawing.Size(37, 48)
        Me.picStep2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picStep2.TabIndex = 95
        Me.picStep2.TabStop = False
        '
        'rdoSetPicture
        '
        Me.rdoSetPicture.BackColor = System.Drawing.Color.Transparent
        Me.rdoSetPicture.Location = New System.Drawing.Point(281, 17)
        Me.rdoSetPicture.Name = "rdoSetPicture"
        Me.rdoSetPicture.Size = New System.Drawing.Size(111, 21)
        Me.rdoSetPicture.TabIndex = 92
        Me.rdoSetPicture.Text = "自定义图片"
        Me.rdoSetPicture.UseVisualStyleBackColor = False
        '
        'rdoSetText
        '
        Me.rdoSetText.BackColor = System.Drawing.Color.Transparent
        Me.rdoSetText.Location = New System.Drawing.Point(171, 17)
        Me.rdoSetText.Name = "rdoSetText"
        Me.rdoSetText.Size = New System.Drawing.Size(104, 21)
        Me.rdoSetText.TabIndex = 91
        Me.rdoSetText.Text = "自定义文字"
        Me.rdoSetText.UseVisualStyleBackColor = False
        '
        'lblReplaceMethod
        '
        Me.lblReplaceMethod.BackColor = System.Drawing.Color.Transparent
        Me.lblReplaceMethod.Location = New System.Drawing.Point(40, 17)
        Me.lblReplaceMethod.Name = "lblReplaceMethod"
        Me.lblReplaceMethod.Size = New System.Drawing.Size(124, 21)
        Me.lblReplaceMethod.TabIndex = 90
        Me.lblReplaceMethod.tbAdriftWhenHover = False
        Me.lblReplaceMethod.tbAutoEllipsis = False
        Me.lblReplaceMethod.tbAutoSize = False
        Me.lblReplaceMethod.tbHideImage = False
        Me.lblReplaceMethod.tbIconImage = Nothing
        Me.lblReplaceMethod.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblReplaceMethod.tbIconPlaceText = 5
        Me.lblReplaceMethod.tbShadow = False
        Me.lblReplaceMethod.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblReplaceMethod.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblReplaceMethod.tbShowScrolling = False
        Me.lblReplaceMethod.Text = "选择替换方式："
        Me.lblReplaceMethod.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblTSPreview
        '
        Me.lblTSPreview.BackColor = System.Drawing.Color.Transparent
        Me.lblTSPreview.ForeColor = System.Drawing.Color.FromArgb(CType(CType(74, Byte), Integer), CType(CType(100, Byte), Integer), CType(CType(123, Byte), Integer))
        Me.lblTSPreview.Location = New System.Drawing.Point(8, 266)
        Me.lblTSPreview.Name = "lblTSPreview"
        Me.lblTSPreview.Size = New System.Drawing.Size(139, 21)
        Me.lblTSPreview.TabIndex = 89
        Me.lblTSPreview.tbAdriftWhenHover = False
        Me.lblTSPreview.tbAutoEllipsis = False
        Me.lblTSPreview.tbAutoSize = False
        Me.lblTSPreview.tbHideImage = False
        Me.lblTSPreview.tbIconImage = Nothing
        Me.lblTSPreview.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTSPreview.tbIconPlaceText = 5
        Me.lblTSPreview.tbShadow = False
        Me.lblTSPreview.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTSPreview.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTSPreview.tbShowScrolling = False
        Me.lblTSPreview.Text = "通知中心："
        Me.lblTSPreview.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblFSOPreview
        '
        Me.lblFSOPreview.BackColor = System.Drawing.Color.Transparent
        Me.lblFSOPreview.ForeColor = System.Drawing.Color.FromArgb(CType(CType(74, Byte), Integer), CType(CType(100, Byte), Integer), CType(CType(123, Byte), Integer))
        Me.lblFSOPreview.Location = New System.Drawing.Point(3, 226)
        Me.lblFSOPreview.Name = "lblFSOPreview"
        Me.lblFSOPreview.Size = New System.Drawing.Size(144, 21)
        Me.lblFSOPreview.TabIndex = 88
        Me.lblFSOPreview.tbAdriftWhenHover = False
        Me.lblFSOPreview.tbAutoEllipsis = False
        Me.lblFSOPreview.tbAutoSize = False
        Me.lblFSOPreview.tbHideImage = False
        Me.lblFSOPreview.tbIconImage = Nothing
        Me.lblFSOPreview.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFSOPreview.tbIconPlaceText = 5
        Me.lblFSOPreview.tbShadow = False
        Me.lblFSOPreview.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFSOPreview.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFSOPreview.tbShowScrolling = False
        Me.lblFSOPreview.Text = "顶部状态栏："
        Me.lblFSOPreview.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblPreview
        '
        Me.lblPreview.BackColor = System.Drawing.Color.Transparent
        Me.lblPreview.ForeColor = System.Drawing.Color.FromArgb(CType(CType(74, Byte), Integer), CType(CType(100, Byte), Integer), CType(CType(123, Byte), Integer))
        Me.lblPreview.Location = New System.Drawing.Point(8, 203)
        Me.lblPreview.Name = "lblPreview"
        Me.lblPreview.Size = New System.Drawing.Size(44, 21)
        Me.lblPreview.TabIndex = 87
        Me.lblPreview.tbAdriftWhenHover = False
        Me.lblPreview.tbAutoEllipsis = False
        Me.lblPreview.tbAutoSize = False
        Me.lblPreview.tbHideImage = False
        Me.lblPreview.tbIconImage = Nothing
        Me.lblPreview.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lblPreview.tbIconPlaceText = 5
        Me.lblPreview.tbShadow = False
        Me.lblPreview.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblPreview.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblPreview.tbShowScrolling = False
        Me.lblPreview.Text = "预览"
        Me.lblPreview.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cprTS
        '
        Me.cprTS.BackgroundImage = My.Resources.tool_ts_background
        Me.cprTS.CarrierText = ""
        Me.cprTS.ContextImage = Nothing
        Me.cprTS.Font = New System.Drawing.Font("华文细黑", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cprTS.ForeColor = System.Drawing.Color.White
        Me.cprTS.IsFSO = False
        Me.cprTS.IsX2 = True
        Me.cprTS.Location = New System.Drawing.Point(151, 266)
        Me.cprTS.Name = "cprTS"
        Me.cprTS.Size = New System.Drawing.Size(319, 20)
        Me.cprTS.TabIndex = 84
        Me.cprTS.Text = "CarrierPreview2"
        Me.cprTS.TextFont = "黑体"
        '
        'btnOK
        '
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(396, 367)
        Me.btnOK.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(74, 23)
        Me.btnOK.TabIndex = 80
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "应用"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.VisibleEx = True
        '
        'pnlStep1
        '
        Me.pnlStep1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlStep1.BackColor = System.Drawing.Color.Transparent
        Me.pnlStep1.Controls.Add(Me.cboCarrierName)
        Me.pnlStep1.Controls.Add(Me.lblReplaceType)
        Me.pnlStep1.Controls.Add(Me.TbPanel2)
        Me.pnlStep1.Controls.Add(Me.txtCarrierVersion)
        Me.pnlStep1.Controls.Add(Me.picArraw)
        Me.pnlStep1.Controls.Add(Me.PictureBox4)
        Me.pnlStep1.Controls.Add(Me.TbLabel9)
        Me.pnlStep1.Controls.Add(Me.lblErrorMessage)
        Me.pnlStep1.Controls.Add(Me.TbLabel10)
        Me.pnlStep1.Controls.Add(Me.btnNextStep)
        Me.pnlStep1.Location = New System.Drawing.Point(34, 109)
        Me.pnlStep1.Name = "pnlStep1"
        Me.pnlStep1.Size = New System.Drawing.Size(500, 397)
        Me.pnlStep1.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlStep1.TabIndex = 84
        Me.pnlStep1.tbBackgroundImage = Nothing
        Me.pnlStep1.tbShowWatermark = False
        Me.pnlStep1.tbSplit = "0,0,0,0"
        Me.pnlStep1.tbWatermark = Nothing
        Me.pnlStep1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlStep1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'cboCarrierName
        '
        Me.cboCarrierName.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cboCarrierName.FormattingEnabled = True
        Me.cboCarrierName.Items.AddRange(New Object() {"中国移动", "中国联通"})
        Me.cboCarrierName.Location = New System.Drawing.Point(132, 15)
        Me.cboCarrierName.Name = "cboCarrierName"
        Me.cboCarrierName.Size = New System.Drawing.Size(249, 20)
        Me.cboCarrierName.TabIndex = 103
        '
        'lblReplaceType
        '
        Me.lblReplaceType.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblReplaceType.BackColor = System.Drawing.Color.Transparent
        Me.lblReplaceType.Location = New System.Drawing.Point(39, 15)
        Me.lblReplaceType.Name = "lblReplaceType"
        Me.lblReplaceType.Size = New System.Drawing.Size(93, 21)
        Me.lblReplaceType.TabIndex = 102
        Me.lblReplaceType.tbAdriftWhenHover = False
        Me.lblReplaceType.tbAutoEllipsis = False
        Me.lblReplaceType.tbAutoSize = False
        Me.lblReplaceType.tbHideImage = False
        Me.lblReplaceType.tbIconImage = Nothing
        Me.lblReplaceType.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblReplaceType.tbIconPlaceText = 5
        Me.lblReplaceType.tbShadow = False
        Me.lblReplaceType.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblReplaceType.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblReplaceType.tbShowScrolling = False
        Me.lblReplaceType.Text = "运营商："
        Me.lblReplaceType.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'TbPanel2
        '
        Me.TbPanel2.Controls.Add(Me.picTips)
        Me.TbPanel2.Controls.Add(Me.picDescribtion)
        Me.TbPanel2.Location = New System.Drawing.Point(78, 98)
        Me.TbPanel2.Name = "TbPanel2"
        Me.TbPanel2.Size = New System.Drawing.Size(322, 243)
        Me.TbPanel2.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.TbPanel2.TabIndex = 101
        Me.TbPanel2.tbBackgroundImage = My.Resources.tool_label_demo
        Me.TbPanel2.tbShowWatermark = False
        Me.TbPanel2.tbSplit = "9,40,9,9"
        Me.TbPanel2.tbWatermark = Nothing
        Me.TbPanel2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel2.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'picTips
        '
        Me.picTips.Image = My.Resources.tool_tip
        Me.picTips.Location = New System.Drawing.Point(7, 3)
        Me.picTips.Name = "picTips"
        Me.picTips.Size = New System.Drawing.Size(309, 29)
        Me.picTips.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picTips.TabIndex = 100
        Me.picTips.TabStop = False
        '
        'picDescribtion
        '
        Me.picDescribtion.Image = My.Resources.tool_carrier_version
        Me.picDescribtion.Location = New System.Drawing.Point(7, 41)
        Me.picDescribtion.Name = "picDescribtion"
        Me.picDescribtion.Size = New System.Drawing.Size(309, 190)
        Me.picDescribtion.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picDescribtion.TabIndex = 99
        Me.picDescribtion.TabStop = False
        '
        'txtCarrierVersion
        '
        Me.txtCarrierVersion.BackColor = System.Drawing.Color.White
        Me.txtCarrierVersion.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtCarrierVersion.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtCarrierVersion.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtCarrierVersion.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtCarrierVersion.ForeColor = System.Drawing.Color.Black
        Me.txtCarrierVersion.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtCarrierVersion.Location = New System.Drawing.Point(132, 44)
        Me.txtCarrierVersion.MaxLength = 32767
        Me.txtCarrierVersion.Name = "txtCarrierVersion"
        Me.txtCarrierVersion.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtCarrierVersion.Size = New System.Drawing.Size(249, 21)
        Me.txtCarrierVersion.TabIndex = 100
        Me.txtCarrierVersion.Tag = Nothing
        Me.txtCarrierVersion.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtCarrierVersion.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtCarrierVersion.tbSelMark = True
        Me.txtCarrierVersion.tbTextBind = ""
        Me.txtCarrierVersion.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtCarrierVersion.TextFormat = tbTextFormat.Normal
        Me.txtCarrierVersion.TextTip = ""
        '
        'picArraw
        '
        Me.picArraw.Image = My.Resources.tool_carrier_arraw
        Me.picArraw.Location = New System.Drawing.Point(402, 60)
        Me.picArraw.Name = "picArraw"
        Me.picArraw.Size = New System.Drawing.Size(83, 265)
        Me.picArraw.TabIndex = 98
        Me.picArraw.TabStop = False
        '
        'PictureBox4
        '
        Me.PictureBox4.Image = CType(resources.GetObject("PictureBox4.Image"), System.Drawing.Image)
        Me.PictureBox4.Location = New System.Drawing.Point(0, 6)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(37, 44)
        Me.PictureBox4.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.PictureBox4.TabIndex = 96
        Me.PictureBox4.TabStop = False
        '
        'TbLabel9
        '
        Me.TbLabel9.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.TbLabel9.AutoEllipsis = True
        Me.TbLabel9.BackColor = System.Drawing.Color.Transparent
        Me.TbLabel9.ForeColor = System.Drawing.Color.Black
        Me.TbLabel9.Location = New System.Drawing.Point(131, 70)
        Me.TbLabel9.Name = "TbLabel9"
        Me.TbLabel9.Size = New System.Drawing.Size(275, 21)
        Me.TbLabel9.TabIndex = 88
        Me.TbLabel9.tbAdriftWhenHover = False
        Me.TbLabel9.tbAutoEllipsis = True
        Me.TbLabel9.tbAutoSize = False
        Me.TbLabel9.tbHideImage = False
        Me.TbLabel9.tbIconImage = Nothing
        Me.TbLabel9.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel9.tbIconPlaceText = 5
        Me.TbLabel9.tbShadow = False
        Me.TbLabel9.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel9.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel9.tbShowScrolling = False
        Me.TbLabel9.Text = "示例：中国联通14.1  ->   只要填写  14.1"
        Me.TbLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblErrorMessage
        '
        Me.lblErrorMessage.AutoEllipsis = True
        Me.lblErrorMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblErrorMessage.Location = New System.Drawing.Point(3, 368)
        Me.lblErrorMessage.Name = "lblErrorMessage"
        Me.lblErrorMessage.Size = New System.Drawing.Size(354, 23)
        Me.lblErrorMessage.TabIndex = 86
        Me.lblErrorMessage.tbAdriftWhenHover = False
        Me.lblErrorMessage.tbAutoEllipsis = True
        Me.lblErrorMessage.tbAutoSize = False
        Me.lblErrorMessage.tbHideImage = False
        Me.lblErrorMessage.tbIconImage = Nothing
        Me.lblErrorMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblErrorMessage.tbIconPlaceText = 5
        Me.lblErrorMessage.tbShadow = False
        Me.lblErrorMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblErrorMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblErrorMessage.tbShowScrolling = False
        Me.lblErrorMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'TbLabel10
        '
        Me.TbLabel10.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.TbLabel10.BackColor = System.Drawing.Color.Transparent
        Me.TbLabel10.Location = New System.Drawing.Point(7, 44)
        Me.TbLabel10.Name = "TbLabel10"
        Me.TbLabel10.Size = New System.Drawing.Size(125, 21)
        Me.TbLabel10.TabIndex = 85
        Me.TbLabel10.tbAdriftWhenHover = False
        Me.TbLabel10.tbAutoEllipsis = False
        Me.TbLabel10.tbAutoSize = False
        Me.TbLabel10.tbHideImage = False
        Me.TbLabel10.tbIconImage = Nothing
        Me.TbLabel10.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel10.tbIconPlaceText = 5
        Me.TbLabel10.tbShadow = False
        Me.TbLabel10.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel10.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel10.tbShowScrolling = False
        Me.TbLabel10.Text = "运营商版本："
        Me.TbLabel10.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'btnNextStep
        '
        Me.btnNextStep.BackColor = System.Drawing.Color.Transparent
        Me.btnNextStep.BindingForm = Nothing
        Me.btnNextStep.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnNextStep.Location = New System.Drawing.Point(396, 368)
        Me.btnNextStep.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnNextStep.Name = "btnNextStep"
        Me.btnNextStep.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnNextStep.Selectable = True
        Me.btnNextStep.Size = New System.Drawing.Size(74, 23)
        Me.btnNextStep.TabIndex = 84
        Me.btnNextStep.tbAdriftIconWhenHover = False
        Me.btnNextStep.tbAutoSize = False
        Me.btnNextStep.tbAutoSizeEx = False
        Me.btnNextStep.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnNextStep.tbBackgroundImageState = ImageState.FourState
        Me.btnNextStep.tbBadgeNumber = 0
        Me.btnNextStep.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnNextStep.tbEndEllipsis = False
        Me.btnNextStep.tbIconHoldPlace = True
        Me.btnNextStep.tbIconImage = Nothing
        Me.btnNextStep.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNextStep.tbIconImageState = ImageState.OneState
        Me.btnNextStep.tbIconMore = False
        Me.btnNextStep.tbIconMouseDown = Nothing
        Me.btnNextStep.tbIconMouseHover = Nothing
        Me.btnNextStep.tbIconMouseLeave = Nothing
        Me.btnNextStep.tbIconPlaceText = 2
        Me.btnNextStep.tbIconReadOnly = Nothing
        Me.btnNextStep.tbImageMouseDown = Nothing
        Me.btnNextStep.tbImageMouseHover = Nothing
        Me.btnNextStep.tbImageMouseLeave = Nothing
        Me.btnNextStep.tbReadOnly = False
        Me.btnNextStep.tbReadOnlyText = False
        Me.btnNextStep.tbShadow = False
        Me.btnNextStep.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnNextStep.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnNextStep.tbShowNew = False
        Me.btnNextStep.tbShowToolTipOnButton = False
        Me.btnNextStep.tbSplit = "13,11,13,11"
        Me.btnNextStep.tbText = "下一步"
        Me.btnNextStep.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNextStep.tbTextColor = System.Drawing.Color.White
        Me.btnNextStep.tbTextColorDisable = System.Drawing.Color.White
        Me.btnNextStep.tbTextColorDown = System.Drawing.Color.White
        Me.btnNextStep.tbTextColorHover = System.Drawing.Color.White
        Me.btnNextStep.tbTextMouseDownPlace = 0
        Me.btnNextStep.tbToolTip = ""
        Me.btnNextStep.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnNextStep.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnNextStep.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnNextStep.VisibleEx = True
        '
        'btnToDefault
        '
        Me.btnToDefault.BackColor = System.Drawing.Color.Transparent
        Me.btnToDefault.BindingForm = Nothing
        Me.btnToDefault.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnToDefault.ForeColor = System.Drawing.Color.Black
        Me.btnToDefault.Location = New System.Drawing.Point(433, 10)
        Me.btnToDefault.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnToDefault.Name = "btnToDefault"
        Me.btnToDefault.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnToDefault.Selectable = True
        Me.btnToDefault.Size = New System.Drawing.Size(78, 23)
        Me.btnToDefault.TabIndex = 81
        Me.btnToDefault.tbAdriftIconWhenHover = False
        Me.btnToDefault.tbAutoSize = False
        Me.btnToDefault.tbAutoSizeEx = False
        Me.btnToDefault.tbBackgroundImage = CType(resources.GetObject("btnToDefault.tbBackgroundImage"), System.Drawing.Image)
        Me.btnToDefault.tbBackgroundImageState = ImageState.FourState
        Me.btnToDefault.tbBadgeNumber = 0
        Me.btnToDefault.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnToDefault.tbEndEllipsis = False
        Me.btnToDefault.tbIconHoldPlace = True
        Me.btnToDefault.tbIconImage = Nothing
        Me.btnToDefault.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnToDefault.tbIconImageState = ImageState.OneState
        Me.btnToDefault.tbIconMore = False
        Me.btnToDefault.tbIconMouseDown = Nothing
        Me.btnToDefault.tbIconMouseHover = Nothing
        Me.btnToDefault.tbIconMouseLeave = Nothing
        Me.btnToDefault.tbIconPlaceText = 2
        Me.btnToDefault.tbIconReadOnly = Nothing
        Me.btnToDefault.tbImageMouseDown = Nothing
        Me.btnToDefault.tbImageMouseHover = Nothing
        Me.btnToDefault.tbImageMouseLeave = Nothing
        Me.btnToDefault.tbReadOnly = False
        Me.btnToDefault.tbReadOnlyText = False
        Me.btnToDefault.tbShadow = False
        Me.btnToDefault.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnToDefault.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnToDefault.tbShowNew = False
        Me.btnToDefault.tbShowToolTipOnButton = False
        Me.btnToDefault.tbSplit = "9,9,9,9"
        Me.btnToDefault.tbText = "恢复默认"
        Me.btnToDefault.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnToDefault.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(207, Byte), Integer))
        Me.btnToDefault.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(207, Byte), Integer))
        Me.btnToDefault.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(207, Byte), Integer))
        Me.btnToDefault.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(207, Byte), Integer))
        Me.btnToDefault.tbTextMouseDownPlace = 0
        Me.btnToDefault.tbToolTip = ""
        Me.btnToDefault.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnToDefault.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnToDefault.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnToDefault.VisibleEx = True
        '
        'TbPanel1
        '
        Me.TbPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TbPanel1.Controls.Add(Me.lblDescribtion)
        Me.TbPanel1.Controls.Add(Me.btnToDefault)
        Me.TbPanel1.Location = New System.Drawing.Point(20, 35)
        Me.TbPanel1.Name = "TbPanel1"
        Me.TbPanel1.Size = New System.Drawing.Size(518, 36)
        Me.TbPanel1.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.TbPanel1.TabIndex = 85
        Me.TbPanel1.tbBackgroundImage = My.Resources.frm_bg_Tip
        Me.TbPanel1.tbShowWatermark = False
        Me.TbPanel1.tbSplit = "60,3,3,3"
        Me.TbPanel1.tbWatermark = Nothing
        Me.TbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblDescribtion
        '
        Me.lblDescribtion.AutoEllipsis = True
        Me.lblDescribtion.BackColor = System.Drawing.Color.Transparent
        Me.lblDescribtion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(116, Byte), Integer), CType(CType(91, Byte), Integer), CType(CType(1, Byte), Integer))
        Me.lblDescribtion.Location = New System.Drawing.Point(3, 12)
        Me.lblDescribtion.Name = "lblDescribtion"
        Me.lblDescribtion.Size = New System.Drawing.Size(425, 17)
        Me.lblDescribtion.TabIndex = 82
        Me.lblDescribtion.Text = "自定义运营商图标：支持中国移动、联通，不支持中国电信。不支持iOS7。"
        Me.lblDescribtion.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmMotifyCarrier
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(558, 515)
        Me.Controls.Add(Me.pnlStep2)
        Me.Controls.Add(Me.pnlStep1)
        Me.Controls.Add(Me.TbPanel1)
        Me.Controls.Add(Me.btnSplit)
        Me.MaximizeBox = False
        Me.MinimumSize = New System.Drawing.Size(40, 66)
        Me.Name = "frmMotifyCarrier"
        Me.tbAutoSetFormSize = True
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "20,33,20,33"
        Me.tbTitleFont = New System.Drawing.Font("宋体", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tbTitleForeColor = System.Drawing.Color.Black
        Me.Text = "自定义运营商图标"
        Me.Controls.SetChildIndex(Me.btnSplit, 0)
        Me.Controls.SetChildIndex(Me.TbPanel1, 0)
        Me.Controls.SetChildIndex(Me.pnlStep1, 0)
        Me.Controls.SetChildIndex(Me.pnlStep2, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlStep2.ResumeLayout(False)
        Me.pnlStep2.PerformLayout()
        Me.pnSelectPicture.ResumeLayout(False)
        Me.pnSelectPicture.PerformLayout()
        CType(Me.picStep3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlSelectText.ResumeLayout(False)
        CType(Me.PictureBox5, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlMessage.ResumeLayout(False)
        CType(Me.picResultTip, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picStep2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlStep1.ResumeLayout(False)
        Me.TbPanel2.ResumeLayout(False)
        Me.TbPanel2.PerformLayout()
        CType(Me.picTips, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picDescribtion, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picArraw, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TbPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btnSplit As tbButton
    Friend WithEvents pnlStep2 As tbPanel
    Friend WithEvents pnlSelectText As tbPanel
    Friend WithEvents lblSelectDefinePicture As tbLabel
    Friend WithEvents pnSelectPicture As tbPanel
    Friend WithEvents lblTSImage As tbLabel
    Friend WithEvents lblFSOImage As tbLabel
    Friend WithEvents btnBrowsePicture2 As tbButton
    Friend WithEvents btnBrowsePicture1 As tbButton
    Friend WithEvents rdoSetPicture As System.Windows.Forms.RadioButton
    Friend WithEvents rdoSetText As System.Windows.Forms.RadioButton
    Friend WithEvents lblReplaceMethod As tbLabel
    Friend WithEvents lblTSPreview As tbLabel
    Friend WithEvents lblFSOPreview As tbLabel
    Friend WithEvents lblPreview As tbLabel
    Friend WithEvents cprTS As iTong.CarrierPreview
    Friend WithEvents btnToDefault As tbButton
    Public WithEvents btnOK As tbButton
    Friend WithEvents lblPictureSuggust As System.Windows.Forms.Label
    Friend WithEvents lblSelectPicture As tbLabel
    Friend WithEvents lblDownloadPicture As System.Windows.Forms.LinkLabel
    Friend WithEvents picStep2 As System.Windows.Forms.PictureBox
    Friend WithEvents picStep3 As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox5 As System.Windows.Forms.PictureBox
    Friend WithEvents btnFirstStep As tbButton
    Friend WithEvents TbPanel1 As tbPanel
    Friend WithEvents TbButton3 As tbButton
    Friend WithEvents txtTSPath As tbTextBox
    Friend WithEvents txtFsoPath As tbTextBox
    Friend WithEvents lblDescribtion As System.Windows.Forms.Label
    Friend WithEvents TbLabel11 As tbLabel
    Friend WithEvents pnlMessage As tbPanel
    Friend WithEvents lblMessage As System.Windows.Forms.Label
    Public WithEvents btnNextStep As tbButton
    Friend WithEvents TbLabel10 As tbLabel
    Friend WithEvents lblErrorMessage As tbLabel
    Friend WithEvents TbLabel9 As tbLabel
    Friend WithEvents PictureBox4 As System.Windows.Forms.PictureBox
    Friend WithEvents picArraw As System.Windows.Forms.PictureBox
    Friend WithEvents pnlStep1 As tbPanel
    Friend WithEvents picDescribtion As System.Windows.Forms.PictureBox
    Friend WithEvents lblReadTutorials As System.Windows.Forms.LinkLabel
    Friend WithEvents btnHorizonSplit As tbButton
    Friend WithEvents cprFSO As iTong.CarrierPreview
    Friend WithEvents txtCarrierString As tbTextBox
    Friend WithEvents txtCarrierVersion As tbTextBox
    Friend WithEvents TbPanel2 As tbPanel
    Friend WithEvents picTips As System.Windows.Forms.PictureBox
    Friend WithEvents btnReadTutorials As tbButton
    Friend WithEvents lblTextTooLongWarning As tbLabel
    Friend WithEvents cboTextFont As System.Windows.Forms.ComboBox
    Friend WithEvents lblFont As tbLabel
    Friend WithEvents picResultTip As System.Windows.Forms.PictureBox
    Friend WithEvents cboCarrierName As System.Windows.Forms.ComboBox
    Friend WithEvents lblReplaceType As tbLabel
    Friend WithEvents btnNotValid As tbButton
    Friend WithEvents lblNotValid As System.Windows.Forms.LinkLabel
End Class
