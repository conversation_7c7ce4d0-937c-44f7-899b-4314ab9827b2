﻿Public Class ClearRubbishArgs
    Private mIsScanTemp As Boolean = True
    Private mIsScanCache As Boolean = True
    Private mIsScanCookie As Boolean = True
    Private mIsScanMedia As Boolean = True
    Public Property IsScanTemp() As Boolean
        Get
            Return IsScanTemp
        End Get
        Set(ByVal value As Boolean)
            mIsScanTemp = value
        End Set
    End Property

    Public Property IsScanCache() As Boolean
        Get
            Return Me.mIsScanCache
        End Get
        Set(ByVal value As Boolean)
            Me.mIsScanCache = value
        End Set
    End Property

    Public Property IsScanCookie() As Boolean
        Get
            Return mIsScanCookie
        End Get
        Set(ByVal value As Boolean)
            Me.mIsScanCookie = value
        End Set
    End Property

    Public Property IsScanMedia() As Boolean
        Get
            Return Me.mIsScanMedia
        End Get
        Set(ByVal value As Boolean)
            Me.mIsScanMedia = value
        End Set
    End Property
End Class
