﻿Imports iTong.CoreFoundation

Public Class frmMotifyCarrier

    Private mbgwChangeCarrier As New System.ComponentModel.BackgroundWorker
    Private mSelectPathFSO As String = ""
    Private mSelectPathTS As String = ""
    Private mSelectPathDefault As String = ""
    Private mCarierName As String = ""
    Private mCarrierVersion As String
    Private mFontText As Font = Nothing
    Private mInstallfailedText As String = ""    '设置运营商图标失败的信息。
    Private mCarrierString As String = ""
    Private mGraphic As Graphics = Nothing

    Private mSetDefault As Boolean = False
    Private mSetText As Boolean = True

    Private mDictDefaultCMCCVersion As New Dictionary(Of Integer, String)
    Private mDictDefaultUnicomVersion As New Dictionary(Of Integer, String)

#Region "--- 初始化 ---"

    Public Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mDevice = device
        Me.mApplication = app
        Me.Language = app.Language
        Me.Icon = My.Resources.iTong
        Me.CanResize = False
        Me.tbShowTitlemark = False
        Me.tbShowWatermark = False
        Me.tbTitleForeColor = Color.White

        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.FilletRadius = 5

        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False
        '  Me.BackColor = Color.FromArgb(179, 178, 171)
        Me.FunctionMappingKey = FunctionKey.MotifyCarrier
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.cboCarrierName.Items.Clear()
        '此处文本无需替换
        Me.cboCarrierName.Items.AddRange(New Object() {"中国移动", "中国联通", "中国电信"})

        Me.btnNextStep.Enabled = False
        Me.txtCarrierVersion.Enabled = False
        Me.cboCarrierName.Enabled = False
        Me.lblErrorMessage.Visible = True
        Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Message.LoadingSetting")             '"正在加载配置，请稍候"
        Me.lblErrorMessage.ForeColor = Color.Black

        Me.btnToDefault.Visible = False
        Me.pnlStep1.Visible = True
        Me.pnlStep2.Visible = False

        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False
        Me.CanResize = False

        Me.btnToDefault.ForeColor = Color.White
        Me.cboCarrierName.SelectedIndex = 0
        Me.cboCarrierName.Enabled = True
        Me.txtCarrierVersion.Enabled = True
        Me.btnNextStep.Enabled = True
        Me.lblErrorMessage.Visible = False

        '设备成功设备上未生效教程（默认隐藏）
        Me.ShowSucceedGuid(False)
        Me.InitCarrierVersion()

        mbgwChangeCarrier.WorkerSupportsCancellation = True
        AddHandler mbgwChangeCarrier.DoWork, AddressOf ChangeCarrier_DoWork
        AddHandler mbgwChangeCarrier.RunWorkerCompleted, AddressOf ChangeCarrier_RunWorkerCompleted

        Dim installedFonts As System.Drawing.Text.InstalledFontCollection = New System.Drawing.Text.InstalledFontCollection
        For Each fontItem As FontFamily In installedFonts.Families
            Me.cboTextFont.Items.Add(fontItem.Name)
        Next
        Me.cboTextFont.Text = "黑体"  '"Vivaldi" '
        'Me.SetCarrierFont()

        Me.InitFromServerIni()
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        Me.mDevice = Nothing
        Me.Close()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        If Me.mDevice Is Nothing Then
            If Me.mbgwChangeCarrier.IsBusy Then
                Me.mbgwChangeCarrier.CancelAsync()
            End If
        Else
            If Me.mbgwChangeCarrier.IsBusy Then
                Dim strMsg As String = String.Empty
                If mSetDefault Then
                    strMsg = Me.Language.GetString("Tools.Carrier.Message.RestoreingCannotClose")               '"正在恢复运营商图标，是否要强制退出？"
                Else
                    strMsg = Me.Language.GetString("Tools.Carrier.Message.ChangingCannotClose")                 '"正在修改运营商图标，是否要强制退出？"
                End If

                Dim result As System.Windows.Forms.DialogResult = tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), _
                                         System.Windows.Forms.MessageBoxButtons.YesNo, _
                                         System.Windows.Forms.MessageBoxIcon.Warning, _
                                         System.Windows.Forms.MessageBoxDefaultButton.Button2)

                If result = Windows.Forms.DialogResult.Yes Then
                    Me.mbgwChangeCarrier.CancelAsync()
                Else
                    pblnCancelClose = True
                End If
            End If
        End If

        If Not pblnCancelClose Then
            RemoveHandler mbgwChangeCarrier.DoWork, AddressOf ChangeCarrier_DoWork
            RemoveHandler mbgwChangeCarrier.RunWorkerCompleted, AddressOf ChangeCarrier_RunWorkerCompleted
        End If

        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Tools.Carrier.Label.Title")                                '"自定义运营商图标"
        Me.lblDescribtion.Text = Me.Language.GetString("Tools.Carrier.Label.CarrierName")           '"自定义运营商图标： 支持中国移动、中国联通"
        Me.btnToDefault.Text = Me.Language.GetString("Tools.Carrier.Button.SetDefault")             '"恢复默认"
        Me.lblReplaceMethod.Text = Me.Language.GetString("Tools.Carrier.Button.SelectReplaceType") & Me.Language.GetString("Common.Symbol.Colon")   '"选择替换方式："
        Me.rdoSetText.Text = Me.Language.GetString("Tools.Carrier.Label.ManualText")                '"自定义文字"
        Me.rdoSetPicture.Text = Me.Language.GetString("Tools.Carrier.Label.ManualPic")              '"自定义图片"
        Me.lblSelectPicture.Text = Me.Language.GetString("Tools.Carrier.Label.SelectPic") & Me.Language.GetString("Common.Symbol.Colon")            '"选择自定义图片："
        Me.lblFSOImage.Text = Me.Language.GetString("Tools.Carrier.Label.TopStatusPic") & Me.Language.GetString("Common.Symbol.Colon")              '"顶部状态栏图片(必填):"
        Me.lblDownloadPicture.Text = Me.Language.GetString("Tools.Carrier.Label.DownloadResources") '"点击下载图片资源"
        Me.btnBrowsePicture1.Text = Me.Language.GetString("Tools.Carrier.Button.Browse")            '"浏览"

        Me.lblTSImage.Text = Me.Language.GetString("Tools.Carrier.Label.NoteCenterPic") & Me.Language.GetString("Common.Symbol.Colon")              '"通知中心图片(必填):"
        Me.btnBrowsePicture2.Text = Me.Language.GetString("Tools.Carrier.Button.Browse")            '"浏览"
        Me.lblPreview.Text = Me.Language.GetString("Tools.Carrier.Button.Browse")                   '"预览"
        Me.lblFSOPreview.Text = Me.Language.GetString("Tools.Carrier.Label.TopStatus") & Me.Language.GetString("Common.Symbol.Colon")               '"顶部状态栏："
        Me.lblTSPreview.Text = Me.Language.GetString("Tools.Carrier.Label.NoteCenter") & Me.Language.GetString("Common.Symbol.Colon")               '"通知中心："
        Me.lblMessage.Text = Me.Language.GetString("Tools.Carrier.Message.FlyModeToReflash")        '"保存成功，请先进入飞行模式后再关闭，以启用新图标。"
        Me.lblNotValid.Text = Me.Language.GetString("Tools.Carrier.Message.ChangeCarrierUnEffective")   '"修改运营商图标成功，但设备上未生效怎么办？"
        Me.lblReadTutorials.Text = Me.Language.GetString("Tools.Carrier.Label.ReadTutorials")       '"自定义运营商图标教程"
        Me.btnFirstStep.Text = Me.Language.GetString("Tools.Carrier.Button.GoBack")                 '"上一步"
        Me.btnOK.Text = Me.Language.GetString("Tools.Carrier.Button.Apply")                         '"应用"

        Me.lblReplaceType.Text = Me.Language.GetString("Tools.Carrier.Label.Carrier") & Me.Language.GetString("Common.Symbol.Colon")                '"运营商："
        Me.TbLabel10.Text = Me.Language.GetString("Tools.Carrier.Label.CarrierVersion") & Me.Language.GetString("Common.Symbol.Colon")              '"运营商版本："
        Me.TbLabel9.Text = Me.Language.GetString("Tools.Carrier.Label.Demo")                        '"示例：中国联通14.1  ->   只要填写  14.1"
        'Me.btnNextStep.Text = Me.Language.GetString("Tools.Carrier.Button.Next")                    '"下一步"
        Me.lblSelectDefinePicture.Text = Me.Language.GetString("Tools.Carrier.Label.FillInValue") & Me.Language.GetString("Common.Symbol.Colon")    '"填写文字内容："
        Me.lblFont.Text = Me.Language.GetString("Tools.Carrier.Label.Font") & Me.Language.GetString("Common.Symbol.Colon")                          '"字体："
        Me.TbLabel11.Text = Me.Language.GetString("Tools.Carrier.Label.ChangeTopLeftText")          '"任意修改左上角运营商的文字内容"

    End Sub

    '服务器配置加载完成
    Private Sub InitFromServerIni()
        Dim iccid As String = Me.mDevice.ICCID
        Dim imsi As String = CStr(mDevice.GetDeviceValue(DeviceInfoKey.InternationalMobileSubscriberIdentity))
        Common.Log("（运营商图标）设备的ICCID：" & iccid)

        '检测设备类型和版本
        If Not Me.CheckDevice() Then
            Return
        End If

        '检测运营商
        If Not CheckCarrier(iccid, imsi) Then
            Return
        End If
        '检测运营商版本
        Me.CheckCarrierVersion(iccid)

        '检查设备对应的分辨率
        Me.CheckX2()

        If IniSetting.GetIsSetCarrierText Then
            Me.rdoSetText.Checked = True
            Me.rdoSetPicture.Checked = False
        Else
            Me.rdoSetText.Checked = False
            Me.rdoSetPicture.Checked = True
        End If

        If Me.txtCarrierString.Text = "" Then
            Me.txtCarrierString.Text = Me.mCarierName
        End If

        If Not String.IsNullOrEmpty(Me.mCarrierVersion) Then
            Me.btnToDefault.Visible = True
            Me.pnlStep1.Visible = False
            Me.pnlStep2.Visible = True
        Else
            Me.pnlStep1.Visible = True
            Me.pnlStep2.Visible = False
        End If

    End Sub

    '初始化运营商版本
    Private Sub InitCarrierVersion()

        mDictDefaultCMCCVersion(50) = "12.1"
        mDictDefaultCMCCVersion(51) = "12.1"
        mDictDefaultCMCCVersion(60) = "14.1"
        mDictDefaultCMCCVersion(61) = "14.1"

        mDictDefaultUnicomVersion(50) = "12.1"
        mDictDefaultUnicomVersion(51) = "12.1"
        mDictDefaultUnicomVersion(60) = "14.1"
        mDictDefaultUnicomVersion(61) = "14.1"

    End Sub

    Private Function CheckDevice() As Boolean
        Dim result As Boolean = True

        If Not Me.mDevice.ProductType.Contains("iPhone") Then
            'result = False
            'Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.JustSupportiPhone")      '"对不起，当前版本只支持iPhone"
        ElseIf Me.mDevice.VersionNumber >= 700 Then
            result = False
            Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.NotSupportiOS7")           '"对不起，当前版本不支持iOS7及以上固件"
        End If

        If Not result Then
            Me.cboCarrierName.Items.Clear()
            Me.cboCarrierName.Enabled = False
            Me.txtCarrierString.Enabled = False
            Me.lblErrorMessage.Visible = True
            Me.lblErrorMessage.ForeColor = Color.Red
            btnNextStep.Text = Me.Language.GetString("Common.Button.Close")             '"关闭"
        End If

        Return result
    End Function

    Private Function CheckCarrier(ByVal iccid As String, ByVal imsi As String) As Boolean
        If String.IsNullOrEmpty(iccid) Then
            Me.cboCarrierName.Items.Clear()
            Me.cboCarrierName.Enabled = False

            Me.txtCarrierString.Enabled = False
            Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.NoSim")          '"对不起，检测到您的设备未插入SIM卡，无法更改运营商图标"
            Me.lblErrorMessage.Visible = True
            Me.lblErrorMessage.ForeColor = Color.Red
            btnNextStep.Text = Me.Language.GetString("Common.Button.Close")             '"关闭"
            Return False
        End If
        '检测到iccid，检查结果是否与imsi的结果相同
        Dim iccidHeader As String = iccid.Substring(0, 6)
        Dim imsiHeader As String = imsi.Substring(0, 5)


        Dim nameCarrier As String = ServerIniSetting.GetIccidCarrier(iccidHeader)
        Dim nameIMBACarrier As String = ServerIniSetting.GetIMSICarrier(imsiHeader)

        '冲突
        If Not nameCarrier.Equals(nameIMBACarrier) Then
            If String.IsNullOrEmpty(nameCarrier) Then  '有一个为空的话，就默认取另外一个
                nameCarrier = nameIMBACarrier
            ElseIf String.IsNullOrEmpty(nameIMBACarrier) Then

            Else                                                    '两个都不为空，就让用户手动选择
                Me.btnNextStep.Enabled = True
                Me.txtCarrierString.Enabled = False
                Me.lblErrorMessage.Visible = True
                Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.UnknowCarrier")          '"无法识别您的的运营商，请手动选择"
                Me.lblErrorMessage.ForeColor = Color.Red
                Me.cboCarrierName.Enabled = True
                Return False
            End If
        End If

        Select Case nameCarrier
            Case "China Mobile"
                'Me.cboCarrierName.Enabled = False
                mCarierName = "中国移动"
                Me.cboCarrierName.Text = mCarierName
                Me.cprFSO.CarrierText = mCarierName
                Me.cprTS.CarrierText = mCarierName

            Case "China Unicom"
                'Me.cboCarrierName.Enabled = False
                mCarierName = "中国联通"
                Me.cboCarrierName.Text = mCarierName
                Me.cprFSO.CarrierText = mCarierName
                Me.cprTS.CarrierText = mCarierName

            Case "China Telecom"
                'Me.cboCarrierName.Enabled = False
                mCarierName = "中国电信"
                Me.cboCarrierName.Text = mCarierName
                Me.cprFSO.CarrierText = mCarierName
                Me.cprTS.CarrierText = mCarierName

                Me.txtCarrierString.Enabled = False
                Me.lblErrorMessage.Visible = True
                Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.UnSupporseCarrier")          '"对不起，当前版本不支持电信用户修改运营商图标"
                Me.lblErrorMessage.ForeColor = Color.Red
                btnNextStep.Text = Me.Language.GetString("Common.Button.Close")         '"关闭"
                Return False

            Case Else
                '取不到值或者匹配不成功
                Me.txtCarrierString.Enabled = False
                Me.lblErrorMessage.Visible = True
                Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.CanNotSupport")              '"对不起，暂时不支持您的运营商"
                Me.lblErrorMessage.ForeColor = Color.Red
                btnNextStep.Text = Me.Language.GetString("Common.Button.Close")             '"关闭"
                Return False

        End Select

        Return True
    End Function

    '检测运营商版本
    Private Sub CheckCarrierVersion(ByVal iccid As String)
        '不需要从配置中去读取，固件与运营商版本是对应
        'If Not String.IsNullOrEmpty(IniSetting.CarrierVersion(Me.Device.SerialNumber + iccid)) Then
        '    Me.mCarrierVersion = IniSetting.CarrierVersion(Me.Device.SerialNumber + iccid)
        '    Me.txtCarrierVersion.Text = Me.mCarrierVersion
        '    Me.ActiveControl = Me.btnNextStep
        '    Me.btnNextStep.Focus()
        'Else
        'End If

        Dim versionNumber As Integer = Me.mDevice.VersionNumber
        versionNumber = Math.Floor(versionNumber / 10)
        If Me.mCarierName = "中国移动" Then
            If Me.mDictDefaultCMCCVersion.ContainsKey(versionNumber) Then
                Me.txtCarrierVersion.Text = mDictDefaultCMCCVersion(versionNumber)
                mCarrierVersion = Me.txtCarrierVersion.Text
            End If
        Else
            If Me.mDictDefaultUnicomVersion.ContainsKey(versionNumber) Then
                Me.txtCarrierVersion.Text = mDictDefaultUnicomVersion(versionNumber)
                mCarrierVersion = Me.txtCarrierVersion.Text
            End If
        End If

    End Sub

    Private Sub CheckX2()
        If Me.mDevice.ProductType.Split(",".ToCharArray)(0) < "iPhone3" Then
            Me.cprTS.IsX2 = False
            Me.cprFSO.IsX2 = False
        Else
            Me.cprTS.IsX2 = True
            Me.cprFSO.IsX2 = True
        End If

    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        If Me.rdoSetPicture.Checked Then
            '选中设置“自定义图片”
            If Not CheckImagePath() Then
                Return
            End If
        Else

            '选中设置“自定义文字”
            If Not String.IsNullOrEmpty(Me.txtCarrierString.Text) Then
                Me.mCarrierString = Me.txtCarrierString.Text
                'Me.mTextFont = Me.cboTextFont.Text
                Me.SetCarrierFont()
            Else
                '"文字不能为空"
                tbMessageBox.Show(Me, Me.Language.GetString("Tools.Carrier.Message.FileCanotEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
        End If

        Me.SetCarrier(False, Me.rdoSetText.Checked)
    End Sub

    Private Sub btnToDefault_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnToDefault.Click
        Me.txtCarrierString.Text = Me.mCarierName
        Me.SetCarrier(True)
    End Sub

    Private Sub btnBrowsePicture1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBrowsePicture1.Click
        Dim openFileDialog As New OpenFileDialog
        openFileDialog.Filter = String.Format("png {0}(*.png)|*.png", Me.Language.GetString("Common.File"))
        If openFileDialog.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Me.txtFsoPath.Text = openFileDialog.FileName
            Me.mSelectPathFSO = openFileDialog.FileName
            Try
                Me.cprFSO.ContextImage = New Bitmap(Me.mSelectPathFSO)
            Catch ex As Exception
                Me.cprFSO.ContextImage = Nothing
                Me.txtFsoPath.Text = ""
                Me.mSelectPathFSO = ""
                '"图片文件已损坏"
                tbMessageBox.Show(Me, Me.Language.GetString("Tools.Carrier.Message.PicDamage"), _
                                            Me.Language.GetString("Common.Info"), _
                                            MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End Try

        End If
    End Sub

    Private Sub btnBrowsePicture2_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBrowsePicture2.Click
        Dim openFileDialog As New OpenFileDialog
        openFileDialog.Filter = String.Format("png {0}(*.png)|*.png", Me.Language.GetString("Common.File"))
        If openFileDialog.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Me.txtTSPath.Text = openFileDialog.FileName
            Me.mSelectPathTS = openFileDialog.FileName
            Try
                Me.cprTS.ContextImage = New Bitmap(Me.mSelectPathTS)
            Catch ex As Exception
                Me.cprTS.ContextImage = Nothing
                Me.txtTSPath.Text = ""
                Me.mSelectPathTS = ""
                '"图片文件已损坏"
                tbMessageBox.Show(Me, Me.Language.GetString("Tools.Carrier.Message.PicDamage"), _
                                           Me.Language.GetString("Common.Info"), _
                                           MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End Try

        End If
    End Sub

    Private Sub rdoSetText_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoSetText.CheckedChanged
        If Not Me.rdoSetText.Checked Then
            Return
        End If

        Me.pnlSelectText.Visible = True
        Me.pnSelectPicture.Visible = False
        Me.cprFSO.ContextImage = Nothing
        Me.cprTS.ContextImage = Nothing
        Me.cprFSO.CarrierText = Me.txtCarrierString.Text
        Me.cprTS.CarrierText = Me.txtCarrierString.Text
        Me.pnlMessage.Visible = False
        Me.ActiveControl = Me.txtCarrierString
        Me.txtCarrierString.Focus()
        IniSetting.SetIsSetCarrierText(True)
    End Sub

    Private Sub rdoSetPicture_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles rdoSetPicture.CheckedChanged
        If Not Me.rdoSetPicture.Checked Then
            Return
        End If

        Me.pnlSelectText.Visible = False
        Me.pnSelectPicture.Visible = True
        Me.cprFSO.CarrierText = ""
        Me.cprTS.CarrierText = ""

        If File.Exists(Me.mSelectPathFSO) AndAlso String.Compare(Path.GetExtension(Me.mSelectPathFSO), ".png", True) = 0 Then
            Me.cprFSO.ContextImage = New Bitmap(mSelectPathFSO)
        End If

        If File.Exists(Me.mSelectPathTS) AndAlso String.Compare(Path.GetExtension(Me.mSelectPathTS), ".png", True) = 0 Then
            Me.cprTS.ContextImage = New Bitmap(mSelectPathTS)
        End If

        IniSetting.SetIsSetCarrierText(False)
        Me.pnlMessage.Visible = False

        Me.ActiveControl = Me.txtFsoPath
        Me.txtFsoPath.Focus()
    End Sub

    Private Sub txtCarrierString_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtCarrierString.TextChanged
        Try
            Me.cprFSO.CarrierText = Me.txtCarrierString.Text
            Me.cprTS.CarrierText = Me.txtCarrierString.Text

            Dim g As Graphics = Me.CreateGraphics()
            Dim sizeOfText As SizeF = g.MeasureString(Me.txtCarrierString.Text, mFontText)

            If sizeOfText.Width > 97 Then
                Dim start As Integer
                start = Me.txtCarrierString.SelectionStart
                Me.txtCarrierString.Text = Me.txtCarrierString.Text.Substring(0, Me.txtCarrierString.Text.Length - 1)
                Me.txtCarrierString.SelectionStart = start
            End If
            g.Dispose()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "txtCarrierString_TextChanged")
        End Try
    End Sub

    Private Sub btnNextStep_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNextStep.Click
        If btnNextStep.Text = Me.Language.GetString("Common.Button.Close") Then     '"关闭"
            Me.Close()
        End If

        If String.IsNullOrEmpty(Me.txtCarrierVersion.Text) Then
            Me.lblErrorMessage.Visible = True
            Me.lblErrorMessage.ForeColor = Color.Red
            Me.lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.TypeVersionFirst")          '"请先填写运营商版本"
            Return
        End If

        Dim carrierVersion As Single
        If Single.TryParse(txtCarrierVersion.Text, carrierVersion) Then
            If carrierVersion > 0 Then
                Dim iccid As String = Me.mDevice.GetDeviceValue(DeviceInfoKey.IntegratedCircuitCardIdentity)
                Dim intCarrierVersion As Integer = carrierVersion

                mCarrierVersion = intCarrierVersion.ToString + ".1"
                Me.btnToDefault.Visible = True
                Me.pnlStep1.Visible = False
                Me.pnlStep2.Visible = True
                Me.pnlMessage.Visible = False

                Me.ShowSucceedGuid(False)

                '不需要从配置中去读取，固件与运营商版本是对应
                'IniSetting.SetCarrierVersion(Me.Device.SerialNumber + iccid, txtCarrierVersion.Text)

                If Me.txtCarrierString.Text = "" Then
                    Me.txtCarrierString.Text = Me.mCarierName
                End If
                Me.ActiveControl = txtCarrierString
                Me.txtCarrierString.Focus()
            End If
        Else
            lblErrorMessage.Text = Me.Language.GetString("Tools.Carrier.Message.VersionFailure")                '"运营商版本格式错误"
        End If

    End Sub

    Private Sub btnFirstStep_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFirstStep.Click
        Me.btnToDefault.Visible = False
        Me.pnlStep2.Visible = False
        Me.pnlStep1.Visible = True
    End Sub

    Private Sub btnCancle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.Close()
    End Sub

    Private Sub LinkLabel2_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblDownloadPicture.LinkClicked
        '"http://bbs.tongbu.com/thread-85930-1-1.html"
        Common.OpenExplorer(WebUrl.ChinaCarrierTheme)
    End Sub

    Private Sub lblReadTutorials_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblReadTutorials.LinkClicked
        '"http://bbs.tongbu.com/thread-87066-1-1.html"
        Common.OpenExplorer(WebUrl.ChinaCarrierHelper)
    End Sub

    Private Sub btnReadTutorials_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReadTutorials.Click
        '"http://bbs.tongbu.com/thread-87066-1-1.html"
        Common.OpenExplorer(WebUrl.ChinaCarrierHelper)
    End Sub

    Private Sub lblNotValid_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblNotValid.LinkClicked
        Common.OpenExplorer(WebUrl.ChinaCarrierNotVaild)
    End Sub

    Private Sub btnNotValid_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNotValid.Click
        Common.OpenExplorer(WebUrl.ChinaCarrierNotVaild)
    End Sub

    Private Sub txtCarrierVersion_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles txtCarrierVersion.KeyPress
        If Char.IsDigit(e.KeyChar) OrElse e.KeyChar = "." OrElse e.KeyChar = Chr(8) Then
            '判断输入的小数点是否唯一  
            If e.KeyChar = "." Then
                If Me.txtCarrierVersion.Text.Contains(".") Then
                    e.Handled = True
                Else
                    e.Handled = False
                End If
            Else
                e.Handled = False
            End If
        Else
            e.Handled = True
        End If
    End Sub

    Private Sub cboTextFont_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboTextFont.SelectedIndexChanged
        Me.SetCarrierFont()
    End Sub

    Private Sub cboCarrierName_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cboCarrierName.SelectedIndexChanged
        Me.mCarierName = Me.cboCarrierName.Text
    End Sub

    Private Sub lblReadTutorials_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblReadTutorials.Resize
        Me.btnReadTutorials.Location = New Point(Me.lblReadTutorials.Right + 5, Me.btnReadTutorials.Top)
    End Sub

#End Region

#Region "---  主逻辑 ---"

    Private Sub SetCarrier(ByVal blnSetDefault As Boolean, Optional ByVal blnSetText As Boolean = True)
        If Me.mbgwChangeCarrier.IsBusy Then
            Return
        End If

        Me.mSetDefault = blnSetDefault
        Me.mSetText = blnSetText

        Me.UpdateUI()

        mbgwChangeCarrier.RunWorkerAsync()
    End Sub

    '修改运营商图标主逻辑
    Private Sub ChangeCarrier_DoWork(ByVal sender As Object, ByVal args As System.ComponentModel.DoWorkEventArgs)

        Try
            Me.mInstallfailedText = ""
            Dim isAddFileSuccess As Boolean = True

            Dim strTempDir As String = Folder.GetTempFilePath()
            Folder.CheckFolder(strTempDir)

            Dim strFileIPCC As String = Path.Combine(strTempDir, "carrier.ipcc")
            Dim strDirIPCC As String = Path.Combine(strTempDir, "carrier")

            ' 1,从资源中释放IPCC
            If Not Me.ReleaseIPCC(strFileIPCC) Then
                Common.LogException("ReleaseIPCC False")
                args.Result = False
                Return
            End If

            ' 2,解压IPCC(如果有越狱，则从手机上拷贝配置覆盖本地)
            If Not Me.UnzipIPCC(strFileIPCC, strDirIPCC) Then
                Common.LogException("UnzipIPCC False")
                args.Result = False
                Return
            End If

            ' 3,替换图片
            If Not Me.mSetDefault Then
                If Not UpdateIpccImage(strDirIPCC) Then
                    Common.LogException("UpdateIpccImage False")
                    args.Result = False
                    Return
                End If
            End If

            ' 4,更新版本
            If Not UpdateIpccPlist(strDirIPCC, Me.mCarrierVersion) Then
                Common.LogException("UpdateIpccPlist False")
                args.Result = False
                Return
            End If

            ' 5,删除冗余资源图片，否则系统应用程序界面中会出现类似China-Unicom
            Me.CheckResourceByDeviceType(strDirIPCC)

            Dim strDesFolder As String = Path.Combine(strDirIPCC, "Payload\ChinaTelecom_cn.bundle")
            'Common.OpenExplorer(strDirIPCC)
            'Dim strFile As String = "C:\Documents and Settings\locustgxf\桌面\Payload\ChinaTelecom_cn.bundle\carrier.plist"
            'Dim strDes As String = Path.Combine(strDesFolder, "carrier.plist")
            'File.Copy(strFile, strDes, True)

            '6 ,生成包
            If Not Utility.PackFiles(strFileIPCC, strDirIPCC) Then
                Common.LogException("PackFiles False")
                args.Result = False
                Return
            End If

            '7,安装到手机
            If Me.iPhone.InstallApplication(strFileIPCC, New InstallApplicationEventHandler(AddressOf OnInstall_Callback), Nothing, Nothing, Nothing) Then
                args.Result = True
            Else
                args.Result = False
            End If

            Try
                Directory.Delete(strTempDir, True)
            Catch ex As Exception
            End Try
        Catch ex As Exception
            Common.LogException(ex.ToString)
            args.Result = False
        End Try

    End Sub

    Private Sub ChangeCarrier_RunWorkerCompleted(ByVal sender As Object, ByVal args As System.ComponentModel.RunWorkerCompletedEventArgs)
        If args.Result = True Then
            Dim strText As String = Me.Language.GetString("Tools.Carrier.Message.SetSucceedLowVersion") '"设置成功，请先打开飞行模式再关闭，以启用新图标。"

            '6.X的固件打开勿扰模式，运营商图标会变成默认图标。
            If Me.mDevice.VersionNumber >= 600 Then
                strText = Me.Language.GetString("Tools.Carrier.Message.SetSucceed")         '"设置成功，请先关闭勿扰模式，打开飞行模式后再关闭，以启用新图标。"
            End If

            Me.lblMessage.Text = strText
            Me.lblMessage.ForeColor = Color.DarkGreen
            Me.pnlMessage.tbBackgroundImage = My.Resources.tool_label_message_success
            Me.picResultTip.Image = My.Resources.tool_tip_success

            '设备成功设备上未生效教程
            Me.ShowSucceedGuid(True)
        Else
            '"设置失败！"
            Me.lblMessage.Text = Me.Language.GetString("Tools.Carrier.Message.UpdateFailure") & Me.mInstallfailedText
            Me.lblMessage.ForeColor = Color.Red
            Me.pnlMessage.tbBackgroundImage = My.Resources.tool_label_message_fail
            Me.picResultTip.Image = My.Resources.tool_tip_fail
        End If
        Me.picResultTip.Visible = True
        ' Me.btnCancel.Enabled = True
        Me.btnFirstStep.Enabled = True
        Me.btnOK.Enabled = True
        Me.btnToDefault.Enabled = True
        Me.txtCarrierString.Enabled = True
        Me.txtTSPath.Enabled = True
        Me.txtFsoPath.Enabled = True
        Me.rdoSetPicture.Enabled = True
        Me.rdoSetText.Enabled = True

        Me.pnlMessage.Visible = True

    End Sub

    Private Sub OnInstall_Callback(ByVal sender As Object, ByVal e As InstallApplicationEventArgs)
        Try
            If e.InstallProgress = InstallapplicationProgress.InstallFailed Then

                Select Case e.LastErrType
                    Case InstallFailureType.InstallFailureUnzip
                        Me.mInstallfailedText = Me.Language.GetString("Tools.Carrier.Message.UnZipFailure")         '"解压失败，该包可能已损坏！"
                    Case InstallFailureType.InstallFailureFileNoExist
                        Me.mInstallfailedText = Me.Language.GetString("Tools.Carrier.Message.UnFoundApp")           '"没有发现 app 程序，不能安装此包！"
                    Case InstallFailureType.InstallFailureFileNoMatch
                        Me.mInstallfailedText = Me.Language.GetString("Tools.Carrier.Message.PackageUnCorrect")     '"包格式不符合"
                    Case InstallFailureType.InstallFailureStartServer
                        Me.mInstallfailedText = Me.Language.GetString("App.Message.InstallFailure")                 '"安装服务启动失败！"
                    Case InstallFailureType.InstallFailureUpload
                        Me.mInstallfailedText = Me.Language.GetString("Tools.Carrier.Message.UploadFailure")        '"上传包失败！"
                    Case InstallFailureType.InstallFailureUnknow
                        If e.LastErrMsg.Contains("402636802") Then
                            Me.mInstallfailedText = Me.Language.GetString("Tools.Carrier.Message.ReSetSetting")     '"请打开设备的设置->通用->访问限制->停用访问限制，再重试。"
                        End If

                    Case Else
                        Me.mInstallfailedText = e.LastErrMsg.Replace("CarrierBundleCopyFailed", Me.Language.GetString("Tools.Carrier.Message.CloseVisitRight"))     '"请关闭访问限制"

                End Select

                Common.Log(Me.mDevice.DeviceName & Me.mDevice.SerialNumber)
                Common.Log("设置运营商图标失败：" & vbCrLf & e.LastErrType & vbTab & Me.mInstallfailedText)
            End If
        Catch
        End Try
    End Sub

#End Region

#Region "---  私有方法  ---"

    '从资源中释放IPCC
    Private Function ReleaseIPCC(ByVal strFileIPCC As String) As Boolean
        Dim blnResult As Boolean = True

        Try
            Dim strCarrier As String = Me.mCarierName

            Dim arrData() As Byte = Nothing
            Select Case Me.mCarierName
                Case "中国移动"
                    If Me.mSetDefault Then
                        arrData = My.Resources.ipcc_iPhone_CMCC_Default
                    Else
                        arrData = My.Resources.ipcc_iPhone_CMCC_Payload
                    End If

                Case "中国联通"
                    If Me.mSetDefault Then
                        arrData = My.Resources.ipcc_iPhone_Unicom_Default
                    Else
                        arrData = My.Resources.ipcc_iPhone_Unicom_Payload
                    End If

                Case "中国电信"
                    If Me.mSetDefault Then
                        arrData = My.Resources.ipcc_iPhone_Telecom_Default
                    Else
                        arrData = My.Resources.ipcc_iPhone_Telecom_Payload
                    End If

            End Select

            Using fs As New FileStream(strFileIPCC, FileMode.Create)
                fs.Write(arrData, 0, arrData.Length)
            End Using

        Catch ex As Exception
            blnResult = False
            Common.LogException(ex.ToString(), "ReleaseIPCC")
        End Try

DO_EXIT:
        Return blnResult
    End Function

    '解压IPCC
    Private Function UnzipIPCC(ByVal strFileIPCC As String, ByVal strDirIPCC As String) As Boolean
        Dim blnResult As Boolean = True

        Try
            If Utility.unzip(strFileIPCC, strDirIPCC) = 0 Then
                '解压失败
                blnResult = False
                GoTo DO_EXIT
            End If

            ''已越狱,将手机中的文件复制出来，并覆盖
            If Me.mDevice.Jailbreaked Then
                'Dim dirCarrierOnPhone As String = String.Empty
                'Dim dirCarrierOnPC As String = String.Empty

                'Select Case Me.mCarierName
                '    Case "中国移动"
                '        dirCarrierOnPC = Path.Combine(strDirIPCC, "Payload\CMCC_cn.bundle")
                '        dirCarrierOnPhone = "/var/mobile/Library/Carrier Bundle.bundle/iPhone/CMCC_cn.bundle/"

                '    Case "中国联通"
                '        dirCarrierOnPC = Path.Combine(strDirIPCC, "Payload\Unicom_cn.bundle")
                '        dirCarrierOnPhone = "/var/mobile/Library/Carrier Bundle.bundle/iPhone/Unicom_cn.bundle/"

                '    Case "中国电信"
                '        dirCarrierOnPC = Path.Combine(strDirIPCC, "Payload\ChinaTelecom_cn.bundle")
                '        dirCarrierOnPhone = "/var/mobile/Library/Carrier Bundle.bundle/iPhone/ChinaTelecom_cn.bundle/"

                'End Select

                'If Me.mDevice.DownFromPhone(dirCarrierOnPhone, dirCarrierOnPC) Then

                'End If

                Dim dirBundleOnPhone As String = "/var/mobile/Library/Carrier Bundle.bundle"
                Dim dirOperatorOnPhone As String = "/var/mobile/Library/Operator Bundle.bundle"
                Dim info As iPhoneFileInfo = Nothing

                If Me.mDevice.IsDirectory(dirBundleOnPhone, info) Then
                    If info IsNot Nothing AndAlso info.LinkTarget.StartsWith("/System/Library/Carrier Bundles") Then
                        Me.mDevice.DeleteDirectory(dirBundleOnPhone)
                    End If
                End If
                If Me.mDevice.IsDirectory(dirOperatorOnPhone, info) Then
                    If info IsNot Nothing AndAlso info.LinkTarget.StartsWith("/System/Library/Carrier Bundles") Then
                        Me.mDevice.DeleteDirectory(dirOperatorOnPhone)
                    End If
                End If

            End If
        Catch ex As Exception
            blnResult = False
            Common.LogException(ex.ToString(), "UnzipIPCC")
        End Try

DO_EXIT:
        Return blnResult
    End Function

    '更新包里面的图片
    Private Function UpdateIpccImage(ByVal strDirIPCC As String) As Boolean
        Dim blnResult As Boolean = True

        Try
            Dim strPathFSO As String = ""
            Dim strPathTS As String = ""
            Dim strPathDefault As String = ""

            Select Case Me.mCarierName
                Case "中国移动"
                    strPathFSO = "Payload\CMCC_cn.bundle\FSO_CARRIER_China-Mobile_1only_.png"
                    strPathTS = "Payload\CMCC_cn.bundle\TS_CARRIER_China-Mobile_1only_.png"
                    strPathDefault = "Payload\CMCC_cn.bundle\Default_CARRIER_China-Mobile_1only_.png"

                Case "中国联通"
                    strPathFSO = "Payload\Unicom_cn.bundle\FSO_CARRIER_China-Unicom_1only_.png"
                    strPathTS = "Payload\Unicom_cn.bundle\TS_CARRIER_China-Unicom_1only_.png"
                    strPathDefault = "Payload\Unicom_cn.bundle\Default_CARRIER_China-Unicom_1only_.png"

                Case "中国电信"
                    strPathFSO = "Payload\ChinaTelecom_cn.bundle\FSO_CARRIER_China-Telecom_1only_.png"
                    strPathTS = "Payload\ChinaTelecom_cn.bundle\TS_CARRIER_China-Telecom_1only_.png"
                    strPathDefault = "Payload\ChinaTelecom_cn.bundle\Default_CARRIER_China-Telecom_1only_.png"

            End Select

            strPathFSO = Path.Combine(strDirIPCC, strPathFSO)
            strPathTS = Path.Combine(strDirIPCC, strPathTS)
            strPathDefault = Path.Combine(strDirIPCC, strPathDefault)

            If Me.mSetText Then
                blnResult = Me.CreateTextImage(strPathFSO, strPathTS, strPathDefault)
            Else
                File.Copy(mSelectPathFSO, strPathFSO, True)
                File.Copy(mSelectPathFSO, strPathFSO.Replace(".png", "@2x.png"), True)
                File.Copy(mSelectPathTS, strPathTS, True)
                File.Copy(mSelectPathTS, strPathTS.Replace(".png", "@2x.png"), True)
                File.Copy(mSelectPathDefault, strPathDefault, True)
                File.Copy(mSelectPathDefault, strPathDefault.Replace(".png", "@2x.png"), True)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateIpccImage")
            blnResult = False
        End Try

        Return blnResult
    End Function

    '通过文字构造图片strPathFSO, strPathTS, strPathDefault
    Private Function CreateTextImage(ByVal strPathFSO As String, ByVal strPathTS As String, ByVal strPathDefault As String) As Boolean
        Dim blnResult As Boolean = True

        Dim gFSO As Graphics = Nothing
        Dim gTS As Graphics = Nothing
        Dim gDefault As Graphics = Nothing

        Dim gFSO_2x As Graphics = Nothing
        Dim gTS_2x As Graphics = Nothing
        Dim gDefault_2x As Graphics = Nothing

        Dim imgFSO As Bitmap = Nothing
        Dim imgTS As Bitmap = Nothing
        Dim imgDefault As Bitmap = Nothing

        Dim imgFSO_2x As Bitmap = Nothing
        Dim imgTS_2x As Bitmap = Nothing
        Dim imgDefault_2x As Bitmap = Nothing

        Try
            Dim strPathFSO_2x As String = strPathFSO.Replace(".png", "@2x.png")
            Dim strPathTS_2x As String = strPathTS.Replace(".png", "@2x.png")
            Dim strPathDefault_2x As String = strPathDefault.Replace(".png", "@2x.png")

            If mGraphic Is Nothing Then
                mGraphic = Me.CreateGraphics()
            End If


            Dim fontText As Font = New Font(Me.mFontText.Name, 9.5, Me.mFontText.Style, GraphicsUnit.Point)
            Dim fontTextX2 As Font = New Font(Me.mFontText.Name, 19, Me.mFontText.Style, GraphicsUnit.Point)
            Dim sizeOfText As SizeF
            Dim sizeOfTextX2 As SizeF

            sizeOfText = mGraphic.MeasureString(Me.mCarrierString, fontText)
            sizeOfTextX2 = mGraphic.MeasureString(Me.mCarrierString, fontTextX2)
            sizeOfText.Height += 3
            sizeOfTextX2.Height += 6


            '构造六张内存图片
            imgFSO = New Bitmap(CInt(Math.Ceiling(sizeOfText.Width)), CInt(Math.Ceiling(sizeOfText.Height)))
            imgTS = New Bitmap(imgFSO.Width, imgFSO.Height)
            imgDefault = New Bitmap(imgFSO.Width, imgFSO.Height)

            imgFSO_2x = New Bitmap(CInt(Math.Ceiling(sizeOfTextX2.Width)), CInt(Math.Ceiling(sizeOfTextX2.Height)))
            imgTS_2x = New Bitmap(imgFSO_2x.Width, imgFSO_2x.Height)
            imgDefault_2x = New Bitmap(imgFSO_2x.Width, imgFSO_2x.Height)

            '构造图形对象
            gFSO = Graphics.FromImage(imgFSO)
            gTS = Graphics.FromImage(imgTS)
            gDefault = Graphics.FromImage(imgDefault)
            gFSO_2x = Graphics.FromImage(imgFSO_2x)
            gTS_2x = Graphics.FromImage(imgTS_2x)
            gDefault_2x = Graphics.FromImage(imgDefault_2x)

            '低分辨率下设置默认平滑，单像素，减少模糊
            gFSO.SmoothingMode = Drawing.Drawing2D.SmoothingMode.Default
            gFSO.TextRenderingHint = Drawing.Text.TextRenderingHint.SingleBitPerPixel

            gTS.SmoothingMode = Drawing.Drawing2D.SmoothingMode.Default
            gTS.TextRenderingHint = Drawing.Text.TextRenderingHint.SingleBitPerPixel

            gDefault.SmoothingMode = Drawing.Drawing2D.SmoothingMode.Default
            gDefault.TextRenderingHint = Drawing.Text.TextRenderingHint.SingleBitPerPixel

            '高分辨率设置平滑，反锯齿
            gFSO_2x.SmoothingMode = Drawing.Drawing2D.SmoothingMode.HighQuality
            gFSO_2x.TextRenderingHint = Drawing.Text.TextRenderingHint.AntiAlias

            gTS_2x.SmoothingMode = Drawing.Drawing2D.SmoothingMode.HighQuality
            gTS_2x.TextRenderingHint = Drawing.Text.TextRenderingHint.AntiAlias

            gDefault_2x.SmoothingMode = Drawing.Drawing2D.SmoothingMode.HighQuality
            gDefault_2x.TextRenderingHint = Drawing.Text.TextRenderingHint.AntiAlias

            Dim diffX As Integer = 3
            Dim diffX_2x As Integer = diffX * 2

            '绘制低分辨率下图片
            gFSO.DrawString(Me.mCarrierString, fontText, GuiHelper.GetCacheBrush(Color.FromArgb(255, 255, 255, 255)), diffX, 2.5)
            gTS.DrawString(Me.mCarrierString, fontText, GuiHelper.GetCacheBrush(Color.FromArgb(255, 20, 20, 20)), diffX, 3)
            gTS.DrawString(Me.mCarrierString, fontText, GuiHelper.GetCacheBrush(Color.FromArgb(255, 255, 255, 255)), diffX, 2.5)
            gDefault.DrawString(Me.mCarrierString, fontText, GuiHelper.GetCacheBrush(Color.FromArgb(255, 10, 10, 10)), diffX, 2.5)

            '绘制高分辨率下图片
            gFSO_2x.DrawString(Me.mCarrierString, fontTextX2, GuiHelper.GetCacheBrush(Color.FromArgb(255, 255, 255, 255)), diffX_2x, 5)
            gTS_2x.DrawString(Me.mCarrierString, fontTextX2, GuiHelper.GetCacheBrush(Color.FromArgb(255, 20, 20, 20)), diffX_2x, 7)
            gTS_2x.DrawString(Me.mCarrierString, fontTextX2, GuiHelper.GetCacheBrush(Color.FromArgb(255, 255, 255, 255)), diffX_2x, 5)
            gDefault_2x.DrawString(Me.mCarrierString, fontTextX2, GuiHelper.GetCacheBrush(Color.FromArgb(255, 10, 10, 10)), diffX_2x, 5)


            imgFSO.Save(strPathFSO, System.Drawing.Imaging.ImageFormat.Png)
            imgTS.Save(strPathTS, System.Drawing.Imaging.ImageFormat.Png)
            imgDefault.Save(strPathDefault, System.Drawing.Imaging.ImageFormat.Png)

            imgFSO_2x.Save(strPathFSO_2x, System.Drawing.Imaging.ImageFormat.Png)
            imgTS_2x.Save(strPathTS_2x, System.Drawing.Imaging.ImageFormat.Png)
            imgDefault_2x.Save(strPathDefault_2x, System.Drawing.Imaging.ImageFormat.Png)

        Catch ex As Exception
            Common.LogException(ex.ToString, "CreateTextImage")
            blnResult = False
        Finally
            If gFSO IsNot Nothing Then
                gFSO.Dispose()
            End If
            If gTS IsNot Nothing Then
                gTS.Dispose()
            End If
            If gDefault IsNot Nothing Then
                gDefault.Dispose()
            End If

            If gFSO_2x IsNot Nothing Then
                gFSO_2x.Dispose()
            End If
            If gTS_2x IsNot Nothing Then
                gTS_2x.Dispose()
            End If
            If gDefault_2x IsNot Nothing Then
                gDefault_2x.Dispose()
            End If

            If imgFSO IsNot Nothing Then
                imgFSO.Dispose()
            End If
            If imgTS IsNot Nothing Then
                imgTS.Dispose()
            End If
            If imgDefault IsNot Nothing Then
                imgDefault.Dispose()
            End If

            If imgFSO_2x IsNot Nothing Then
                imgFSO_2x.Dispose()
            End If
            If imgTS_2x IsNot Nothing Then
                imgTS_2x.Dispose()
            End If
            If imgDefault_2x IsNot Nothing Then
                imgDefault_2x.Dispose()
            End If
        End Try

        Return blnResult
    End Function

    '更新Plist信息
    Private Function UpdateIpccPlist(ByVal strDirIPCC As String, ByVal strVersion As String) As Boolean
        Dim blnResult As Boolean = False

        Try
            Dim strPathVersion As String = ""
            Dim strPathInfo As String = ""


            Select Case Me.mCarierName
                Case "中国移动"
                    strPathVersion = Path.Combine(strDirIPCC, "Payload\CMCC_cn.bundle\version.plist")
                    strPathInfo = Path.Combine(strDirIPCC, "Payload\CMCC_cn.bundle\Info.plist")

                Case "中国联通"
                    strPathVersion = Path.Combine(strDirIPCC, "Payload\Unicom_cn.bundle\version.plist")
                    strPathInfo = Path.Combine(strDirIPCC, "Payload\Unicom_cn.bundle\Info.plist")

                Case "中国电信"
                    strPathVersion = Path.Combine(strDirIPCC, "Payload\ChinaTelecom_cn.bundle\version.plist")
                    strPathInfo = Path.Combine(strDirIPCC, "Payload\ChinaTelecom_cn.bundle\Info.plist")

            End Select

            Dim plistVersion As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ReadPlist_managed(strPathVersion)
            If plistVersion Is Nothing Then
                GoTo DO_EXIT
            End If

            If plistVersion.ContainsKey("CFBundleShortVersionString") Then
                plistVersion("CFBundleShortVersionString") = mCarrierVersion & ".0"
            Else
                plistVersion.Add("CFBundleShortVersionString", mCarrierVersion & ".0")
                'GoTo DO_EXIT
            End If

            If plistVersion.ContainsKey("CFBundleVersion") Then
                plistVersion("CFBundleVersion") = mCarrierVersion
            Else
                plistVersion.Add("CFBundleVersion", mCarrierVersion & ".0")
                'GoTo DO_EXIT
            End If

            If Not iTong.Device.CoreFoundation.WritePlist(plistVersion, strPathVersion) Then
                GoTo DO_EXIT
            End If

            Dim plistInfo As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ReadPlist_managed(strPathInfo)
            If plistInfo Is Nothing Then
                Return False
            End If

            If plistInfo.ContainsKey("CFBundleShortVersionString") Then
                plistInfo("CFBundleShortVersionString") = mCarrierVersion + ".0"
            Else
                plistInfo.Add("CFBundleShortVersionString", mCarrierVersion & ".0")
                'GoTo DO_EXIT
            End If

            If plistInfo.ContainsKey("CFBundleVersion") Then
                plistInfo("CFBundleVersion") = mCarrierVersion
            Else
                plistInfo.Add("CFBundleVersion", mCarrierVersion & ".0")
                'GoTo DO_EXIT
            End If

            If Not iTong.Device.CoreFoundation.WritePlist(plistInfo, strPathInfo) Then
                GoTo DO_EXIT
            End If

            blnResult = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateIpccPlist")
        End Try

DO_EXIT:
        Return blnResult
    End Function

    '删除冗余资源图片，否则系统应用程序界面中会出现类似China-Unicom
    Private Sub CheckResourceByDeviceType(ByVal strDirIPCC As String)
        Try
            Dim deviceType As String = Common.GetNumber(Me.mDevice.ProductType).PadRight(3, "0"c)
            Dim number As Integer = Val(deviceType)

            Dim strDir As String = Directory.GetDirectories(Path.Combine(strDirIPCC, "Payload"))(0)
            If number >= 300 Then
                For Each strFile As String In Directory.GetFiles(strDir, "*.png")
                    If strFile.Contains("@2x.png") Then
                        Continue For
                    Else
                        File.Delete(strFile)
                    End If
                Next
            Else
                '低分辨率的用户，不能删除高清图标，否则会模糊不清
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckResourceByDeviceType")
        End Try
    End Sub

    Private Sub SetCarrierFont()
        Try
            Try
                mFontText = New Font(Me.cboTextFont.Text, 10)
            Catch ex As Exception
                Common.LogException(Me.cboTextFont.Text & vbTab & Me.Font.Name & vbCrLf & ex.ToString(), "cboTextFont_SelectedIndexChanged")
                mFontText = New Font(Me.Font.Name, 10, Me.Font.Style)
            End Try

            Me.cprFSO.TextFont = Me.mFontText.Name
            Me.cprTS.TextFont = Me.mFontText.Name
        Catch
        End Try
    End Sub

    Private Function CheckImagePath() As Boolean
        If String.IsNullOrEmpty(Me.mSelectPathFSO) Then
            '"顶部状态栏图片不能为空"
            tbMessageBox.Show(Me, Me.Language.GetString("Tools.Carrier.Message.TopPicNotEmpty"), _
                                         Me.Language.GetString("Common.Info"), _
                                         MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtFsoPath.Focus()
            Return False
        End If

        If String.IsNullOrEmpty(Me.mSelectPathTS) Then
            '"通知中心图片不能为空"
            tbMessageBox.Show(Me, Me.Language.GetString("Tools.Carrier.Message.NoteCenterPicNotEmpty"), _
                                         Me.Language.GetString("Common.Info"), _
                                         MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtTSPath.Focus()
            Return False
        End If


        '检查传入参数
        If Not String.Compare(Path.GetExtension(Me.mSelectPathFSO), ".png", True) = 0 Then
            '"顶部状态栏图片：请选择png格式的文件"
            tbMessageBox.Show(Me, Me.Language.GetString("Tools.Carrier.Message.TopPicType"), _
                                         Me.Language.GetString("Common.Info"), _
                                         MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If Not String.Compare(Path.GetExtension(Me.mSelectPathTS), ".png", True) = 0 Then
            '"通知中心图片：请选择png格式的文件"
            tbMessageBox.Show(Me, Me.Language.GetString("Tools.Carrier.Message.NoteCenterPicType"), _
                                         Me.Language.GetString("Common.Info"), _
                                         MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If Not File.Exists(Me.mSelectPathFSO) Then
            '"文件:{0}不存在"
            tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Tools.Carrier.Message.FileNotExist"), Me.mSelectPathFSO), _
                                        Me.Language.GetString("Common.Info"), _
                                        MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        If Not File.Exists(Me.mSelectPathTS) Then
            '"文件:{0}不存在"
            tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Tools.Carrier.Message.FileNotExist"), Me.mSelectPathTS), _
                                        Me.Language.GetString("Common.Info"), _
                                        MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Me.mSelectPathFSO = Me.txtFsoPath.Text
        Me.mSelectPathTS = Me.txtTSPath.Text
        Me.mSelectPathDefault = Me.txtFsoPath.Text
        Return True
    End Function

    Private Sub ShowSucceedGuid(ByVal blnShow As Boolean)
        If blnShow Then
            Me.lblNotValid.Location = Me.lblReadTutorials.Location
            Me.btnNotValid.Location = New Point(Me.lblNotValid.Right, Me.lblNotValid.Location.Y)

            Me.lblNotValid.Visible = True
            Me.btnNotValid.Visible = True
        Else
            Me.lblNotValid.Visible = False
            Me.btnNotValid.Visible = False
        End If
    End Sub

    Private Sub UpdateUI()
        Me.txtCarrierString.Enabled = False
        Me.txtTSPath.Enabled = False
        Me.txtFsoPath.Enabled = False

        Me.btnOK.Enabled = False
        Me.btnToDefault.Enabled = False
        Me.btnFirstStep.Enabled = False

        Me.rdoSetPicture.Enabled = False
        Me.rdoSetText.Enabled = False

        Me.picResultTip.Visible = False
        Me.pnlMessage.Visible = True

        Me.ShowSucceedGuid(False)

        Me.pnlMessage.tbBackgroundImage = My.Resources.tool_label_message

        If mSetDefault Then
            Me.lblMessage.Text = Me.Language.GetString("Tools.Carrier.Message.RestoreCarrier")      '"正在恢复运营商图标，请稍候..."
            Me.lblMessage.ForeColor = Color.Black
        Else
            Me.pnlMessage.tbBackgroundImage = My.Resources.tool_label_message
            Me.lblMessage.Text = Me.Language.GetString("Tools.Carrier.Message.ChangeCarrier")       '"正在修改运营商图标，请稍候..."
            Me.lblMessage.ForeColor = Color.FromArgb(255, 116, 91, 1)
        End If
    End Sub

#End Region

End Class