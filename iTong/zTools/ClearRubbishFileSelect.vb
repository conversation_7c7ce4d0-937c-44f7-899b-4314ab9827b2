﻿Public Class ClearRubbishFileSelectControl
    Inherits System.Windows.Forms.Control

    Private mFileSize As Long = 0

    Private mStringHeader As String = "已选择垃圾："

    Private mFontText As Font = Nothing
    Private mFontNumber As Font = Nothing

    Private mColorText As Color = Nothing
    Private mColorNumber As Color = Nothing

    Private mBrashText As Brush = Nothing
    Private mBrushNumber As Brush = Nothing

    Public Property StringHeader() As String
        Get
            Return mStringHeader
        End Get
        Set(ByVal value As String)
            mStringHeader = value
        End Set
    End Property

    Public Property FileSize() As Long
        Get
            Return Me.mFileSize
        End Get
        Set(ByVal value As Long)
            Me.mFileSize = value
            Me.Invalidate()
        End Set
    End Property

    'Public Property FontText() As Font
    '    Get
    '        Return Me.mFontText
    '    End Get
    '    Set(ByVal value As Font)
    '        Me.mFontText = value
    '        Me.Invalidate()
    '    End Set
    'End Property

    'Public Property FontNumber() As Font
    '    Get
    '        Return Me.mFontNumber
    '    End Get
    '    Set(ByVal value As Font)
    '        Me.mFontNumber = value
    '        Me.Invalidate()
    '    End Set
    'End Property

    Public Property ColorText() As Color
        Get
            Return Me.mColorText
        End Get
        Set(ByVal value As Color)
            Me.mColorText = value
            If Me.mBrashText IsNot Nothing Then
                Me.mBrashText.Dispose()
            End If
            Me.mBrashText = New SolidBrush(value)
            Me.Invalidate()
        End Set
    End Property

    Public Property ColorNumber() As Color
        Get
            Return Me.mColorNumber
        End Get
        Set(ByVal value As Color)
            Me.mColorNumber = value
            If Me.mBrushNumber IsNot Nothing Then
                Me.mBrushNumber.Dispose()
            End If
            Me.mBrushNumber = New SolidBrush(value)
            Me.Invalidate()
        End Set
    End Property

    Public Sub New()

        Try
            Me.ColorText = Color.Black
            Me.ColorNumber = Color.FromArgb(214, 109, 77)
        Catch ex As Exception

        End Try

        Me.SetStyle(ControlStyles.UserPaint, True)
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)
        Me.SetStyle(ControlStyles.DoubleBuffer, True)
        Me.SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        Me.SetStyle(ControlStyles.ResizeRedraw, True)
        Me.UpdateStyles()

        Me.mFontText = CreateFontSafe("微软雅黑", 12, FontStyle.Regular)
        Me.mFontNumber = CreateFontSafe("微软雅黑", 12, FontStyle.Regular)
    End Sub

    '创建字体
    Private Function CreateFontSafe(ByVal fontFamaly As String, ByVal size As Integer, ByVal style As FontStyle) As Font
        Dim fontCreate As Font = Nothing
        Try
            fontCreate = New Font(fontFamaly, size, style)
        Catch ex As Exception
            fontCreate = Common.CreateFont("宋体", size, style)
        End Try
        Return fontCreate
    End Function


    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        'MyBase.OnResize(e)
        'Me.Invalidate()
    End Sub
    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
       
        Dim size3 As SizeF
        Dim size4 As SizeF

        Dim rect3 As Rectangle
        Dim rect4 As Rectangle

        Dim string1 As String = Me.FormatFileSize(Me.mFileSize)
        Dim string2 As String = mStringHeader

        Dim formatCenter As New StringFormat
        formatCenter.Alignment = StringAlignment.Center
        formatCenter.LineAlignment = StringAlignment.Center

        Dim g As Graphics = e.Graphics

        Dim intSizeWidth As Integer = 110

        size3 = g.MeasureString(string2, Me.mFontNumber)
        rect3.Height = Me.Height
        rect3.Width = size3.Width + 1
        rect3.Location = New Point(Me.Width - intSizeWidth - size3.Width, 0)
        g.DrawString(string2, Me.mFontNumber, Me.mBrashText, rect3, formatCenter)

        size4 = g.MeasureString(string1, Me.mFontText)
        rect4.Height = Me.Height
        rect4.Width = size4.Width + 2
        rect4.Location = New Point(Me.Width - intSizeWidth, 0)
        g.DrawString(string1, Me.mFontText, Me.mBrushNumber, rect4, formatCenter)

    End Sub


    Public Function FormatFileSize(ByVal filesize As Long) As String
        Dim strReturn As String = ""
        If (filesize >= 1073741824) Then
            strReturn = (1.0 * filesize / 1073741824).ToString("f2") & " GB"
        ElseIf (filesize >= 1048576) Then
            strReturn = (1.0 * filesize / 1048576).ToString("f2") & " MB"
        ElseIf (filesize >= 1024) Then
            strReturn = (1.0 * filesize / 1024).ToString("f2") & " KB"
        Else
            strReturn = (1.0 * filesize).ToString("f2") & " Bytes"
        End If

        Return strReturn
    End Function

End Class
