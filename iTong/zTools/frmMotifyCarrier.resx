<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_normal.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAdBJREFUWEftmM1K
        w0AUhftjSrEFac1CbKR14yJiI4pIsShYfxbWhboSQaTu1IUL932BvoB7ty7c+XZxjlBIh9yZTCZpokzh
        rHpPOvP1ztx7U/R9v2A+AgIAZEQzMHAkCWIA5QxQkR3nVaYu066CEA8f/OSfurC0UrYarXPLbr9V7M64
        Yq9HUGeMePjg558/7wxqKUAJAwg/uWZruX1adbaeFzf6tzV3cFPfPLuSCXGIhw/+rAF5moDgJwGxTY7q
        3uWwcfh41Dx+6jcHLwdSsTjEwwd/1oBUjhUVSwJCJvzCiQKGi4EPfiVAhb37d5kUW4SZTa8NXz9lCsk4
        ElDNPbkOwintP3zJFIyH/18Dwn2jAwh+ZUDMsEMJ2aWbQcx/RwnZpZJBYYAmH98jSsiuINC4gMiUTggQ
        eS8lAUh0HxlAXIbwsAwgA2i2Jwq7g8wRCzSOmQD6S33Q3Mu8qMQHvlOZ5/iKRZb4QOnnPeTv8YBEJX76
        nVaZV+xxooBKdRZjnfBFnDFj6oFfqVFMAZDuNO+I1sRmKU9zFtvOGhDe52CTcd4HwSd8H8Sm8TKbyl0G
        qac4zffgqzrdUtaAohzDXMXkajEpHGnt/Wk/II+bSnJNP9l82XqV/N0oAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnSplit.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGQAAAACCAYAAACuT3kTAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAQlJREFUOE+90C1M
        QlEYxvHLN8jHnIWN5KzOKtnNzGaX7mZ3du2MzKyatVq1adKEBQJuEJjy/Xng/7D3FDue7bfnPTv3vu89
        N1RvtENBEKygDCOOGJJIIW0yZBY5qFZqr9rT8ztGdcL6+Z7qG0XEZmmep/k63+bSPf3ytdLXjlq0Vy7N
        gpybGTk1E3KMkRmQQ/T/6LHXmafn9Z7eVz/11ywXOOfC8BmjTmEXeezjEMc4QQnnuMAVblBBDfd4xDNe
        8IYP1NHAN9ro4hd9jDDGFHOs/gEjNmtpFjZ7Zt8xIYcY4Me+t0O20MQXPvGOV+i+T3jAHaq4xTUuUcYZ
        TlHEEQ5QwB4ySED/ProGrKeXHSsily8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="picStep3.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACIAAAAjCAYAAADxG9hnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAABO5JREFUWEe9mF1s
        U2UYxwuySNBFyRKSXalL/EiMiZErrhzRCxMTEy+IksAVyZQLPxOVcDOIF4xAwGjicGMTJrCx1S2MLegK
        dJMOxj664krdXCedrOoEF2hqYW7t8fkd3tMd2tOe06y65L++fc/7Ps+/7/P5nhWujD9N0zKn0t8D4ZmH
        5curgo2C5wVPCB5RC27L5zVBQOAVdAviOYXZPYBIJkYnr78gqL8SnlkMR2/M/jEXm4j9fXfkn4XF/mQq
        1QcYM8cz1rCWPWqvSz7zIouXmYRsfkzwWfDab4nZudj4YjL5gzz3ChoEmwRPCkoVGDPHMy9r2cNeZAge
        z0cmJxHZVCWmSEZv3priF4twt6DM6sRyzLHWzV5kIAuZuchYEpHF1fyS+J27Q+oE1hVAINO07PUiS51O
        tRWZLCKyaHdo+vfYvNhcBHQsg0AmITcykY2OTDL3EZGHb8FaHM8nBGqLSMIgVYtsdTJvm8mkieBM2DF+
        Zx5z4A9Z0VOkOTc6lM+kHThNZGRi+nOcSvnEf0XCkOtFFzqNU9GJEOscVyqV6hUijh3zuXcPVQhqBJoJ
        HhlX2ZzeOnQpE62HjP4nzA4T84U4J8o2fNyobT3Yoe1x+9J4r/477fU9JyH2iQ0ZNzpFd4NORP6VkgVV
        snKUJ0TJyyiDwNnRqWllTu9cLDFwvPfHOea3HmiP2xApQ6fKwKWukfHIm6TkQnzjtU+bZ1DmuxrBp8xm
        KPdPRtt4trulj1NZb0PGi244QMQwS10BUXFCEd+cuaflfGCLYaqOC2Mv2cis080jHCAySLGSDdSJZUfL
        5r1tLRD5tv/qnyKv3EbmJnTDweX/+debKoFVLIcIZhC0bvuiU2vo8cduxRM1DuRVoBsOOOu8KmpU0YJP
        hOggdF/ceVTbXtutdQ1OREXOVwIn8krRDYdlE/mg/syYOXw5ESHmcfijlogUwTT7jfANRmaDB05dWiCX
        kOgckFkyTZGdtbLjYijCCclJRRwQWXLWwdAvjSqrFhK+OX2p6ay/CyL72/tvOCCihy8cXL7Rn7Y4TWhE
        haopOKMlmR1f9wxA5Nj5QMgBET2hwcHV2Ny+1mmKr+26PIySHUc8162UkPqJHNYEwtGDdik+mUzpKR4O
        1LwVl4Pho8o8efuQM0MTTSj56Ignq6gRxq/sOn7blPqfclL00A0Hvfp+7xva4LANqCRjouyNfW5z6de/
        U19U/clK/Rmkyow2AN3pNkC4PNB/ZfyQw8aoGmVfdg8mjPxByFKFqb6i0I4EvkVjFEYnus1EXK2ne54u
        oFVEmVH4vDLuVFXYrrZAwmgVF1u7PM/c16GpfnFl7/DY+/9X84wu0bvSigh8Vl3wh/aarhPFbKLT1wl0
        oMuyizfdK0r6hoM19y5YekfP0TvuYy0iRV2w5vULFrJFV0nee40iQyiVnBsY/dC4cqqmutAr56P4A3uN
        KycykU242hIxfhFHV/fNyWfFloeNSzgJSJ2QcQmnh1mjwDh9CZe1fcYlHBnIyjRHXtOYjxZnEqxuajtV
        KUd6giw4ZbyWSOivJXz8YsA4JnO8lmANa2VPc5O7k3cpq82O6ejua5UNiXWEbdv+Tvnpc74qUXBsYGwy
        IJX7L2kjFgBj5njGGtYqAnqesIPJR+8Nbd5h4D+rBA8K1ggeEvAWCTBmjmesyfKDfLL/BeUDkZ01zbXR
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="PictureBox5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACIAAAAjCAYAAADxG9hnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAABO5JREFUWEe9mF1s
        U2UYxwuySNBFyRKSXalL/EiMiZErrhzRCxMTEy+IksAVyZQLPxOVcDOIF4xAwGjicGMTJrCx1S2MLegK
        dJMOxj664krdXCedrOoEF2hqYW7t8fkd3tMd2tOe06y65L++fc/7Ps+/7/P5nhWujD9N0zKn0t8D4ZmH
        5curgo2C5wVPCB5RC27L5zVBQOAVdAviOYXZPYBIJkYnr78gqL8SnlkMR2/M/jEXm4j9fXfkn4XF/mQq
        1QcYM8cz1rCWPWqvSz7zIouXmYRsfkzwWfDab4nZudj4YjL5gzz3ChoEmwRPCkoVGDPHMy9r2cNeZAge
        z0cmJxHZVCWmSEZv3priF4twt6DM6sRyzLHWzV5kIAuZuchYEpHF1fyS+J27Q+oE1hVAINO07PUiS51O
        tRWZLCKyaHdo+vfYvNhcBHQsg0AmITcykY2OTDL3EZGHb8FaHM8nBGqLSMIgVYtsdTJvm8mkieBM2DF+
        Zx5z4A9Z0VOkOTc6lM+kHThNZGRi+nOcSvnEf0XCkOtFFzqNU9GJEOscVyqV6hUijh3zuXcPVQhqBJoJ
        HhlX2ZzeOnQpE62HjP4nzA4T84U4J8o2fNyobT3Yoe1x+9J4r/477fU9JyH2iQ0ZNzpFd4NORP6VkgVV
        snKUJ0TJyyiDwNnRqWllTu9cLDFwvPfHOea3HmiP2xApQ6fKwKWukfHIm6TkQnzjtU+bZ1DmuxrBp8xm
        KPdPRtt4trulj1NZb0PGi244QMQwS10BUXFCEd+cuaflfGCLYaqOC2Mv2cis080jHCAySLGSDdSJZUfL
        5r1tLRD5tv/qnyKv3EbmJnTDweX/+debKoFVLIcIZhC0bvuiU2vo8cduxRM1DuRVoBsOOOu8KmpU0YJP
        hOggdF/ceVTbXtutdQ1OREXOVwIn8krRDYdlE/mg/syYOXw5ESHmcfijlogUwTT7jfANRmaDB05dWiCX
        kOgckFkyTZGdtbLjYijCCclJRRwQWXLWwdAvjSqrFhK+OX2p6ay/CyL72/tvOCCihy8cXL7Rn7Y4TWhE
        haopOKMlmR1f9wxA5Nj5QMgBET2hwcHV2Ny+1mmKr+26PIySHUc8162UkPqJHNYEwtGDdik+mUzpKR4O
        1LwVl4Pho8o8efuQM0MTTSj56Ignq6gRxq/sOn7blPqfclL00A0Hvfp+7xva4LANqCRjouyNfW5z6de/
        U19U/clK/Rmkyow2AN3pNkC4PNB/ZfyQw8aoGmVfdg8mjPxByFKFqb6i0I4EvkVjFEYnus1EXK2ne54u
        oFVEmVH4vDLuVFXYrrZAwmgVF1u7PM/c16GpfnFl7/DY+/9X84wu0bvSigh8Vl3wh/aarhPFbKLT1wl0
        oMuyizfdK0r6hoM19y5YekfP0TvuYy0iRV2w5vULFrJFV0nee40iQyiVnBsY/dC4cqqmutAr56P4A3uN
        KycykU242hIxfhFHV/fNyWfFloeNSzgJSJ2QcQmnh1mjwDh9CZe1fcYlHBnIyjRHXtOYjxZnEqxuajtV
        KUd6giw4ZbyWSOivJXz8YsA4JnO8lmANa2VPc5O7k3cpq82O6ejua5UNiXWEbdv+Tvnpc74qUXBsYGwy
        IJX7L2kjFgBj5njGGtYqAnqesIPJR+8Nbd5h4D+rBA8K1ggeEvAWCTBmjmesyfKDfLL/BeUDkZ01zbXR
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnHorizonSplit.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGQAAAACCAYAAACuT3kTAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAQlJREFUOE+90C1M
        QlEYxvHLN8jHnIWN5KzOKtnNzGaX7mZ3du2MzKyatVq1adKEBQJuEJjy/Xng/7D3FDue7bfnPTv3vu89
        N1RvtENBEKygDCOOGJJIIW0yZBY5qFZqr9rT8ztGdcL6+Z7qG0XEZmmep/k63+bSPf3ytdLXjlq0Vy7N
        gpybGTk1E3KMkRmQQ/T/6LHXmafn9Z7eVz/11ywXOOfC8BmjTmEXeezjEMc4QQnnuMAVblBBDfd4xDNe
        8IYP1NHAN9ro4hd9jDDGFHOs/gEjNmtpFjZ7Zt8xIYcY4Me+t0O20MQXPvGOV+i+T3jAHaq4xTUuUcYZ
        TlHEEQ5QwB4ySED/ProGrKeXHSsily8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="TbButton3.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGQAAAACCAYAAACuT3kTAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAQlJREFUOE+90C1M
        QlEYxvHLN8jHnIWN5KzOKtnNzGaX7mZ3du2MzKyatVq1adKEBQJuEJjy/Xng/7D3FDue7bfnPTv3vu89
        N1RvtENBEKygDCOOGJJIIW0yZBY5qFZqr9rT8ztGdcL6+Z7qG0XEZmmep/k63+bSPf3ytdLXjlq0Vy7N
        gpybGTk1E3KMkRmQQ/T/6LHXmafn9Z7eVz/11ywXOOfC8BmjTmEXeezjEMc4QQnnuMAVblBBDfd4xDNe
        8IYP1NHAN9ro4hd9jDDGFHOs/gEjNmtpFjZ7Zt8xIYcY4Me+t0O20MQXPvGOV+i+T3jAHaq4xTUuUcYZ
        TlHEEQ5QwB4ySED/ProGrKeXHSsily8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="picStep2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACIAAAAjCAYAAADxG9hnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAABN5JREFUWEe9mFts
        FGUUx5dLI0EbJE1IeAKbeEmMiZHEpE9cwhuEhIdGTfTBaFZ98BIJYvCBNDyA3OMLpbRNQWgFRyoXg1Jx
        WtlKabdsK6W2sKUttCixNLCUwtLujuc3fLOdTndnZuvqJv/db7/LOf8553znO9/MCDg+hmE4u1L/26ID
        T8mfVYLlgpcFzwjmqQl35bdX0CbQBT8IRjIK8xqAiBORqzdeEexvjw6MRwf/vvXXcKw7dv9h66Ox8cZE
        MtkAaNPHGHOYyxq1NiC/rpjCy05CFi8S7OnovTl6azjWNZ5I/CrjuqBCUCx4VpCvQJs+xnTmsoa1yBAs
        diOTkYgsCoorEoNDd3p4YhGuCQrSWSxDH3M11iIDWcjMRCYtEZm8iScZefCwRVlgQRYEnK5lrY4sZZ1N
        6chMISKTSjr7/4zFxecioPZfEHAS0pCJbHQ4yUwiIoPvwVoCLyQE9uaQhEVqL7KVZd63k0kRIZjw48iD
        OO4gHqbsnhz1aehQMZMK4BSR1u7+rwgqFRP/FQlLro4udFpWMYmw1zFXMpmsFyK+A/Olj0oLBVsFwwLD
        Bvrmu1hwAbqUi5ZAxvwIs3L2fDbBKYqCRZ9VGm/trjVKvmkwtmihFOgrWl8R8SCjoVN0V5hE5CufLKiS
        la88IQpWrt1yxFR8qrl7cDT+6Jxyqf5zpKef/vVVdcaKjVW7XKxSgE6VgfMDrV19r5OSs4mNNZtrBlAW
        utxHTK1TmdXyf/Bw/e/DjH9edabTI8B1dMMBIpZbyvzsCrFG8ReHfjF2HT8/JvPfSLemWm+7AJGdxxr7
        PGSWme4RDhBp5rCSBZwTfnbLMmW9E5nmYwmIfP/bZc4mN5nF6IZD4OKV60MqgRX6JOJKViy25OP9P5oW
        k5P4XQ+ZheiGA8EaV4cap6gfi7jOWVVyuANr6O3XiA8vmfnohkNOiYg16tgt7JxM8eN42AkiuXAN+QIS
        uESRCPq07oRrphGsk1xDTLy6rrwXEuErA91CwC8J5EwEa3PntUqVVX1tX/uTCokNSzceMLZ9F7o3OHQ3
        LGOrfVrCehhz+8IhEIr88Wa2CQ0rCMKvbdcMkpcorxY8lyUJyJgJDQ6Byppj87NJ8ZYVCErlih3TIACJ
        gkQiaaZ4OHDmzbjQET2g3ONah3DSYgVyxDRdYY8v89BDNxzM0/enUEuRVxlAaudUJUdw2hIbjqPf/h93
        ueWbAqsMQHeqDBAusxrbu0rdCqO399SG7Ue9W7v0dMsdr8NOdEXRiW47kcDRk2ee9ygViQXdJ/a5ELFK
        xfGjp+pemFShqXpxZn340if/V/GMLtE7Mx0R+Mw+d7HzS9t1IpdFdOo6gQ50pa3ibfeKvIZwx9bHFyyz
        oscdvuvYNC5RF6y4ecFCtujKc73XKDJspbyzTZFPrSunKqqzvXI+LaQ01lpXTmQim+3qScR6IkxX9vWR
        F8WX5dYlnASkLGRdwqlh5irQTl3CZW6DdQlHBrKc7nB1jd20BJNgzsFvjy8Tk1aTBXus1xKj5muJEE8M
        aMekj9cSzGGurKk5qJ3gXcoce2D6uvum23bsdYS988GHC0+eDQVFwaGmS1fb5OS+LWXEGKBNH2PMYa4i
        YOYJL9hi9HHT4x0G8TNb8IRgruBJAW+RAG36GGPOlDhwk/0PQDSOMFeQDgUAAAAASUVORK5CYII=
</value>
  </data>
  <data name="PictureBox4.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACIAAAAjCAYAAADxG9hnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAABJtJREFUWEe9mEts
        VFUYxwekgYCNkCYkrMQmPhJjQmTFTtYmJixINOqKpOJCg8+NhtIVsEHShdQpBa2DQB3knYLjzLQyldJp
        p1P6oC1TKcirgE2ZlJbSzozf73DOeDudztx7QZv8e++ce873/e53vvO6Czw5f5lMJrco+zueuP6s/HhT
        tF60RvSC6Dld4b5cr4jiorDotGh8XmPFHgCSq87Lf70uqu1KXJ9J3Lg7cns0OZB88LDj0fRMSyqdbkbc
        U8Yz6lCXNrqtR64FNYfLCiGNnxft7rlyc2JkNNk/k0r9Ls/DojrRRtGLolIt7injWZi6tKEtNkSrC8HM
        CyKNKqQrUjfujQ3xxmLcLyrLF7F5yqjrpy02sIXN+WDygkjlSt5kfPJhVEdgpQOA3K6lbRhbOjqV+WDm
        gEilqr6rt5JT0udi4OgTAOQC+bGJbXzkwswCkYcfQC2JFxGAPU8RwkDtwbaOzGYrTBaEZKIfxyen6A7y
        Yc7oeUplfnzonMkmcBakY+BqNUmlc+K/gjB2w/jCp4mKAmGsE650Ot0kIK4S87WPaypEGa42IrcSX7qL
        1gKj/oRsL2PebXLifMP2w5nt/kjmyLmLX9sAITJ+fIrvOgUi/0qZBfVk5WSeUGEWiB0GApCBayPrbYKU
        4VPPwKWejv7ht5mSneaG7or29785qiKBvj3dNqFnWbs5FsY3DICYbvHafBOiECAKX3wfyBxp6b0T6R0e
        AsR7JnrLrg1dz6u6RxgAaWOx0uuErTf5cn9gEoDR5ESrtKv8pLaxG5Bjf/SyFtmyoettxDcMntjgtXt6
        Ait3YOQEAKJVtNnqC40BUncmWuvABsDl+IaBZJ3SixqrqJO3Mcm64itfSOVIMDa4xaGNUnzD8MQg7+5s
        eAuIXcfPTwvES65BXHZNNnKfexurAalpjI45hJjdNW6S1eqw6kAoCIgvFO9zAfJvsrb1/blPz6q2h6/V
        4bYDoUFA6n+LnXIBooYvDJ5I56X33ExoxilDGZBDTfHdLkDUhAaDZ9/BX1a4neJlYiuvOtSsRsz53uF3
        HIKUpVJpNcXDwJq34EJP4gfdPY72IZurj1VYRoyaUxxILXr4hkGtvmcj0XVutgHbfMHvANkfiN1xAABs
        mdkG4Du7DRCWZ1q6+mucbozMiKk7297lEISNUQKf+LaCeBpO/vqyi63iZwIQFnG12y1mqzjTcCrwyqwd
        mt4vLmxq797yf22e8SV+F+YDgWfRuVjfTstxwlHyFolK9jiBD3zl3cVbzhUlze09Ox4fsNSOntC72sdq
        MH3AmlIHLGyLr5KC5xoNw1AqCbZ2fmqOnHpT7fTIuVxA/LQ1R05sYpvhWhTEhJfQeX88/Kr05V5zCGcC
        0hEyh3D2MEu1uM8ewqVuszmEYwNbud1RsGus/UwyiZbU/3z8DQnpT8yCQ+azxIT6LBHhjRH3SSnjswR1
        qCttDtb7T/AtZYk1MW2dffMlHGMdY5s+/GjVyWCkQhz4Wrsvx2Xl/lu2EdOIe8p4Rh3qagA1TxSTJUcf
        3xb5hkH+LBItFi0VLRPxFQlxTxnPqDMnDwrZ/gdjQpjTNnREAwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnToDefault.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAFwAAAAWCAYAAABNLPtSAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAPVJREFUaEPtmbEN
        wjAQRQ1IsBRZJJFgAxgASnpYgVnicSgYAGR8kpsQo1iRP9WLdGUup2f7+ewsTrtl75zbxqj5+Mv93dyO
        G0nuWGjzeL4kua3u834ly+0i8KCIEIK7HtZBFNK6FTwspzGRFi6CbYMorRvg45UC8O9ZgVLGqkYpmf3L
        oKAUlDLd2aAUlFLU/qKUfC8v9SwOx+E4fM4qoC2kLSy+e8HhnDTLNCudKVxeDQcBh+NwHF50Osu1SBzt
        fx/t+zk95cQ7PgHvBR736QeErG777ahiYptmW/kDPubrEvA2wqkJ3WB3CbisbiUTZ2CI/zH4AOmE5emk
        jM5GAAAAAElFTkSuQmCC
</value>
  </data>
</root>