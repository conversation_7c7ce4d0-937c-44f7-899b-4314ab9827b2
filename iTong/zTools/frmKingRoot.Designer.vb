﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmKingRoot
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmKingRoot))
        Me.lblDiscription = New iTong.Components.tbLabel()
        Me.lblTitle = New iTong.Components.tbLabel()
        Me.pbImage = New System.Windows.Forms.PictureBox()
        Me.btnInstall = New iTong.Components.tbButton()
        Me.lblDiscriptionDetail = New iTong.Components.tbLabel()
        Me.lblTip = New System.Windows.Forms.Label()
        CType(Me.pbImage, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(416, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(392, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        Me.btn_normal.Visible = False
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(368, 0)
        Me.btn_minimize.Visible = False
        '
        'lblDiscription
        '
        Me.lblDiscription.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblDiscription.BackColor = System.Drawing.Color.Transparent
        Me.lblDiscription.Location = New System.Drawing.Point(97, 76)
        Me.lblDiscription.Name = "lblDiscription"
        Me.lblDiscription.Size = New System.Drawing.Size(333, 29)
        Me.lblDiscription.TabIndex = 109
        Me.lblDiscription.tbAdriftWhenHover = False
        Me.lblDiscription.tbAutoEllipsis = False
        Me.lblDiscription.tbAutoSize = False
        Me.lblDiscription.tbHideImage = False
        Me.lblDiscription.tbIconImage = Nothing
        Me.lblDiscription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDiscription.tbIconPlaceText = 5
        Me.lblDiscription.tbShadow = False
        Me.lblDiscription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDiscription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDiscription.tbShowScrolling = False
        Me.lblDiscription.Text = "KingRoot是一款获取手机Root权限的工具" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'lblTitle
        '
        Me.lblTitle.AutoEllipsis = True
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblTitle.Location = New System.Drawing.Point(97, 46)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(322, 25)
        Me.lblTitle.TabIndex = 113
        Me.lblTitle.tbAdriftWhenHover = False
        Me.lblTitle.tbAutoEllipsis = True
        Me.lblTitle.tbAutoSize = False
        Me.lblTitle.tbHideImage = False
        Me.lblTitle.tbIconImage = Nothing
        Me.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.lblTitle.tbIconPlaceText = 5
        Me.lblTitle.tbShadow = False
        Me.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTitle.tbShowScrolling = False
        Me.lblTitle.Text = "KingRoot"
        '
        'pbImage
        '
        Me.pbImage.BackColor = System.Drawing.Color.Transparent
        Me.pbImage.Image = Global.iTong.My.Resources.Resources.more_KingRoot
        Me.pbImage.Location = New System.Drawing.Point(18, 47)
        Me.pbImage.Name = "pbImage"
        Me.pbImage.Size = New System.Drawing.Size(58, 58)
        Me.pbImage.TabIndex = 112
        Me.pbImage.TabStop = False
        '
        'btnInstall
        '
        Me.btnInstall.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnInstall.BackColor = System.Drawing.Color.Transparent
        Me.btnInstall.BindingForm = Nothing
        Me.btnInstall.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnInstall.Location = New System.Drawing.Point(331, 263)
        Me.btnInstall.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnInstall.Name = "btnInstall"
        Me.btnInstall.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnInstall.Selectable = True
        Me.btnInstall.Size = New System.Drawing.Size(70, 25)
        Me.btnInstall.TabIndex = 110
        Me.btnInstall.tbAdriftIconWhenHover = False
        Me.btnInstall.tbAutoSize = False
        Me.btnInstall.tbAutoSizeEx = True
        Me.btnInstall.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_welcome_connect
        Me.btnInstall.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbBadgeNumber = 0
        Me.btnInstall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstall.tbEndEllipsis = False
        Me.btnInstall.tbIconHoldPlace = True
        Me.btnInstall.tbIconImage = Nothing
        Me.btnInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInstall.tbIconMore = False
        Me.btnInstall.tbIconMouseDown = Nothing
        Me.btnInstall.tbIconMouseHover = Nothing
        Me.btnInstall.tbIconMouseLeave = Nothing
        Me.btnInstall.tbIconPlaceText = 2
        Me.btnInstall.tbIconReadOnly = Nothing
        Me.btnInstall.tbImageMouseDown = Nothing
        Me.btnInstall.tbImageMouseHover = Nothing
        Me.btnInstall.tbImageMouseLeave = Nothing
        Me.btnInstall.tbProgressValue = 50
        Me.btnInstall.tbReadOnly = False
        Me.btnInstall.tbReadOnlyText = False
        Me.btnInstall.tbShadow = False
        Me.btnInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstall.tbShowDot = False
        Me.btnInstall.tbShowNew = False
        Me.btnInstall.tbShowProgress = False
        Me.btnInstall.tbShowToolTipOnButton = False
        Me.btnInstall.tbSplit = "13,11,13,11"
        Me.btnInstall.tbText = "立即安装"
        Me.btnInstall.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbTextColor = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDown = System.Drawing.Color.White
        Me.btnInstall.tbTextColorHover = System.Drawing.Color.White
        Me.btnInstall.tbTextMouseDownPlace = 0
        Me.btnInstall.tbToolTip = ""
        Me.btnInstall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnInstall.VisibleEx = True
        '
        'lblDiscriptionDetail
        '
        Me.lblDiscriptionDetail.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblDiscriptionDetail.BackColor = System.Drawing.Color.Transparent
        Me.lblDiscriptionDetail.Location = New System.Drawing.Point(16, 108)
        Me.lblDiscriptionDetail.Name = "lblDiscriptionDetail"
        Me.lblDiscriptionDetail.Size = New System.Drawing.Size(414, 138)
        Me.lblDiscriptionDetail.TabIndex = 116
        Me.lblDiscriptionDetail.tbAdriftWhenHover = False
        Me.lblDiscriptionDetail.tbAutoEllipsis = False
        Me.lblDiscriptionDetail.tbAutoSize = False
        Me.lblDiscriptionDetail.tbHideImage = False
        Me.lblDiscriptionDetail.tbIconImage = Nothing
        Me.lblDiscriptionDetail.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDiscriptionDetail.tbIconPlaceText = 5
        Me.lblDiscriptionDetail.tbShadow = False
        Me.lblDiscriptionDetail.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDiscriptionDetail.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDiscriptionDetail.tbShowScrolling = False
        Me.lblDiscriptionDetail.Text = "    KingRoot为用户提供简单快捷的一键Root方案,集成10套获取Root权限引" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "擎,支持Android 2.1-4.1，超过65个品牌，接近50" & _
    "0款型号的手机获取Root权" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "限。Root过程安全稳定，无需用户干预。获取权限后，更可以管理敏感的隐" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "私权限。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'lblTip
        '
        Me.lblTip.AutoSize = True
        Me.lblTip.Location = New System.Drawing.Point(44, 263)
        Me.lblTip.Name = "lblTip"
        Me.lblTip.Size = New System.Drawing.Size(0, 12)
        Me.lblTip.TabIndex = 117
        '
        'frmKingRoot
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(440, 300)
        Me.Controls.Add(Me.lblTip)
        Me.Controls.Add(Me.lblDiscriptionDetail)
        Me.Controls.Add(Me.lblDiscription)
        Me.Controls.Add(Me.lblTitle)
        Me.Controls.Add(Me.pbImage)
        Me.Controls.Add(Me.btnInstall)
        Me.Name = "frmKingRoot"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,31,5,54"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "KingRoot"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.btnInstall, 0)
        Me.Controls.SetChildIndex(Me.pbImage, 0)
        Me.Controls.SetChildIndex(Me.lblTitle, 0)
        Me.Controls.SetChildIndex(Me.lblDiscription, 0)
        Me.Controls.SetChildIndex(Me.lblDiscriptionDetail, 0)
        Me.Controls.SetChildIndex(Me.lblTip, 0)
        CType(Me.pbImage, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents lblDiscription As iTong.Components.tbLabel
    Friend WithEvents lblTitle As iTong.Components.tbLabel
    Friend WithEvents pbImage As System.Windows.Forms.PictureBox
    Friend WithEvents btnInstall As iTong.Components.tbButton
    Friend WithEvents lblDiscriptionDetail As iTong.Components.tbLabel
    Friend WithEvents lblTip As System.Windows.Forms.Label
End Class
