﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmRepair
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmRepair))
        Me.pnlLoadingEx = New iTong.Components.tbPanel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.pnlSearch = New iTong.Components.tbPanel()
        Me.txtSearch = New iTong.Components.tbSearch()
        Me.btnSearch = New iTong.Components.tbButton()
        Me.txtNameValue = New iTong.Components.tbTextBox()
        Me.pnlBottom = New iTong.Components.tbPanel()
        Me.lblJailbreak = New iTong.Components.tbLabel()
        Me.btnJailbreak = New iTong.Components.tbButton()
        Me.lblState = New iTong.Components.tbLabel()
        Me.lblHelper = New System.Windows.Forms.Label()
        Me.pnlMain = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlIcon = New iTong.Components.tbPanel()
        Me.pnlEmpty = New iTong.Components.tbPanel()
        Me.split = New iTong.Components.tbButton()
        Me.picFileEmpty = New System.Windows.Forms.PictureBox()
        Me.lblFileEmpty = New iTong.Components.tbLabel()
        Me.flpContainer = New iTong.tbFlowLayoutPanelEx()
        Me.btnminimize = New iTong.Components.tbButton()
        Me.pnlLoadingEx.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlSearch.SuspendLayout()
        Me.pnlBottom.SuspendLayout()
        Me.pnlMain.SuspendLayout()
        Me.pnlIcon.SuspendLayout()
        Me.pnlEmpty.SuspendLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'pnlLoadingEx
        '
        Me.pnlLoadingEx.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoadingEx.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlLoadingEx.Controls.Add(Me.lblLoading)
        Me.pnlLoadingEx.Controls.Add(Me.picLoading)
        Me.pnlLoadingEx.Location = New System.Drawing.Point(0, 0)
        Me.pnlLoadingEx.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoadingEx.Name = "pnlLoadingEx"
        Me.pnlMain.SetRowSpan(Me.pnlLoadingEx, 3)
        Me.pnlLoadingEx.Size = New System.Drawing.Size(300, 541)
        Me.pnlLoadingEx.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoadingEx.TabIndex = 8
        Me.pnlLoadingEx.tbBackgroundImage = Nothing
        Me.pnlLoadingEx.tbShowWatermark = False
        Me.pnlLoadingEx.tbSplit = "0,0,0,0"
        Me.pnlLoadingEx.tbWatermark = Nothing
        Me.pnlLoadingEx.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoadingEx.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(69, Byte), Integer), CType(CType(69, Byte), Integer), CType(CType(69, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(-47, 90)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(395, 48)
        Me.lblLoading.TabIndex = 13
        Me.lblLoading.Text = "正在检测设备中闪退的应用..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picLoading
        '
        Me.picLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.picLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_repair
        Me.picLoading.Location = New System.Drawing.Point(-93, 167)
        Me.picLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(486, 284)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picLoading.TabIndex = 12
        Me.picLoading.TabStop = False
        '
        'pnlSearch
        '
        Me.pnlSearch.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlSearch.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlSearch.Controls.Add(Me.txtSearch)
        Me.pnlSearch.Controls.Add(Me.btnSearch)
        Me.pnlSearch.Controls.Add(Me.txtNameValue)
        Me.pnlSearch.Location = New System.Drawing.Point(300, 0)
        Me.pnlSearch.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlSearch.Name = "pnlSearch"
        Me.pnlSearch.Size = New System.Drawing.Size(498, 104)
        Me.pnlSearch.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlSearch.TabIndex = 9
        Me.pnlSearch.tbBackgroundImage = Nothing
        Me.pnlSearch.tbShowWatermark = False
        Me.pnlSearch.tbSplit = "0,0,0,0"
        Me.pnlSearch.tbWatermark = Nothing
        Me.pnlSearch.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlSearch.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.Location = New System.Drawing.Point(50, 7)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "Search"
        Me.txtSearch.ShowClear = True
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(65, 23)
        Me.txtSearch.TabIndex = 12
        Me.txtSearch.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.5R
        Me.txtSearch.Visible = False
        '
        'btnSearch
        '
        Me.btnSearch.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnSearch.BackColor = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSearch.BindingForm = Nothing
        Me.btnSearch.Location = New System.Drawing.Point(424, 33)
        Me.btnSearch.Margin = New System.Windows.Forms.Padding(0)
        Me.btnSearch.Name = "btnSearch"
        Me.btnSearch.Padding = New System.Windows.Forms.Padding(18, 0, 0, 0)
        Me.btnSearch.Selectable = True
        Me.btnSearch.Size = New System.Drawing.Size(51, 38)
        Me.btnSearch.TabIndex = 2
        Me.btnSearch.tbAdriftIconWhenHover = False
        Me.btnSearch.tbAutoSize = False
        Me.btnSearch.tbAutoSizeEx = True
        Me.btnSearch.tbBackgroundImage = Nothing
        Me.btnSearch.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSearch.tbBadgeNumber = 0
        Me.btnSearch.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSearch.tbEndEllipsis = False
        Me.btnSearch.tbIconHoldPlace = True
        Me.btnSearch.tbIconImage = Global.iTong.My.Resources.Resources.icon_search
        Me.btnSearch.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSearch.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSearch.tbIconMore = False
        Me.btnSearch.tbIconMouseDown = Nothing
        Me.btnSearch.tbIconMouseHover = Nothing
        Me.btnSearch.tbIconMouseLeave = Nothing
        Me.btnSearch.tbIconPlaceText = 0
        Me.btnSearch.tbIconReadOnly = Nothing
        Me.btnSearch.tbImageMouseDown = Nothing
        Me.btnSearch.tbImageMouseHover = Nothing
        Me.btnSearch.tbImageMouseLeave = Nothing
        Me.btnSearch.tbProgressValue = 50
        Me.btnSearch.tbReadOnly = False
        Me.btnSearch.tbReadOnlyText = False
        Me.btnSearch.tbShadow = False
        Me.btnSearch.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSearch.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSearch.tbShowDot = False
        Me.btnSearch.tbShowMoreIconImg = CType(resources.GetObject("btnSearch.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSearch.tbShowNew = False
        Me.btnSearch.tbShowProgress = False
        Me.btnSearch.tbShowTip = True
        Me.btnSearch.tbShowToolTipOnButton = False
        Me.btnSearch.tbSplit = "3,3,3,3"
        Me.btnSearch.tbText = ""
        Me.btnSearch.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearch.tbTextColor = System.Drawing.Color.White
        Me.btnSearch.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSearch.tbTextColorDown = System.Drawing.Color.White
        Me.btnSearch.tbTextColorHover = System.Drawing.Color.White
        Me.btnSearch.tbTextMouseDownPlace = 0
        Me.btnSearch.tbToolTip = ""
        Me.btnSearch.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSearch.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSearch.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearch.VisibleEx = True
        '
        'txtNameValue
        '
        Me.txtNameValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtNameValue.BackColor = System.Drawing.Color.White
        Me.txtNameValue.BorderColor = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtNameValue.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtNameValue.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtNameValue.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtNameValue.ForeColor = System.Drawing.Color.Black
        Me.txtNameValue.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNameValue.Location = New System.Drawing.Point(34, 33)
        Me.txtNameValue.Margin = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtNameValue.MaxLength = 50
        Me.txtNameValue.Name = "txtNameValue"
        Me.txtNameValue.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtNameValue.Size = New System.Drawing.Size(390, 37)
        Me.txtNameValue.TabIndex = 1
        Me.txtNameValue.Tag = Nothing
        Me.txtNameValue.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtNameValue.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtNameValue.tbSelMark = True
        Me.txtNameValue.tbTextBind = ""
        Me.txtNameValue.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtNameValue.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNameValue.TextPadding = New System.Windows.Forms.Padding(10, 3, 3, 3)
        Me.txtNameValue.TextTip = ""
        '
        'pnlBottom
        '
        Me.pnlBottom.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlBottom.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(227, Byte), Integer))
        Me.pnlBottom.Controls.Add(Me.lblJailbreak)
        Me.pnlBottom.Controls.Add(Me.btnJailbreak)
        Me.pnlBottom.Location = New System.Drawing.Point(300, 503)
        Me.pnlBottom.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(498, 38)
        Me.pnlBottom.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBottom.TabIndex = 9
        Me.pnlBottom.tbBackgroundImage = Nothing
        Me.pnlBottom.tbShowWatermark = False
        Me.pnlBottom.tbSplit = "0,0,0,0"
        Me.pnlBottom.tbWatermark = Nothing
        Me.pnlBottom.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBottom.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblJailbreak
        '
        Me.lblJailbreak.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblJailbreak.AutoSize = True
        Me.lblJailbreak.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblJailbreak.Location = New System.Drawing.Point(167, 13)
        Me.lblJailbreak.Name = "lblJailbreak"
        Me.lblJailbreak.Size = New System.Drawing.Size(53, 12)
        Me.lblJailbreak.TabIndex = 22
        Me.lblJailbreak.tbAdriftWhenHover = False
        Me.lblJailbreak.tbAutoEllipsis = False
        Me.lblJailbreak.tbAutoSize = True
        Me.lblJailbreak.tbHideImage = False
        Me.lblJailbreak.tbIconImage = Nothing
        Me.lblJailbreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblJailbreak.tbIconPlaceText = 5
        Me.lblJailbreak.tbShadow = False
        Me.lblJailbreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblJailbreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblJailbreak.tbShowScrolling = False
        Me.lblJailbreak.Text = "杜绝闪退"
        '
        'btnJailbreak
        '
        Me.btnJailbreak.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnJailbreak.BackColor = System.Drawing.Color.Transparent
        Me.btnJailbreak.BindingForm = Nothing
        Me.btnJailbreak.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnJailbreak.Location = New System.Drawing.Point(238, 8)
        Me.btnJailbreak.Name = "btnJailbreak"
        Me.btnJailbreak.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnJailbreak.Selectable = True
        Me.btnJailbreak.Size = New System.Drawing.Size(73, 23)
        Me.btnJailbreak.TabIndex = 21
        Me.btnJailbreak.tbAdriftIconWhenHover = False
        Me.btnJailbreak.tbAutoSize = False
        Me.btnJailbreak.tbAutoSizeEx = True
        Me.btnJailbreak.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnJailbreak.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnJailbreak.tbBadgeNumber = 0
        Me.btnJailbreak.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnJailbreak.tbEndEllipsis = False
        Me.btnJailbreak.tbIconHoldPlace = True
        Me.btnJailbreak.tbIconImage = Nothing
        Me.btnJailbreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailbreak.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnJailbreak.tbIconMore = False
        Me.btnJailbreak.tbIconMouseDown = Nothing
        Me.btnJailbreak.tbIconMouseHover = Nothing
        Me.btnJailbreak.tbIconMouseLeave = Nothing
        Me.btnJailbreak.tbIconPlaceText = 2
        Me.btnJailbreak.tbIconReadOnly = Nothing
        Me.btnJailbreak.tbImageMouseDown = Nothing
        Me.btnJailbreak.tbImageMouseHover = Nothing
        Me.btnJailbreak.tbImageMouseLeave = Nothing
        Me.btnJailbreak.tbProgressValue = 50
        Me.btnJailbreak.tbReadOnly = False
        Me.btnJailbreak.tbReadOnlyText = False
        Me.btnJailbreak.tbShadow = False
        Me.btnJailbreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnJailbreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnJailbreak.tbShowDot = False
        Me.btnJailbreak.tbShowMoreIconImg = CType(resources.GetObject("btnJailbreak.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnJailbreak.tbShowNew = False
        Me.btnJailbreak.tbShowProgress = False
        Me.btnJailbreak.tbShowTip = True
        Me.btnJailbreak.tbShowToolTipOnButton = False
        Me.btnJailbreak.tbSplit = "13,11,13,11"
        Me.btnJailbreak.tbText = "立即越狱"
        Me.btnJailbreak.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailbreak.tbTextColor = System.Drawing.Color.White
        Me.btnJailbreak.tbTextColorDisable = System.Drawing.Color.White
        Me.btnJailbreak.tbTextColorDown = System.Drawing.Color.White
        Me.btnJailbreak.tbTextColorHover = System.Drawing.Color.White
        Me.btnJailbreak.tbTextMouseDownPlace = 0
        Me.btnJailbreak.tbToolTip = ""
        Me.btnJailbreak.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnJailbreak.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnJailbreak.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailbreak.VisibleEx = True
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(224, 577)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(356, 18)
        Me.lblState.TabIndex = 32
        Me.lblState.tbAdriftWhenHover = False
        Me.lblState.tbAutoEllipsis = False
        Me.lblState.tbAutoSize = False
        Me.lblState.tbHideImage = False
        Me.lblState.tbIconImage = Nothing
        Me.lblState.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblState.tbIconPlaceText = 5
        Me.lblState.tbShadow = False
        Me.lblState.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblState.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblState.tbShowScrolling = False
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblHelper
        '
        Me.lblHelper.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblHelper.AutoSize = True
        Me.lblHelper.BackColor = System.Drawing.Color.Transparent
        Me.lblHelper.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblHelper.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblHelper.Location = New System.Drawing.Point(8, 579)
        Me.lblHelper.Name = "lblHelper"
        Me.lblHelper.Size = New System.Drawing.Size(119, 12)
        Me.lblHelper.TabIndex = 33
        Me.lblHelper.Text = "仍然无法修复，求救>"
        Me.lblHelper.Visible = False
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.ColumnCount = 2
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 300.0!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 498.0!))
        Me.pnlMain.Controls.Add(Me.pnlLoadingEx, 0, 0)
        Me.pnlMain.Controls.Add(Me.pnlSearch, 1, 0)
        Me.pnlMain.Controls.Add(Me.pnlBottom, 1, 2)
        Me.pnlMain.Controls.Add(Me.pnlIcon, 1, 1)
        Me.pnlMain.Location = New System.Drawing.Point(1, 32)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.RowCount = 3
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 104.0!))
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 38.0!))
        Me.pnlMain.Size = New System.Drawing.Size(798, 541)
        Me.pnlMain.TabIndex = 31
        '
        'pnlIcon
        '
        Me.pnlIcon.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlIcon.Controls.Add(Me.pnlEmpty)
        Me.pnlIcon.Controls.Add(Me.flpContainer)
        Me.pnlIcon.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlIcon.Location = New System.Drawing.Point(300, 104)
        Me.pnlIcon.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlIcon.Name = "pnlIcon"
        Me.pnlIcon.Size = New System.Drawing.Size(498, 399)
        Me.pnlIcon.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlIcon.TabIndex = 10
        Me.pnlIcon.tbBackgroundImage = Global.iTong.My.Resources.Resources.repair_bg_white
        Me.pnlIcon.tbShowWatermark = False
        Me.pnlIcon.tbSplit = "20,3,20,3"
        Me.pnlIcon.tbWatermark = Nothing
        Me.pnlIcon.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlIcon.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlEmpty
        '
        Me.pnlEmpty.Controls.Add(Me.split)
        Me.pnlEmpty.Controls.Add(Me.picFileEmpty)
        Me.pnlEmpty.Controls.Add(Me.lblFileEmpty)
        Me.pnlEmpty.Location = New System.Drawing.Point(3, 87)
        Me.pnlEmpty.Name = "pnlEmpty"
        Me.pnlEmpty.Size = New System.Drawing.Size(402, 106)
        Me.pnlEmpty.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlEmpty.TabIndex = 11
        Me.pnlEmpty.tbBackgroundImage = Nothing
        Me.pnlEmpty.tbShowWatermark = False
        Me.pnlEmpty.tbSplit = "0,0,0,0"
        Me.pnlEmpty.tbWatermark = Nothing
        Me.pnlEmpty.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlEmpty.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'split
        '
        Me.split.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.split.BackColor = System.Drawing.Color.Transparent
        Me.split.BindingForm = Nothing
        Me.split.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.split.Location = New System.Drawing.Point(12, 15)
        Me.split.Name = "split"
        Me.split.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.split.Selectable = True
        Me.split.Size = New System.Drawing.Size(378, 1)
        Me.split.TabIndex = 44
        Me.split.tbAdriftIconWhenHover = False
        Me.split.tbAutoSize = False
        Me.split.tbAutoSizeEx = False
        Me.split.tbBackgroundImage = Global.iTong.My.Resources.Resources.welcome_btn_split
        Me.split.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.split.tbBadgeNumber = 0
        Me.split.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.split.tbEndEllipsis = False
        Me.split.tbIconHoldPlace = True
        Me.split.tbIconImage = Nothing
        Me.split.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.split.tbIconImageState = iTong.Components.ImageState.OneState
        Me.split.tbIconMore = False
        Me.split.tbIconMouseDown = Nothing
        Me.split.tbIconMouseHover = Nothing
        Me.split.tbIconMouseLeave = Nothing
        Me.split.tbIconPlaceText = 2
        Me.split.tbIconReadOnly = Nothing
        Me.split.tbImageMouseDown = Nothing
        Me.split.tbImageMouseHover = Nothing
        Me.split.tbImageMouseLeave = Nothing
        Me.split.tbProgressValue = 50
        Me.split.tbReadOnly = False
        Me.split.tbReadOnlyText = False
        Me.split.tbShadow = False
        Me.split.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.split.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.split.tbShowDot = False
        Me.split.tbShowMoreIconImg = CType(resources.GetObject("split.tbShowMoreIconImg"), System.Drawing.Image)
        Me.split.tbShowNew = False
        Me.split.tbShowProgress = False
        Me.split.tbShowTip = True
        Me.split.tbShowToolTipOnButton = False
        Me.split.tbSplit = "35,0,35,0"
        Me.split.tbText = ""
        Me.split.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.split.tbTextColor = System.Drawing.Color.White
        Me.split.tbTextColorDisable = System.Drawing.Color.White
        Me.split.tbTextColorDown = System.Drawing.Color.White
        Me.split.tbTextColorHover = System.Drawing.Color.White
        Me.split.tbTextMouseDownPlace = 0
        Me.split.tbToolTip = ""
        Me.split.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.split.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.split.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.split.VisibleEx = True
        '
        'picFileEmpty
        '
        Me.picFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picFileEmpty.Image = Global.iTong.My.Resources.Resources.file_fileempty
        Me.picFileEmpty.Location = New System.Drawing.Point(100, 12)
        Me.picFileEmpty.Name = "picFileEmpty"
        Me.picFileEmpty.Size = New System.Drawing.Size(77, 82)
        Me.picFileEmpty.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picFileEmpty.TabIndex = 3
        Me.picFileEmpty.TabStop = False
        '
        'lblFileEmpty
        '
        Me.lblFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFileEmpty.AutoSize = True
        Me.lblFileEmpty.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFileEmpty.ForeColor = System.Drawing.Color.FromArgb(CType(CType(159, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(163, Byte), Integer))
        Me.lblFileEmpty.Location = New System.Drawing.Point(192, 47)
        Me.lblFileEmpty.Name = "lblFileEmpty"
        Me.lblFileEmpty.Size = New System.Drawing.Size(104, 16)
        Me.lblFileEmpty.TabIndex = 2
        Me.lblFileEmpty.tbAdriftWhenHover = False
        Me.lblFileEmpty.tbAutoEllipsis = False
        Me.lblFileEmpty.tbAutoSize = True
        Me.lblFileEmpty.tbHideImage = False
        Me.lblFileEmpty.tbIconImage = Nothing
        Me.lblFileEmpty.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileEmpty.tbIconPlaceText = 5
        Me.lblFileEmpty.tbShadow = False
        Me.lblFileEmpty.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileEmpty.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileEmpty.tbShowScrolling = False
        Me.lblFileEmpty.Text = "这里没有内容"
        '
        'flpContainer
        '
        Me.flpContainer.AutoScroll = True
        Me.flpContainer.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink
        Me.flpContainer.BackColor = System.Drawing.Color.Transparent
        Me.flpContainer.Location = New System.Drawing.Point(0, 0)
        Me.flpContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.flpContainer.Name = "flpContainer"
        Me.flpContainer.Padding = New System.Windows.Forms.Padding(15, 0, 0, 0)
        Me.flpContainer.Size = New System.Drawing.Size(405, 84)
        Me.flpContainer.TabIndex = 10
        Me.flpContainer.tbGroupPadding = 0
        '
        'btnminimize
        '
        Me.btnminimize.BackColor = System.Drawing.Color.Transparent
        Me.btnminimize.BindingForm = Nothing
        Me.btnminimize.Location = New System.Drawing.Point(670, 2)
        Me.btnminimize.Name = "btnminimize"
        Me.btnminimize.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnminimize.Selectable = True
        Me.btnminimize.Size = New System.Drawing.Size(24, 24)
        Me.btnminimize.TabIndex = 34
        Me.btnminimize.tbAdriftIconWhenHover = False
        Me.btnminimize.tbAutoSize = False
        Me.btnminimize.tbAutoSizeEx = False
        Me.btnminimize.tbBackgroundImage = Nothing
        Me.btnminimize.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnminimize.tbBadgeNumber = 0
        Me.btnminimize.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnminimize.tbEndEllipsis = False
        Me.btnminimize.tbIconHoldPlace = True
        Me.btnminimize.tbIconImage = Nothing
        Me.btnminimize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnminimize.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnminimize.tbIconMore = False
        Me.btnminimize.tbIconMouseDown = Nothing
        Me.btnminimize.tbIconMouseHover = Nothing
        Me.btnminimize.tbIconMouseLeave = Nothing
        Me.btnminimize.tbIconPlaceText = 2
        Me.btnminimize.tbIconReadOnly = Nothing
        Me.btnminimize.tbImageMouseDown = Nothing
        Me.btnminimize.tbImageMouseHover = Nothing
        Me.btnminimize.tbImageMouseLeave = Nothing
        Me.btnminimize.tbProgressValue = 50
        Me.btnminimize.tbReadOnly = False
        Me.btnminimize.tbReadOnlyText = False
        Me.btnminimize.tbShadow = False
        Me.btnminimize.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnminimize.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnminimize.tbShowDot = False
        Me.btnminimize.tbShowMoreIconImg = CType(resources.GetObject("btnminimize.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnminimize.tbShowNew = False
        Me.btnminimize.tbShowProgress = False
        Me.btnminimize.tbShowTip = True
        Me.btnminimize.tbShowToolTipOnButton = False
        Me.btnminimize.tbSplit = "3,3,3,3"
        Me.btnminimize.tbText = ""
        Me.btnminimize.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnminimize.tbTextColor = System.Drawing.Color.White
        Me.btnminimize.tbTextColorDisable = System.Drawing.Color.White
        Me.btnminimize.tbTextColorDown = System.Drawing.Color.White
        Me.btnminimize.tbTextColorHover = System.Drawing.Color.White
        Me.btnminimize.tbTextMouseDownPlace = 0
        Me.btnminimize.tbToolTip = ""
        Me.btnminimize.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnminimize.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnminimize.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnminimize.VisibleEx = True
        '
        'frmRepair
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.lblHelper)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.lblState)
        Me.Controls.Add(Me.btnminimize)
        Me.Name = "frmRepair"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "强力修复"
        Me.Controls.SetChildIndex(Me.btnminimize, 0)
        Me.Controls.SetChildIndex(Me.lblState, 0)
        Me.Controls.SetChildIndex(Me.pnlMain, 0)
        Me.Controls.SetChildIndex(Me.lblHelper, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlLoadingEx.ResumeLayout(False)
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlSearch.ResumeLayout(False)
        Me.pnlBottom.ResumeLayout(False)
        Me.pnlMain.ResumeLayout(False)
        Me.pnlIcon.ResumeLayout(False)
        Me.pnlEmpty.ResumeLayout(False)
        Me.pnlEmpty.PerformLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents pnlLoadingEx As tbPanel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblState As tbLabel
    Friend WithEvents pnlSearch As tbPanel
    Friend WithEvents pnlBottom As tbPanel
    Friend WithEvents lblHelper As System.Windows.Forms.Label
    Friend WithEvents lblJailbreak As tbLabel
    Friend WithEvents btnJailbreak As tbButton
    Friend WithEvents txtNameValue As tbTextBox
    Friend WithEvents btnSearch As tbButton
    Friend WithEvents pnlMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents flpContainer As iTong.tbFlowLayoutPanelEx
    Friend WithEvents txtSearch As tbSearch
    Friend WithEvents pnlIcon As tbPanel
    Friend WithEvents pnlEmpty As tbPanel
    Friend WithEvents picFileEmpty As System.Windows.Forms.PictureBox
    Friend WithEvents lblFileEmpty As tbLabel
    Friend WithEvents split As tbButton
    Friend WithEvents btnminimize As tbButton
End Class
