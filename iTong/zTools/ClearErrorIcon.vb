﻿Imports System.ComponentModel
Imports System.IO

'Main.Button.ClearErrorIcon                               	=	清理灰色图标
'Tools.ClearErrorIcon.Checking                            	=	正在检查灰色图标...
'Tools.ClearErrorIcon.ClearFail                           	=	清理灰色图标失败，请重新检查。
'Tools.ClearErrorIcon.ClearFailDetail                     	=	{0}个灰色图标清理失败：
'Tools.ClearErrorIcon.ClearSucceed                        	=	成功清理了{0}个灰色图标：
'Tools.ClearErrorIcon.NoErrorIcon                         	=	没有检查到灰色图标。
'Tools.Message.ClearErrorIconDescription                  	=	如果您的设备在安装软件时出现异常，\r\n\r\n导致设备桌面出现了灰色图标，同时无法手动删除，\r\n\r\n那么您可以尝试用此功能进行清理。"

Public Class ClearErrorIcon
    Inherits tbBackgroundWorker

    Private Language As LanguageInterface

#Region "--- 初始化 ---"

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice, ByVal item As tbButton)
        MyBase.New(app, device, item)

        Me.Language = app.Language
    End Sub

#End Region
    
#Region "--- 实现OnDoWork ---"

    Protected Overrides Sub OnDoWork(ByVal e As System.ComponentModel.DoWorkEventArgs)
        MyBase.OnDoWork(e)

        Try
            Me.OnProgressChanged(New ProgressChangedEventArgs(10, Me.Language.GetString("Tools.ClearErrorIcon.Checking")))   '"正在检查灰色图标..."

            '1、加载图标列表
            Dim iconMgr As iPhoneIcon = IconHelper.Instance(Me.mDevice)
            If Not iconMgr.LoadFromDevice() Then
                Me.OnProgressChanged(New ProgressChangedEventArgs(-1, Me.Language.GetString("Tools.ClearErrorIcon.ClearFail"))) '"清理灰色图标失败，请重新检查。"
                Return
            End If

            '2、获取异常图标
            Dim lstErrorIcon As New List(Of IconItem)
            For Each page As IconPage In iconMgr.ListPages  '按页遍历
                For Each item As IconBase In page.ListIcons
                    If TypeOf item Is IconList Then                      '文件夹，需遍历其列表
                        For Each subItem As IconBase In CType(item, IconList).Items
                            Me.CheckIsErrorIcon(subItem, lstErrorIcon)
                        Next
                    Else
                        Me.CheckIsErrorIcon(item, lstErrorIcon)
                    End If
                Next
            Next

            If lstErrorIcon.Count = 0 Then
                Me.OnProgressChanged(New ProgressChangedEventArgs(100, Me.Language.GetString("Tools.ClearErrorIcon.NoErrorIcon"))) '"没有检查到灰色图标。"
                Return
            End If

            '3、卸载异常图标
            Dim lstSucceedIcon As New List(Of IconItem)
            Dim lstFailIcon As New List(Of IconItem)
            Dim strError As String = ""
            Dim isSucceed As Boolean = False
            Dim strName As String = String.Empty

            For Each Item As IconItem In lstErrorIcon

                isSucceed = Me.mDevice.UninstallApplication(Item.DisplayIdentifier, strError)

                If isSucceed Then
                    lstSucceedIcon.Add(Item)
                Else
                    lstFailIcon.Add(Item)
                End If

                strName = IIf(Item.DisplayName.Length > 0, Item.DisplayName, Item.DisplayIdentifier)
                Me.OnProgressChanged(New ProgressChangedEventArgs(50, String.Format(Me.Language.GetString("Clear.Message.Clearing") & "{0}...", strName))) '"正在清理"
            Next

            '4、提示清理完成。（清理失败的要展示出来）
            Dim resultInfo As New StringBuilder()

            If lstSucceedIcon.Count > 0 Then
                resultInfo.Append(String.Format(Me.Language.GetString("Tools.ClearErrorIcon.ClearSucceed"), lstSucceedIcon.Count)) '"成功清理了{0}个灰色图标："

                For index As Integer = 0 To lstSucceedIcon.Count - 1
                    Dim Item As IconItem = lstSucceedIcon(index)
                    strName = IIf(Item.DisplayName.Length > 0, Item.DisplayName, Item.DisplayIdentifier)
                    resultInfo.Append(strName)

                    If index = lstSucceedIcon.Count - 1 Then
                        resultInfo.Append("。")
                    Else
                        resultInfo.Append("、")
                    End If
                Next
            End If

            If lstFailIcon.Count > 0 Then
                resultInfo.Append(vbCrLf)
                resultInfo.Append(String.Format(Me.Language.GetString("Tools.ClearErrorIcon.ClearFailDetail"), lstFailIcon.Count)) '"{0}个灰色图标清理失败："

                For index As Integer = 0 To lstFailIcon.Count - 1
                    Dim Item As IconItem = lstFailIcon(index)
                    strName = IIf(Item.DisplayName.Length > 0, Item.DisplayName, Item.DisplayIdentifier)
                    resultInfo.Append(strName)

                    If index = lstFailIcon.Count - 1 Then
                        resultInfo.Append("。")
                    Else
                        resultInfo.Append("、")
                    End If
                Next
            End If

            Me.OnProgressChanged(New ProgressChangedEventArgs(100, resultInfo.ToString()))

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ClearErrorIcon_OnDoWork")
        End Try
    End Sub

#End Region

#Region "--- 私有方法 ---"

    '图标的类型为IconItem，并且属性isErrorIcon为true，判断是否为异常图标。
    Private Sub CheckIsErrorIcon(ByVal item As IconItem, ByRef lstErrorIcon As List(Of IconItem))
        Dim newIconItem As IconItem = Me.ConvertToIconItem(item)

        If newIconItem IsNot Nothing AndAlso newIconItem.IsErrorIcon Then
            lstErrorIcon.Add(newIconItem)
        End If
    End Sub

    Private Function ConvertToIconItem(ByVal item As IconBase) As IconItem
        Dim newItem As IconItem = Nothing

        If item IsNot Nothing AndAlso TypeOf item Is IconItem Then
            newItem = CType(item, IconItem)
        Else
            Common.Log("转化为图标项失败：" & item.DisplayName & vbTab & item.Index)
        End If

        Return newItem
    End Function

#End Region

End Class