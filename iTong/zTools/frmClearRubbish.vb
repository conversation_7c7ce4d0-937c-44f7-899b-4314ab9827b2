﻿Imports System.ComponentModel
Imports iTong.Device
Imports System.Runtime.InteropServices

Public Class frmClearRubbish
    Private Shared mIsIgnoreListLoad As Boolean = False

    Private bgwScanFiles As BackgroundWorker = Nothing
    Private bgwClearFiles As BackgroundWorker = Nothing
    Private bgwUploadUseMessage As BackgroundWorker = Nothing

    Private mScanResult As RubbishScanResult = Nothing

    Dim WithEvents mTempList As New ClearRubbishList
    Dim WithEvents mCacheList As New ClearRubbishList
    Dim WithEvents mCookieList As New ClearRubbishList
    Dim WithEvents mMediaList As New ClearRubbishList
    Dim WithEvents mCrashLogList As New ClearRubbishList
    Dim WithEvents mPhotosList As New ClearRubbishList
    Dim WithEvents mInstallFailedList As New ClearRubbishList

    Private mStatusTempRow As ExpendStatus = New ExpendStatus
    Private mStatusCacheRow As ExpendStatus = New ExpendStatus
    Private mStatusCookieRow As ExpendStatus = New ExpendStatus
    Private mStatusMediaRow As ExpendStatus = New ExpendStatus
    Private mStatusCrashLogRow As ExpendStatus = New ExpendStatus
    Private mStatusPhotosRow As ExpendStatus = New ExpendStatus
    Private mInstaioFailedRow As ExpendStatus = New ExpendStatus

    Private mScanSetting As RubbishScanSetting = New RubbishScanSetting
    Private mClearSetting As RubbishClearSetting = New RubbishClearSetting

    Private mIconCacheDir As String = ""
    Private mClearingAppName As String = ""
    Private mIsCheckTriggle As Boolean = False
    Private mPreDataListWidth As Integer = 0

    Private mIgnorePlistPath As String = ""
    Private mLoadPlist As Boolean = False

    Private mTotalRubbishSize As Long = 0
    Private mTotalRubbishCount As Integer = 0

    Private mTotalClearSize As Long = 0
    Private mTotalClearCount As Integer = 0
    Private mDictDirName As New Dictionary(Of String, Object)

    Dim mSelectedRubbishSize As Long = 0
    Dim mSelectedRubbishCount As Long = 0

    ' 脏目录
    Private mDictDirtyApp As New Dictionary(Of String, RubbishClearSetting)
    Private mDictIgnoreInfo As New Dictionary(Of String, IgnoreInfo)
    Private mDictIgnoreCatagory As New Dictionary(Of String, String)
    Private mDictSoftwareType As New Dictionary(Of String, String)
    Private mIsFirstPage As Boolean = False

    Private Shared mUserCount As Long = 0
    Private mCrashReporterHandler As IntPtr = IntPtr.Zero

    Shared Sub New()
        Dim strCount As String = IniClass.GetIniSectionKey("Clear", "UserCount", Folder.ConfigIniFile)
        Dim lngCount As Long = Val(strCount)

        If lngCount > 0 Then
            mUserCount = lngCount
        Else
            mUserCount = 2130000 + New Random().Next(1, 9999)
            IniClass.SetIniSectionKey("Clear", "UserCount", mUserCount.ToString(), Folder.ConfigIniFile)
        End If
    End Sub

#Region "--- 初始化 ---"

    Public Sub New()

        ' 此调用是Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice)
        ' 此调用是Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在InitializeComponent() 调用之后添加任何初始化。

        Me.mDevice = device
        Me.mApplication = app
        Me.Language = app.Language
        Me.Icon = My.Resources.iTong
        Me.CanResize = True

        Me.tbGuiBackground = My.Resources.pnl_bg_info
        Me.FilletRadius = 5
        Me.Size = New Size(820, 615)
        Me.MinimumSize = New Size(820, 615)
        Me.tbShowTitlemark = False
        Me.tbShowWatermark = False
        Me.tbTitleForeColor = Color.White

        'Me.btn_normal.Enabled = False

        Me.prgCleanProgress.tbBackgroundImage = My.Resources.jindu_bg_07
        Me.prgScanProgress.tbBackgroundImage = My.Resources.jindu_bg_07

        mUserCount += 1
        IniClass.SetIniSectionKey("Clear", "UserCount", mUserCount.ToString(), Folder.ConfigIniFile)
        Me.FunctionMappingKey = FunctionKey.ClearRubbish
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        Try
            If Not pblnCancelClose Then

                If bgwScanFiles IsNot Nothing Then
                    RemoveHandler bgwScanFiles.DoWork, AddressOf bgwScanFiles_DoWork
                    RemoveHandler bgwScanFiles.ProgressChanged, AddressOf bgwScanFiles_ProgressChanged
                    RemoveHandler bgwScanFiles.RunWorkerCompleted, AddressOf bgwScanFiles_RunWorkerCompleted
                End If

                If bgwClearFiles IsNot Nothing Then
                    RemoveHandler bgwClearFiles.DoWork, AddressOf bgwClearFiles_DoWork
                    RemoveHandler bgwClearFiles.ProgressChanged, AddressOf bgwClearFiles_ProgressChanged
                    RemoveHandler bgwClearFiles.RunWorkerCompleted, AddressOf bgwClearFiles_RunWorkerCompleted
                End If

                If bgwScanFiles IsNot Nothing AndAlso Me.bgwScanFiles.IsBusy Then
                    bgwScanFiles.CancelAsync()
                End If

                If bgwClearFiles IsNot Nothing AndAlso Me.bgwClearFiles.IsBusy Then
                    Me.bgwClearFiles.CancelAsync()
                End If

                If Me.bgwUploadUseMessage IsNot Nothing AndAlso Me.bgwUploadUseMessage.IsBusy Then
                    Me.bgwUploadUseMessage.CancelAsync()
                End If
            End If
        Catch ex As Exception
        End Try

        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

    Private Sub InitPanel()
        Dim cloOushu As Color = Color.FromArgb(250, 251, 252)
        Dim clrJishu As Color = Color.FromArgb(247, 247, 247)
        Me.tbGuiBackground = My.Resources.frm_bg_state
        Me.tbSplit = "20,33,20,33"

        mTempList.BackgroundColor = cloOushu
        mTempList.Icon = My.Resources.tool_clean_junk_file
        mTempList.CaptionText = Me.Language.GetString("Clear.Label.TempFile")                           '"临时文件"
        mTempList.DescribText = Me.Language.GetString("Clear.Label.TempFileDescription")                '"应用程序处理和计算时产生的垃圾"

        mTempList.Checked = True
        mTempList.Anchor = AnchorStyles.Top + AnchorStyles.Left
        Me.pnlListData.Controls.Add(mTempList)
        Me.pnlListData.ScrollControlIntoView(mTempList)

        mCookieList.BackgroundColor = clrJishu
        mCookieList.Icon = My.Resources.tool_clean_cookies
        mCookieList.CaptionText = Me.Language.GetString("Clear.Label.ScriptAndCookie")                  '"脚本文件和Cookie"
        mCookieList.DescribText = Me.Language.GetString("Clear.Label.ScriptAndCookieDescription")       '"应用登录和浏览时产生的文件。清理后可能丢失登录信息。"
        mCookieList.Checked = True
        mCookieList.Anchor = AnchorStyles.Top + AnchorStyles.Left
        Me.pnlListData.Controls.Add(mCookieList)
        Me.pnlListData.ScrollControlIntoView(mCookieList)

        mMediaList.BackgroundColor = cloOushu
        mMediaList.Icon = My.Resources.tool_clean_media
        mMediaList.CaptionText = Me.Language.GetString("Clear.Label.SyncFailureFile")                   '"同步失败的媒体文件"
        mMediaList.DescribText = Me.Language.GetString("Clear.Label.SyncFailureFileDescrip")            '"iTunes同步意外中断产生的""其他""文件。"
        mMediaList.Checked = True
        mMediaList.Anchor = AnchorStyles.Top + AnchorStyles.Left
        Me.pnlListData.Controls.Add(mMediaList)
        Me.pnlListData.ScrollControlIntoView(mMediaList)

        'mCrashLogList
        mCrashLogList.BackgroundColor = clrJishu
        mCrashLogList.Icon = My.Resources.tool_clean_crashlog
        mCrashLogList.CaptionText = Me.Language.GetString("Clear.Label.CrashLogRubbish")                '"应用崩溃记录"
        mCrashLogList.DescribText = Me.Language.GetString("Clear.Label.CrashLogRubbishDescription")     '"iOS生成的应用使用和崩溃的历史记录。"
        mCrashLogList.Checked = True
        mCrashLogList.Anchor = AnchorStyles.Top + AnchorStyles.Left
        Me.pnlListData.Controls.Add(mCrashLogList)
        Me.pnlListData.ScrollControlIntoView(mCrashLogList)

        'mPhotosList
        mPhotosList.BackgroundColor = cloOushu
        mPhotosList.Icon = My.Resources.tool_clean_photos
        mPhotosList.CaptionText = Me.Language.GetString("Clear.Label.PhotosRubbish")                    '"图片缓存"
        mPhotosList.DescribText = Me.Language.GetString("Clear.Label.PhotosRubbishDescription")         '"第三方软件为相机胶卷，图片流和照片图库生成的缓存文件。"
        mPhotosList.Checked = True
        mPhotosList.Anchor = AnchorStyles.Top + AnchorStyles.Left
        Me.pnlListData.Controls.Add(mPhotosList)
        Me.pnlListData.ScrollControlIntoView(mPhotosList)

        Me.mInstallFailedList.BackgroundColor = clrJishu
        mInstallFailedList.Icon = My.Resources.tool_clean_sync
        mInstallFailedList.CaptionText = Me.Language.GetString("Clear.Label.InstallTemp")               '"安装缓存"
        mInstallFailedList.DescribText = Me.Language.GetString("Clear.Label.InstallTempDesc1")          '"1.同步助手安装/备份时的残留文件；"
        mInstallFailedList.DescribText2 = Me.Language.GetString("Clear.Label.InstallTempDesc2")         '"2.在设备的AppStore下载安装时产生的残留文件（清理后需重启）。"
        mInstallFailedList.Checked = True
        mInstallFailedList.Anchor = AnchorStyles.Top + AnchorStyles.Left
        Me.pnlListData.Controls.Add(mInstallFailedList)
        Me.pnlListData.ScrollControlIntoView(mInstallFailedList)

        mCacheList.BackgroundColor = cloOushu
        mCacheList.Icon = My.Resources.tool_clean_off_line
        mCacheList.CaptionText = Me.Language.GetString("Clear.Label.CacheFiles")                        '"离线文件和缓存"
        mCacheList.DescribText = Me.Language.GetString("Clear.Label.CacheFilesDescription")             '"应用运行时从网络下载的图片、视频、音乐等文件。"

        '8.3及以上没越狱设备才提示
        If Me.mDevice.VersionNumber >= 830 AndAlso Not (Me.mDevice.Jailbreaked AndAlso Me.mDevice.InstallCydia) Then
            mCacheList.DescribText2 = Me.Language.GetString("Clear.Label.CacheFilesDescription830")     '"(由于苹果更改了8.3以上固件的目录结构，无法扫描出更多的此类垃圾文件)"
            mCacheList.DescribForeColor2 = Color.Red
        End If

        mCacheList.Checked = True
        mCacheList.Anchor = AnchorStyles.Top + AnchorStyles.Left
        Me.pnlListData.Controls.Add(mCacheList)
        Me.pnlListData.ScrollControlIntoView(mCacheList)


        Me.lblDescrib.Font = CreateFontSafe("微软雅黑", 12, FontStyle.Regular)
        Me.lblScanFinish_Describ.Font = CreateFontSafe("微软雅黑", 12, FontStyle.Regular)


        Me.btnScan.Font = CreateFontSafe("微软雅黑", 14.5, FontStyle.Regular)
        Me.btnClear.Font = CreateFontSafe("微软雅黑", 14.5, FontStyle.Regular)

        Me.btnReportFinish.Font = CreateFontSafe("微软雅黑", 14.5, FontStyle.Regular)
        Me.lblScanFinish_Describ.Font = CreateFontSafe("微软雅黑", 12, FontStyle.Regular)

        Me.pnlCleanning.Location = New Point(26, 16)
        Me.pnlDescrib.Location = New Point(26, 16)
        Me.pnlScanFinish.Location = New Point(26, 16)
        Me.pnlScanning.Location = New Point(26, 16)

        Me.pnlDescrib.Visible = True
        Me.pnlCleanning.Visible = False
        Me.pnlScanFinish.Visible = False
        Me.pnlScanning.Visible = False


        Me.pnlDescrib.Anchor += AnchorStyles.Right
        Me.pnlCleanning.Anchor += AnchorStyles.Right
        Me.pnlScanFinish.Anchor += AnchorStyles.Right
        Me.pnlScanning.Anchor += AnchorStyles.Right


        Me.pnlReport.Anchor = AnchorStyles.Bottom + AnchorStyles.Left + AnchorStyles.Right + AnchorStyles.Top
        Me.pnlFunction.Anchor = AnchorStyles.Bottom + AnchorStyles.Left + AnchorStyles.Right + AnchorStyles.Top
        Me.webFirstPage.Anchor = AnchorStyles.Bottom + AnchorStyles.Left + AnchorStyles.Right + AnchorStyles.Top

    End Sub

    '初始化控件
    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.AutoSetFont = True
        Me.InitPanel()

        Me.InitialFirstPage()
        If Me.webFirstPage.Visible Then
            Me.pnlReport.Visible = False
            Me.pnlFunction.Visible = False
        End If

        bgwScanFiles = New BackgroundWorker()
        bgwScanFiles.WorkerReportsProgress = True
        bgwScanFiles.WorkerSupportsCancellation = True

        AddHandler bgwScanFiles.DoWork, AddressOf bgwScanFiles_DoWork
        AddHandler bgwScanFiles.ProgressChanged, AddressOf bgwScanFiles_ProgressChanged
        AddHandler bgwScanFiles.RunWorkerCompleted, AddressOf bgwScanFiles_RunWorkerCompleted

        bgwClearFiles = New BackgroundWorker()
        bgwClearFiles.WorkerReportsProgress = True
        bgwClearFiles.WorkerSupportsCancellation = True
        AddHandler bgwClearFiles.DoWork, AddressOf bgwClearFiles_DoWork
        AddHandler bgwClearFiles.ProgressChanged, AddressOf bgwClearFiles_ProgressChanged
        AddHandler bgwClearFiles.RunWorkerCompleted, AddressOf bgwClearFiles_RunWorkerCompleted


        '  webFirstPage.Url = New Uri("http://www.china.com")
        'webFirstPage.Navigate(New Uri("http://www.china.com"))
        'Me.chkSelectAll.ThreeState = True

        Me.mIconCacheDir = System.IO.Path.Combine(iTong.CoreFoundation.Folder.CacheFolder, "IconCache\" & Me.mDevice.UniqueDeviceID)
        Me.mIgnorePlistPath = System.IO.Path.Combine(iTong.CoreFoundation.Folder.CacheFolder, "RubbishIgnore.db")

        Folder.CheckFolder(Me.mIconCacheDir)
        '先加载配置
        Me.DownloadIgnorePlist()

        Me.PostFunctionMessage()

        Select Case Folder.LangType
            Case LanguageType.vi_VN, LanguageType.th_TH
                Me.picSinaWeiBo.Image = My.Resources.weibo_facebook32
                Me.picSinaWeiBo.Left = (Me.picSinaWeiBo.Left + Me.picQQWeiBo.Left) \ 2
                Me.lblReportWeibo.Location = New Point(Me.picSinaWeiBo.Right, Me.lblReportWeibo.Top)

                Me.picQQWeiBo.Visible = False
                Me.lblReportQQWeibo.Visible = False

            Case LanguageType.en_US
                Me.picSinaWeiBo.Image = My.Resources.weibo_facebook32
                Me.picQQWeiBo.Image = My.Resources.weibo_twitter32

            Case LanguageType.zh_CN
        End Select

        If Common.VerIs30 Then
            Me.chkSelectAll.BackColor = Color.FromArgb(235, 236, 238)
        End If
        Me.mCrashReporterHandler = Me.mDevice.HandleCrashReportCopyMobile()
    End Sub

    Protected Overrides Sub SetInterface()
        Me.Text = Me.Language.GetString("Main.Button.ClearRubbish")                                     '"垃圾清理"

        Me.lblSuguest.Text = Me.Language.GetString("Clear.Label.Suguest")                               '"每天清一清，手机更健康"
        Me.lblDescrib.Text = Me.Language.GetString("Clear.Label.Description")                           '"全面清理设备中的垃圾，合理利用有限的空间"
        Me.btnScan.Text = Me.Language.GetString("Clear.Label.ScanStart")                                '"开始扫描"
        Me.btnStopScan.Text = Me.Language.GetString("Clear.Label.ScanStop")                             '"停止扫描"
        Me.btnStopClear.Text = Me.Language.GetString("Clear.Label.ClearStop")                           '"停止清理"

        Me.lblReScan.Text = Me.Language.GetString("Clear.Label.ReScan")                                 '"重新扫描"
        Me.btnClear.Text = Me.Language.GetString("Clear.Label.ClearStart")                              '"开始清理"
        Me.crlRubbishFound.StringHeader = Me.Language.GetString("Clear.Label.RubbishFound") & Me.Language.GetString("Common.Symbol.Colon")      '"发现垃圾 :"
        Me.crlRubbishSelct.StringHeader = Me.Language.GetString("Clear.Label.Selected") & Me.Language.GetString("Common.Symbol.Colon")         '"   已选择 :"

        Me.lblHeaderItemName.Text = Me.Language.GetString("Clear.Label.ItemName")                       '"项目名称"
        Me.lblRowHeaderFileCount.Text = Me.Language.GetString("Clear.Label.FileCount")                  '"文件个数"

        Me.lblRowHeaderFileSize.Text = Me.Language.GetString("App.Column.FileSize")                     '"文件大小"
        Me.lblRowHeaderDetail.Text = Me.Language.GetString("Clear.Label.Detail")                        '"详情"

        Me.lblReportRescan.Text = Me.Language.GetString("Clear.Label.ReScan")                           '"重新扫描"

        Me.btnResult.Text = Me.Language.GetString("Clear.Label.ClearFinish")                             '"清理完成"
        Me.btnResult.tbToolTip = Me.Language.GetString("Clear.Label.ClearSuguest")                    '"建议您经常清理垃圾，保持设备运行顺畅。"

        Me.btnReportFinish.Text = Me.Language.GetString("Media.Message.Finished")                       '"完成"
        Me.lblReportWeibo.Text = Me.Language.GetString("File.Menu.ShareToSina")                         '"分享到新浪微博"
        Me.lblReportQQWeibo.Text = Me.Language.GetString("WeiBo.Button.ShareToTencent")                 '分享到腾讯微博
        Me.ClearRubbishFileFoundControl1.StringHead = Me.Language.GetString("Clear.Message.RubbishFoundResult") '"已发现 {0} 个垃圾文件，共占用 {1} 空间"

        If Folder.LangType = LanguageType.zh_CN Then
            Me.lblReportWeibo.Text = Me.Language.GetString("File.Menu.ShareToSina")                         '"分享到新浪微博"
            Me.lblReportQQWeibo.Text = Me.Language.GetString("WeiBo.Button.ShareToTencent")                 '分享到腾讯微博
        Else
            Me.lblReportWeibo.Text = String.Format(Me.Language.GetString("WeiBo.Label.ShareTo"), "Facebook")
            Me.lblReportQQWeibo.Text = String.Format(Me.Language.GetString("WeiBo.Label.ShareTo"), "Twitter")
        End If

        'Me.lblReportRescan.Location = New Point(Me.lblSuccessedClear.Right + 10, Me.lblReportRescan.Top)
        Me.lblCleanning.Text = Me.Language.GetString("Clear.Message.Clearing")
    End Sub

    '创建字体
    Private Function CreateFontSafe(ByVal fontFamaly As String, ByVal size As Integer, ByVal style As FontStyle) As Font
        Dim fontCreate As Font = Nothing
        Try
            fontCreate = New Font(fontFamaly, Size, style)
        Catch ex As Exception
            fontCreate = Common.CreateFont("宋体", size, style)
        End Try
        Return fontCreate
    End Function

    '初始化浏览器
    Private Sub InitialFirstPage()
        If IniSetting.GetClearRubbishShowFormNew OrElse (TongbuPlist.GetLastClearTime(Me.mDevice) = Nothing) Then
            Dim strPath As String = Me.ReleaseWebPage1(mUserCount)
            mIsFirstPage = True
            Me.webFirstPage.Navigate(strPath)
            IniSetting.SetClearRubbishShowFormNew()
        Else
            mIsFirstPage = False
            '获取上次清理时间
            Dim strSpan As String
            Dim lastClear As DateTime = TongbuPlist.GetLastClearTime(Me.mDevice)
            Dim span As TimeSpan = DateTime.Now - lastClear

            If span.TotalHours < 24 Then
                strSpan = String.Format("{0}{1} ", span.TotalHours.ToString("f1"), Me.Language.GetString("Music.Label.Hour"))
                Me.webFirstPage.Visible = False
                Me.pnlFunction.Visible = True
                Me.pnlReport.Visible = False
                Return
            Else
                strSpan = String.Format("{0} {1} ", CType(Math.Floor(span.TotalDays), Integer).ToString, Me.Language.GetString("Clear.Label.Day"))
            End If
            Dim strPath As String = Me.ReleaseWebPage2(strSpan)
            Me.webFirstPage.Navigate(strPath)
        End If
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)

        MyBase.OnDisconnect(device)
        Me.mDevice = Nothing
        Me.Close()
    End Sub

    Private Sub btnScan_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnScan.Click
        Me.DoScan()
    End Sub

    Private Sub btnStopScan_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnStopScan.Click
        If Me.bgwScanFiles.IsBusy Then
            Me.lblScanning.Text = Me.Language.GetString("Clear.Message.StopCurrentOperate")         '"正在停止当前操作"
            Me.bgwScanFiles.CancelAsync()
            Me.btnStopScan.Enabled = False
        End If
    End Sub

    Private Sub btnStopClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnStopClear.Click
        If Me.bgwClearFiles.IsBusy Then
            Me.lblCleanning.Text = Me.Language.GetString("Clear.Message.StopCurrentOperate")        '"正在停止当前操作"
            Me.bgwClearFiles.CancelAsync()
            Me.btnStopClear.Enabled = False
        End If
    End Sub

    Private Sub btnClear_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnClear.Click
        Me.DoClear()
    End Sub

    Private Sub btnReportFinish_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReportFinish.Click
        'If Me.chkSharedWeibo.Checked Then
        '    ShareWeibo()
        'End If
        Me.Close()
        'Me.pnlReport.Visible = False
        'Me.mScanResult = New RubbishScanResult
        'Me.pnlCleanning.Visible = False
        'Me.pnlDescrib.Visible = True
        'Me.pnlFunction.Visible = True
    End Sub

    Private Sub mTempList_CheckChange() Handles mTempList.CheckChange
        UpdateSelectScanItems()
        UpdateCheckSelectAll()
    End Sub

    Private Sub mCacheList_CheckChange() Handles mCacheList.CheckChange
        UpdateSelectScanItems()
        UpdateCheckSelectAll()
    End Sub

    Private Sub mCookieList_CheckChange() Handles mCookieList.CheckChange
        UpdateSelectScanItems()
        UpdateCheckSelectAll()
    End Sub

    Private Sub mMediaList_CheckChange() Handles mMediaList.CheckChange
        UpdateSelectScanItems()
        UpdateCheckSelectAll()
    End Sub

    Private Sub mCrashLogList_CheckChange() Handles mCrashLogList.CheckChange
        UpdateSelectScanItems()
        UpdateCheckSelectAll()
    End Sub

    Private Sub mPhotosList_CheckChange() Handles mPhotosList.CheckChange
        UpdateSelectScanItems()
        UpdateCheckSelectAll()
    End Sub

    Private Sub mInstallFailedList_CheckChange() Handles mInstallFailedList.CheckChange
        UpdateSelectScanItems()
        UpdateCheckSelectAll()
    End Sub

    Private Sub lblReScan_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblReScan.LinkClicked
        Me.mCacheList.Checked = True
        Me.DoScan()
    End Sub

    Private Sub lblReportWeibo_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblReportWeibo.LinkClicked
        Dim type As ClientType = ClientType.Sina
        If Folder.LangType <> LanguageType.zh_CN Then
            type = ClientType.Facebook
        End If
        Me.ShareWeibo(type)
        'imagePicrure.Dispose()
    End Sub

    Private Sub lblReportQQWeibo_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblReportQQWeibo.LinkClicked
        Dim type As ClientType = ClientType.Tecent
        If Folder.LangType <> LanguageType.zh_CN Then
            type = ClientType.Twitter
        End If
        Me.ShareWeibo(type)
    End Sub

    Private Sub SetResultImage(ByVal sizeClear As Long)
        Dim stringSizeClear As String = Utility.FormatFileSize(sizeClear)
        'stringSizeClear = "999.00 Bytes"
        mTotalClearSize = sizeClear
        Dim imagePicrure As Image = My.Resources.weibo_sina_.Clone
        Dim gPicture As Graphics = Nothing
        'Dim locationString As Point
        'Dim sizeString As Size
        Dim formatString As StringFormat
        Dim fontOfString As Font = Nothing
        Dim deviceType As String = ""
        'locationString = New Point(32, 0)
        'sizeString = New Size(128, 31)
        formatString = New StringFormat()
        formatString.Alignment = StringAlignment.Center
        fontOfString = CreateFontSafe("微软雅黑", 17, FontStyle.Regular)

        gPicture = Graphics.FromImage(imagePicrure)
        gPicture.SmoothingMode = Drawing2D.SmoothingMode.HighQuality
        gPicture.TextRenderingHint = Drawing.Text.TextRenderingHint.AntiAlias

        Dim recFirstLise As New Rectangle(140, 56, 185, 42)
        '"哇咔咔，同步助手"  カチャチャ、Tongbu Assistant
        gPicture.DrawString(Me.Language.GetString("Clear.Label.ClearFinshDesc1"), fontOfString, New SolidBrush(Color.Black), recFirstLise, formatString)

        '"清理了{0}垃圾"
        Dim strValues As String() = String.Format(Me.Language.GetString("Clear.Label.ClearFinshDesc2"), "#FileSize#").Split("#")
        Dim intLocationX As Integer = 145
        Dim diff As Integer = 8
        For Each Item As String In strValues
            If Item = "FileSize" Then
                Dim size As SizeF = gPicture.MeasureString(stringSizeClear.Trim, fontOfString)
                Dim recTemp As New Rectangle(intLocationX, 110, size.Width + diff, 35)
                intLocationX += size.Width
                gPicture.DrawString(stringSizeClear.Trim, fontOfString, New SolidBrush(Color.FromArgb(214, 59, 12)), recTemp, formatString)

            Else
                Dim size As SizeF = gPicture.MeasureString(Item, fontOfString)
                Dim recTemp As New Rectangle(intLocationX, 110, size.Width + diff, 35)
                intLocationX += size.Width
                gPicture.DrawString(Item, fontOfString, New SolidBrush(Color.Black), recTemp, formatString)

            End If
        Next

        'gPicture.TranslateTransform(158, 110)
        'gPicture.DrawString(stringSizeClear.Trim, fontOfString, New SolidBrush(Color.FromArgb(214, 59, 12)), New Rectangle(locationString, sizeString), formatString)
        'gPicture.ResetTransform()

        If Me.mDevice.ProductType.StartsWith("iPhone") Then
            deviceType = "iPhone"
        ElseIf Me.mDevice.ProductType.StartsWith("iPad") Then
            deviceType = "iPad"
        ElseIf Me.mDevice.ProductType.StartsWith("iPod") Then
            deviceType = "iTouch"
        End If
        Me.picReportResult.Image = imagePicrure
        gPicture.Dispose()
    End Sub

    Private Sub chkSelectAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkSelectAll.CheckedChanged
        If Me.chkSelectAll.CheckState = CheckState.Checked Then
            mIsCheckTriggle = True
            Me.mTempList.Checked = True
            Me.mCacheList.Checked = True
            Me.mCookieList.Checked = True
            Me.mMediaList.Checked = True
            Me.mCrashLogList.Checked = True
            Me.mPhotosList.Checked = True
            Me.mInstallFailedList.Checked = True
            mIsCheckTriggle = False
        ElseIf Me.chkSelectAll.CheckState = CheckState.Unchecked Then
            mIsCheckTriggle = True
            Me.mTempList.Checked = False
            Me.mCacheList.Checked = False
            Me.mCookieList.Checked = False
            Me.mMediaList.Checked = False
            Me.mCrashLogList.Checked = False
            Me.mPhotosList.Checked = False
            Me.mInstallFailedList.Checked = False
            mIsCheckTriggle = False
        End If
    End Sub

    Private Sub lblReportRescan_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles lblReportRescan.Click
        Me.pnlReport.Visible = False
        Me.pnlFunction.Visible = True

        Me.prgScanProgress.tbPlayValue = 0

        '显示正在扫描页面
        Me.pnlDescrib.Visible = False
        Me.pnlCleanning.Visible = False
        Me.pnlScanFinish.Visible = False
        Me.pnlScanning.Visible = True
        Me.btnStopScan.Enabled = True
        Me.pnlListData.Enabled = False
        Me.chkSelectAll.Enabled = False

        Dim settingClearRubbish As New ClearRubbishArgs
        bgwScanFiles = New BackgroundWorker()
        bgwScanFiles.WorkerReportsProgress = True
        bgwScanFiles.WorkerSupportsCancellation = True

        RemoveHandler bgwScanFiles.DoWork, AddressOf bgwScanFiles_DoWork
        AddHandler bgwScanFiles.DoWork, AddressOf bgwScanFiles_DoWork
        RemoveHandler bgwScanFiles.ProgressChanged, AddressOf bgwScanFiles_ProgressChanged
        AddHandler bgwScanFiles.ProgressChanged, AddressOf bgwScanFiles_ProgressChanged
        RemoveHandler bgwScanFiles.RunWorkerCompleted, AddressOf bgwScanFiles_RunWorkerCompleted
        AddHandler bgwScanFiles.RunWorkerCompleted, AddressOf bgwScanFiles_RunWorkerCompleted
        bgwScanFiles.RunWorkerAsync(settingClearRubbish)
    End Sub

    Private Sub pnlListData_ClientSizeChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles pnlListData.ClientSizeChanged
        pnlListData.WrapContents = False
    End Sub

    Private Sub pnlListData_Resize(ByVal sender As Object, ByVal e As System.EventArgs) Handles pnlListData.Resize
        Me.pnlListData.FlowDirection = FlowDirection.LeftToRight
        Me.pnlListData.FlowDirection = FlowDirection.TopDown
    End Sub

    Private Sub pnlListData_SizeChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles pnlListData.SizeChanged
        If Me.mPreDataListWidth <> Me.pnlListData.Width Then
            pnlListData.WrapContents = False

            Me.mTempList.Width = Me.pnlListData.Width - 25
            Me.mCookieList.Width = Me.pnlListData.Width - 25
            Me.mCacheList.Width = Me.pnlListData.Width - 25
            Me.mMediaList.Width = Me.pnlListData.Width - 25
            Me.mCrashLogList.Width = Me.pnlListData.Width - 25
            Me.mPhotosList.Width = Me.pnlListData.Width - 25
            Me.mInstallFailedList.Width = Me.pnlListData.Width - 25

            Dim val As Integer = Me.pnlListData.HorizontalScroll.Value
            mPreDataListWidth = Me.pnlListData.Width
        End If
    End Sub

#End Region

#Region "--- 加载配置 ---"

    Private Sub DownloadIgnorePlist()
        If Folder.LangType <> LanguageType.zh_CN Then
            Return
        End If

        Dim thd As New Threading.Thread(AddressOf DownloadIgnorePlistThread)
        thd.IsBackground = True

        thd.Start()
    End Sub

    '下载ignoreplist
    Private Sub DownloadIgnorePlistThread(ByVal state As Object)
        Try
            If mIsIgnoreListLoad Then
                '加载忽略列表
                Me.ReadDownloadPlist()
                mLoadPlist = True

                Return
            End If

            Dim strPlistTemp As String = Me.mIgnorePlistPath + ".temp"
            '
            'http://server.tongbu.com/tbCloud/TbzsData/RubbishIgnore.aspx
            '"http://server.tongbu.com/tbCloud/TbzsData/RubbishApi.aspx"
            If Common.HttpDownload(WebUrl.ChinaRubbishUnClearApp, strPlistTemp, 5000) Then
                '删除掉原来的plist文件
                If File.Exists(Me.mIgnorePlistPath) Then
                    File.Delete(Me.mIgnorePlistPath)
                End If


                '删除掉原来的json文件
                Dim strOldPlist As String = Me.mIgnorePlistPath.Replace(".db", ".json")
                If File.Exists(strOldPlist) Then
                    File.Delete(strOldPlist)
                End If

                '将临时文件重命名成plist文件
                File.Move(strPlistTemp, Me.mIgnorePlistPath)
                mIsIgnoreListLoad = True

                '加载忽略列表
                Me.ReadDownloadPlist()
                mLoadPlist = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadIgnorePlistThread")
        End Try
    End Sub

    Private Sub LoadIgnoreSoftwareType()
        Me.mDictSoftwareType.Clear()
        mDictSoftwareType.Add("newsstand", "清理后可能丢失下载的报刊杂志资料")
    End Sub

    '读取忽略列表
    Private Sub ReadDownloadPlist()
        If mLoadPlist OrElse Folder.LangType <> LanguageType.zh_CN Then
            Return
        End If

        'If Not File.Exists(Me.mIgnorePlistPath) AndAlso Not File.Exists(Me.mIgnorePlistPath.Replace(".db", ".json")) Then
        '    InitialIgnoreList()
        'End If

        Try
            Dim ignoreString As String = String.Empty

            If File.Exists(Me.mIgnorePlistPath) Then
                Using reader As New StreamReader(Me.mIgnorePlistPath)
                    ignoreString = reader.ReadToEnd()
                End Using
                ignoreString = Common.DecryptDES(ignoreString, "ac68!3#1", "ac68!3#1")

            ElseIf File.Exists(Me.mIgnorePlistPath.Replace(".db", ".json")) Then
                Using reader As New StreamReader(Me.mIgnorePlistPath)
                    ignoreString = reader.ReadToEnd()
                End Using

            End If

            Dim listIgnore As JsonObject = iTong.CoreFoundation.JsonParser.ParseString(ignoreString)
            Dim items As JsonArray = listIgnore("table")
            For Each item As iTong.CoreFoundation.JsonObject In items
                If item.ContainsKey("Sku") And item.ContainsKey("Version") And item.ContainsKey("Context") Then
                    Dim ignoreInfo As New IgnoreInfo
                    ignoreInfo.Reason = CType(item("Context"), JsonString).Value
                    ignoreInfo.Sku = CType(item("Sku"), JsonString).Value
                    ignoreInfo.Version = CType(item("Version"), JsonString).Value
                    Me.mDictIgnoreInfo(CType(item("Sku"), JsonString).Value) = ignoreInfo
                End If
            Next

            If listIgnore.ContainsKey("catagorytable") Then
                Dim catagoryItems As JsonArray = listIgnore("catagorytable")
                For Each item As iTong.CoreFoundation.JsonObject In catagoryItems
                    If item.ContainsKey("CatagoryID") And item.ContainsKey("Context") Then
                        Me.mDictIgnoreCatagory(CType(item("CatagoryID"), JsonString).Value) = CType(item("Context"), JsonString).Value
                    End If
                Next
            End If
        Catch ex As Exception
            '读取失败，清空忽略列表
            mDictIgnoreInfo.Clear()
        End Try
    End Sub

    '创建初始忽略列表
    Private Sub InitialIgnoreList()
        Dim listPlist As New List(Of Object)
        Dim dictQQPlayerNode As New Dictionary(Of Object, Object)
        dictQQPlayerNode("SKU") = "com.tencent.QQMusic"
        dictQQPlayerNode("Version") = "3.1.2"
        dictQQPlayerNode("Context") = Me.Language.GetString("Clear.Message.LostMusicAfterClear")            '"删除后可能丢失缓存的音乐"
        listPlist.Add(dictQQPlayerNode)
        Dim fileJson As New FileStream(Me.mIgnorePlistPath, FileMode.OpenOrCreate, FileAccess.Write)
        Dim writer As New StreamWriter(fileJson)
        '删除后可能丢失缓存的音乐
        writer.Write(String.Format("{""table"":[{""Sku"":""com.tencent.QQMusic"",""Context"":""{0}"",""Version"":""3.1.2""}]}", Me.Language.GetString("Clear.Message.LostMusicAfterClear")))
        writer.Flush()
        fileJson.Close()
    End Sub

#End Region

#Region "--- 更新UI ---"

    Private Sub UpdateSelectScanItems()

        Dim allRubbishSize As Long = 0
        Me.mSelectedRubbishCount = 0
        If Me.mScanResult Is Nothing Then
            Return
        End If
        For Each item As RubbishInfo In Me.mScanResult.DictTempDetail.Values
            If item.IsChecked Then
                allRubbishSize += item.FileSize
                mSelectedRubbishCount += item.FileCount
            End If
        Next
        For Each item As RubbishInfo In Me.mScanResult.DictCacheDetail.Values
            If item.IsChecked Then
                allRubbishSize += item.FileSize
                mSelectedRubbishCount += item.FileCount
            End If
        Next
        For Each item As RubbishInfo In Me.mScanResult.DictCookieDetail.Values
            If item.IsChecked Then
                allRubbishSize += item.FileSize
                mSelectedRubbishCount += item.FileCount
            End If
        Next

        For Each item As Long In Me.mScanResult.DictMediaDetail.Values
            If Me.mMediaList.Checked Then
                allRubbishSize += item
            End If
        Next
        mSelectedRubbishCount += Me.mScanResult.DictMediaDetail.Count

        For Each item As Long In Me.mScanResult.DictCrashLogDetail.Values
            If Me.mCrashLogList.Checked Then
                allRubbishSize += item
            End If
        Next
        mSelectedRubbishCount += Me.mScanResult.DictCrashLogDetail.Count

        For Each item As Long In Me.mScanResult.DictPhotosDetail.Values
            If Me.mPhotosList.Checked Then
                allRubbishSize += item
            End If
        Next
        mSelectedRubbishCount += Me.mScanResult.DictPhotosDetail.Count


        If Me.mInstallFailedList.Checked Then
            allRubbishSize += Me.mScanResult.AllInstallFailRubbish.FileSize
            mSelectedRubbishCount += Me.mScanResult.AllInstallFailRubbish.FileCount
        End If
        Me.mSelectedRubbishSize = allRubbishSize

        Me.prgCleanProgress.tbPlayMax = mSelectedRubbishCount
        ' Me.lblScanFinishRubbishSize.Text = Utility.FormatFileSize(allRubbishSize)
        Me.crlRubbishSelct.FileSize = allRubbishSize
    End Sub

    '更新chkSelectAll的选中状态
    Private Sub UpdateCheckSelectAll()
        If Not Me.mIsCheckTriggle Then
            If Me.mTempList.Checked = True And _
                      Me.mCacheList.Checked = True And _
                   Me.mCookieList.Checked = True And _
                  Me.mMediaList.Checked = True And _
                  Me.mCrashLogList.Checked = True And _
                  Me.mPhotosList.Checked = True And _
                  Me.mInstallFailedList.Checked = True Then
                Me.chkSelectAll.CheckState = CheckState.Checked
            ElseIf Me.mTempList.Checked = False And _
                    Me.mCacheList.Checked = False And _
                    Me.mCookieList.Checked = False And _
                    Me.mMediaList.Checked = False And _
                    Me.mCrashLogList.Checked = False And _
                    Me.mPhotosList.Checked = False And _
                    Me.mInstallFailedList.Checked = False Then
                Me.chkSelectAll.CheckState = CheckState.Unchecked
            Else
                Me.chkSelectAll.CheckState = CheckState.Indeterminate
            End If
        End If
    End Sub

    '删除回调
    Private Delegate Sub DeleteFileHandler(ByVal args As FileDeleteEventArgs)
    Private Sub DeleteFile_CallBack(ByVal args As FileDeleteEventArgs)
        If Me.IsDisposed Then
            Return
        End If

        If String.IsNullOrEmpty(Me.mClearingAppName) Then
            Return
        End If

        If args.IsDirectory Then
            Return
        End If

        If Me.bgwClearFiles.CancellationPending Then
            args.Cancel = True
            Return
        End If

        If Me.InvokeRequired Then
            Me.Invoke(New DeleteFileHandler(AddressOf DeleteFile_CallBack), New Object() {args})
        Else

            Dim realTotalCount As Integer = mTotalClearCount
            Dim clearItemName As String = ""

            If Me.mClearingAppName = Me.Language.GetString("Clear.Message.SyncFailureMusic") Then
                clearItemName = mClearingAppName
                realTotalCount = Me.mScanResult.AllMediaRubbishs.FileCount

            ElseIf Me.mClearingAppName = Me.Language.GetString("Clear.Label.InstallFailureRubbish") Then
                clearItemName = mClearingAppName
                realTotalCount = Me.mScanResult.AllInstallFailRubbish.FileCount

            Else
                If Me.mDictDirtyApp(Me.mClearingAppName).TotalFileCount < Me.mTotalClearCount Then
                    realTotalCount = Me.mTotalClearCount
                Else
                    realTotalCount = Me.mDictDirtyApp(Me.mClearingAppName).TotalFileCount
                End If
                clearItemName = Me.mDictDirtyApp(Me.mClearingAppName).Name
            End If

            Me.mTotalClearCount += 1
            If Me.mTotalClearCount > realTotalCount Then
                realTotalCount = Me.mTotalClearCount
            End If

            Me.lblCleanning.Text = String.Format("{3}{4}{0}({1}/{2})", clearItemName, Me.mTotalClearCount, realTotalCount, Me.Language.GetString("Clear.Message.Clearing"), Me.Language.GetString("Common.Symbol.Colon"))
            Me.prgCleanProgress.tbPlayValue = Me.prgCleanProgress.tbPlayValue + 1
        End If
    End Sub

    Private Delegate Sub ClearFileHandler(ByVal args As String)
    Private Sub ClearFile_CallBack(ByVal args As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ClearFileHandler(AddressOf ClearFile_CallBack), New Object() {args})
        Else
            Me.lblCleanning.Text = String.Format("{1}{2}{0}", args, Me.Language.GetString("Clear.Message.Clearing"), Me.Language.GetString("Common.Symbol.Colon"))
        End If
    End Sub

    Private Delegate Sub ScanFileHandler(ByVal args As String)
    Private Sub ScanFile_CallBack(ByVal args As String)
        If Not Me.IsHandleCreated Or Me.IsDisposed Then
            Return
        End If

        If Me.InvokeRequired Then
            Me.Invoke(New ScanFileHandler(AddressOf ScanFile_CallBack), New Object() {args})
        Else
            Me.lblScanning.Text = String.Format("{1}{2}{0}", args, Me.Language.GetString("Clear.Message.Scaning"), Me.Language.GetString("Common.Symbol.Colon"))

            Me.ClearRubbishFileFoundControl1.FileCount = Me.mTotalRubbishCount
            Me.ClearRubbishFileFoundControl1.FileSize = Me.mTotalRubbishSize
        End If
    End Sub

#End Region

#Region "--- 分享微薄 ---"

    Private Sub ShareWeibo(ByVal type As ClientType)
        Dim fontOfString As Font = Nothing
        Dim stringSizeClear As String = Utility.FormatFileSize(Me.mTotalClearSize)
        Dim deviceType As String = ""

        If Me.mDevice.ProductType.StartsWith("iPhone") Then
            deviceType = "iPhone"
        ElseIf Me.mDevice.ProductType.StartsWith("iPad") Then
            deviceType = "iPad"
        ElseIf Me.mDevice.ProductType.StartsWith("iPod") Then
            deviceType = "iTouch"
        End If
        '"我刚刚用#同步助手#清理了{0}的垃圾，省了 {1} 的空间，真是太好用了！同步助手下载：http://www.tongbu.com/zhushou
        Dim strUrl As String = String.Empty
        Dim strShow As String = Me.Language.GetString("Clear.Message.ShowClearResult")

        strUrl = WebUrl.PageAssitantHome
        'Select Case Folder.LangType
        '    Case LanguageType.zh_CN
        '        strUrl = "http://www.tongbu.com/zhushou"
        '    Case LanguageType.en_US
        '        strUrl = "www.iClover.com"
        '    Case LanguageType.vi_VN
        '        strUrl = "http://www.dongbo.vn"
        '    Case LanguageType.th_TH
        '        strUrl = "http://www.appleAA.com"
        'End Select

        If Utility.IsPanda Then
            strUrl = "http://www.yuyue111.com"
        End If

        Dim strMsg As String = String.Format(strShow, deviceType, stringSizeClear, strUrl)

        'If type = ClientType.Twitter Then
        '    'Twitter不支持连接分享
        '    strMsg = strMsg.Replace("Download iClover here: www.iClover.com", "")
        'End If

        WeiboHelper.SendMessage(type, Me, strMsg, picReportResult.Image, False)
    End Sub

#End Region

    Public Function IsDirectory(ByVal info As FileSharingPackageInfo, ByVal strPath As String, ByRef e As iPhoneFileInfo) As Boolean
        If info Is Nothing Then
            Return Me.mDevice.IsDirectory(Me.mDevice.MobileAfcHandle, strPath, e)

        ElseIf Me.mDevice.Jailbreaked AndAlso Me.mDevice.VersionNumber >= 800 Then
            Return Me.mDevice.IsDirectory(Me.mDevice.DefaultAfcHandle, info.ContainerPath & strPath, e)

        ElseIf Me.mDevice.CheckUIFileSharingEnabled(info.Identifier) Then
            Return Me.mDevice.IsDirectory(Me.mDevice.GetAfcByFileSharing(info.Identifier), strPath, e)

        End If
    End Function

    Public Function GetFiles(ByVal info As FileSharingPackageInfo, ByVal strPath As String, ByVal includeDirectory As Boolean) As String()
        If info Is Nothing Then
            Return Me.mDevice.GetFiles(Me.mDevice.MobileAfcHandle, strPath, includeDirectory)

        ElseIf Me.mDevice.Jailbreaked AndAlso Me.mDevice.VersionNumber >= 800 Then
            Return Me.mDevice.GetFiles(Me.mDevice.DefaultAfcHandle, info.ContainerPath & strPath, includeDirectory)

        ElseIf Me.mDevice.CheckUIFileSharingEnabled(info.Identifier) Then
            Return Me.mDevice.GetFiles(Me.mDevice.GetAfcByFileSharing(info.Identifier), strPath, includeDirectory)

        End If
    End Function

    Public Sub DeleteDirectory(ByVal info As FileSharingPackageInfo, ByVal strPath As String, ByVal includeDirectory As Boolean, ByVal callback As FileDeleteHandler)
        If info Is Nothing Then
            Me.mDevice.DeleteDirectory(Me.mDevice.MobileAfcHandle, strPath, includeDirectory, callback)

        ElseIf Me.mDevice.Jailbreaked AndAlso Me.mDevice.VersionNumber >= 800 Then
            Me.mDevice.DeleteDirectory(Me.mDevice.DefaultAfcHandle, info.ContainerPath & strPath, includeDirectory, callback)

        ElseIf Me.mDevice.CheckUIFileSharingEnabled(info.Identifier) Then
            Me.mDevice.DeleteDirectory(Me.mDevice.GetAfcByFileSharing(info.Identifier), strPath, includeDirectory, callback)

        End If
    End Sub

    Public Sub DeleteFile(ByVal info As FileSharingPackageInfo, ByVal strPath As String)
        If info Is Nothing Then
            Me.mDevice.DeleteFile(Me.mDevice.MobileAfcHandle, strPath)

        ElseIf Me.mDevice.Jailbreaked AndAlso Me.mDevice.VersionNumber >= 800 Then
            Me.mDevice.DeleteFile(Me.mDevice.DefaultAfcHandle, info.ContainerPath & strPath)

        ElseIf Me.mDevice.CheckUIFileSharingEnabled(info.Identifier) Then
            Me.mDevice.DeleteFile(Me.mDevice.GetAfcByFileSharing(info.Identifier), strPath)

        End If
    End Sub

    Private Sub CheckIsIgnore(ByVal info As FileSharingPackageInfo, ByRef bIsChecked As Boolean, ByRef strIgnoreReasion As String)
        Dim strSKU As String = info.Identifier

        If Utility.CheckNeedDecodeSku(strSKU) Then
            strSKU = Utility.DecodeSku(strSKU)
        End If

        '在例外列表中，跳过
        If Me.mDictIgnoreInfo.ContainsKey(strSKU) Then
            Dim ignoreInfo As IgnoreInfo = mDictIgnoreInfo(strSKU)
            Dim verDiff As Integer = Common.CompareVer(info.Version.ToString, ignoreInfo.Version)
            If verDiff < 0 Then
                bIsChecked = False
                strIgnoreReasion = ignoreInfo.Reason
            End If
        End If

        If String.IsNullOrEmpty(info.Genre) Then
            Dim pkgInfo As PackageInfo = SoftManageDB.GetInstance.Get_SKU_ITEM(strSKU)
            If pkgInfo IsNot Nothing AndAlso pkgInfo.Genre.Length > 0 Then
                info.Genre = pkgInfo.Genre
                info.GenreId = pkgInfo.GenreId
            Else
                Me.mDevice.UpdateFileSharingPackageInfo(info, True)
                If Not String.IsNullOrEmpty(info.GenreId) Then
                    info.Genre = SoftManageDB.GetInstance.Get_GenreText_by_GenreId(info.GenreId, Me.Language.CurrentLanguage)
                End If

                If info.Genre.Length > 0 Then
                    SoftManageDB.GetInstance.Insert_SKU_ITEM(strSKU, info.ItemId, info.GenreId, info.Genre, info.SoftwareType)
                End If
            End If
        End If

        '取不到类别，或者是游戏，跳过
        If String.IsNullOrEmpty(info.Genre) OrElse String.IsNullOrEmpty(info.GenreId) Then
            bIsChecked = False
        End If

        '在例外的类别中
        If Me.mDictIgnoreCatagory.ContainsKey(info.GenreId) Then
            bIsChecked = False
            strIgnoreReasion = mDictIgnoreCatagory(info.GenreId)
        End If

        '在列外的软件类别中
        If Me.mDictSoftwareType.ContainsKey(info.SoftwareType) Then
            bIsChecked = False
            strIgnoreReasion = mDictSoftwareType(info.SoftwareType)
        End If

    End Sub

    Private Sub UpdateGenreFromServer(ByRef dict As Dictionary(Of String, FileSharingPackageInfo))
        If dict Is Nothing OrElse dict.Count <= 0 Then
            Return
        End If

        '先从本地数据库取值
        Dim lst As New List(Of String)
        For Each Item As PackageInfo In dict.Values
            If String.IsNullOrEmpty(Item.Genre) OrElse _
               String.IsNullOrEmpty(Item.GenreId) OrElse _
               Item.ItemId.Length = 0 OrElse _
               Val(Item.ItemId) = 0 OrElse _
               String.IsNullOrEmpty(Item.SoftwareType) Then

                '从本地资料库中取得分类信息
                Dim pkgInfo As PackageInfo = SoftManageDB.GetInstance.Get_SKU_ITEM(Item.Identifier)
                If pkgInfo IsNot Nothing AndAlso pkgInfo.ItemId.Length > 0 AndAlso Val(pkgInfo.ItemId) <> 0 Then
                    Item.ItemId = pkgInfo.ItemId
                    Item.GenreId = pkgInfo.GenreId
                    Item.Genre = pkgInfo.Genre
                    Item.SoftwareType = pkgInfo.SoftwareType
                Else
                    If Item.Identifier.Length > 0 Then
                        lst.Add(Item.Identifier)
                    End If
                End If

            End If
        Next

        '本地数据没有信息，需从服务器获取
        If lst.Count > 0 Then
            Dim dictServer As Dictionary(Of String, FileSharingPackageInfo) = HtmlHelper.GetAppInfoFromServer(lst, Me.mDevice.Identifier, Me.mDevice.Jailbreaked)
            Dim listDB As New List(Of PackageInfo)

            For Each Item As String In dictServer.Keys
                If dict.ContainsKey(Item) Then
                    Dim info As FileSharingPackageInfo = dict(Item)
                    Dim infoServer As FileSharingPackageInfo = dictServer(Item)

                    info.ItemId = infoServer.ItemId
                    info.GenreId = infoServer.GenreId
                    info.Genre = infoServer.Genre
                    info.SoftwareType = infoServer.SoftwareType

                    listDB.Add(info)
                End If
            Next

            If listDB.Count > 0 Then
                SoftManageDB.GetInstance.Insert_SKU_ITEM(listDB)
            End If
        End If
    End Sub

    'Private Sub UpdateGenreFromServer(ByRef dict As Dictionary(Of String, FileSharingPackageInfo))
    '    '越狱，6.0以下的不用从服务器取
    '    If dict Is Nothing OrElse dict.Count <= 0 Then
    '        Return
    '    End If

    '    Dim listPost As New List(Of String)
    '    For Each Item As FileSharingPackageInfo In dict.Values
    '        listPost.Add(Item.Identifier)
    '    Next

    '    Dim dictServer As Dictionary(Of String, FileSharingPackageInfo) = HtmlHelper.GetAppInfoFromServer(listPost, Me.mDevice.Identifier, Me.mDevice.Jailbreaked)

    '    For Each Item As String In dictServer.Keys
    '        If dict.ContainsKey(Item) Then
    '            Dim info As FileSharingPackageInfo = dict(Item)
    '            Dim infoServer As FileSharingPackageInfo = dictServer(Item)

    '            info.ItemId = infoServer.ItemId
    '            info.GenreId = infoServer.GenreId
    '            info.Genre = infoServer.Genre
    '        End If
    '    Next

    '    Dim listDB As New List(Of PackageInfo)
    '    For Each Item As FileSharingPackageInfo In dict.Values
    '        If Not String.IsNullOrEmpty(Item.GenreId) Then
    '            listDB.Add(Item)
    '        End If
    '    Next
    '    SoftManageDB.GetInstance.Insert_SKU_ITEM(listDB)
    'End Sub

    '获取整个文件夹的大小
    Private Sub GetDirectorySizeInfo(ByVal info As FileSharingPackageInfo, ByVal dictName As String, ByRef fileCount As Long, ByRef fileSize As Long)

        If Me.bgwScanFiles.CancellationPending Then
            GoTo LABEL_EXIT
        End If

        Try
            Dim sizeDirectory As Long = 0
            Dim aryFiles As String() = Me.GetFiles(info, dictName, True)
            If aryFiles Is Nothing Then
                GoTo LABEL_EXIT
            End If

            For Each fileName As String In aryFiles
                If Me.bgwScanFiles.CancellationPending Then
                    GoTo LABEL_EXIT
                End If

                Dim fileInfo As iPhoneFileInfo = Nothing
                Dim fullPath As String = dictName + "/" + fileName
                If Me.IsDirectory(info, fullPath, fileInfo) Then
                    GetDirectorySizeInfo(info, fullPath, fileCount, fileSize)
                Else
                    'If fileInfo.st_size <> 0 Then

                    fileSize += fileInfo.st_size

                    Me.mTotalRubbishSize += fileInfo.st_size
                    Me.mTotalRubbishCount += 1
                    fileCount += 1
                    'End If
                End If

                ScanFile_CallBack(Me.mClearingAppName)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

LABEL_EXIT:
    End Sub

    Private Sub GetDirectorySizeInfoEx(ByVal info As FileSharingPackageInfo, ByVal dictName As String, ByRef fileCount As Long, ByRef fileSize As Long)

        If Me.bgwScanFiles.CancellationPending Then
            GoTo LABEL_EXIT
        End If

        Try
            Dim sizeDirectory As Long = 0
            For Each strName As String In Me.GetFiles(info, dictName, True)
                If Me.bgwScanFiles.CancellationPending Then
                    GoTo LABEL_EXIT
                End If

                If strName.StartsWith("downloads", StringComparison.OrdinalIgnoreCase) AndAlso _
                    (strName.EndsWith(".sqlitedb", StringComparison.OrdinalIgnoreCase) OrElse strName.EndsWith(".db", StringComparison.OrdinalIgnoreCase)) Then
                    Continue For
                End If

                Dim fileInfo As iPhoneFileInfo = Nothing
                Dim fullPath As String = dictName + "/" + strName
                If Me.IsDirectory(info, fullPath, fileInfo) Then
                    GetDirectorySizeInfo(info, fullPath, fileCount, fileSize)
                Else
                    'If fileInfo.st_size <> 0 Then

                    fileSize += fileInfo.st_size

                    Me.mTotalRubbishSize += fileInfo.st_size
                    Me.mTotalRubbishCount += 1
                    fileCount += 1
                    'End If
                End If

                ScanFile_CallBack(Me.mClearingAppName)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

LABEL_EXIT:
    End Sub

    '加载图标
    Private Function LoadAppIcon(ByVal sku As String) As Image
        Dim result As Image = Nothing
        Dim pathIcon As String = System.IO.Path.Combine(Me.mIconCacheDir, sku) + ".png"
        If File.Exists(pathIcon) Then
            Try
                result = Image.FromFile(pathIcon)
            Catch ex As Exception
                result = Nothing
            End Try
        Else
            Try
                result = mDevice.GetIconPNGData(sku)
                If result IsNot Nothing Then
                    result.Save(pathIcon)
                End If
            Catch ex As Exception
            End Try
           
        End If
        Return result
    End Function

    Private Sub ScanAppRubbish()
        If Not Me.mTempList.Checked AndAlso Not Me.mCacheList.Checked AndAlso Not Me.mCookieList.Checked Then
            Return
        End If

        Dim arrTmpPath As String() = {"/tmp", "/Library/tmp", "/Library/Tmp", "/Library/temp", "/Library/Temp"}
        Dim pathCache As String = "/Library/Caches"
        Dim pathCookie As String = "/Library/Cookies"
        Dim scanedApplications As Long = 0

        '加载配置
        Me.ReadDownloadPlist()

        '加载忽略的软件类别 software-type
        Me.LoadIgnoreSoftwareType()

        '获取全部安装的软件
        Dim dict As Dictionary(Of String, FileSharingPackageInfo) = mDevice.InstalledApplications(ApplicationType.User)

        '从服务器更新软件类别信息
        Me.UpdateGenreFromServer(dict)

        For Each pair As KeyValuePair(Of String, FileSharingPackageInfo) In dict
            Dim bIsChecked As Boolean = True
            Dim strIgnoreReasion As String = ""

            '操作被取消，退出
            If Me.bgwScanFiles.CancellationPending Then
                Return
            End If

            'If pair.Value.Name.Contains("GQ24") Then
            '    Stop
            'End If

            'If pair.Value.GenreId.Contains("6012") Then
            '    Stop
            'End If

            'If pair.Value.SoftwareType.Contains("newsstand") Then
            '    Stop
            'End If

            If Folder.LangType = LanguageType.zh_CN Then
                '检查例外列表
                Me.CheckIsIgnore(pair.Value, bIsChecked, strIgnoreReasion)
            Else
                bIsChecked = False
            End If

            Me.ScanFile_CallBack(pair.Value.Name)

            Me.mClearingAppName = pair.Value.Name

            ' 计算temp 文件
            If Me.mTempList.Checked Then
                For Each pathTemp As String In arrTmpPath

                    Dim sizeDirTemp As Long = 0
                    Dim countDirTemp As Long = 0
                    Me.GetDirectorySizeInfo(pair.Value, pathTemp, countDirTemp, sizeDirTemp)

                    If sizeDirTemp <> 0 Then
                        mScanResult.AllTempRubbishs.FileSize += sizeDirTemp
                        mScanResult.AllTempRubbishs.FileCount += countDirTemp

                        Dim itemResult As RubbishInfo = Nothing
                        If mScanResult.DictTempDetail.ContainsKey(pair.Key) Then
                            itemResult = mScanResult.DictTempDetail(pair.Key)
                        Else
                            itemResult = New RubbishInfo()

                            itemResult.Icon = Me.LoadAppIcon(pair.Key)
                            itemResult.AppName = pair.Value.Name
                            itemResult.IsChecked = bIsChecked
                            itemResult.IsSkipped = Not bIsChecked
                            itemResult.IgnoreReason = strIgnoreReasion

                            mScanResult.DictTempDetail(pair.Key) = itemResult
                        End If
                        itemResult.FileSize += sizeDirTemp
                        itemResult.FileCount += countDirTemp

                    End If
                Next
            End If



            '计算cache
            If Me.mCacheList.Checked Then
                Dim sizeDirCaches As Long = 0
                Dim countDirCaches As Long = 0
                Me.GetDirectorySizeInfo(pair.Value, pathCache, countDirCaches, sizeDirCaches)

                If sizeDirCaches <> 0 Then
                    mScanResult.AllCacheRubbishs.FileSize += sizeDirCaches
                    mScanResult.AllCacheRubbishs.FileCount += countDirCaches

                    Dim itemResult As RubbishInfo = Nothing
                    If mScanResult.DictCacheDetail.ContainsKey(pair.Key) Then
                        itemResult = mScanResult.DictCacheDetail(pair.Key)
                    Else
                        itemResult = New RubbishInfo()

                        itemResult.Icon = Me.LoadAppIcon(pair.Key)
                        itemResult.AppName = pair.Value.Name
                        itemResult.IsChecked = bIsChecked
                        itemResult.IsSkipped = Not bIsChecked
                        itemResult.IgnoreReason = strIgnoreReasion

                        mScanResult.DictCacheDetail(pair.Key) = itemResult
                    End If
                    itemResult.FileSize += sizeDirCaches
                    itemResult.FileCount += countDirCaches

                End If
            End If



            '计算cookie
            If Me.mCookieList.Checked Then
                Dim sizeCookies As Long = 0
                Dim countCookies As Long = 0
                Me.GetDirectorySizeInfo(pair.Value, pathCookie, countCookies, sizeCookies)

                If sizeCookies <> 0 Then
                    mScanResult.AllCookieRubbishs.FileSize += sizeCookies
                    mScanResult.AllCookieRubbishs.FileCount += countCookies

                    Dim itemResult As RubbishInfo = Nothing
                    If mScanResult.DictCookieDetail.ContainsKey(pair.Key) Then
                        itemResult = mScanResult.DictCookieDetail(pair.Key)
                    Else
                        itemResult = New RubbishInfo()

                        itemResult.Icon = Me.LoadAppIcon(pair.Key)
                        itemResult.AppName = pair.Value.Name
                        itemResult.IsChecked = bIsChecked
                        itemResult.IsSkipped = Not bIsChecked
                        itemResult.IgnoreReason = strIgnoreReasion

                        mScanResult.DictCookieDetail(pair.Key) = itemResult
                    End If
                    itemResult.FileSize += sizeCookies
                    itemResult.FileCount += countCookies

                    Me.mTotalRubbishSize += sizeCookies
                    Me.mTotalRubbishCount += countCookies
                End If
            End If

            Me.bgwScanFiles.ReportProgress(100.0 * scanedApplications / dict.Count)
            scanedApplications += 1
        Next
    End Sub

    Private Sub ScanMediaRubbish()
        Try
            ' 扫描媒体库
            If Not Me.mMediaList.Checked OrElse Me.bgwScanFiles.CancellationPending Then
                Return
            End If

            '"iTunes同步失败的音乐"
            Me.ScanFile_CallBack(Me.Language.GetString("Clear.Message.SyncFailureMusic"))
            Dim listMedia As New List(Of String)
            Dim iPodObj As SharePodLib.IPod = SharePodLib.IPod.Instance(Me.mDevice, False)

            If iPodObj Is Nothing OrElse iPodObj.Tracks Is Nothing Then
                Return
            End If

            For Each nameItem As SharePodLib.Parsers.iTunesDB.Track In iPodObj.Tracks.Items
                listMedia.Add(nameItem.FilePath)
            Next

            '如果媒体加载失败、加载出来的数量为0 就不做媒体操作
            If listMedia.Count <= 0 Then
                Return
            End If

            ' 音乐
            Dim strMusicFolder As String = "/iTunes_Control/Music"
            Dim arrMusicFolder As String() = Me.mDevice.GetFilesByAFC(strMusicFolder, True)

            For Each strFolder As String In arrMusicFolder
                Dim strMusicItemFolder As String = strMusicFolder & "/" & strFolder
                If Not Me.mDevice.IsDirectoryByAFC(strMusicItemFolder) Then
                    Continue For
                End If

                Dim arrMusicFiles() As String = Me.mDevice.GetFilesByAFC(strMusicItemFolder, True)

                For Each strName As String In arrMusicFiles
                    Dim strFile As String = strMusicItemFolder & "/" & strName
                    If listMedia.Contains(strFile) Then
                        Continue For
                    End If

                    Dim itemInfo As iPhoneFileInfo = Nothing
                    If Me.mDevice.IsDirectoryByAFC(strFile, itemInfo) Then
                        Continue For
                    End If

                    If itemInfo Is Nothing Then
                        Continue For
                    End If


                    mScanResult.AllMediaRubbishs.FileCount += 1
                    mScanResult.AllMediaRubbishs.FileSize += itemInfo.st_size
                    mScanResult.DictMediaDetail(strFile) = itemInfo.st_size
                    Me.mTotalRubbishSize += itemInfo.st_size
                    Me.mTotalRubbishCount += 1
                Next
            Next

            ' 铃声
            Dim strRingFolder As String = "/iTunes_Control/Ringtones"
            Dim arrRingFiles() As String = Me.mDevice.GetFilesByAFC(strRingFolder, True)

            For Each strName As String In arrRingFiles
                Dim strFile As String = strRingFolder & "/" & strName
                If listMedia.Contains(strFile) Then
                    Continue For
                End If

                Dim itemInfo As iPhoneFileInfo = Nothing
                If Me.mDevice.IsDirectoryByAFC(strFile, itemInfo) Then
                    Continue For
                End If

                If itemInfo Is Nothing Then
                    Continue For
                End If

                mScanResult.AllMediaRubbishs.FileCount += 1
                mScanResult.AllMediaRubbishs.FileSize += itemInfo.st_size
                mScanResult.DictMediaDetail(strFile) = itemInfo.st_size
                Me.mTotalRubbishSize += itemInfo.st_size
                Me.mTotalRubbishCount += 1

                System.Console.Out.WriteLine(strFile)

            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmClearRubbish_ScanMediaRubbish")
        End Try
    End Sub

    Private Sub ScanCrashLogRubbish()
        Try
            '扫描应用崩溃记录
            If Not Me.mCrashLogList.Checked OrElse Me.bgwScanFiles.CancellationPending Then
                Return
            End If
            Me.ScanFile_CallBack(Me.Language.GetString("Clear.Label.CrashLogRubbish"))          '"应用崩溃记录"
            Dim listCrashLogs As New List(Of String)
            Dim strCrashLogRoot As String = "/"
            Me.ScanCrashLogFolderRubbish("/", listCrashLogs)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmClearRubbish_ScanCrashLogRubbish")
        End Try
    End Sub

    Private Sub ScanCrashLogFolderRubbish(ByVal strPath As String, ByVal listCrashLogs As List(Of String))
        Dim arrCrashLogFiles() As String = Me.mDevice.GetFiles(Me.mCrashReporterHandler, strPath, True)
        Dim intIndex As Integer = 0
        For Each strName As String In arrCrashLogFiles
            Dim strFile As String = strPath & IIf(strPath.EndsWith("/"), "", "/") & strName
            If listCrashLogs.Contains(strFile) Then
                Continue For
            End If

            Dim itemInfo As iPhoneFileInfo = Nothing
            If Me.mDevice.IsDirectory(Me.mCrashReporterHandler, strFile, itemInfo) Then
                Me.ScanCrashLogFolderRubbish(strFile, listCrashLogs)
                Continue For
            End If

            If itemInfo Is Nothing OrElse strFile.ToLower.Contains("security.log") Then
                Continue For
            End If

            mScanResult.AllCrashLogRubbishs.FileCount += 1
            mScanResult.AllCrashLogRubbishs.FileSize += itemInfo.st_size
            mScanResult.DictCrashLogDetail(strFile) = itemInfo.st_size
            Me.mTotalRubbishSize += itemInfo.st_size
            Me.mTotalRubbishCount += 1
            System.Console.Out.WriteLine(strFile)

            intIndex += 1
            If intIndex Mod 20 = 0 Then
                Me.ScanFile_CallBack(Me.Language.GetString("Clear.Label.CrashLogRubbish"))          '"应用崩溃记录"
            End If
        Next
    End Sub

    Private Sub ScanPhotoRubbish()
        Try
            '扫描图片缓存
            If Not Me.mPhotosList.Checked OrElse Me.bgwScanFiles.CancellationPending Then
                Return
            End If

            Me.ScanFile_CallBack(Me.Language.GetString("Clear.Label.PhotosRubbish"))            '"图片缓存"
            Dim intIndex As Integer = 0
            '图片缓存
            Dim listPhotos As New List(Of String)
            Dim strTHMBRoot As String = "/DCIM/.THMB"
            Dim arrTHMBFolders() As String = Me.mDevice.GetFilesByAFC(strTHMBRoot, True)

            For Each strName As String In arrTHMBFolders
                Dim strPhotoFolderPath As String = strTHMBRoot & "/" & strName
                Dim arrPhotoFiles() As String = Me.mDevice.GetFilesByAFC(strPhotoFolderPath, True)
                For Each Item As String In arrPhotoFiles
                    Dim strFile As String = strPhotoFolderPath & "/" & Item
                    If listPhotos.Contains(strFile) Then
                        Continue For
                    End If

                    Dim itemInfo As iPhoneFileInfo = Nothing
                    If Me.mDevice.IsDirectoryByAFC(strFile, itemInfo) Then
                        Continue For
                    End If

                    If itemInfo Is Nothing Then
                        Continue For
                    End If

                    mScanResult.AllPhotosRubbishs.FileCount += 1
                    mScanResult.AllPhotosRubbishs.FileSize += itemInfo.st_size
                    mScanResult.DictPhotosDetail(strFile) = itemInfo.st_size
                    Me.mTotalRubbishSize += itemInfo.st_size
                    Me.mTotalRubbishCount += 1
                    System.Console.Out.WriteLine(strFile)
                    intIndex += 1
                    If intIndex Mod 20 = 0 Then
                        Me.ScanFile_CallBack(Me.Language.GetString("Clear.Label.PhotosRubbish"))            '"图片缓存"
                    End If
                Next
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmClearRubbish_ScanPhotoRubbish")
        End Try
    End Sub

    Private Sub ScanInstallFail()
        If Not Me.mInstallFailedList.Checked OrElse Me.bgwScanFiles.CancellationPending Then
            Return
        End If

        Me.mClearingAppName = Me.Language.GetString("Clear.Label.InstallTemp")                 '"安装程序缓存"

        '"安装程序缓存"
        Me.ScanFile_CallBack(Me.Language.GetString("Clear.Label.InstallTemp"))
        Dim countApplicationArchives As Long = 0
        Dim sizeApplicationArchives As Long = 0
        Dim countPublicStaging As Long = 0
        Dim sizePublicStaging As Long = 0
        Dim countDownloads As Long = 0
        Dim sizeDownloads As Long = 0
        Dim pathArchives As String = "/ApplicationArchives/"
        Dim pathPublicStaging As String = "/PublicStaging/"
        Dim pathDownloads As String = "/Downloads/"

        '取得当前备份未成功的列表
        Dim fileCount As Integer = 0
        Dim fileSize As Long = 0

        Me.mDevice.ApplicationCacheClear()


        Try
            Dim pathOnPhone As String = "/ApplicationArchives/"
            '取得当前备份未成功的列表
            Dim dicArchive As Dictionary(Of Object, Object) = Nothing

            'ios9无法清理安装失败缓存。
            If Me.mDevice.VersionNumber < 900 Then
                dicArchive = Me.mDevice.LookupArchive()
            End If

            For Each subFolder As String In Me.mDevice.GetFilesByAFC(pathOnPhone, True)
                Dim strFullPath As String = pathOnPhone & subFolder

                If Not Me.mDevice.IsDirectoryByAFC(strFullPath) Then
                    Dim strAppID As String = ""
                    If subFolder.ToLower.EndsWith(".zip") Then
                        strAppID = subFolder.Replace(".zip", "").Replace(".ZIP", "")
                        '如果列表中存在就清空记录
                        If dicArchive IsNot Nothing AndAlso dicArchive.ContainsKey(strAppID) Then
                            Me.mDevice.RemoveArchive(strAppID)
                        End If

                    End If

                    If Me.mDevice.ExistsByAFC(strFullPath) Then
                        Me.mDevice.DeleteFileByAFC(strFullPath)
                    End If
                Else
                    Me.mDevice.DeleteDirectoryByAFC(strFullPath, True)
                End If
            Next

            If dicArchive IsNot Nothing Then
                dicArchive.Clear()
                dicArchive = Nothing
            End If
        Catch ex As Exception
            Debug.Print("清理安装程序缓存出错：" & ex.ToString)
        End Try


        GetDirectorySizeInfo(Nothing, pathArchives, countApplicationArchives, sizeApplicationArchives)
        GetDirectorySizeInfo(Nothing, pathPublicStaging, countPublicStaging, sizePublicStaging)
        GetDirectorySizeInfoEx(Nothing, pathDownloads, countDownloads, sizeDownloads)

        fileCount = countApplicationArchives + countPublicStaging + countDownloads
        fileSize = sizeApplicationArchives + sizePublicStaging + sizeDownloads
        Me.mScanResult.AllInstallFailRubbish.FileCount = fileCount
        Me.mScanResult.AllInstallFailRubbish.FileSize = fileSize

    End Sub

    '扫描线程
    Private Sub bgwScanFiles_DoWork(ByVal sender As Object, ByVal args As DoWorkEventArgs)
        Try
            Me.mScanResult = New RubbishScanResult()

            Me.mTotalRubbishCount = 0
            Me.mTotalRubbishSize = 0

            Me.ScanAppRubbish()
            Me.ScanMediaRubbish()
            Me.ScanCrashLogRubbish()
            Me.ScanPhotoRubbish()
            Me.ScanInstallFail()

            args.Cancel = Me.bgwScanFiles.CancellationPending
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoScanAppRubbish")
        End Try
    End Sub

    '扫描进度变化
    Private Sub bgwScanFiles_ProgressChanged(ByVal sender As Object, ByVal args As ProgressChangedEventArgs)
        Me.prgScanProgress.tbPlayValue = args.ProgressPercentage
        Me.mTempList.FileCount = Me.mScanResult.AllTempRubbishs.FileCount.ToString
        Me.mTempList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllTempRubbishs.FileSize)
        Me.mCacheList.FileCount = Me.mScanResult.AllCacheRubbishs.FileCount.ToString
        Me.mCacheList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllCacheRubbishs.FileSize)
        Me.mCookieList.FileCount = Me.mScanResult.AllCookieRubbishs.FileCount.ToString
        Me.mCookieList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllCookieRubbishs.FileSize)
        Me.mMediaList.FileCount = Me.mScanResult.AllMediaRubbishs.FileCount.ToString
        Me.mMediaList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllMediaRubbishs.FileSize)
        Me.mCrashLogList.FileCount = Me.mScanResult.AllCrashLogRubbishs.FileCount.ToString
        Me.mCrashLogList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllCrashLogRubbishs.FileSize)
        Me.mPhotosList.FileCount = Me.mScanResult.AllPhotosRubbishs.FileCount.ToString
        Me.mPhotosList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllPhotosRubbishs.FileSize)
        Me.mInstallFailedList.FileCount = Me.mScanResult.AllInstallFailRubbish.FileCount.ToString
        Me.mInstallFailedList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllInstallFailRubbish.FileSize)
    End Sub

    '扫描完成
    Private Sub bgwScanFiles_RunWorkerCompleted(ByVal sender As Object, ByVal args As RunWorkerCompletedEventArgs)
        If Me.IsDisposed Then
            Return
        End If

        '显示扫描完成的panel
        Me.pnlDescrib.Visible = False
        Me.pnlScanning.Visible = False
        Me.pnlScanFinish.Visible = True
        Me.pnlCleanning.Visible = False

        '启用列表
        Me.pnlListData.Enabled = True
        Me.chkSelectAll.Enabled = True

        mTempList.Items = Me.mScanResult.DictTempDetail
        mCacheList.Items = Me.mScanResult.DictCacheDetail
        mCookieList.Items = Me.mScanResult.DictCookieDetail

        Dim allRubbishSize As Long = 0
        allRubbishSize += Me.mScanResult.AllTempRubbishs.FileSize
        allRubbishSize += Me.mScanResult.AllCacheRubbishs.FileSize
        allRubbishSize += Me.mScanResult.AllCookieRubbishs.FileSize
        allRubbishSize += Me.mScanResult.AllMediaRubbishs.FileSize
        allRubbishSize += Me.mScanResult.AllCrashLogRubbishs.FileSize
        allRubbishSize += Me.mScanResult.AllPhotosRubbishs.FileSize
        allRubbishSize += Me.mScanResult.AllInstallFailRubbish.FileSize

        Me.mTempList.FileCount = Me.mScanResult.AllTempRubbishs.FileCount.ToString
        Me.mTempList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllTempRubbishs.FileSize)
        Me.mCacheList.FileCount = Me.mScanResult.AllCacheRubbishs.FileCount.ToString
        Me.mCacheList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllCacheRubbishs.FileSize)
        Me.mCookieList.FileCount = Me.mScanResult.AllCookieRubbishs.FileCount.ToString
        Me.mCookieList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllCookieRubbishs.FileSize)
        Me.mMediaList.FileCount = Me.mScanResult.AllMediaRubbishs.FileCount.ToString
        Me.mMediaList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllMediaRubbishs.FileSize)
        Me.mCrashLogList.FileCount = Me.mScanResult.AllCrashLogRubbishs.FileCount.ToString
        Me.mCrashLogList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllCrashLogRubbishs.FileSize)
        Me.mPhotosList.FileCount = Me.mScanResult.AllPhotosRubbishs.FileCount.ToString
        Me.mPhotosList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllPhotosRubbishs.FileSize)
        Me.mInstallFailedList.FileCount = Me.mScanResult.AllInstallFailRubbish.FileCount.ToString
        Me.mInstallFailedList.FileSize = Utility.FormatFileSize(Me.mScanResult.AllInstallFailRubbish.FileSize)

        If allRubbishSize = 0 Then
            If Not args.Cancelled Then
                Me.lblScanFinish_Describ.Text = Me.Language.GetString("Clear.Message.NoFindRubbish")            '"恭喜您，没有发现垃圾！"
            Else
                Me.lblScanFinish_Describ.Text = Me.Language.GetString("Clear.Message.BreakOffNoFindRubbish")    '"扫描中断，没有发现垃圾！"
            End If

            Me.btnClear.Visible = False
            Me.crlRubbishSelct.Visible = False
            Me.picScanFinish.Image = My.Resources.icon_ok1
            'me.lb
            Me.crlRubbishFound.FileSize = 0
        Else
            If Not args.Cancelled Then
                Me.lblScanFinish_Describ.Text = Me.Language.GetString("Clear.Message.ScanFinish")       '"扫描完成" ' String.Format("扫描完成，共发现 {0} 垃圾", Utility.FormatFileSize(allRubbishSize))
            Else
                Me.lblScanFinish_Describ.Text = Me.Language.GetString("Clear.Message.ScanBreakOff")     '"扫描中断" ' String.Format("扫描中断，共发现 {0} 垃圾", Utility.FormatFileSize(allRubbishSize))
            End If
            Me.crlRubbishFound.FileSize = allRubbishSize
            UpdateSelectScanItems()
            Me.UpdateCheckSelectAll()
            'Me.UpdateScanFileInfo()
            Me.btnClear.Visible = True
            Me.crlRubbishSelct.Visible = True
            Me.mCacheList.IsAppend = True
            Me.picScanFinish.Image = My.Resources.icon_warning1
        End If
        Me.pnlListData.Focus()
    End Sub

    Private Sub DoScan()
        If Me.chkSelectAll.CheckState = Windows.Forms.CheckState.Unchecked Then
            '"请至少选择一个扫描项"
            tbMessageBox.Show(Me, Me.Language.GetString("Clear.Message.SelectOneItem"), Me.Language.GetString("Common.Info"), Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
            Return
        End If

        Me.ClearRubbishFileFoundControl1.FileCount = 0
        Me.ClearRubbishFileFoundControl1.FileSize = 0

        Me.prgScanProgress.tbPlayValue = 0
        'Me.lblScanningRubbishSize.Text = ""

        '显示正在扫描的panel
        Me.pnlDescrib.Visible = False
        Me.pnlScanning.Visible = True
        Me.pnlCleanning.Visible = False
        Me.pnlScanFinish.Visible = False
        Me.btnStopScan.Enabled = True

        '禁用列表
        Me.pnlListData.Enabled = False
        Me.chkSelectAll.Enabled = False

        '收起列表
        Me.mTempList.IsAppend = False
        Me.mCookieList.IsAppend = False
        Me.mCacheList.IsAppend = False
        Me.mMediaList.IsAppend = False
        Me.mCrashLogList.IsAppend = False
        Me.mPhotosList.IsAppend = False
        Me.mInstallFailedList.IsAppend = False

        '显示正在加载软件列表
        Me.lblScanning.Text = Me.Language.GetString("Clear.Message.LoadingAppList")             '"正在加载软件列表..."

        If Not bgwScanFiles.IsBusy Then
            bgwScanFiles.RunWorkerAsync()
        End If

    End Sub

    '清空手机里面的目录
    Private Sub ClearDirectory(ByVal info As FileSharingPackageInfo, ByVal strDir As String)
        Try
            If Me.bgwClearFiles.CancellationPending Then
                Return
            End If

            If Not strDir.EndsWith("/") Then
                strDir &= "/"
            End If

            Dim arrNames() As String = Me.GetFiles(info, strDir, True)
            For Each strName As String In arrNames
                Dim strPath As String = strDir & strName
                Dim iFileInfo As iPhoneFileInfo = Nothing

                If Me.IsDirectory(info, strPath, iFileInfo) Then
                    Me.DeleteDirectory(info, strPath, True, AddressOf DeleteFile_CallBack)
                Else
                    Me.DeleteFile(info, strPath)
                End If
            Next
        Catch ex As Exception
        End Try
    End Sub

    '清空手机里面的目录
    Private Sub ClearDirectoryEx(ByVal info As FileSharingPackageInfo, ByVal strDir As String)
        Try
            If Me.bgwClearFiles.CancellationPending Then
                Return
            End If

            If Not strDir.EndsWith("/") Then
                strDir &= "/"
            End If

            Dim arrNames() As String = Me.GetFiles(info, strDir, True)
            For Each strName As String In arrNames
                If strName.StartsWith("downloads", StringComparison.OrdinalIgnoreCase) AndAlso _
                   (strName.EndsWith(".sqlitedb", StringComparison.OrdinalIgnoreCase) OrElse strName.EndsWith(".db", StringComparison.OrdinalIgnoreCase)) Then
                    Continue For
                End If

                Dim strPath As String = strDir & strName
                Dim iFileInfo As iPhoneFileInfo = Nothing

                If Me.IsDirectory(info, strPath, iFileInfo) Then
                    Me.DeleteDirectory(info, strPath, True, AddressOf DeleteFile_CallBack)
                Else
                    Me.DeleteFile(info, strPath)
                End If
            Next
        Catch ex As Exception
        End Try
    End Sub

    Private Function ClearAppRubbish() As Long
        Dim sizeCleared As Long = 0
        Dim iClearedAppCount As Integer = 0

        ' 临时目录
        Dim arrTmpPath As String() = {"/tmp", "/Library/tmp", "/Library/Tmp", "/Library/temp", "/Library/Temp"}
        Dim pathCache As String = "/Library/Caches"
        Dim pathCookie As String = "/Library/Cookies"

        mDictDirtyApp.Clear()

        '初始含有垃圾的app
        For Each pair As KeyValuePair(Of String, RubbishInfo) In Me.mScanResult.DictTempDetail
            If pair.Value.IsChecked Then
                If Not mDictDirtyApp.ContainsKey(pair.Key) Then
                    Dim clrSetting As New RubbishClearSetting
                    clrSetting.Name = pair.Value.AppName
                    mDictDirtyApp(pair.Key) = clrSetting
                End If
                mDictDirtyApp(pair.Key).IsScanTemp = True
                mDictDirtyApp(pair.Key).TotalFileCount += pair.Value.FileCount
            End If
        Next

        For Each pair As KeyValuePair(Of String, RubbishInfo) In Me.mScanResult.DictCacheDetail
            If pair.Value.IsChecked Then
                If Not mDictDirtyApp.ContainsKey(pair.Key) Then
                    Dim clrSetting As New RubbishClearSetting
                    clrSetting.Name = pair.Value.AppName
                    mDictDirtyApp(pair.Key) = clrSetting
                End If
                mDictDirtyApp(pair.Key).IsScanCache = True
                mDictDirtyApp(pair.Key).TotalFileCount += pair.Value.FileCount
            End If
        Next

        For Each pair As KeyValuePair(Of String, RubbishInfo) In Me.mScanResult.DictCookieDetail
            If pair.Value.IsChecked Then
                If Not mDictDirtyApp.ContainsKey(pair.Key) Then
                    Dim clrSetting As New RubbishClearSetting
                    clrSetting.Name = pair.Value.AppName
                    mDictDirtyApp(pair.Key) = clrSetting
                End If
                mDictDirtyApp(pair.Key).IsScanCookie = True
                mDictDirtyApp(pair.Key).TotalFileCount += pair.Value.FileCount
            End If
        Next

        Dim dictApp As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User, False)
        '开始清理脏目录
        For Each pair As KeyValuePair(Of String, RubbishClearSetting) In mDictDirtyApp
            If Me.bgwClearFiles.CancellationPending Then
                Exit For
            End If

            If Not dictApp.ContainsKey(pair.Key) Then
                Continue For
            End If

            Dim strSKU As String = pair.Key
            Dim info As FileSharingPackageInfo = dictApp(strSKU)
            Dim clearInfo As RubbishClearSetting = pair.Value

            Me.mTotalClearCount = 0
            Me.mClearingAppName = strSKU

            If clearInfo.IsScanTemp Then
                For Each strTmpDirectory As String In arrTmpPath
                    Me.ClearDirectory(info, strTmpDirectory)
                Next
                If Me.mScanResult.DictTempDetail.ContainsKey(strSKU) Then
                    sizeCleared += Me.mScanResult.DictTempDetail(strSKU).FileSize
                End If

            End If

            If clearInfo.IsScanCache Then
                Me.ClearDirectory(info, pathCache)
                If Me.mScanResult.DictCacheDetail.ContainsKey(strSKU) Then
                    sizeCleared += Me.mScanResult.DictCacheDetail(strSKU).FileSize
                End If
            End If

            If clearInfo.IsScanCookie Then
                Me.ClearDirectory(info, pathCookie)
                If Me.mScanResult.DictCookieDetail.ContainsKey(strSKU) Then
                    sizeCleared += Me.mScanResult.DictCookieDetail(strSKU).FileSize
                End If
            End If

            'Me.ClearFile_CallBack(clearInfo.Name)
            iClearedAppCount += 1
        Next

        Return sizeCleared
    End Function

    Private Function ClearMediaRubbish() As Long
        mClearingAppName = Me.Language.GetString("Clear.Message.SyncFailureMusic")          '"iTune同步失败的音乐"

        Dim sizeCleared As Long = 0

        If Me.mMediaList.Checked Then
            For Each strRubbishMedia As String In Me.mScanResult.DictMediaDetail.Keys
                If Me.bgwClearFiles.CancellationPending Then
                    Exit For
                End If

                Me.mDevice.DeleteFileByAFC(strRubbishMedia)
                sizeCleared += Me.mScanResult.DictMediaDetail(strRubbishMedia)
            Next
        End If
        Return sizeCleared
    End Function

    Private Function ClearCrashLogRubbish() As Long
        mClearingAppName = Me.Language.GetString("Clear.Label.CrashLogRubbish")             '"应用崩溃记录"

        Dim sizeCleared As Long = 0

        If Me.mCrashLogList.Checked Then
            For Each strRubbishCrashLog As String In Me.mScanResult.DictCrashLogDetail.Keys
                If Me.bgwClearFiles.CancellationPending Then
                    Exit For
                End If

                Me.mDevice.DeleteFile(Me.mCrashReporterHandler, strRubbishCrashLog)
                sizeCleared += Me.mScanResult.DictCrashLogDetail(strRubbishCrashLog)
            Next
        End If
        Return sizeCleared
    End Function

    Private Function ClearPhotosRubbish() As Long
        mClearingAppName = Me.Language.GetString("Clear.Label.PhotosRubbish")                ' "图片缓存"

        Dim sizeCleared As Long = 0

        If Me.mPhotosList.Checked Then
            For Each strRubbishPhoto As String In Me.mScanResult.DictPhotosDetail.Keys
                If Me.bgwClearFiles.CancellationPending Then
                    Exit For
                End If

                Me.mDevice.DeleteFileByAFC(strRubbishPhoto)
                sizeCleared += Me.mScanResult.DictPhotosDetail(strRubbishPhoto)
            Next
        End If
        Return sizeCleared
    End Function

    Private Function ClearInstallFailRubbish() As Long
        mClearingAppName = Me.Language.GetString("Clear.Label.InstallFailureRubbish")           '"安装失败产生的垃圾"

        Dim sizeCleared As Long = 0

        If Me.mInstallFailedList.Checked Then
            Me.ClearDirectory(Nothing, "/ApplicationArchives/")
            Me.ClearDirectory(Nothing, "/PublicStaging/")
            Me.ClearDirectoryEx(Nothing, "/Downloads/")

            sizeCleared += Me.mScanResult.AllInstallFailRubbish.FileSize
        End If

        Return sizeCleared
    End Function

    '清理线程
    Private Sub bgwClearFiles_DoWork(ByVal sender As Object, ByVal args As DoWorkEventArgs)
        Try
            Dim sizeCleared As Long = 0
            Me.mTotalClearSize = 0

            sizeCleared += ClearAppRubbish()
            sizeCleared += ClearMediaRubbish()
            sizeCleared += ClearCrashLogRubbish()
            sizeCleared += ClearPhotosRubbish()
            sizeCleared += ClearInstallFailRubbish()

            args.Result = sizeCleared
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwClearFiles_DoWork")
        End Try
    End Sub

    '清理进度变化
    Private Sub bgwClearFiles_ProgressChanged(ByVal sender As Object, ByVal args As ProgressChangedEventArgs)
        Me.prgCleanProgress.tbPlayValue = args.ProgressPercentage
    End Sub

    '清理完成
    Private Sub bgwClearFiles_RunWorkerCompleted(ByVal sender As Object, ByVal args As RunWorkerCompletedEventArgs)
        If Me.IsDisposed Then
            Return
        End If
        Dim sizeCleared As Long = args.Result
        Me.prgCleanProgress.tbPlayValue = 100
        'Me.lblReportRubbishSize.Text = Utility.FormatFileSize(sizeCleared)
        Me.mScanResult.DictTempDetail.Clear()

        Me.mScanResult.DictTempDetail.Clear()
        Me.mScanResult.DictMediaDetail.Clear()
        Me.mScanResult.DictCacheDetail.Clear()
        Me.mScanResult.DictCookieDetail.Clear()

        Me.mCacheList.Items = Nothing
        Me.mCookieList.Items = Nothing
        Me.mCacheList.Items = Nothing
        Me.mMediaList.Items = Nothing
        Me.mCrashLogList.Items = Nothing
        Me.mPhotosList.Items = Nothing

        Me.mCacheList.FileCount = ""
        Me.mTempList.FileCount = ""
        Me.mCookieList.FileCount = ""
        Me.mMediaList.FileCount = ""
        Me.mCrashLogList.FileCount = ""
        Me.mPhotosList.FileCount = ""
        Me.mInstallFailedList.FileCount = ""

        Me.mCacheList.FileSize = ""
        Me.mTempList.FileSize = ""
        Me.mCookieList.FileSize = ""
        Me.mMediaList.FileSize = ""
        Me.mCrashLogList.FileSize = ""
        Me.mPhotosList.FileSize = ""
        Me.mInstallFailedList.FileSize = ""

        mScanResult = Nothing
        ' SetLastClearTimeOnDevice()

        SetResultImage(sizeCleared)

        Me.pnlFunction.Visible = False
        Me.pnlReport.Visible = True
        Me.pnlListData.Enabled = True
        Me.chkSelectAll.Enabled = True
        Me.chkSharedWeibo.Checked = True

    End Sub

    Private Sub DoClear()
        Try
            If Me.chkSelectAll.CheckState = Windows.Forms.CheckState.Unchecked Then
                '"请至少选择一个清理项"
                tbMessageBox.Show(Me, Me.Language.GetString("Clear.Message.SelectOneItemToClear"), Me.Language.GetString("Common.Info"), Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                Return
            End If

            If mSelectedRubbishSize = 0 Then
                '"您未选择任何垃圾"
                tbMessageBox.Show(Me, Me.Language.GetString("Clear.Message.HaveNoSelectRubbish"), Me.Language.GetString("Common.Info"), Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                Return
            End If


            '显示正在清理页面
            Me.pnlDescrib.Visible = False
            Me.pnlCleanning.Visible = True
            Me.pnlScanFinish.Visible = False
            Me.pnlScanning.Visible = False
            Me.btnStopClear.Visible = True
            Me.btnStopClear.Enabled = True

            '禁用列表
            Me.pnlListData.Enabled = False
            Me.chkSelectAll.Enabled = False

            Me.mTempList.ApplyCheckToData()
            Me.mCookieList.ApplyCheckToData()
            Me.mCacheList.ApplyCheckToData()
            Me.mMediaList.ApplyCheckToData()
            Me.mCrashLogList.ApplyCheckToData()
            Me.mPhotosList.ApplyCheckToData()

            Me.mTempList.IsAppend = False
            Me.mCookieList.IsAppend = False
            Me.mCacheList.IsAppend = False
            Me.mMediaList.IsAppend = False
            Me.mCrashLogList.IsAppend = False
            Me.mPhotosList.IsAppend = False

            Me.prgCleanProgress.tbPlayValue = 0

            If Not Me.bgwClearFiles.IsBusy Then
                bgwClearFiles.RunWorkerAsync()
            End If

            TongbuPlist.SetLastClearTime(Me.mDevice)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmClearRubbish_ClickClear")
        End Try
    End Sub

    ''获取最近清理的时间
    'Public Shared Function GetLastClearTime(ByVal device As iPhoneDevice) As DateTime
    '    Dim result As DateTime = Nothing
    '    Dim pathOnDevice As String = "/iTunes_Control/iTunes/Tongbu.plist"
    '    Dim pathOnPC As String = Folder.GetTempFilePath()
    '    If device IsNot Nothing AndAlso device.DownFromPhoneByAFC(pathOnDevice, pathOnPC) Then
    '        Dim dict As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ReadPlist_managed(pathOnPC)
    '        If dict IsNot Nothing AndAlso dict.ContainsKey("LastClearTime") Then
    '            result = dict("LastClearTime")
    '        End If
    '    End If
    '    Return result
    'End Function

    ''设置最近清理的时间
    'Private Sub SetLastClearTimeOnDevice()
    '    Dim pathOnPC As String = Folder.GetTempFilePath()
    '    Dim pathOnDevice As String = "/iTunes_Control/iTunes/Tongbu.plist" ' DateTime.Now.ToString("yyyy-MM-dd hh_mm_ss")
    '    Dim dict As New Dictionary(Of Object, Object)
    '    dict.Add("LastClearTime", Now)
    '    If iTong.Device.CoreFoundation.CreatePlist(dict, pathOnPC) Then
    '        Me.mDevice.CopyToPhoneByAFC(pathOnPC, pathOnDevice)
    '    End If
    'End Sub

    '统计接口访问并且获取数据
    Private Sub PostFunctionMessage()
        Me.bgwUploadUseMessage = New BackgroundWorker
        Me.bgwUploadUseMessage.WorkerSupportsCancellation = True

        AddHandler Me.bgwUploadUseMessage.DoWork, AddressOf Me.DoPostFunctionMessage
        AddHandler Me.bgwUploadUseMessage.RunWorkerCompleted, AddressOf Me.OnPostFunctionMessageFinish
        Me.bgwUploadUseMessage.RunWorkerAsync()
        'Dim threadPostFunction As Threading.Thread = New Threading.Thread(AddressOf DoPostFunctionMessage)
        'threadPostFunction.IsBackground = True

        'threadPostFunction.Start()
    End Sub

    '接口访问线程
    Private Sub DoPostFunctionMessage(ByVal sender As Object, ByVal args As DoWorkEventArgs)
        Try
            Dim strInfo = String.Format("{0};{1};{2}", Me.mDevice.UniqueDeviceID, Me.mDevice.WiFiAddress, Me.mDevice.SerialNumber)
            strInfo = Common.EncryptDES(strInfo, "ac68!3#1", "ac68!3#1")
            strInfo = strInfo.Replace("+", "_@_")
            '"http://server.tongbu.com/tbCloud/TbzsData/PCData.aspx?op=1&v={0}"
            Dim url As String = String.Format(WebUrl.ChinaRubbishUserCount, strInfo)
            Dim res As String = Utility.GetContentStringFromUrl(url, System.Text.Encoding.UTF8)
            res = res.Replace("Num=", "")

            Try
                Dim users As Long = 0
                Try
                    If Not String.IsNullOrEmpty(res) Then
                        users = Long.Parse(res)
                    End If
                Catch ex As Exception
                    users = -1
                End Try


                If users > mUserCount Then
                    mUserCount = users
                    IniClass.SetIniSectionKey("Clear", "UserCount", mUserCount.ToString(), Folder.ConfigIniFile)
                End If

                If Me.webFirstPage.Visible = True Then
                    If Me.mIsFirstPage Then
                        args.Result = Me.ReleaseWebPage1(mUserCount)
                    End If

                End If
            Catch ex As Exception

            End Try

        Catch ex As Exception
        End Try
    End Sub

    '统计接口完成回调
    Private Sub OnPostFunctionMessageFinish(ByVal sender As Object, ByVal args As RunWorkerCompletedEventArgs)
        'If Me.mIsFirstPage Then
        '    Dim strFilePath As String = args.Result
        '    Me.webFirstPage.Navigate(strFilePath)
        'End If
    End Sub

    '跳转
    Private Sub webFirstPage_Navigating(ByVal sender As Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs) Handles webFirstPage.Navigating
        If e.Url.ToString.StartsWith("tongbu://clear/") Then
            e.Cancel = True
            Me.webFirstPage.Visible = False
            Me.pnlFunction.Visible = True
            Me.pnlReport.Visible = False

            Me.btnScan_Click(Me, Nothing)
        End If
    End Sub

#Region "--- 加载静态网页 ---"

    Private Function ReleaseWebPage1(ByVal strNum As String) As String
        Dim folderWebPage As String = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage")
        Folder.CheckFolder(folderWebPage)

        Dim webPage1 As String = Path.Combine(folderWebPage, "welcome1.html")
        Dim bg1 As String = Path.Combine(folderWebPage, "bg01.png")
        Dim btn As String = Path.Combine(folderWebPage, "btn01.png")

        Dim strContent As String = My.Resources.html_clear_welcome1
        Dim strCount As String = String.Format("{0}<i>{1}</i>", strNum, Me.Language.GetString("Clear.Label.Man"))   '"人"
        strContent = strContent.Replace("#Count#", strCount)
        strContent = strContent.Replace("#HadClear#", Me.Language.GetString("Clear.Message.HadClear"))              '"已清理垃圾..."
        strContent = strContent.Replace("#Description#", Me.Language.GetString("Clear.Message.ClearDescription"))   '"清理垃圾，找回可用空间，设备更快更干净！"
        strContent = strContent.Replace("#AtOnce#", Me.Language.GetString("Clear.Button.ClearAtOnce"))              '"马上清理"

        If File.Exists(webPage1) Then
            Try
                File.Delete(webPage1)
                File.Delete(btn)
            Catch
            End Try
        End If

        Using objWriter As New System.IO.StreamWriter(webPage1, False, System.Text.Encoding.UTF8)
            objWriter.Write(strContent)
        End Using

        If Not File.Exists(bg1) Then
            Using objWriter As New FileStream(bg1, FileMode.OpenOrCreate, FileAccess.Write)
                objWriter.Write(My.Resources.web_bg_welcome1, 0, My.Resources.web_bg_welcome1.Length)
            End Using
        End If

        If Not File.Exists(btn) Then
            Using objWriter As New FileStream(btn, FileMode.OpenOrCreate, FileAccess.Write)
                objWriter.Write(My.Resources.web_btn_clear, 0, My.Resources.web_btn_clear.Length)
            End Using
        End If

        Return webPage1
    End Function

    Private Function ReleaseWebPage2(ByVal strTime As String) As String
        Dim folderWebPage As String = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage")
        Folder.CheckFolder(folderWebPage)

        Dim webPage2 As String = Path.Combine(folderWebPage, "welcome2.html")
        Dim bg2 As String = Path.Combine(folderWebPage, "bg2.png")
        Dim btn As String = Path.Combine(folderWebPage, "btn01.png")

        Dim strContent As String = My.Resources.html_clear_welcome2
        Dim days As String = String.Format("<em>{0} </em>", strTime)
        Dim strReplace As String = String.Format(Me.Language.GetString("Clear.Message.LastTimeClear"), days)        '"上次清理：{0}前，又攒了不少垃圾了吧..."
        strContent = strContent.Replace("#Description#", strReplace)
        strContent = strContent.Replace("#AtOnce#", Me.Language.GetString("Clear.Button.ClearAtOnce"))              '"马上清理"

        If File.Exists(webPage2) Then
            Try
                File.Delete(webPage2)
                File.Delete(btn)
            Catch
            End Try
        End If

        Using objWriter As New System.IO.StreamWriter(webPage2, False, System.Text.Encoding.UTF8)
            objWriter.Write(strContent)
        End Using

        If Not File.Exists(bg2) Then
            Using objWriter As New FileStream(bg2, FileMode.OpenOrCreate, FileAccess.Write)
                objWriter.Write(My.Resources.web_bg_welcome2, 0, My.Resources.web_bg_welcome2.Length)
            End Using
        End If

        If Not File.Exists(btn) Then
            Using objWriter As New FileStream(btn, FileMode.OpenOrCreate, FileAccess.Write)
                objWriter.Write(My.Resources.web_btn_clear, 0, My.Resources.web_btn_clear.Length)
            End Using
        End If

        Return webPage2
    End Function

#End Region

End Class

Public Class ExpendStatus
    Public IsExpended As Boolean = False
    Public ListChilds As List(Of tbDataGridViewRow) = New List(Of tbDataGridViewRow)
End Class

Public Class IgnoreInfo
    Public Sku As String = ""
    Public Version As String = ""
    Public Reason As String = ""
End Class

Public Class RubbishInfo
    Public IgnoreReason As String = ""                    '例外的文字
    Public IsSkipped As Boolean = False
    Public IsChecked As Boolean = True
    Public AppName As String = ""
    Public Icon As Image = Nothing
    Public FileSize As Long = 0
    Public FileCount As Long = 0
    Public FileCountWithDir As Long = 0
    Public DictFileInfo As New Dictionary(Of String, Long)
End Class

Public Class RubbishScanSetting
    Public IsScanTemp As Boolean = False
    Public IsScanCache As Boolean = False
    Public IsScanCookie As Boolean = False
End Class

Public Class RubbishClearSetting
    Public Name As String = ""
    Public IsScanTemp As Boolean = False
    Public IsScanCache As Boolean = False
    Public IsScanCookie As Boolean = False
    Public TotalFileCount As Integer = 0
    Public TotalFileCountIncludeDir As Integer = 0
End Class

Public Class RubbishScanResult
    Public AllTempRubbishs As New RubbishInfo
    Public AllCacheRubbishs As New RubbishInfo
    Public AllCookieRubbishs As New RubbishInfo
    Public AllMediaRubbishs As New RubbishInfo
    Public AllCrashLogRubbishs As New RubbishInfo
    Public AllPhotosRubbishs As New RubbishInfo
    Public AllInstallFailRubbish As New RubbishInfo

    Public DictTempDetail As New Dictionary(Of String, RubbishInfo)   'key:软件sky,value:垃圾扫描结果
    Public DictCacheDetail As New Dictionary(Of String, RubbishInfo)
    Public DictCookieDetail As New Dictionary(Of String, RubbishInfo)
    Public DictMediaDetail As New Dictionary(Of String, Long)            'key：文件名,value:文件大小
    Public DictCrashLogDetail As New Dictionary(Of String, Long)            'key：文件名,value:文件大小
    Public DictPhotosDetail As New Dictionary(Of String, Long)            'key：文件名,value:文件大小
    Public DictInstallFailDetail As New Dictionary(Of String, Long)
End Class
