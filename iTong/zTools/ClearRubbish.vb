﻿Imports System.ComponentModel
Imports System.IO

Public Class ClearRubbish
    Inherits tbBackgroundWorker

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice, ByVal item As tbButton)
        MyBase.New(app, device, item)

    End Sub

    Protected Overrides Sub OnDoWork(ByVal e As System.ComponentModel.DoWorkEventArgs)
        MyBase.OnDoWork(e)

        Dim intFileCount As Integer = 0

        Try
            Me.OnProgressChanged(New ProgressChangedEventArgs(10, "正在检查冗余数据..."))

            Me.mDevice.ApplicationCacheClear()

            Try
                Dim pathOnPhone As String = "/ApplicationArchives/"
                '取得当前备份未成功的列表
                Dim dicArchive As Dictionary(Of Object, Object) = Me.mDevice.LookupArchive()


                For Each subFolder As String In Me.mDevice.GetFilesByAFC(pathOnPhone, True)
                    Dim strFullPath As String = pathOnPhone & subFolder

                    If Not Me.mDevice.IsDirectoryByAFC(strFullPath) Then
                        Dim strAppID As String = ""

                        If subFolder.ToLower.EndsWith(".zip") Then
                            strAppID = subFolder.Replace(".zip", "").Replace(".ZIP", "")
                            '如果列表中存在就清空记录
                            If dicArchive IsNot Nothing AndAlso dicArchive.ContainsKey(strAppID) Then
                                Me.mDevice.RemoveArchive(strAppID)
                            End If

                        End If

                        If Me.mDevice.ExistsByAFC(strFullPath) Then
                            Me.mDevice.DeleteFileByAFC(strFullPath)
                        End If
                    Else
                        Me.mDevice.DeleteDirectoryByAFC(strFullPath, True)
                    End If
                Next

                If dicArchive IsNot Nothing Then
                    dicArchive.Clear()
                    dicArchive = Nothing
                End If
            Catch ex As Exception
                Debug.Print("清理安装程序缓存出错：" & ex.ToString)
            End Try

            Dim strPath As String = String.Empty

            '删除下载的临时目录
            strPath = "/Downloads/"
            Me.DeleteFolderEx(strPath, intFileCount)

            '删除安装的目录
            strPath = "/PublicStaging/"
            Me.DeleteFolder(strPath, intFileCount)

            e.Result = True
        Catch ex As Exception
            e.Result = False

            '"修复失败"
            Me.OnProgressChanged(New ProgressChangedEventArgs(100, "修复失败"))
            Common.LogException(ex.ToString(), "RepairSpringboard_OnDoWork")

        End Try

        '"清理完毕。请长按关机键，关机后再启动设备。"
        Me.OnProgressChanged(New ProgressChangedEventArgs(100, "清理完毕。请长按关机键，关机后再启动设备。"))
    End Sub

    Private Sub DeleteFolder(ByVal strPath As String, ByRef intFileCount As Integer)
        Dim arrFile() As String = Me.mDevice.GetFilesByAFC(strPath, True)
        For Each strName As String In arrFile
            Dim strFilePath As String = strPath & strName
            If Me.mDevice.IsDirectoryByAFC(strFilePath) Then
                Me.mDevice.DeleteDirectoryByAFC(strFilePath)
            Else
                Me.mDevice.DeleteFileByAFC(strFilePath)
            End If

            intFileCount += 1

            Me.OnProgressChanged(New ProgressChangedEventArgs(100, String.Format("正在删除 {0}", strName)))
        Next
    End Sub

    Private Sub DeleteFolderEx(ByVal strPath As String, ByRef intFileCount As Integer)
        Dim arrFile() As String = Me.mDevice.GetFilesByAFC(strPath, True)
        For Each strName As String In arrFile
            If strName.StartsWith("downloads", StringComparison.OrdinalIgnoreCase) AndAlso _
                 (strName.EndsWith(".sqlitedb", StringComparison.OrdinalIgnoreCase) OrElse strName.EndsWith(".db", StringComparison.OrdinalIgnoreCase)) Then
                Continue For
            End If

            Dim strFilePath As String = strPath & strName
            If Me.mDevice.IsDirectoryByAFC(strFilePath) Then
                Me.mDevice.DeleteDirectoryByAFC(strFilePath)
            Else
                Me.mDevice.DeleteFileByAFC(strFilePath)
            End If

            intFileCount += 1

            Me.OnProgressChanged(New ProgressChangedEventArgs(100, String.Format("正在删除 {0}", strName)))
        Next
    End Sub

End Class