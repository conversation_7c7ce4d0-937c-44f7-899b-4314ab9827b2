﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmTools
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmTools))
        Me.btnClose = New tbButton
        Me.lblDiscription = New tbLabel
        Me.btnStart = New tbButton
        Me.pbImage = New System.Windows.Forms.PictureBox
        Me.lblTitle = New tbLabel
        Me.btnForce = New tbButton
        Me.lblMessage = New System.Windows.Forms.Label
        Me.btnSubmit = New tbButton
        Me.btnHelpWeb = New tbButton
        Me.lblHelpWeb = New System.Windows.Forms.LinkLabel
        Me.tmrLoading = New System.Windows.Forms.Timer(Me.components)
        Me.lblQA = New System.Windows.Forms.LinkLabel
        CType(Me.pbImage, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(416, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(392, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(368, 0)
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose.BackColor = System.Drawing.Color.Transparent
        Me.btnClose.BindingForm = Nothing
        Me.btnClose.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClose.Location = New System.Drawing.Point(414, 3)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnClose.Selectable = True
        Me.btnClose.Size = New System.Drawing.Size(24, 24)
        Me.btnClose.TabIndex = 38
        Me.btnClose.TabStop = False
        Me.btnClose.tbAdriftIconWhenHover = False
        Me.btnClose.tbAutoSize = True
        Me.btnClose.tbAutoSizeEx = False
        Me.btnClose.tbBackgroundImage = My.Resources.btn_close
        Me.btnClose.tbBackgroundImageState = ImageState.ThreeState
        Me.btnClose.tbBadgeNumber = 0
        Me.btnClose.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClose.tbEndEllipsis = False
        Me.btnClose.tbIconHoldPlace = True
        Me.btnClose.tbIconImage = Nothing
        Me.btnClose.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbIconImageState = ImageState.OneState
        Me.btnClose.tbIconMore = False
        Me.btnClose.tbIconMouseDown = Nothing
        Me.btnClose.tbIconMouseHover = Nothing
        Me.btnClose.tbIconMouseLeave = Nothing
        Me.btnClose.tbIconPlaceText = 2
        Me.btnClose.tbIconReadOnly = Nothing
        Me.btnClose.tbImageMouseDown = Nothing
        Me.btnClose.tbImageMouseHover = Nothing
        Me.btnClose.tbImageMouseLeave = Nothing
        Me.btnClose.tbReadOnly = False
        Me.btnClose.tbReadOnlyText = False
        Me.btnClose.tbShadow = False
        Me.btnClose.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnClose.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnClose.tbShowNew = False
        Me.btnClose.tbShowToolTipOnButton = False
        Me.btnClose.tbSplit = "3,3,3,3"
        Me.btnClose.tbText = ""
        Me.btnClose.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbTextColor = System.Drawing.Color.White
        Me.btnClose.tbTextColorDisable = System.Drawing.Color.White
        Me.btnClose.tbTextColorDown = System.Drawing.Color.White
        Me.btnClose.tbTextColorHover = System.Drawing.Color.White
        Me.btnClose.tbTextMouseDownPlace = 0
        Me.btnClose.tbToolTip = ""
        Me.btnClose.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClose.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClose.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.VisibleEx = True
        '
        'lblDiscription
        '
        Me.lblDiscription.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblDiscription.BackColor = System.Drawing.Color.Transparent
        Me.lblDiscription.Location = New System.Drawing.Point(99, 78)
        Me.lblDiscription.Name = "lblDiscription"
        Me.lblDiscription.Size = New System.Drawing.Size(333, 73)
        Me.lblDiscription.TabIndex = 39
        Me.lblDiscription.tbAdriftWhenHover = False
        Me.lblDiscription.tbAutoEllipsis = False
        Me.lblDiscription.tbAutoSize = False
        Me.lblDiscription.tbHideImage = False
        Me.lblDiscription.tbIconImage = Nothing
        Me.lblDiscription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDiscription.tbIconPlaceText = 5
        Me.lblDiscription.tbShadow = False
        Me.lblDiscription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDiscription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDiscription.tbShowScrolling = False
        Me.lblDiscription.Text = "如果您下载的正版软件运行时需要输入Apple ID和密码，" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "那么您可以尝试用此功能进行授权。"
        '
        'btnStart
        '
        Me.btnStart.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnStart.BackColor = System.Drawing.Color.Transparent
        Me.btnStart.BindingForm = Nothing
        Me.btnStart.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStart.Location = New System.Drawing.Point(348, 261)
        Me.btnStart.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnStart.Name = "btnStart"
        Me.btnStart.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStart.Selectable = True
        Me.btnStart.Size = New System.Drawing.Size(74, 25)
        Me.btnStart.TabIndex = 41
        Me.btnStart.tbAdriftIconWhenHover = False
        Me.btnStart.tbAutoSize = False
        Me.btnStart.tbAutoSizeEx = False
        Me.btnStart.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnStart.tbBackgroundImageState = ImageState.FourState
        Me.btnStart.tbBadgeNumber = 0
        Me.btnStart.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStart.tbEndEllipsis = False
        Me.btnStart.tbIconHoldPlace = True
        Me.btnStart.tbIconImage = Nothing
        Me.btnStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.tbIconImageState = ImageState.OneState
        Me.btnStart.tbIconMore = False
        Me.btnStart.tbIconMouseDown = Nothing
        Me.btnStart.tbIconMouseHover = Nothing
        Me.btnStart.tbIconMouseLeave = Nothing
        Me.btnStart.tbIconPlaceText = 2
        Me.btnStart.tbIconReadOnly = Nothing
        Me.btnStart.tbImageMouseDown = Nothing
        Me.btnStart.tbImageMouseHover = Nothing
        Me.btnStart.tbImageMouseLeave = Nothing
        Me.btnStart.tbReadOnly = False
        Me.btnStart.tbReadOnlyText = False
        Me.btnStart.tbShadow = False
        Me.btnStart.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStart.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStart.tbShowNew = False
        Me.btnStart.tbShowToolTipOnButton = False
        Me.btnStart.tbSplit = "13,11,13,11"
        Me.btnStart.tbText = "修复"
        Me.btnStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.tbTextColor = System.Drawing.Color.White
        Me.btnStart.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStart.tbTextColorDown = System.Drawing.Color.White
        Me.btnStart.tbTextColorHover = System.Drawing.Color.White
        Me.btnStart.tbTextMouseDownPlace = 0
        Me.btnStart.tbToolTip = ""
        Me.btnStart.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStart.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnStart.VisibleEx = True
        '
        'pbImage
        '
        Me.pbImage.BackColor = System.Drawing.Color.Transparent
        Me.pbImage.Image = My.Resources.more_repair
        Me.pbImage.Location = New System.Drawing.Point(20, 96)
        Me.pbImage.Name = "pbImage"
        Me.pbImage.Size = New System.Drawing.Size(58, 58)
        Me.pbImage.TabIndex = 43
        Me.pbImage.TabStop = False
        '
        'lblTitle
        '
        Me.lblTitle.AutoEllipsis = True
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblTitle.Location = New System.Drawing.Point(99, 48)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(322, 25)
        Me.lblTitle.TabIndex = 44
        Me.lblTitle.tbAdriftWhenHover = False
        Me.lblTitle.tbAutoEllipsis = True
        Me.lblTitle.tbAutoSize = False
        Me.lblTitle.tbHideImage = False
        Me.lblTitle.tbIconImage = Nothing
        Me.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.lblTitle.tbIconPlaceText = 5
        Me.lblTitle.tbShadow = False
        Me.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTitle.tbShowScrolling = False
        Me.lblTitle.Text = "正版授权"
        '
        'btnForce
        '
        Me.btnForce.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnForce.BackColor = System.Drawing.Color.Transparent
        Me.btnForce.BindingForm = Nothing
        Me.btnForce.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnForce.Location = New System.Drawing.Point(177, 261)
        Me.btnForce.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnForce.Name = "btnForce"
        Me.btnForce.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnForce.Selectable = True
        Me.btnForce.Size = New System.Drawing.Size(70, 25)
        Me.btnForce.TabIndex = 41
        Me.btnForce.tbAdriftIconWhenHover = False
        Me.btnForce.tbAutoSize = False
        Me.btnForce.tbAutoSizeEx = True
        Me.btnForce.tbBackgroundImage = My.Resources.btn_welcome_connect
        Me.btnForce.tbBackgroundImageState = ImageState.FourState
        Me.btnForce.tbBadgeNumber = 0
        Me.btnForce.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnForce.tbEndEllipsis = False
        Me.btnForce.tbIconHoldPlace = True
        Me.btnForce.tbIconImage = Nothing
        Me.btnForce.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnForce.tbIconImageState = ImageState.OneState
        Me.btnForce.tbIconMore = False
        Me.btnForce.tbIconMouseDown = Nothing
        Me.btnForce.tbIconMouseHover = Nothing
        Me.btnForce.tbIconMouseLeave = Nothing
        Me.btnForce.tbIconPlaceText = 2
        Me.btnForce.tbIconReadOnly = Nothing
        Me.btnForce.tbImageMouseDown = Nothing
        Me.btnForce.tbImageMouseHover = Nothing
        Me.btnForce.tbImageMouseLeave = Nothing
        Me.btnForce.tbReadOnly = False
        Me.btnForce.tbReadOnlyText = False
        Me.btnForce.tbShadow = False
        Me.btnForce.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnForce.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnForce.tbShowNew = False
        Me.btnForce.tbShowToolTipOnButton = False
        Me.btnForce.tbSplit = "13,11,13,11"
        Me.btnForce.tbText = "强力修复"
        Me.btnForce.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnForce.tbTextColor = System.Drawing.Color.White
        Me.btnForce.tbTextColorDisable = System.Drawing.Color.White
        Me.btnForce.tbTextColorDown = System.Drawing.Color.White
        Me.btnForce.tbTextColorHover = System.Drawing.Color.White
        Me.btnForce.tbTextMouseDownPlace = 0
        Me.btnForce.tbToolTip = ""
        Me.btnForce.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnForce.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnForce.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnForce.Visible = False
        Me.btnForce.VisibleEx = True
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblMessage.AutoSize = True
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.Location = New System.Drawing.Point(99, 154)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(233, 84)
        Me.lblMessage.TabIndex = 45
        Me.lblMessage.Text = "授权服务启动失败，您可以尝试以下操作：" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "1、再次重试授权； " & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "2、重启设备后再授权；" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "3、重装最新版的iTunes。"
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnSubmit
        '
        Me.btnSubmit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSubmit.BackColor = System.Drawing.Color.Transparent
        Me.btnSubmit.BindingForm = Nothing
        Me.btnSubmit.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSubmit.Location = New System.Drawing.Point(264, 261)
        Me.btnSubmit.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnSubmit.Name = "btnSubmit"
        Me.btnSubmit.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSubmit.Selectable = True
        Me.btnSubmit.Size = New System.Drawing.Size(70, 25)
        Me.btnSubmit.TabIndex = 46
        Me.btnSubmit.tbAdriftIconWhenHover = False
        Me.btnSubmit.tbAutoSize = False
        Me.btnSubmit.tbAutoSizeEx = True
        Me.btnSubmit.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnSubmit.tbBackgroundImageState = ImageState.FourState
        Me.btnSubmit.tbBadgeNumber = 0
        Me.btnSubmit.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSubmit.tbEndEllipsis = False
        Me.btnSubmit.tbIconHoldPlace = True
        Me.btnSubmit.tbIconImage = Nothing
        Me.btnSubmit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSubmit.tbIconImageState = ImageState.OneState
        Me.btnSubmit.tbIconMore = False
        Me.btnSubmit.tbIconMouseDown = Nothing
        Me.btnSubmit.tbIconMouseHover = Nothing
        Me.btnSubmit.tbIconMouseLeave = Nothing
        Me.btnSubmit.tbIconPlaceText = 2
        Me.btnSubmit.tbIconReadOnly = Nothing
        Me.btnSubmit.tbImageMouseDown = Nothing
        Me.btnSubmit.tbImageMouseHover = Nothing
        Me.btnSubmit.tbImageMouseLeave = Nothing
        Me.btnSubmit.tbReadOnly = False
        Me.btnSubmit.tbReadOnlyText = False
        Me.btnSubmit.tbShadow = False
        Me.btnSubmit.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSubmit.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSubmit.tbShowNew = False
        Me.btnSubmit.tbShowToolTipOnButton = False
        Me.btnSubmit.tbSplit = "13,11,13,11"
        Me.btnSubmit.tbText = "错误反馈"
        Me.btnSubmit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSubmit.tbTextColor = System.Drawing.Color.Black
        Me.btnSubmit.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnSubmit.tbTextColorDown = System.Drawing.Color.Black
        Me.btnSubmit.tbTextColorHover = System.Drawing.Color.Black
        Me.btnSubmit.tbTextMouseDownPlace = 0
        Me.btnSubmit.tbToolTip = ""
        Me.btnSubmit.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSubmit.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSubmit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnSubmit.Visible = False
        Me.btnSubmit.VisibleEx = True
        '
        'btnHelpWeb
        '
        Me.btnHelpWeb.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnHelpWeb.BackColor = System.Drawing.Color.Transparent
        Me.btnHelpWeb.BindingForm = Nothing
        Me.btnHelpWeb.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnHelpWeb.Location = New System.Drawing.Point(106, 267)
        Me.btnHelpWeb.Name = "btnHelpWeb"
        Me.btnHelpWeb.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnHelpWeb.Selectable = True
        Me.btnHelpWeb.Size = New System.Drawing.Size(12, 12)
        Me.btnHelpWeb.TabIndex = 107
        Me.btnHelpWeb.tbAdriftIconWhenHover = False
        Me.btnHelpWeb.tbAutoSize = True
        Me.btnHelpWeb.tbAutoSizeEx = False
        Me.btnHelpWeb.tbBackgroundImage = My.Resources.btn_3_goto
        Me.btnHelpWeb.tbBackgroundImageState = ImageState.ThreeState
        Me.btnHelpWeb.tbBadgeNumber = 0
        Me.btnHelpWeb.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnHelpWeb.tbEndEllipsis = False
        Me.btnHelpWeb.tbIconHoldPlace = True
        Me.btnHelpWeb.tbIconImage = Nothing
        Me.btnHelpWeb.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnHelpWeb.tbIconImageState = ImageState.OneState
        Me.btnHelpWeb.tbIconMore = False
        Me.btnHelpWeb.tbIconMouseDown = Nothing
        Me.btnHelpWeb.tbIconMouseHover = Nothing
        Me.btnHelpWeb.tbIconMouseLeave = Nothing
        Me.btnHelpWeb.tbIconPlaceText = 2
        Me.btnHelpWeb.tbIconReadOnly = Nothing
        Me.btnHelpWeb.tbImageMouseDown = Nothing
        Me.btnHelpWeb.tbImageMouseHover = Nothing
        Me.btnHelpWeb.tbImageMouseLeave = Nothing
        Me.btnHelpWeb.tbReadOnly = False
        Me.btnHelpWeb.tbReadOnlyText = False
        Me.btnHelpWeb.tbShadow = False
        Me.btnHelpWeb.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnHelpWeb.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnHelpWeb.tbShowNew = False
        Me.btnHelpWeb.tbShowToolTipOnButton = False
        Me.btnHelpWeb.tbSplit = "3,3,3,3"
        Me.btnHelpWeb.tbText = ""
        Me.btnHelpWeb.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnHelpWeb.tbTextColor = System.Drawing.Color.White
        Me.btnHelpWeb.tbTextColorDisable = System.Drawing.Color.White
        Me.btnHelpWeb.tbTextColorDown = System.Drawing.Color.White
        Me.btnHelpWeb.tbTextColorHover = System.Drawing.Color.White
        Me.btnHelpWeb.tbTextMouseDownPlace = 0
        Me.btnHelpWeb.tbToolTip = ""
        Me.btnHelpWeb.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnHelpWeb.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnHelpWeb.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnHelpWeb.VisibleEx = True
        '
        'lblHelpWeb
        '
        Me.lblHelpWeb.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblHelpWeb.AutoSize = True
        Me.lblHelpWeb.BackColor = System.Drawing.Color.Transparent
        Me.lblHelpWeb.DisabledLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblHelpWeb.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lblHelpWeb.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblHelpWeb.Location = New System.Drawing.Point(23, 267)
        Me.lblHelpWeb.Name = "lblHelpWeb"
        Me.lblHelpWeb.Size = New System.Drawing.Size(77, 12)
        Me.lblHelpWeb.TabIndex = 106
        Me.lblHelpWeb.TabStop = True
        Me.lblHelpWeb.Text = "授权问题详解"
        Me.lblHelpWeb.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tmrLoading
        '
        Me.tmrLoading.Interval = 500
        '
        'lblQA
        '
        Me.lblQA.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblQA.AutoSize = True
        Me.lblQA.BackColor = System.Drawing.Color.Transparent
        Me.lblQA.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblQA.DisabledLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblQA.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lblQA.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(104, Byte), Integer), CType(CType(170, Byte), Integer))
        Me.lblQA.Location = New System.Drawing.Point(12, 190)
        Me.lblQA.Name = "lblQA"
        Me.lblQA.Size = New System.Drawing.Size(65, 12)
        Me.lblQA.TabIndex = 108
        Me.lblQA.TabStop = True
        Me.lblQA.Text = "详细教程>>"
        Me.lblQA.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblQA.Visible = False
        '
        'frmTools
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(440, 300)
        Me.Controls.Add(Me.lblQA)
        Me.Controls.Add(Me.btnHelpWeb)
        Me.Controls.Add(Me.lblHelpWeb)
        Me.Controls.Add(Me.lblDiscription)
        Me.Controls.Add(Me.btnSubmit)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.lblTitle)
        Me.Controls.Add(Me.pbImage)
        Me.Controls.Add(Me.btnForce)
        Me.Controls.Add(Me.btnStart)
        Me.Controls.Add(Me.btnClose)
        Me.MinimumSize = New System.Drawing.Size(10, 85)
        Me.Name = "frmTools"
        Me.tbGuiBackground = My.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,31,5,54"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "正版授权"
        Me.Controls.SetChildIndex(Me.btnClose, 0)
        Me.Controls.SetChildIndex(Me.btnStart, 0)
        Me.Controls.SetChildIndex(Me.btnForce, 0)
        Me.Controls.SetChildIndex(Me.pbImage, 0)
        Me.Controls.SetChildIndex(Me.lblTitle, 0)
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.btnSubmit, 0)
        Me.Controls.SetChildIndex(Me.lblDiscription, 0)
        Me.Controls.SetChildIndex(Me.lblHelpWeb, 0)
        Me.Controls.SetChildIndex(Me.btnHelpWeb, 0)
        Me.Controls.SetChildIndex(Me.lblQA, 0)
        CType(Me.pbImage, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnClose As tbButton
    Friend WithEvents lblDiscription As tbLabel
    Friend WithEvents btnStart As tbButton
    Friend WithEvents pbImage As System.Windows.Forms.PictureBox
    Friend WithEvents lblTitle As tbLabel
    Friend WithEvents btnForce As tbButton
    Friend WithEvents lblMessage As System.Windows.Forms.Label
    Friend WithEvents btnSubmit As tbButton
    Friend WithEvents btnHelpWeb As tbButton
    Friend WithEvents lblHelpWeb As System.Windows.Forms.LinkLabel
    Friend WithEvents tmrLoading As System.Windows.Forms.Timer
    Friend WithEvents lblQA As System.Windows.Forms.LinkLabel
End Class
