﻿Imports System.ComponentModel
Imports System.IO

Public Class RepairSpringboard
    Inherits tbBackgroundWorker
    Private Language As LanguageInterface
    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice, ByVal item As tbButton)
        MyBase.New(app, device, item)
        Me.Language = app.Language
    End Sub

    Private Function CheckProgramExists(ByVal fileOnPhone As String) As String

        Dim blnReturn As Boolean = True     '默认文件中包含的程序都存在！！！
        Dim strReturn As String = ""

        Dim fileOnPC As String = Path.Combine(Folder.TempFolder, System.Guid.NewGuid.ToString("N"))
        Dim programOnPhone As String = ""

        If fileOnPhone.EndsWith(".plist", StringComparison.OrdinalIgnoreCase) AndAlso _
           Me.mDevice.DownFromPhone(fileOnPhone, fileOnPC) Then

            Dim fileContent As String = ""
            Using objReader As New System.IO.StreamReader(fileOnPC)
                fileContent = objReader.ReadToEnd
            End Using

            programOnPhone = Utility.SearchXmlByKey(fileContent, "Program")

            If programOnPhone.Length = 0 Then
                programOnPhone = Utility.SearchXmlByKey(fileContent, "ProgramArguments")
            End If

            If programOnPhone.Length = 0 Then
                blnReturn = True
            Else
                blnReturn = Me.mDevice.Exists(programOnPhone)
            End If

        End If

        If System.IO.File.Exists(fileOnPC) Then
            System.IO.File.Delete(fileOnPC)
        End If

        If blnReturn = False Then
            strReturn = programOnPhone
        End If

        Return strReturn
    End Function

    Private Function CheckDylibExists(ByVal fileOnPhone As String) As String()
        Dim aryReturn As List(Of String) = New List(Of String)

        Dim fileOnPC As String = Path.Combine(Folder.TempFolder, System.Guid.NewGuid.ToString("N"))
        Dim dylibOnPhone As String = ""

        If fileOnPhone.EndsWith(".plist", StringComparison.OrdinalIgnoreCase) AndAlso _
           Me.mDevice.DownFromPhone(fileOnPhone, fileOnPC) Then

            Dim fileContent As String = ""
            Using objReader As New System.IO.StreamReader(fileOnPC)
                fileContent = objReader.ReadToEnd
            End Using

            dylibOnPhone = Utility.SearchXmlByKey(fileContent, "DYLD_INSERT_LIBRARIES")

            If dylibOnPhone.Length > 0 Then
                For Each dylib As String In dylibOnPhone.Split(":")
                    If Me.mDevice.Exists(dylib) = False Then
                        aryReturn.Add(dylib)
                    End If
                Next
            End If

            Try
                System.IO.File.Delete(fileOnPC)
            Catch ex As Exception

            End Try
        End If

        If System.IO.File.Exists(fileOnPC) Then
            Try
                System.IO.File.Delete(fileOnPC)
            Catch ex As Exception

            End Try
        End If

        Return aryReturn.ToArray
    End Function

    Private Function RepairDylib(ByVal fileOnPhone As String, ByVal dylibCollection As Collection) As Boolean
        Dim blnReturn As Boolean = False

        Dim fileOnPC As String = Path.Combine(Folder.TempFolder, System.Guid.NewGuid.ToString("N"))
        Dim dylibOnPhone As String = ""

        If Me.mDevice IsNot Nothing AndAlso _
           Me.mDevice.IsConnected AndAlso _
           Me.mDevice.DownFromPhone(fileOnPhone, fileOnPC) Then

            Dim fileContent As String = ""
            Using objReader As New System.IO.StreamReader(fileOnPC)
                fileContent = objReader.ReadToEnd
            End Using

            dylibOnPhone = Utility.SearchXmlByKey(fileContent, "DYLD_INSERT_LIBRARIES")

            If dylibOnPhone.Length > 0 Then
                Dim dylibUsefull As String = ""

                For Each dylib As String In dylibOnPhone.Split(":")
                    If Me.mDevice.Exists(dylib) Then
                        dylibUsefull &= dylib & ":"
                    End If
                Next

                dylibUsefull = dylibUsefull.TrimEnd(":")
                fileContent = fileContent.Replace(dylibOnPhone, dylibUsefull)

                Using objWriter As New System.IO.StreamWriter(fileOnPC, False, System.Text.Encoding.UTF8)
                    objWriter.Write(fileContent)
                End Using

                blnReturn = Me.mDevice.CopyToPhone(fileOnPC, fileOnPhone)
            End If

        End If

        If System.IO.File.Exists(fileOnPC) Then
            System.IO.File.Delete(fileOnPC)
        End If

        Return blnReturn
    End Function

    Protected Overrides Sub OnDoWork(ByVal e As System.ComponentModel.DoWorkEventArgs)
        MyBase.OnDoWork(e)
       
        Try
            Dim fileOnPhone As String = ""
            Dim pathOnPhone As String = "/System/Library/LaunchDaemons/"
            Dim errfileCollection As New Collection
            Dim errDylibInSpringBoard As New Collection
            Dim errDylibInCommCenter As New Collection
            Dim blnReboot As Boolean = False

            Try
                '1. 检查 /System/Library/LaunchDaemons 下所有的启动文件
                '--------------------------------------------------------------------------------------------------------------------------
                Dim aryFiles() As String = Me.mDevice.GetFiles(pathOnPhone)
                Dim intIndex As Integer = 0
                Dim intTotal As Integer = aryFiles.Length + 2

                For Each fileOnPhone In aryFiles
                    intIndex += 1
                    Dim blnNormal As Boolean = True
                    If fileOnPhone.ToLower.StartsWith("com.apple.") OrElse Me.CheckProgramExists(pathOnPhone & fileOnPhone).Length = 0 Then
                        blnNormal = True
                    Else
                        errfileCollection.Add(fileOnPhone)
                        blnNormal = False
                    End If


                    '"正在检查 {0} {1}"  ,"正常"  ,"异常"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(5, String.Format(Me.Language.GetString("Tools.Message.Checking"), fileOnPhone, IIf(blnNormal, Me.Language.GetString("Tools.Label.Normal"), Me.Language.GetString("Tools.Label.Exception")))))
                Next

                '2. 正在检测 SpringBoard
                '--------------------------------------------------------------------------------------------------------------------------
                fileOnPhone = "/System/Library/LaunchDaemons/com.apple.SpringBoard.plist"
                intIndex += 1

                '"正在检查 {0} {1}"
                Me.OnProgressChanged(New ProgressChangedEventArgs(10, String.Format(Me.Language.GetString("Tools.Message.Checking"), "SpringBoard ...", "")))
                Dim aryDylib() As String = Me.CheckDylibExists(fileOnPhone)

                For Each dylibOnPhone As String In aryDylib
                    errDylibInSpringBoard.Add(dylibOnPhone)

                    '"异常"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(10, dylibOnPhone & " " & Me.Language.GetString("Tools.Label.Exception")))
                Next

                If aryDylib.Length = 0 Then
                    '"正常"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(15, "SpringBoard ..." & Me.Language.GetString("Tools.Label.Normal")))
                End If

                '3. 正在检测 CommCenter
                '--------------------------------------------------------------------------------------------------------------------------
                fileOnPhone = "/System/Library/LaunchDaemons/com.apple.CommCenter.plist"

                intIndex += 1


                '"正在检查 {0} {1}"
                Me.OnProgressChanged(New ProgressChangedEventArgs(20, String.Format(Me.Language.GetString("Tools.Message.Checking"), "CommCenter ...", "")))

                aryDylib = Me.CheckDylibExists(fileOnPhone)

                For Each dylibOnPhone As String In aryDylib
                    errDylibInCommCenter.Add(dylibOnPhone)


                    '"异常"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(25, dylibOnPhone & " " & Me.Language.GetString("Tools.Label.Exception")))
                Next

                If aryDylib.Length = 0 Then
                    '"正常"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(30, "CommCenter ..." & Me.Language.GetString("Tools.Label.Normal")))
                End If

                '4.清除第三方软件 MobileSubstrate 的所有附加启动项
                '--------------------------------------------------------------------------------------------------------------------------
                pathOnPhone = "/Library/MobileSubstrate/DynamicLibraries"

                Dim blnThirdParty As Boolean = False
                If blnThirdParty AndAlso _
                   mDevice IsNot Nothing AndAlso _
                   mDevice.IsConnected AndAlso _
                   mDevice.Exists(pathOnPhone) Then

                    Dim aryThirdFiles() As String = Me.mDevice.GetFiles(pathOnPhone)


                    '"正在检查 {0} {1}"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(35, String.Format(Me.Language.GetString("Tools.Message.Checking"), "MobileSubstrate ...", "")))

                    intTotal = aryThirdFiles.Length + 2
                    intIndex = 0

                    If aryThirdFiles IsNot Nothing AndAlso _
                       aryThirdFiles.Length > 0 Then
                        '"发现 MobileSubstrate 存在的附加启动项，是否清除？"
                        If tbMessageBox.Show(String.Format(Me.Language.GetString("Tools.Message.FindError"), "MobileSubstrate"), _
                                       Me.Language.GetString("Common.Info"), _
                                       MessageBoxButtons.YesNo, _
                                       MessageBoxIcon.Warning, _
                                       MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then

                            Dim blnDelSuccess As Boolean = False
                            For Each fileOnPhone In aryThirdFiles

                                intIndex += 1
                                Me.mDevice.DeleteFile(pathOnPhone.TrimEnd("/") & "/" & fileOnPhone)

                                If Me.mDevice.Exists(pathOnPhone.TrimEnd("/") & "/" & fileOnPhone) = False Then
                                    blnDelSuccess = True

                                    '"MobileSubstrate {0} 删除成功"
                                    Me.OnProgressChanged(New ProgressChangedEventArgs(40, String.Format(Me.Language.GetString("Tools.Message.DeleteSucceed"), "MobileSubstrate", fileOnPhone)))
                                Else

                                    '"MobileSubstrate {0} 删除失败"
                                    Me.OnProgressChanged(New ProgressChangedEventArgs(40, String.Format(Me.Language.GetString("Tools.Message.DeleteFailure"), "MobileSubstrate", fileOnPhone)))
                                End If

                                System.Windows.Forms.Application.DoEvents()
                            Next

                            blnReboot = blnDelSuccess '删除成功把重启标志设为true
                        Else

                            '"放弃清除"  "MobileSubstrate ... 放弃清除"
                            Me.OnProgressChanged(New ProgressChangedEventArgs(40, String.Format(Me.Language.GetString("Tools.Message.CancelDelete"), "MobileSubstrate")))
                        End If
                    Else

                        '"正常"
                        Me.OnProgressChanged(New ProgressChangedEventArgs(40, String.Format(String.Format("MobileSubstrate ... {0}", Me.Language.GetString("Tools.Label.Normal")), fileOnPhone)))
                    End If

                End If

                '--------------------------------------------------------------------------------------------------------------------------
                '有错误的启动项！！！
                '--------------------------------------------------------------------------------------------------------------------------
                Dim errCount As Integer = errfileCollection.Count + errDylibInSpringBoard.Count + errDylibInCommCenter.Count
                '"共发现 {0} 项错误的启动项，是否立即进行清除？"
                If errCount > 0 AndAlso _
                   tbMessageBox.Show(String.Format(Me.Language.GetString("Tools.Message.FindErrorCount"), errCount), _
                                   Me.Language.GetString("Common.Info"), _
                                   MessageBoxButtons.YesNo, MessageBoxIcon.Question, _
                                   MessageBoxDefaultButton.Button2) = DialogResult.Yes Then


                    '"正在清除错误的启动项，请稍候 ..."
                    Me.OnProgressChanged(New ProgressChangedEventArgs(45, Me.Language.GetString("Tools.Message.ClearningErrorStart")))

                    intIndex = 0
                    intTotal = errCount

                    '1. 清除错误的启动文件

                    '----------------------------------------------------------------------------------------------------------------------
                    For Each fileOnPhone In errfileCollection
                        intIndex += 1

                        '"正在清除 {0} "
                        Me.OnProgressChanged(New ProgressChangedEventArgs(45, String.Format(Me.Language.GetString("Tools.Message.Deleteing"), fileOnPhone)))

                        Me.mDevice.DeleteFile(pathOnPhone & fileOnPhone)
                    Next

                    '2. 清除 SpringBoard 中错误的 dylib
                    '----------------------------------------------------------------------------------------------------------------------
                    intIndex += 1

                    '"正在修复 {0}"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(50, String.Format(Me.Language.GetString("Tools.Message.Repairing"), "SpringBoard")))

                    fileOnPhone = "/System/Library/LaunchDaemons/com.apple.SpringBoard.plist"
                    Me.RepairDylib(fileOnPhone, errDylibInSpringBoard)

                    '3. 清除 CommCenter 中错误的 dylib
                    '----------------------------------------------------------------------------------------------------------------------
                    intIndex += errDylibInSpringBoard.Count


                    '"正在修复 {0}"
                    Me.OnProgressChanged(New ProgressChangedEventArgs(50, String.Format(Me.Language.GetString("Tools.Message.Repairing"), "CommCenter")))

                    fileOnPhone = "/System/Library/LaunchDaemons/com.apple.CommCenter.plist"
                    Me.RepairDylib(fileOnPhone, errDylibInCommCenter)

                    blnReboot = True    '清除成功把重启标志设为true
                End If

            Catch ex As Exception

                '"修复失败"
                Me.OnProgressChanged(New ProgressChangedEventArgs(100, Me.Language.GetString("Tools.Message.RepairFailure")))
                Return
            End Try

            '清理完毕！！！

            '----------------------------------------------------------------------------------------------------------------------

            '"修复成功，请长按开机键及home键，关机后再启动设备"
            Me.OnProgressChanged(New ProgressChangedEventArgs(100, Me.Language.GetString("Tools.Message.RepairFinish")))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RepairSpringboard_OnDoWork")
        End Try
    End Sub

End Class