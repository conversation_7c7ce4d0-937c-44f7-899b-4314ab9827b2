﻿Public Class AppRubbish
    Private mTempFileCount As Integer
    Private mTempFileSize As Int64
    Private mCacheFileCount As Integer
    Private mCacheFileSize As Int64
    Private mCookieFileCount As Integer
    Private mCookieFileSize As Int64

    Public Property TempFileCount() As Integer
        Get
            Return mTempFileCount
        End Get
        Set(ByVal value As Integer)
            mTempFileCount = value
        End Set
    End Property

    Public Property TempFileSize() As Int64
        Get
            Return mTempFileSize
        End Get
        Set(ByVal value As Int64)
            mTempFileSize = value
        End Set
    End Property

    Public Property CacheFileCount() As Integer
        Get
            Return mCacheFileCount
        End Get
        Set(ByVal value As Integer)
            mCacheFileCount = value
        End Set
    End Property

    Public Property CacheFileSize() As Int64
        Get
            Return Me.mCacheFileSize
        End Get
        Set(ByVal value As Int64)
            Me.mCacheFileSize = value
        End Set
    End Property

    Public Property CookieFileCount() As Integer
        Get
            Return mCookieFileCount
        End Get
        Set(ByVal value As Integer)
            mCookieFileCount = value
        End Set
    End Property

    Public Property CookieFileSize() As Int64
        Get
            Return Me.mCookieFileSize
        End Get
        Set(ByVal value As Int64)
            Me.mCookieFileSize = value
        End Set
    End Property
End Class
