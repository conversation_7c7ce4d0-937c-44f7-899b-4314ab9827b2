﻿Imports System.Threading
Public Class frmBatteryManager

    Private mThrLoadRepaorApps As Thread = Nothing
    Private mTimerLoadBattery As New System.Windows.Forms.Timer

    '充电完成 
    Private mFullyCharged As Boolean = False
    Private mInfoDevice As New tbDeviceInfo

    Private mFontTitle As Font = Nothing
    Private mFontNumbers As Font = Nothing
    Private mFontDate As Font = Nothing

    Private mCurrentElectricity As Integer = 0
    Private mActualCapacity As Integer = 0
    Private mIntCapacityProgress As Integer = 0
    Private mCurrentCapacityProgress As Integer = 0
    Private mIntDoLoadBattery As Single = 0.0F

    Private mIntProgress As Single = 0.0F

    '涓流充电
    Private mIsTrickleCharge As Boolean = False '是否完成涓流充电
    Private mTrickleChargeTime As Integer = 30 '涓流充电时间 30分钟
    Private mIsStratTrickleCharge As Boolean = False '是否开始涓流充电
    Private mBatteryEfficiency As String = ""   '电池寿命
#Region "----初始化、窗体闭关----"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice, ByVal infoDevice As tbDeviceInfo, ByVal batteryEffieiency As String)
        MyBase.New(application, device)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.mDelayLoad = True

        Me.Language = application.Language
        Me.mApplication = application
        Me.mDevice = device
        Me.mInfoDevice = infoDevice
        Me.FunctionMappingKey = FunctionKey.None
        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False

        Me.Icon = My.Resources.iTong
        Me.FilletRadius = 5
        Me.mBatteryEfficiency = batteryEffieiency
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.CanResize = False

        Me.mFontTitle = Common.CreateFont("微软雅黑", 16.0F, FontStyle.Bold)
        Me.mFontNumbers = Common.CreateFont("微软雅黑", 22.0F, FontStyle.Bold)
        Me.mFontDate = Common.CreateFont("微软雅黑", 12.0F, FontStyle.Regular)

        AddHandler DeviceShellHelper.DevriceShellEventHandler, AddressOf OnDevriceShell

        '赋初始值
        Me.SetText(Me.lblChargeRecordCountEx, True)    '历史循环充电次数
        Me.SetText(Me.lblActualCapacityEx, True)          '实际容量
        Me.SetText(Me.lblDesignCapacityEx, True)          '设计容量
        Me.SetText(Me.lblVmainEx, True)                            '当前电压
        Me.SetText(Me.lblStartVmainEx, True)                  '开机电压
        Me.SetText(Me.lblEfficiencyEx, True)                  '电池效率
        Me.SetText(Me.lblTemperatureEx, True)                '电池温度
        Me.SetText(Me.lblElectricityEx, True)                '电池电流
        Me.SetText(Me.lblCompanyEx, True)                        '电池厂商
        Me.SetText(Me.lblSerialNumberEx, True)              '电池序列号

        If Me.mDevice.IsIPad Then
            Me.pbDevice.Image = My.Resources.battery_device_ipad_default
        Else
            Me.pbDevice.Image = My.Resources.battery_device_iphone_default
        End If

        'Me.SetBatteryState(BatteryState.Unknown)

        'Me.SetBatteryTime("", "", False, True, True)
        'Me.mCurrentCapacityProgress = Me.mDevice.BatteryCurrentCapacity()
        'Me.LoadBatteryInfo()
        'Me.LoadPicture()
        'Me.LoadBattreyProgress()
        'Me.Animation()

        RemoveHandler Me.mTimerLoadBattery.Tick, AddressOf mTimerLoadBattery_Tick
        AddHandler Me.mTimerLoadBattery.Tick, AddressOf mTimerLoadBattery_Tick
        Me.mTimerLoadBattery.Interval = 0.03 * 1000
        Me.mTimerLoadBattery.Start()

        Me.btnContinueCharge.tbIconImageState = ImageState.OneState
        Me.btnContinueCharge.tbIconImage = My.Resources.btn_continuecharge
        Me.btnContinueCharge.tbIconReadOnly = My.Resources.btn_continuecharge_readonly
        Me.btnContinueCharge.tbIconImageAlign = ContentAlignment.TopCenter

        Me.btnQuickCharge.tbIconImageState = ImageState.OneState
        Me.btnQuickCharge.tbIconImage = My.Resources.btn_quickcharge
        Me.btnQuickCharge.tbIconReadOnly = My.Resources.btn_quickcharge_readonly
        Me.btnQuickCharge.tbIconImageAlign = ContentAlignment.TopCenter

        Me.btnTrickleCharge.tbIconImageState = ImageState.OneState
        Me.btnTrickleCharge.tbIconImage = My.Resources.btn_tricklecharge
        Me.btnTrickleCharge.tbIconReadOnly = My.Resources.btn_tricklecharge_readonly
        Me.btnTrickleCharge.tbIconImageAlign = ContentAlignment.TopCenter

        tbBatteryProgress.BackColor = Color.Black

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("Tools.Button.BatteryManager")                                             '省电优化
        Me.lblChargeRecord.Text = Me.Language.GetString("Tools.Label.BatteryManager.ChargeRecord")              '电池充电记录
        Me.lblChargeReport.Text = Me.Language.GetString("Tools.Label.BatteryManager.ChargeReport")              '电池体检报告
        Me.btnQuickCharge.Text = Me.Language.GetString("Tools.Button.BatteryManager.QuickCharge")               '快速充电
        Me.btnContinueCharge.Text = Me.Language.GetString("Tools.Button.BatteryManager.ContinueCharge")         '连续充电
        Me.btnTrickleCharge.Text = Me.Language.GetString("Tools.Button.BatteryManager.TrickleCharge")           '涓流充电
        Me.lblHint.Text = Me.Language.GetString("Tools.Label.BatteryManager.Hint")                              '定期进行完全循环充电，有利于锂电池的保养，建议您每周使用同步助手循环充电一次，有效延长锂电池的使用寿命。

        Me.lblChargeRecordCount.Text = Me.Language.GetString("Tools.Label.BatteryManager.ChargeRecordCount")   '历史循环充电次数
        Me.lblActualCapacity.Text = Me.Language.GetString("Tools.Label.BatteryManager.ActualCapacity")         '实际容量
        Me.lblDesignCapacity.Text = Me.Language.GetString("Tools.Label.BatteryManager.DesignCapacity")         '设计容量
        Me.lblVmain.Text = Me.Language.GetString("Tools.Label.BatteryManager.Vmain")                           '当前电压
        Me.lblStartVmain.Text = Me.Language.GetString("Tools.Label.BatteryManager.StartVmain")                 '开机电压
        Me.lblEfficiency.Text = Me.Language.GetString("Tools.Label.BatteryManager.Efficiency")                 '电池效率
        Me.lblTemperature.Text = Me.Language.GetString("Tools.Label.BatteryManager.Temperature")               '电池温度
        Me.lblElectricity.Text = Me.Language.GetString("Tools.Label.BatteryManager.Electricity")               '电池电流
        Me.lblCompany.Text = Me.Language.GetString("Tools.Label.BatteryManager.Company")                       '电池厂商
        Me.lblSerialNumber.Text = Me.Language.GetString("Tools.Label.BatteryManager.SerialNumber")             '电池序列号
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        Try
            If Me.mTimerLoadBattery.Enabled Then
                Me.mTimerLoadBattery.Enabled = False
                Me.mTimerLoadBattery.Dispose()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "BeforeFormClose")
        End Try
        
        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As CoreFoundation.IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)
        Try
            If CType(Me.mApplication, MainForm).Left < -2000 Then
                Me.Size = New Size(870, 610)
                Me.Location = New Point((Screen.PrimaryScreen.Bounds.Width - Me.Width) / 2, (Screen.PrimaryScreen.Bounds.Height - Me.Height) / 2)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmBatteryManager_OnShown")
        End Try
        ActionCollectHelper.ShowDeviceHomePage(Me.mDevice, ModelKey.SummaryBattery)
    End Sub

    Protected Overrides Sub OnFormClosing(ByVal e As System.Windows.Forms.FormClosingEventArgs)
        RemoveHandler DeviceShellHelper.DevriceShellEventHandler, AddressOf OnDevriceShell

        Try
            If Me.mThrLoadRepaorApps IsNot Nothing AndAlso Me.mThrLoadRepaorApps.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadRepaorApps.Abort()
            End If
        Catch ex As Exception
        End Try

        MyBase.OnFormClosing(e)
    End Sub

    Protected Overrides Sub FormDelayShow()
        MyBase.FormDelayShow()
        '  Me.SetBatteryState(BatteryState.Unknown)

        Me.SetBatteryTime("", "", False, True, True)

        Me.LoadBatteryInfo()
        Me.LoadPicture()
        Me.LoadBattreyProgress()
        Me.Animation()

    End Sub

#End Region

#Region "---- 加载数据 ----"

    Private mlngPassTime As Double = 0

    Private Sub mTimerLoadBattery_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            '计时器的响应间隔是0.03秒,如有改动需要变更下面自增量
            mlngPassTime += 1

            Dim iCapacityProgress As Integer = 90

            '充电进度 界面展示
            If Me.mCurrentCapacityProgress > 0 AndAlso Me.mCurrentCapacityProgress < iCapacityProgress Then
                Animation()
            End If

            If mlngPassTime Mod 2000 = 0 Then
                '1分钟获取电池信息
                Me.LoadBattery()

                '1分钟涓流充电倒计时
                StratTrickleCharge()

            ElseIf Me.mCurrentCapacityProgress >= iCapacityProgress AndAlso mlngPassTime Mod 100 = 0 Then
                '3秒钟执行一次
                Animation()
            End If

        Catch ex As Exception
            Debug.Print(ex.ToString)
            Common.Log(ex.ToString)
        End Try
    End Sub

    Private Sub Animation()
        Dim isFull As Boolean = Me.mCurrentCapacityProgress > 0 AndAlso Me.mCurrentCapacityProgress = 100 AndAlso Me.mIntCapacityProgress = 100
        If Not isFull Then
            If Me.mIntCapacityProgress = 0 OrElse Me.mIntCapacityProgress >= 100 Then
                Me.mIntCapacityProgress = Me.mCurrentCapacityProgress
            ElseIf Me.mIntCapacityProgress < 100 Then
                Me.mIntCapacityProgress = Me.mIntCapacityProgress + 1
            End If
            Me.tbBatteryProgress.tbCurrentProgressValue = Me.mIntCapacityProgress
        End If
    End Sub

    Private Sub StratTrickleCharge()
        '开始涓流充电
        If Me.mIsStratTrickleCharge Then

            Me.mTrickleChargeTime = Me.mTrickleChargeTime - 1

            If Me.mTrickleChargeTime <= 0 Then
                mIsTrickleCharge = True
                Me.SetBatteryState(BatteryState.ChargeComplete)
                Me.SetBatteryTime("", Me.mTrickleChargeTime, False, True)
            Else
                Me.SetBatteryTime("", Me.mTrickleChargeTime, True, False)
            End If
        End If
    End Sub

    Private Sub mTimerLoadBattery_Tick1(ByVal sender As System.Object, ByVal e As System.EventArgs)

        '1分钟获取电池信息

        '1分钟涓流充电倒计时

        '0.03秒要刷新充电状态，90以后变成1秒刷新



        '间隔1分钟 刷新获取 电池信息
        Me.mIntDoLoadBattery = Me.mIntDoLoadBattery + 0.03
        If Me.mIntDoLoadBattery >= 60 Then
            Me.mIntDoLoadBattery = 0
            Me.LoadBattery()
        End If

        '开始涓流充电
        If Me.mIsStratTrickleCharge Then

            'Me.mStratTrickleChargeTime = Me.mStratTrickleChargeTime + 0.03
            'If Me.mStratTrickleChargeTime >= 60 Then
            '    Me.mTrickleChargeTime = Me.mTrickleChargeTime - 1
            '    Me.mStratTrickleChargeTime = 0
            '    Me.mStratTrickleChargeTime = False
            'End If

            If Me.mTrickleChargeTime <= 0 Then
                mIsTrickleCharge = True
                Me.SetBatteryState(BatteryState.ChargeComplete)
                Me.SetBatteryTime("", Me.mTrickleChargeTime, False, True)
            Else
                Me.SetBatteryTime("", Me.mTrickleChargeTime, True, False)
            End If
        End If

        '充电进度 界面展示
        If Me.mCurrentCapacityProgress >= 89 Then

        End If

        Dim isFull As Boolean = Me.mCurrentCapacityProgress > 0 AndAlso Me.mCurrentCapacityProgress = 100 AndAlso Me.mIntCapacityProgress = 100
        If Not isFull Then
            If Me.mIntCapacityProgress = 0 OrElse Me.mIntCapacityProgress >= 100 Then
                Me.mIntCapacityProgress = Me.mCurrentCapacityProgress
            ElseIf Me.mIntCapacityProgress < 100 Then
                Me.mIntCapacityProgress = Me.mIntCapacityProgress + 1
            End If
            Me.tbBatteryProgress.tbCurrentProgressValue = Me.mIntCapacityProgress
        End If
    End Sub

    Private Sub LoadBattery()
        Try
            If Me.mThrLoadRepaorApps IsNot Nothing AndAlso Me.mThrLoadRepaorApps.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadRepaorApps.Abort()
            End If
        Catch ex As Exception
        End Try

        Me.mThrLoadRepaorApps = New Thread(AddressOf DoLoadBattery)
        With Me.mThrLoadRepaorApps
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub DoLoadBattery()
        Try
            Me.LoadBatteryInfo()
            Me.LoadBattreyProgress()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadBattery")
        End Try
    End Sub

    Private Sub LoadBatteryInfo()
        Try
            Dim obj As Dictionary(Of Object, Object) = Nothing
            Dim intCount As Integer = 0

            While obj Is Nothing
                obj = Me.mDevice.GetIORegistryInfo()
                intCount += 1
                If obj IsNot Nothing Then
                    Exit While
                End If

                If intCount > 4 Then
                    Return
                End If
                Utility.WaitSeconds(3)
            End While

            Dim strSerialNumber As String = String.Empty
            Dim strTemperature As String = String.Empty
            Dim strChargeRecordCount As String = String.Empty
            Dim strStartVmain As String = String.Empty
            Dim strCompany As String = String.Empty
            Dim strVmain As String = String.Empty
            Dim strEfficiency As String = String.Empty
            Dim intDesignCapacity As Integer = 0

            For Each item As KeyValuePair(Of Object, Object) In obj
                If item.Key.ToString().ToLower() = "serial" Then
                    strSerialNumber = item.Value

                ElseIf item.Key.ToString().ToLower() = "temperature" Then
                    strTemperature = Format(Common.GetValue(item.Value, 0) * 0.01, "0.00")

                ElseIf item.Key.ToString().ToLower() = "cyclecount" Then
                    strChargeRecordCount = item.Value

                ElseIf item.Key.ToString().ToLower() = "bootvoltage" Then
                    strStartVmain = Format(Common.GetValue(item.Value, 0) * 0.001, "0.00")

                ElseIf item.Key.ToString().ToLower() = "manufacturer" Then
                    strCompany = item.Value

                ElseIf item.Key.ToString().ToLower() = "instantamperage" Then
                    Me.mCurrentElectricity = Common.GetValue(item.Value, 0)

                ElseIf item.Key.ToString().ToLower() = "fullycharged" Then
                    Me.mFullyCharged = Convert.ToBoolean(item.Value)

                ElseIf item.Key.ToString().ToLower() = "maxcapacity" Then
                    Me.mActualCapacity = item.Value

                ElseIf item.Key.ToString().ToLower() = "designcapacity" Then
                    intDesignCapacity = item.Value

                ElseIf item.Key.ToString().ToLower() = "voltage" Then
                    strVmain = Format(Common.GetValue(item.Value, 0) * 0.001, "0.00")

                End If

                If item.Key.ToString().ToLower() = "batterydata" Then
                    Dim dicBatteryData As Dictionary(Of Object, Object) = CType(item.Value, Dictionary(Of Object, Object))
                    For Each batteryItem As KeyValuePair(Of Object, Object) In dicBatteryData
                        If batteryItem.Key.ToString().ToLower() = "maxcapacity" Then
                            Me.mActualCapacity = batteryItem.Value

                        ElseIf batteryItem.Key.ToString().ToLower() = "designcapacity" Then
                            intDesignCapacity = batteryItem.Value

                        ElseIf batteryItem.Key.ToString().ToLower() = "voltage" Then
                            strVmain = Format(Common.GetValue(batteryItem.Value, 0) * 0.001, "0.00")
                        End If
                    Next
                End If


            Next
            If Me.mActualCapacity > 0 AndAlso intDesignCapacity > 0 Then
                strEfficiency = Format(mActualCapacity / intDesignCapacity, "0.00") * 100
            End If

            strEfficiency = Me.mBatteryEfficiency
            Me.SetText(Me.lblVmainEx, False, strVmain, "V")
            Me.SetText(Me.lblSerialNumberEx, False, strSerialNumber, "")
            Me.SetText(Me.lblTemperatureEx, False, strTemperature, "°C")
            Me.SetText(Me.lblChargeRecordCountEx, False, strChargeRecordCount, Me.Language.GetString("Tools.Button.BatteryManager.CycleCount"))
            Me.SetText(Me.lblStartVmainEx, False, strStartVmain, "V")
            Me.SetText(Me.lblCompanyEx, False, strCompany, "")
            Me.SetText(Me.lblElectricityEx, False, Me.mCurrentElectricity, "mA")
            Me.SetText(Me.lblActualCapacityEx, False, IIf(Me.mActualCapacity > 0, Me.mActualCapacity, ""), "mAh")
            Me.SetText(Me.lblDesignCapacityEx, False, IIf(intDesignCapacity > 0, intDesignCapacity, ""), "mAh")
            Me.SetText(Me.lblEfficiencyEx, False, strEfficiency, "%")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadBatteryInfo")
        End Try

    End Sub

    Private Sub LoadBattreyProgress()
        Try

            Me.mCurrentCapacityProgress = Me.mDevice.BatteryCurrentCapacity()

            If Me.mCurrentCapacityProgress < 0 Then
                Me.tbBatteryProgress.tbProgressValue = Me.Language.GetString("Welcome.Label.Loading")
                Me.SetBatteryState(BatteryState.Unknown)
                Return
            End If

            Me.tbBatteryProgress.tbProgressValue = Me.mCurrentCapacityProgress & "%"

            '快速充电：0%-80%    ‘
            '连续充电：81%-99%   
            '涓流充电：100%且mFullyCharged（是否完成充电） =true  mIsTrickleCharge(是否开始涓流充电)=true    
            If Me.mCurrentCapacityProgress >= 0 AndAlso Me.mCurrentCapacityProgress <= 80 Then
                Me.SetBatteryState(BatteryState.QuickCharge)

            ElseIf Me.mCurrentCapacityProgress >= 81 AndAlso Me.mCurrentCapacityProgress <= 99 Then
                Me.SetBatteryState(BatteryState.ContinueCharge)

            ElseIf Me.mCurrentCapacityProgress = 100 AndAlso Not Me.mFullyCharged AndAlso Not Me.mIsTrickleCharge Then
                Me.SetBatteryState(BatteryState.TrickleCharge)
                If Not mIsStratTrickleCharge Then
                    Me.SetBatteryTime("", "30", True, False)
                End If
                mIsStratTrickleCharge = True
                Return
            Else
                SetBatteryState(BatteryState.ChargeComplete)
                Me.SetBatteryTime("", "", False, True)
                Return
            End If

            '如果当前电流是负数则通过百分比去计算充电时间
            Dim intHour As Integer = 0
            Dim intMinute As Integer = 0
            Dim totalMinute As Integer = 0
            Dim doubleUnCharge As Integer = 0
            If mCurrentElectricity >= 0 Then
                '计算还有多少没充 获取充电需要时间
                doubleUnCharge = Me.mActualCapacity - (Me.mActualCapacity * Me.mCurrentCapacityProgress * 0.01)
                If doubleUnCharge <> 0 Then
                    totalMinute = (doubleUnCharge * 1.2 / mCurrentElectricity) * 60
                End If
            Else
                totalMinute = (100 - Me.mCurrentCapacityProgress) * 0.01 * 4 * 60

            End If

            If totalMinute < 0 Then
                intHour = 0
                totalMinute = 0
            ElseIf totalMinute < 60 Then
                intMinute = totalMinute
            Else
                intHour = totalMinute \ 60
                intMinute = IIf((totalMinute - intHour * 60) < 0, 0, (totalMinute - intHour * 60))
            End If
            Me.SetBatteryTime(intHour.ToString(), intMinute.ToString(), False, False)


        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadBattreyProgress")
        End Try
    End Sub

#Region "---- 私有方法 ----"

    Private Sub SetText(ByVal lblContent As Label, ByVal initValue As Boolean, Optional ByVal strValue As String = "", Optional ByVal strUnit As String = "")

        If initValue Then
            lblContent.Text = String.Format("{0}{1}", Me.Language.GetString("Welcome.Label.Loading"), "")
            Return
        End If

        If strValue.Length > 0 Then
            lblContent.Text = String.Format("{0}{1}", strValue, strUnit)
        Else
            lblContent.Text = String.Format("{0}{1}", Me.Language.GetString("Common.Label.Unknow"), "")

        End If
    End Sub

    Private Sub SetBatteryState(ByVal bState As BatteryState)

        Select Case bState
            Case BatteryState.Unknown
                tbBatteryProgress.tbChargedTitle = Me.Language.GetString("Welcome.Label.Loading")
                Me.btnQuickCharge.tbReadOnly = True
                Me.btnQuickCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)
                Me.btnContinueCharge.tbReadOnly = True
                Me.btnContinueCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)
                Me.btnTrickleCharge.tbReadOnly = True
                Me.btnTrickleCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)

            Case BatteryState.QuickCharge
                tbBatteryProgress.tbChargedTitle = Me.Language.GetString("Tools.Button.BatteryManager.QuickChargeing")
                Me.btnQuickCharge.tbReadOnly = False
                Me.btnQuickCharge.ForeColor = System.Drawing.Color.FromArgb(68, 68, 68)
                Me.btnContinueCharge.tbReadOnly = True
                Me.btnContinueCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)
                Me.btnTrickleCharge.tbReadOnly = True
                Me.btnTrickleCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)

            Case BatteryState.ContinueCharge
                tbBatteryProgress.tbChargedTitle = Me.Language.GetString("Tools.Button.BatteryManager.ContinueChargeing")
                Me.btnQuickCharge.tbReadOnly = False
                Me.btnQuickCharge.ForeColor = System.Drawing.Color.FromArgb(68, 68, 68)
                Me.btnContinueCharge.tbReadOnly = False
                Me.btnContinueCharge.ForeColor = System.Drawing.Color.FromArgb(68, 68, 68)
                Me.btnTrickleCharge.tbReadOnly = True
                Me.btnTrickleCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)

            Case BatteryState.TrickleCharge
                tbBatteryProgress.tbChargedTitle = Me.Language.GetString("Tools.Button.BatteryManager.TrickleChargeing")
                Me.btnQuickCharge.tbReadOnly = False
                Me.btnQuickCharge.ForeColor = System.Drawing.Color.FromArgb(68, 68, 68)
                Me.btnContinueCharge.tbReadOnly = False
                Me.btnContinueCharge.ForeColor = System.Drawing.Color.FromArgb(68, 68, 68)
                Me.btnTrickleCharge.tbReadOnly = False
                Me.btnTrickleCharge.ForeColor = System.Drawing.Color.FromArgb(68, 68, 68)

            Case BatteryState.ChargeComplete
                tbBatteryProgress.tbChargedTitle = Me.Language.GetString("Tools.Button.BatteryManager.ChargeCompleteed")
                Me.btnQuickCharge.tbReadOnly = True
                Me.btnQuickCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)
                Me.btnContinueCharge.tbReadOnly = True
                Me.btnContinueCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)
                Me.btnTrickleCharge.tbReadOnly = True
                Me.btnTrickleCharge.ForeColor = System.Drawing.Color.FromArgb(153, 153, 153)

        End Select

    End Sub

    Private Sub SetBatteryTime(ByVal strHour As String, ByVal strMinute As String, ByVal isTrickle As Boolean, ByVal isFullyCharged As Boolean, Optional ByVal initValue As Boolean = False)
        Me.lblBattreyProgress.ColorTextList.Clear()

        If initValue Then
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("Tools.Label.BatteryManager.PredictTime"), 0, System.Drawing.Color.FromArgb(85, 85, 85), mFontTitle, False)
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("Welcome.Label.Loading"), 1, System.Drawing.Color.FromArgb(85, 85, 85), mFontDate, False)
            Return
        End If

        If isFullyCharged Then
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("Tools.Button.BatteryManager.FullyCharged"), 0, System.Drawing.Color.FromArgb(85, 85, 85), mFontTitle, False)
            Return
        End If

        If isTrickle Then
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("Tools.Button.BatteryManager.TricklePredictTime"), 0, System.Drawing.Color.FromArgb(85, 85, 85), mFontTitle, False)
            Me.lblBattreyProgress.ColorTextList.AddItem(strMinute, 1, System.Drawing.Color.FromArgb(255, 131, 76), mFontNumbers, True)
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("Music.Label.Minute"), 2, System.Drawing.Color.FromArgb(85, 85, 85), mFontDate, False)

        Else
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("Tools.Label.BatteryManager.PredictTime"), 0, System.Drawing.Color.FromArgb(85, 85, 85), mFontTitle, False)
            Me.lblBattreyProgress.ColorTextList.AddItem(strHour, 1, System.Drawing.Color.FromArgb(255, 131, 76), mFontNumbers, True)
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("Music.Label.Hour"), 2, System.Drawing.Color.FromArgb(85, 85, 85), mFontDate, False)
            Me.lblBattreyProgress.ColorTextList.AddItem(strMinute, 3, System.Drawing.Color.FromArgb(255, 131, 76), mFontNumbers, True)
            Me.lblBattreyProgress.ColorTextList.AddItem(Me.Language.GetString("RingtoneMaker.Label.Minute"), 4, System.Drawing.Color.FromArgb(85, 85, 85), mFontDate, False)

        End If
    End Sub

#End Region

#End Region

#Region "---- 加载设备图片 ----"

    Private Sub LoadPicture()
        Dim imgDevice As Image = Nothing
        Dim strShellPath As String = DeviceShellHelper.GetInstance(Me.mDevice).ShellFilePath
        If String.IsNullOrEmpty(strShellPath) AndAlso File.Exists(strShellPath) = False Then
            DeviceShellHelper.GetInstance(Me.mDevice).Start2GetShell(Me.mDevice.ProductType)
        End If

        Dim imgShell As Image = Nothing
        If Me.mDevice.IsIPad Then
            imgShell = Utility.GetThumbnail(DeviceShellHelper.GetInstance(Me.mDevice).ImgShell, New Size(230, 330))
            If imgShell IsNot Nothing Then
                imgDevice = imgShell
            End If

        Else
            imgShell = Utility.GetThumbnail(DeviceShellHelper.GetInstance(Me.mDevice).ImgShell, New Size(160, 330))
            If imgShell IsNot Nothing Then
                imgDevice = imgShell
            End If

        End If

Do_Exit:
        If imgDevice IsNot Nothing Then
            Me.pbDevice.Image = imgDevice
        End If
    End Sub

    Private Sub OnDevriceShell(ByVal sender As Object, ByVal args As DeviceShellEventArgs)
        If args.mStrSN = Me.mDevice.SerialNumber Then
            Me.LoadPicture()
        End If
    End Sub

#End Region

End Class

Public Enum BatteryState
    Unknown = 0
    QuickCharge = 1
    ContinueCharge = 2
    TrickleCharge = 3
    ChargeComplete = 4
End Enum