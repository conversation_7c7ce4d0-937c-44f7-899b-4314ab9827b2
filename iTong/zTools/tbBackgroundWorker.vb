﻿Imports System.ComponentModel
Imports System.IO

Public Class tbBackgroundWorker
    Inherits System.ComponentModel.BackgroundWorker

    Protected mApplication As IApplication
    Protected mDevice As iPhoneDevice
    Protected mControl As tbButton

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice, ByVal item As tbButton)
        mApplication = app
        mDevice = device
        mControl = item

        Me.WorkerReportsProgress = True
        Me.WorkerSupportsCancellation = True
    End Sub

    Private Sub ShowInfo(ByVal sender As Object, ByVal e As System.ComponentModel.ProgressChangedEventArgs)
        Dim frm As MainForm = Me.mApplication
        If frm.InvokeRequired Then
            frm.Invoke(New ProgressChangedEventHandler(AddressOf ShowInfo), sender, e)
        Else
            frm.SetLabelText(FunctionKey.More, String.Format("{0} {1}", IIf(e.ProgressPercentage = 100, "", e.ProgressPercentage & "%"), e.UserState.ToString()))
        End If
    End Sub

    'Protected Overrides Sub OnProgressChanged(ByVal e As System.ComponentModel.ProgressChangedEventArgs)
    '    MyBase.OnProgressChanged(e)

    '    Me.ShowInfo(Me, e)
    'End Sub

    Protected Overrides Sub OnRunWorkerCompleted(ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs)
        MyBase.OnRunWorkerCompleted(e)

        Try
            If mControl IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                mControl.tbReadOnly = False
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "tbBackgroundWorker_OnRunWorkerCompleted")
        End Try
    End Sub

End Class
