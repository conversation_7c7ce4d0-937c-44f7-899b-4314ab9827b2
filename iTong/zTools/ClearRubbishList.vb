﻿Public Class ClearRubbishList
    Private mIcon As Image
    Private mCaptionText As String
    Private mLocatDataGridView As New Point(57, 46)
    Private mListItems As Dictionary(Of String, RubbishInfo)
    Private mIsAppend As Boolean = True
    Dim mCheckTriggle As Boolean = False
    Private mOffsetXList
    Private WithEvents dgvData As New tbDataGridViewListStyle
    Private mDictRowToSku As New Dictionary(Of tbDataGridViewRow, String)
    Private mDefaultHeight As Integer = 70

    Public Event CheckChange()

    Public Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.SetStyle(ControlStyles.UserPaint, True)
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)
        Me.SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        Me.SetStyle(ControlStyles.DoubleB<PERSON>er, True)
        Me.SetStyle(ControlStyles.Selectable, True)
        Me.SetStyle(ControlStyles.ContainerControl, True)
        Me.SetStyle(ControlStyles.ResizeRedraw, True)
        Me.chkIsSelect.ThreeState = False
        Me.AutoScroll = False

        mLocatDataGridView = New Point(mLocatDataGridView.X, mDefaultHeight + 4)
        Me.Height = mDefaultHeight
        Me.lblCaption.Font = New Font("宋体", 9, FontStyle.Bold)
        Me.InitialListControl()
    End Sub

    Public Property Icon()
        Get
            Return mIcon
        End Get
        Set(ByVal value)
            mIcon = value
            Me.picIcon.Image = mIcon
        End Set
    End Property

    Public Property CaptionText() As String
        Get
            Return Me.mCaptionText
        End Get
        Set(ByVal value As String)
            Me.mCaptionText = value
            Me.lblCaption.Text = value
        End Set
    End Property

    Public Property DescribText() As String
        Get
            Return Me.lblDescrib.Text
        End Get
        Set(ByVal value As String)
            Me.lblDescrib.Text = value
        End Set
    End Property

    Public Property DescribText2() As String
        Get
            Return Me.lblDescrib2.Text
        End Get
        Set(ByVal value As String)
            Me.lblDescrib2.Text = value
        End Set
    End Property

    Public Property DescribForeColor2() As Color
        Get
            Return Me.lblDescrib2.ForeColor
        End Get
        Set(ByVal value As Color)
            Me.lblDescrib2.ForeColor = value
        End Set
    End Property

    Public Property Items() As Dictionary(Of String, RubbishInfo)
        Get
            Return mListItems
        End Get
        Set(ByVal value As Dictionary(Of String, RubbishInfo))
            mListItems = value
            Me.mIsAppend = False
            UpdateCheckStatus()
            Me.dgvData.Sort(Me.dgvData.Columns(3), System.ComponentModel.ListSortDirection.Descending)

            SetHeight()

        End Set
    End Property

    Public Property FileCount() As String
        Get
            Return Me.lblFileCount.Text
        End Get
        Set(ByVal value As String)
            Me.lblFileCount.Text = value
        End Set
    End Property

    Public Property FileSize() As String
        Get
            Return Me.lblFileSize.Text
        End Get
        Set(ByVal value As String)
            Me.lblFileSize.Text = value
        End Set
    End Property

    Public Overloads Property BackgroundColor() As Color
        Get
            Return MyBase.BackColor
        End Get
        Set(ByVal value As Color)
            MyBase.BackColor = value
            dgvData.BackgroundColor = value
        End Set
    End Property

    Public Property Checked() As Boolean
        Get
            Return Me.chkIsSelect.Checked
        End Get
        Set(ByVal value As Boolean)
            Me.chkIsSelect.Checked = value
        End Set
    End Property

    Public Property IsAppend() As Boolean
        Get
            Return Me.mIsAppend
        End Get
        Set(ByVal value As Boolean)
            Me.mIsAppend = value
            Me.SetHeight()
            Dim parentLayout As FlowLayoutPanel = CType(Me.Parent, FlowLayoutPanel)
            'parentLayout.SuspendLayout()
            'parentLayout.Invalidate()
            'parentLayout.ResumeLayout()
            'parentLayout.AutoScroll = False
            'parentLayout.AutoScroll = True
            'parentLayout.FlowDirection = FlowDirection.LeftToRight
            'parentLayout.FlowDirection = FlowDirection.TopDown
        End Set
    End Property

    Public Sub UpdateCheckStatus()
        Me.SuspendLayout()
        mDictRowToSku.Clear()
        dgvData.Font = New Font("宋体", 9)
        Me.dgvData.Rows.Clear()
        Dim bIsAllUnchecked As Boolean = True
        Dim bIsAllChecked As Boolean = True
        If mListItems IsNot Nothing Then
            Me.dgvData.Height = Me.dgvData.RowTemplate.Height * Me.mListItems.Count
            For Each pair As KeyValuePair(Of String, RubbishInfo) In Me.mListItems
                Dim cellName As tbDataGridViewTextBoxCellEx
                Dim info As RubbishInfo = pair.Value
                Dim nameColText As String

                If info.IsSkipped AndAlso (Not String.IsNullOrEmpty(info.IgnoreReason)) Then
                    nameColText = String.Format("{0} ({1})", info.AppName, info.IgnoreReason)
                Else
                    nameColText = info.AppName
                End If

                Dim rowIndex As Integer = Me.dgvData.Rows.Add(info.IsChecked, nameColText, info.FileCount.ToString, Utility.FormatFileSize(info.FileSize))
                Dim row As DataGridViewRow = Me.dgvData.Rows(rowIndex)

                If info.IsChecked Then
                    bIsAllUnchecked = False
                Else
                    bIsAllChecked = False
                End If
                mDictRowToSku(row) = pair.Key
                cellName = row.Cells(1)
                cellName.tbIcon = info.Icon
                cellName.tbIconSize = New Size(25, 25)
            Next
        End If
        If Me.chkIsSelect.Checked Then
            If bIsAllChecked Then
                Me.chkIsSelect.CheckState = CheckState.Checked
            ElseIf bIsAllUnchecked Then
                Me.chkIsSelect.CheckState = CheckState.Unchecked
            Else
                Me.chkIsSelect.CheckState = CheckState.Indeterminate
            End If
        End If
       
        Me.ResumeLayout()
        Me.SetHeight()
    End Sub

    Public Sub ApplyCheckToData()
        For Each row As tbDataGridViewRow In Me.dgvData.Rows
            Dim strSku As String = Me.mDictRowToSku(row)
            Me.mListItems(strSku).IsChecked = row.Cells("colCheckbox").Value
        Next
    End Sub

    Private Sub SetHeight()
        Dim preHeight As Integer = Me.Height
        Me.SuspendLayout()
        If mIsAppend Then
            If (Me.mListItems IsNot Nothing) AndAlso (Me.mListItems.Count > 0) Then
                Me.Height = Me.dgvData.RowTemplate.Height * Me.mListItems.Count + Me.mDefaultHeight + 20 '- Me.dgvData.ColumnHeadersHeight
                Me.dgvData.Visible = True
            End If
            Me.picAppend.Image = My.Resources.tool_clean_detail_dropdown
            Me.picAppend.Invalidate()
        Else
            Me.Size = New Size(Me.Size.Width, mDefaultHeight)
            Me.dgvData.Visible = False
            Me.mIsAppend = False

            Me.picAppend.Image = My.Resources.tool_clean_detail
            Me.picAppend.Invalidate()
        End If
        Me.ResumeLayout()

        If preHeight <> Me.Height Then
            Dim parentLayout As FlowLayoutPanel = CType(Me.Parent, FlowLayoutPanel)
            parentLayout.SuspendLayout()
            parentLayout.Invalidate()
            parentLayout.ResumeLayout()


            parentLayout.FlowDirection = FlowDirection.LeftToRight
            parentLayout.FlowDirection = FlowDirection.TopDown
            parentLayout.ScrollControlIntoView(Me)
        End If

    End Sub

    Private Sub UppdateAppendStatus()
        If mIsAppend Then
            mIsAppend = False
        Else
            Me.mIsAppend = True
        End If
        SetHeight()
    End Sub

    Private Sub btnAppend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.DoubleClick

    End Sub

    Private Sub CheckJson()


    End Sub
    Private Sub InitialListControl()

        dgvData = New tbDataGridViewListStyle()
        Dim cellStyle As New DataGridViewCellStyle()

        'cellStyle.Font=
        With dgvData
            .SuspendLayout()
            'Dim cellStyle As New DataGridViewCellStyle()
            .Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
            cellStyle.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
            cellStyle.ForeColor = Color.Gray
            cellStyle.SelectionForeColor = Color.Gray

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 30
            .Columns.Clear()

            Dim columnType As Type = GetType(tbDataGridViewTextBoxColumnEx)

            Dim colCheckBox As tbDataGridViewCheckBoxColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 28, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False)
            .Columns.Add(colCheckBox)

            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(columnType, "ColName", "扫描项", _
                                                                                               300, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            colName.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            colName.DefaultCellStyle = cellStyle
            .Columns.Add(colName)

            Dim colFileCount As tbDataGridViewTextBoxColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colFileCount", "文件个数", _
                                    107, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)
            colFileCount.DefaultCellStyle.ForeColor = Color.Gray
            colFileCount.DefaultCellStyle.SelectionForeColor = Color.Gray
            .Columns.Add(colFileCount)

            Dim colFileSize As tbDataGridViewTextBoxColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colFileSize", "文件大小", _
                                    93, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)
            colFileSize.DefaultCellStyle.ForeColor = Color.Gray
            colFileSize.DefaultCellStyle.SelectionForeColor = Color.Gray
            .Columns.Add(colFileSize)



            .ColumnHeadersVisible = False
            .Anchor = AnchorStyles.Left + AnchorStyles.Top
            .Dock = DockStyle.None
            .tbShowNoData = False
            .ScrollBars = ScrollBars.None
            .Visible = False
            .Width = 657
            .ResumeLayout()
            .Anchor = AnchorStyles.Top + AnchorStyles.Right + AnchorStyles.Left
            .Location = mLocatDataGridView
            '  .BackgroundColor = Color.FromArgb(255, 255, 200)

        End With

        Me.Controls.Add(Me.dgvData)
        ' Me.dgvData.Location = 
    End Sub

    Private Sub lblContext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblCaption.Click
        Me.btnAppend_Click(Nothing, Nothing)
    End Sub

    Private Sub chkIsSelect_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkIsSelect.CheckedChanged
        If Me.chkIsSelect.CheckState = CheckState.Checked Then
            mCheckTriggle = True
            For Each row As tbDataGridViewRow In Me.dgvData.Rows
                row.Cells("colCheckbox").Value = True
            Next
            mCheckTriggle = False
        ElseIf Me.chkIsSelect.CheckState = CheckState.Unchecked Then
            mCheckTriggle = True
            For Each row As tbDataGridViewRow In Me.dgvData.Rows
                row.Cells("colCheckbox").Value = False
            Next
            mCheckTriggle = False
        End If

        If Me.mListItems IsNot Nothing Then
            ApplyCheckToData()

        End If
        RaiseEvent CheckChange()
    End Sub

    Private Sub dgvData_CellValueChanged(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles dgvData.CellValueChanged
        If e.ColumnIndex = 0 Then
            Dim row As tbDataGridViewRow = Me.dgvData.Rows(e.RowIndex)
            Dim appName As String = Me.mDictRowToSku(row)

            Me.mListItems(appName).IsChecked = Me.dgvData.Rows(e.RowIndex).Cells(0).Value

            If Not Me.mCheckTriggle Then
                Dim isAllSelect As Boolean = True
                Dim isAllNoSelect As Boolean = True
                For Each rowItem As tbDataGridViewRow In Me.dgvData.Rows
                    If rowItem.Cells(0).Value = True Then
                        isAllNoSelect = False
                    Else
                        isAllSelect = False
                    End If
                Next
                If isAllSelect Then             '全选
                    Me.chkIsSelect.CheckState = CheckState.Checked
                ElseIf isAllNoSelect Then       '全不选
                    Me.chkIsSelect.CheckState = CheckState.Unchecked
                Else                            '部分选中
                    Me.chkIsSelect.CheckState = CheckState.Indeterminate
                End If
            End If

            RaiseEvent CheckChange()
        End If
    End Sub

    Private Sub picAppend_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles picAppend.Click
        Me.UppdateAppendStatus()
    End Sub


End Class
