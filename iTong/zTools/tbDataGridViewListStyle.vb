﻿Public Class tbDataGridViewListStyle
    Inherits tbDataGridViewEx
    Public Sub New()
        MyBase.New()

        Me.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(242, 244, 247) 'Color.White
        Me.DefaultCellStyle.SelectionBackColor = Color.FromArgb(242, 244, 247) 'Color.White

        Me.SetStyle(ControlStyles.Selectable, False)
    End Sub
    Protected Overrides Sub SetColumnHeadState()

    End Sub

    Public Overloads Property BackgroundColor() As Color
        Get
            Return MyBase.BackgroundColor
        End Get
        Set(ByVal value As Color)
            MyBase.BackgroundColor = value
            'MyBase.DefaultBackColor = value
            Me.AlternatingRowsDefaultCellStyle.BackColor = value
            Me.DefaultCellStyle.SelectionBackColor = value
            Me.DefaultCellStyle.BackColor = value
        End Set
    End Property

End Class
