﻿Public Class frmKingRoot

    Private mDownManager As MultiThreadDownload

#Region "---  初始化  ---"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As IDevice)
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mAndroid = device
        Me.mApplication = app
        Me.Language = app.Language
        Me.Icon = My.Resources.iTong
        Me.tbGuiBackground = My.Resources.frm_bg_sub
        Me.FilletRadius = 5
        Me.BackColor = System.Drawing.Color.FromArgb(233, 237, 243) ' Color.FromArgb(53, 137, 199)
        Me.tbSplit = "10,33,10,55"
        Me.Size = New Size(440, 300)
        Me.Owner = Me.mApplication
        Me.tbAutoSetFormSize = True

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        If Me.mDevice IsNot Nothing Then
            Me.Name = Me.mDevice.Identifier & Me.Name
        End If

        Me.pbImage.Image = My.Resources.more_KingRoot
        Me.btnInstall.Text = Me.Language.GetString("Download.Button.Install")               '立即安装
        Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.KingRootTip")         '"KingRoot是一款获取手机Root权限的工具"
        Me.lblDiscriptionDetail.Text = "  " & Me.Language.GetString("Tools.Message.KingRootDescription")  'KingRoot为用户提供简单快捷的一键Root方案,集成10套获取Root权限 引擎,支持Android 2.1-4.1，超过65个品牌，接近500款型号的手机获取Root 权限。Root过程安全稳定，无需用户干预。获取权限后，更可以管理敏感的 隐私权限。
    End Sub


    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

    End Sub


    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub


    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

    End Sub

#End Region

#Region "---  窗体事件  ---"
    Private Sub btnInstall_Click(sender As Object, e As EventArgs) Handles btnInstall.Click
        IniSetting.SetIsShowKingRootForm(False)
        KingRootHelper.Instance().DownloadZip()
        Me.Close()
    End Sub
#End Region

#Region "--- 私有方法 ---"
    'Private Sub DownloadZip()
    '    Try
    '        Dim info As New MultiThreadDownloadItemInfo
    '        With info
    '            .IsSingleThread = False
    '            .SaveFolder = Folder.Plugins
    '            .Type = ResourceType.zip
    '            .Source = TaskSource.Tongbu
    '            .Url = mDownloadUrl
    '            Dim strName As String = String.Empty
    '            If strName.Length = 0 Then
    '                strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
    '            End If
    '            info.Name = System.Web.HttpUtility.UrlDecode(strName)

    '        End With

    '        Me.mDownManager.NewTask(info)

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString, "DownloadZip")
    '    End Try
    'End Sub
#End Region

#Region "--- 下载事件 ---"
    'Private Sub InitDownloadManager()
    '    Try
    '        'Me.mDownManager = tbPlugins.DownloadManager

    '        'AddHandler Me.mDownManager.TaskAdd, AddressOf OnTaskAdd
    '        'AddHandler Me.mDownManager.TaskUpdate, AddressOf OnTaskUpdate
    '        'AddHandler Me.mDownManager.TaskDelete, AddressOf OnTaskDelete
    '        AddHandler Me.mDownManager.DownloadItemCallBack, AddressOf OnDownloading
    '        AddHandler Me.mDownManager.DownloadItemCompleted, AddressOf OnDownloaded
    '        AddHandler Me.mDownManager.TaskCombine, AddressOf OnDownTaskCombine
    '        'AddHandler Me.mDownManager.IconDownloadedEvent, AddressOf OnIconDownloaded
    '        'AddHandler Me.mDownManager.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "InitDownloadManager")
    '    End Try

    'End Sub

    'Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
    '    Try
    '        If Me.InvokeRequired Then
    '            Me.Invoke(New MultiThreadDownloadHandler(AddressOf OnDownloading), New Object() {sender, e})
    '        Else
    '            If Me Is Nothing OrElse Me.IsDisposed = True Then
    '                Return
    '            End If

    '            Try
    '                Dim item As MultiThreadDownloadItem = sender
    '                If item.ItemInfo.ResourceSize = 0 Then
    '                    Return
    '                End If
    '                lblTip.Text = String.Format("正在下载{0}", IIf(e.ReceiveSize = e.ResourceSize, 100, Math.Round(e.ReceiveSize / e.ResourceSize, 4) * 100).ToString() & "%")


    '            Catch ex As Exception
    '                Common.LogException(ex.ToString(), "OnDownloading")
    '            End Try
    '        End If
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "OnDownloading1")
    '    End Try

    'End Sub

    'Private Sub OnDownTaskCombine(ByVal item As MultiThreadDownloadItem)
    '    AppleDownload.CombineAppInfo(item.ItemInfo)
    'End Sub

    'Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New MultiThreadDownloadCompletedHandler(AddressOf OnDownloaded), New Object() {sender, e})
    '    Else
    '        Dim item As MultiThreadDownloadItem = sender
    '        Dim isSucceed As Boolean = (e.Cancel = False AndAlso e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0)
    '        If Not isSucceed Then
    '            Common.LogException("下载失败")
    '            Return
    '        End If
    '        Dim outPackageInfoPath As String = Path.Combine(Folder.Plugins, "KingRoot")
    '        If Utility.unzip(item.ItemInfo.DownloadPath, outPackageInfoPath) <= 0 Then
    '            Common.LogException("解压失败")
    '            Return
    '        End If

    '        Dim strKingRootPath As String = Path.Combine(outPackageInfoPath, "KingRoot.exe")
    '        If File.Exists(strKingRootPath) Then
    '            Process.Start(strKingRootPath)
    '        End If
    '        Me.Close()
    '    End If
    'End Sub


#End Region

End Class