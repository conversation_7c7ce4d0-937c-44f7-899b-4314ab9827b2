﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ClearRubbishList
    Inherits System.Windows.Forms.UserControl

    'UserControl 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(ClearRubbishList))
        Me.picAppend = New tbPictureBox
        Me.lblDescrib = New tbLabel
        Me.lblFileCount = New tbLabel
        Me.lblFileSize = New tbLabel
        Me.chkIsSelect = New tbCheckBox
        Me.lblCaption = New tbLabel
        Me.picIcon = New tbPictureBox
        Me.lblDescrib2 = New tbLabel
        CType(Me.picAppend, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picIcon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'picAppend
        '
        Me.picAppend.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.picAppend.BackColor = System.Drawing.Color.Transparent
        Me.picAppend.Image = My.Resources.tool_clean_detail
        Me.picAppend.Location = New System.Drawing.Point(757, 0)
        Me.picAppend.Name = "picAppend"
        Me.picAppend.Size = New System.Drawing.Size(52, 70)
        Me.picAppend.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picAppend.TabIndex = 93
        Me.picAppend.TabStop = False
        Me.picAppend.tbAutoSize = False
        Me.picAppend.tbBackgroundImage = Nothing
        Me.picAppend.tbSplit = "0,0,0,0"
        '
        'lblDescrib
        '
        Me.lblDescrib.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDescrib.ForeColor = System.Drawing.Color.Gray
        Me.lblDescrib.Location = New System.Drawing.Point(89, 36)
        Me.lblDescrib.Name = "lblDescrib"
        Me.lblDescrib.Size = New System.Drawing.Size(600, 18)
        Me.lblDescrib.TabIndex = 91
        Me.lblDescrib.tbAdriftWhenHover = False
        Me.lblDescrib.tbAutoEllipsis = False
        Me.lblDescrib.tbAutoSize = False
        Me.lblDescrib.tbHideImage = False
        Me.lblDescrib.tbIconImage = Nothing
        Me.lblDescrib.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDescrib.tbIconPlaceText = 5
        Me.lblDescrib.tbShadow = False
        Me.lblDescrib.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDescrib.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDescrib.tbShowScrolling = False
        Me.lblDescrib.Text = "详细描述"
        '
        'lblFileCount
        '
        Me.lblFileCount.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblFileCount.Location = New System.Drawing.Point(518, 14)
        Me.lblFileCount.Name = "lblFileCount"
        Me.lblFileCount.Size = New System.Drawing.Size(102, 22)
        Me.lblFileCount.TabIndex = 90
        Me.lblFileCount.tbAdriftWhenHover = False
        Me.lblFileCount.tbAutoEllipsis = False
        Me.lblFileCount.tbAutoSize = False
        Me.lblFileCount.tbHideImage = False
        Me.lblFileCount.tbIconImage = Nothing
        Me.lblFileCount.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileCount.tbIconPlaceText = 5
        Me.lblFileCount.tbShadow = False
        Me.lblFileCount.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileCount.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileCount.tbShowScrolling = False
        Me.lblFileCount.Text = "0"
        Me.lblFileCount.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblFileSize
        '
        Me.lblFileSize.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblFileSize.Location = New System.Drawing.Point(622, 14)
        Me.lblFileSize.Name = "lblFileSize"
        Me.lblFileSize.Size = New System.Drawing.Size(91, 22)
        Me.lblFileSize.TabIndex = 89
        Me.lblFileSize.tbAdriftWhenHover = False
        Me.lblFileSize.tbAutoEllipsis = False
        Me.lblFileSize.tbAutoSize = False
        Me.lblFileSize.tbHideImage = False
        Me.lblFileSize.tbIconImage = Nothing
        Me.lblFileSize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileSize.tbIconPlaceText = 5
        Me.lblFileSize.tbShadow = False
        Me.lblFileSize.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileSize.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileSize.tbShowScrolling = False
        Me.lblFileSize.Text = "0"
        Me.lblFileSize.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'chkIsSelect
        '
        Me.chkIsSelect.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkIsSelect.CheckAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.chkIsSelect.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.chkIsSelect.Location = New System.Drawing.Point(7, 23)
        Me.chkIsSelect.Name = "chkIsSelect"
        Me.chkIsSelect.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkIsSelect.Size = New System.Drawing.Size(21, 22)
        Me.chkIsSelect.TabIndex = 87
        Me.chkIsSelect.tbAdriftIconWhenHover = False
        Me.chkIsSelect.tbAutoSize = False
        Me.chkIsSelect.tbAutoSizeEx = False
        'Me.chkIsSelect.tbIconChecked = CType(resources.GetObject("chkIsSelect.tbIconChecked"), System.Drawing.Image)
        Me.chkIsSelect.tbIconCheckedMouseDown = Nothing
        Me.chkIsSelect.tbIconCheckedMouseHover = Nothing
        Me.chkIsSelect.tbIconCheckedMouseLeave = Nothing
        Me.chkIsSelect.tbIconCheckedState = ImageState.FourState
        Me.chkIsSelect.tbIconHoldPlace = True
        Me.chkIsSelect.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        'Me.chkIsSelect.tbIconIndeterminate = CType(resources.GetObject("chkIsSelect.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkIsSelect.tbIconIndeterminateMouseDown = Nothing
        Me.chkIsSelect.tbIconIndeterminateMouseHover = Nothing
        Me.chkIsSelect.tbIconIndeterminateMouseLeave = Nothing
        Me.chkIsSelect.tbIconIndeterminateState = ImageState.FourState
        Me.chkIsSelect.tbIconPlaceText = 1
        'Me.chkIsSelect.tbIconUnChecked = CType(resources.GetObject("chkIsSelect.tbIconUnChecked"), System.Drawing.Image)
        Me.chkIsSelect.tbIconUnCheckedMouseDown = Nothing
        Me.chkIsSelect.tbIconUnCheckedMouseHover = Nothing
        Me.chkIsSelect.tbIconUnCheckedMouseLeave = Nothing
        Me.chkIsSelect.tbIconUnCheckedState = ImageState.FourState
        Me.chkIsSelect.tbImageBackground = Nothing
        Me.chkIsSelect.tbImageBackgroundState = ImageState.TwoState
        Me.chkIsSelect.tbImageCheckedMouseDown = Nothing
        Me.chkIsSelect.tbImageCheckedMouseHover = Nothing
        Me.chkIsSelect.tbImageCheckedMouseLeave = Nothing
        Me.chkIsSelect.tbImageUnCheckedMouseDown = Nothing
        Me.chkIsSelect.tbImageUnCheckedMouseHover = Nothing
        Me.chkIsSelect.tbImageUnCheckedMouseLeave = Nothing
        Me.chkIsSelect.tbReadOnly = False
        Me.chkIsSelect.tbShadow = False
        Me.chkIsSelect.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkIsSelect.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkIsSelect.tbSplit = "3,3,3,3"
        Me.chkIsSelect.tbToolTip = ""
        Me.chkIsSelect.Text = " "
        Me.chkIsSelect.UseVisualStyleBackColor = True
        '
        'lblCaption
        '
        Me.lblCaption.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblCaption.Location = New System.Drawing.Point(89, 14)
        Me.lblCaption.Name = "lblCaption"
        Me.lblCaption.Size = New System.Drawing.Size(400, 22)
        Me.lblCaption.TabIndex = 1
        Me.lblCaption.tbAdriftWhenHover = False
        Me.lblCaption.tbAutoEllipsis = False
        Me.lblCaption.tbAutoSize = False
        Me.lblCaption.tbHideImage = False
        Me.lblCaption.tbIconImage = Nothing
        Me.lblCaption.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCaption.tbIconPlaceText = 5
        Me.lblCaption.tbShadow = False
        Me.lblCaption.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCaption.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCaption.tbShowScrolling = False
        Me.lblCaption.Text = "详细描述"
        Me.lblCaption.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picIcon
        '
        Me.picIcon.Location = New System.Drawing.Point(46, 19)
        Me.picIcon.Name = "picIcon"
        Me.picIcon.Size = New System.Drawing.Size(35, 35)
        Me.picIcon.TabIndex = 0
        Me.picIcon.TabStop = False
        Me.picIcon.tbAutoSize = False
        Me.picIcon.tbBackgroundImage = Nothing
        Me.picIcon.tbSplit = "0,0,0,0"
        '
        'lblDescrib2
        '
        Me.lblDescrib2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDescrib2.ForeColor = System.Drawing.Color.Gray
        Me.lblDescrib2.Location = New System.Drawing.Point(89, 53)
        Me.lblDescrib2.Name = "lblDescrib2"
        Me.lblDescrib2.Size = New System.Drawing.Size(600, 18)
        Me.lblDescrib2.TabIndex = 94
        Me.lblDescrib2.tbAdriftWhenHover = False
        Me.lblDescrib2.tbAutoEllipsis = False
        Me.lblDescrib2.tbAutoSize = False
        Me.lblDescrib2.tbHideImage = False
        Me.lblDescrib2.tbIconImage = Nothing
        Me.lblDescrib2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDescrib2.tbIconPlaceText = 5
        Me.lblDescrib2.tbShadow = False
        Me.lblDescrib2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDescrib2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDescrib2.tbShowScrolling = False
        '
        'ClearRubbishList
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.Controls.Add(Me.lblDescrib2)
        Me.Controls.Add(Me.picAppend)
        Me.Controls.Add(Me.lblDescrib)
        Me.Controls.Add(Me.lblFileCount)
        Me.Controls.Add(Me.lblFileSize)
        Me.Controls.Add(Me.chkIsSelect)
        Me.Controls.Add(Me.lblCaption)
        Me.Controls.Add(Me.picIcon)
        Me.Name = "ClearRubbishList"
        Me.Size = New System.Drawing.Size(809, 70)
        CType(Me.picAppend, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picIcon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Private WithEvents picIcon As tbPictureBox
    Private WithEvents lblCaption As tbLabel
    Private WithEvents chkIsSelect As tbCheckBox
    Private WithEvents lblFileSize As tbLabel
    Private WithEvents lblFileCount As tbLabel
    Private WithEvents lblDescrib As tbLabel
    Friend WithEvents picAppend As tbPictureBox
    Private WithEvents lblDescrib2 As tbLabel

End Class
