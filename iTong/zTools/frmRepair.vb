﻿Imports System.Threading
Public Class frmRepair

    Private mThrLoadRepaorApps As Thread = Nothing
    Private mDownManage As MultiThreadDownload
    Private mInstallHelper As iPhoneInstallHelper = Nothing
    Private mItems As New Dictionary(Of String, AppItemRecomEx)
    Private mDictCanRepaid As New Dictionary(Of String, RepairObj)
    Private mDictCanNotRepaid As New Dictionary(Of String, RepairObj)
    Private mDictNotNeedRepaid As New Dictionary(Of String, RepairObj)

    Private mQueueDownlaod As New Queue(Of RepairObj)
    Private mThrDownlaod As Thread
    Private mRepairPaymentResult As RepairPaymentResult = New RepairPaymentResult()

#Region "----初始化、窗体闭关----"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        MyBase.New(application, device)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.Language = application.Language
        Me.mApplication = application
        Me.mDevice = device
        Me.FunctionMappingKey = FunctionKey.None
        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False

        Me.btnminimize.tbBackgroundImage = My.Resources.btn_min
        Me.btnminimize.tbBackgroundImageState = Me.btn_minimize.tbBackgroundImageState
        Me.btnminimize.Location = Me.btn_normal.Location
        Me.btnminimize.Anchor = Me.btn_normal.Anchor


        Me.Icon = My.Resources.iTong
        Me.FilletRadius = 5
        'If ownerform Is Nothing Then
        '    Me.Owner = Me.mApplication
        'Else
        '    Me.Owner = ownerform
        'End If
        'Me.Name = Me.mDevice.Identifier & "iTone.frmRepair"
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.CanResize = False
        Me.lblLoading.Font = Common.CreateFont("微软雅黑", 20.0F, FontStyle.Regular)
        Me.flpContainer.tbGroupPadding = 15
        Me.flpContainer.Dock = DockStyle.Fill
        Me.pnlEmpty.Dock = DockStyle.Fill
        Me.pnlEmpty.SendToBack()
        Me.InitDownloadAndInstall()
        Me.DoLoadRepairApps()
        Me.txtNameValue.Size = New Size(Me.txtNameValue.Width, Me.txtNameValue.Height + 1)
        '找开textbox 的ime设置
        Try
            Me.txtNameValue.ImeMode = Windows.Forms.ImeMode.OnHalf
        Catch ex As Exception
        End Try

        If Utility.IsPanda Then
            Me.btnJailbreak.Enabled = False
        End If

        RemoveHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
        AddHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
    End Sub

    Private Sub InitDownloadAndInstall()
        Me.mDownManage = MultiThreadDownload.Instance
        RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
        AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
        RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
        RemoveHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
        AddHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
        RemoveHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
        AddHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse

        RemoveHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
        AddHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd

        Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)
        RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
        AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lblLoading.Text = Me.Language.GetString("Tools.Message.CheckingErrorApp")        '"正在检测设备中闪退的应用..."
        Me.Text = Me.Language.GetString("Tools.Button.StronRepair")                         '"强力修复"
        Me.txtNameValue.TextTip = Me.Language.GetString("Tools.Message.SearchRepairApp")    '"搜索您要修复的应用"
        Me.txtNameValue.Text = ""
        Me.txtNameValue.tbTextBind = ""
        Me.lblJailbreak.Text = Me.Language.GetString("Tools.Message.PreventFlashBack")      '"杜绝闪退"
        Me.btnJailbreak.Text = Me.Language.GetString("Tools.Message.JailbreakImmediately")  '"立即越狱"
        Me.lblFileEmpty.Text = Me.Language.GetString("Media.Label.Empty")                   '"这里没有数据"
        Me.btnminimize.tbToolTip = Me.Language.GetString("Setting.RadioButton.MiniToTray")
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Try
            If Me.mThrLoadRepaorApps IsNot Nothing AndAlso Me.mThrLoadRepaorApps.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadRepaorApps.Abort()
            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As CoreFoundation.IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)
        Try
            If CType(Me.mApplication, MainForm).Left < -2000 Then
                Me.Size = New Size(870, 610)
                Me.Location = New Point((Screen.PrimaryScreen.Bounds.Width - Me.Width) / 2, (Screen.PrimaryScreen.Bounds.Height - Me.Height) / 2)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_OnShown")
        End Try
        ActionCollectHelper.ShowDeviceHomePage(Me.mDevice, ModelKey.SummaryStrongRepair)
    End Sub

    Protected Overrides Sub OnFormClosing(ByVal e As System.Windows.Forms.FormClosingEventArgs)
        MyBase.OnFormClosing(e)
        RemoveHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
    End Sub

#End Region

#Region "----加载数据----"

    Private Sub DoLoadRepairApps()
        Try
            If Me.mThrLoadRepaorApps IsNot Nothing AndAlso Me.mThrLoadRepaorApps.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadRepaorApps.Abort()
            End If
        Catch ex As Exception
        End Try

        Me.mThrLoadRepaorApps = New Thread(AddressOf DoLoadRepairAppsThread)
        With Me.mThrLoadRepaorApps
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub DoLoadRepairAppsThread()
        Try
            Me.SetView(True)
            Me.LoadInfo()
            Me.SetView(False)
            Me.ShowItem()
            Me.LoadItemIcon()
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_Load")
        End Try
    End Sub

    Private Delegate Sub SetViewHandler(ByVal isLoading As Boolean)
    Private Sub SetView(ByVal isLoading As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewHandler(AddressOf SetView), isLoading)
        Else
            Me.pnlMain.SuspendLayout()
            If isLoading Then
                Me.pnlLoadingEx.Visible = True
                Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Percent
                Me.pnlMain.ColumnStyles(0).Width = 100
                Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Absolute
                Me.pnlMain.ColumnStyles(1).Width = 0
            Else
                Me.pnlLoadingEx.Visible = False
                Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Absolute
                Me.pnlMain.ColumnStyles(0).Width = 0
                Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Percent
                Me.pnlMain.ColumnStyles(1).Width = 100
            End If
            Me.pnlMain.ResumeLayout()
        End If
    End Sub

    Private Sub LoadInfo()
        Dim dictApps As New Dictionary(Of String, FileSharingPackageInfo)
        Try
            '加载本手机上的软件信息
            dictApps = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            If dictApps Is Nothing OrElse dictApps.Count <= 0 Then
                Utility.WaitSeconds(0.5)
                dictApps = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            End If

            ''从服务器取得要可以修复的软件信息
            'Dim lstPkgInfo As New List(Of PackageInfo)
            'For Each Item As FileSharingPackageInfo In dictApps.Values
            '    iPhoneDeviceHelper.GetDeviceITunesMedataInfo(Me.mDevice, Item, False)
            '    lstPkgInfo.Add(Item)
            'Next

            '从服务器取得要可以修复的软件信息
            iPhoneDeviceHelper.GetDeviceITunesMedataInfo(Me.mDevice, dictApps, False, True)
            Dim lstPkgInfo As New List(Of PackageInfo)
            For Each Item As FileSharingPackageInfo In dictApps.Values
                lstPkgInfo.Add(Item)
            Next

            Dim blnJail As Boolean = False
            If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
                blnJail = True
            End If

            Dim blnAccount As Boolean = True
            If String.IsNullOrEmpty(IniSetting.GetDefaultAppleID(Me.mDevice)) Then
                blnAccount = False
            End If

            'Dim dictUpInfo As Dictionary(Of String, RepairPackageInfo) = HtmlHelper.CheckSoftRepair(lstPkgInfo, IniSetting.GetLanguage, _
            '                                                                                        My.Application.Info.Version.ToString(4), _
            '                                                                                        Me.mDevice.ProductType, Me.mDevice.ProductVersion, _
            '                                                                                        Me.mDevice.SerialNumber, Me.mDevice.UniqueDeviceID, blnJail, _
            '                                                                                        Me.mDevice.WiFiAddress, DeviceIni.GetAuthVersion, False, blnAccount)
            Dim tuiItem As FileSharingPackageInfo = Me.mDevice.GetTuiInfo()
            Dim tuiIdentify As String = ""
            Dim tuiVersion As String = ""
            If tuiItem IsNot Nothing Then
                tuiIdentify = tuiItem.Identifier
                tuiVersion = tuiItem.Version
            End If

            Me.mRepairPaymentResult = HtmlHelper.CheckSoftRepairPayment(False, _
                                                                        lstPkgInfo, _
                                                                        tuiIdentify, _
                                                                        tuiVersion, _
                                                                        IniSetting.GetDefaultAppleID(Me.mDevice), _
                                                                        My.Application.Info.Version.ToString(4), _
                                                                        Me.mDevice.ProductType, Me.mDevice.ProductVersion, _
                                                                        Me.mDevice.SerialNumber, blnJail, _
                                                                        DeviceIni.GetAuthVersion)

            For Each Item As PackageInfo In dictApps.Values
                Dim obj As New RepairObj
                obj.pkgInfo = Item
                obj.repairInfo = Nothing

                If dictApps.ContainsKey(Item.Identifier) AndAlso (dictApps(Item.Identifier).CrackedInfo = CrakedInfo.Craked OrElse dictApps(Item.Identifier).AppleId.Length > 0) Then
                    Me.mDictNotNeedRepaid.Add(Item.Identifier, obj)
                    Continue For
                End If

                If Me.mRepairPaymentResult.RepairApps.ContainsKey(Item.Identifier) Then
                    obj.repairInfo = Me.mRepairPaymentResult.RepairApps(Item.Identifier)

                    If obj.repairInfo.Type = RepairType.CanRepair Then
                        Me.mDictCanRepaid.Add(Item.Identifier, obj)

                    ElseIf obj.repairInfo.Type = RepairType.NotRepair Then
                        Me.mDictCanNotRepaid.Add(Item.Identifier, obj)

                    Else
                        Me.mDictNotNeedRepaid.Add(Item.Identifier, obj)

                    End If

                Else
                    Me.mDictCanNotRepaid.Add(Item.Identifier, obj)

                End If
            Next
        Catch ex As Exception
        End Try
    End Sub

    Private Sub ShowItem()
        Me.CreateItem(RepairType.CanRepair, Me.mDictCanRepaid, True)
        Me.CreateItem(RepairType.NotRepair, Me.mDictCanNotRepaid, False)
        Me.CreateItem(RepairType.NotNeedRepair, Me.mDictNotNeedRepaid, False)

        Me.SetGroup(RepairType.CanRepair)
        Me.SetGroup(RepairType.NotNeedRepair)
        Me.SetGroup(RepairType.NotRepair)
    End Sub

    Private Delegate Sub SetGroupHandler(ByVal type As RepairType)
    Private Sub SetGroup(ByVal type As RepairType)
        If Me.InvokeRequired Then
            Me.Invoke(New SetGroupHandler(AddressOf SetGroup), type)
        Else
            Me.flpContainer.tbSuspendLayout()
            For Each Item As Object In Me.flpContainer.Controls
                If Item IsNot Nothing AndAlso TypeOf Item Is Label AndAlso CType(Item, tbGroupLabel).Text.StartsWith(Me.GetGroupName(type)) Then
                    Dim lbl As tbGroupLabel = CType(Item, tbGroupLabel)
                    lbl.tbIconImage = Me.GetGroupIcon(type)
                    lbl.Text = Me.GetGroupText(type)
                    lbl.tbIconImageAlign = ContentAlignment.MiddleLeft
                    lbl.Padding = New Padding(6, 0, 0, 0)
                    lbl.BackColor = Color.FromArgb(209, 220, 236)
                    lbl.Font = Common.CreateFont("微软雅黑", 11.0F, FontStyle.Bold)
                    lbl.AutoSize = False
                    lbl.Size = New Size(Me.Width - 35, 32)
                    lbl.TextAlign = ContentAlignment.MiddleLeft
                    lbl.ForeColor = Color.FromArgb(80, 80, 80)
                    lbl.Anchor = AnchorStyles.Left And AnchorStyles.Right And AnchorStyles.Top
                    lbl.Tag = type
                    Exit For
                End If
            Next
            Me.flpContainer.tbResumeLayout()

            '如果没有数据显示空界面
            If Me.flpContainer.tbControls.Count <= 0 Then
                Me.pnlEmpty.BringToFront()
            Else
                Me.pnlEmpty.SendToBack()
            End If
        End If
    End Sub

    Private Function GetGroupIcon(ByVal type As RepairType) As Image
        Dim img As Image = My.Resources.icon_canrepair

        Select Case type
            Case RepairType.CanRepair
                img = My.Resources.icon_canrepair

            Case RepairType.NotRepair
                img = My.Resources.icon_cannotrepair

            Case RepairType.NotNeedRepair
                img = My.Resources.icon_notneedrepair

        End Select

        Return img
    End Function

    Private Function GetGroupName(ByVal type As RepairType) As String
        Dim strGroup As String = Me.Language.GetString("Tools.Label.CanRepair")                 '"可修复"
        Select Case type
            Case RepairType.CanRepair
                strGroup = Me.Language.GetString("Tools.Label.CanRepair")                       '"可修复"

            Case RepairType.NotRepair
                strGroup = Me.Language.GetString("Tools.Label.NotRepair")                       '"暂不能修复"

            Case RepairType.NotNeedRepair
                strGroup = Me.Language.GetString("Tools.Label.NotNeedRepair")                   '"无需修复"

        End Select
        Return strGroup
    End Function

    Private Function GetGroupText(ByVal type As RepairType) As String
        Dim strGroup As String = Me.GetGroupName(type)
        Dim dict As Dictionary(Of String, RepairObj)

        Select Case type
            Case RepairType.CanRepair
                dict = Me.mDictCanRepaid

            Case RepairType.NotRepair
                dict = Me.mDictCanNotRepaid

            Case RepairType.NotNeedRepair
                dict = Me.mDictNotNeedRepaid

        End Select

        Dim intCount As Integer = 0
        For Each Item As String In dict.Keys
            If Me.mItems.ContainsKey(Item) AndAlso Me.mItems(Item).Visible Then
                intCount += 1
            End If
        Next

        strGroup = strGroup & String.Format("({0})", intCount)
        Return strGroup
    End Function

    Private Delegate Sub CreateItemHandler(ByVal type As RepairType, ByVal dictGroup As Dictionary(Of String, RepairObj), ByVal blnShowBtn As Boolean)
    Private Sub CreateItem(ByVal type As RepairType, ByVal dictGroup As Dictionary(Of String, RepairObj), ByVal blnShowBtn As Boolean)
        If dictGroup Is Nothing OrElse dictGroup.Count <= 0 Then
            Return
        End If

        If Me.InvokeRequired Then
            Me.Invoke(New CreateItemHandler(AddressOf CreateItem), type, dictGroup, blnShowBtn)
        Else
            Me.flpContainer.tbSuspendLayout() 
            If type = RepairType.CanRepair Then
                Me.flpContainer.AddGroup(Me.GetGroupName(type), Me.Language.GetString("Tools.Button.RepairAll"))            '"全部修复"
            Else
                Me.flpContainer.AddGroup(Me.GetGroupName(type))
            End If

            For Each ItemRepair As RepairObj In dictGroup.Values
                Dim strName As String = ItemRepair.pkgInfo.DisplayName
                If String.IsNullOrEmpty(strName) Then
                    If ItemRepair.repairInfo IsNot Nothing AndAlso Not String.IsNullOrEmpty(ItemRepair.repairInfo.Name) Then
                        strName = ItemRepair.repairInfo.Name
                    Else
                        strName = ItemRepair.pkgInfo.Name
                    End If

                End If
                Dim item As New AppItemRecomEx(strName, Nothing, ItemRepair.pkgInfo.Identifier, Nothing)
                item.Font = flpContainer.Font
                item.Icon = Nothing  ' ItemRepair.pkgInfo.AppIconOriginal
                item.Tag = ItemRepair
                item.TipText = strName
                item.TextHove = Me.Language.GetString("Tools.Button.Repair")                '"修复"
                item.GroupName = Me.GetGroupName(type)
                item.IconRangeImage = My.Resources.summary_apprange_white
                item.IconCursor = Cursors.Default

                If Not blnShowBtn Then
                    item.Status = AppIconStatus.Unavailable
                End If
                AddHandler item.ButtonClickIcon, AddressOf Item_ButtonClickIcon
                AddHandler item.ButtonClickText, AddressOf Item_ButtonClickText


                Me.flpContainer.Controls.Add(item)
                Me.mItems.Add(ItemRepair.pkgInfo.Identifier, item)
            Next
            Me.flpContainer.tbResumeLayout()
        End If
    End Sub

    Private Sub LoadItemIcon()
        For Each Item As Object In Me.flpContainer.Controls
            If Item IsNot Nothing AndAlso TypeOf Item Is AppItemRecomEx AndAlso _
               Item.tag IsNot Nothing AndAlso TypeOf Item.tag Is RepairObj Then
                Dim info As RepairObj = Item.tag
                CType(Item, AppItemRecomEx).Icon = iPhoneDeviceHelper.GetDevicePNGData(Me.mDevice, info.pkgInfo.Identifier, info.pkgInfo.Version)
            End If
            Utility.WaitSeconds(0.01)
        Next
    End Sub

#End Region

#Region "----修复事件、越狱----"

    Private Sub Item_ButtonClickIcon(ByVal sender As System.Object, ByVal e As AppItemEventArgs)
        If sender IsNot Nothing AndAlso sender.tag IsNot Nothing AndAlso TypeOf sender.tag Is PackageInfo Then

        End If
    End Sub

    Private Sub Item_ButtonClickText(ByVal sender As System.Object, ByVal e As AppItemEventArgs)
        Try
            If sender IsNot Nothing AndAlso sender.tag IsNot Nothing AndAlso TypeOf sender.tag Is RepairObj Then
                Me.DoRepair(sender, True)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmRepair_Start")
        End Try
    End Sub

    Private Sub DoRepair(ByVal btn As AppItemRecomEx, Optional ByVal blnClickOne As Boolean = False)
        Try
            Dim obj As RepairObj = btn.Tag
            Dim itemDown As MultiThreadDownloadItem = Me.GetDownItem(obj.repairInfo.Identifier, obj.repairInfo.Itemid)

            obj.hasdownload = False
            Dim blnIsCancel As Boolean = False
            If itemDown IsNot Nothing Then
                If btn.Status = AppIconStatus.Repairing OrElse btn.Status = AppIconStatus.Waiting Then
                    Dim pkgInfo As PackageInfo = PackageInfoReader.GetPackageInfo(itemDown.DownloadInfo.FilePath)

                    Me.mInstallHelper.CancelInstallApp(pkgInfo)
                    Me.mDownManage.Stop(itemDown)
                    Me.SetIconStatus(btn, AppIconStatus.None, 0)
                    blnIsCancel = True
                Else
                    Me.SetIconStatus(btn, AppIconStatus.Waiting, 0)

                    If itemDown.ItemInfo.Status = TaskState.Completed Then
                        Dim pkgInfo As PackageInfo = PackageInfoReader.GetPackageInfo(itemDown.ItemInfo.FilePath)
                        If File.Exists(itemDown.ItemInfo.FilePath) AndAlso pkgInfo IsNot Nothing Then
                            Me.mInstallHelper.InstallApplication(pkgInfo)
                            obj.hasdownload = True
                            Return
                        End If
                    End If
                    If itemDown.ItemInfo.Status = TaskState.Completed Then
                        Me.StarDownloadApp(obj)
                    Else
                        '如果海外版下载就先不设置状态。下载的时候自动会启动状态设置
                        '不然如果用户不下载就没办法设置成正常状态了
                        If obj IsNot Nothing AndAlso obj.repairInfo IsNot Nothing AndAlso obj.repairInfo.IsAbroad AndAlso String.IsNullOrEmpty(IniSetting.GetDefaultAppleID(Me.mDevice)) Then
                            Me.SetIconStatus(btn, AppIconStatus.None, 0)
                        End If
                        Me.mDownManage.Start(itemDown)
                    End If
                End If
            Else
                '如果海外版下载就先不设置状态。下载的时候自动会启动状态设置
                '不然如果用户不下载就没办法设置成正常状态了
                If obj IsNot Nothing AndAlso obj.repairInfo IsNot Nothing AndAlso obj.repairInfo.IsAbroad AndAlso String.IsNullOrEmpty(IniSetting.GetDefaultAppleID(Me.mDevice)) Then
                    Me.SetIconStatus(btn, AppIconStatus.None, 0)
                Else
                    Me.SetIconStatus(btn, AppIconStatus.Waiting, 0)
                End If
                Me.StarDownloadApp(obj)
            End If
            

            '如果是全部修复就不收集（全部修复已经收集了）
            If Not blnClickOne Then
                Return
            End If

            If blnIsCancel Then
                ActionCollectHelper.ClickStrongRepairAccition(Me.mDevice, ModelKey.SummaryStrongRepairCancel)
            Else
                ActionCollectHelper.ClickStrongRepairAccition(Me.mDevice, ModelKey.SummaryStrongRepairApp)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmRepair_Start")
        End Try
    End Sub

    Private Sub StarDownloadApp(ByVal obj As RepairObj)
        Try
            Me.mQueueDownlaod.Enqueue(obj)

            If Me.mThrDownlaod IsNot Nothing AndAlso Me.mThrDownlaod.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mThrDownlaod = New Thread(AddressOf StarDownloadAppThread)

            With Me.mThrDownlaod
                .IsBackground = True
                .Start()
            End With

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_StarDownloadApp")
        End Try
    End Sub

    Private Sub StarDownloadAppThread()
        Try
            While Me.mQueueDownlaod.Count > 0
                Dim objDown As RepairObj = Me.mQueueDownlaod.Dequeue
                Me.DownloadApp(objDown.repairInfo.Url, objDown.repairInfo, objDown.pkgInfo)
                Utility.WaitSeconds(0.1)
            End While
            'Dim obj As RepairObj = objPara
            'Me.DownloadApp(obj.repairInfo.Url, obj.repairInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_StarDownloadAppThread")
        End Try
    End Sub

    '取得当前要下载的软件是不是在下载列表中
    Private Function GetDownItem(ByVal strIdentifier As String, ByVal strItemid As String)
        Dim lstDownload As List(Of MultiThreadDownloadItem) = Me.mDownManage.GetItems(ResourceClass.Software, ResourceType.IPA, TaskState.All)

        Dim itemDown As MultiThreadDownloadItem = Nothing
        For Each Item As MultiThreadDownloadItem In lstDownload
            '苹果商店下载的loadid为空。所以无法比较
            If (strIdentifier.Length > 0 AndAlso Item.ItemInfo.Identifier = strIdentifier) OrElse _
                 (strItemid.Length > 0 AndAlso Item.ItemInfo.ItemId = strItemid) Then
                itemDown = Item
                Exit For
            End If
        Next
        Return itemDown
    End Function

    Private Delegate Sub SetIconStatusHandler(ByVal itemRecom As AppItemRecomEx, ByVal status As AppIconStatus, ByVal intProgress As Integer)
    Private Sub SetIconStatus(ByVal itemRecom As AppItemRecomEx, ByVal status As AppIconStatus, ByVal intProgress As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New SetIconStatusHandler(AddressOf SetIconStatus), itemRecom, status, intProgress)
        Else
            itemRecom.Status = status
            itemRecom.Progress = intProgress
            Select Case status
                Case AppIconStatus.None
                    itemRecom.Text = itemRecom.TipText
                    itemRecom.TextHove = Me.Language.GetString("Tools.Button.Repair")                                           '"修复"

                Case AppIconStatus.Repairing
                    itemRecom.TextHove = Me.Language.GetString("Tools.Button.CancelRepair")                                     '"取消修复"
                    itemRecom.Text = String.Format(Me.Language.GetString("Tools.Message.RepairingProgess"), intProgress)        '"修复中{0}%"

                Case AppIconStatus.Waiting
                    itemRecom.Text = Me.Language.GetString("Tools.Button.WaitingRepair")                                        '"等待修复"
                    itemRecom.TextHove = Me.Language.GetString("Tools.Button.CancelRepair")                                     '"取消修复"

                Case AppIconStatus.Succeed
                    itemRecom.Text = Me.Language.GetString("Tools.Button.RepairSucceed")                                        '"修复成功"

                Case AppIconStatus.Failure
                    itemRecom.Text = Me.Language.GetString("Tools.Message.RepairFailure")                                       '"修复失败"
                    itemRecom.TextHove = Me.Language.GetString("Tools.Button.Repair")                                           '"修复"

                Case AppIconStatus.Unavailable
                    itemRecom.Text = itemRecom.TipText

            End Select
        End If
    End Sub

#Region "--- 下载ipa逻辑 ---"

    Private Sub DownloadApp(ByVal strUrl As String, ByVal repairInfo As RepairPackageInfo, ByVal pkgInfo As PackageInfo)
        Try

            Dim strItemId As String = Utility.GetParamValueFromQuery("appleid", strUrl, "&", True)
            If String.IsNullOrEmpty(strItemId) AndAlso pkgInfo IsNot Nothing Then
                strItemId = pkgInfo.ItemId
            End If
            WebSiteHelper.Instance().Start2AnalyseDownloadUrlByDownloadCenter(Me.mApplication, repairInfo.Url, repairInfo.Name, repairInfo.LoadId, strItemId, repairInfo.Version, False, repairInfo.IconUrl, Me.mDevice, False, "", repairInfo.Identifier, repairInfo.IsAbroad, repairInfo.ShowLogingAppleId)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadApp")
        End Try
    End Sub

    Private Sub DownloadIPA(ByVal url As String, _
                            ByVal repairInfo As RepairPackageInfo, _
                            Optional ByVal strItemId As String = "", _
                            Optional ByVal isNeedToBackInfo As Boolean = False)
        Try
            Dim info As New MultiThreadDownloadItemInfo
            With info
                .IsSingleThread = False

                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .Source = TaskSource.Tongbu
                .LoadId = repairInfo.LoadId
                .Url = url
                .ItemId = strItemId
                .IconUrl = repairInfo.IconUrl

                If isNeedToBackInfo Then
                    .UrlId = repairInfo.LoadId
                End If
                Dim strName As String = repairInfo.Name
                If strName.Length > 0 Then
                    If repairInfo.Version.Length > 0 Then
                        strName &= String.Format("_v{0}", repairInfo.Version)
                    End If
                    If strName.ToLower().EndsWith(".ipa") = False Then
                        strName &= ".ipa"
                    End If
                End If
                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If .Url.ToLower().Contains("tbtui") Then
                    .IsUpdate = True
                End If
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                    .Identifier = Me.mDevice.Identifier
                End If
                .Identifier = repairInfo.Identifier
            End With

            Me.mDownManage.NewTask(info)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadIPA")
        End Try
    End Sub

#End Region

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of MultiThreadDownloadEventArgs)(AddressOf OnDownloading), sender, e)
            Else
                Dim item As MultiThreadDownloadItem = sender
                Dim strIdentify As String = item.ItemInfo.Identifier
                
                If item.ItemInfo.Class = ResourceClass.Software AndAlso Me.mItems.ContainsKey(strIdentify) Then
                    Dim appitem As AppItemRecomEx = Me.mItems(strIdentify)
                    If appitem Is Nothing OrElse _
                      appitem.Tag Is Nothing OrElse _
                      CType(appitem.Tag, RepairObj).repairInfo Is Nothing OrElse _
                      CType(appitem.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair Then
                        Return
                    End If

                    Me.SetIconStatus(appitem, AppIconStatus.Repairing, item.DownloadInfo.Progress / 2)
                    appitem.Invalidate()
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_OnDownloading")
        End Try
    End Sub

    Private Sub OnTaskAppleStoreAnalyse(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.ItemInfo.Class = ResourceClass.Software AndAlso Me.mItems.ContainsKey(item.ItemInfo.Identifier) Then
            Dim appitem As AppItemRecomEx = Me.mItems(item.ItemInfo.Identifier)
            If Not item.ItemInfo.IsDeleted AndAlso Not String.IsNullOrEmpty(item.ItemInfo.ErrorMsg) Then
                Me.SetIconStatus(appitem, AppIconStatus.Failure, 0)
            End If
        End If
    End Sub

    'Private Function GetIdentifyByLoadId(ByVal itemInfo As MultiThreadDownloadItem) As String
    '    Dim strIdentify As String = ""

    '    For Each Item As AppItemRecomEx In Me.mItems.Values()
    '        Dim info As RepairObj = CType(Item.Tag, RepairObj)
    '        If info.repairInfo IsNot Nothing AndAlso (info.repairInfo.LoadId = itemInfo.ItemInfo.LoadId OrElse info.pkgInfo.ItemId = itemInfo.ItemInfo.ItemId) Then
    '            strIdentify = info.repairInfo.Identifier
    '            Exit For
    '        End If
    '    Next
    '    If String.IsNullOrEmpty(strIdentify) Then
    '        strIdentify = itemInfo.ItemInfo.Identifier
    '    End If

    '    Return strIdentify
    'End Function

    Public Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If String.IsNullOrEmpty(item.ItemInfo.Url) Then
            Try
                '如果当前下载的任务url SourceUrl  为空就设置修复列表的状态。
                Dim itemInfo As AppItemRecomEx = Me.mItems(item.ItemInfo.Identifier)
                If itemInfo IsNot Nothing AndAlso String.IsNullOrEmpty(item.ItemInfo.Url) AndAlso Not item.ItemInfo.Source = TaskSource.AppleDownload Then
                    Me.SetIconStatus(itemInfo, AppIconStatus.Failure, 0)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "frmRepair_OnTaskAdd")
            End Try
        End If
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloaded), sender, e)
            Else
                Dim item As MultiThreadDownloadItem = sender
                If item.ItemInfo.Class = ResourceClass.Software AndAlso Me.mItems.ContainsKey(item.ItemInfo.Identifier) Then
                    Dim appitem As AppItemRecomEx = Me.mItems(item.ItemInfo.Identifier)

                    If appitem Is Nothing OrElse _
                       appitem.Tag Is Nothing OrElse _
                       CType(appitem.Tag, RepairObj).repairInfo Is Nothing OrElse _
                       CType(appitem.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair Then
                        Return
                    End If
                    If e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
                        Me.SetIconStatus(appitem, AppIconStatus.Repairing, 50)
                    Else
                        Me.SetIconStatus(appitem, AppIconStatus.Failure, 0)
                    End If
                    'If item.ItemInfo.Status = TaskState.Completed Then
                    '    Me.SetIconStatus(appitem, AppIconStatus.Repairing, 50)
                    'ElseIf item.ItemInfo.Status = TaskState.Failed Then
                    '    Me.SetIconStatus(appitem, AppIconStatus.Failure, 0)
                    'End If
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_OnDownloaded")
        End Try
    End Sub

    Private Sub OnTaskDelete(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.Status <> TaskState.Completed AndAlso item.ItemInfo.Class = ResourceClass.Software Then
                Dim appitem As AppItemRecomEx = Nothing
                If Me.mItems.ContainsKey(item.ItemInfo.Identifier) Then
                    appitem = Me.mItems(item.ItemInfo.Identifier)
                End If

                'If appitem Is Nothing OrElse appitem.GroupName <> "可修复" Then
                If appitem Is Nothing OrElse _
                   appitem.Tag Is Nothing OrElse _
                   CType(appitem.Tag, RepairObj).repairInfo Is Nothing OrElse _
                   CType(appitem.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair Then
                    Return
                End If
                Utility.WaitSeconds(0.2)
                Me.SetIconStatus(appitem, AppIconStatus.None, 0)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_OnTaskDelete")
        End Try
    End Sub

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            If Not Me.mItems.ContainsKey(args.PackageInfo.Identifier) Then
                Return
            End If

            Dim appitem As AppItemRecomEx = Me.GetItemByID(args.PackageInfo.Identifier, args.PackageInfo.ItemId)
            If appitem Is Nothing OrElse appitem.Tag Is Nothing OrElse _
               (CType(appitem.Tag, RepairObj).repairInfo IsNot Nothing AndAlso CType(appitem.Tag, RepairObj).repairInfo.Type <> RepairType.CanRepair) Then
                Return
            End If
            Dim obj As RepairObj = appitem.Tag
            Select Case args.InstallState
                Case InstallState.Succeed
                    Me.SetIconStatus(appitem, AppIconStatus.Succeed, 100)

                Case InstallState.CancelInstall
                    Me.SetIconStatus(appitem, AppIconStatus.None, 0)

                Case InstallState.UnSucceed, InstallState.HaveNotInstall
                    Me.SetIconStatus(appitem, AppIconStatus.Failure, 0)

                Case InstallState.AllInstallCompleted

                Case Else
                    '用户点修复的时候如果已经下载过的应用就从0开始走进度，如果没下载的应该 下载0-50% 上专50-80%，安装80-100%
                    Dim intProgress As Integer = 0
                    If obj.hasdownload Then
                        If args.InstallState = InstallState.Installing Then
                            intProgress = 60 + (args.TransferProgress * 0.4)
                        Else
                            intProgress = args.TransferProgress * 0.6
                        End If
                    Else
                        If args.InstallState = InstallState.Installing Then
                            intProgress = 80 + (args.TransferProgress * 0.2)
                        Else
                            intProgress = 50 + (args.TransferProgress * 0.3)
                        End If
                    End If

                    If intProgress < appitem.Progress Then
                        intProgress = appitem.Progress
                    End If
                    Me.SetIconStatus(appitem, AppIconStatus.Repairing, intProgress)

            End Select
            appitem.Invalidate()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "mInstallHelper_OnInstall")
        End Try
    End Sub

    Private Sub btnJailbreak_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnJailbreak.Click
        Try
            For Each Item As Form In Application.OpenForms
                If Item IsNot Nothing AndAlso TypeOf Item Is frmSummary AndAlso CType(Item, frmSummary).mDevice Is Me.mDevice Then
                    CType(Me.mApplication, MainForm).GotoItem(ActionFuncType.Welcome)
                    CType(Item, frmSummary).StartJailbreakTool()
                End If
            Next

            Me.WindowState = FormWindowState.Minimized
            If Me.Owner IsNot Nothing AndAlso TypeOf Me.Owner Is frmTools Then
                CType(Me.Owner, frmTools).WindowState = FormWindowState.Minimized
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_Jailbreak")
        End Try

    End Sub

#End Region

#Region "----软件查找----"

    Private Sub txtSearchApp_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChangedByTimer
        Dim ifShowIcon As Boolean = False
        Me.flpContainer.tbSuspendLayout()

        For Each Item As Object In Me.flpContainer.tbControls
            If Item IsNot Nothing AndAlso TypeOf Item Is AppItemRecomEx AndAlso _
               Item.tag IsNot Nothing AndAlso TypeOf Item.tag Is RepairObj Then
                If Me.CheckInclude(Item, Me.txtSearch.Text) Then
                    Item.Visible = True
                    ifShowIcon = True
                Else
                    Item.Visible = False
                End If
            End If
        Next
        Me.flpContainer.VerticalScroll.Value = 0
        Me.flpContainer.tbResumeLayout()
        Me.flpContainer.tbResumeLayout()

        Me.SetGroup(RepairType.CanRepair)
        Me.SetGroup(RepairType.NotNeedRepair)
        Me.SetGroup(RepairType.NotRepair)

        If ifShowIcon Then
            Me.pnlEmpty.SendToBack()
        Else
            Me.pnlEmpty.BringToFront()
        End If
    End Sub

    Private Sub txtNameValue_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtNameValue.TextChanged
        Me.txtSearch.Text = Me.txtNameValue.Text.Trim()
    End Sub

    Private Function CheckInclude(ByVal noteCurrent As AppItemRecomEx, ByVal strSearch As String) As Boolean
        Dim blnReturn As Boolean = False

        strSearch = strSearch.Replace(" ", "").Replace("　", "").ToLower
        Dim strTipTest As String = noteCurrent.TipText.ToLower().Replace(" ", "").Replace("　", "")

        '软件名称是不是是相符的
        If strTipTest.Contains(strSearch) Then
            blnReturn = True
            GoTo DO_EXIST

        End If

        '软件名称中拼音是不是是相符的
        Dim strPinYinSubject As String = PinYinClass.MakePinYin(strTipTest, PinYinOptions.FirstCharacterOnly)
        If strPinYinSubject.StartsWith(strSearch, StringComparison.OrdinalIgnoreCase) Then
            blnReturn = True
            GoTo DO_EXIST
        End If

        Dim strPinYin As String = PinYinClass.MakePinYin(strTipTest, PinYinOptions.Default)
        If strPinYin.StartsWith(strSearch, StringComparison.OrdinalIgnoreCase) Then
            blnReturn = True
            GoTo DO_EXIST
        End If

DO_EXIST:
        Return blnReturn
    End Function

#End Region

    Private Sub lblJailbreak_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblJailbreak.TextChanged
        Me.btnJailbreak.Location = New Point(Me.lblJailbreak.Right + 20, Me.btnJailbreak.Top)
    End Sub

    Private Sub flpContainer_ButtonClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles flpContainer.ButtonClick
        Dim type As RepairType = RepairType.CanRepair
        If sender IsNot Nothing AndAlso TypeOf sender Is tbGroupLabel AndAlso _
           sender.tag IsNot Nothing AndAlso TypeOf sender.tag Is RepairType Then

            type = sender.tag
        End If

        If type <> RepairType.CanRepair Then
            Return
        End If

        For Each Item As AppItemRecomEx In Me.flpContainer.tbControls
            If Item.Status = AppIconStatus.Failure OrElse Item.Status = AppIconStatus.None AndAlso Item.Visible = True Then
                Me.DoRepair(Item)
                Utility.WaitSeconds(0.2)
            End If
        Next
        ActionCollectHelper.ClickStrongRepairAccition(Me.mDevice, ModelKey.SummaryStrongRepairAll)
    End Sub

    Private Sub frmRepair_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Resize
        Me.flpContainer.VerticalScroll.Value = 0

        Me.flpContainer.tbResumeLayout()
        Me.flpContainer.tbResumeLayout()

    End Sub

    Private Sub btnminimize_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnminimize.Click
        Me.WindowState = FormWindowState.Minimized
    End Sub

    Private Sub OnDownloadEventHandler(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        If sender IsNot Nothing AndAlso TypeOf sender Is WebSiteHelperBase.AnalyseDownloadInfo Then
            Dim info As WebSiteHelperBase.AnalyseDownloadInfo = sender
            If info.Status = TaskState.Failed Then
                If Me.mDictCanRepaid.ContainsKey(info.Identifier) AndAlso Me.mItems.ContainsKey(info.Identifier) Then
                    Me.SetIconStatus(Me.mItems(info.Identifier), AppIconStatus.Failure, 0)
                    Dim obj As RepairObj = Me.mDictCanRepaid(info.Identifier)
                    If obj IsNot Nothing AndAlso obj.repairInfo IsNot Nothing Then
                        obj.repairInfo.ShowLogingAppleId = True
                    End If
                End If
            End If
        End If
    End Sub

    Private Function GetItemByID(ByVal strIdnetify As String, ByVal strItemid As String) As AppItemRecomEx
        Dim appItem As AppItemRecomEx = Nothing
        Try
            If Me.mItems.ContainsKey(strIdnetify) Then
                appItem = Me.mItems(strIdnetify)

            ElseIf strItemid.Length > 0 Then
                For Each Item As AppItemRecomEx In Me.mItems.Values
                    If Item IsNot Nothing AndAlso Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is RepairObj Then
                        Dim obj As RepairObj = Item.Tag
                        If obj.repairInfo.Itemid = strItemid Then
                            appItem = Item
                            Exit For
                        End If
                    End If
                Next

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmRepair_GetItemByID")
        End Try
        Return appItem
    End Function
    
End Class

Public Class RepairObj
    Public pkgInfo As PackageInfo = Nothing
    Public repairInfo As RepairPackageInfo = Nothing
    Public hasdownload As Boolean = False                   '标记软件是不是在本地存在
End Class