﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmBatteryManager
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmBatteryManager))
        Me.pnlMain = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlBattery = New iTong.Components.tbPanel()
        Me.tbBatteryProgress = New iTong.tbBatteryProgress()
        Me.btnTrickleCharge = New iTong.Components.tbRadioButton()
        Me.btnQuickCharge = New iTong.Components.tbRadioButton()
        Me.btnContinueCharge = New iTong.Components.tbRadioButton()
        Me.pbDevice = New System.Windows.Forms.PictureBox()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.lblHint = New System.Windows.Forms.Label()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.lblBattreyProgress = New iTong.tbBattery()
        Me.lblChargeRecordCountEx = New System.Windows.Forms.Label()
        Me.lblChargeRecordCount = New System.Windows.Forms.Label()
        Me.lblChargeRecord = New System.Windows.Forms.Label()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.TbButton2 = New iTong.Components.tbButton()
        Me.TbButton1 = New iTong.Components.tbButton()
        Me.lblSerialNumberEx = New System.Windows.Forms.Label()
        Me.lblSerialNumber = New System.Windows.Forms.Label()
        Me.lblCompanyEx = New System.Windows.Forms.Label()
        Me.lblCompany = New System.Windows.Forms.Label()
        Me.lblElectricityEx = New System.Windows.Forms.Label()
        Me.lblElectricity = New System.Windows.Forms.Label()
        Me.lblTemperatureEx = New System.Windows.Forms.Label()
        Me.lblTemperature = New System.Windows.Forms.Label()
        Me.lblEfficiencyEx = New System.Windows.Forms.Label()
        Me.lblEfficiency = New System.Windows.Forms.Label()
        Me.lblStartVmainEx = New System.Windows.Forms.Label()
        Me.lblStartVmain = New System.Windows.Forms.Label()
        Me.lblVmainEx = New System.Windows.Forms.Label()
        Me.lblVmain = New System.Windows.Forms.Label()
        Me.lblDesignCapacityEx = New System.Windows.Forms.Label()
        Me.lblDesignCapacity = New System.Windows.Forms.Label()
        Me.lblActualCapacityEx = New System.Windows.Forms.Label()
        Me.lblActualCapacity = New System.Windows.Forms.Label()
        Me.lblChargeReport = New System.Windows.Forms.Label()
        Me.pnlMain.SuspendLayout()
        Me.pnlBattery.SuspendLayout()
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.Panel3.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(771, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(747, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(723, 0)
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.pnlMain.ColumnCount = 2
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 27.69423!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 520.0!))
        Me.pnlMain.Controls.Add(Me.pnlBattery, 0, 0)
        Me.pnlMain.Controls.Add(Me.Panel1, 1, 2)
        Me.pnlMain.Controls.Add(Me.Panel2, 1, 0)
        Me.pnlMain.Controls.Add(Me.Panel3, 1, 1)
        Me.pnlMain.Location = New System.Drawing.Point(1, 32)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.RowCount = 3
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 334.0!))
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 67.0!))
        Me.pnlMain.Size = New System.Drawing.Size(793, 541)
        Me.pnlMain.TabIndex = 22
        '
        'pnlBattery
        '
        Me.pnlBattery.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlBattery.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlBattery.Controls.Add(Me.tbBatteryProgress)
        Me.pnlBattery.Controls.Add(Me.btnTrickleCharge)
        Me.pnlBattery.Controls.Add(Me.btnQuickCharge)
        Me.pnlBattery.Controls.Add(Me.btnContinueCharge)
        Me.pnlBattery.Controls.Add(Me.pbDevice)
        Me.pnlBattery.Location = New System.Drawing.Point(0, 0)
        Me.pnlBattery.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBattery.Name = "pnlBattery"
        Me.pnlMain.SetRowSpan(Me.pnlBattery, 3)
        Me.pnlBattery.Size = New System.Drawing.Size(273, 541)
        Me.pnlBattery.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBattery.TabIndex = 0
        Me.pnlBattery.tbBackgroundImage = Nothing
        Me.pnlBattery.tbShowWatermark = False
        Me.pnlBattery.tbSplit = "0,0,0,0"
        Me.pnlBattery.tbWatermark = Nothing
        Me.pnlBattery.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBattery.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'tbBatteryProgress
        '
        Me.tbBatteryProgress.Location = New System.Drawing.Point(71, 152)
        Me.tbBatteryProgress.Margin = New System.Windows.Forms.Padding(0)
        Me.tbBatteryProgress.Name = "tbBatteryProgress"
        Me.tbBatteryProgress.Size = New System.Drawing.Size(131, 149)
        Me.tbBatteryProgress.TabIndex = 6
        Me.tbBatteryProgress.tbChargedTitle = ""
        Me.tbBatteryProgress.tbCurrentProgressValue = -1
        Me.tbBatteryProgress.tbProgressValue = "0%"
        Me.tbBatteryProgress.Text = "TbBatteryProgress1"
        '
        'btnTrickleCharge
        '
        Me.btnTrickleCharge.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnTrickleCharge.CheckAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnTrickleCharge.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.btnTrickleCharge.Location = New System.Drawing.Point(176, 423)
        Me.btnTrickleCharge.Name = "btnTrickleCharge"
        Me.btnTrickleCharge.Padding = New System.Windows.Forms.Padding(0, 0, 0, 5)
        Me.btnTrickleCharge.Size = New System.Drawing.Size(80, 68)
        Me.btnTrickleCharge.TabIndex = 7
        Me.btnTrickleCharge.tbAdriftIconWhenHover = False
        Me.btnTrickleCharge.tbAutoSize = False
        Me.btnTrickleCharge.tbAutoSizeEx = False
        Me.btnTrickleCharge.tbBadgeNumber = 0
        Me.btnTrickleCharge.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTrickleCharge.tbEnableToolTip = False
        Me.btnTrickleCharge.tbIconCheckedMouseDown = Nothing
        Me.btnTrickleCharge.tbIconCheckedMouseHover = Nothing
        Me.btnTrickleCharge.tbIconCheckedMouseLeave = Nothing
        Me.btnTrickleCharge.tbIconHoldPlace = False
        Me.btnTrickleCharge.tbIconImage = Global.iTong.My.Resources.Resources.btn_continuecharge
        Me.btnTrickleCharge.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnTrickleCharge.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnTrickleCharge.tbIconMore = False
        Me.btnTrickleCharge.tbIconMoreImage = CType(resources.GetObject("btnTrickleCharge.tbIconMoreImage"), System.Drawing.Image)
        Me.btnTrickleCharge.tbIconPlaceText = 5
        Me.btnTrickleCharge.tbIconReadOnly = Nothing
        Me.btnTrickleCharge.tbIconReadOnlyLight = Nothing
        Me.btnTrickleCharge.tbIconUnCheckedMouseDown = Nothing
        Me.btnTrickleCharge.tbIconUnCheckedMouseHover = Nothing
        Me.btnTrickleCharge.tbIconUnCheckedMouseLeave = Nothing
        Me.btnTrickleCharge.tbImageBackground = Nothing
        Me.btnTrickleCharge.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnTrickleCharge.tbImageCheckedMouseDown = Nothing
        Me.btnTrickleCharge.tbImageCheckedMouseHover = Nothing
        Me.btnTrickleCharge.tbImageCheckedMouseLeave = Nothing
        Me.btnTrickleCharge.tbImageUnCheckedMouseDown = Nothing
        Me.btnTrickleCharge.tbImageUnCheckedMouseHover = Nothing
        Me.btnTrickleCharge.tbImageUnCheckedMouseLeave = Nothing
        Me.btnTrickleCharge.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnTrickleCharge.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnTrickleCharge.tbReadOnly = False
        Me.btnTrickleCharge.tbShadow = False
        Me.btnTrickleCharge.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTrickleCharge.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTrickleCharge.tbShowBadgeNumber = True
        Me.btnTrickleCharge.tbShowBadgeNumberWhenSelection = True
        Me.btnTrickleCharge.tbShowNew = False
        Me.btnTrickleCharge.tbSplit = "3,3,3,3"
        Me.btnTrickleCharge.tbTag = Nothing
        Me.btnTrickleCharge.tbToolTip = ""
        Me.btnTrickleCharge.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnTrickleCharge.Text = "涓流充电"
        Me.btnTrickleCharge.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.btnTrickleCharge.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText
        Me.btnTrickleCharge.UseVisualStyleBackColor = True
        '
        'btnQuickCharge
        '
        Me.btnQuickCharge.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnQuickCharge.CheckAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnQuickCharge.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.btnQuickCharge.Location = New System.Drawing.Point(16, 423)
        Me.btnQuickCharge.Name = "btnQuickCharge"
        Me.btnQuickCharge.Padding = New System.Windows.Forms.Padding(0, 0, 0, 5)
        Me.btnQuickCharge.Size = New System.Drawing.Size(80, 68)
        Me.btnQuickCharge.TabIndex = 6
        Me.btnQuickCharge.tbAdriftIconWhenHover = False
        Me.btnQuickCharge.tbAutoSize = False
        Me.btnQuickCharge.tbAutoSizeEx = False
        Me.btnQuickCharge.tbBadgeNumber = 0
        Me.btnQuickCharge.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnQuickCharge.tbEnableToolTip = False
        Me.btnQuickCharge.tbIconCheckedMouseDown = Nothing
        Me.btnQuickCharge.tbIconCheckedMouseHover = Nothing
        Me.btnQuickCharge.tbIconCheckedMouseLeave = Nothing
        Me.btnQuickCharge.tbIconHoldPlace = False
        Me.btnQuickCharge.tbIconImage = Global.iTong.My.Resources.Resources.btn_continuecharge
        Me.btnQuickCharge.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnQuickCharge.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnQuickCharge.tbIconMore = False
        Me.btnQuickCharge.tbIconMoreImage = CType(resources.GetObject("btnQuickCharge.tbIconMoreImage"), System.Drawing.Image)
        Me.btnQuickCharge.tbIconPlaceText = 5
        Me.btnQuickCharge.tbIconReadOnly = Nothing
        Me.btnQuickCharge.tbIconReadOnlyLight = Nothing
        Me.btnQuickCharge.tbIconUnCheckedMouseDown = Nothing
        Me.btnQuickCharge.tbIconUnCheckedMouseHover = Nothing
        Me.btnQuickCharge.tbIconUnCheckedMouseLeave = Nothing
        Me.btnQuickCharge.tbImageBackground = Nothing
        Me.btnQuickCharge.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnQuickCharge.tbImageCheckedMouseDown = Nothing
        Me.btnQuickCharge.tbImageCheckedMouseHover = Nothing
        Me.btnQuickCharge.tbImageCheckedMouseLeave = Nothing
        Me.btnQuickCharge.tbImageUnCheckedMouseDown = Nothing
        Me.btnQuickCharge.tbImageUnCheckedMouseHover = Nothing
        Me.btnQuickCharge.tbImageUnCheckedMouseLeave = Nothing
        Me.btnQuickCharge.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnQuickCharge.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnQuickCharge.tbReadOnly = False
        Me.btnQuickCharge.tbShadow = False
        Me.btnQuickCharge.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnQuickCharge.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnQuickCharge.tbShowBadgeNumber = True
        Me.btnQuickCharge.tbShowBadgeNumberWhenSelection = True
        Me.btnQuickCharge.tbShowNew = False
        Me.btnQuickCharge.tbSplit = "3,3,3,3"
        Me.btnQuickCharge.tbTag = Nothing
        Me.btnQuickCharge.tbToolTip = ""
        Me.btnQuickCharge.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnQuickCharge.Text = "快速充电"
        Me.btnQuickCharge.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.btnQuickCharge.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText
        Me.btnQuickCharge.UseVisualStyleBackColor = True
        '
        'btnContinueCharge
        '
        Me.btnContinueCharge.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnContinueCharge.CheckAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnContinueCharge.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.btnContinueCharge.Location = New System.Drawing.Point(96, 423)
        Me.btnContinueCharge.Name = "btnContinueCharge"
        Me.btnContinueCharge.Padding = New System.Windows.Forms.Padding(0, 0, 0, 5)
        Me.btnContinueCharge.Size = New System.Drawing.Size(80, 68)
        Me.btnContinueCharge.TabIndex = 3
        Me.btnContinueCharge.tbAdriftIconWhenHover = False
        Me.btnContinueCharge.tbAutoSize = False
        Me.btnContinueCharge.tbAutoSizeEx = False
        Me.btnContinueCharge.tbBadgeNumber = 0
        Me.btnContinueCharge.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnContinueCharge.tbEnableToolTip = False
        Me.btnContinueCharge.tbIconCheckedMouseDown = Nothing
        Me.btnContinueCharge.tbIconCheckedMouseHover = Nothing
        Me.btnContinueCharge.tbIconCheckedMouseLeave = Nothing
        Me.btnContinueCharge.tbIconHoldPlace = False
        Me.btnContinueCharge.tbIconImage = Global.iTong.My.Resources.Resources.btn_continuecharge
        Me.btnContinueCharge.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnContinueCharge.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnContinueCharge.tbIconMore = False
        Me.btnContinueCharge.tbIconMoreImage = CType(resources.GetObject("btnContinueCharge.tbIconMoreImage"), System.Drawing.Image)
        Me.btnContinueCharge.tbIconPlaceText = 5
        Me.btnContinueCharge.tbIconReadOnly = Nothing
        Me.btnContinueCharge.tbIconReadOnlyLight = Nothing
        Me.btnContinueCharge.tbIconUnCheckedMouseDown = Nothing
        Me.btnContinueCharge.tbIconUnCheckedMouseHover = Nothing
        Me.btnContinueCharge.tbIconUnCheckedMouseLeave = Nothing
        Me.btnContinueCharge.tbImageBackground = Nothing
        Me.btnContinueCharge.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnContinueCharge.tbImageCheckedMouseDown = Nothing
        Me.btnContinueCharge.tbImageCheckedMouseHover = Nothing
        Me.btnContinueCharge.tbImageCheckedMouseLeave = Nothing
        Me.btnContinueCharge.tbImageUnCheckedMouseDown = Nothing
        Me.btnContinueCharge.tbImageUnCheckedMouseHover = Nothing
        Me.btnContinueCharge.tbImageUnCheckedMouseLeave = Nothing
        Me.btnContinueCharge.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnContinueCharge.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnContinueCharge.tbReadOnly = False
        Me.btnContinueCharge.tbShadow = False
        Me.btnContinueCharge.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnContinueCharge.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnContinueCharge.tbShowBadgeNumber = True
        Me.btnContinueCharge.tbShowBadgeNumberWhenSelection = True
        Me.btnContinueCharge.tbShowNew = False
        Me.btnContinueCharge.tbSplit = "3,3,3,3"
        Me.btnContinueCharge.tbTag = Nothing
        Me.btnContinueCharge.tbToolTip = ""
        Me.btnContinueCharge.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnContinueCharge.Text = "连续充电"
        Me.btnContinueCharge.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.btnContinueCharge.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageAboveText
        Me.btnContinueCharge.UseVisualStyleBackColor = True
        '
        'pbDevice
        '
        Me.pbDevice.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.pbDevice.Location = New System.Drawing.Point(16, 53)
        Me.pbDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.pbDevice.Name = "pbDevice"
        Me.pbDevice.Size = New System.Drawing.Size(240, 350)
        Me.pbDevice.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.pbDevice.TabIndex = 3
        Me.pbDevice.TabStop = False
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.Panel1.Controls.Add(Me.lblHint)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel1.Location = New System.Drawing.Point(273, 474)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(520, 67)
        Me.Panel1.TabIndex = 2
        '
        'lblHint
        '
        Me.lblHint.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblHint.Location = New System.Drawing.Point(25, 8)
        Me.lblHint.Margin = New System.Windows.Forms.Padding(0)
        Me.lblHint.Name = "lblHint"
        Me.lblHint.Size = New System.Drawing.Size(474, 39)
        Me.lblHint.TabIndex = 2
        Me.lblHint.Text = "定期进行完全循环充电，有利于锂电池的保养，建议您每周使用同步助手循环充电一次，有效延长锂电池的使用寿命。"
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.Panel2.Controls.Add(Me.lblBattreyProgress)
        Me.Panel2.Controls.Add(Me.lblChargeRecordCountEx)
        Me.Panel2.Controls.Add(Me.lblChargeRecordCount)
        Me.Panel2.Controls.Add(Me.lblChargeRecord)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(273, 0)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(520, 140)
        Me.Panel2.TabIndex = 3
        '
        'lblBattreyProgress
        '
        Me.lblBattreyProgress.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblBattreyProgress.Location = New System.Drawing.Point(13, 34)
        Me.lblBattreyProgress.Margin = New System.Windows.Forms.Padding(0, 0, 3, 0)
        Me.lblBattreyProgress.Name = "lblBattreyProgress"
        Me.lblBattreyProgress.Size = New System.Drawing.Size(499, 41)
        Me.lblBattreyProgress.TabIndex = 2
        '
        'lblChargeRecordCountEx
        '
        Me.lblChargeRecordCountEx.AutoSize = True
        Me.lblChargeRecordCountEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblChargeRecordCountEx.Location = New System.Drawing.Point(238, 115)
        Me.lblChargeRecordCountEx.Name = "lblChargeRecordCountEx"
        Me.lblChargeRecordCountEx.Size = New System.Drawing.Size(101, 12)
        Me.lblChargeRecordCountEx.TabIndex = 1
        Me.lblChargeRecordCountEx.Text = "历史循环充电次数"
        '
        'lblChargeRecordCount
        '
        Me.lblChargeRecordCount.AutoSize = True
        Me.lblChargeRecordCount.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblChargeRecordCount.Location = New System.Drawing.Point(25, 115)
        Me.lblChargeRecordCount.Name = "lblChargeRecordCount"
        Me.lblChargeRecordCount.Size = New System.Drawing.Size(101, 12)
        Me.lblChargeRecordCount.TabIndex = 1
        Me.lblChargeRecordCount.Text = "历史循环充电次数"
        '
        'lblChargeRecord
        '
        Me.lblChargeRecord.AutoSize = True
        Me.lblChargeRecord.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblChargeRecord.ForeColor = System.Drawing.Color.FromArgb(CType(CType(85, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(85, Byte), Integer))
        Me.lblChargeRecord.Location = New System.Drawing.Point(24, 85)
        Me.lblChargeRecord.Name = "lblChargeRecord"
        Me.lblChargeRecord.Size = New System.Drawing.Size(97, 14)
        Me.lblChargeRecord.TabIndex = 0
        Me.lblChargeRecord.Text = "电池充电记录"
        '
        'Panel3
        '
        Me.Panel3.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.Panel3.Controls.Add(Me.TbButton2)
        Me.Panel3.Controls.Add(Me.TbButton1)
        Me.Panel3.Controls.Add(Me.lblSerialNumberEx)
        Me.Panel3.Controls.Add(Me.lblSerialNumber)
        Me.Panel3.Controls.Add(Me.lblCompanyEx)
        Me.Panel3.Controls.Add(Me.lblCompany)
        Me.Panel3.Controls.Add(Me.lblElectricityEx)
        Me.Panel3.Controls.Add(Me.lblElectricity)
        Me.Panel3.Controls.Add(Me.lblTemperatureEx)
        Me.Panel3.Controls.Add(Me.lblTemperature)
        Me.Panel3.Controls.Add(Me.lblEfficiencyEx)
        Me.Panel3.Controls.Add(Me.lblEfficiency)
        Me.Panel3.Controls.Add(Me.lblStartVmainEx)
        Me.Panel3.Controls.Add(Me.lblStartVmain)
        Me.Panel3.Controls.Add(Me.lblVmainEx)
        Me.Panel3.Controls.Add(Me.lblVmain)
        Me.Panel3.Controls.Add(Me.lblDesignCapacityEx)
        Me.Panel3.Controls.Add(Me.lblDesignCapacity)
        Me.Panel3.Controls.Add(Me.lblActualCapacityEx)
        Me.Panel3.Controls.Add(Me.lblActualCapacity)
        Me.Panel3.Controls.Add(Me.lblChargeReport)
        Me.Panel3.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel3.Location = New System.Drawing.Point(273, 140)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(520, 334)
        Me.Panel3.TabIndex = 4
        '
        'TbButton2
        '
        Me.TbButton2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton2.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.TbButton2.BindingForm = Nothing
        Me.TbButton2.Location = New System.Drawing.Point(25, 327)
        Me.TbButton2.Name = "TbButton2"
        Me.TbButton2.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton2.Selectable = True
        Me.TbButton2.Size = New System.Drawing.Size(470, 1)
        Me.TbButton2.TabIndex = 3
        Me.TbButton2.tbAdriftIconWhenHover = False
        Me.TbButton2.tbAutoSize = False
        Me.TbButton2.tbAutoSizeEx = False
        Me.TbButton2.tbBackgroundImage = Global.iTong.My.Resources.Resources.icon_shadow_iphone
        Me.TbButton2.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton2.tbBadgeNumber = 0
        Me.TbButton2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton2.tbEndEllipsis = False
        Me.TbButton2.tbIconHoldPlace = True
        Me.TbButton2.tbIconImage = Nothing
        Me.TbButton2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton2.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton2.tbIconMore = False
        Me.TbButton2.tbIconMouseDown = Nothing
        Me.TbButton2.tbIconMouseHover = Nothing
        Me.TbButton2.tbIconMouseLeave = Nothing
        Me.TbButton2.tbIconPlaceText = 2
        Me.TbButton2.tbIconReadOnly = Nothing
        Me.TbButton2.tbImageMouseDown = Nothing
        Me.TbButton2.tbImageMouseHover = Nothing
        Me.TbButton2.tbImageMouseLeave = Nothing
        Me.TbButton2.tbProgressValue = 50
        Me.TbButton2.tbReadOnly = False
        Me.TbButton2.tbReadOnlyText = False
        Me.TbButton2.tbShadow = False
        Me.TbButton2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton2.tbShowDot = False
        Me.TbButton2.tbShowMoreIconImg = CType(resources.GetObject("TbButton2.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton2.tbShowNew = False
        Me.TbButton2.tbShowProgress = False
        Me.TbButton2.tbShowTip = True
        Me.TbButton2.tbShowToolTipOnButton = False
        Me.TbButton2.tbSplit = "35,0,35,0"
        Me.TbButton2.tbText = ""
        Me.TbButton2.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton2.tbTextColor = System.Drawing.Color.White
        Me.TbButton2.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton2.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton2.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton2.tbTextMouseDownPlace = 0
        Me.TbButton2.tbToolTip = ""
        Me.TbButton2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton2.VisibleEx = True
        '
        'TbButton1
        '
        Me.TbButton1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton1.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(228, Byte), Integer))
        Me.TbButton1.BindingForm = Nothing
        Me.TbButton1.Location = New System.Drawing.Point(27, 7)
        Me.TbButton1.Name = "TbButton1"
        Me.TbButton1.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton1.Selectable = True
        Me.TbButton1.Size = New System.Drawing.Size(470, 1)
        Me.TbButton1.TabIndex = 2
        Me.TbButton1.tbAdriftIconWhenHover = False
        Me.TbButton1.tbAutoSize = False
        Me.TbButton1.tbAutoSizeEx = False
        Me.TbButton1.tbBackgroundImage = Global.iTong.My.Resources.Resources.icon_shadow_iphone
        Me.TbButton1.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton1.tbBadgeNumber = 0
        Me.TbButton1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton1.tbEndEllipsis = False
        Me.TbButton1.tbIconHoldPlace = True
        Me.TbButton1.tbIconImage = Nothing
        Me.TbButton1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton1.tbIconMore = False
        Me.TbButton1.tbIconMouseDown = Nothing
        Me.TbButton1.tbIconMouseHover = Nothing
        Me.TbButton1.tbIconMouseLeave = Nothing
        Me.TbButton1.tbIconPlaceText = 2
        Me.TbButton1.tbIconReadOnly = Nothing
        Me.TbButton1.tbImageMouseDown = Nothing
        Me.TbButton1.tbImageMouseHover = Nothing
        Me.TbButton1.tbImageMouseLeave = Nothing
        Me.TbButton1.tbProgressValue = 50
        Me.TbButton1.tbReadOnly = False
        Me.TbButton1.tbReadOnlyText = False
        Me.TbButton1.tbShadow = False
        Me.TbButton1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton1.tbShowDot = False
        Me.TbButton1.tbShowMoreIconImg = CType(resources.GetObject("TbButton1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton1.tbShowNew = False
        Me.TbButton1.tbShowProgress = False
        Me.TbButton1.tbShowTip = True
        Me.TbButton1.tbShowToolTipOnButton = False
        Me.TbButton1.tbSplit = "35,0,35,0"
        Me.TbButton1.tbText = ""
        Me.TbButton1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbTextColor = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton1.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton1.tbTextMouseDownPlace = 0
        Me.TbButton1.tbToolTip = ""
        Me.TbButton1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.VisibleEx = True
        '
        'lblSerialNumberEx
        '
        Me.lblSerialNumberEx.AutoSize = True
        Me.lblSerialNumberEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblSerialNumberEx.Location = New System.Drawing.Point(238, 294)
        Me.lblSerialNumberEx.Name = "lblSerialNumberEx"
        Me.lblSerialNumberEx.Size = New System.Drawing.Size(65, 12)
        Me.lblSerialNumberEx.TabIndex = 1
        Me.lblSerialNumberEx.Text = "电池序列号"
        '
        'lblSerialNumber
        '
        Me.lblSerialNumber.AutoSize = True
        Me.lblSerialNumber.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblSerialNumber.Location = New System.Drawing.Point(25, 294)
        Me.lblSerialNumber.Name = "lblSerialNumber"
        Me.lblSerialNumber.Size = New System.Drawing.Size(65, 12)
        Me.lblSerialNumber.TabIndex = 1
        Me.lblSerialNumber.Text = "电池序列号"
        '
        'lblCompanyEx
        '
        Me.lblCompanyEx.AutoSize = True
        Me.lblCompanyEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblCompanyEx.Location = New System.Drawing.Point(238, 265)
        Me.lblCompanyEx.Name = "lblCompanyEx"
        Me.lblCompanyEx.Size = New System.Drawing.Size(53, 12)
        Me.lblCompanyEx.TabIndex = 1
        Me.lblCompanyEx.Text = "电池厂商"
        '
        'lblCompany
        '
        Me.lblCompany.AutoSize = True
        Me.lblCompany.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblCompany.Location = New System.Drawing.Point(25, 265)
        Me.lblCompany.Name = "lblCompany"
        Me.lblCompany.Size = New System.Drawing.Size(53, 12)
        Me.lblCompany.TabIndex = 1
        Me.lblCompany.Text = "电池厂商"
        '
        'lblElectricityEx
        '
        Me.lblElectricityEx.AutoSize = True
        Me.lblElectricityEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblElectricityEx.Location = New System.Drawing.Point(238, 236)
        Me.lblElectricityEx.Name = "lblElectricityEx"
        Me.lblElectricityEx.Size = New System.Drawing.Size(53, 12)
        Me.lblElectricityEx.TabIndex = 1
        Me.lblElectricityEx.Text = "电池电流"
        '
        'lblElectricity
        '
        Me.lblElectricity.AutoSize = True
        Me.lblElectricity.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblElectricity.Location = New System.Drawing.Point(25, 236)
        Me.lblElectricity.Name = "lblElectricity"
        Me.lblElectricity.Size = New System.Drawing.Size(53, 12)
        Me.lblElectricity.TabIndex = 1
        Me.lblElectricity.Text = "电池电流"
        '
        'lblTemperatureEx
        '
        Me.lblTemperatureEx.AutoSize = True
        Me.lblTemperatureEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblTemperatureEx.Location = New System.Drawing.Point(238, 207)
        Me.lblTemperatureEx.Name = "lblTemperatureEx"
        Me.lblTemperatureEx.Size = New System.Drawing.Size(53, 12)
        Me.lblTemperatureEx.TabIndex = 1
        Me.lblTemperatureEx.Text = "电池温度"
        '
        'lblTemperature
        '
        Me.lblTemperature.AutoSize = True
        Me.lblTemperature.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblTemperature.Location = New System.Drawing.Point(25, 207)
        Me.lblTemperature.Name = "lblTemperature"
        Me.lblTemperature.Size = New System.Drawing.Size(53, 12)
        Me.lblTemperature.TabIndex = 1
        Me.lblTemperature.Text = "电池温度"
        '
        'lblEfficiencyEx
        '
        Me.lblEfficiencyEx.AutoSize = True
        Me.lblEfficiencyEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblEfficiencyEx.Location = New System.Drawing.Point(238, 178)
        Me.lblEfficiencyEx.Name = "lblEfficiencyEx"
        Me.lblEfficiencyEx.Size = New System.Drawing.Size(53, 12)
        Me.lblEfficiencyEx.TabIndex = 1
        Me.lblEfficiencyEx.Text = "电池效率"
        '
        'lblEfficiency
        '
        Me.lblEfficiency.AutoSize = True
        Me.lblEfficiency.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblEfficiency.Location = New System.Drawing.Point(25, 178)
        Me.lblEfficiency.Name = "lblEfficiency"
        Me.lblEfficiency.Size = New System.Drawing.Size(53, 12)
        Me.lblEfficiency.TabIndex = 1
        Me.lblEfficiency.Text = "电池效率"
        '
        'lblStartVmainEx
        '
        Me.lblStartVmainEx.AutoSize = True
        Me.lblStartVmainEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblStartVmainEx.Location = New System.Drawing.Point(238, 149)
        Me.lblStartVmainEx.Name = "lblStartVmainEx"
        Me.lblStartVmainEx.Size = New System.Drawing.Size(53, 12)
        Me.lblStartVmainEx.TabIndex = 1
        Me.lblStartVmainEx.Text = "开机电压"
        '
        'lblStartVmain
        '
        Me.lblStartVmain.AutoSize = True
        Me.lblStartVmain.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblStartVmain.Location = New System.Drawing.Point(25, 149)
        Me.lblStartVmain.Name = "lblStartVmain"
        Me.lblStartVmain.Size = New System.Drawing.Size(53, 12)
        Me.lblStartVmain.TabIndex = 1
        Me.lblStartVmain.Text = "开机电压"
        '
        'lblVmainEx
        '
        Me.lblVmainEx.AutoSize = True
        Me.lblVmainEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblVmainEx.Location = New System.Drawing.Point(238, 120)
        Me.lblVmainEx.Name = "lblVmainEx"
        Me.lblVmainEx.Size = New System.Drawing.Size(53, 12)
        Me.lblVmainEx.TabIndex = 1
        Me.lblVmainEx.Text = "当前电压"
        '
        'lblVmain
        '
        Me.lblVmain.AutoSize = True
        Me.lblVmain.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblVmain.Location = New System.Drawing.Point(25, 120)
        Me.lblVmain.Name = "lblVmain"
        Me.lblVmain.Size = New System.Drawing.Size(53, 12)
        Me.lblVmain.TabIndex = 1
        Me.lblVmain.Text = "当前电压"
        '
        'lblDesignCapacityEx
        '
        Me.lblDesignCapacityEx.AutoSize = True
        Me.lblDesignCapacityEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblDesignCapacityEx.Location = New System.Drawing.Point(238, 91)
        Me.lblDesignCapacityEx.Name = "lblDesignCapacityEx"
        Me.lblDesignCapacityEx.Size = New System.Drawing.Size(53, 12)
        Me.lblDesignCapacityEx.TabIndex = 1
        Me.lblDesignCapacityEx.Text = "设计容量"
        '
        'lblDesignCapacity
        '
        Me.lblDesignCapacity.AutoSize = True
        Me.lblDesignCapacity.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblDesignCapacity.Location = New System.Drawing.Point(25, 91)
        Me.lblDesignCapacity.Name = "lblDesignCapacity"
        Me.lblDesignCapacity.Size = New System.Drawing.Size(53, 12)
        Me.lblDesignCapacity.TabIndex = 1
        Me.lblDesignCapacity.Text = "设计容量"
        '
        'lblActualCapacityEx
        '
        Me.lblActualCapacityEx.AutoSize = True
        Me.lblActualCapacityEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblActualCapacityEx.Location = New System.Drawing.Point(238, 62)
        Me.lblActualCapacityEx.Name = "lblActualCapacityEx"
        Me.lblActualCapacityEx.Size = New System.Drawing.Size(53, 12)
        Me.lblActualCapacityEx.TabIndex = 1
        Me.lblActualCapacityEx.Text = "实际容量"
        '
        'lblActualCapacity
        '
        Me.lblActualCapacity.AutoSize = True
        Me.lblActualCapacity.ForeColor = System.Drawing.Color.FromArgb(CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblActualCapacity.Location = New System.Drawing.Point(25, 62)
        Me.lblActualCapacity.Name = "lblActualCapacity"
        Me.lblActualCapacity.Size = New System.Drawing.Size(53, 12)
        Me.lblActualCapacity.TabIndex = 1
        Me.lblActualCapacity.Text = "实际容量"
        '
        'lblChargeReport
        '
        Me.lblChargeReport.AutoSize = True
        Me.lblChargeReport.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblChargeReport.ForeColor = System.Drawing.Color.FromArgb(CType(CType(85, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(85, Byte), Integer))
        Me.lblChargeReport.Location = New System.Drawing.Point(22, 29)
        Me.lblChargeReport.Name = "lblChargeReport"
        Me.lblChargeReport.Size = New System.Drawing.Size(97, 14)
        Me.lblChargeReport.TabIndex = 0
        Me.lblChargeReport.Text = "电池体检报告"
        '
        'frmBatteryManager
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(795, 600)
        Me.Controls.Add(Me.pnlMain)
        Me.Name = "frmBatteryManager"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "省电优化"
        Me.Controls.SetChildIndex(Me.pnlMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlMain.ResumeLayout(False)
        Me.pnlBattery.ResumeLayout(False)
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.Panel3.ResumeLayout(False)
        Me.Panel3.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlBattery As iTong.Components.tbPanel
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents lblHint As System.Windows.Forms.Label
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents lblChargeRecord As System.Windows.Forms.Label
    Friend WithEvents lblChargeReport As System.Windows.Forms.Label
    Friend WithEvents TbButton1 As iTong.Components.tbButton
    Friend WithEvents lblChargeRecordCount As System.Windows.Forms.Label
    Friend WithEvents lblSerialNumber As System.Windows.Forms.Label
    Friend WithEvents lblCompany As System.Windows.Forms.Label
    Friend WithEvents lblElectricity As System.Windows.Forms.Label
    Friend WithEvents lblTemperature As System.Windows.Forms.Label
    Friend WithEvents lblEfficiency As System.Windows.Forms.Label
    Friend WithEvents lblStartVmain As System.Windows.Forms.Label
    Friend WithEvents lblVmain As System.Windows.Forms.Label
    Friend WithEvents lblDesignCapacity As System.Windows.Forms.Label
    Friend WithEvents lblActualCapacity As System.Windows.Forms.Label
    Friend WithEvents TbButton2 As iTong.Components.tbButton
    Friend WithEvents pbDevice As System.Windows.Forms.PictureBox
    Friend WithEvents lblBattreyProgress As iTong.tbBattery
    Friend WithEvents tbBatteryProgress As iTong.tbBatteryProgress
    Friend WithEvents btnContinueCharge As iTong.Components.tbRadioButton
    Friend WithEvents btnTrickleCharge As iTong.Components.tbRadioButton
    Friend WithEvents btnQuickCharge As iTong.Components.tbRadioButton
    Friend WithEvents lblChargeRecordCountEx As System.Windows.Forms.Label
    Friend WithEvents lblSerialNumberEx As System.Windows.Forms.Label
    Friend WithEvents lblCompanyEx As System.Windows.Forms.Label
    Friend WithEvents lblElectricityEx As System.Windows.Forms.Label
    Friend WithEvents lblTemperatureEx As System.Windows.Forms.Label
    Friend WithEvents lblEfficiencyEx As System.Windows.Forms.Label
    Friend WithEvents lblStartVmainEx As System.Windows.Forms.Label
    Friend WithEvents lblVmainEx As System.Windows.Forms.Label
    Friend WithEvents lblDesignCapacityEx As System.Windows.Forms.Label
    Friend WithEvents lblActualCapacityEx As System.Windows.Forms.Label
End Class
