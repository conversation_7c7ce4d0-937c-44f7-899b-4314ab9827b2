﻿Imports System.Threading

Public Class frmTools

    Private Enum RepairStatus
        Start
        Fail
        Finished
    End Enum

    Private mType As ToolsType = ToolsType.RepairAuthorize
    Private mAuthorizeHelper As AuthorizeHelper
    Private mAuthorizeArgs As AuthorizeEventArgs
    Private mIsHasJumpToCourse As Boolean = False
    Private bgwParent As tbBackgroundWorker   '（ClearRubbish、RepairSpringboard、ClearErrorIcon的父对象）
    Private mIsHasCheckTui As Boolean = False      '是否检查过
    'Private intRepairHeight As Integer = 400
    Private mTimerCount As Integer = 0
    Private mAuthorizeResult As SoftDownloadErrorState = SoftDownloadErrorState.AuthNormal
    Private mIsStrongRepairMode As Boolean = False  '国内版本走强力修复模式， 国外还是原来逻辑
    'Private mDnsAddress As String() = Nothing
    'Private misRetry As Boolean = False

#Region "---  初始化  ---"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice, ByVal type As ToolsType)
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mDevice = device
        Me.mApplication = app
        Me.Language = app.Language
        Me.mType = type
        Me.Icon = My.Resources.iTong
        Me.tbGuiBackground = My.Resources.frm_bg_sub
        Me.FilletRadius = 5
        Me.BackColor = System.Drawing.Color.FromArgb(233, 237, 243) ' Color.FromArgb(53, 137, 199)
        Me.tbSplit = "10,33,10,55"
        Me.Size = New Size(440, 280)
        If type = ToolsType.Authorization OrElse type = ToolsType.RepairSpringboard Then
            Me.tbAutoSetFormSize = True
        End If
        Me.Owner = Me.mApplication
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        If Me.mDevice IsNot Nothing Then
            Me.Name = Me.mDevice.Identifier & Me.Name
        End If

        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False
        Me.btn_close.Visible = False
        Me.btnStart.Tag = RepairStatus.Start
        Me.btnSubmit.Visible = False
        Me.btnForce.Visible = False
        'Me.btnClearAuthorize.Visible = False
        Me.lblMessage.Text = ""
        Me.lblHelpWeb.Visible = False
        Me.btnHelpWeb.Visible = False

        If Me.mType = ToolsType.RepairAuthorize OrElse Me.mType = ToolsType.Authorization Then
            If Folder.LangType = LanguageType.zh_CN Then
                Me.btnForce.Visible = True
                Me.mIsStrongRepairMode = True
            End If

            Me.FunctionMappingKey = FunctionKey.Authorize
            Me.pbImage.Image = My.Resources.more_repair

            Me.lblHelpWeb.Visible = True
            Me.btnHelpWeb.Visible = True

            Me.InitAuthorizeHelper(True)

            Me.ShowButtonForce()
            Me.ShowButtonClearAuthorize()

            '默认进来就自动开始授权
            Me.btnStart_Click(Me, EventArgs.Empty)

#If IS_ITONG Then
        ElseIf Me.mType = ToolsType.ClearRubbish Then
            Me.FunctionMappingKey = FunctionKey.ClearRubbish
            Me.pbImage.Image = My.Resources.more_clear

            Me.bgwParent = New ClearRubbish(Me.mApplication, Me.mDevice, Nothing)
            Me.InitBackgroundWorker(Me.bgwParent, True)

        ElseIf Me.mType = ToolsType.RepairSpringboard Then
            Me.FunctionMappingKey = FunctionKey.SystemRepair
            Me.pbImage.Image = My.Resources.more_repair_ios

            Me.bgwParent = New RepairSpringboard(Me.mApplication, Me.mDevice, Nothing)
            Me.InitBackgroundWorker(Me.bgwParent, True)

        ElseIf Me.mType = ToolsType.ClearErrorIcon Then
            Me.FunctionMappingKey = FunctionKey.ClearErrorIcon
            Me.pbImage.Image = My.Resources.more_clearicon

            Me.bgwParent = New ClearErrorIcon(Me.mApplication, Me.mDevice, Nothing)
            Me.InitBackgroundWorker(Me.bgwParent, True)
#End If

        End If
    End Sub

    'Private mPnlBottom As New tbPanel()
    'Private mPnlBackcolor As New tbPanel
    'Private Sub InitAuthorization()
    '    Me.Size = New Size(700, Me.Height + intRepairHeight)
    '    Me.lblMessage.Size = New Size(Me.lblMessage.Width, Me.lblMessage.Height - intRepairHeight)
    '    Me.btnStart.Location = New Point(Me.btnStart.Left, Me.lblMessage.Bottom - Me.btnStart.Height - 25)
    '    Me.btnStart.BringToFront()

    '    Me.Controls.Add(Me.mPnlBackcolor)
    '    Me.mPnlBackcolor.BackColor = Color.FromArgb(255, 242, 244, 247)
    '    Me.mPnlBackcolor.Location = New Point(1, 32)
    '    Me.mPnlBackcolor.Size = New Size(Me.Width - 2, Me.Height - 100)
    '    Me.mPnlBackcolor.Anchor = AnchorStyles.Top Or AnchorStyles.Left Or AnchorStyles.Right
    '    Me.mPnlBackcolor.SendToBack()

    '    Me.Controls.Add(Me.mPnlBottom)
    '    Me.mPnlBottom.Dock = DockStyle.Bottom
    '    Me.mPnlBottom.Size = New Size(Me.mPnlBottom.Width, intRepairHeight + 53)
    '    Me.mPnlBottom.BackColor = Me.mPnlBackcolor.BackColor
    '    Me.mPnlBottom.BringToFront()
    '    Dim frm As New frmAppRepair(Me.mApplication, Me.mDevice)
    '    Utility.AddForm2Panel(frm, Me.mPnlBottom)

    '    Me.lblTitle.BackColor = Me.mPnlBackcolor.BackColor
    '    Me.lblDiscription.BackColor = Me.mPnlBackcolor.BackColor
    '    Me.lblMessage.BackColor = Me.mPnlBackcolor.BackColor
    '    Me.pbImage.BackColor = Me.mPnlBackcolor.BackColor
    '    Me.lblHelpWeb.BringToFront()
    '    Me.btnHelpWeb.BringToFront()
    'End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.btnForce.Text = Me.Language.GetString("Tools.Button.StronRepair")            '"强力修复"
        If Me.mType = ToolsType.RepairAuthorize Then
            Me.Text = Me.Language.GetString("App.Button.Repair")                        '"修复闪退"
            Me.btnStart.Text = Me.Language.GetString("Tools.Button.Repair")             '"修复"

            If Me.mDevice.VersionNumber >= 600 Then
                Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.RepairAuthorizeDescription")                  '"如果您下载的正版软件运行时需要输入Apple ID和密码，" & vbCrLf & vbCrLf & _'"那么您可以尝试用此功能进行修复。" & vbCrLf & vbCrLf & _ '"注意：仅修复从同步助手、同步推中下载的正版软件。"
            Else
                Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.RepairAuthorDes")                             '"如果您下载的正版软件运行时闪退，" & vbCrLf & vbCrLf & _  '"那么您可以尝试用此功能进行修复。" & vbCrLf & vbCrLf & _ '"注意：仅修复从同步助手、同步推中下载的正版软件。"
            End If
        ElseIf Me.mType = ToolsType.Authorization Then
            Me.Text = Me.Language.GetString("Welcome.Button.Repair")                        '"正版授权"
            'Me.btnStart.Text = Me.Language.GetString("Download.Button.Start")               '"开始"
            Me.btnStart.Text = Me.Language.GetString("Tools.Button.Authorize")               '"授权"

            If Me.mAuthorizeHelper.CheckHasAuthorizeTag(False) Then
                Me.btnStart.Text = Me.Language.GetString("Tools.Button.Repair")             '"修复"
                Me.lblMessage.Text = Me.Language.GetString("Tools.Message.AuthorizationDone")                               '"您已经取得了正版授权，无需重新授权。"
                Me.lblMessage.ForeColor = Color.Green
                Me.lblMessage.Visible = True
            End If

            If Me.mDevice.VersionNumber >= 600 Then
                Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.AuthorizationDesc")                           '"如果您下载的正版软件运行时需要输入Apple ID和密码，" & vbCrLf & vbCrLf & _ '"那么您可以尝试用此功能进行授权。"
            Else
                Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.AuthorizationDescLowVer")                     '"如果您下载的正版软件运行时闪退，" & vbCrLf & vbCrLf & _'"那么您可以尝试用此功能进行授权。"
            End If
        ElseIf Me.mType = ToolsType.ClearRubbish Then
            Me.Text = Me.Language.GetString("Main.Button.ClearRubbish")                     '"垃圾清理"
            Me.btnStart.Text = Me.Language.GetString("Download.Button.Start")               '"开始"

            Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.ClearRubbishDescription")                  ' "清理设备中的垃圾文件，释放磁盘空间，让设备运行更流畅。" & vbCrLf & vbCrLf & _'"1. 同步助手安装 / 备份过程中产生的残留文件" & vbCrLf & vbCrLf & _'"2. 设备上 App Store 下载安装产生的残留文件" & vbCrLf & vbCrLf & _ '"    （清理后需要重启设备才能生效）"

        ElseIf Me.mType = ToolsType.RepairSpringboard Then
            Me.Text = Me.Language.GetString("Main.Button.RepairWhiteApple")         '"白苹果修复"
            Me.btnStart.Text = Me.Language.GetString("Tools.Button.Repair")         '"修复"

            Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.RepairWhiteAppleDescription")             '"如果您的设备屏幕中间出现一个白色的苹果图标，" & vbCrLf & vbCrLf & _ '"无法正常进入设备的界面，" & vbCrLf & vbCrLf & _ '"但同步助手能正常识别到的话，" & vbCrLf & vbCrLf & _'"那么您可以尝试用此功能进行修复。"
            Me.lblDiscription.Height = 90
            Me.lblDiscription.BringToFront()
        ElseIf Me.mType = ToolsType.ClearErrorIcon Then
            Me.Text = Me.Language.GetString("Main.Button.ClearErrorIcon") '"清理灰色图标"
            Me.btnStart.Text = Me.Language.GetString("Download.Button.Start")               '"开始"

            Me.lblDiscription.Text = Me.Language.GetString("Tools.Message.ClearErrorIconDescription")
            '"如果您的设备在安装软件时出现异常，" & vbCrLf & vbCrLf & "导致设备桌面出现了灰色图标，同时无法手动删除，" & vbCrLf & vbCrLf & "那么您可以尝试用此功能进行清理。"
        End If

        Me.btnSubmit.Text = Me.Language.GetString("Tools.Button.Submit")  '错误反馈
        'Me.btnClearAuthorize.Text = Me.Language.GetString("Tools.Button.ClearAuthorize")
        Me.lblHelpWeb.Text = Me.Language.GetString("Tools.Label.AuthorizeHelpPage")  '授权问题详解
        Me.lblTitle.Text = Me.Text

        If Folder.LangType = LanguageType.zh_CN Then
            Me.lblQA.Text = Me.Language.GetString("Tools.Label.FAQ")            '"详细教程>>"
        Else
            Me.lblQA.Visible = False
        End If
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        '关闭的时候会调用此方法, 所以释放的代码要写在 frmTools_FormClosed

        'Try
        '    '关闭前看一下DNS如果让助手修改过就重新修改回来.
        '    If Me.mDnsAddress IsNot Nothing AndAlso Me.mDnsAddress.Length = 2 Then
        '        Common.SetDNS(Me.mDnsAddress(0), Me.mDnsAddress(1))
        '    End If
        'Catch ex As Exception
        'End Try
    End Sub

    Private Sub frmTools_FormClosed(ByVal sender As System.Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles MyBase.FormClosed
        Me.InitBackgroundWorker(Me.bgwParent, False)

        Me.InitAuthorizeHelper(False)
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

    Private Sub InitBackgroundWorker(ByVal bgw As tbBackgroundWorker, ByVal isAddHandler As Boolean)

        If bgw Is Nothing Then
            Return
        End If

        If isAddHandler Then
            RemoveHandler bgw.ProgressChanged, AddressOf ProgressChanged
            AddHandler bgw.ProgressChanged, AddressOf ProgressChanged

            RemoveHandler bgw.RunWorkerCompleted, AddressOf RunWorkerCompleted
            AddHandler bgw.RunWorkerCompleted, AddressOf RunWorkerCompleted

        Else
            RemoveHandler bgwParent.ProgressChanged, AddressOf ProgressChanged
            RemoveHandler bgwParent.RunWorkerCompleted, AddressOf RunWorkerCompleted

            Try
                If Me.bgwParent.IsBusy Then
                    Me.bgwParent.CancelAsync()
                End If
            Catch
            End Try
        End If
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

        'Me.btnStart_Click(Me.btnStart, EventArgs.Empty)
    End Sub

#End Region

#Region "---  窗体事件  ---"

    Private Sub btnStart_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStart.Click
        Me.lblQA.Visible = False

        If Me.btnStart.Tag IsNot Nothing AndAlso _
          (Me.btnStart.Tag = RepairStatus.Fail OrElse Me.btnStart.Tag = RepairStatus.Start) Then

            Me.lblMessage.ForeColor = Color.Black
            If Me.mType = ToolsType.RepairAuthorize OrElse Me.mType = ToolsType.Authorization Then
                If ServerIniSetting.GetLowestVersion.Length > 0 AndAlso Common.CompareVer(ServerIniSetting.GetLowestVersion, My.Application.Info.Version.ToString(4), 4) > 0 Then
                    tbMessageBox.Show(Me.mApplication, "您当前版本过低，请到官网下载安装最新版本后再使用！", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
                    Common.OpenExplorer(WebUrl.PageAssitantHome)
                    Return
                End If
                Me.DoRepair(False)

            ElseIf Me.mType = ToolsType.ClearRubbish OrElse _
                     Me.mType = ToolsType.RepairSpringboard OrElse _
                     Me.mType = ToolsType.ClearErrorIcon Then

                Me.bgwParent.RunWorkerAsync()
                Me.btnStart.Enabled = False

            End If
        Else
            Me.Close()
        End If
    End Sub

    Private Sub btnForce_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnForce.Click

#If IS_ITONG Then
        '国内版的才运行现在的逻辑
        If Me.mIsStrongRepairMode Then
            If Me.btnForce.Tag Is Nothing Then
                Me.btnForce.Tag = New Object() {RepairFormType.Repair}
            End If
            MainForm.ItemShow(GetType(frmRepairPayment), Me.mDevice, Me.mApplication, Me.btnForce.Tag)
            Return
        End If
#End If


        '海外版走原来的逻辑
        Me.lblQA.Visible = False
        Dim strValue As String = Me.Language.GetString("Tools.Button.Flash")            '"闪退"
        If Me.mDevice.VersionNumber >= 600 Then
            strValue = Me.Language.GetString("Tools.Message.TypeInAppleId")             '"提示需要输入AppleID和密码"
        End If

        Dim strMsg As String = String.Format(Me.Language.GetString("Tools.Message.ForceRepairDescription"), strValue)            '"此操作只保留同步的授权信息，其余授权信息将会被抹掉。" + vbCrLf + vbCrLf + _
        '"如果你在使用同步授权成功后，打开同步正版软件仍然" + strValue + "，可以尝试使用该功能。" + vbCrLf + vbCrLf + _
        '"在强力修复授权之后，会导致你自己帐号购买的正版软件会出现" + strValue + "。" + vbCrLf + vbCrLf + _
        '"此时你只要打开设备上的AppStore应用，使用你自己帐号购买一款免费软件即可解决问题。"

        If tbMessageBox.Show(Me, strMsg, _
                                            Me.Language.GetString("Common.Warning"), MessageBoxButtons.OKCancel, _
                                            MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1) _
                                            = Windows.Forms.DialogResult.OK Then
            Me.DoRepair(True)
        End If

        'Dim frmRep As New frmRepair(Me.mApplication, Me.mDevice, Me)
        'frmRep.Show()
    End Sub

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub btnSubmit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSubmit.Click
        Me.lblQA.Visible = False
        Try
            Dim strContent As String = String.Empty

            If Me.mDevice IsNot Nothing Then
                strContent = Me.AppendContent(strContent, String.Format("ProductType：{0}", Me.mDevice.ProductType))
                strContent = Me.AppendContent(strContent, String.Format("VersionNumber：{0}", Me.mDevice.VersionNumber))
            End If

            strContent = Me.AppendContent(strContent, String.Format("iTunesVersion：{0}", iTunesHelper.iTunesVersion))

            If Me.mAuthorizeArgs IsNot Nothing Then
                strContent = Me.AppendContent(strContent, String.Format("Code：{0}", Me.mAuthorizeArgs.Code))
                strContent = Me.AppendContent(strContent, String.Format("Error：{0}", Me.mAuthorizeArgs.ErrMsg))
            End If

            Utility.ShowFeedback(strContent)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnSubmit_Click")
        End Try
    End Sub

    '清理授权，针对已越狱的设备。（界面上的按钮暂时删除）
    Private Sub btnClearAuthorize_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        If tbMessageBox.Show(Me, Me.Language.GetString("Tools.Message.ClearAuthorize"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            '1、删除系统目录的授权文件
            If Me.mDevice.Jailbreaked Then
                Dim strDir As String = "/var/mobile/Library/FairPlay/iTunes_Control/iTunes"
                Dim arrFile() As String = Me.mDevice.GetFiles(strDir, True)
                For Each strFile As String In arrFile
                    Me.mDevice.DeleteFile(strDir & "/" & strFile)
                Next
            End If

            '2、删除用户目录的授权文件
            Dim strDir2 As String = "/iTunes_Control/iTunes/"
            Dim strFile1 As String = strDir2 & "IC-Info.sidf"
            Dim strFile2 As String = strDir2 & "IC-Info.sidv"
            Dim strFile3 As String = strDir2 & "AH-Info.plist"
            Dim strFile4 As String = strDir2 & "Tongbu.plist"

            Me.mDevice.DeleteFileByAFC(strFile1)
            Me.mDevice.DeleteFileByAFC(strFile2)
            Me.mDevice.DeleteFileByAFC(strFile3)
            Me.mDevice.DeleteFileByAFC(strFile4)

            '3、重启

            Me.iPhone.Restart()
        End If

    End Sub

    Private Sub btnSubmit_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSubmit.Resize, btnForce.Resize
        Me.btnForce.Location = New Point(btnStart.Left - Me.btnForce.Width - 10, Me.btnStart.Location.Y)
        Me.btnSubmit.Location = New Point(btnForce.Left - Me.btnSubmit.Width - 10, Me.btnStart.Location.Y)
    End Sub

    Private Sub lblHelpWeb_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblHelpWeb.LinkClicked
        '"http://news.tongbu.com/72024.html"
        Common.OpenExplorer(WebUrl.PageAuthroizeHelper)
    End Sub

    Private Sub btnHelpWeb_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnHelpWeb.Click
        '"http://news.tongbu.com/72024.html"
        Common.OpenExplorer(WebUrl.PageAuthroizeHelper)
    End Sub

    Private Sub lblHelpWeb_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblHelpWeb.Resize
        Me.btnHelpWeb.Location = New Point(Me.lblHelpWeb.Right + 2, Me.lblHelpWeb.Location.Y)
    End Sub

#End Region

#Region "--- 正版授权 ---"

    Private Sub DoRepair(ByVal blnResetDsid As Boolean)
        Me.btnSubmit.Visible = False

        If Me.mAuthorizeHelper Is Nothing Then
            Me.InitAuthorizeHelper(True)
        End If

        If Me.mAuthorizeHelper IsNot Nothing Then
            Me.mAuthorizeHelper.Authorize(True, blnResetDsid)
        End If

        '检查同步推是否正常
        Me.StartToCheckTuiIsNormal()
    End Sub

    'isConnect，ture表示注册事件，false表示移除事件
    Private Sub InitAuthorizeHelper(ByVal isConnect As Boolean)
        If isConnect Then
            If Me.mDevice Is Nothing Then
                Return
            End If

            Me.mAuthorizeHelper = AuthorizeHelper.GetInstance(Me.mDevice)

            RemoveHandler mAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
            RemoveHandler mAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted
            AddHandler mAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
            AddHandler mAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted

            If Me.mAuthorizeHelper.IsAuthorizing Then
                Me.SwitchBtnRepairView(True, True)
            Else
                Me.SwitchBtnRepairView(True, False)
            End If

        Else
            If Me.mAuthorizeHelper IsNot Nothing Then
                RemoveHandler mAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
                RemoveHandler mAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted

                Me.mAuthorizeHelper = Nothing

                Me.SwitchBtnRepairView(False, False)
            End If
        End If
    End Sub

    Private Sub AuthorizeHelper_AuthorizeStart(ByVal sender As Object, ByVal e As AuthorizeEventArgs)
        Me.SwitchBtnRepairView(True, True)
    End Sub

    Private Sub AuthorizeHelper_AuthorizeCompleted(ByVal sender As Object, ByVal e As AuthorizeEventArgs)
        '不再继续授权 或 不是修复闪退
        If e.Result = SoftDownloadErrorState.AuthNoCotinue OrElse Not e.IsRepaire Then
            Me.SwitchBtnRepairView(True, False)
            Return
        End If

        Dim strText As String = ""
        Dim forColor As Color = Color.Green
        Dim isSucceed As Boolean = False
        Me.lblQA.Visible = False

        'If Me.SetDNSRetry(e) Then
        '    Return
        'End If
        
        Select Case e.Result
            Case SoftDownloadErrorState.AuthFailed
                isSucceed = False
                If e.Code = "-6" Then
                    strText = Me.Language.GetString("Tools.Message.AuthorizeFailure") & vbCrLf             '"由于网络繁忙，您此次没有授权成功，请稍候再试。"
                ElseIf e.Code = "-7" Then
                    strText = String.Format(Me.Language.GetString("Tools.Message.AuthorizeFailureAndContact"), Utility.TongbuContact)    '"授权计算失败，请加入客服QQ群：291311056"
                    If Me.mDevice.Jailbreaked Then
                        'Me.ShowButtonForce()
                        '授权失败-7,并且越狱的设备开出强力修复
                        Me.btnForce.Visible = True
                        Me.btnForce.Text = Me.Language.GetString("Tools.Button.StronRepair")            '"强力修复"
                    End If
                    If Folder.LangType = LanguageType.zh_CN Then
                        Me.lblQA.Visible = True
                    End If
                    Me.mAuthorizeResult = SoftDownloadErrorState.AuthFailed

                Else
                    strText = Me.mDevice.DeviceName & " " & Me.Language.GetString("App.Lable.RepairFail")   '"修复失败。"
                End If

            Case SoftDownloadErrorState.AuthFailedGetFairPlayCertificate
                isSucceed = False
                strText = String.Format(Me.Language.GetString("App.Lable.DeviceNoData"), iPhoneDeviceHelper.GetDeviceProductName(Me.mDevice)) & vbCrLf '"无法获取{0}数据，请尝试重启{0}"

            Case SoftDownloadErrorState.AuthFailedGetAfsyncRq
                isSucceed = False
                strText = Me.Language.GetString("Tools.Message.FailGetAfsyncRq") '"授权服务启动失败，您可以尝试以下操作：" & vbCrLf & vbCrLf & _'"1、再次重试授权；" & vbCrLf & vbCrLf & _"2、重启设备后再授权；" & vbCrLf & vbCrLf & _"3、重装最新版的iTunes。"

                '错误提示过长，因此自动调节窗体size
                If Me.Language.CurrentLanguage.LangName = "en-US" Then
                    Me.Size = New Size(440, 320)
                Else
                    Me.Size = New Size(440, 340)
                End If

                If Not Me.mIsHasJumpToCourse Then
                    Me.mIsHasJumpToCourse = True
                    Common.OpenExplorer(WebUrl.PageUnInstalliTunes)
                End If

            Case SoftDownloadErrorState.LoginFailedWithNetworkError
                isSucceed = False
                strText = String.Format(Me.Language.GetString("Tools.Message.AuthorizeFailTimeout"), Utility.TongbuContact) & vbCrLf      '"无法访问服务器，请加入客服QQ群：291311056"
                If Folder.LangType = LanguageType.zh_CN Then
                    Me.lblQA.Visible = True
                End If
                Me.mAuthorizeResult = SoftDownloadErrorState.LoginFailedWithNetworkError

            Case SoftDownloadErrorState.Succeed
                isSucceed = True

            Case SoftDownloadErrorState.AuthSucceedServerNoData
                isSucceed = True
                strText = Me.Language.GetString("App.Lable.AdviseToRepair")   '"如果有闪退问题，请修复。"
        End Select


        If isSucceed Then
            If Not e.IsResetDsid Then
                Me.btnStart.Text = Me.Language.GetString("Media.Message.Finished")                  '"完成"
                Me.btnStart.Tag = RepairStatus.Finished
            End If

            strText = Me.mDevice.DeviceName & " " & Me.Language.GetString("App.Lable.RepairSucceed") & vbCrLf & strText  '"修复成功。"
        Else
            If Not e.IsResetDsid Then
                Me.btnStart.Text = Me.Language.GetString("Common.Retry")                            '"重试"
                Me.btnStart.Tag = RepairStatus.Fail
            End If

            forColor = Color.Red

            '修复失败，则展示提交错误按钮。

            Me.mAuthorizeArgs = e
            If Utility.IsPanda Then
                Me.btnSubmit.Visible = False
            Else
                Me.btnSubmit.Visible = True
            End If
        End If

        Me.SwitchBtnRepairView(True, False)
        Me.lblMessage.Text = strText & vbCrLf & Me.Language.GetString("Tools.Message.StronRepair")                  '"如果遇到闪退的困惑，您可以尝试强力修复！"
        Me.lblMessage.ForeColor = forColor
    End Sub

    Private Sub SwitchBtnRepairView(ByVal isConnect As Boolean, ByVal isAuthorizing As Boolean)
        Try
            If isAuthorizing Then
                Me.tmrLoading.Start()
                Me.lblMessage.Text = Me.Language.GetString("App.Button.Repairing")  '"正在授权"
                Me.btnStart.Enabled = False
                Me.btnClose.Enabled = False

                '在修复的时候， 可以进入扫描强力修复
                If Not Me.mIsStrongRepairMode Then
                    Me.btnForce.Enabled = False
                End If

                'Me.btnClearAuthorize.Enabled = False
            Else
                If Me.tmrLoading.Enabled Then
                    Me.tmrLoading.Stop()
                End If

                Me.lblMessage.Text = "" 'Me.Language.GetString("Welcome.Button.Repair")
                Me.btnStart.Enabled = IIf(isConnect, True, False)
                Me.btnClose.Enabled = True
                Me.btnForce.Enabled = IIf(isConnect, True, False)
                'Me.btnClearAuthorize.Enabled = True
            End If

            Me.lblMessage.ForeColor = Color.Black
            Application.DoEvents()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SwitchBtnRepairView")
        End Try
    End Sub

    '修复授权和正版授权
    Private Sub ShowButtonForce()
        Dim strValueLocal As Boolean = IniSetting.GetShowForceRepairButton()
        Dim strValueServer As Boolean = ServerIniSetting.GetShowForceRepairButton()

        If Me.mDevice.Jailbreaked Then 'AndAlso (strValueLocal OrElse strValueServer)
            Me.btnForce.Visible = True
            Me.btnForce.Text = Me.Language.GetString("Tools.Button.StronRepair")            '"强力修复"
        End If
    End Sub

    '修复授权和正版授权
    Private Sub ShowButtonClearAuthorize()
        If Me.mDevice.Jailbreaked Then
            'Me.btnClearAuthorize.Visible = True
        End If
    End Sub

    Private Sub tmrLoading_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrLoading.Tick
        Try
            Dim result As Integer = Me.mTimerCount Mod 4
            Dim strPoint As String = ""

            Select Case result
                Case 0
                    strPoint = ""
                Case 1
                    strPoint = "."
                Case 2
                    strPoint = ".."
                Case 3
                    strPoint = "..."
            End Select

            Me.ShowMessage(Me.Language.GetString("App.Button.Repairing") & strPoint)

            Me.mTimerCount += 1
        Catch
        End Try
    End Sub

#End Region

#Region "---  BackgroundWorker回调事件  ---"

    Public Sub ProgressChanged(ByVal sender As Object, ByVal e As System.ComponentModel.ProgressChangedEventArgs)
        If TypeOf e.UserState Is String Then
            Me.ShowMessage(e.UserState)
        End If
    End Sub

    Public Sub RunWorkerCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs)
        Me.btnStart.Text = Me.Language.GetString("Media.Message.Finished")                          '"完成"
        Me.btnStart.Tag = RepairStatus.Finished
        Me.btnStart.Enabled = True

        Me.lblMessage.ForeColor = Color.Green
    End Sub

    Private Delegate Sub ShowMessageHandler(ByVal str As String)
    Private Sub ShowMessage(ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowMessageHandler(AddressOf ShowMessage), strMessage)
        Else
            Me.lblMessage.Text = strMessage
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function AppendContent(ByVal strContent As String, ByVal strAppend As String) As String

        If String.IsNullOrEmpty(strContent) Then
            strContent = strAppend
        Else
            strContent = strContent & vbCrLf & strAppend
        End If

        Return strContent
    End Function

    '检查同步推是否正常
    Private Sub StartToCheckTuiIsNormal()
        Try
            If Me.mIsHasCheckTui Then
                Return
            End If
            Me.mIsHasCheckTui = True

            Dim state As PackageInstallState = AuthorizeByAppleID.Instance(Me.mDevice).CheckTuiInstallState()
            If state = PackageInstallState.Uninstalled OrElse state = PackageInstallState.InstallLower Then
                AuthorizeByAppleID.Instance(Me.mDevice).CheckTui(ResourceClass.Plugins)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "StartToCheckTuiIsNormal")
        End Try
    End Sub

    '没有安装或安装的旧版同步推，则强装最新版同步推正版
    Private Sub CheckTuiIsNormal()
        Try
            Dim isJailbreak As Boolean = False  '默认设备都未越狱，都强装最新同步推正版

            If Me.mDevice.InstallCydia AndAlso TuiInstallHelper.CheckAppSyncInstall(Me.mDevice) Then
                isJailbreak = True
            End If


            'Dim checkUpgradeInfo As KeyValuePair(Of String, String) = ServerIniSetting.GetTuiCheckUpgradeInfo(Me.mDevice.ProductType, isJailbreak)

            'Dim info As KeyValuePair(Of String, String) = TuiInstallHelper.GetTuiLatestInfo(Me.mDevice.ProductType, Me.mDevice.ProductVersion, isJailbreak)

            'Dim strSKU As String = Common.GetTuiAppSKU(Me.mDevice.ProductType)
            'Dim strVersion As String = info.Key

            'If String.IsNullOrEmpty(strSKU) OrElse String.IsNullOrEmpty(strVersion) Then
            '    Return
            'End If

            'Dim installState As PackageInstallState = TuiInstallHelper.CheckPackageInstalled(Me.mDevice, isJailbreak)
            Dim installState As PackageInstallState = AuthorizeByAppleID.Instance(Me.mDevice).CheckTuiInstall()

            If installState = PackageInstallState.Unknow Then
                Return
            End If

            If ServerIniSetting.GetOpenH5 AndAlso Not isJailbreak AndAlso Me.mDevice IsNot Nothing AndAlso Not AuthorizeHelper.GetInstance(mDevice).CheckTuiMC() Then
                installState = PackageInstallState.UnInstalled
            End If

            '越狱设备如果安装的是高版本的同步推那就可能是“同步推正版”就换面越狱版。
            If isJailbreak AndAlso installState = PackageInstallState.InstallHigher Then
                installState = PackageInstallState.InstallLower
            End If

            If installState = PackageInstallState.UnInstalled OrElse installState = PackageInstallState.InstallLower Then
                'If Me.mDevice.ProductType.ToLower().StartsWith("ipad") Then
                '    TuiInstallHelper.DownloadTbTuiHD(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, isJailbreak, ResourceClass.Plugins, False)
                'Else
                '    TuiInstallHelper.DownloadTbTui(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, isJailbreak, ResourceClass.Plugins, False)
                'End If
                'ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.TuiAutoInstall, ActionDataType.Install)

                AuthorizeByAppleID.Instance(Me.mDevice).CheckTui(ResourceClass.Plugins)
                ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.TuiAutoInstall, ActionDataType.Install)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckTuiIsNormal")
        End Try
    End Sub

#End Region

    Private Sub lblMessage_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblMessage.SizeChanged
        Me.lblMessage.Location = New Point(Me.lblMessage.Left, (Me.Height - Me.lblMessage.Height - Me.lblDiscription.Bottom - 54) / 2 + Me.lblDiscription.Bottom)
        Me.lblQA.Location = New Point(Me.lblMessage.Right + 3, Me.lblMessage.Bottom - Me.lblQA.Height)
        If Me.lblQA.Right >= Me.Width Then
            Me.lblQA.Location = New Point(Me.lblMessage.Left, Me.lblMessage.Bottom + 2)
        End If

    End Sub

    Private Sub lblQA_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblQA.LinkClicked
        If Me.mAuthorizeResult = SoftDownloadErrorState.AuthFailed Then
            Common.OpenExplorer("http://news.tongbu.com/75183.html")

        ElseIf Me.mAuthorizeResult = SoftDownloadErrorState.LoginFailedWithNetworkError Then
            Common.OpenExplorer("http://news.tongbu.com/75104.html")

        End If
    End Sub

    Private Sub btnminimize_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.WindowState = FormWindowState.Minimized
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_sub
        End If
    End Sub
End Class

Public Enum ToolsType
    RepairAuthorize         '修复授权
    ClearRubbish             '清理垃圾
    RepairSpringboard     '修复白苹果
    Authorization             '正版授权
    ClearErrorIcon            '清理异常图标
End Enum