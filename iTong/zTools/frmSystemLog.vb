﻿Imports System.IO
Imports System.Runtime.InteropServices
Imports System.Threading
Imports System.Text.RegularExpressions
Imports iTong.Components

Public Class frmSystemLog

    ' <DllImport("wsock32.dll")> _
    ' Public Shared Function WSAAsyncSelect(ByVal socket As Integer, ByVal hWnd As Integer, ByVal wMsg As Integer, ByVal lEvent As Integer) As Integer
    ' End Function

    ' <DllImport("user32.dll", CharSet:=CharSet.Auto, SetLastError:=True)> _
    'Private Shared Function RegisterWindowMessage(ByVal lpString As String) As Integer
    ' End Function

    ' Private WM_USER As Integer = &H400
    ' Private WM_SOCKET As Integer = WM_USER + 101

    ' Public Const FD_READ As Integer = &H1
    ' Public Const FD_WRITE As Integer = &H2
    ' Public Const FD_ACCEPT As Integer = &H8
    ' Public Const FD_CONNECT As Integer = &H10
    ' Public Const FD_CLOSE As Integer = &H20

    Private mSocketSyslog As Integer
    Private mThreadLog As Threading.Thread
    Private mLineBuffer As String = String.Empty
    Private mState As Integer = 1

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal iPhone As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.mDevice = iPhone
        Me.Language = Me.mApplication.Language

        Me.Icon = My.Resources.iTong
        Me.FilletRadius = 5
        Me.StartPosition = FormStartPosition.CenterParent
        Me.FunctionMappingKey = FunctionKey.SystemLog
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Try
            Me.rtbContent.Font = New Font("Arial", 9, FontStyle.Regular)
        Catch
        End Try

        '设置行间距
        'Me.rtbContent.SetLineSpace(12)

        '设置字的边左距
        Me.rtbContent.SelectionIndent = 10

        '开始读取日志
        Me.ReadLog()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Main.Button.SystemLog")                                '"实时日志"

        Me.btnCleanAll.Text = Me.Language.GetString("Common.Button.Save")                 '"保存"
        Me.btnCleanAll.Text = Me.Language.GetString("Common.CleanAll")                     '"清空"

        Me.tsmiCleanAll.Text = Me.Language.GetString("Common.CleanAll")                    '"清空"
        Me.tsmiSelectAll.Text = Me.Language.GetString("Common.SelectAll")                  '"全选"
        Me.tsmiCopy.Text = Me.Language.GetString("Common.Button.Copy")                  '"复制"
        Me.btnExport.Text = Me.Language.GetString("Common.Button.Save")
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        If pblnCancelClose Then
            Return
        End If

        Try
            If Me.mThreadLog IsNot Nothing AndAlso Me.mThreadLog.ThreadState <> ThreadState.Stopped Then
                Me.mThreadLog.Abort()
            End If
        Catch
        End Try

        Try
            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                Me.mDevice.CloseSyslogRelay()
            End If
        Catch
        End Try

    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        Me.Close()
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub frmNote_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles MyBase.KeyDown
        If e.Control AndAlso e.KeyCode = Keys.S AndAlso Me.btnCleanAll.Enabled Then
            Me.btnCleanAll.Enabled = False
            Me.Export()
            Me.btnCleanAll.Enabled = True
        End If
    End Sub

    Private Sub btnCleanAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCleanAll.Click
        Me.ClearAll()
    End Sub

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        Me.btnCleanAll.Enabled = False

        Me.Export()

        Me.btnCleanAll.Enabled = True
    End Sub

    Private Sub Export()
        Dim sfd As New SaveFileDialog()
        sfd.Filter = String.Format("{0}(*.txt)|*.txt", Me.Language.GetString("Note.Label.TxtFile"))          '"文本文档"
        sfd.FileName = String.Format("Log_{0}.txt", Now.ToString("yyMMdd_HHmm"))
        sfd.InitialDirectory = System.Environment.GetFolderPath(Environment.SpecialFolder.Desktop)

        If sfd.ShowDialog = Windows.Forms.DialogResult.OK Then
            Dim blnResult As Boolean = Me.WriteToFile(sfd.FileName, Me.rtbContent.Text)

            Dim strPath As String = Path.Combine(sfd.InitialDirectory, sfd.FileName)
            If blnResult AndAlso File.Exists(strPath) Then
                Common.OpenExplorer(strPath)
            End If
        End If
    End Sub

    Private Function WriteToFile(ByVal strFilePath As String, ByVal strContent As String) As Boolean
        Dim blnReturn As Boolean = True

        Try
            strContent = Me.ConvertVBCrlf(strContent)

            Using fileWriter As New System.IO.StreamWriter(strFilePath, False, System.Text.Encoding.UTF8)
                fileWriter.Write(strContent)
            End Using
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSystemLog.WriteToFile")
            blnReturn = False
        End Try

        Return blnReturn
    End Function

    Private Function ConvertVBCrlf(ByVal strContent As String) As String
        Dim strReturn As String = ""
        strReturn = strContent.Replace(vbCrLf, vbLf)
        strReturn = strReturn.Replace(vbLf, vbCrLf)
        Return strReturn
    End Function

    Private Sub tsmiSelectAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiSelectAll.Click
        Me.rtbContent.SelectAll()
    End Sub

    Private Sub tsmiCopy_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiCopy.Click
        Me.rtbContent.Copy()
    End Sub

    Private Sub tsmiCleanAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiCleanAll.Click
        Me.ClearAll()
    End Sub

    Private Sub ClearAll()
        Me.rtbContent.Clear()
        Me.rtbContent.SelectionIndent = 10
    End Sub

    'Protected Overrides Sub WndProc(ByRef m As Message)

    '    'Debug.Print("WndProc:" & m.Msg.ToString())
    '    If m.Msg = WM_SOCKET Then
    '        Debug.Print("WndProc")
    '        Me.ReadLog()
    '        Debug.Print("WndProc:" & m.LParam.ToInt32().ToString())

    '        'Select Case m.LParam.ToInt32()
    '        '    Case FD_READ
    '        '        Debug.Print("读取系统日志")

    '        '        Me.ReadLog()

    '        '    Case FD_WRITE
    '        '    Case FD_CLOSE
    '        '        Debug.Print("关闭读取系统日志")
    '        '    Case Else
    '        'End Select

    '    Else
    '        MyBase.WndProc(m)
    '    End If

    'End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Sub ReadLog()
        If Me.mThreadLog IsNot Nothing AndAlso Me.mThreadLog.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mThreadLog = New Thread(AddressOf ReadLogInThread)
        With Me.mThreadLog
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub ReadLogInThread()
        Try
            While Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected
                Dim strLog As String = Me.mDevice.ReadSyslogRelay()
                'Debug.Print(strLog)             
                If Me.mDevice.VersionNumber >= 800 Then
                    Me.ShowLogFor8X(strLog)
                Else
                    Me.ShowLogFor7X(strLog)
                End If

            End While
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSystemLog.ReadLogInThread")
        End Try
    End Sub

    Private Function ConvertLog(ByVal strLog As String) As List(Of String)
        Dim lstLog As New List(Of String)

        Try
            Dim strArray As String()
            Dim input As String = String.Empty
            Dim item As String = String.Empty

            strArray = strLog.Split(New Char() {ChrW(10)})  '换行键

            Dim index As Integer = 0
            Do While True
                Dim intFlag As Integer
                If index >= strArray.Length Then
                    Exit Do
                End If

                input = strArray(index).Trim()
                If index = strArray.Length - 1 AndAlso input.LastIndexOf(ChrW(10)) <> input.Length - 1 Then
                    intFlag = 1
                Else
                    intFlag = 0
                End If

                Dim blnFlag As Boolean = CBool(intFlag)
                If input.StartsWith(">") Then
                    Select Case Me.mState
                        Case 1
                            Me.mState = 3
                            Exit Select
                        Case 3
                            Dim states As Integer
                            If blnFlag Then
                                states = 2
                            Else
                                states = 3
                            End If

                            Me.mState = states
                            Exit Select
                    End Select

                ElseIf blnFlag Then
                    Me.mLineBuffer = input

                Else
                    Select Case Me.mState
                        Case 2
                            Me.mState = 3
                            Exit Select
                        Case 3
                            item = Regex.Replace(input, "[^ -~]", "").Trim

                            If (Not item.StartsWith(">") AndAlso (item.Length <> 0)) Then
                                lstLog.Add(item)
                            End If
                            Exit Select
                    End Select
                End If

                index += 1
            Loop
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSystemLog.ConvertLog")
        End Try

        Return lstLog
    End Function

    Private Sub ShowLogFor7X(ByVal strLog As String)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ShowLogHandler(AddressOf ShowLogFor7X), strLog)
        Else
            Try
                Dim lstLog As List(Of String) = Me.ConvertLog(strLog)

                Dim str As String = ""
                For Each item As String In lstLog
                    str = str & item & vbCrLf
                Next

                Me.rtbContent.Focus()
                Me.rtbContent.AppendText(str)
                'Me.rtbContent.ScrollToCaret()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmSystemLog_ShowLogFor7")
            End Try
        End If
    End Sub

    Private Delegate Sub ShowLogHandler(ByVal strLog As String)
    Private Sub ShowLogFor8X(ByVal strLog As String)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ShowLogHandler(AddressOf ShowLogFor8X), strLog)
        Else
            Try
                Me.rtbContent.Focus()
                Me.rtbContent.AppendText(strLog)
                'Me.rtbContent.ScrollToCaret()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmSystemLog_ShowLogFor8")
            End Try
        End If
    End Sub

#End Region

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_sub
        End If
    End Sub

End Class