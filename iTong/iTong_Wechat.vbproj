﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{56664D9F-835A-4F20-BFB5-536852F12220}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>iTong.My.MyApplication</StartupObject>
    <RootNamespace>iTong</RootNamespace>
    <AssemblyName>WechatMasterWin</AssemblyName>
    <MyType>WindowsForms</MyType>
    <ApplicationIcon>Resources\iTong.ico</ApplicationIcon>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetZone>LocalIntranet</TargetZone>
    <GenerateManifests>false</GenerateManifests>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>2.0</OldToolsVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <WarningsAsErrors>
    </WarningsAsErrors>
    <DefineConstants>IS_ITONG=True,IS_ITONG_ZJ=False,IS_ITONG_LITE=False,IS_WECHAT=True</DefineConstants>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <Optimize>true</Optimize>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleAssemblies>C:\Program Files\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <WarningsAsErrors>
    </WarningsAsErrors>
    <DefineConstants>IS_ITONG=True,IS_ITONG_ZJ=False,IS_ITONG_LITE=False,IS_WECHAT=True</DefineConstants>
    <DefineDebug>false</DefineDebug>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleAssemblies>C:\Program Files\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=True,IS_ITONG_ZJ=False,IS_ITONG_LITE=False,IS_WECHAT=True</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <Optimize>true</Optimize>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleAssemblies>C:\Program Files\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=True,IS_ITONG_ZJ=False,IS_ITONG_LITE=False,IS_WECHAT=True</DefineConstants>
    <DefineDebug>false</DefineDebug>
    <UseVSHostingProcess>false</UseVSHostingProcess>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=********, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=AMD64" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.XML" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong.Android" />
    <Import Include="iTong.AppUnion" />
    <Import Include="iTong.Components" />
    <Import Include="iTong.CoreFoundation" />
    <Import Include="iTong.CoreModule" />
    <Import Include="iTong.CoreReses" />
    <Import Include="iTong.Device" />
    <Import Include="iTong.Update" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Data.SQLite3" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.IO" />
    <Import Include="System.Net" />
    <Import Include="System.Text" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\iDataRecovery\MainFormDataRecovery.designer.vb">
      <Link>MainFormDataRecovery.designer.vb</Link>
      <DependentUpon>MainFormDataRecovery.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iDataRecovery\MainFormDataRecovery.vb">
      <Link>MainFormDataRecovery.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTest.Designer.vb">
      <DependentUpon>frmTest.vb</DependentUpon>
    </Compile>
    <Compile Include="frmTest.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidQRCode.Designer.vb">
      <DependentUpon>frmAndroidQRCode.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidQRCode.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidPhoto\frmAndroidWechatMedia.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidRoot\KingRootHelper.vb" />
    <Compile Include="zClass\ProcFormHelper.vb" />
    <Compile Include="zComponents\tbDownloadBar.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zComponents\tbStepView.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDeviceClass\CameraHelper.vb">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmImportLivePhoto.Designer.vb">
      <DependentUpon>frmImportLivePhoto.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmImportLivePhoto.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoConvert.designer.vb">
      <DependentUpon>frmPhotoConvert.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoConvert.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoWithTui.Designer.vb">
      <DependentUpon>frmPhotoWithTui.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoWithTui.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmAppDownloaded.Designer.vb">
      <DependentUpon>frmAppDownloaded.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmAppDownloaded.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmTip.Designer.vb">
      <DependentUpon>frmTip.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmTip.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDuobao\frmDuobao.Designer.vb">
      <DependentUpon>frmDuobao.vb</DependentUpon>
    </Compile>
    <Compile Include="zDuobao\frmDuobao.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDuobao\frmJinbaoCourse.Designer.vb">
      <DependentUpon>frmJinbaoCourse.vb</DependentUpon>
    </Compile>
    <Compile Include="zDuobao\frmJinbaoCourse.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFlash\frmCommonFlash.Designer.vb">
      <DependentUpon>frmCommonFlash.vb</DependentUpon>
    </Compile>
    <Compile Include="zFlash\frmCommonFlash.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFlash\frmLoginAppleid.Designer.vb">
      <DependentUpon>frmLoginAppleid.vb</DependentUpon>
    </Compile>
    <Compile Include="zFlash\frmLoginAppleid.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zGameBox\MyWorldHelper.vb" />
    <Compile Include="zImageHelper\frmImageSearch.Designer.vb">
      <DependentUpon>frmImageSearch.vb</DependentUpon>
    </Compile>
    <Compile Include="zImageHelper\frmImageSearch.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zImageHelper\ImageSearchHelper.vb" />
    <Compile Include="zImageHelper\MatchHelper.vb" />
    <Compile Include="zImageHelper\tbBox.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zImportData\CheckImport.vb" />
    <Compile Include="frmMore.Designer.vb">
      <DependentUpon>frmMore.vb</DependentUpon>
    </Compile>
    <Compile Include="frmMore.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSplash.Designer.vb">
      <DependentUpon>frmSplash.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSplash.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zComponents\tbCharBar.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDataGridView\tbDataGridViewEditCell.vb" />
    <Compile Include="zDataGridView\tbDataGridViewMediaCell.vb" />
    <Compile Include="zComponents\tbTreeViewEx.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmAppDetail.Designer.vb">
      <DependentUpon>frmAppDetail.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmAppDetail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceIcon\frmIconManage.designer.vb">
      <DependentUpon>frmIconManage.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceIcon\frmIconManage.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceIcon\frmIconSort.designer.vb">
      <DependentUpon>frmIconSort.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceIcon\frmIconSort.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceIcon\tbIconControl.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDeviceIcon\tbIconItem.vb" />
    <Compile Include="zDeviceIcon\tbPageView.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDeviceIcon\tbScreenView.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmContact.Designer.vb">
      <DependentUpon>frmContact.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmContact.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmContactConfirm.Designer.vb">
      <DependentUpon>frmContactConfirm.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmContactConfirm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmNote.Designer.vb">
      <DependentUpon>frmNote.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmNote.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmEBook.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmMediaBase.Designer.vb">
      <DependentUpon>frmMediaBase.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmMediaBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmMediaOthers.Designer.vb">
      <DependentUpon>frmMediaOthers.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmMediaOthers.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusic.Designer.vb">
      <DependentUpon>frmMusic.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtone.Designer.vb">
      <DependentUpon>frmRingtone.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtone.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtoneLocal.Designer.vb">
      <DependentUpon>frmRingtoneLocal.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtoneLocal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhoto.Designer.vb">
      <DependentUpon>frmPhoto.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhoto.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoBase.Designer.vb">
      <DependentUpon>frmPhotoBase.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoInfo.Designer.vb">
      <DependentUpon>frmPhotoInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoOrder.Designer.vb">
      <DependentUpon>frmPhotoOrder.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmPhotoOrder.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmWallpaper.Designer.vb">
      <DependentUpon>frmWallpaper.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmWallpaper.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDevicePhoto\frmWallpaperLocal.Designer.vb">
      <DependentUpon>frmWallpaperLocal.vb</DependentUpon>
    </Compile>
    <Compile Include="zDevicePhoto\frmWallpaperLocal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zQQ\BackupFolderHelper.vb" />
    <Compile Include="zQQ\frmQQ.Designer.vb">
      <DependentUpon>frmQQ.vb</DependentUpon>
    </Compile>
    <Compile Include="zQQ\frmQQ.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zQQ\QQDB.vb" />
    <Compile Include="zQQ\QQHelper.vb" />
    <Compile Include="zRestore\BackupListDeleteHelper.vb" />
    <Compile Include="zRestore\frmMSMSetting.Designer.vb">
      <DependentUpon>frmMSMSetting.vb</DependentUpon>
    </Compile>
    <Compile Include="zRestore\frmMSMSetting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\AuthorityWarningForm.Designer.vb">
      <DependentUpon>AuthorityWarningForm.vb</DependentUpon>
    </Compile>
    <Compile Include="zSetting\AuthorityWarningForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zRegister\RegistDat.vb" />
    <Compile Include="zRegister\Regist_Helper.vb" />
    <Compile Include="zRegister\RegistWin32.vb" />
    <Compile Include="zTools\frmKingRoot.Designer.vb">
      <DependentUpon>frmKingRoot.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmKingRoot.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zVIP\ChargeHelper.vb" />
    <Compile Include="zVIP\DataRecoveryHelper.vb" />
    <Compile Include="zVIP\frmCharge.Designer.vb">
      <DependentUpon>frmCharge.vb</DependentUpon>
    </Compile>
    <Compile Include="zVIP\frmCharge.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zVIP\frmPayCoexist.Designer.vb">
      <DependentUpon>frmPayCoexist.vb</DependentUpon>
    </Compile>
    <Compile Include="zVIP\frmPayCoexist.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zVIP\frmWait.Designer.vb">
      <DependentUpon>frmWait.vb</DependentUpon>
    </Compile>
    <Compile Include="zVIP\frmWait.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zVIP\TBGuideHelper.vb" />
    <Compile Include="zWeibo\frmWeiboLogin.Designer.vb">
      <DependentUpon>frmWeiboLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeibo\frmWeiboLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeibo\frmWeiboSend.Designer.vb">
      <DependentUpon>frmWeiboSend.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeibo\frmWeiboSend.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeibo\WeiboConfig.vb" />
    <Compile Include="zWeibo\WeiboHelper.vb" />
    <Compile Include="zSetting\frmAbout.Designer.vb">
      <DependentUpon>frmAbout.vb</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmAbout.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\frmSetting.Designer.vb">
      <DependentUpon>frmSetting.vb</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmSetting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zComponents\tbFlowLayoutPanelEx.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zComponents\tbGroupPanel.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmDeviceApp.Designer.vb">
      <DependentUpon>frmDeviceApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmDeviceApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmApp.Designer.vb">
      <DependentUpon>frmApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmLocalApp.Designer.vb">
      <DependentUpon>frmLocalApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmLocalApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmUpdate.Designer.vb">
      <DependentUpon>frmUpdate.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmUpdate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmUpdateIgnore.Designer.vb">
      <DependentUpon>frmUpdateIgnore.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmUpdateIgnore.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmDeviceAppBackup.Designer.vb">
      <DependentUpon>frmDeviceAppBackup.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmDeviceAppBackup.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDataGridView\tbDataGridViewCollect.vb" />
    <Compile Include="zDataGridView\tbDataGridViewEx.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDataGridView\tbDataGridViewProgressEx.vb" />
    <Compile Include="zDataGridView\tbDataGridViewTextBoxCellEx.vb" />
    <Compile Include="zDeviceClass\AutoInstallHelper.vb" />
    <Compile Include="MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="ProcForm.Designer.vb">
      <DependentUpon>ProcForm.vb</DependentUpon>
    </Compile>
    <Compile Include="ProcForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zClass\ContactBase.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zClass\ContactDateTimePicker.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zClass\ContactTextBox.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zClass\ContactTextBoxAddress.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zClass\ContactTextBoxIM.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zClass\MP3Http.vb" />
    <Compile Include="zClass\MP3Search.vb" />
    <Compile Include="zDeviceFile\frmFile.Designer.vb">
      <DependentUpon>frmFile.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceFile\frmFile.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceFile\frmFileAppList.Designer.vb">
      <DependentUpon>frmFileAppList.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceFile\frmFileAppList.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceFile\frmFileBase.Designer.vb">
      <DependentUpon>frmFileBase.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceFile\frmFileBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceFile\frmPlistEdit.Designer.vb">
      <DependentUpon>frmPlistEdit.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceFile\frmPlistEdit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zComponents\tbTextBoxPath.Designer.vb">
      <DependentUpon>tbTextBoxPath.vb</DependentUpon>
    </Compile>
    <Compile Include="zComponents\tbTextBoxPath.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusic.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusicEdit.Designer.vb">
      <DependentUpon>frmMusicEdit.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusicEdit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusicLocal.Designer.vb">
      <DependentUpon>frmMusicLocal.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusicLocal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusicInfo.Designer.vb">
      <DependentUpon>frmMusicInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmMusicInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmVideo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\LyricsAnalysis.vb" />
    <Compile Include="zDeviceMedia\LyricsDouble.vb" />
    <Compile Include="zDeviceMedia\LyricsDrawBase.vb" />
    <Compile Include="zComponents\tbMusicPlayer.Designer.vb">
      <DependentUpon>tbMusicPlayer.vb</DependentUpon>
    </Compile>
    <Compile Include="zComponents\tbMusicPlayer.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zMsgForm\frmFileExist.designer.vb">
      <DependentUpon>frmFileExist.vb</DependentUpon>
    </Compile>
    <Compile Include="zMsgForm\frmFileExist.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zLiveUpdate\LiveUpdateForm.Designer.vb">
      <DependentUpon>LiveUpdateForm.vb</DependentUpon>
    </Compile>
    <Compile Include="zLiveUpdate\LiveUpdateForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zLiveUpdate\LiveUpdateHelper.vb" />
    <Compile Include="zPlugin\PluginLyrics.vb" />
    <Compile Include="zPlugin\PluginWebSite.vb" />
    <Compile Include="zSetting\frmAppExist.Designer.vb">
      <DependentUpon>frmAppExist.vb</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmAppExist.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\frmVideoConvert.Designer.vb">
      <DependentUpon>frmVideoConvert.vb</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmVideoConvert.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\AppRubbish.vb" />
    <Compile Include="zTools\CarrierPreview.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zTools\ClearRubbish.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zTools\ClearRubbishArgs.vb" />
    <Compile Include="zTools\ClearRubbishFileFound.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zTools\ClearRubbishList.Designer.vb">
      <DependentUpon>ClearRubbishList.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\ClearRubbishList.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zTools\frmClearRubbish.Designer.vb">
      <DependentUpon>frmClearRubbish.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmClearRubbish.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\frmMotifyCarrier.Designer.vb">
      <DependentUpon>frmMotifyCarrier.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmMotifyCarrier.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\frmTools.Designer.vb">
      <DependentUpon>frmTools.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmTools.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\RepairSpringboard.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zTools\tbBackgroundWorker.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zTools\tbDataGridViewListStyle.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zTools\tbFlowLayoutPanel.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zWeixin\AloneBackupsHelper.vb" />
    <Compile Include="zWeixin\frmChoiceWeChatSpareApp.Designer.vb">
      <DependentUpon>frmChoiceWeChatSpareApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmChoiceWeChatSpareApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmFindWeChatData.Designer.vb">
      <DependentUpon>frmFindWeChatData.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmFindWeChatData.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmWeChatSetting.Designer.vb">
      <DependentUpon>frmWeChatSetting.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmWeChatSetting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmWeixinEmoticon.Designer.vb">
      <DependentUpon>frmWeixinEmoticon.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmWeixinEmoticon.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmWeixinEmoticonNotify.Designer.vb">
      <DependentUpon>frmWeixinEmoticonNotify.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmWeixinEmoticonNotify.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmWeixinMain.Designer.vb">
      <DependentUpon>frmWeixinMain.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmWeixinMain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmWeixinRepair.Designer.vb">
      <DependentUpon>frmWeixinRepair.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmWeixinRepair.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\IMHtmlNodeHelper.vb" />
    <Compile Include="zWeixin\WeixinEmoticonHepler.vb" />
    <Compile Include="zWeixin\WeixinEmotionWebHelper.vb" />
    <Compile Include="zWeixin\WeixinHelper.vb" />
    <Compile Include="zWeixin\WeixinImportOrExportHelper.vb" />
    <Compile Include="zWelcome\DeviceShellHelper.vb" />
    <Compile Include="zWelcome\frmCanceliOSUpgrade.Designer.vb">
      <DependentUpon>frmCanceliOSUpgrade.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmCanceliOSUpgrade.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmDetail.Designer.vb">
      <DependentUpon>frmDetail.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmDetail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmDeviceList.Designer.vb">
      <DependentUpon>frmDeviceList.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmDeviceList.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmDevicePrice.Designer.vb">
      <DependentUpon>frmDevicePrice.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmDevicePrice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmH5GameInstall.Designer.vb">
      <DependentUpon>frmH5GameInstall.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmH5GameInstall.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmNewDevice.Designer.vb">
      <DependentUpon>frmNewDevice.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmNewDevice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmPreview.Designer.vb">
      <DependentUpon>frmPreview.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmPreview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmTuiInstall.designer.vb">
      <DependentUpon>frmTuiInstall.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmTuiInstall.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmWelcome.Designer.vb">
      <DependentUpon>frmWelcome.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmWelcome.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmSummary.Designer.vb">
      <DependentUpon>frmSummary.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmSummary.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWhatsApp\frmWhatsApp.Designer.vb">
      <DependentUpon>frmWhatsApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zWhatsApp\frmWhatsApp.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="..\iDataRecovery\MainFormDataRecovery.resx">
      <Link>MainFormDataRecovery.resx</Link>
      <DependentUpon>MainFormDataRecovery.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmMore.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmMore.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPopMsg.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmPopMsg.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSplash.resx">
      <DependentUpon>frmSplash.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTest.resx">
      <DependentUpon>frmTest.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidApp\frmAndroidDeviceApp.resx">
      <DependentUpon>frmAndroidDeviceApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidApp\frmAndroidLocalApp.resx">
      <DependentUpon>frmAndroidLocalApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidApp\frmAndroidUpdate.resx">
      <DependentUpon>frmAndroidUpdate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidApp\frmAndroidUpdateIgnore.resx">
      <DependentUpon>frmAndroidUpdateIgnore.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidContact\frmAndroidContact.resx">
      <DependentUpon>frmAndroidContact.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidFile\frmAndroidFile.resx">
      <DependentUpon>frmAndroidFile.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidFile\frmAndroidFileBase.resx">
      <DependentUpon>frmAndroidFileBase.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidFile\frmAndroidPlistEdit.resx">
      <DependentUpon>frmAndroidPlistEdit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMedia\frmAndroidMusic.resx">
      <DependentUpon>frmAndroidMusic.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMedia\frmAndroidMusicEdit.resx">
      <DependentUpon>frmAndroidMusicEdit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMedia\frmAndroidMusicInfo.resx">
      <DependentUpon>frmAndroidMusicInfo.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMedia\frmAndroidMusicLocal.resx">
      <DependentUpon>frmAndroidMusicLocal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMsgForm\frmAndroidDriver.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmAndroidDriver.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMsgForm\frmAndroidFeedback.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmAndroidFeedback.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMsgForm\frmAndroidNoConnecting.resx">
      <DependentUpon>frmAndroidNoConnecting.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMsgForm\frmAndroidQRCode.resx">
      <DependentUpon>frmAndroidQRCode.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMsgForm\frmAndroidUninstallMsg.resx">
      <DependentUpon>frmAndroidUninstallMsg.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMsgForm\frmAndroidUSBDebugBrowser.resx">
      <DependentUpon>frmAndroidUSBDebugBrowser.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidMsgForm\frmAndroidUSBDebugSimple.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmAndroidUSBDebugSimple.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidPhoto\frmAndroidWallpaperLocal.resx">
      <DependentUpon>frmAndroidWallpaperLocal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidPhoto\frmAndroidPhoto.resx">
      <DependentUpon>frmAndroidPhoto.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\ctrlChatSend.resx">
      <DependentUpon>ctrlChatSend.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\CtrlSelectContactItem.resx">
      <DependentUpon>CtrlSelectContactItem.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\EasyFlowLayoutPanel.resx">
      <DependentUpon>EasyFlowLayoutPanel.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMS.resx">
      <DependentUpon>frmAndroidSMS.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSChatGroup.resx">
      <DependentUpon>frmAndroidSMSChatGroup.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSChatSingle.resx">
      <DependentUpon>frmAndroidSMSChatSingle.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSDelete.resx">
      <DependentUpon>frmAndroidSMSDelete.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSExport.resx">
      <DependentUpon>frmAndroidSMSExport.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSFaces.resx">
      <DependentUpon>frmAndroidSMSFaces.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSFail.resx">
      <DependentUpon>frmAndroidSMSFail.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSNotify.resx">
      <DependentUpon>frmAndroidSMSNotify.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSSelectContact.resx">
      <DependentUpon>frmAndroidSMSSelectContact.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\frmAndroidSMSSend.resx">
      <DependentUpon>frmAndroidSMSSend.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidSMS\TableSelectContact.resx">
      <DependentUpon>TableSelectContact.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidWelcome\frmAndroidDetail.resx">
      <DependentUpon>frmAndroidDetail.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidWelcome\frmAndroidNoDevice.resx">
      <DependentUpon>frmAndroidNoDevice.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidWelcome\frmAndroidPreview.resx">
      <DependentUpon>frmAndroidPreview.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidWelcome\frmAndroidSummary.resx">
      <DependentUpon>frmAndroidSummary.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroidWelcome\frmAndroidWelcome.resx">
      <DependentUpon>frmAndroidWelcome.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zAndroid\frmAndroidBase.resx">
      <DependentUpon>frmAndroidBase.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zClass\ContactMerge.resx">
      <SubType>Designer</SubType>
      <DependentUpon>ContactMerge.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmAppDetail.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmAppDetail.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmAppFilter.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmAppFilter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmCommonApp.resx">
      <DependentUpon>frmCommonApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmiTunesLoginV3.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmiTunesLoginV3.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceIcon\frmIconManage.resx">
      <DependentUpon>frmIconManage.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceIcon\frmIconSort.resx">
      <DependentUpon>frmIconSort.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceInfoMgr\frmBookmark.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBookmark.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceInfoMgr\frmContact.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmContact.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceInfoMgr\frmContactConfirm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmContactConfirm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceInfoMgr\frmContactRepeat.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmContactRepeat.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceInfoMgr\frmNote.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmNote.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmMediaBase.resx">
      <DependentUpon>frmMediaBase.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmRingtoneInfo.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmRingtoneInfo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmRingtoneSetup.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmRingtoneSetup.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDevicePhoto\frmImportLivePhoto.resx">
      <DependentUpon>frmImportLivePhoto.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDevicePhoto\frmPhotoBase.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmPhotoBase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDevicePhoto\frmPhotoConvert.resx">
      <DependentUpon>frmPhotoConvert.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDevicePhoto\frmPhotoInfo.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmPhotoInfo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDevicePhoto\frmPhotoOrder.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmPhotoOrder.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDevicePhoto\frmWallpaperLocal.resx">
      <DependentUpon>frmWallpaperLocal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDevicePhoto\frmPhotoWithTui.resx">
      <DependentUpon>frmPhotoWithTui.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmAppDownloaded.resx">
      <DependentUpon>frmAppDownloaded.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmDownloadAction.resx">
      <DependentUpon>frmDownloadAction.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmDownloadCenter.resx">
      <DependentUpon>frmDownloadCenter.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmDownloaded.resx">
      <DependentUpon>frmDownloaded.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmDownloading.resx">
      <DependentUpon>frmDownloading.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDuobao\frmDuobao.resx">
      <DependentUpon>frmDuobao.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDuobao\frmJinbaoCourse.resx">
      <DependentUpon>frmJinbaoCourse.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFlash\frmCheckiCloud.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmCheckiCloud.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFlash\frmCommonFlash.resx">
      <DependentUpon>frmCommonFlash.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFlash\frmFirmware.resx">
      <DependentUpon>frmFirmware.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zFlash\frmFlash.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmFlash.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zFlash\frmLoginAppleid.resx">
      <DependentUpon>frmLoginAppleid.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zLogin\frmLogin.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmLogin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zLogin\frmPersonalCenter.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmPersonalCenter.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zQQ\frmQQ.resx">
      <DependentUpon>frmQQ.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zRestore\frmMSMSetting.resx">
      <DependentUpon>frmMSMSetting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmSettingSkin.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmSettingSkin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmSettingV3.resx">
      <DependentUpon>frmSettingV3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmBatteryManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBatteryManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmKingRoot.resx">
      <DependentUpon>frmKingRoot.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmRepair.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmRepair.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmRepairPayment.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmRepairPayment.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmSystemLog.resx">
      <DependentUpon>frmSystemLog.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmTip.resx">
      <DependentUpon>frmTip.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmDownloadWallpaper.resx">
      <DependentUpon>frmDownloadWallpaper.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDownload\frmSelectDevice.resx">
      <DependentUpon>frmSelectDevice.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zImageHelper\frmImageSearch.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmImageSearch.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zMsgForm\tbSplashBox.resx">
      <DependentUpon>tbSplashBox.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\AuthorityWarningForm.resx">
      <DependentUpon>AuthorityWarningForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zRestore\frmBackupCallHistory.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBackupCallHistory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zRestore\frmBackupContact.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBackupContact.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zRestore\frmBackupCreate.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBackupCreate.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zRestore\frmBackupList.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBackupList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zRestore\frmBackupPreview.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBackupPreview.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zRestore\frmBackupSMS.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmBackupSMS.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmAbout.resx">
      <DependentUpon>frmAbout.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmSetting.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmSetting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>MainForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ProcForm.resx">
      <DependentUpon>ProcForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmDeviceApp.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmDeviceApp.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmApp.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmApp.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmLocalApp.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmLocalApp.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmDeviceAppBackup.resx">
      <DependentUpon>frmDeviceAppBackup.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmUpdate.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmUpdate.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceApp\frmUpdateIgnore.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmUpdateIgnore.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceFile\frmFile.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmFile.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceFile\frmFileAppList.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmFileAppList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceFile\frmFileBase.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmFileBase.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceFile\frmPlistEdit.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmPlistEdit.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zComponents\tbTextBoxPath.resx">
      <SubType>Designer</SubType>
      <DependentUpon>tbTextBoxPath.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmMusic.resx">
      <DependentUpon>frmMusic.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmMusicEdit.resx">
      <DependentUpon>frmMusicEdit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmMusicLocal.resx">
      <DependentUpon>frmMusicLocal.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmMusicInfo.resx">
      <DependentUpon>frmMusicInfo.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zComponents\tbMusicPlayer.resx">
      <DependentUpon>tbMusicPlayer.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceMedia\frmVideo.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmVideo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zMsgForm\frmFileExist.resx">
      <DependentUpon>frmFileExist.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zLiveUpdate\LiveUpdateForm.resx">
      <DependentUpon>LiveUpdateForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmAppExist.resx">
      <DependentUpon>frmAppExist.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmVideoConvert.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmVideoConvert.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zVIP\frmCharge.resx">
      <DependentUpon>frmCharge.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zVIP\frmPayCoexist.resx">
      <DependentUpon>frmPayCoexist.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zVIP\frmWait.resx">
      <DependentUpon>frmWait.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWebsite\frmSite.resx">
      <DependentUpon>frmSite.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeibo\frmVerify.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmVerify.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeibo\frmWeiboLogin.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmWeiboLogin.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeibo\frmWeiboSend.resx">
      <DependentUpon>frmWeiboSend.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\ClearRubbishList.resx">
      <SubType>Designer</SubType>
      <DependentUpon>ClearRubbishList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmClearRubbish.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmClearRubbish.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmMotifyCarrier.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmMotifyCarrier.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zTools\frmTools.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmTools.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmAndroidWeixin.resx">
      <DependentUpon>frmAndroidWeixin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmChoiceWeChatSpareApp.resx">
      <DependentUpon>frmChoiceWeChatSpareApp.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmFindWeChatData.resx">
      <DependentUpon>frmFindWeChatData.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmWeChatSetting.resx">
      <DependentUpon>frmWeChatSetting.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmWeixin.resx">
      <DependentUpon>frmWeixin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmWeixinEmoticon.resx">
      <DependentUpon>frmWeixinEmoticon.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmWeixinEmoticonApp.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmWeixinEmoticonApp.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmWeixinEmoticonNotify.resx">
      <DependentUpon>frmWeixinEmoticonNotify.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmWeixinMain.resx">
      <DependentUpon>frmWeixinMain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWeixin\frmWeixinRepair.resx">
      <DependentUpon>frmWeixinRepair.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmCanceliOSUpgrade.resx">
      <DependentUpon>frmCanceliOSUpgrade.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmCommonWel.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmCommonWel.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmDetail.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmDetail.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmDeviceList.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmDeviceList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmDevicePrice.resx">
      <DependentUpon>frmDevicePrice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmH5GameInstall.resx">
      <DependentUpon>frmH5GameInstall.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmHotApps.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmHotApps.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmNewDevice.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmNewDevice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmPreview.resx">
      <DependentUpon>frmPreview.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmSummaryRecommend.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmSummaryRecommend.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmTuiInstall.resx">
      <DependentUpon>frmTuiInstall.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmTuiInstallTutorial.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmTuiInstallTutorial.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmWelcome.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmWelcome.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmSummary.resx">
      <DependentUpon>frmSummary.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zWhatsApp\frmWhatsApp.resx">
      <DependentUpon>frmWhatsApp.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="IncludeDlls\clear.bat">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\frm_bg_shadow.png" />
    <None Include="Resources\frm_top_bg.png" />
    <None Include="Resources\btn_close.png" />
    <None Include="Resources\btn_max.png" />
    <None Include="Resources\btn_min.png" />
    <None Include="Resources\btn_restore.png" />
    <None Include="Resources\top_default.png" />
    <None Include="Resources\top_bg_select.png" />
    <None Include="Resources\top_bg_select_h.png" />
    <None Include="Resources\tsmi_checked.png" />
    <None Include="Resources\btn_4_white.png" />
    <None Include="Resources\btn_4_blue.png" />
    <None Include="Resources\iTong.ico" />
    <None Include="Resources\frm_top_split.png" />
    <None Include="Resources\login_setting.png" />
    <None Include="Resources\login_pic_bg.png" />
    <None Include="Resources\login_down_price.png" />
    <None Include="Resources\login_upgrade.png" />
    <None Include="Resources\btn_error_input.png" />
    <None Include="Resources\icon_upgrade.png" />
    <None Include="Resources\summary_btn_3_edit.png" />
    <None Include="Resources\summary_btn_4_close.png" />
    <None Include="Resources\summary_btn_4_ok.png" />
    <None Include="Resources\contact_btn_hover.png" />
    <None Include="Resources\contact_btn_down.png" />
    <None Include="Resources\contact_btn_3_add.png" />
    <None Include="Resources\contact_btn_3_del.png" />
    <None Include="Resources\btn_3_goto.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_green.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="IncludeDlls\7z.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\7z_x64.dll" />
    <Content Include="IncludeDlls\Flash\msvcp120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\Flash\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\Flash\tblibideviceactivation.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\Flash\tblibidevicerestore.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\Flash\tblibxml2.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\libchara.dll" />
    <Content Include="IncludeDlls\libeay32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\libeay64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\libWPD.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\libWPD_x64.dll" />
    <Content Include="IncludeDlls\ssleay32.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\ssleay64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\System.Data.SQLite.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\System.Data.SQLite3.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <BaseApplicationManifest Include="My Project\app.manifest">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </BaseApplicationManifest>
    <None Include="IncludeDlls\x64.bat" />
    <None Include="IncludeDlls\x86.bat" />
    <None Include="Lang\en-US.lang">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Lang\ja-JP.lang">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Lang\zh-CN.lang">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Lang\zh-TW.lang">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\file_unfindfile_large.png" />
    <None Include="Resources\file_unfindfile_small.png" />
    <None Include="Resources\btn_checkExcpt_4.png" />
    <None Include="Resources\app_downloads_background.png" />
    <None Include="Resources\app_downloads_value.png" />
    <None Include="Resources\btn_sinawebbo_4.png" />
    <None Include="Resources\ipcc_iPhone_Telecom_Default.zip" />
    <None Include="Resources\ipcc_iPhone_Telecom_Payload.zip" />
    <None Include="Resources\pnl_Slider.png" />
    <None Include="Resources\pnl_slider_normal.png" />
    <None Include="Resources\pnl_slider_blue.png" />
    <None Include="Resources\gif_searching.gif" />
    <None Include="Resources\btn_3_search.png" />
    <None Include="Resources\ljql-btn_03.png" />
    <None Include="Resources\jindu-bg_07.png" />
    <None Include="Resources\jindu_07.png" />
    <None Include="Resources\icon_warning1.png" />
    <None Include="Resources\sina-weibo_21.png" />
    <None Include="Resources\frm_bg_state_button.png" />
    <None Include="Resources\btn_4_clearrubbish.png" />
    <None Include="Resources\favicon.png" />
    <None Include="Resources\html_clear_welcome1.html" />
    <None Include="Resources\html_clear_welcome2.html" />
    <None Include="Resources\icon_facebook.png" />
    <None Include="Resources\icon_twitter.png" />
    <None Include="Resources\btn_facebook_4.png" />
    <None Include="Resources\more_recover.png" />
    <None Include="Resources\more_unlock.png" />
    <None Include="Resources\close_icloud_oversea.png" />
    <None Include="Resources\close_icloud_step.png" />
    <None Include="Resources\file_fileempty_smile.png" />
    <None Include="Resources\email_account_oversea.png" />
    <None Include="Resources\icon_time.png" />
    <None Include="Resources\more_restar_light.png" />
    <None Include="Resources\more_shurtdown.png" />
    <None Include="Resources\more_shurtdown_light.png" />
    <None Include="Resources\more_restar.png" />
    <None Include="Resources\more_feedback.png" />
    <None Include="Resources\more_feedback_light.png" />
    <None Include="Resources\more_restore_light.png" />
    <None Include="Resources\more_restore.png" />
    <None Include="Resources\icon_note.png" />
    <None Include="Resources\icon_appdata.png" />
    <None Include="Resources\icon_setting.png" />
    <None Include="Resources\icon_sms.png" />
    <None Include="Resources\icon_photo.png" />
    <None Include="Resources\icon_contact.png" />
    <None Include="Resources\more_weixin_light.png" />
    <None Include="Resources\btn_4_refresh1.png" />
    <None Include="Resources\more_weixin.png" />
    <None Include="Resources\file_weixin_large.png" />
    <None Include="Resources\backup_contact_16.png" />
    <None Include="Resources\backup_callhistory_16.png" />
    <None Include="Resources\backup_save_16.png" />
    <None Include="Resources\backup_normal_16.png" />
    <None Include="Resources\backup_photo_16.png" />
    <None Include="Resources\backup_message_16.png" />
    <None Include="Resources\backup_contact_75.png" />
    <None Include="Resources\backup_message_75.png" />
    <None Include="Resources\backup_callhistory_75.png" />
    <None Include="Resources\backup_photo_75.png" />
    <None Include="Resources\btn_4_restore.png" />
    <None Include="Resources\icon_download.png" />
    <None Include="Resources\btn_4_blue1.png" />
    <None Include="Resources\more_pay.png" />
    <None Include="Resources\more_sms.png" />
    <None Include="Resources\more_sms_light.png" />
    <None Include="Resources\more_weixinrepair_light.png" />
    <None Include="Resources\more_weixinrepair.png" />
    <None Include="Resources\btn_4_close.png" />
    <None Include="Resources\backup_notes_75.png" />
    <None Include="Resources\backup_notes_16.png" />
    <None Include="Resources\btn_4_view.png" />
    <None Include="Resources\icon_lock.png" />
    <None Include="Resources\device_default.png" />
    <None Include="Resources\more_clearicon.png" />
    <None Include="Resources\more_clearicon_light.png" />
    <None Include="Resources\radio_bg.png" />
    <None Include="Resources\icon_listMusic.png" />
    <None Include="Resources\radio_left.png" />
    <None Include="Resources\radio_left_down.png" />
    <None Include="Resources\radio_right.png" />
    <None Include="Resources\radio_right_down.png" />
    <None Include="Resources\icons-itong_downloading_116.png" />
    <None Include="Resources\repair_pnl_bg.png" />
    <None Include="Resources\repair_device.png" />
    <None Include="Resources\pic_trust.png" />
    <None Include="Resources\file_letv.png" />
    <None Include="Resources\icon_ringtone.png" />
    <None Include="Resources\icon_alarm.png" />
    <None Include="Resources\icon_notify.png" />
    <None Include="Resources\btn_welcome_connect.png" />
    <None Include="Resources\contact_delete.png" />
    <None Include="Resources\contact_delete_hover.png" />
    <None Include="Resources\contact-default-android.png" />
    <None Include="Resources\btn_transpat.png" />
    <None Include="Resources\icon_remove_all.png" />
    <None Include="Resources\frm_splashmsgbox_bg.png" />
    <None Include="Resources\bg_light_blue.png" />
    <None Include="Resources\btn_add.png" />
    <None Include="Resources\btn_faces_1.png" />
    <None Include="Resources\btn_send_sms.png" />
    <None Include="Resources\btn_exchange.png" />
    <None Include="Resources\gif_loading_white 14.gif" />
    <None Include="Resources\btn_contactmarge_4.png" />
    <None Include="Resources\btn_contactremove_4.png" />
    <None Include="Resources\new_top_app.png" />
    <None Include="Resources\new_top_file.png" />
    <None Include="Resources\new_top_home.png" />
    <None Include="Resources\new_top_more.png" />
    <None Include="Resources\new_top_music.png" />
    <None Include="Resources\new_top_photo.png" />
    <None Include="Resources\new_top_site.png" />
    <None Include="Resources\new_top_video.png" />
    <None Include="Resources\new_tool_hover.png" />
    <None Include="Resources\new_tool_select.png" />
    <None Include="Resources\new_dev_exit_hover.png" />
    <None Include="Resources\new_dev_exit_select.png" />
    <None Include="Resources\new_dev_exit_unselect.png" />
    <None Include="Resources\new_top_video_light.png" />
    <None Include="Resources\new_top_file_light.png" />
    <None Include="Resources\new_frm_bg_sub.png" />
    <None Include="Resources\new_device_airdroid.png" />
    <None Include="Resources\new_device_airdroid_gray.png" />
    <None Include="Resources\new_device_ios.png" />
    <None Include="Resources\new_device_ios_gray.png" />
    <None Include="Resources\new_device_default.png" />
    <None Include="Resources\new_icon_repair.png" />
    <None Include="Resources\more_crashreport.png" />
    <None Include="Resources\more_crashreport_light.png" />
    <None Include="Resources\more_systemlog.png" />
    <None Include="Resources\more_systemlog_light.png" />
    <None Include="Resources\btn_4_failurerestore.png" />
    <None Include="Resources\settingtype_selected.png" />
    <None Include="Resources\settingtype_hover.png" />
    <None Include="Resources\btn_update_4.png" />
    <None Include="Resources\more_ebook_light.png" />
    <None Include="Resources\more_applemedia.png" />
    <None Include="Resources\more_applemedia_light.png" />
    <None Include="Resources\more_ebook.png" />
    <None Include="Resources\sms_auth.png" />
    <None Include="Resources\btn_photo_delete_one.png" />
    <None Include="Resources\btn_photo_import_one_android.png" />
    <None Include="Resources\btn_photo_import_one_ipad.png" />
    <None Include="Resources\btn_photo_import_one_iphone.png" />
    <None Include="Resources\btn_photo_share_one.png" />
    <None Include="Resources\photo_toolbar_bg.png" />
    <None Include="Resources\photo_chk_true.png" />
    <None Include="Resources\photo_chk_false.png" />
    <None Include="Resources\android_pmdata_auth.png" />
    <None Include="Resources\more_smschat_light.png" />
    <None Include="Resources\more_smschat.png" />
    <None Include="Resources\btn_viewdate_checked.png" />
    <None Include="Resources\btn_viewdate_unchecked.png" />
    <None Include="Resources\btn_setringtone_4.png" />
    <None Include="Resources\dgv_4_setringtone.png" />
    <None Include="Resources\dgv_4_setalarm.png" />
    <None Include="Resources\dgv_4_setnotification.png" />
    <None Include="Resources\btn_viewlargeicon_android.png" />
    <None Include="Resources\btn_viewlargeicon_unandroid.png" />
    <None Include="Resources\btn_viewlist_unipad.png" />
    <None Include="Resources\btn_photo_export_one.png" />
    <None Include="Resources\btn_viewlist_ipad.png" />
    <None Include="Resources\btn_viewlist_uniphone.png" />
    <None Include="Resources\btn_viewlist_ios.png" />
    <None Include="Resources\btn_viewlist_iphone.png" />
    <None Include="Resources\btn_viewlist_unios.png" />
    <None Include="Resources\btn_shortmessagechat_4.png" />
    <None Include="Resources\btn_wallpaper_4.png" />
    <None Include="Resources\icon_android_feedback.png" />
    <None Include="Resources\new_top_jailbreak.png" />
    <None Include="Resources\new_top_ebook_light.png" />
    <None Include="Resources\new_top_jailbreak_light.png" />
    <None Include="Resources\new_top_ebook.png" />
    <None Include="Resources\btn_viewlist_unselected.png" />
    <None Include="Resources\btn_viewlist_selected.png" />
    <None Include="Resources\more_itunes_on.png" />
    <None Include="Resources\more_itunes_light.png" />
    <None Include="Resources\more_itunes_off.png" />
    <None Include="Resources\btn_4_reply.png" />
    <None Include="Resources\Connection_icon_b.png" />
    <None Include="Resources\Connection_icon_c.png" />
    <None Include="Resources\Connection_icon_a.png" />
    <None Include="Resources\Connection_icon_s.png" />
    <None Include="Resources\connection_image.png" />
    <None Include="Resources\more_emoticon.png" />
    <None Include="Resources\more_emoticon_light.png" />
    <None Include="Resources\btn_back_4.png" />
    <None Include="Resources\btn_editor_1.png" />
    <None Include="Resources\icon_emotion.png" />
    <None Include="Resources\download_icon_weixinemoticon.png" />
    <None Include="Resources\gif_loading_repair.gif" />
    <None Include="Resources\icon_canrepair.png" />
    <None Include="Resources\icon_cannotrepair.png" />
    <None Include="Resources\icon_search.png" />
    <None Include="Resources\repair_red.png" />
    <None Include="Resources\repair_green.png" />
    <None Include="Resources\repair_bg_white.png" />
    <None Include="Resources\icon_notneedrepair.png" />
    <Content Include="IncludeDlls\System.Data.SQLite3_x64.dll" />
    <None Include="Resources\icon_shield.png" />
    <None Include="Resources\icon_appleid.png" />
    <None Include="Resources\frm_bg_tip_binding.png" />
    <None Include="Resources\icon_attention.png" />
    <None Include="Resources\pic_login_apple_id.png" />
    <None Include="Resources\frm_bg_tip_binding1.png" />
    <None Include="Resources\file_pptv.png" />
    <None Include="Resources\btn_4_white1.png" />
    <None Include="Resources\btn_closedeye.png" />
    <None Include="Resources\btn_openeye.png" />
    <None Include="Resources\icon_login.png" />
    <None Include="Resources\repair_bg_payment.png" />
    <None Include="Resources\repair_bg_title.png" />
    <None Include="Resources\repair_icon_norecord.png" />
    <None Include="Resources\repair_setp1.png" />
    <None Include="Resources\repair_setp2.png" />
    <None Include="Resources\repair_icon_tui.png" />
    <None Include="Resources\icon_login_empty.png" />
    <None Include="Resources\frm_bg_login.png" />
    <None Include="Resources\btn_4_orange.png" />
    <None Include="Resources\app_cell_failretry.png" />
    <None Include="Resources\dgv_payment.png" />
    <None Include="Resources\icon_login_empty_light.png" />
    <None Include="Resources\backup_password.png" />
    <None Include="Resources\media_ringtone_setup.gif" />
    <None Include="Resources\more_itunesuninstaller.png" />
    <None Include="Resources\backup_weixin_16.png" />
    <None Include="Resources\backup_weixin_75.png" />
    <None Include="Resources\btn_2_tricklecharge.png" />
    <None Include="Resources\btn_2_quickcharge.png" />
    <None Include="Resources\btn_2_continuecharge.png" />
    <None Include="Resources\btn_4_battery.png" />
    <None Include="Resources\more_bookmark_light.png" />
    <None Include="Resources\more_bookmark.png" />
    <None Include="Resources\btn_tricklecharge_readonly.png" />
    <None Include="Resources\btn_quickcharge_readonly.png" />
    <None Include="Resources\btn_continuecharge_readonly.png" />
    <None Include="Resources\btn_tricklecharge.png" />
    <None Include="Resources\btn_quickcharge.png" />
    <None Include="Resources\btn_continuecharge.png" />
    <None Include="Resources\battery_progress_background01.png" />
    <None Include="Resources\battery_progress_background.png" />
    <None Include="Resources\close_icloud_safari.png" />
    <None Include="Resources\btn_1_transparent.png" />
    <None Include="Resources\battery_device_iphone_default.png" />
    <None Include="Resources\battery_device_ipad_default.png" />
    <None Include="Resources\btn_1_checked.png" />
    <None Include="Resources\btn_4_blue_jaibreak.png" />
    <None Include="Resources\btn_skin_3.png" />
    <None Include="Resources\appitem_check.png" />
    <None Include="Resources\appitem_uncheck.png" />
    <None Include="Resources\appitem_installed.png" />
    <None Include="Resources\btn_close1.png" />
    <None Include="Resources\gif_fiveyear.gif" />
    <None Include="Resources\file_bfyy.png" />
    <None Include="Resources\gif_loading_231.gif">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\flash_second.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\flash_first.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\flashsucceed.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Resources\gif_installtui.gif" />
    <None Include="Resources\close_findmyiphone.png" />
    <None Include="Resources\androidSummary_weixin.png" />
    <None Include="Resources\androidSummary_contacts.png" />
    <None Include="Resources\androidSummary_SMS.png" />
    <None Include="Resources\btn_StartRoot.png" />
    <None Include="Resources\cell_false.png" />
    <None Include="Resources\cell_true.png" />
    <None Include="Resources\btn_Weixin_CancelLimit.png" />
    <None Include="Resources\btn_WeixinNotRoot_Step.png" />
    <None Include="Resources\btn_weixinNotRoot_step1.png" />
    <None Include="Resources\btn_weixinNotRoot_step2.png" />
    <None Include="Resources\btn_bg_border.png" />
    <None Include="Resources\btn_icom.png" />
    <None Include="Resources\flash_decollator.png" />
    <None Include="Resources\dgv_personal.png" />
    <Content Include="IncludeDlls\System.Data.SQLite_x64.dll" />
    <None Include="Resources\download_icon_firmwarebeta.png" />
    <None Include="Resources\btn_3_openlog.png" />
    <None Include="Resources\bg_Flash_Guide.png" />
    <None Include="Resources\bg_Jailbreak_Guide.png" />
    <None Include="Resources\bg_guide_median.png" />
    <None Include="Resources\close_findmyiphone.gif" />
    <None Include="Resources\app_icon_jinbao.png" />
    <None Include="Resources\gif_getinivationcode.gif" />
    <Content Include="IncludeDlls\tbDecrypt.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\tbDecrypt_x64.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="IncludeDlls\WeChat\WeChatHelper.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\btn_4_finddata.png" />
    <None Include="Resources\btn_4_refresh2.png" />
    <None Include="Resources\bg_Emoticon_Android.png" />
    <None Include="Resources\btn_Emoticon_Download.png" />
    <None Include="Resources\excel_header.txt" />
    <None Include="Resources\btn_photo_download_one.png" />
    <None Include="Resources\flash_third.png" />
    <None Include="Resources\btn_4_red.png" />
    <None Include="Resources\btn_datarecovery_4.png" />
    <None Include="Resources\btn_active_3.png" />
    <Content Include="IncludeDlls\WeChat\WeChatHelper1.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\dgv_bg_header_green.png" />
    <None Include="Resources\backup_exitapp.gif" />
    <Content Include="Resources\gif_loading_24.gif" />
    <None Include="Resources\icon_warning2.png" />
    <None Include="Resources\new_top_bg.png" />
    <None Include="Resources\new_bottom_bg.png" />
    <None Include="Resources\new_top_fiveyear.png" />
    <None Include="Resources\new_top_watermark.png" />
    <None Include="Resources\new_top_line.png" />
    <None Include="Resources\new_top_light.png" />
    <None Include="Resources\skin_default_setting.png" />
    <None Include="Resources\skin_select.png" />
    <None Include="Resources\icon_icloud_hint.png" />
    <None Include="Resources\halving_line.png" />
    <None Include="Resources\more_KingRoot.png" />
    <None Include="Resources\pnl_bg_border_light.png" />
    <None Include="Resources\photo_livephoto.png" />
    <None Include="Resources\ico_plaint.png" />
    <None Include="Resources\H5Game_install_step2.png" />
    <None Include="Resources\h5game_install_detail.png" />
    <None Include="Resources\H5Game_install_step1.png" />
    <None Include="Resources\H5Game_install_step2.jpg" />
    <None Include="Resources\icon_hot.png" />
    <None Include="Resources\icon_booknodata_120.png" />
    <None Include="Resources\icon_musicnodata_100.png" />
    <None Include="Resources\icon_musicnodata_120.png" />
    <None Include="Resources\icon_photonodata_100.png" />
    <None Include="Resources\icon_photonodata_120.png" />
    <None Include="Resources\icon_ringnodata_100.png" />
    <None Include="Resources\icon_ringnodata_120.png" />
    <None Include="Resources\icon_videonodata_100.png" />
    <None Include="Resources\icon_videonodata_120.png" />
    <None Include="Resources\icon_appnodata_100.png" />
    <None Include="Resources\icon_appnodata_120.png" />
    <None Include="Resources\icon_booknodata_100.png" />
    <None Include="Resources\icon_downnodata_100.png" />
    <None Include="Resources\icon_firmwarenodata_100.png" />
    <None Include="Resources\icon_prompt.png" />
    <None Include="Resources\icon_emoticon_large.png" />
    <None Include="Resources\icon_Discount.png" />
    <None Include="Resources\icon_taobao.png" />
    <Content Include="Resources\livephoto_btn_add.png" />
    <Content Include="Resources\livephoto_pic_jpg.png" />
    <None Include="Resources\livephoto_pic_jpgini.png" />
    <Content Include="Resources\livephoto_pic_mov.png" />
    <None Include="Resources\livephoto_pic_movini.png" />
    <None Include="Resources\new_device_model.png" />
    <None Include="Resources\photo_continuousshots.png" />
    <None Include="Resources\sinaweibologo.png" />
    <None Include="Resources\qzonelogo.png" />
    <None Include="Resources\logo.png" />
    <None Include="Resources\more_weixincoexist_light.png" />
    <None Include="Resources\more_weixincoexist.png" />
    <None Include="Resources\more_valuation_light.png" />
    <None Include="Resources\more_valuation.png" />
    <None Include="Resources\more_qq.png" />
    <None Include="Resources\new_device_default_ios.png" />
    <Content Include="Resources\step_1.png" />
    <Content Include="Resources\step_1_w.png" />
    <Content Include="Resources\step_2.png" />
    <Content Include="Resources\step_2_w.png" />
    <Content Include="Resources\step_3.png" />
    <Content Include="Resources\step_3_w.png" />
    <Content Include="Resources\step_bg.png" />
    <Content Include="Resources\step_bg_w.png" />
    <Content Include="Resources\step_ok.png" />
    <None Include="Resources\summary_binding_apple_id.png" />
    <None Include="Resources\weixin_install.png" />
    <None Include="Resources\weixin_emoticon.png" />
    <None Include="Resources\weixin_error.png" />
    <None Include="Resources\weixin_close_pwd.png" />
    <None Include="Resources\weixin_bg_bottom.png" />
    <None Include="Resources\weixin_nolockscreen.png" />
    <None Include="Resources\summary_appleid_split.png" />
    <None Include="Resources\summary_repair_pay_v3.png" />
    <None Include="Resources\weixin_close_pwd2.png" />
    <None Include="Resources\weixin_close_pwd1.png" />
    <None Include="Resources\tool_clean_crashlog.png" />
    <None Include="Resources\tool_clean_photos.png" />
    <None Include="Resources\tui_tutorial_bg.png" />
    <None Include="Resources\summary_appinfo_bg.png" />
    <None Include="Resources\summary_weixin.png" />
    <None Include="Resources\summary_backup.png" />
    <None Include="Resources\weixin_recommendKingRoot.png" />
    <None Include="Resources\weixin_notroot.png" />
    <None Include="Resources\weixin_startKingRoot.png" />
    <None Include="Resources\Weixin_KingrootRecommand.png" />
    <None Include="Resources\Weixin_notRootOrLimit.png" />
    <None Include="Resources\Weixin_notRootOrLimit_New.png" />
    <None Include="Resources\usb_debug_miui1.png" />
    <None Include="Resources\usb_debug_miui2.png" />
    <None Include="Resources\usb_debug_miui3.png" />
    <None Include="Resources\usb_debug_miui4.png" />
    <None Include="Resources\usb_debug_miui5.png" />
    <None Include="Resources\usb_debug_miui6.png" />
    <None Include="Resources\usb_debug_miui7.png" />
    <None Include="Resources\usb_debug_miui8.png" />
    <None Include="Resources\usb_debug_flyme8.png" />
    <None Include="Resources\usb_debug_flyme1.png" />
    <None Include="Resources\usb_debug_flyme2.png" />
    <None Include="Resources\usb_debug_flyme3.png" />
    <None Include="Resources\usb_debug_flyme4.png" />
    <None Include="Resources\usb_debug_flyme5.png" />
    <None Include="Resources\usb_debug_flyme6.png" />
    <None Include="Resources\usb_debug_flyme7.png" />
    <None Include="Resources\usb_debug_htc9.png" />
    <None Include="Resources\usb_debug_htc1.png" />
    <None Include="Resources\usb_debug_htc2.png" />
    <None Include="Resources\usb_debug_htc3.png" />
    <None Include="Resources\usb_debug_htc4.png" />
    <None Include="Resources\usb_debug_htc5.png" />
    <None Include="Resources\usb_debug_htc6.png" />
    <None Include="Resources\usb_debug_htc7.png" />
    <None Include="Resources\usb_debug_htc8.png" />
    <None Include="Resources\usb_debug_coolpad6.png" />
    <None Include="Resources\usb_debug_coolpad1.png" />
    <None Include="Resources\usb_debug_coolpad2.png" />
    <None Include="Resources\usb_debug_coolpad3.png" />
    <None Include="Resources\usb_debug_coolpad4.png" />
    <None Include="Resources\usb_debug_coolpad5.png" />
    <None Include="Resources\summary_icon_valuation.png" />
    <None Include="Resources\summary_qq.png" />
    <Content Include="Resources\tui_web_InstallOperate.png" />
    <Content Include="Resources\tui_web_installStep1.png" />
    <Content Include="Resources\tui_web_installStep2.png" />
    <Content Include="Resources\tui_web_installStep3.png" />
    <Content Include="Resources\tui_web_mobliePicture.png" />
    <None Include="Resources\wechat_message_lock.png" />
    <None Include="Resources\tvw_node_bg_select.png" />
    <None Include="Resources\weixin_dll_silk.dll" />
    <None Include="Resources\weixin_guide_bg.png" />
    <None Include="Resources\weixin_close3.gif" />
    <None Include="Resources\wechat_device_left.png" />
    <None Include="Resources\wechat_restore_left.png" />
    <None Include="Resources\wechat_device_bg.png" />
    <None Include="Resources\wechat_restore_new.png" />
    <None Include="Resources\wechat_device_new.png" />
    <None Include="Resources\tvw_hot_2.png" />
    <None Include="Resources\tvw_select_2.png" />
    <None Include="Resources\weixin_emo_app.png" />
    <None Include="Resources\weixin_emo_banner.png" />
    <None Include="Resources\weixin_emo_importing.png" />
    <None Include="Resources\weixin_emo_importingfailure.png" />
    <None Include="Resources\weixin_emo_importingsucceed.png" />
    <None Include="Resources\wechat_device_return.png" />
    <Content Include="zzReplace\ImageResources\7z.dll" />
    <Content Include="zzReplace\ImageResources\libiTunes.dll" />
    <Content Include="zzReplace\ImageResources\libWPD.dll" />
    <Content Include="zzReplace\ImageResources\System.Data.SQLite.dll" />
    <Content Include="zzReplace\ImageResources\System.Data.SQLite3.dll" />
    <Content Include="zzReplace\ImageResources\WebSocket4Net.dll" />
    <Content Include="zzReplace\ResourceTool.exe" />
    <None Include="Resources\summary_apprange_white.png" />
    <None Include="Resources\summary_error.png" />
    <None Include="Resources\weibo_icon.gif" />
    <None Include="Resources\weibo_v3.jpg" />
    <None Include="Resources\summary_weixinemoticon_v3.png" />
    <None Include="Resources\summary_weixinemoticon.png" />
    <None Include="Resources\webclip_note.png" />
    <None Include="Resources\tvw_emoticonstore.png" />
    <None Include="Resources\tvw_emoticon.png" />
    <None Include="Resources\summary_zj_v3.png" />
    <None Include="Resources\summary_zj.png" />
    <None Include="Resources\summary_tui_v3.png" />
    <None Include="Resources\summary_icon_jailbreak.png" />
    <None Include="Resources\webclip_tuimc.jpg" />
    <None Include="Resources\summary_unselect.png" />
    <None Include="Resources\summary_select.png" />
    <None Include="Resources\summary_defaulticon.png" />
    <None Include="Resources\summary_apprange.png" />
    <None Include="Resources\usb_debug_jellybean7.png" />
    <None Include="Resources\usb_debug_jellybean6.png" />
    <None Include="Resources\usb_debug_jellybean5.png" />
    <None Include="Resources\usb_debug_jellybean4.png" />
    <None Include="Resources\usb_debug_jellybean3.png" />
    <None Include="Resources\usb_debug_jellybean2.png" />
    <None Include="Resources\usb_debug_jellybean1.png" />
    <None Include="Resources\usb_debug_icecream6.png" />
    <None Include="Resources\usb_debug_icecream5.png" />
    <None Include="Resources\usb_debug_icecream4.png" />
    <None Include="Resources\usb_debug_icecream3.png" />
    <None Include="Resources\usb_debug_icecream2.png" />
    <None Include="Resources\usb_debug_icecream1.png" />
    <None Include="Resources\usb_debug_gingerbread4.png" />
    <None Include="Resources\usb_debug_gingerbread3.png" />
    <None Include="Resources\usb_debug_gingerbread2.png" />
    <None Include="Resources\usb_debug_gingerbread1.png" />
    <None Include="Resources\weixin_zip_exporthtml.zip" />
    <None Include="Resources\welcome_phone_empty.png" />
    <None Include="Resources\tvw_musicvideo_2.png" />
    <None Include="Resources\tvw_itunesu_2.png" />
    <None Include="Resources\tvw_audiobook_2.png" />
    <None Include="Resources\tvw_voicememo_2.png" />
    <None Include="Resources\tvw_tvshow_2.png" />
    <None Include="Resources\tvw_podcast_2.png" />
    <None Include="Resources\welcome_daemon_read_data_auth.png" />
    <None Include="Resources\welcome_android_driver.png" />
    <None Include="Resources\welcome_install_daemon_auth.png" />
    <None Include="Resources\welcome_android_connect_fail.png" />
    <None Include="Resources\usb_debug_combo_bg.png" />
    <None Include="Resources\usb_debug_version.png" />
    <None Include="Resources\usb_debug_android32.png" />
    <None Include="Resources\usb_debug_android42.png" />
    <None Include="Resources\usb_debug_android40.png" />
    <None Include="Resources\usb_safe.png" />
    <None Include="Resources\usb_Debug.png" />
    <None Include="Resources\usb_windows_safe.png" />
    <None Include="Resources\usb_debug_next.png" />
    <None Include="Resources\usb_debug_pre.png" />
    <None Include="Resources\welcome_dev_coming.gif" />
    <None Include="Resources\welcome_phone_frame.png" />
    <None Include="Resources\welcome_connect_phone.png" />
    <None Include="Resources\welcome_change_usb_line.png" />
    <None Include="Resources\welcome_change_usb_port.png" />
    <None Include="Resources\welcome_enable_usb_debug.png" />
    <None Include="Resources\welcome_connect_helper.png" />
    <None Include="Resources\welcome_connect_helper_step1.png" />
    <None Include="Resources\welcome_connect_helper_step2.png" />
    <None Include="Resources\welcome_connect_helper_step3.png" />
    <None Include="Resources\tvw_notify_2.png" />
    <None Include="Resources\tvw_alarm_2.png" />
    <None Include="Resources\summary_udisk.png" />
    <None Include="Resources\tvw_repair_2.png" />
    <None Include="Resources\summary_apprepair.png" />
    <None Include="Resources\summary_repair.png" />
    <None Include="Resources\webclip_step1.jpg" />
    <None Include="Resources\webclip_step2.jpg" />
    <None Include="Resources\summary_contact_1.png" />
    <None Include="Resources\summary_carrier.png" />
    <None Include="Resources\summary_sns.png" />
    <None Include="Resources\summary_icon.png" />
    <None Include="Resources\tvw_plug_2.png" />
    <None Include="Resources\tvw_tutorials_2.png" />
    <None Include="Resources\tvw_firmware_2.png" />
    <None Include="Resources\top_jailbreak.png" />
    <None Include="Resources\top_jailbreak_light.png" />
    <None Include="Resources\weixin_dll_aud" />
    <None Include="Resources\weixin_welcome.png" />
    <None Include="Resources\weixin_icon_qqsync.png" />
    <None Include="Resources\weixin_icon_newsapp.png" />
    <None Include="Resources\weixin_icon_blogapp.png" />
    <None Include="Resources\weixin_icon_qqmail.png" />
    <None Include="Resources\weixin_icon_medianote.png" />
    <None Include="Resources\weixin_icon_masssendapp.png" />
    <None Include="Resources\tvw_callunaccept_2.png" />
    <None Include="Resources\tvw_callin_2.png" />
    <None Include="Resources\tvw_callall_2.png" />
    <None Include="Resources\tvw_callout_2.png" />
    <None Include="Resources\weixin_icon_default.png" />
    <None Include="Resources\weibo_facebook32.png" />
    <None Include="Resources\weibo_tecent32.png" />
    <None Include="Resources\weibo_sina32.png" />
    <None Include="Resources\weibo_twitter32.png" />
    <None Include="Resources\tools_bg.png" />
    <None Include="Resources\tools_recover.png" />
    <None Include="Resources\tools_unlocked.png" />
    <None Include="Resources\weibo_facebook.png" />
    <None Include="Resources\weibo_tecent.png" />
    <None Include="Resources\weibo_sina.png" />
    <None Include="Resources\weibo_twitter.png" />
    <None Include="Resources\tvw_tongbuappjail_2.png" />
    <None Include="Resources\tool_clean_sync.png" />
    <None Include="Resources\verison_split.png" />
    <None Include="Resources\web_bg_welcome1" />
    <None Include="Resources\web_bg_welcome2" />
    <None Include="Resources\web_btn_clear" />
    <None Include="Resources\weibo-sina_.png" />
    <None Include="Resources\summary_tuiinstall_jailbreak.png" />
    <None Include="Resources\summary_nav_split.png" />
    <None Include="Resources\summary_nav_bg.png" />
    <None Include="Resources\weibo-sina_03.png" />
    <None Include="Resources\tool_clean_rubbish_icon.png" />
    <None Include="Resources\tool_clean_rowheader.png" />
    <None Include="Resources\tool_clean_detail_dropdown.png" />
    <None Include="Resources\tool_clean_detail.png" />
    <None Include="Resources\tool_clean_off-line.png" />
    <None Include="Resources\tool_clean_media.png" />
    <None Include="Resources\tool_clean_junk-file.png" />
    <None Include="Resources\tool_clean_cookies.png" />
    <None Include="Resources\summary_jbhelper.png" />
    <None Include="Resources\tool_tip_success.png" />
    <None Include="Resources\tool_tip_fail.png" />
    <None Include="Resources\tool_label_message_success.png" />
    <None Include="Resources\tool_label_message_fail.png" />
    <None Include="Resources\tool_label_demo.png" />
    <None Include="Resources\tool_tip.png" />
    <None Include="Resources\summary_tuiinstall.png" />
    <None Include="Resources\tongbu_bg_48.png" />
    <None Include="Resources\iTong_check2.ico" />
    <None Include="Resources\iTong_check3.ico" />
    <None Include="Resources\iTong_check4.ico" />
    <None Include="Resources\iTong_check1.ico" />
    <None Include="Resources\tool_label_message.png" />
    <None Include="Resources\tool_carrier_arraw.png" />
    <None Include="Resources\btn_4_gary.png" />
    <None Include="Resources\tool_label_descrypt.png" />
    <None Include="Resources\tongbu_upgrade_bg.png" />
    <None Include="Resources\tongbu_bg_64.png" />
    <None Include="Resources\tongbu_bg_32.png" />
    <None Include="Resources\tongbu_bg_256.png" />
    <None Include="Resources\tongbu_bg_128.png" />
    <None Include="Resources\tongbu_256x256.png" />
    <None Include="Resources\tool_fso_background.png" />
    <None Include="Resources\tool_ts_background.png" />
    <None Include="Resources\tool_carrier_step2.png" />
    <None Include="Resources\tool_carrier_step3.png" />
    <None Include="Resources\tool_carrier_step1.png" />
    <None Include="Resources\tool_carrier_version.PNG" />
    <None Include="Resources\tool_2G_signal_light.png" />
    <None Include="Resources\tool_3G_signal_light.png" />
    <None Include="Resources\tool_notify_background.png" />
    <None Include="Resources\tool_2G_signal.png" />
    <None Include="Resources\tool_3G_signal.png" />
    <None Include="Resources\more_motify_carrier_light.png" />
    <None Include="Resources\more_motify_carrier.png" />
    <None Include="Resources\pic_carrier_demo.PNG" />
    <None Include="Resources\ipcc_iPad_CMCC_Default.zip" />
    <None Include="Resources\ipcc_iPad_CMCC_Payload.zip" />
    <None Include="Resources\ipcc_iPad_Unicom_Default.zip" />
    <None Include="Resources\ipcc_iPad_Unicom_Payload.zip" />
    <None Include="Resources\ipcc_iPhone_CMCC_Default.zip" />
    <None Include="Resources\ipcc_iPhone_CMCC_Payload.zip" />
    <None Include="Resources\ipcc_iPhone_Unicom_Default.zip" />
    <None Include="Resources\ipcc_iPhone_Unicom_Payload.zip" />
    <None Include="Resources\top_ringtone.png" />
    <None Include="Resources\app_btn_2_more.png" />
    <None Include="Resources\more_repair_ios_light.png" />
    <None Include="Resources\more_repair_ios.png" />
    <None Include="Resources\more_clear.png" />
    <None Include="Resources\more_clear_light.png" />
    <None Include="Resources\more_repair.png" />
    <None Include="Resources\more_repair_light.png" />
    <None Include="Resources\icon_ok.png" />
    <None Include="Resources\icon_question.png" />
    <None Include="Resources\icon_warning.png" />
    <None Include="Resources\icon_error.png" />
    <None Include="Resources\icon_sinaweibo.png" />
    <None Include="Resources\icon_qqweibo.png" />
    <None Include="Resources\pnl_bg_about.png" />
    <None Include="Resources\logo_13.png" />
    <None Include="Resources\pnl_bg_about_bottom.png" />
    <None Include="Resources\more_tui.png" />
    <None Include="Resources\more_home.png" />
    <None Include="Resources\more_joinme.png" />
    <None Include="Resources\more_about.png" />
    <None Include="Resources\more_web.png" />
    <None Include="Resources\gif_loading_14.gif" />
    <None Include="Resources\dgv_update.png" />
    <None Include="Resources\gif_loading_summary_16.gif" />
    <None Include="Resources\file_fileempty.png" />
    <None Include="Resources\top_file_light.png" />
    <None Include="Resources\top_file.png" />
    <None Include="Resources\app_btn_4_open.png" />
    <None Include="Resources\frm_bg_Tip_down.png" />
    <None Include="Resources\frm_bg_Tip.png" />
    <None Include="Resources\dgv_uncraked.png" />
    <None Include="Resources\dgv_craked.png" />
    <None Include="Resources\dgv_uncrakedwepay.png" />
    <None Include="Resources\tvw_jvjing_2.png" />
    <None Include="Resources\tvw_tongbuapp_2.png" />
    <None Include="Resources\tvw_musicsearch_2.png" />
    <None Include="Resources\tvw_wallpaper_2.png" />
    <None Include="Resources\btn_pause_4.png" />
    <None Include="Resources\summary_tui.png" />
    <None Include="Resources\btn_ringmaker_4.png" />
    <None Include="Resources\btn_feedback_3.png" />
    <None Include="Resources\btn_ignore_4.png" />
    <None Include="Resources\btn_sort_4.png" />
    <None Include="Resources\btn_repair_4.png" />
    <None Include="Resources\btn_revert_4.png" />
    <None Include="Resources\btn_selectall_4.png" />
    <None Include="Resources\btn_addnote_4.png" />
    <None Include="Resources\tvw_ringtone_2.png" />
    <None Include="Resources\btn_addfolder_4.png" />
    <None Include="Resources\btn_backup_4.png" />
    <None Include="Resources\btn_selectvideo_4.png" />
    <None Include="Resources\btn_save_4.png" />
    <None Include="Resources\btn_play_4.png" />
    <None Include="Resources\btn_addcontactgroup_4.png" />
    <None Include="Resources\btn_addcontact_4.png" />
    <None Include="Resources\btn_stop_4.png" />
    <None Include="Resources\btn_add_4.png" />
    <None Include="Resources\btn_delete_4.png" />
    <None Include="Resources\btn_export_4.png" />
    <None Include="Resources\btn_import_4.png" />
    <None Include="Resources\btn_install_4.png" />
    <None Include="Resources\tvw_filesharing_2.png" />
    <None Include="Resources\tvw_iphone_2.png" />
    <None Include="Resources\tvw_photolib_2.png" />
    <None Include="Resources\tvw_myfav_2.png" />
    <None Include="Resources\tvw_camera_2.png" />
    <None Include="Resources\tvw_apps_2.png" />
    <None Include="Resources\tvw_update_2.png" />
    <None Include="Resources\tvw_video_2.png" />
    <None Include="Resources\tvw_ipad_2.png" />
    <None Include="Resources\tvw_playlist_2.png" />
    <None Include="Resources\nav_top_bg.png" />
    <None Include="Resources\tvw_music_2.png" />
    <None Include="Resources\welcome_description_bg.png" />
    <None Include="Resources\app_ascend_arrow.png" />
    <None Include="Resources\app_drop_bg_h.png" />
    <None Include="Resources\btn_turn_left.png" />
    <None Include="Resources\btn_Setup.png" />
    <None Include="Resources\btn_refresh.png" />
    <None Include="Resources\btn_turn_right.png" />
    <None Include="Resources\btn_view.png" />
    <None Include="Resources\more_ringmaker.png" />
    <None Include="Resources\download_icon_ringtone.png" />
    <None Include="Resources\download_icon_wallpaper.png" />
    <None Include="Resources\download_icon_music.png" />
    <None Include="Resources\media_pic_importringtone.png" />
    <None Include="Resources\photo_pic_importwallpaper.png" />
    <None Include="Resources\media_pic_installibook.png" />
    <None Include="Resources\btn_hide.png" />
    <None Include="Resources\icon_icloud.png" />
    <None Include="Resources\notify_bg.png" />
    <None Include="Resources\icon_bulb_off.png" />
    <None Include="Resources\icon_arrow2.png" />
    <None Include="Resources\top_more_light.png" />
    <None Include="Resources\top_more.png" />
    <None Include="Resources\top_site.png" />
    <None Include="Resources\authority_user.png" />
    <None Include="Resources\authority_icon.png" />
    <None Include="Resources\wallpaper_selected_icon.png" />
    <None Include="Resources\wallpaper_bg.png" />
    <None Include="Resources\wallpaper_default_ipad.png" />
    <None Include="Resources\wallpaper_default_iphone.png" />
    <None Include="Resources\more_iconmanager_light.png" />
    <None Include="Resources\more_iconmanager.png" />
    <None Include="Resources\more_videoconvert.png" />
    <None Include="Resources\more_musicvideo_light.png" />
    <None Include="Resources\more_tvshow_light.png" />
    <None Include="Resources\more_musicvideo.png" />
    <None Include="Resources\more_tvshow.png" />
    <None Include="Resources\more_contacts.png" />
    <None Include="Resources\more_contacts_light.png" />
    <None Include="Resources\more_audiobook_light.png" />
    <None Include="Resources\more_itunesu_light.png" />
    <None Include="Resources\more_voicememo_light.png" />
    <None Include="Resources\more_note_light.png" />
    <None Include="Resources\more_video_light.png" />
    <None Include="Resources\more_ring_light.png" />
    <None Include="Resources\more_music_light.png" />
    <None Include="Resources\more_podcast_light.png" />
    <None Include="Resources\more_udisk_light.png" />
    <None Include="Resources\more_file_light.png" />
    <None Include="Resources\more_document_light.png" />
    <None Include="Resources\media_ibook_24.png" />
    <None Include="Resources\more_itunesu.png" />
    <None Include="Resources\more_podcast.png" />
    <None Include="Resources\more_note.png" />
    <None Include="Resources\contact_default.png" />
    <None Include="Resources\more_voicememo.png" />
    <None Include="Resources\more_audiobook.png" />
    <None Include="Resources\more_document.png" />
    <None Include="Resources\more_udisk.png" />
    <None Include="Resources\more_ring.png" />
    <None Include="Resources\more_file.png" />
    <None Include="Resources\summary_books.png" />
    <None Include="Resources\summary_photo.png" />
    <None Include="Resources\summary_contact.png" />
    <None Include="Resources\split.png" />
    <None Include="Resources\close_app.png" />
    <None Include="Resources\icon_step2.png" />
    <None Include="Resources\icon_step1.png" />
    <None Include="Resources\icon_home.png" />
    <None Include="Resources\app_info.html" />
    <None Include="Resources\icon_clear.png" />
    <None Include="Resources\icon_select.png" />
    <None Include="Resources\icon_news.png" />
    <None Include="Resources\icon_folder.png" />
    <None Include="Resources\icon_folder_drop.png" />
    <None Include="Resources\icon_del.png" />
    <None Include="Resources\icon_app_blank.png" />
    <None Include="Resources\icon_cover_iphone.png" />
    <None Include="Resources\icon_iphone_bottom.png" />
    <None Include="Resources\icon_cover_ipad_V.png" />
    <None Include="Resources\icon_cover_ipad_H.png" />
    <None Include="Resources\icon_ipad_bottom_V.png" />
    <None Include="Resources\icon_ipad_bottom_H.png" />
    <None Include="Resources\icon_shadow_iphone.png" />
    <None Include="Resources\icon_shadow_ipad.png" />
    <None Include="Resources\icon_txt_bg.png" />
    <None Include="Resources\icon_triangle_top.png" />
    <None Include="Resources\icon_triangle_bottom.png" />
    <None Include="Resources\icon_Screen_cover.png" />
    <None Include="Resources\icon_folder_cover.png" />
    <None Include="Resources\icon_clear_d.png" />
    <None Include="Resources\icon_video_100.png" />
    <None Include="Resources\media_ibook_75.png" />
    <None Include="Resources\pnl_loading_small.gif" />
    <None Include="Resources\splash_bg.png" />
    <None Include="Resources\app_icon_noapp.png" />
    <None Include="Resources\btn_checked.png" />
    <None Include="Resources\btn_unchecked.png" />
    <None Include="Resources\gif_loading_16.gif" />
    <None Include="Resources\top_document_light.png" />
    <None Include="Resources\top_udisk_light.png" />
    <None Include="Resources\top_udisk.png" />
    <None Include="Resources\dgv_33_loading.png" />
    <None Include="Resources\dgv_waiting.png" />
    <None Include="Resources\file_icon_no_app.png" />
    <None Include="Resources\welcome_htm_error.html" />
    <None Include="Resources\welcome_gif_error.gif" />
    <None Include="Resources\btn_setting.png" />
    <None Include="app.config" />
    <None Include="Resources\media_video_24.png" />
    <None Include="Resources\dgv_4_edit.png" />
    <None Include="Resources\download_stop.png" />
    <None Include="Resources\download_downloading.png" />
    <None Include="Resources\summary_video.png" />
    <None Include="Resources\summary_app.png" />
    <None Include="Resources\summary_music.png" />
    <None Include="Resources\app_cell_fail.png" />
    <None Include="Resources\app_icon_57_white.png" />
    <None Include="Resources\media_default_album.png" />
    <None Include="Resources\dgv_4_download_pause.png" />
    <None Include="Resources\dgv_4_download_start.png" />
    <None Include="Resources\dgv_4_delete.png" />
    <None Include="Resources\frm_bg_sub.png" />
    <None Include="Resources\btn_4_prev.png" />
    <None Include="Resources\btn_4_next.png" />
    <None Include="Resources\dgv_filenoexist.png" />
    <None Include="Resources\btn_2_tab.png" />
    <None Include="Resources\pnl_bg_border.png" />
    <None Include="Resources\dgv_4_stop.png" />
    <None Include="Resources\player_voice.png" />
    <None Include="Resources\player_time_bg.png" />
    <None Include="Resources\player_voice_bg.png" />
    <None Include="Resources\dgv_4_play.png" />
    <None Include="Resources\dgv_playing.png" />
    <None Include="Resources\player_bg.png" />
    <None Include="Resources\app_btn_4_ignore.png" />
    <None Include="Resources\app_btn_4_showmore.png" />
    <None Include="Resources\btn_filter.png" />
    <None Include="Resources\player_play.png" />
    <None Include="Resources\player_pause.png" />
    <None Include="Resources\player_next.png" />
    <None Include="Resources\player_prev.png" />
    <None Include="Resources\player_loop.png" />
    <None Include="Resources\player_single.png" />
    <None Include="Resources\player_random.png" />
    <None Include="Resources\player_order.png" />
    <None Include="Resources\app_icon_select.png" />
    <None Include="Resources\app_icon_gray.png" />
    <None Include="Resources\app_icon_white.png" />
    <None Include="Resources\file_filesearch_30.png" />
    <None Include="Resources\pnl_loading.gif" />
    <None Include="Resources\welcome_frm_bg_new.png" />
    <None Include="Resources\welcome_new_wifi.png" />
    <None Include="Resources\welcome_new_usb.png" />
    <None Include="Resources\screenshot_btn_4_refresh.png" />
    <None Include="Resources\welcome_icon_wifi.png" />
    <None Include="Resources\welcome_icon_usb_h.png" />
    <None Include="Resources\welcome_icon_usb.png" />
    <None Include="Resources\welcome_icon_iphone_h.png" />
    <None Include="Resources\welcome_icon_iphone.png" />
    <None Include="Resources\welcome_icon_ipad_h.png" />
    <None Include="Resources\welcome_icon_ipad.png" />
    <None Include="Resources\welcome_icon_wifi_h.png" />
    <None Include="Resources\top_video_light.png" />
    <None Include="Resources\top_photo.png" />
    <None Include="Resources\top_photo_light.png" />
    <None Include="Resources\top_music.png" />
    <None Include="Resources\top_ebook.png" />
    <None Include="Resources\top_ebook_light.png" />
    <None Include="Resources\top_document.png" />
    <None Include="Resources\top_app.png" />
    <None Include="Resources\top_video.png" />
    <None Include="Resources\dgv_info.png" />
    <None Include="Resources\file_btn_myfavorite.png" />
    <None Include="Resources\pnl_bg_info.png" />
    <None Include="Resources\lvw_item_LargeIcon_selected.png" />
    <None Include="Resources\btn_4_fullScreen.png" />
    <None Include="Resources\btn_4_sharing.png" />
    <None Include="Resources\btn_4_setting.png" />
    <None Include="Resources\btn_4_screenshot.png" />
    <None Include="Resources\app_pic_importFiles.png" />
    <None Include="Resources\app_pic_imporFolder.png" />
    <None Include="Resources\btn_3_openfolder.png" />
    <None Include="Resources\app_pic_importitunes.png" />
    <None Include="Resources\app_descend_arrow.png" />
    <None Include="Resources\app_drop_bg.png" />
    <None Include="Resources\welcome_frm_bg.png" />
    <None Include="Resources\welcome_btn_bg_h.png" />
    <None Include="Resources\summary_btn_3_disconnect.png" />
    <None Include="Resources\icon_battery_wifi.png" />
    <None Include="Resources\file_bg_applistbottom.png" />
    <None Include="Resources\icon_battery.png" />
    <None Include="Resources\icon_wifi_24.png" />
    <None Include="Resources\icon_usb_24.png" />
    <None Include="Resources\icon_error_14.png" />
    <None Include="Resources\gif_loading_32.gif" />
    <None Include="Resources\app_product_all.png" />
    <None Include="Resources\app_product_iPhone.png" />
    <None Include="Resources\app_product_iPad.png" />
    <None Include="Resources\file_oplayer_113.png" />
    <None Include="Resources\file_oplayer_47.png" />
    <None Include="Resources\file_flexplayer_113.png" />
    <None Include="Resources\file_flexplayer_47.png" />
    <None Include="Resources\file_fileapp_113.png" />
    <None Include="Resources\file_fileapp_47.png" />
    <None Include="Resources\file_qqvideo_113.png" />
    <None Include="Resources\file_qqvideo_47.png" />
    <None Include="Resources\summary_btn_3_info.png" />
    <None Include="Resources\file_icon_ipad.png" />
    <None Include="Resources\file_icon_iphone.png" />
    <None Include="Resources\file_icon_searchnext.png" />
    <None Include="Resources\file_icon_searchprevious.png" />
    <None Include="Resources\frm_bg_state.png" />
    <None Include="Resources\frm_bg_blank.png" />
    <None Include="Resources\pnl_bg_state.png" />
    <None Include="Resources\file_btn_2_myfavorite.png" />
    <None Include="Resources\file_btn_4_back.png" />
    <None Include="Resources\app_pxl_uninstall.txt" />
    <None Include="Resources\file_bg_filepath.png" />
    <None Include="Resources\pnl_bg_navigation.png" />
    <None Include="Resources\pnl_bg_status.png" />
    <None Include="Resources\btn_3_cancel.png" />
    <None Include="Resources\app_icon_default.png" />
    <None Include="Resources\btn_4_refresh.png" />
    <None Include="Resources\btn_viewlist_unchecked.png" />
    <None Include="Resources\btn_viewlist_checked.png" />
    <None Include="Resources\btn_viewlargeicon_unchecked.png" />
    <None Include="Resources\btn_viewlargeicon_checked.png" />
    <None Include="Resources\welcome_btn_split.png" />
    <None Include="Resources\welcome_icon_link.png" />
    <None Include="Resources\app_progress_value.png" />
    <None Include="Resources\app_progress_background.png" />
    <None Include="Resources\btn_4_uninstall.png" />
    <None Include="Resources\btn_4_backup.png" />
    <None Include="Resources\file_filesystem_16.png" />
    <None Include="Resources\file_filesearch_24.png" />
    <None Include="Resources\file_tbtui_24.png" />
    <None Include="Resources\file_folder_24.png" />
    <None Include="Resources\file_excel_24.png" />
    <None Include="Resources\file_excel_16.png" />
    <None Include="Resources\file_word_75.png" />
    <None Include="Resources\file_word_24.png" />
    <None Include="Resources\file_word_16.png" />
    <None Include="Resources\file_video_75.png" />
    <None Include="Resources\file_video_24.png" />
    <None Include="Resources\file_video_16.png" />
    <None Include="Resources\file_txt_75.png" />
    <None Include="Resources\file_txt_24.png" />
    <None Include="Resources\file_txt_16.png" />
    <None Include="Resources\file_ppt_75.png" />
    <None Include="Resources\file_ppt_24.png" />
    <None Include="Resources\file_ppt_16.png" />
    <None Include="Resources\file_pdf_75.png" />
    <None Include="Resources\file_pdf_24.png" />
    <None Include="Resources\file_pdf_16.png" />
    <None Include="Resources\file_excel_75.png" />
    <None Include="Resources\more_btn_3_bg.png" />
    <None Include="Resources\file_tbtui_small.png" />
    <None Include="Resources\file_tbtui_large.png" />
    <None Include="Resources\file_unfind_large.png" />
    <None Include="Resources\file_unfind_small.png" />
    <None Include="Resources\file_myfavorite_largel.png" />
    <None Include="Resources\file_myfavorite_small.png" />
    <None Include="Resources\file_pic_large.png" />
    <None Include="Resources\file_pic_small.png" />
    <None Include="Resources\file_plist_large.png" />
    <None Include="Resources\file_plist_small.png" />
    <None Include="Resources\file_fileshortcut_large.png" />
    <None Include="Resources\file_fileshortcut_small.png" />
    <None Include="Resources\file_filesearch_large.png" />
    <None Include="Resources\file_filesearch_small.png" />
    <None Include="Resources\file_installous_large.png" />
    <None Include="Resources\file_installous_small.png" />
    <None Include="Resources\file_app_large.png" />
    <None Include="Resources\file_app_small.png" />
    <None Include="Resources\file_documents_large.png" />
    <None Include="Resources\file_documents_small.png" />
    <None Include="Resources\file_folder_large.png" />
    <None Include="Resources\file_folder_small.png" />
    <None Include="Resources\file_cydia_large.png" />
    <None Include="Resources\file_cydia_small.png" />
    <None Include="Resources\file_file_small.png" />
    <None Include="Resources\file_file_large.png" />
    <None Include="Resources\file_foldershortcut_small.png" />
    <None Include="Resources\file_foldershortcut_large.png" />
    <None Include="Resources\file_ipa_large.png" />
    <None Include="Resources\btn_file_arrow.png" />
    <None Include="Resources\btn_file_copy.png" />
    <None Include="zzReplace\ImageResources\reference.ini" />
    <Compile Include="ApplicationEvents.vb" />
    <Compile Include="frmPopMsg.Designer.vb">
      <DependentUpon>frmPopMsg.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPopMsg.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidApp\AndroidAppMgr.vb" />
    <Compile Include="zAndroidApp\AndroidDeviceHelper.vb" />
    <Compile Include="zAndroidApp\AndroidPackageReader.vb" />
    <Compile Include="zAndroidApp\frmAndroidAppListBase.Designer.vb">
      <DependentUpon>frmAndroidAppListBase.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidAppListBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidDeviceApp.Designer.vb">
      <DependentUpon>frmAndroidDeviceApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidDeviceApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidLocalApp.Designer.vb">
      <DependentUpon>frmAndroidLocalApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidLocalApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidUpdate.Designer.vb">
      <DependentUpon>frmAndroidUpdate.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidUpdate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidUpdateIgnore.Designer.vb">
      <DependentUpon>frmAndroidUpdateIgnore.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidApp\frmAndroidUpdateIgnore.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidContact\frmAndroidContact.Designer.vb">
      <DependentUpon>frmAndroidContact.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidContact\frmAndroidContact.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidFile\frmAndroidFile.designer.vb">
      <DependentUpon>frmAndroidFile.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidFile\frmAndroidFile.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidFile\frmAndroidFileBase.Designer.vb">
      <DependentUpon>frmAndroidFileBase.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidFile\frmAndroidFileBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidFile\frmAndroidPlistEdit.Designer.vb">
      <DependentUpon>frmAndroidPlistEdit.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidFile\frmAndroidPlistEdit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusic.Designer.vb">
      <DependentUpon>frmAndroidMusic.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusic.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusicEdit.Designer.vb">
      <DependentUpon>frmAndroidMusicEdit.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusicEdit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusicInfo.Designer.vb">
      <DependentUpon>frmAndroidMusicInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusicInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusicLocal.Designer.vb">
      <DependentUpon>frmAndroidMusicLocal.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidMusicLocal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidRingtoneLocal.Designer.vb">
      <DependentUpon>frmAndroidRingtoneLocal.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidRingtoneLocal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMedia\frmAndroidVideo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidDriver.Designer.vb">
      <DependentUpon>frmAndroidDriver.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidDriver.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidFeedback.Designer.vb">
      <DependentUpon>frmAndroidFeedback.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidFeedback.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidNoConnecting.Designer.vb">
      <DependentUpon>frmAndroidNoConnecting.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidNoConnecting.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidUninstallMsg.Designer.vb">
      <DependentUpon>frmAndroidUninstallMsg.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidUninstallMsg.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidUSBDebugBrowser.Designer.vb">
      <DependentUpon>frmAndroidUSBDebugBrowser.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidUSBDebugBrowser.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidUSBDebugSimple.Designer.vb">
      <DependentUpon>frmAndroidUSBDebugSimple.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidMsgForm\frmAndroidUSBDebugSimple.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidPhoto\frmAndroidWallpaperLocal.designer.vb">
      <DependentUpon>frmAndroidWallpaperLocal.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidPhoto\frmAndroidWallpaperLocal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidPhoto\frmAndroidPhoto.designer.vb">
      <DependentUpon>frmAndroidPhoto.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidPhoto\frmAndroidPhoto.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\ctrlChatSend.Designer.vb">
      <DependentUpon>ctrlChatSend.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\ctrlChatSend.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\CtrlFaceItem.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\CtrlSelectContactItem.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\ctrlSender.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\EasyFlowLayoutPanel.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMS.Designer.vb">
      <DependentUpon>frmAndroidSMS.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSChatGroup.Designer.vb">
      <DependentUpon>frmAndroidSMSChatGroup.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSChatGroup.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSChatSingle.Designer.vb">
      <DependentUpon>frmAndroidSMSChatSingle.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSChatSingle.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSDelete.Designer.vb">
      <DependentUpon>frmAndroidSMSDelete.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSDelete.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSExport.Designer.vb">
      <DependentUpon>frmAndroidSMSExport.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSExport.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSFaces.Designer.vb">
      <DependentUpon>frmAndroidSMSFaces.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSFaces.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSFail.Designer.vb">
      <DependentUpon>frmAndroidSMSFail.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSFail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSNotify.Designer.vb">
      <DependentUpon>frmAndroidSMSNotify.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSNotify.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSSelectContact.Designer.vb">
      <DependentUpon>frmAndroidSMSSelectContact.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSSelectContact.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSSend.Designer.vb">
      <DependentUpon>frmAndroidSMSSend.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidSMS\frmAndroidSMSSend.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\SmsExportHelper.vb" />
    <Compile Include="zAndroidSMS\TableSelectContact.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zAndroidSMS\tbRichTextBoxMenu.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidDetail.Designer.vb">
      <DependentUpon>frmAndroidDetail.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidDetail.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidNoDevice.Designer.vb">
      <DependentUpon>frmAndroidNoDevice.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidNoDevice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidPreview.Designer.vb">
      <DependentUpon>frmAndroidPreview.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidPreview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidSummary.Designer.vb">
      <DependentUpon>frmAndroidSummary.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidSummary.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidWelcome.Designer.vb">
      <DependentUpon>frmAndroidWelcome.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroidWelcome\frmAndroidWelcome.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAndroid\AndroidImportHelper.vb" />
    <Compile Include="zAndroid\frmAndroidBase.Designer.vb">
      <DependentUpon>frmAndroidBase.vb</DependentUpon>
    </Compile>
    <Compile Include="zAndroid\frmAndroidBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zClass\AgentHelper.vb" />
    <Compile Include="zClass\ContactHelper.vb">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="zClass\ContactMerge.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zClass\CommonInfo.vb" />
    <Compile Include="zClass\ModifyShortcut.vb" />
    <Compile Include="zComponents\tbBattery.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zComponents\tbBatteryProgress.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zComponents\tbCheckButton.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zComponents\tbCheckButtonEx.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDataGridView\tbDataGridViewMusicStateCell.vb" />
    <Compile Include="zDataGridView\tbDataGridViewShowMoreRow.vb" />
    <Compile Include="zDataMove\DataMoveHelper.vb" />
    <Compile Include="zDeviceApp\frmAppFilter.Designer.vb">
      <DependentUpon>frmAppFilter.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmAppFilter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmCommonApp.Designer.vb">
      <DependentUpon>frmCommonApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmCommonApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\frmiTunesLoginV3.Designer.vb">
      <DependentUpon>frmiTunesLoginV3.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceApp\frmiTunesLoginV3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceApp\IApp.vb" />
    <Compile Include="zDeviceInfoMgr\frmBookmark.Designer.vb">
      <DependentUpon>frmBookmark.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmBookmark.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmBookmarkEdit.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmContactRepeat.Designer.vb">
      <DependentUpon>frmContactRepeat.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceInfoMgr\frmContactRepeat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceInfoMgr\SMSDBHelper.vb" />
    <Compile Include="zDeviceMedia\frmEBook.Designer.vb">
      <DependentUpon>frmEBook.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtoneInfo.Designer.vb">
      <DependentUpon>frmRingtoneInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtoneInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtoneSetup.Designer.vb">
      <DependentUpon>frmRingtoneSetup.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceMedia\frmRingtoneSetup.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmDownloadAction.designer.vb">
      <DependentUpon>frmDownloadAction.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmDownloadAction.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmDownloadCenter.designer.vb">
      <DependentUpon>frmDownloadCenter.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmDownloadCenter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmDownloaded.designer.vb">
      <DependentUpon>frmDownloaded.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmDownloaded.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmDownloading.designer.vb">
      <DependentUpon>frmDownloading.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmDownloading.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmDownloadWallpaper.Designer.vb">
      <DependentUpon>frmDownloadWallpaper.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmDownloadWallpaper.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFlash\FlashHelper.vb" />
    <Compile Include="zFlash\frmCheckiCloud.Designer.vb">
      <DependentUpon>frmCheckiCloud.vb</DependentUpon>
    </Compile>
    <Compile Include="zFlash\frmCheckiCloud.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFlash\frmFirmware.Designer.vb">
      <DependentUpon>frmFirmware.vb</DependentUpon>
    </Compile>
    <Compile Include="zFlash\frmFirmware.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zFlash\frmFlash.Designer.vb">
      <DependentUpon>frmFlash.vb</DependentUpon>
    </Compile>
    <Compile Include="zFlash\frmFlash.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zLogin\frmLogin.Designer.vb">
      <DependentUpon>frmLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="zLogin\frmLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zLogin\frmPersonalCenter.Designer.vb">
      <DependentUpon>frmPersonalCenter.vb</DependentUpon>
    </Compile>
    <Compile Include="zLogin\frmPersonalCenter.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\frmSettingSkin.Designer.vb">
      <DependentUpon>frmSettingSkin.vb</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmSettingSkin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\frmSettingV3.Designer.vb">
      <DependentUpon>frmSettingV3.vb</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmSettingV3.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\JBIniSetting.vb" />
    <Compile Include="zTools\frmBatteryManager.Designer.vb">
      <DependentUpon>frmBatteryManager.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmBatteryManager.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\frmRepair.Designer.vb">
      <DependentUpon>frmRepair.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmRepair.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\frmRepairPayment.Designer.vb">
      <DependentUpon>frmRepairPayment.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmRepairPayment.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\frmSystemLog.Designer.vb">
      <DependentUpon>frmSystemLog.vb</DependentUpon>
    </Compile>
    <Compile Include="zTools\frmSystemLog.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDownload\frmSelectDevice.designer.vb">
      <DependentUpon>frmSelectDevice.vb</DependentUpon>
    </Compile>
    <Compile Include="zDownload\frmSelectDevice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zMsgForm\tbSplashBox.Designer.vb">
      <DependentUpon>tbSplashBox.vb</DependentUpon>
    </Compile>
    <Compile Include="zMsgForm\tbSplashBox.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zRestore\BackupContactDB.vb" />
    <Compile Include="zRestore\BackupNotesDB.vb" />
    <Compile Include="zRestore\BackupSMSDB.vb" />
    <Compile Include="zRestore\BackupCallHistoryDB.vb" />
    <Compile Include="zRestore\frmBackupCallHistory.Designer.vb">
      <DependentUpon>frmBackupCallHistory.vb</DependentUpon>
    </Compile>
    <Compile Include="zRestore\frmBackupCallHistory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zRestore\frmBackupContact.Designer.vb">
      <DependentUpon>frmBackupContact.vb</DependentUpon>
    </Compile>
    <Compile Include="zRestore\frmBackupContact.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zRestore\frmBackupCreate.Designer.vb">
      <DependentUpon>frmBackupCreate.vb</DependentUpon>
    </Compile>
    <Compile Include="zRestore\frmBackupCreate.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zRestore\frmBackupList.Designer.vb">
      <DependentUpon>frmBackupList.vb</DependentUpon>
    </Compile>
    <Compile Include="zRestore\frmBackupList.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zRestore\frmBackupPreview.Designer.vb">
      <DependentUpon>frmBackupPreview.vb</DependentUpon>
    </Compile>
    <Compile Include="zRestore\frmBackupPreview.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zRestore\frmBackupSMS.Designer.vb">
      <DependentUpon>frmBackupSMS.vb</DependentUpon>
    </Compile>
    <Compile Include="zRestore\frmBackupSMS.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zTools\ClearErrorIcon.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zTools\ClearRubbishFileSelect.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zWebsite\frmSite.Designer.vb">
      <DependentUpon>frmSite.vb</DependentUpon>
    </Compile>
    <Compile Include="zWebsite\frmSite.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWebsite\frmSiteAnniversary.Designer.vb">
      <DependentUpon>frmSiteAnniversary.vb</DependentUpon>
    </Compile>
    <Compile Include="zWebsite\frmSiteAnniversary.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeibo\frmVerify.Designer.vb">
      <DependentUpon>frmVerify.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeibo\frmVerify.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmAndroidWeixin.Designer.vb">
      <DependentUpon>frmAndroidWeixin.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmAndroidWeixin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmWeixin.Designer.vb">
      <DependentUpon>frmWeixin.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmWeixin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWeixin\frmWeixinEmoticonApp.Designer.vb">
      <DependentUpon>frmWeixinEmoticonApp.vb</DependentUpon>
    </Compile>
    <Compile Include="zWeixin\frmWeixinEmoticonApp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmCommonWel.Designer.vb">
      <DependentUpon>frmCommonWel.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmCommonWel.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmHotApps.Designer.vb">
      <DependentUpon>frmHotApps.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmHotApps.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmSummaryRecommend.Designer.vb">
      <DependentUpon>frmSummaryRecommend.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmSummaryRecommend.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zWelcome\frmTuiInstallTutorial.Designer.vb">
      <DependentUpon>frmTuiInstallTutorial.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmTuiInstallTutorial.vb">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android.csproj">
      <Project>{898E7D3C-7ED3-4BD4-A668-5FBFB5C3F3AB}</Project>
      <Name>Android</Name>
    </ProjectReference>
    <ProjectReference Include="..\AppUnion\AppUnion.csproj">
      <Project>{D5156F82-717C-4C8E-A513-5343566FB2EA}</Project>
      <Name>AppUnion</Name>
    </ProjectReference>
    <ProjectReference Include="..\Components\Components.vbproj">
      <Project>{28459798-5E28-45D3-AAE3-74A9F1761D1A}</Project>
      <Name>Components</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685AE51B-3C37-4B37-B3A2-B485D07A6E6B}</Project>
      <Name>CoreMisc</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\CoreModuleEx\CoreModuleCS.csproj">
      <Project>{608e58d6-914f-45d2-a3a6-55cec62ef261}</Project>
      <Name>CoreModuleCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModule\CoreModule.vbproj">
      <Project>{EAB1560F-6CF1-4B7B-A611-09FD2FCB23D5}</Project>
      <Name>CoreModule</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65CE9103-521A-49E6-A8CD-89137B452842}</Project>
      <Name>CoreReses</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag.csproj">
      <Project>{B02CE9CF-6163-411C-870E-1508DDD16CFC}</Project>
      <Name>CoreTag</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\CoreUpdate\CoreUpdate.csproj">
      <Project>{998A7F44-F7AF-4E25-9EF3-696834EE6242}</Project>
      <Name>CoreUpdate</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil.vbproj">
      <Project>{718B167F-1B33-4B7C-A7E3-E15615DDAAC4}</Project>
      <Name>CoreUtil</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone.csproj">
      <Project>{6691EA4B-1EDB-4330-A3A3-E1D47F4D35BE}</Project>
      <Name>iPhone</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPodDB\iPodDB.csproj">
      <Project>{8B5C5494-3487-4632-AB5F-9193E29623DB}</Project>
      <Name>iPodDB</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf.csproj">
      <Project>{d8ab1668-b7ab-45d6-8c6c-bfc132a37ec9}</Project>
      <Name>ProtoBuf</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <PostBuildEvent>md "$(TargetDir)WeChat
move "$(TargetDir)IncludeDlls\WeChat\*.*" "$(TargetDir)\WeChat"
rd "$(TargetDir)IncludeDlls\WeChat" /Q
md "$(TargetDir)Flash
move "$(TargetDir)IncludeDlls\Flash\*.*" "$(TargetDir)\Flash"
rd "$(TargetDir)IncludeDlls\Flash" /Q
move "$(TargetDir)IncludeDlls\*.*" "$(TargetDir)"
rd "$(TargetDir)IncludeDlls" /Q
clear.bat</PostBuildEvent>
  </PropertyGroup>
</Project>