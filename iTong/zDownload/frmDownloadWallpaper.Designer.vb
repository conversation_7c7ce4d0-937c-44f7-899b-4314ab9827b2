﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDownloadWallPaper
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDownloadWallPaper))
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.pnlDeviceType = New iTong.Components.tbPanel()
        Me.btnIPhone = New iTong.Components.tbRadioButton()
        Me.btnAndroid = New iTong.Components.tbRadioButton()
        Me.btniPad = New iTong.Components.tbRadioButton()
        Me.btnShareTo = New iTong.Components.tbButton()
        Me.btnSelectAll = New iTong.Components.tbButton()
        Me.btnImportTo = New iTong.Components.tbButton()
        Me.pnl_Container = New System.Windows.Forms.Panel()
        Me.pnlDownloadWallpaper = New System.Windows.Forms.Panel()
        Me.picImportRingtone = New System.Windows.Forms.PictureBox()
        Me.TbButton1 = New iTong.Components.tbButton()
        Me.lblWallpaperDescription = New iTong.Components.tbLabel()
        Me.lblWallpaperTitle = New iTong.Components.tbLabel()
        Me.btnGotoSite = New iTong.Components.tbButton()
        Me.lblGetWallpaper = New System.Windows.Forms.Label()
        Me.lblWallpaper = New System.Windows.Forms.Label()
        Me.pnlFileEmpty = New System.Windows.Forms.Panel()
        Me.picFileEmpty = New System.Windows.Forms.PictureBox()
        Me.lblFileEmpty = New iTong.Components.tbLabel()
        Me.menuWallpaper = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiLook = New System.Windows.Forms.ToolStripMenuItem()
        Me.tssLook = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiRefresh = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiOpenExplorer = New System.Windows.Forms.ToolStripMenuItem()
        Me.tssOpenExplorer = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.tssDelete = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiShareToSina = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShareToTecent = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShareToFacebook = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShareToTwitter = New System.Windows.Forms.ToolStripMenuItem()
        Me.tss4 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiImportTo = New System.Windows.Forms.ToolStripMenuItem()
        Me.cmsWeibo = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiSina = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTecent = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiFacebook = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTwitter = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlTop.SuspendLayout()
        Me.pnlDeviceType.SuspendLayout()
        Me.pnl_Container.SuspendLayout()
        Me.pnlDownloadWallpaper.SuspendLayout()
        CType(Me.picImportRingtone, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlFileEmpty.SuspendLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.menuWallpaper.SuspendLayout()
        Me.cmsWeibo.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(760, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(736, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(712, 0)
        '
        'pnlTop
        '
        Me.pnlTop.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.btnRefresh)
        Me.pnlTop.Controls.Add(Me.btnDelete)
        Me.pnlTop.Controls.Add(Me.pnlDeviceType)
        Me.pnlTop.Controls.Add(Me.btnShareTo)
        Me.pnlTop.Controls.Add(Me.btnSelectAll)
        Me.pnlTop.Controls.Add(Me.btnImportTo)
        Me.pnlTop.Location = New System.Drawing.Point(1, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(783, 32)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 23
        Me.pnlTop.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(165, 5)
        Me.btnRefresh.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(63, 23)
        Me.btnRefresh.TabIndex = 31
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = " 刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 2
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(97, 5)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(57, 23)
        Me.btnDelete.TabIndex = 30
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "13,11,13,11"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbTextColor = System.Drawing.Color.Black
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'pnlDeviceType
        '
        Me.pnlDeviceType.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.pnlDeviceType.BackColor = System.Drawing.Color.Transparent
        Me.pnlDeviceType.Controls.Add(Me.btnIPhone)
        Me.pnlDeviceType.Controls.Add(Me.btnAndroid)
        Me.pnlDeviceType.Controls.Add(Me.btniPad)
        Me.pnlDeviceType.Location = New System.Drawing.Point(312, 2)
        Me.pnlDeviceType.Name = "pnlDeviceType"
        Me.pnlDeviceType.Size = New System.Drawing.Size(258, 27)
        Me.pnlDeviceType.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDeviceType.TabIndex = 29
        Me.pnlDeviceType.tbBackgroundImage = Nothing
        Me.pnlDeviceType.tbShowWatermark = False
        Me.pnlDeviceType.tbSplit = "0,0,0,0"
        Me.pnlDeviceType.tbWatermark = Nothing
        Me.pnlDeviceType.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDeviceType.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnIPhone
        '
        Me.btnIPhone.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnIPhone.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnIPhone.Checked = True
        Me.btnIPhone.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnIPhone.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnIPhone.ForeColor_Checked = System.Drawing.Color.White
        Me.btnIPhone.Location = New System.Drawing.Point(65, 3)
        Me.btnIPhone.Name = "btnIPhone"
        Me.btnIPhone.Size = New System.Drawing.Size(65, 23)
        Me.btnIPhone.TabIndex = 27
        Me.btnIPhone.TabStop = True
        Me.btnIPhone.tbAdriftIconWhenHover = False
        Me.btnIPhone.tbAutoSize = False
        Me.btnIPhone.tbAutoSizeEx = False
        Me.btnIPhone.tbBadgeNumber = 0
        Me.btnIPhone.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnIPhone.tbEnableToolTip = False
        Me.btnIPhone.tbIconCheckedMouseDown = Nothing
        Me.btnIPhone.tbIconCheckedMouseHover = Nothing
        Me.btnIPhone.tbIconCheckedMouseLeave = Nothing
        Me.btnIPhone.tbIconHoldPlace = True
        Me.btnIPhone.tbIconImage = Nothing
        Me.btnIPhone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnIPhone.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnIPhone.tbIconMore = False
        Me.btnIPhone.tbIconMoreImage = CType(resources.GetObject("btnIPhone.tbIconMoreImage"), System.Drawing.Image)
        Me.btnIPhone.tbIconPlaceText = 5
        Me.btnIPhone.tbIconReadOnly = Nothing
        Me.btnIPhone.tbIconReadOnlyLight = Nothing
        Me.btnIPhone.tbIconUnCheckedMouseDown = Nothing
        Me.btnIPhone.tbIconUnCheckedMouseHover = Nothing
        Me.btnIPhone.tbIconUnCheckedMouseLeave = Nothing
        Me.btnIPhone.tbImageBackground = Nothing
        Me.btnIPhone.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnIPhone.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnIPhone.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnIPhone.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnIPhone.tbImageUnCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnIPhone.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnIPhone.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnIPhone.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnIPhone.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnIPhone.tbReadOnly = False
        Me.btnIPhone.tbShadow = False
        Me.btnIPhone.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnIPhone.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnIPhone.tbShowBadgeNumber = True
        Me.btnIPhone.tbShowBadgeNumberWhenSelection = True
        Me.btnIPhone.tbShowNew = False
        Me.btnIPhone.tbSplit = "3,3,3,3"
        Me.btnIPhone.tbTag = Nothing
        Me.btnIPhone.tbToolTip = ""
        Me.btnIPhone.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnIPhone.Text = "iPhone"
        Me.btnIPhone.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnIPhone.UseVisualStyleBackColor = True
        '
        'btnAndroid
        '
        Me.btnAndroid.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnAndroid.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnAndroid.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnAndroid.ForeColor_Checked = System.Drawing.Color.White
        Me.btnAndroid.Location = New System.Drawing.Point(191, 3)
        Me.btnAndroid.Name = "btnAndroid"
        Me.btnAndroid.Size = New System.Drawing.Size(59, 23)
        Me.btnAndroid.TabIndex = 26
        Me.btnAndroid.TabStop = True
        Me.btnAndroid.tbAdriftIconWhenHover = False
        Me.btnAndroid.tbAutoSize = False
        Me.btnAndroid.tbAutoSizeEx = False
        Me.btnAndroid.tbBadgeNumber = 0
        Me.btnAndroid.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAndroid.tbEnableToolTip = False
        Me.btnAndroid.tbIconCheckedMouseDown = Nothing
        Me.btnAndroid.tbIconCheckedMouseHover = Nothing
        Me.btnAndroid.tbIconCheckedMouseLeave = Nothing
        Me.btnAndroid.tbIconHoldPlace = True
        Me.btnAndroid.tbIconImage = Nothing
        Me.btnAndroid.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAndroid.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnAndroid.tbIconMore = False
        Me.btnAndroid.tbIconMoreImage = CType(resources.GetObject("btnAndroid.tbIconMoreImage"), System.Drawing.Image)
        Me.btnAndroid.tbIconPlaceText = 5
        Me.btnAndroid.tbIconReadOnly = Nothing
        Me.btnAndroid.tbIconReadOnlyLight = Nothing
        Me.btnAndroid.tbIconUnCheckedMouseDown = Nothing
        Me.btnAndroid.tbIconUnCheckedMouseHover = Nothing
        Me.btnAndroid.tbIconUnCheckedMouseLeave = Nothing
        Me.btnAndroid.tbImageBackground = Nothing
        Me.btnAndroid.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnAndroid.tbImageCheckedMouseDown = Nothing
        Me.btnAndroid.tbImageCheckedMouseHover = Nothing
        Me.btnAndroid.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnAndroid.tbImageUnCheckedMouseDown = Nothing
        Me.btnAndroid.tbImageUnCheckedMouseHover = Nothing
        Me.btnAndroid.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnAndroid.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnAndroid.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnAndroid.tbReadOnly = False
        Me.btnAndroid.tbShadow = False
        Me.btnAndroid.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAndroid.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAndroid.tbShowBadgeNumber = True
        Me.btnAndroid.tbShowBadgeNumberWhenSelection = True
        Me.btnAndroid.tbShowNew = False
        Me.btnAndroid.tbSplit = "3,3,3,3"
        Me.btnAndroid.tbTag = Nothing
        Me.btnAndroid.tbToolTip = ""
        Me.btnAndroid.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnAndroid.Text = "Android"
        Me.btnAndroid.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAndroid.UseVisualStyleBackColor = True
        Me.btnAndroid.Visible = False
        '
        'btniPad
        '
        Me.btniPad.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniPad.Appearance = System.Windows.Forms.Appearance.Button
        Me.btniPad.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btniPad.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btniPad.ForeColor_Checked = System.Drawing.Color.White
        Me.btniPad.Location = New System.Drawing.Point(129, 3)
        Me.btniPad.Name = "btniPad"
        Me.btniPad.Size = New System.Drawing.Size(65, 23)
        Me.btniPad.TabIndex = 28
        Me.btniPad.tbAdriftIconWhenHover = False
        Me.btniPad.tbAutoSize = False
        Me.btniPad.tbAutoSizeEx = False
        Me.btniPad.tbBadgeNumber = 0
        Me.btniPad.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniPad.tbEnableToolTip = False
        Me.btniPad.tbIconCheckedMouseDown = Nothing
        Me.btniPad.tbIconCheckedMouseHover = Nothing
        Me.btniPad.tbIconCheckedMouseLeave = Nothing
        Me.btniPad.tbIconHoldPlace = True
        Me.btniPad.tbIconImage = Nothing
        Me.btniPad.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniPad.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btniPad.tbIconMore = False
        Me.btniPad.tbIconMoreImage = CType(resources.GetObject("btniPad.tbIconMoreImage"), System.Drawing.Image)
        Me.btniPad.tbIconPlaceText = 5
        Me.btniPad.tbIconReadOnly = Nothing
        Me.btniPad.tbIconReadOnlyLight = Nothing
        Me.btniPad.tbIconUnCheckedMouseDown = Nothing
        Me.btniPad.tbIconUnCheckedMouseHover = Nothing
        Me.btniPad.tbIconUnCheckedMouseLeave = Nothing
        Me.btniPad.tbImageBackground = Nothing
        Me.btniPad.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btniPad.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btniPad.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btniPad.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btniPad.tbImageUnCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btniPad.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btniPad.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btniPad.tbNoShowBadgeNumberImage = CType(resources.GetObject("btniPad.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btniPad.tbReadOnly = False
        Me.btniPad.tbShadow = False
        Me.btniPad.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniPad.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniPad.tbShowBadgeNumber = True
        Me.btniPad.tbShowBadgeNumberWhenSelection = True
        Me.btniPad.tbShowNew = False
        Me.btniPad.tbSplit = "3,3,3,3"
        Me.btniPad.tbTag = Nothing
        Me.btniPad.tbToolTip = ""
        Me.btniPad.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btniPad.Text = "iPad"
        Me.btniPad.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniPad.UseVisualStyleBackColor = True
        '
        'btnShareTo
        '
        Me.btnShareTo.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnShareTo.BackColor = System.Drawing.Color.Transparent
        Me.btnShareTo.BindingForm = Nothing
        Me.btnShareTo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnShareTo.Location = New System.Drawing.Point(600, 5)
        Me.btnShareTo.Margin = New System.Windows.Forms.Padding(0)
        Me.btnShareTo.Name = "btnShareTo"
        Me.btnShareTo.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnShareTo.Selectable = True
        Me.btnShareTo.Size = New System.Drawing.Size(69, 23)
        Me.btnShareTo.TabIndex = 23
        Me.btnShareTo.tbAdriftIconWhenHover = False
        Me.btnShareTo.tbAutoSize = False
        Me.btnShareTo.tbAutoSizeEx = True
        Me.btnShareTo.tbBackgroundImage = Nothing
        Me.btnShareTo.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnShareTo.tbBadgeNumber = 0
        Me.btnShareTo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnShareTo.tbEndEllipsis = False
        Me.btnShareTo.tbIconHoldPlace = True
        Me.btnShareTo.tbIconImage = Global.iTong.My.Resources.Resources.btn_facebook_4
        Me.btnShareTo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnShareTo.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnShareTo.tbIconMore = False
        Me.btnShareTo.tbIconMouseDown = Nothing
        Me.btnShareTo.tbIconMouseHover = Nothing
        Me.btnShareTo.tbIconMouseLeave = Nothing
        Me.btnShareTo.tbIconPlaceText = 2
        Me.btnShareTo.tbIconReadOnly = Nothing
        Me.btnShareTo.tbImageMouseDown = Nothing
        Me.btnShareTo.tbImageMouseHover = Nothing
        Me.btnShareTo.tbImageMouseLeave = Nothing
        Me.btnShareTo.tbProgressValue = 50
        Me.btnShareTo.tbReadOnly = False
        Me.btnShareTo.tbReadOnlyText = False
        Me.btnShareTo.tbShadow = False
        Me.btnShareTo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnShareTo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnShareTo.tbShowDot = False
        Me.btnShareTo.tbShowMoreIconImg = CType(resources.GetObject("btnShareTo.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnShareTo.tbShowNew = False
        Me.btnShareTo.tbShowProgress = False
        Me.btnShareTo.tbShowTip = True
        Me.btnShareTo.tbShowToolTipOnButton = False
        Me.btnShareTo.tbSplit = "3,3,3,3"
        Me.btnShareTo.tbText = "分享到"
        Me.btnShareTo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnShareTo.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnShareTo.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnShareTo.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnShareTo.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnShareTo.tbTextMouseDownPlace = 2
        Me.btnShareTo.tbToolTip = ""
        Me.btnShareTo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnShareTo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnShareTo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnShareTo.VisibleEx = True
        '
        'btnSelectAll
        '
        Me.btnSelectAll.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnSelectAll.BackColor = System.Drawing.Color.Transparent
        Me.btnSelectAll.BindingForm = Nothing
        Me.btnSelectAll.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSelectAll.Location = New System.Drawing.Point(680, 6)
        Me.btnSelectAll.Name = "btnSelectAll"
        Me.btnSelectAll.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSelectAll.Selectable = True
        Me.btnSelectAll.Size = New System.Drawing.Size(93, 21)
        Me.btnSelectAll.TabIndex = 22
        Me.btnSelectAll.tbAdriftIconWhenHover = False
        Me.btnSelectAll.tbAutoSize = False
        Me.btnSelectAll.tbAutoSizeEx = True
        Me.btnSelectAll.tbBackgroundImage = Nothing
        Me.btnSelectAll.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSelectAll.tbBadgeNumber = 0
        Me.btnSelectAll.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSelectAll.tbEndEllipsis = False
        Me.btnSelectAll.tbIconHoldPlace = True
        Me.btnSelectAll.tbIconImage = Global.iTong.My.Resources.Resources.btn_selectall_4
        Me.btnSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSelectAll.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnSelectAll.tbIconMore = False
        Me.btnSelectAll.tbIconMouseDown = Nothing
        Me.btnSelectAll.tbIconMouseHover = Nothing
        Me.btnSelectAll.tbIconMouseLeave = Nothing
        Me.btnSelectAll.tbIconPlaceText = 2
        Me.btnSelectAll.tbIconReadOnly = Nothing
        Me.btnSelectAll.tbImageMouseDown = Nothing
        Me.btnSelectAll.tbImageMouseHover = Nothing
        Me.btnSelectAll.tbImageMouseLeave = Nothing
        Me.btnSelectAll.tbProgressValue = 50
        Me.btnSelectAll.tbReadOnly = False
        Me.btnSelectAll.tbReadOnlyText = False
        Me.btnSelectAll.tbShadow = False
        Me.btnSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSelectAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSelectAll.tbShowDot = False
        Me.btnSelectAll.tbShowMoreIconImg = CType(resources.GetObject("btnSelectAll.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSelectAll.tbShowNew = False
        Me.btnSelectAll.tbShowProgress = False
        Me.btnSelectAll.tbShowTip = True
        Me.btnSelectAll.tbShowToolTipOnButton = False
        Me.btnSelectAll.tbSplit = "13,11,13,11"
        Me.btnSelectAll.tbText = "Select all"
        Me.btnSelectAll.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectAll.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSelectAll.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnSelectAll.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSelectAll.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSelectAll.tbTextMouseDownPlace = 2
        Me.btnSelectAll.tbToolTip = ""
        Me.btnSelectAll.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSelectAll.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSelectAll.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectAll.VisibleEx = True
        '
        'btnImportTo
        '
        Me.btnImportTo.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnImportTo.BackColor = System.Drawing.Color.Transparent
        Me.btnImportTo.BindingForm = Nothing
        Me.btnImportTo.Enabled = False
        Me.btnImportTo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImportTo.Location = New System.Drawing.Point(10, 5)
        Me.btnImportTo.Margin = New System.Windows.Forms.Padding(0)
        Me.btnImportTo.Name = "btnImportTo"
        Me.btnImportTo.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImportTo.Selectable = True
        Me.btnImportTo.Size = New System.Drawing.Size(81, 23)
        Me.btnImportTo.TabIndex = 6
        Me.btnImportTo.tbAdriftIconWhenHover = False
        Me.btnImportTo.tbAutoSize = False
        Me.btnImportTo.tbAutoSizeEx = True
        Me.btnImportTo.tbBackgroundImage = Nothing
        Me.btnImportTo.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnImportTo.tbBadgeNumber = 0
        Me.btnImportTo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImportTo.tbEndEllipsis = False
        Me.btnImportTo.tbIconHoldPlace = True
        Me.btnImportTo.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.btnImportTo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImportTo.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImportTo.tbIconMore = False
        Me.btnImportTo.tbIconMouseDown = Nothing
        Me.btnImportTo.tbIconMouseHover = Nothing
        Me.btnImportTo.tbIconMouseLeave = Nothing
        Me.btnImportTo.tbIconPlaceText = 2
        Me.btnImportTo.tbIconReadOnly = Nothing
        Me.btnImportTo.tbImageMouseDown = Nothing
        Me.btnImportTo.tbImageMouseHover = Nothing
        Me.btnImportTo.tbImageMouseLeave = Nothing
        Me.btnImportTo.tbProgressValue = 50
        Me.btnImportTo.tbReadOnly = False
        Me.btnImportTo.tbReadOnlyText = False
        Me.btnImportTo.tbShadow = False
        Me.btnImportTo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImportTo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImportTo.tbShowDot = False
        Me.btnImportTo.tbShowMoreIconImg = CType(resources.GetObject("btnImportTo.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImportTo.tbShowNew = False
        Me.btnImportTo.tbShowProgress = False
        Me.btnImportTo.tbShowTip = True
        Me.btnImportTo.tbShowToolTipOnButton = False
        Me.btnImportTo.tbSplit = "3,3,3,3"
        Me.btnImportTo.tbText = "导入设备"
        Me.btnImportTo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportTo.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnImportTo.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnImportTo.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImportTo.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImportTo.tbTextMouseDownPlace = 2
        Me.btnImportTo.tbToolTip = ""
        Me.btnImportTo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImportTo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImportTo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportTo.VisibleEx = True
        '
        'pnl_Container
        '
        Me.pnl_Container.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnl_Container.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnl_Container.Controls.Add(Me.pnlDownloadWallpaper)
        Me.pnl_Container.Controls.Add(Me.pnlFileEmpty)
        Me.pnl_Container.Location = New System.Drawing.Point(1, 32)
        Me.pnl_Container.Name = "pnl_Container"
        Me.pnl_Container.Size = New System.Drawing.Size(783, 542)
        Me.pnl_Container.TabIndex = 37
        '
        'pnlDownloadWallpaper
        '
        Me.pnlDownloadWallpaper.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlDownloadWallpaper.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlDownloadWallpaper.Controls.Add(Me.picImportRingtone)
        Me.pnlDownloadWallpaper.Controls.Add(Me.TbButton1)
        Me.pnlDownloadWallpaper.Controls.Add(Me.lblWallpaperDescription)
        Me.pnlDownloadWallpaper.Controls.Add(Me.lblWallpaperTitle)
        Me.pnlDownloadWallpaper.Controls.Add(Me.btnGotoSite)
        Me.pnlDownloadWallpaper.Controls.Add(Me.lblGetWallpaper)
        Me.pnlDownloadWallpaper.Controls.Add(Me.lblWallpaper)
        Me.pnlDownloadWallpaper.Location = New System.Drawing.Point(10, 3)
        Me.pnlDownloadWallpaper.Name = "pnlDownloadWallpaper"
        Me.pnlDownloadWallpaper.Size = New System.Drawing.Size(399, 495)
        Me.pnlDownloadWallpaper.TabIndex = 38
        Me.pnlDownloadWallpaper.Visible = False
        '
        'picImportRingtone
        '
        Me.picImportRingtone.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.picImportRingtone.Image = Global.iTong.My.Resources.Resources.photo_pic_importwallpaper
        Me.picImportRingtone.Location = New System.Drawing.Point(48, 236)
        Me.picImportRingtone.Name = "picImportRingtone"
        Me.picImportRingtone.Size = New System.Drawing.Size(78, 80)
        Me.picImportRingtone.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picImportRingtone.TabIndex = 44
        Me.picImportRingtone.TabStop = False
        '
        'TbButton1
        '
        Me.TbButton1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton1.BackColor = System.Drawing.Color.Transparent
        Me.TbButton1.BindingForm = Nothing
        Me.TbButton1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TbButton1.Location = New System.Drawing.Point(10, 152)
        Me.TbButton1.Name = "TbButton1"
        Me.TbButton1.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton1.Selectable = True
        Me.TbButton1.Size = New System.Drawing.Size(380, 2)
        Me.TbButton1.TabIndex = 43
        Me.TbButton1.tbAdriftIconWhenHover = False
        Me.TbButton1.tbAutoSize = False
        Me.TbButton1.tbAutoSizeEx = False
        Me.TbButton1.tbBackgroundImage = Global.iTong.My.Resources.Resources.welcome_btn_split
        Me.TbButton1.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton1.tbBadgeNumber = 0
        Me.TbButton1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton1.tbEndEllipsis = False
        Me.TbButton1.tbIconHoldPlace = True
        Me.TbButton1.tbIconImage = Nothing
        Me.TbButton1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton1.tbIconMore = False
        Me.TbButton1.tbIconMouseDown = Nothing
        Me.TbButton1.tbIconMouseHover = Nothing
        Me.TbButton1.tbIconMouseLeave = Nothing
        Me.TbButton1.tbIconPlaceText = 2
        Me.TbButton1.tbIconReadOnly = Nothing
        Me.TbButton1.tbImageMouseDown = Nothing
        Me.TbButton1.tbImageMouseHover = Nothing
        Me.TbButton1.tbImageMouseLeave = Nothing
        Me.TbButton1.tbProgressValue = 50
        Me.TbButton1.tbReadOnly = False
        Me.TbButton1.tbReadOnlyText = False
        Me.TbButton1.tbShadow = False
        Me.TbButton1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton1.tbShowDot = False
        Me.TbButton1.tbShowMoreIconImg = CType(resources.GetObject("TbButton1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton1.tbShowNew = False
        Me.TbButton1.tbShowProgress = False
        Me.TbButton1.tbShowTip = True
        Me.TbButton1.tbShowToolTipOnButton = False
        Me.TbButton1.tbSplit = "35,0,35,0"
        Me.TbButton1.tbText = ""
        Me.TbButton1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbTextColor = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton1.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton1.tbTextMouseDownPlace = 0
        Me.TbButton1.tbToolTip = ""
        Me.TbButton1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.VisibleEx = True
        '
        'lblWallpaperDescription
        '
        Me.lblWallpaperDescription.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblWallpaperDescription.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWallpaperDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblWallpaperDescription.Location = New System.Drawing.Point(45, 107)
        Me.lblWallpaperDescription.Name = "lblWallpaperDescription"
        Me.lblWallpaperDescription.Size = New System.Drawing.Size(321, 23)
        Me.lblWallpaperDescription.TabIndex = 42
        Me.lblWallpaperDescription.tbAdriftWhenHover = False
        Me.lblWallpaperDescription.tbAutoEllipsis = False
        Me.lblWallpaperDescription.tbAutoSize = False
        Me.lblWallpaperDescription.tbHideImage = False
        Me.lblWallpaperDescription.tbIconImage = Nothing
        Me.lblWallpaperDescription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblWallpaperDescription.tbIconPlaceText = 5
        Me.lblWallpaperDescription.tbShadow = False
        Me.lblWallpaperDescription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblWallpaperDescription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblWallpaperDescription.tbShowScrolling = False
        Me.lblWallpaperDescription.Text = "您通过 同步壁纸站 下载的壁纸将会自动添加到这里。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'lblWallpaperTitle
        '
        Me.lblWallpaperTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblWallpaperTitle.Font = New System.Drawing.Font("宋体", 21.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWallpaperTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblWallpaperTitle.Location = New System.Drawing.Point(45, 46)
        Me.lblWallpaperTitle.Name = "lblWallpaperTitle"
        Me.lblWallpaperTitle.Size = New System.Drawing.Size(296, 46)
        Me.lblWallpaperTitle.TabIndex = 41
        Me.lblWallpaperTitle.tbAdriftWhenHover = False
        Me.lblWallpaperTitle.tbAutoEllipsis = False
        Me.lblWallpaperTitle.tbAutoSize = False
        Me.lblWallpaperTitle.tbHideImage = False
        Me.lblWallpaperTitle.tbIconImage = Nothing
        Me.lblWallpaperTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblWallpaperTitle.tbIconPlaceText = 5
        Me.lblWallpaperTitle.tbShadow = False
        Me.lblWallpaperTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblWallpaperTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblWallpaperTitle.tbShowScrolling = False
        Me.lblWallpaperTitle.Text = "资料库中没有壁纸"
        '
        'btnGotoSite
        '
        Me.btnGotoSite.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnGotoSite.BackColor = System.Drawing.Color.Transparent
        Me.btnGotoSite.BindingForm = Nothing
        Me.btnGotoSite.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnGotoSite.Location = New System.Drawing.Point(323, 293)
        Me.btnGotoSite.Name = "btnGotoSite"
        Me.btnGotoSite.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnGotoSite.Selectable = True
        Me.btnGotoSite.Size = New System.Drawing.Size(12, 12)
        Me.btnGotoSite.TabIndex = 36
        Me.btnGotoSite.tbAdriftIconWhenHover = False
        Me.btnGotoSite.tbAutoSize = False
        Me.btnGotoSite.tbAutoSizeEx = True
        Me.btnGotoSite.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_goto
        Me.btnGotoSite.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnGotoSite.tbBadgeNumber = 0
        Me.btnGotoSite.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGotoSite.tbEndEllipsis = False
        Me.btnGotoSite.tbIconHoldPlace = True
        Me.btnGotoSite.tbIconImage = Nothing
        Me.btnGotoSite.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoSite.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGotoSite.tbIconMore = False
        Me.btnGotoSite.tbIconMouseDown = Nothing
        Me.btnGotoSite.tbIconMouseHover = Nothing
        Me.btnGotoSite.tbIconMouseLeave = Nothing
        Me.btnGotoSite.tbIconPlaceText = 2
        Me.btnGotoSite.tbIconReadOnly = Nothing
        Me.btnGotoSite.tbImageMouseDown = Nothing
        Me.btnGotoSite.tbImageMouseHover = Nothing
        Me.btnGotoSite.tbImageMouseLeave = Nothing
        Me.btnGotoSite.tbProgressValue = 50
        Me.btnGotoSite.tbReadOnly = False
        Me.btnGotoSite.tbReadOnlyText = False
        Me.btnGotoSite.tbShadow = False
        Me.btnGotoSite.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGotoSite.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGotoSite.tbShowDot = False
        Me.btnGotoSite.tbShowMoreIconImg = CType(resources.GetObject("btnGotoSite.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGotoSite.tbShowNew = False
        Me.btnGotoSite.tbShowProgress = False
        Me.btnGotoSite.tbShowTip = True
        Me.btnGotoSite.tbShowToolTipOnButton = False
        Me.btnGotoSite.tbSplit = "0,0,0,0"
        Me.btnGotoSite.tbText = ""
        Me.btnGotoSite.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoSite.tbTextColor = System.Drawing.Color.White
        Me.btnGotoSite.tbTextColorDisable = System.Drawing.Color.White
        Me.btnGotoSite.tbTextColorDown = System.Drawing.Color.White
        Me.btnGotoSite.tbTextColorHover = System.Drawing.Color.White
        Me.btnGotoSite.tbTextMouseDownPlace = 0
        Me.btnGotoSite.tbToolTip = ""
        Me.btnGotoSite.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGotoSite.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGotoSite.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoSite.VisibleEx = True
        '
        'lblGetWallpaper
        '
        Me.lblGetWallpaper.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblGetWallpaper.AutoSize = True
        Me.lblGetWallpaper.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblGetWallpaper.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblGetWallpaper.Location = New System.Drawing.Point(144, 293)
        Me.lblGetWallpaper.Name = "lblGetWallpaper"
        Me.lblGetWallpaper.Size = New System.Drawing.Size(173, 12)
        Me.lblGetWallpaper.TabIndex = 34
        Me.lblGetWallpaper.Text = "到 同步壁纸 中浏览和下载壁纸"
        '
        'lblWallpaper
        '
        Me.lblWallpaper.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblWallpaper.AutoSize = True
        Me.lblWallpaper.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblWallpaper.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblWallpaper.Location = New System.Drawing.Point(141, 249)
        Me.lblWallpaper.Name = "lblWallpaper"
        Me.lblWallpaper.Size = New System.Drawing.Size(76, 16)
        Me.lblWallpaper.TabIndex = 32
        Me.lblWallpaper.Text = "下载壁纸"
        '
        'pnlFileEmpty
        '
        Me.pnlFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlFileEmpty.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFileEmpty.Controls.Add(Me.picFileEmpty)
        Me.pnlFileEmpty.Controls.Add(Me.lblFileEmpty)
        Me.pnlFileEmpty.Location = New System.Drawing.Point(415, 223)
        Me.pnlFileEmpty.Name = "pnlFileEmpty"
        Me.pnlFileEmpty.Size = New System.Drawing.Size(354, 147)
        Me.pnlFileEmpty.TabIndex = 29
        Me.pnlFileEmpty.Visible = False
        '
        'picFileEmpty
        '
        Me.picFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picFileEmpty.Image = Global.iTong.My.Resources.Resources.icon_photonodata_100
        Me.picFileEmpty.Location = New System.Drawing.Point(126, 2)
        Me.picFileEmpty.Name = "picFileEmpty"
        Me.picFileEmpty.Size = New System.Drawing.Size(100, 100)
        Me.picFileEmpty.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picFileEmpty.TabIndex = 1
        Me.picFileEmpty.TabStop = False
        '
        'lblFileEmpty
        '
        Me.lblFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFileEmpty.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFileEmpty.ForeColor = System.Drawing.Color.FromArgb(CType(CType(159, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(163, Byte), Integer))
        Me.lblFileEmpty.Location = New System.Drawing.Point(86, 123)
        Me.lblFileEmpty.Name = "lblFileEmpty"
        Me.lblFileEmpty.Size = New System.Drawing.Size(181, 19)
        Me.lblFileEmpty.TabIndex = 0
        Me.lblFileEmpty.tbAdriftWhenHover = False
        Me.lblFileEmpty.tbAutoEllipsis = False
        Me.lblFileEmpty.tbAutoSize = False
        Me.lblFileEmpty.tbHideImage = False
        Me.lblFileEmpty.tbIconImage = Nothing
        Me.lblFileEmpty.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileEmpty.tbIconPlaceText = 5
        Me.lblFileEmpty.tbShadow = False
        Me.lblFileEmpty.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileEmpty.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileEmpty.tbShowScrolling = False
        Me.lblFileEmpty.Text = "这里没有内容"
        Me.lblFileEmpty.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'menuWallpaper
        '
        Me.menuWallpaper.AccessibleDescription = "155x248"
        Me.menuWallpaper.DropShadowEnabled = False
        Me.menuWallpaper.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.menuWallpaper.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiLook, Me.tssLook, Me.tsmiRefresh, Me.tsmiOpenExplorer, Me.tssOpenExplorer, Me.tsmiDelete, Me.tssDelete, Me.tsmiShareToSina, Me.tsmiShareToTecent, Me.tsmiShareToFacebook, Me.tsmiShareToTwitter, Me.tss4, Me.tsmiImportTo})
        Me.menuWallpaper.Name = "menuWallpaper"
        Me.menuWallpaper.Size = New System.Drawing.Size(155, 226)
        Me.menuWallpaper.tbBackColor = System.Drawing.Color.White
        Me.menuWallpaper.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuWallpaper.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuWallpaper.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiLook
        '
        Me.tsmiLook.Name = "tsmiLook"
        Me.tsmiLook.Size = New System.Drawing.Size(154, 22)
        Me.tsmiLook.Text = "查看"
        '
        'tssLook
        '
        Me.tssLook.Name = "tssLook"
        Me.tssLook.Size = New System.Drawing.Size(151, 6)
        '
        'tsmiRefresh
        '
        Me.tsmiRefresh.Name = "tsmiRefresh"
        Me.tsmiRefresh.Size = New System.Drawing.Size(154, 22)
        Me.tsmiRefresh.Text = "刷新"
        '
        'tsmiOpenExplorer
        '
        Me.tsmiOpenExplorer.Name = "tsmiOpenExplorer"
        Me.tsmiOpenExplorer.Size = New System.Drawing.Size(154, 22)
        Me.tsmiOpenExplorer.Text = "打开文件夹"
        '
        'tssOpenExplorer
        '
        Me.tssOpenExplorer.Name = "tssOpenExplorer"
        Me.tssOpenExplorer.Size = New System.Drawing.Size(151, 6)
        '
        'tsmiDelete
        '
        Me.tsmiDelete.Name = "tsmiDelete"
        Me.tsmiDelete.Size = New System.Drawing.Size(154, 22)
        Me.tsmiDelete.Text = "删除"
        '
        'tssDelete
        '
        Me.tssDelete.Name = "tssDelete"
        Me.tssDelete.Size = New System.Drawing.Size(151, 6)
        '
        'tsmiShareToSina
        '
        Me.tsmiShareToSina.Name = "tsmiShareToSina"
        Me.tsmiShareToSina.Size = New System.Drawing.Size(154, 22)
        Me.tsmiShareToSina.Text = "分享到新浪微博"
        '
        'tsmiShareToTecent
        '
        Me.tsmiShareToTecent.Name = "tsmiShareToTecent"
        Me.tsmiShareToTecent.Size = New System.Drawing.Size(154, 22)
        Me.tsmiShareToTecent.Text = "分享到腾讯微博"
        '
        'tsmiShareToFacebook
        '
        Me.tsmiShareToFacebook.Name = "tsmiShareToFacebook"
        Me.tsmiShareToFacebook.Size = New System.Drawing.Size(154, 22)
        Me.tsmiShareToFacebook.Text = "分享到Facebook"
        '
        'tsmiShareToTwitter
        '
        Me.tsmiShareToTwitter.Name = "tsmiShareToTwitter"
        Me.tsmiShareToTwitter.Size = New System.Drawing.Size(154, 22)
        Me.tsmiShareToTwitter.Text = "分享到Twitter"
        '
        'tss4
        '
        Me.tss4.Name = "tss4"
        Me.tss4.Size = New System.Drawing.Size(151, 6)
        '
        'tsmiImportTo
        '
        Me.tsmiImportTo.Name = "tsmiImportTo"
        Me.tsmiImportTo.Size = New System.Drawing.Size(154, 22)
        Me.tsmiImportTo.Text = "添加到设备"
        '
        'cmsWeibo
        '
        Me.cmsWeibo.AccessibleDescription = "153x114"
        Me.cmsWeibo.DropShadowEnabled = False
        Me.cmsWeibo.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsWeibo.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiSina, Me.tsmiTecent, Me.tsmiFacebook, Me.tsmiTwitter})
        Me.cmsWeibo.Name = "munSearch"
        Me.cmsWeibo.Size = New System.Drawing.Size(119, 92)
        Me.cmsWeibo.tbBackColor = System.Drawing.Color.White
        Me.cmsWeibo.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsWeibo.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsWeibo.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiSina
        '
        Me.tsmiSina.Name = "tsmiSina"
        Me.tsmiSina.Size = New System.Drawing.Size(118, 22)
        Me.tsmiSina.Text = "新浪微博"
        '
        'tsmiTecent
        '
        Me.tsmiTecent.Name = "tsmiTecent"
        Me.tsmiTecent.Size = New System.Drawing.Size(118, 22)
        Me.tsmiTecent.Text = "腾讯微博"
        '
        'tsmiFacebook
        '
        Me.tsmiFacebook.Name = "tsmiFacebook"
        Me.tsmiFacebook.Size = New System.Drawing.Size(118, 22)
        Me.tsmiFacebook.Text = "Facebook"
        '
        'tsmiTwitter
        '
        Me.tsmiTwitter.Name = "tsmiTwitter"
        Me.tsmiTwitter.Size = New System.Drawing.Size(118, 22)
        Me.tsmiTwitter.Text = "Twitter"
        '
        'frmDownloadWallPaper
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.Silver
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(784, 574)
        Me.Controls.Add(Me.pnl_Container)
        Me.Controls.Add(Me.pnlTop)
        Me.Name = "frmDownloadWallPaper"
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmWallpaperLocal"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlTop, 0)
        Me.Controls.SetChildIndex(Me.pnl_Container, 0)
        Me.pnlTop.ResumeLayout(False)
        Me.pnlDeviceType.ResumeLayout(False)
        Me.pnl_Container.ResumeLayout(False)
        Me.pnlDownloadWallpaper.ResumeLayout(False)
        Me.pnlDownloadWallpaper.PerformLayout()
        CType(Me.picImportRingtone, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlFileEmpty.ResumeLayout(False)
        Me.pnlFileEmpty.PerformLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).EndInit()
        Me.menuWallpaper.ResumeLayout(False)
        Me.cmsWeibo.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents btnImportTo As tbButton
    Friend WithEvents pnl_Container As System.Windows.Forms.Panel
    Friend WithEvents menuWallpaper As tbContextMenuStrip
    Friend WithEvents tsmiLook As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiRefresh As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiOpenExplorer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiImportTo As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tssLook As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tssOpenExplorer As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tssDelete As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents pnlDownloadWallpaper As System.Windows.Forms.Panel
    Friend WithEvents lblGetWallpaper As System.Windows.Forms.Label
    Friend WithEvents lblWallpaper As System.Windows.Forms.Label
    Friend WithEvents btnGotoSite As tbButton
    Friend WithEvents picImportRingtone As System.Windows.Forms.PictureBox
    Friend WithEvents TbButton1 As tbButton
    Friend WithEvents lblWallpaperDescription As tbLabel
    Friend WithEvents lblWallpaperTitle As tbLabel
    Friend WithEvents btnSelectAll As tbButton
    Friend WithEvents tsmiShareToSina As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnShareTo As tbButton
    Friend WithEvents tsmiShareToTecent As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShareToFacebook As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShareToTwitter As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents cmsWeibo As tbContextMenuStrip
    Friend WithEvents tsmiSina As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTecent As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiFacebook As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTwitter As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tss4 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents btnAndroid As tbRadioButton
    Friend WithEvents btniPad As tbRadioButton
    Friend WithEvents btnIPhone As tbRadioButton
    Friend WithEvents pnlDeviceType As tbPanel
    Friend WithEvents btnDelete As tbButton
    Friend WithEvents pnlFileEmpty As System.Windows.Forms.Panel
    Friend WithEvents picFileEmpty As System.Windows.Forms.PictureBox
    Friend WithEvents lblFileEmpty As tbLabel
    Friend WithEvents btnRefresh As iTong.Components.tbButton
End Class
