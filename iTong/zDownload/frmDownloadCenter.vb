﻿Imports System.Xml
Imports System.Text
Imports System.IO
Imports System.Data
Imports iTong.CoreFoundation
Imports iTong.Components


Public Class frmDownloadCenter

    Const MYTASK As String = "MyTask"
    Const DOWNLOADING As String = "Downloading"
    Const DOWNLOADDED As String = "Downloadded"

    Private mfrmDownloading As frmDownloading = Nothing
    Private mfrmDownloaded As frmDownloaded = Nothing

#If IS_ITONG Then
    Private mfrmImageLocal As frmDownloadWallPaper = Nothing
#End If

    Private mCurrentForm As Form = Nothing
    Private mNodeProcessing As tbTreeNode = Nothing
    Private mNodeAction As tbTreeNode = Nothing
    Private mDictTypeToNode As New Dictionary(Of DownloadView, tbTreeNode)
    Private mDownloadDB As MultiThreadDownloadDB = Nothing
    Private Shared mDeviceType As DeviceType = DeviceType.iOS

    Dim mSoftCount As Integer = 0
    Dim mMusicCount As Integer = 0
    Dim mRingtongCount As Integer = 0
    Dim mPaperCount As Integer = 0
    Dim mFirmwareCount As Integer = 0

#Region "--- 初始化 ---"

    Private Sub New(ByVal app As IApplication)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong

        Me.Name = ""
        Me.FunctionMappingKey = FunctionKey.Download
        Me.FilletRadius = 5
        Me.mApplication = app

        mDownloadDB = MultiThreadDownloadDB.GetInstance()
        'AddHandler tbPlugins.DownloadManager.DownloadItemCompleted, AddressOf OnDownloaded
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()


        Me.InitTree()
        Me.InitialForms()

        tvwDownload.SelectedNode = Me.mNodeProcessing
        If Not btwLoadCount.IsBusy Then
            btwLoadCount.RunWorkerAsync()
        End If
    End Sub

    Protected Sub InitialForms()
        mfrmDownloading = New frmDownloading
        'mfrmDownloadAction = New frmDownloadAction
        Utility.AddForm2Panel(Me.mfrmDownloading, Me.pnlRight, False)
    End Sub

    Protected Sub InitTree()
        'tvwDownload.BackColor = Color.FromArgb(233, 237, 243)
        Me.tvwDownload.SuspendLayout()
        Dim nodeMyTask As New tbTreeNode(MYTASK, Me.Language.GetString("Download.Column.MyDownload"), _
                                         Color.FromArgb(119, 143, 178), True)
        'nodeMyTask.BackColor = Color.Transparent
        nodeMyTask.CanCollapse = True
        Me.tvwDownload.Nodes.Add(nodeMyTask)

        Dim nodeDownloaded As New tbTreeNode(DOWNLOADDED, Me.Language.GetString("Download.Column.Downloaded"), _
                                         Color.FromArgb(119, 143, 178), True)
        nodeDownloaded.CanCollapse = True
        'nodeDownloaded.BackColor = Color.Transparent
        Me.tvwDownload.Nodes.Add(nodeDownloaded)

        Dim nodeDownloading As New tbTreeNode("Downloading", Me.Language.GetString("App.Cell.Downloading"))
        nodeDownloading.NodeIcon = My.Resources.icons_itong_downloading_116
        nodeDownloading.NodeIconState = ImageState.TwoState
        nodeMyTask.ChildNodes.Add(nodeDownloading)

        Dim nodeSoftware As New tbTreeNode("Software", Me.Language.GetString("Main.Button.App"))

        nodeSoftware.Tag = DownloadView.Software
        nodeSoftware.NodeIcon = My.Resources.tvw_apps_2
        nodeSoftware.NodeIconState = ImageState.TwoState

        nodeDownloaded.ChildNodes.Add(nodeSoftware)

        Me.mNodeProcessing = nodeDownloading
        Me.mDictTypeToNode(DownloadView.Software) = nodeSoftware


#If IS_ITONG Then

        Dim nodeMusic As New tbTreeNode("Music", Me.Language.GetString("Main.Button.Music"))
        Dim nodeRingtone As New tbTreeNode("Ringtone", Me.Language.GetString("Media.Type.Ringtone"))
        Dim nodePaper As New tbTreeNode("Paper", Me.Language.GetString("Photo.Label.WallPaper"))
        Dim nodeFirmware As New tbTreeNode("Firmware", Me.Language.GetString("Download.Label.Firmware"))            '"固件"

        nodeMusic.Tag = DownloadView.Music
        nodeMusic.NodeIcon = My.Resources.tvw_music_2
        nodeMusic.NodeIconState = ImageState.TwoState

        nodeRingtone.Tag = DownloadView.Ringtone
        nodeRingtone.NodeIcon = My.Resources.tvw_ringtone_2
        nodeRingtone.NodeIconState = ImageState.TwoState

        nodePaper.Tag = DownloadView.Paper
        nodePaper.NodeIcon = My.Resources.tvw_wallpaper_2
        nodePaper.NodeIconState = ImageState.TwoState

        nodeFirmware.Tag = DownloadView.Firmware
        nodeFirmware.NodeIcon = My.Resources.tvw_firmware_2
        nodeFirmware.NodeIconState = ImageState.TwoState

        nodeDownloaded.ChildNodes.Add(nodeMusic)
        nodeDownloaded.ChildNodes.Add(nodeRingtone)
        nodeDownloaded.ChildNodes.Add(nodePaper)
        nodeDownloaded.ChildNodes.Add(nodeFirmware)

        Me.mDictTypeToNode(DownloadView.Music) = nodeMusic
        Me.mDictTypeToNode(DownloadView.Ringtone) = nodeRingtone
        Me.mDictTypeToNode(DownloadView.Paper) = nodePaper
        Me.mDictTypeToNode(DownloadView.Firmware) = nodeFirmware
        nodeFirmware.Visible = False

        If Folder.LangType <> LanguageType.zh_CN Then
            nodeMusic.Visible = False
            nodeRingtone.Visible = False
            nodePaper.Visible = False

        End If
#End If

        Me.tvwDownload.ResumeLayout()

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Download.FormTitle")
    End Sub

#End Region

#Region "--- 单实例 ---"

    Private Shared frmDown As frmDownloadCenter = Nothing
    Public Shared Sub ShowForm(ByVal frmMain As Form)
        ShowForm(frmMain, DeviceType.iOS)
    End Sub

    Public Shared Sub ShowForm(ByVal frmMain As Form, ByVal currentDeviceType As DeviceType)
        Try
            ' 确认窗体已经被创建
            If frmDown Is Nothing OrElse frmDown.IsDisposed Then
                frmDown = New frmDownloadCenter(frmMain)
                frmDown.StartPosition = FormStartPosition.Manual
            End If

            mDeviceType = currentDeviceType
            frmDown.Application = frmMain
            If (Not frmDown.Visible) OrElse frmDown.WindowState = FormWindowState.Minimized Then
                frmDown.Show()
                frmDown.WindowState = FormWindowState.Normal
            End If
            frmDown.Activate()

            ' Show以后再调整位置，否则最小化的情况下，窗体长宽为0，位置计算会出错
            Dim pos As Point = frmMain.Location
            Dim posNew As Point = New Point(pos.X + (frmMain.Width - frmDown.Width) \ 2, pos.Y + (frmMain.Height - frmDown.Height) \ 2 + 20)
            frmDown.Location = posNew

            If mDeviceType = DeviceType.Android Then
                frmDown.SwitchToAndroid()
            Else
                frmDown.SwitchToIphone()
            End If

        Catch ex As Exception
        End Try
    End Sub

    Public Shared ReadOnly Property Instance() As frmDownloadCenter
        Get
            Return frmDown
        End Get
    End Property

    Private Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
    End Sub

#End Region

#Region "--- 窗体和控件事件 ---"

    Private Sub btn_Visible_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btn_Visible.Click
        Me.Hide()
    End Sub

    Private Sub tvwDownload_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As tbTreeView_SelectedIndexChanged_EventArgs) Handles tvwDownload.SelectedIndexChanged

        Try
            If Not e.Node.Checked Then
                Exit Sub
            End If

            Dim newForm As Form = Nothing

            If e.Node.Name = DOWNLOADING Then   ' 正在下载节点
                newForm = Me.mfrmDownloading
            ElseIf e.Node.Parent IsNot Nothing AndAlso e.Node.Parent.Name = DOWNLOADDED Then    '已下载节点
                Dim type As DownloadView = CType(e.Node.Tag, DownloadView)
                Common.Log(type.ToString())
                If type <> DownloadView.Paper Then  ' 软件或铃声
                    CheckFrmDownloaded()
                    newForm = Me.mfrmDownloaded
                    Me.mfrmDownloaded.SwitchView(type)
                Else                                ' 壁纸
#If IS_ITONG Then
                    ChcekFrmLocalPaper()
                    newForm = Me.mfrmImageLocal
#End If
                End If
            End If

            ' If mCurrentForm Is Nothing Then 'OrElse Not Me.mCurrentForm.Equals(newForm) Then
            If newForm IsNot Nothing Then
                Common.Log(newForm.GetType().FullName)
            End If
            Me.mCurrentForm = newForm
            newForm.Visible = True
            newForm.BringToFront()

            If mDeviceType = DeviceType.Android Then
                Me.SwitchToAndroid()
            Else
                Me.SwitchToIphone()
            End If


            ' 隐藏除了frm的其他窗口
            Me.HideOtherForms(newForm)
            ' End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try

    End Sub

    Private Sub frmDownloadCenter_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        Me.Hide()
        e.Cancel = True
    End Sub

    Private Sub btwLoadCount_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles btwLoadCount.DoWork
        Try
            Dim listSoftware As List(Of MultiThreadDownloadItemInfo) = mDownloadDB.GetAll_Task(ResourceClass.Software, ResourceType.All, TaskState.Completed, "")
            Dim listMusic As List(Of MultiThreadDownloadItemInfo) = mDownloadDB.GetAll_Task(ResourceClass.Music, ResourceType.All, TaskState.Completed, "")
            Dim listRingtone As List(Of MultiThreadDownloadItemInfo) = mDownloadDB.GetAll_Task(ResourceClass.Ringtone, ResourceType.All, TaskState.Completed, "")
            Dim listFirmware As List(Of MultiThreadDownloadItemInfo) = mDownloadDB.GetAll_Task(ResourceClass.Firmware, ResourceType.Ipsw, TaskState.Completed, "")
			Dim listPaper As List(Of MultiThreadDownloadItemInfo) = mDownloadDB.GetAll_Task(ResourceClass.Wallpaper, ResourceType.All, TaskState.Completed, "")
            Dim dicExistedPath As Dictionary(Of String, String) = New Dictionary(Of String, String)()


            mSoftCount = 0
            mMusicCount = 0
            mRingtongCount = 0
            mPaperCount = 0
            mFirmwareCount = 0

			Dim strDnldPath As String

			For Each item As MultiThreadDownloadItemInfo In listSoftware
				strDnldPath = item.DownloadPath.ToLower()

				If File.Exists(item.DownloadPath) AndAlso strDnldPath.EndsWith(".ipa") _
					AndAlso Not dicExistedPath.ContainsKey(strDnldPath) Then

					dicExistedPath(strDnldPath) = strDnldPath
					mSoftCount += 1
				End If

				If Common.VerIs30() Then
					If File.Exists(item.DownloadPath) AndAlso strDnldPath.EndsWith(".apk") _
						AndAlso Not dicExistedPath.ContainsKey(strDnldPath) Then

						dicExistedPath(strDnldPath) = strDnldPath
						mSoftCount += 1
					End If
				End If
			Next

			For Each item As MultiThreadDownloadItemInfo In listMusic
				strDnldPath = item.DownloadPath.ToLower()

				If File.Exists(item.DownloadPath) AndAlso Not dicExistedPath.ContainsKey(strDnldPath) Then
					dicExistedPath(strDnldPath) = strDnldPath
					mMusicCount += 1
				End If
			Next


			For Each item As MultiThreadDownloadItemInfo In listRingtone
				strDnldPath = item.DownloadPath.ToLower()

				If File.Exists(item.DownloadPath) AndAlso Not dicExistedPath.ContainsKey(strDnldPath) Then
					dicExistedPath(strDnldPath) = strDnldPath
					mRingtongCount += 1
				End If
			Next

			For Each item As MultiThreadDownloadItemInfo In listPaper
				strDnldPath = item.DownloadPath.ToLower()

				If File.Exists(item.DownloadPath) AndAlso Not dicExistedPath.ContainsKey(strDnldPath) Then
					dicExistedPath(strDnldPath) = strDnldPath
					mPaperCount += 1
				End If
			Next

			For Each item As MultiThreadDownloadItemInfo In listFirmware
				strDnldPath = item.DownloadPath.ToLower()

				If File.Exists(item.DownloadPath) AndAlso Not dicExistedPath.ContainsKey(strDnldPath) Then
					dicExistedPath(strDnldPath) = strDnldPath
					mFirmwareCount += 1
				End If
			Next

            Dim lstPaths As New List(Of String)
            lstPaths.Add("iPhone")
            lstPaths.Add("iPad")
            If Common.VerIs30() Then
                lstPaths.Add("Android")
            End If

#If IS_ITONG Then
       mPaperCount = frmDownloadWallPaper.WallpaperLoaclObject.GetTotalWallpaperCount(lstPaths)
#End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmDownloadCenter.btwLoadCount_DoWork")
        End Try
    End Sub

    Private Sub btwLoadCount_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles btwLoadCount.RunWorkerCompleted
        UpdateCompleteCount(DownloadView.Music, Me.mMusicCount)
        UpdateCompleteCount(DownloadView.Ringtone, Me.mRingtongCount)
        UpdateCompleteCount(DownloadView.Software, Me.mSoftCount)
        UpdateCompleteCount(DownloadView.Paper, Me.mPaperCount)
        UpdateCompleteCount(DownloadView.Firmware, Me.mFirmwareCount)
    End Sub

    Public Sub SwitchToAndroid()
        If mfrmDownloaded IsNot Nothing Then
            Me.mfrmDownloaded.SwithToAndroid()
        End If

#If IS_ITONG Then
        If Me.mfrmImageLocal IsNot Nothing Then
            Me.mfrmImageLocal.SwithToAndroid()
        End If
#End If
    End Sub

    Public Sub SwitchToIphone()
        If mfrmDownloaded IsNot Nothing Then
            Me.mfrmDownloaded.SwitchToIphone()
        End If

#If IS_ITONG Then
        If Me.mfrmImageLocal IsNot Nothing Then
            Me.mfrmImageLocal.SwitchToIphone()
        End If
#End If
    End Sub

#End Region

#Region "--- 界面刷新 ---"

    Private Sub HideOtherForms(ByVal frm As Form)
        For Each item As Control In Me.pnlRight.Controls
            If Not item.Equals(frm) Then
                item.Hide()
            End If
        Next
    End Sub

    Public Sub UpdateProcessingCount(ByVal count As Integer)
        Me.mNodeProcessing.NodeNumber = count.ToString
    End Sub

    Public Sub UpdateDownloadedCount()

        If Me.mfrmDownloaded Is Nothing And Not btwLoadCount.IsBusy Then
            btwLoadCount.RunWorkerAsync()
        End If
    End Sub

    Public Sub UpdateActionCount(ByVal count As Integer)
        Me.mNodeAction.NodeNumber = count.ToString
    End Sub

    Public Sub UpdateCompleteCount(ByVal type As DownloadView, ByVal count As Integer)
        If Me.mDictTypeToNode.ContainsKey(type) Then
            Dim node As tbTreeNode = Me.mDictTypeToNode(type)
            node.NodeNumber = count.ToString
        End If
    End Sub

#End Region

#Region "--- 辅助函数 ---"

    Private Sub ChcekFrmLocalPaper()
#If IS_ITONG Then
        If mfrmImageLocal Is Nothing Then
            Dim device As iPhoneDevice = Nothing
            If MobileDeviceManager.Instance.ConnectedDevices.Count > 0 Then
                device = MobileDeviceManager.Instance.ConnectedDevices(0)
            End If
            mfrmImageLocal = New frmDownloadWallPaper(Me.mApplication, device, FunctionKey.Photo)
            Utility.AddForm2Panel(Me.mfrmImageLocal, Me.pnlRight, False)
            mfrmImageLocal.SysApplication = Me.Application
        End If
#End If
    End Sub

    Private Sub CheckFrmDownloaded()
        If Me.mfrmDownloaded Is Nothing Then
            mfrmDownloaded = New frmDownloaded(Me.Application)
            Utility.AddForm2Panel(Me.mfrmDownloaded, Me.pnlRight, False)
        End If
        mfrmDownloaded.Application = Me.Application
        mfrmDownloaded.Name = "Downloaded"
    End Sub

#End Region

    Public Property Application() As IApplication
        Get
            Return Me.mApplication
        End Get
        Set(ByVal value As IApplication)
            Me.mApplication = value

#If IS_ITONG Then
            If Me.mfrmImageLocal IsNot Nothing Then
                Me.mfrmImageLocal.SysApplication = value
            End If
#End If
        End Set
    End Property

    Public Property CurrentDeviceType() As DeviceType
        Get
            Return mDeviceType
        End Get
        Set(ByVal value As DeviceType)
            mDeviceType = value
        End Set
    End Property

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_state
        End If
        Me.tvwDownload.tbNodeSelectedBackground = My.Resources.tvw_node_bg_select
    End Sub

End Class