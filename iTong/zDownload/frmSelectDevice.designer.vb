﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSelectDevice
    Inherits tbBaseGuiForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Me.menuDownload = New tbContextMenuStrip(Me.components)
        Me.tsmiOpenExplorer = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiDeleteItem = New System.Windows.Forms.ToolStripMenuItem
        Me.btn_Visible = New tbButton
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.pnlMain = New System.Windows.Forms.Panel
        Me.btnCancel = New tbButton
        Me.btnOK = New tbButton
        Me.lblMessage = New tbLabel
        Me.menuDownload.SuspendLayout()
        Me.SuspendLayout()
        '
        'menuDownload
        '
        Me.menuDownload.DropShadowEnabled = False
        Me.menuDownload.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenExplorer, Me.tsmiDeleteItem})
        Me.menuDownload.Name = "menuInInstalGrid"
        Me.menuDownload.Size = New System.Drawing.Size(135, 48)
        Me.menuDownload.tbBackColor = System.Drawing.Color.White
        Me.menuDownload.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuDownload.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuDownload.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenExplorer
        '
        Me.tsmiOpenExplorer.Name = "tsmiOpenExplorer"
        Me.tsmiOpenExplorer.Size = New System.Drawing.Size(134, 22)
        Me.tsmiOpenExplorer.Text = "打开文件夹"
        '
        'tsmiDeleteItem
        '
        Me.tsmiDeleteItem.Name = "tsmiDeleteItem"
        Me.tsmiDeleteItem.Size = New System.Drawing.Size(134, 22)
        Me.tsmiDeleteItem.Text = "删除"
        '
        'btn_Visible
        '
        Me.btn_Visible.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Visible.BackColor = System.Drawing.Color.Transparent
        Me.btn_Visible.BindingForm = Nothing
        Me.btn_Visible.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_Visible.Location = New System.Drawing.Point(345, 2)
        Me.btn_Visible.Name = "btn_Visible"
        Me.btn_Visible.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Visible.Selectable = True
        Me.btn_Visible.Size = New System.Drawing.Size(24, 24)
        Me.btn_Visible.TabIndex = 22
        Me.btn_Visible.tbAdriftIconWhenHover = False
        Me.btn_Visible.tbAutoSize = False
        Me.btn_Visible.tbAutoSizeEx = False
        Me.btn_Visible.tbBackgroundImage = My.Resources.btn_close
        Me.btn_Visible.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_Visible.tbBadgeNumber = 0
        Me.btn_Visible.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Visible.tbEndEllipsis = False
        Me.btn_Visible.tbIconHoldPlace = True
        Me.btn_Visible.tbIconImage = Nothing
        Me.btn_Visible.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbIconImageState = ImageState.OneState
        Me.btn_Visible.tbIconMore = False
        Me.btn_Visible.tbIconMouseDown = Nothing
        Me.btn_Visible.tbIconMouseHover = Nothing
        Me.btn_Visible.tbIconMouseLeave = Nothing
        Me.btn_Visible.tbIconPlaceText = 2
        Me.btn_Visible.tbIconReadOnly = Nothing
        Me.btn_Visible.tbImageMouseDown = Nothing
        Me.btn_Visible.tbImageMouseHover = Nothing
        Me.btn_Visible.tbImageMouseLeave = Nothing
        Me.btn_Visible.tbReadOnly = False
        Me.btn_Visible.tbReadOnlyText = False
        Me.btn_Visible.tbShadow = False
        Me.btn_Visible.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Visible.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Visible.tbShowNew = False
        Me.btn_Visible.tbShowToolTipOnButton = False
        Me.btn_Visible.tbSplit = "3,3,3,3"
        Me.btn_Visible.tbText = ""
        Me.btn_Visible.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbTextColor = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDown = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorHover = System.Drawing.Color.White
        Me.btn_Visible.tbTextMouseDownPlace = 0
        Me.btn_Visible.tbToolTip = ""
        Me.btn_Visible.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Visible.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Visible.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.VisibleEx = True
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.White
        Me.pnlMain.Location = New System.Drawing.Point(14, 68)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(348, 200)
        Me.pnlMain.TabIndex = 23
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.ForeColor = System.Drawing.Color.Black
        Me.btnCancel.Location = New System.Drawing.Point(196, 282)
        Me.btnCancel.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(74, 23)
        Me.btnCancel.TabIndex = 98
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnCancel.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(286, 282)
        Me.btnOK.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(74, 23)
        Me.btnOK.TabIndex = 97
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.VisibleEx = True
        '
        'lblMessage
        '
        Me.lblMessage.AutoSize = True
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.Location = New System.Drawing.Point(12, 45)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(137, 12)
        Me.lblMessage.TabIndex = 99
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = False
        Me.lblMessage.tbAutoSize = True
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.Text = "请选择您要导入的设备："
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmSelectDevice
        '
        Me.BackColor = System.Drawing.Color.Silver
        Me.ClientSize = New System.Drawing.Size(372, 320)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.btn_Visible)
        Me.MinimumSize = New System.Drawing.Size(20, 66)
        Me.Name = "frmSelectDevice"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,33"
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "选择设备"
        Me.menuDownload.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents menuDownload As tbContextMenuStrip
    Friend WithEvents tsmiOpenExplorer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDeleteItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btn_Visible As tbButton
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents pnlMain As System.Windows.Forms.Panel
    Friend WithEvents btnCancel As tbButton
    Public WithEvents btnOK As tbButton
    Friend WithEvents lblMessage As tbLabel

End Class
