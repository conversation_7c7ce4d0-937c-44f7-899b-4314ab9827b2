﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDownloaded
    Inherits frmDeviceBase

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDownloaded))
        Me.menuDownload = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpenExplorer = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator1 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiInstallItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiDeleteItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnImport = New iTong.Components.tbButton()
        Me.btnInstall = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.btnAndroid = New iTong.Components.tbRadioButton()
        Me.btnIPhone = New iTong.Components.tbRadioButton()
        Me.btnOpen = New iTong.Components.tbButton()
        Me.txtSearch = New iTong.Components.tbSearch()
        Me.pnlBottom = New System.Windows.Forms.Panel()
        Me.pnlFileEmpty = New System.Windows.Forms.Panel()
        Me.btnGotoDownload = New iTong.Components.tbButton()
        Me.picFileEmpty = New System.Windows.Forms.PictureBox()
        Me.lblFileEmpty = New iTong.Components.tbLabel()
        Me.pnlLoading = New System.Windows.Forms.Panel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.bgwLoadData = New System.ComponentModel.BackgroundWorker()
        Me.pnlProgress = New iTong.Components.tbPanel()
        Me.pgbProgress = New iTong.Components.tbControlBar()
        Me.lblCurrentFile = New iTong.Components.tbLabel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.btnAbrot = New iTong.Components.tbButton()
        Me.tpDgvDetail = New iTong.Components.tbToolTip(Me.components)
        Me.menuDownload.SuspendLayout()
        Me.pnlTop.SuspendLayout()
        Me.pnlBottom.SuspendLayout()
        Me.pnlFileEmpty.SuspendLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlProgress.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(869, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(845, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(821, 0)
        '
        'menuDownload
        '
        Me.menuDownload.AccessibleDescription = "153x98"
        Me.menuDownload.DropShadowEnabled = False
        Me.menuDownload.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.menuDownload.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenExplorer, Me.ToolStripSeparator1, Me.tsmiInstallItem, Me.tsmiDeleteItem})
        Me.menuDownload.Name = "menuInInstalGrid"
        Me.menuDownload.Size = New System.Drawing.Size(131, 76)
        Me.menuDownload.tbBackColor = System.Drawing.Color.White
        Me.menuDownload.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuDownload.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuDownload.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenExplorer
        '
        Me.tsmiOpenExplorer.Name = "tsmiOpenExplorer"
        Me.tsmiOpenExplorer.Size = New System.Drawing.Size(130, 22)
        Me.tsmiOpenExplorer.Text = "打开文件夹"
        '
        'ToolStripSeparator1
        '
        Me.ToolStripSeparator1.Name = "ToolStripSeparator1"
        Me.ToolStripSeparator1.Size = New System.Drawing.Size(127, 6)
        '
        'tsmiInstallItem
        '
        Me.tsmiInstallItem.Name = "tsmiInstallItem"
        Me.tsmiInstallItem.Size = New System.Drawing.Size(130, 22)
        Me.tsmiInstallItem.Text = "安装"
        '
        'tsmiDeleteItem
        '
        Me.tsmiDeleteItem.Name = "tsmiDeleteItem"
        Me.tsmiDeleteItem.Size = New System.Drawing.Size(130, 22)
        Me.tsmiDeleteItem.Text = "删除"
        '
        'pnlTop
        '
        Me.pnlTop.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.btnRefresh)
        Me.pnlTop.Controls.Add(Me.btnImport)
        Me.pnlTop.Controls.Add(Me.btnInstall)
        Me.pnlTop.Controls.Add(Me.btnDelete)
        Me.pnlTop.Controls.Add(Me.btnAndroid)
        Me.pnlTop.Controls.Add(Me.btnIPhone)
        Me.pnlTop.Controls.Add(Me.btnOpen)
        Me.pnlTop.Controls.Add(Me.txtSearch)
        Me.pnlTop.Location = New System.Drawing.Point(1, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(892, 31)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 23
        Me.pnlTop.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(162, 5)
        Me.btnRefresh.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(63, 23)
        Me.btnRefresh.TabIndex = 27
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = " 刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 2
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnImport
        '
        Me.btnImport.BackColor = System.Drawing.Color.Transparent
        Me.btnImport.BindingForm = Nothing
        Me.btnImport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImport.Location = New System.Drawing.Point(10, 5)
        Me.btnImport.Name = "btnImport"
        Me.btnImport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImport.Selectable = True
        Me.btnImport.Size = New System.Drawing.Size(81, 23)
        Me.btnImport.TabIndex = 26
        Me.btnImport.tbAdriftIconWhenHover = False
        Me.btnImport.tbAutoSize = False
        Me.btnImport.tbAutoSizeEx = True
        Me.btnImport.tbBackgroundImage = Nothing
        Me.btnImport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbBadgeNumber = 0
        Me.btnImport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImport.tbEndEllipsis = False
        Me.btnImport.tbIconHoldPlace = True
        Me.btnImport.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.btnImport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbIconMore = False
        Me.btnImport.tbIconMouseDown = Nothing
        Me.btnImport.tbIconMouseHover = Nothing
        Me.btnImport.tbIconMouseLeave = Nothing
        Me.btnImport.tbIconPlaceText = 2
        Me.btnImport.tbIconReadOnly = Nothing
        Me.btnImport.tbImageMouseDown = Nothing
        Me.btnImport.tbImageMouseHover = Nothing
        Me.btnImport.tbImageMouseLeave = Nothing
        Me.btnImport.tbProgressValue = 50
        Me.btnImport.tbReadOnly = False
        Me.btnImport.tbReadOnlyText = False
        Me.btnImport.tbShadow = False
        Me.btnImport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImport.tbShowDot = False
        Me.btnImport.tbShowMoreIconImg = CType(resources.GetObject("btnImport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImport.tbShowNew = False
        Me.btnImport.tbShowProgress = False
        Me.btnImport.tbShowTip = True
        Me.btnImport.tbShowToolTipOnButton = False
        Me.btnImport.tbSplit = "13,11,13,11"
        Me.btnImport.tbText = "导入设备"
        Me.btnImport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.tbTextColor = System.Drawing.Color.Black
        Me.btnImport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnImport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextMouseDownPlace = 2
        Me.btnImport.tbToolTip = ""
        Me.btnImport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.VisibleEx = True
        '
        'btnInstall
        '
        Me.btnInstall.BackColor = System.Drawing.Color.Transparent
        Me.btnInstall.BindingForm = Nothing
        Me.btnInstall.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnInstall.Location = New System.Drawing.Point(11, 4)
        Me.btnInstall.Name = "btnInstall"
        Me.btnInstall.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnInstall.Selectable = True
        Me.btnInstall.Size = New System.Drawing.Size(59, 23)
        Me.btnInstall.TabIndex = 24
        Me.btnInstall.tbAdriftIconWhenHover = False
        Me.btnInstall.tbAutoSize = False
        Me.btnInstall.tbAutoSizeEx = True
        Me.btnInstall.tbBackgroundImage = Nothing
        Me.btnInstall.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbBadgeNumber = 0
        Me.btnInstall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstall.tbEndEllipsis = False
        Me.btnInstall.tbIconHoldPlace = True
        Me.btnInstall.tbIconImage = Global.iTong.My.Resources.Resources.btn_install_4
        Me.btnInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnInstall.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbIconMore = False
        Me.btnInstall.tbIconMouseDown = Nothing
        Me.btnInstall.tbIconMouseHover = Nothing
        Me.btnInstall.tbIconMouseLeave = Nothing
        Me.btnInstall.tbIconPlaceText = 2
        Me.btnInstall.tbIconReadOnly = Nothing
        Me.btnInstall.tbImageMouseDown = Nothing
        Me.btnInstall.tbImageMouseHover = Nothing
        Me.btnInstall.tbImageMouseLeave = Nothing
        Me.btnInstall.tbProgressValue = 50
        Me.btnInstall.tbReadOnly = False
        Me.btnInstall.tbReadOnlyText = False
        Me.btnInstall.tbShadow = False
        Me.btnInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstall.tbShowDot = False
        Me.btnInstall.tbShowMoreIconImg = CType(resources.GetObject("btnInstall.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstall.tbShowNew = False
        Me.btnInstall.tbShowProgress = False
        Me.btnInstall.tbShowTip = True
        Me.btnInstall.tbShowToolTipOnButton = False
        Me.btnInstall.tbSplit = "13,11,13,11"
        Me.btnInstall.tbText = "安装"
        Me.btnInstall.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbTextColor = System.Drawing.Color.Black
        Me.btnInstall.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnInstall.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnInstall.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnInstall.tbTextMouseDownPlace = 2
        Me.btnInstall.tbToolTip = ""
        Me.btnInstall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(96, 5)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(57, 23)
        Me.btnDelete.TabIndex = 25
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "13,11,13,11"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbTextColor = System.Drawing.Color.Black
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'btnAndroid
        '
        Me.btnAndroid.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAndroid.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnAndroid.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(170, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnAndroid.ForeColor_Checked = System.Drawing.Color.White
        Me.btnAndroid.Location = New System.Drawing.Point(653, 5)
        Me.btnAndroid.Name = "btnAndroid"
        Me.btnAndroid.Size = New System.Drawing.Size(60, 22)
        Me.btnAndroid.TabIndex = 22
        Me.btnAndroid.TabStop = True
        Me.btnAndroid.tbAdriftIconWhenHover = False
        Me.btnAndroid.tbAutoSize = False
        Me.btnAndroid.tbAutoSizeEx = False
        Me.btnAndroid.tbBadgeNumber = 0
        Me.btnAndroid.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAndroid.tbEnableToolTip = False
        Me.btnAndroid.tbIconCheckedMouseDown = Nothing
        Me.btnAndroid.tbIconCheckedMouseHover = Nothing
        Me.btnAndroid.tbIconCheckedMouseLeave = Nothing
        Me.btnAndroid.tbIconHoldPlace = True
        Me.btnAndroid.tbIconImage = Nothing
        Me.btnAndroid.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAndroid.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnAndroid.tbIconMore = False
        Me.btnAndroid.tbIconMoreImage = Nothing
        Me.btnAndroid.tbIconPlaceText = 5
        Me.btnAndroid.tbIconReadOnly = Nothing
        Me.btnAndroid.tbIconReadOnlyLight = Nothing
        Me.btnAndroid.tbIconUnCheckedMouseDown = Nothing
        Me.btnAndroid.tbIconUnCheckedMouseHover = Nothing
        Me.btnAndroid.tbIconUnCheckedMouseLeave = Nothing
        Me.btnAndroid.tbImageBackground = Nothing
        Me.btnAndroid.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnAndroid.tbImageCheckedMouseDown = Nothing
        Me.btnAndroid.tbImageCheckedMouseHover = Nothing
        Me.btnAndroid.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnAndroid.tbImageUnCheckedMouseDown = Nothing
        Me.btnAndroid.tbImageUnCheckedMouseHover = Nothing
        Me.btnAndroid.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnAndroid.tbNoShowBadgeNumberImage = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnAndroid.tbReadOnly = False
        Me.btnAndroid.tbShadow = False
        Me.btnAndroid.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAndroid.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAndroid.tbShowBadgeNumber = True
        Me.btnAndroid.tbShowBadgeNumberWhenSelection = True
        Me.btnAndroid.tbShowNew = False
        Me.btnAndroid.tbSplit = "3,3,3,3"
        Me.btnAndroid.tbTag = Nothing
        Me.btnAndroid.tbToolTip = ""
        Me.btnAndroid.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnAndroid.Text = "Android"
        Me.btnAndroid.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAndroid.UseVisualStyleBackColor = True
        '
        'btnIPhone
        '
        Me.btnIPhone.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnIPhone.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnIPhone.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnIPhone.ForeColor_Checked = System.Drawing.Color.White
        Me.btnIPhone.Location = New System.Drawing.Point(593, 5)
        Me.btnIPhone.Name = "btnIPhone"
        Me.btnIPhone.Size = New System.Drawing.Size(60, 22)
        Me.btnIPhone.TabIndex = 21
        Me.btnIPhone.TabStop = True
        Me.btnIPhone.tbAdriftIconWhenHover = False
        Me.btnIPhone.tbAutoSize = False
        Me.btnIPhone.tbAutoSizeEx = False
        Me.btnIPhone.tbBadgeNumber = 0
        Me.btnIPhone.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnIPhone.tbEnableToolTip = False
        Me.btnIPhone.tbIconCheckedMouseDown = Nothing
        Me.btnIPhone.tbIconCheckedMouseHover = Nothing
        Me.btnIPhone.tbIconCheckedMouseLeave = Nothing
        Me.btnIPhone.tbIconHoldPlace = True
        Me.btnIPhone.tbIconImage = Nothing
        Me.btnIPhone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnIPhone.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnIPhone.tbIconMore = False
        Me.btnIPhone.tbIconMoreImage = CType(resources.GetObject("btnIPhone.tbIconMoreImage"), System.Drawing.Image)
        Me.btnIPhone.tbIconPlaceText = 5
        Me.btnIPhone.tbIconReadOnly = Nothing
        Me.btnIPhone.tbIconReadOnlyLight = Nothing
        Me.btnIPhone.tbIconUnCheckedMouseDown = Nothing
        Me.btnIPhone.tbIconUnCheckedMouseHover = Nothing
        Me.btnIPhone.tbIconUnCheckedMouseLeave = Nothing
        Me.btnIPhone.tbImageBackground = Nothing
        Me.btnIPhone.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnIPhone.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnIPhone.tbImageCheckedMouseHover = Nothing
        Me.btnIPhone.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnIPhone.tbImageUnCheckedMouseDown = Nothing
        Me.btnIPhone.tbImageUnCheckedMouseHover = Nothing
        Me.btnIPhone.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnIPhone.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnIPhone.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnIPhone.tbReadOnly = False
        Me.btnIPhone.tbShadow = False
        Me.btnIPhone.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnIPhone.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnIPhone.tbShowBadgeNumber = True
        Me.btnIPhone.tbShowBadgeNumberWhenSelection = True
        Me.btnIPhone.tbShowNew = False
        Me.btnIPhone.tbSplit = "3,3,3,3"
        Me.btnIPhone.tbTag = Nothing
        Me.btnIPhone.tbToolTip = ""
        Me.btnIPhone.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnIPhone.Text = "iOS"
        Me.btnIPhone.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnIPhone.UseVisualStyleBackColor = True
        '
        'btnOpen
        '
        Me.btnOpen.BackColor = System.Drawing.Color.Transparent
        Me.btnOpen.BindingForm = Nothing
        Me.btnOpen.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpen.Location = New System.Drawing.Point(206, 31)
        Me.btnOpen.Name = "btnOpen"
        Me.btnOpen.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOpen.Selectable = True
        Me.btnOpen.Size = New System.Drawing.Size(75, 23)
        Me.btnOpen.TabIndex = 20
        Me.btnOpen.TabStop = False
        Me.btnOpen.tbAdriftIconWhenHover = False
        Me.btnOpen.tbAutoSize = False
        Me.btnOpen.tbAutoSizeEx = True
        Me.btnOpen.tbBackgroundImage = Nothing
        Me.btnOpen.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnOpen.tbBadgeNumber = 0
        Me.btnOpen.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpen.tbEndEllipsis = False
        Me.btnOpen.tbIconHoldPlace = True
        Me.btnOpen.tbIconImage = Nothing
        Me.btnOpen.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOpen.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpen.tbIconMore = False
        Me.btnOpen.tbIconMouseDown = Nothing
        Me.btnOpen.tbIconMouseHover = Nothing
        Me.btnOpen.tbIconMouseLeave = Nothing
        Me.btnOpen.tbIconPlaceText = 2
        Me.btnOpen.tbIconReadOnly = Nothing
        Me.btnOpen.tbImageMouseDown = Nothing
        Me.btnOpen.tbImageMouseHover = Nothing
        Me.btnOpen.tbImageMouseLeave = Nothing
        Me.btnOpen.tbProgressValue = 50
        Me.btnOpen.tbReadOnly = False
        Me.btnOpen.tbReadOnlyText = False
        Me.btnOpen.tbShadow = False
        Me.btnOpen.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOpen.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOpen.tbShowDot = False
        Me.btnOpen.tbShowMoreIconImg = CType(resources.GetObject("btnOpen.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOpen.tbShowNew = False
        Me.btnOpen.tbShowProgress = False
        Me.btnOpen.tbShowTip = True
        Me.btnOpen.tbShowToolTipOnButton = False
        Me.btnOpen.tbSplit = "3,3,3,3"
        Me.btnOpen.tbText = "打开文件夹"
        Me.btnOpen.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOpen.tbTextColor = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorDown = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorHover = System.Drawing.Color.Black
        Me.btnOpen.tbTextMouseDownPlace = 0
        Me.btnOpen.tbToolTip = ""
        Me.btnOpen.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpen.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpen.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpen.Visible = False
        Me.btnOpen.VisibleEx = True
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.ForeColor = System.Drawing.SystemColors.WindowText
        Me.txtSearch.Location = New System.Drawing.Point(724, 5)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "过滤文件名"
        Me.txtSearch.ShowClear = True
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(160, 23)
        Me.txtSearch.TabIndex = 0
        Me.txtSearch.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.7R
        '
        'pnlBottom
        '
        Me.pnlBottom.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlBottom.Controls.Add(Me.pnlFileEmpty)
        Me.pnlBottom.Controls.Add(Me.pnlLoading)
        Me.pnlBottom.Location = New System.Drawing.Point(1, 31)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(892, 584)
        Me.pnlBottom.TabIndex = 24
        '
        'pnlFileEmpty
        '
        Me.pnlFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlFileEmpty.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFileEmpty.Controls.Add(Me.btnGotoDownload)
        Me.pnlFileEmpty.Controls.Add(Me.picFileEmpty)
        Me.pnlFileEmpty.Controls.Add(Me.lblFileEmpty)
        Me.pnlFileEmpty.Location = New System.Drawing.Point(269, 219)
        Me.pnlFileEmpty.Name = "pnlFileEmpty"
        Me.pnlFileEmpty.Size = New System.Drawing.Size(354, 147)
        Me.pnlFileEmpty.TabIndex = 30
        Me.pnlFileEmpty.Visible = False
        '
        'btnGotoDownload
        '
        Me.btnGotoDownload.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnGotoDownload.BackColor = System.Drawing.Color.Transparent
        Me.btnGotoDownload.BindingForm = Nothing
        Me.btnGotoDownload.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnGotoDownload.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnGotoDownload.Location = New System.Drawing.Point(187, 120)
        Me.btnGotoDownload.Margin = New System.Windows.Forms.Padding(0)
        Me.btnGotoDownload.Name = "btnGotoDownload"
        Me.btnGotoDownload.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnGotoDownload.Selectable = True
        Me.btnGotoDownload.Size = New System.Drawing.Size(66, 23)
        Me.btnGotoDownload.TabIndex = 25
        Me.btnGotoDownload.tbAdriftIconWhenHover = False
        Me.btnGotoDownload.tbAutoSize = True
        Me.btnGotoDownload.tbAutoSizeEx = True
        Me.btnGotoDownload.tbBackgroundImage = Nothing
        Me.btnGotoDownload.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnGotoDownload.tbBadgeNumber = 0
        Me.btnGotoDownload.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGotoDownload.tbEndEllipsis = False
        Me.btnGotoDownload.tbIconHoldPlace = True
        Me.btnGotoDownload.tbIconImage = Nothing
        Me.btnGotoDownload.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoDownload.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGotoDownload.tbIconMore = False
        Me.btnGotoDownload.tbIconMouseDown = Nothing
        Me.btnGotoDownload.tbIconMouseHover = Nothing
        Me.btnGotoDownload.tbIconMouseLeave = Nothing
        Me.btnGotoDownload.tbIconPlaceText = 0
        Me.btnGotoDownload.tbIconReadOnly = Nothing
        Me.btnGotoDownload.tbImageMouseDown = Nothing
        Me.btnGotoDownload.tbImageMouseHover = Nothing
        Me.btnGotoDownload.tbImageMouseLeave = Nothing
        Me.btnGotoDownload.tbProgressValue = 50
        Me.btnGotoDownload.tbReadOnly = False
        Me.btnGotoDownload.tbReadOnlyText = False
        Me.btnGotoDownload.tbShadow = False
        Me.btnGotoDownload.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGotoDownload.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGotoDownload.tbShowDot = False
        Me.btnGotoDownload.tbShowMoreIconImg = CType(resources.GetObject("btnGotoDownload.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGotoDownload.tbShowNew = False
        Me.btnGotoDownload.tbShowProgress = False
        Me.btnGotoDownload.tbShowTip = True
        Me.btnGotoDownload.tbShowToolTipOnButton = False
        Me.btnGotoDownload.tbSplit = "3,3,3,3"
        Me.btnGotoDownload.tbText = "去逛逛"
        Me.btnGotoDownload.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoDownload.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.btnGotoDownload.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.btnGotoDownload.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.btnGotoDownload.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.btnGotoDownload.tbTextMouseDownPlace = 0
        Me.btnGotoDownload.tbToolTip = ""
        Me.btnGotoDownload.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGotoDownload.tbToolTipFont = New System.Drawing.Font("宋体", 7.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnGotoDownload.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoDownload.VisibleEx = True
        '
        'picFileEmpty
        '
        Me.picFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picFileEmpty.Image = Global.iTong.My.Resources.Resources.icon_appnodata_100
        Me.picFileEmpty.Location = New System.Drawing.Point(126, 2)
        Me.picFileEmpty.Name = "picFileEmpty"
        Me.picFileEmpty.Size = New System.Drawing.Size(100, 100)
        Me.picFileEmpty.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picFileEmpty.TabIndex = 1
        Me.picFileEmpty.TabStop = False
        '
        'lblFileEmpty
        '
        Me.lblFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFileEmpty.AutoSize = True
        Me.lblFileEmpty.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFileEmpty.ForeColor = System.Drawing.Color.FromArgb(CType(CType(159, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(163, Byte), Integer))
        Me.lblFileEmpty.Location = New System.Drawing.Point(71, 123)
        Me.lblFileEmpty.Name = "lblFileEmpty"
        Me.lblFileEmpty.Size = New System.Drawing.Size(104, 16)
        Me.lblFileEmpty.TabIndex = 0
        Me.lblFileEmpty.tbAdriftWhenHover = False
        Me.lblFileEmpty.tbAutoEllipsis = False
        Me.lblFileEmpty.tbAutoSize = True
        Me.lblFileEmpty.tbHideImage = False
        Me.lblFileEmpty.tbIconImage = Nothing
        Me.lblFileEmpty.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileEmpty.tbIconPlaceText = 5
        Me.lblFileEmpty.tbShadow = False
        Me.lblFileEmpty.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileEmpty.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileEmpty.tbShowScrolling = False
        Me.lblFileEmpty.Text = "这里没有内容"
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(236, 11)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(319, 600)
        Me.pnlLoading.TabIndex = 27
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(145, 278)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(120, 45)
        Me.lblLoading.TabIndex = 10
        Me.lblLoading.Text = "正在加载..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_24
        Me.pbLoading.Location = New System.Drawing.Point(108, 289)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(24, 24)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 0
        Me.pbLoading.TabStop = False
        '
        'bgwLoadData
        '
        '
        'pnlProgress
        '
        Me.pnlProgress.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlProgress.BackColor = System.Drawing.Color.FromArgb(CType(CType(205, Byte), Integer), CType(CType(215, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.pnlProgress.Controls.Add(Me.pgbProgress)
        Me.pnlProgress.Controls.Add(Me.lblCurrentFile)
        Me.pnlProgress.Controls.Add(Me.PictureBox1)
        Me.pnlProgress.Controls.Add(Me.btnAbrot)
        Me.pnlProgress.Location = New System.Drawing.Point(1, 615)
        Me.pnlProgress.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlProgress.Name = "pnlProgress"
        Me.pnlProgress.Size = New System.Drawing.Size(891, 27)
        Me.pnlProgress.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlProgress.TabIndex = 25
        Me.pnlProgress.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_status
        Me.pnlProgress.tbShowWatermark = False
        Me.pnlProgress.tbSplit = "3,3,3,3"
        Me.pnlProgress.tbWatermark = Nothing
        Me.pnlProgress.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlProgress.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pgbProgress
        '
        Me.pgbProgress.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.pgbProgress.BackColor = System.Drawing.Color.Transparent
        Me.pgbProgress.Font = New System.Drawing.Font("宋体", 8.0!)
        Me.pgbProgress.Location = New System.Drawing.Point(760, 8)
        Me.pgbProgress.Name = "pgbProgress"
        Me.pgbProgress.Size = New System.Drawing.Size(100, 11)
        Me.pgbProgress.TabIndex = 14
        Me.pgbProgress.tbBackgroundImage = CType(resources.GetObject("pgbProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.pgbProgress.tbCanDragValue = False
        Me.pgbProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pgbProgress.tbDotImage = CType(resources.GetObject("pgbProgress.tbDotImage"), System.Drawing.Image)
        Me.pgbProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pgbProgress.tbDotMouseDown = Nothing
        Me.pgbProgress.tbDotMouseHover = Nothing
        Me.pgbProgress.tbDotMouseLeave = Nothing
        Me.pgbProgress.tbDownloadImage = CType(resources.GetObject("pgbProgress.tbDownloadImage"), System.Drawing.Image)
        Me.pgbProgress.tbDownMax = 100
        Me.pgbProgress.tbDownMin = 0
        Me.pgbProgress.tbDownValue = 0
        Me.pgbProgress.tbIsWaiting = False
        Me.pgbProgress.tbPlayImage = Global.iTong.My.Resources.Resources.app_downloads_value
        Me.pgbProgress.tbPlayMax = 100
        Me.pgbProgress.tbPlayMin = 0
        Me.pgbProgress.tbPlayValue = 50
        Me.pgbProgress.tbShowDot = False
        Me.pgbProgress.tbShowText = True
        Me.pgbProgress.tbSplit = "5,0,6,0"
        Me.pgbProgress.Text = "TbProgressBar1"
        Me.pgbProgress.Visible = False
        '
        'lblCurrentFile
        '
        Me.lblCurrentFile.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblCurrentFile.AutoEllipsis = True
        Me.lblCurrentFile.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblCurrentFile.Location = New System.Drawing.Point(36, 4)
        Me.lblCurrentFile.Name = "lblCurrentFile"
        Me.lblCurrentFile.Size = New System.Drawing.Size(649, 21)
        Me.lblCurrentFile.TabIndex = 13
        Me.lblCurrentFile.tbAdriftWhenHover = False
        Me.lblCurrentFile.tbAutoEllipsis = True
        Me.lblCurrentFile.tbAutoSize = False
        Me.lblCurrentFile.tbHideImage = False
        Me.lblCurrentFile.tbIconImage = Nothing
        Me.lblCurrentFile.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCurrentFile.tbIconPlaceText = 5
        Me.lblCurrentFile.tbShadow = False
        Me.lblCurrentFile.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCurrentFile.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCurrentFile.tbShowScrolling = False
        Me.lblCurrentFile.Text = "正在同步媒体，请勿断开设备"
        Me.lblCurrentFile.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.gif_loading_14
        Me.PictureBox1.Location = New System.Drawing.Point(7, 8)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(14, 14)
        Me.PictureBox1.TabIndex = 21
        Me.PictureBox1.TabStop = False
        '
        'btnAbrot
        '
        Me.btnAbrot.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnAbrot.BackColor = System.Drawing.Color.Transparent
        Me.btnAbrot.BindingForm = Nothing
        Me.btnAbrot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAbrot.Location = New System.Drawing.Point(865, 6)
        Me.btnAbrot.Name = "btnAbrot"
        Me.btnAbrot.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnAbrot.Selectable = True
        Me.btnAbrot.Size = New System.Drawing.Size(15, 15)
        Me.btnAbrot.TabIndex = 12
        Me.btnAbrot.tbAdriftIconWhenHover = False
        Me.btnAbrot.tbAutoSize = False
        Me.btnAbrot.tbAutoSizeEx = True
        Me.btnAbrot.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_cancel
        Me.btnAbrot.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnAbrot.tbBadgeNumber = 0
        Me.btnAbrot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAbrot.tbEndEllipsis = False
        Me.btnAbrot.tbIconHoldPlace = True
        Me.btnAbrot.tbIconImage = Nothing
        Me.btnAbrot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAbrot.tbIconMore = False
        Me.btnAbrot.tbIconMouseDown = Nothing
        Me.btnAbrot.tbIconMouseHover = Nothing
        Me.btnAbrot.tbIconMouseLeave = Nothing
        Me.btnAbrot.tbIconPlaceText = 2
        Me.btnAbrot.tbIconReadOnly = Nothing
        Me.btnAbrot.tbImageMouseDown = Nothing
        Me.btnAbrot.tbImageMouseHover = Nothing
        Me.btnAbrot.tbImageMouseLeave = Nothing
        Me.btnAbrot.tbProgressValue = 50
        Me.btnAbrot.tbReadOnly = False
        Me.btnAbrot.tbReadOnlyText = False
        Me.btnAbrot.tbShadow = False
        Me.btnAbrot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAbrot.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAbrot.tbShowDot = False
        Me.btnAbrot.tbShowMoreIconImg = CType(resources.GetObject("btnAbrot.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAbrot.tbShowNew = False
        Me.btnAbrot.tbShowProgress = False
        Me.btnAbrot.tbShowTip = True
        Me.btnAbrot.tbShowToolTipOnButton = False
        Me.btnAbrot.tbSplit = "0,0,0,0"
        Me.btnAbrot.tbText = ""
        Me.btnAbrot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbTextColor = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDisable = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDown = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorHover = System.Drawing.Color.White
        Me.btnAbrot.tbTextMouseDownPlace = 0
        Me.btnAbrot.tbToolTip = ""
        Me.btnAbrot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAbrot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAbrot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.Visible = False
        Me.btnAbrot.VisibleEx = True
        '
        'tpDgvDetail
        '
        Me.tpDgvDetail.AutoPopDelay = 5000
        Me.tpDgvDetail.InitialDelay = 500
        Me.tpDgvDetail.OwnerDraw = True
        Me.tpDgvDetail.ReshowDelay = 800
        '
        'frmDownloaded
        '
        Me.BackColor = System.Drawing.Color.Silver
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(893, 642)
        Me.Controls.Add(Me.pnlProgress)
        Me.Controls.Add(Me.pnlBottom)
        Me.Controls.Add(Me.pnlTop)
        Me.Name = "frmDownloaded"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "Download"
        Me.Controls.SetChildIndex(Me.pnlTop, 0)
        Me.Controls.SetChildIndex(Me.pnlBottom, 0)
        Me.Controls.SetChildIndex(Me.pnlProgress, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.menuDownload.ResumeLayout(False)
        Me.pnlTop.ResumeLayout(False)
        Me.pnlBottom.ResumeLayout(False)
        Me.pnlFileEmpty.ResumeLayout(False)
        Me.pnlFileEmpty.PerformLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlProgress.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents menuDownload As tbContextMenuStrip
    Friend WithEvents tsmiOpenExplorer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDeleteItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents btnOpen As tbButton
    Friend WithEvents txtSearch As tbSearch
    Friend WithEvents pnlBottom As System.Windows.Forms.Panel
    Friend WithEvents btnAndroid As tbRadioButton
    Friend WithEvents btnIPhone As tbRadioButton
    Friend WithEvents btnInstall As tbButton
    Friend WithEvents btnDelete As tbButton
    Friend WithEvents btnImport As tbButton
    Friend WithEvents bgwLoadData As System.ComponentModel.BackgroundWorker
    Friend WithEvents pnlProgress As tbPanel
    Protected WithEvents pgbProgress As tbControlBar
    Protected WithEvents lblCurrentFile As tbLabel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Protected WithEvents btnAbrot As tbButton
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents tpDgvDetail As tbToolTip
    Friend WithEvents ToolStripSeparator1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiInstallItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlFileEmpty As System.Windows.Forms.Panel
    Friend WithEvents picFileEmpty As System.Windows.Forms.PictureBox
    Friend WithEvents lblFileEmpty As tbLabel
    Friend WithEvents btnGotoDownload As iTong.Components.tbButton
    Friend WithEvents btnRefresh As iTong.Components.tbButton

End Class
