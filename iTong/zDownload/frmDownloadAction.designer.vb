﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDownloadAction
    Inherits tbBaseGuiForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Me.menuDownload = New tbContextMenuStrip(Me.components)
        Me.tsmiOpenExplorer = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiDeleteItem = New System.Windows.Forms.ToolStripMenuItem
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.pnlTop = New tbPanel
        Me.btnOpen = New tbButton
        Me.txtSearch = New tbSearch
        Me.pnlBottom = New System.Windows.Forms.Panel
        Me.btnDelete = New tbButton
        Me.menuDownload.SuspendLayout()
        Me.pnlTop.SuspendLayout()
        Me.SuspendLayout()
        '
        'menuDownload
        '
        Me.menuDownload.DropShadowEnabled = False
        Me.menuDownload.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenExplorer, Me.tsmiDeleteItem})
        Me.menuDownload.Name = "menuInInstalGrid"
        Me.menuDownload.Size = New System.Drawing.Size(137, 48)
        Me.menuDownload.tbBackColor = System.Drawing.Color.White
        Me.menuDownload.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuDownload.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuDownload.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenExplorer
        '
        Me.tsmiOpenExplorer.Name = "tsmiOpenExplorer"
        Me.tsmiOpenExplorer.Size = New System.Drawing.Size(136, 22)
        Me.tsmiOpenExplorer.Text = "打开文件夹"
        '
        'tsmiDeleteItem
        '
        Me.tsmiDeleteItem.Name = "tsmiDeleteItem"
        Me.tsmiDeleteItem.Size = New System.Drawing.Size(136, 22)
        Me.tsmiDeleteItem.Text = "删除"
        '
        'pnlTop
        '
        Me.pnlTop.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.btnDelete)
        Me.pnlTop.Controls.Add(Me.btnOpen)
        Me.pnlTop.Controls.Add(Me.txtSearch)
        Me.pnlTop.Location = New System.Drawing.Point(1, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(891, 32)
        Me.pnlTop.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 23
        Me.pnlTop.tbBackgroundImage = My.Resources.pnl_bg_navigation
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnOpen
        '
        Me.btnOpen.BackColor = System.Drawing.Color.Transparent
        Me.btnOpen.BindingForm = Nothing
        Me.btnOpen.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpen.Location = New System.Drawing.Point(352, 4)
        Me.btnOpen.Name = "btnOpen"
        Me.btnOpen.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOpen.Selectable = True
        Me.btnOpen.Size = New System.Drawing.Size(75, 23)
        Me.btnOpen.TabIndex = 20
        Me.btnOpen.TabStop = False
        Me.btnOpen.tbAdriftIconWhenHover = False
        Me.btnOpen.tbAutoSize = False
        Me.btnOpen.tbAutoSizeEx = True
        Me.btnOpen.tbBackgroundImage = Nothing
        Me.btnOpen.tbBackgroundImageState = ImageState.OneState
        Me.btnOpen.tbBadgeNumber = 0
        Me.btnOpen.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpen.tbEndEllipsis = False
        Me.btnOpen.tbIconHoldPlace = True
        Me.btnOpen.tbIconImage = Nothing
        Me.btnOpen.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOpen.tbIconImageState = ImageState.OneState
        Me.btnOpen.tbIconMore = False
        Me.btnOpen.tbIconMouseDown = Nothing
        Me.btnOpen.tbIconMouseHover = Nothing
        Me.btnOpen.tbIconMouseLeave = Nothing
        Me.btnOpen.tbIconPlaceText = 2
        Me.btnOpen.tbIconReadOnly = Nothing
        Me.btnOpen.tbImageMouseDown = Nothing
        Me.btnOpen.tbImageMouseHover = Nothing
        Me.btnOpen.tbImageMouseLeave = Nothing
        Me.btnOpen.tbReadOnly = False
        Me.btnOpen.tbReadOnlyText = False
        Me.btnOpen.tbShadow = False
        Me.btnOpen.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOpen.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOpen.tbShowNew = False
        Me.btnOpen.tbShowToolTipOnButton = False
        Me.btnOpen.tbSplit = "3,3,3,3"
        Me.btnOpen.tbText = "打开文件夹"
        Me.btnOpen.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOpen.tbTextColor = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorDown = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorHover = System.Drawing.Color.Black
        Me.btnOpen.tbTextMouseDownPlace = 0
        Me.btnOpen.tbToolTip = ""
        Me.btnOpen.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpen.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpen.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpen.Visible = False
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.ForeColor = System.Drawing.SystemColors.WindowText
        Me.txtSearch.Location = New System.Drawing.Point(659, 5)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "过滤文件名"
        Me.txtSearch.ShowClear = True
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(229, 23)
        Me.txtSearch.TabIndex = 0
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.7
        '
        'pnlBottom
        '
        Me.pnlBottom.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlBottom.Location = New System.Drawing.Point(1, 31)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(892, 611)
        Me.pnlBottom.TabIndex = 24
        '
        'btnDelete
        '
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(11, 4)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(59, 23)
        Me.btnDelete.TabIndex = 26
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = My.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "13,11,13,11"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'frmDownloadAction
        '
        Me.BackColor = System.Drawing.Color.Silver
        Me.ClientSize = New System.Drawing.Size(893, 642)
        Me.Controls.Add(Me.pnlBottom)
        Me.Controls.Add(Me.pnlTop)
        Me.Name = "frmDownloadAction"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "Download"
        Me.menuDownload.ResumeLayout(False)
        Me.pnlTop.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents menuDownload As tbContextMenuStrip
    Friend WithEvents tsmiOpenExplorer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDeleteItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents btnOpen As tbButton
    Friend WithEvents txtSearch As tbSearch
    Friend WithEvents pnlBottom As System.Windows.Forms.Panel
    Friend WithEvents btnDelete As tbButton

End Class
