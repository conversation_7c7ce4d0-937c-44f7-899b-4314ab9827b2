﻿Imports System.Xml
Imports System.Text
Imports System.IO
Imports System.Data
Imports iTong.CoreFoundation
Imports iTong.Components


Public Class frmDownloadAction
    Private mIconSize As New Size(40, 40)
    Const MYTASK As String = "MyTask"
    Const DOWNLOADING As String = "Downloading"
    Const DOWNLOADDED As String = "Downloadded"

    Friend WithEvents dgvTask As New tbDataGridViewEx
    Private mDictFiles As New Dictionary(Of String, InstallArgs)

#Region "--- 初始化 ---"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong

        Me.Name = ""
        Me.FunctionMappingKey = FunctionKey.Download
        Me.FilletRadius = 5
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        InitInstallCenter()
        InitDataGridView()
        Me.LoadData()

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")
        Me.tsmiDeleteItem.Text = Me.Language.GetString("Common.Delete")
        Me.tsmiOpenExplorer.Text = Me.Language.GetString("App.Button.ShowInFile")
    End Sub

    Private Sub InitDataGridView()
        With dgvTask
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.Font = Common.CreateFont("Arial", 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black

            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "ColName", Me.Language.GetString("Common.Label.Name"), _
                                                                                               210, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            colName.DefaultCellStyle = cellStyle

            .ContextMenuStrip = Me.menuDownload

            .SuspendLayout()

            .RowTemplate.Height = 58
            .ScrollBars = System.Windows.Forms.ScrollBars.Both

            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", _
                                                                    26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(colName)
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), _
                                                                     80, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colResult", Me.Language.GetString("Download.Label.Progress"), _
                                                                    200, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))             '"进度"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colDevice", Me.Language.GetString("Download.Label.Operate"), _
                                                                    160, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))             '"操作"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 100.0!, True, True, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))

            Me.Controls.Add(dgvTask)
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .ResumeLayout()
            .tbNoDataImage = My.Resources.icon_downnodata_100
        End With
        Utility.AddForm2Panel(Me.dgvTask, Me.pnlBottom)
    End Sub

    Private Sub InitInstallCenter()
        'AddHandler InstallHelperCenter.Instance.InstallEvent, AddressOf OnInstallEvent
    End Sub

#End Region

    Private Sub btn_Visible_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.Hide()
    End Sub
    Private Delegate Sub OnInstallEventHandler(ByVal sender As Object, ByVal args As InstallArgs)
    Private Sub OnInstallEvent(ByVal sender As Object, ByVal args As InstallArgs)
        'Try
        '    If Me.IsHandleCreated AndAlso Me.InvokeRequired Then
        '        Me.Invoke(New OnInstallEventHandler(AddressOf OnInstallEvent), New Object() {sender, args})
        '    Else
        '        ' 只监听安装完成的事件
        '        If args.IsProcessing Then
        '            Exit Sub
        '        End If

        '        If Not mDictFiles.ContainsKey(args.InstallID) Then
        '            AddCompleteInstallTask(args)
        '            mDictFiles(args.InstallID) = args
        '            UpdateParentCount()

        '            Dim strMessage As String = String.Format("{0}{1}", args.PackageInfo.Name, GetInstallStateString(args.InstallState))
        '            tbSplashBox.ShowMessage(strMessage, frmDownloadCenter.Instance)
        '        End If

        '    End If
        'Catch ex As Exception

        'End Try

    End Sub


    Private Sub UpdateParentCount()
        If frmDownloadCenter.Instance IsNot Nothing Then
            frmDownloadCenter.Instance.UpdateActionCount(Me.mDictFiles.Count)
        End If
    End Sub

    Private Sub LoadData()
        'For Each args As InstallArgs In InstallHelperCenter.Instance.GetCompleteTasks
        '    AddCompleteInstallTask(args)
        '    Me.mDictFiles(args.InstallID) = args
        'Next
        'Me.UpdateParentCount()
    End Sub

    Private Sub AddCompleteInstallTask(ByVal args As InstallArgs)
        'Dim iIndex As Integer = Me.dgvTask.Rows.Add(False, args.PackageInfo.Name, Utility.FormatFileSize(args.PackageInfo.FileSize), GetInstallStateString(args.InstallState), args.Device.DeviceName)
        'Dim row As DataGridViewRow = Me.dgvTask.Rows(iIndex)

        'Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")

        'cellName.tbIconSize = Me.mIconSize
        'cellName.tbIconShadow = True

        'Dim imageIcon As Image = args.PackageInfo.AppIcon
        'If imageIcon IsNot Nothing Then
        '    cellName.tbIcon = imageIcon.Clone
        '    imageIcon.Dispose()
        '    imageIcon = Nothing
        'Else
        '    cellName.tbIcon = My.Resources.app_icon_default
        'End If
        'row.Tag = args
    End Sub
    

    Private Function GetInstallStateString(ByVal stat As InstallState) As String
        Dim res As String = Me.Language.GetString("App.Cell.InstallSucceed")            '"安装成功"

        Select Case stat
            Case InstallState.Succeed
                res = Me.Language.GetString("App.Cell.InstallSucceed")                  '"安装成功"
            Case InstallState.UnSucceed
                res = Me.Language.GetString("PkgInstaller.Label.InstallFail")           '"安装失败"
            Case InstallState.CancelInstall
                res = Me.Language.GetString("Common.Cancel")                            '"取消"
            Case InstallState.AlreadyInstall
                res = Me.Language.GetString("App.Cell.Installed")                       '"已安装"

        End Select
        
        Return res
    End Function


    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click
        'If Me.dgvTask.SelectedRows.Count = 0 Then
        '    tbSplashBox.ShowMessage("未选中任何项", frmDownloadCenter.Instance)
        '    Return
        'End If

        'If tbMessageBox.Show(Me, "确定删除选中的任务？", "警告", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) <> Windows.Forms.DialogResult.OK Then
        '    Return
        'End If

        'Dim listRows As New List(Of DataGridViewRow)
        'listRows.AddRange(Me.dgvTask.SelectedRows)
        'For Each row As DataGridViewRow In listRows
        '    Dim args As InstallArgs = CType(row.Tag, InstallArgs)
        '    Me.mDictFiles.Remove(args.InstallID)
        '    dgvTask.Rows.Remove(row)
        'Next
        'tbSplashBox.ShowMessage("删除完成", frmDownloadCenter.Instance)
        'Me.UpdateParentCount()
    End Sub

    Private Sub txtSearch_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChangedByTimer
        Dim strText As String = Me.txtSearch.Text.ToUpper()
        If String.IsNullOrEmpty(strText) Then
            ShowAllRows()
            Return
        End If

        For Each row As DataGridViewRow In Me.dgvTask.Rows
            Dim args As InstallArgs = CType(row.Tag, InstallArgs)
            Dim strPinyin As String = PinYinClass.MakePinYin(args.PackageInfo.Name, PinYinOptions.FirstCharacterOnly)

            If strPinyin.Contains(strText) Then
                row.Visible = True
            Else
                row.Visible = False
            End If
        Next
    End Sub


    Private Sub txtSearch_ClearClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.ClearClick
        ShowAllRows()
    End Sub


    Private Sub ShowAllRows()
        For Each row As DataGridViewRow In Me.dgvTask.Rows
            row.Visible = True
        Next
    End Sub
End Class