﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDownloadCenter
    Inherits frmDeviceBase

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDownloadCenter))
        Me.menuDownload = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpenExplorer = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiDeleteItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.btn_Visible = New iTong.Components.tbButton()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.pnlLeft = New System.Windows.Forms.Panel()
        Me.pnlRight = New System.Windows.Forms.Panel()
        Me.btwLoadCount = New System.ComponentModel.BackgroundWorker()
        Me.tvwDownload = New iTong.Components.tbTreeView()
        Me.menuDownload.SuspendLayout()
        Me.pnlLeft.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(937, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(913, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(889, 0)
        '
        'menuDownload
        '
        Me.menuDownload.DropShadowEnabled = False
        Me.menuDownload.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenExplorer, Me.tsmiDeleteItem})
        Me.menuDownload.Name = "menuInInstalGrid"
        Me.menuDownload.Size = New System.Drawing.Size(137, 48)
        Me.menuDownload.tbBackColor = System.Drawing.Color.White
        Me.menuDownload.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuDownload.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuDownload.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenExplorer
        '
        Me.tsmiOpenExplorer.Name = "tsmiOpenExplorer"
        Me.tsmiOpenExplorer.Size = New System.Drawing.Size(136, 22)
        Me.tsmiOpenExplorer.Text = "打开文件夹"
        '
        'tsmiDeleteItem
        '
        Me.tsmiDeleteItem.Name = "tsmiDeleteItem"
        Me.tsmiDeleteItem.Size = New System.Drawing.Size(136, 22)
        Me.tsmiDeleteItem.Text = "删除"
        '
        'btn_Visible
        '
        Me.btn_Visible.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Visible.BackColor = System.Drawing.Color.Transparent
        Me.btn_Visible.BindingForm = Nothing
        Me.btn_Visible.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_Visible.Location = New System.Drawing.Point(934, 2)
        Me.btn_Visible.Name = "btn_Visible"
        Me.btn_Visible.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Visible.Selectable = True
        Me.btn_Visible.Size = New System.Drawing.Size(24, 24)
        Me.btn_Visible.TabIndex = 22
        Me.btn_Visible.tbAdriftIconWhenHover = False
        Me.btn_Visible.tbAutoSize = False
        Me.btn_Visible.tbAutoSizeEx = False
        Me.btn_Visible.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_Visible.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_Visible.tbBadgeNumber = 0
        Me.btn_Visible.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Visible.tbEndEllipsis = False
        Me.btn_Visible.tbIconHoldPlace = True
        Me.btn_Visible.tbIconImage = Nothing
        Me.btn_Visible.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_Visible.tbIconMore = False
        Me.btn_Visible.tbIconMouseDown = Nothing
        Me.btn_Visible.tbIconMouseHover = Nothing
        Me.btn_Visible.tbIconMouseLeave = Nothing
        Me.btn_Visible.tbIconPlaceText = 2
        Me.btn_Visible.tbIconReadOnly = Nothing
        Me.btn_Visible.tbImageMouseDown = Nothing
        Me.btn_Visible.tbImageMouseHover = Nothing
        Me.btn_Visible.tbImageMouseLeave = Nothing
        Me.btn_Visible.tbProgressValue = 50
        Me.btn_Visible.tbReadOnly = False
        Me.btn_Visible.tbReadOnlyText = False
        Me.btn_Visible.tbShadow = False
        Me.btn_Visible.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Visible.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Visible.tbShowDot = False
        Me.btn_Visible.tbShowMoreIconImg = CType(resources.GetObject("btn_Visible.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_Visible.tbShowNew = False
        Me.btn_Visible.tbShowProgress = False
        Me.btn_Visible.tbShowTip = True
        Me.btn_Visible.tbShowToolTipOnButton = False
        Me.btn_Visible.tbSplit = "3,3,3,3"
        Me.btn_Visible.tbText = ""
        Me.btn_Visible.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbTextColor = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDown = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorHover = System.Drawing.Color.White
        Me.btn_Visible.tbTextMouseDownPlace = 0
        Me.btn_Visible.tbToolTip = ""
        Me.btn_Visible.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Visible.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Visible.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.VisibleEx = True
        '
        'pnlLeft
        '
        Me.pnlLeft.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.pnlLeft.Controls.Add(Me.tvwDownload)
        Me.pnlLeft.Location = New System.Drawing.Point(2, 32)
        Me.pnlLeft.Name = "pnlLeft"
        Me.pnlLeft.Size = New System.Drawing.Size(165, 629)
        Me.pnlLeft.TabIndex = 23
        '
        'pnlRight
        '
        Me.pnlRight.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlRight.Location = New System.Drawing.Point(167, 32)
        Me.pnlRight.Name = "pnlRight"
        Me.pnlRight.Size = New System.Drawing.Size(793, 629)
        Me.pnlRight.TabIndex = 24
        '
        'btwLoadCount
        '
        '
        'tvwDownload
        '
        Me.tvwDownload.AutoScroll = True
        Me.tvwDownload.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tvwDownload.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tvwDownload.ImageList = Nothing
        Me.tvwDownload.Indent = 13
        Me.tvwDownload.ItemHeight = 28
        Me.tvwDownload.Location = New System.Drawing.Point(0, 0)
        Me.tvwDownload.Margin = New System.Windows.Forms.Padding(0)
        Me.tvwDownload.Name = "tvwDownload"
        Me.tvwDownload.Padding = New System.Windows.Forms.Padding(0, 0, 0, 10)
        Me.tvwDownload.PathSeparator = "\"
        Me.tvwDownload.SelectedNode = Nothing
        Me.tvwDownload.Size = New System.Drawing.Size(165, 629)
        Me.tvwDownload.TabIndex = 1
        Me.tvwDownload.TabStop = True
        Me.tvwDownload.tbBackgroundImage = Nothing
        Me.tvwDownload.tbNodeCountSelectedColor = System.Drawing.Color.White
        Me.tvwDownload.tbNodeDiff = 20
        Me.tvwDownload.tbNodeFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.tvwDownload.tbNodeForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.tvwDownload.tbNodeLabelFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.tvwDownload.tbNodeLabelForeColor = System.Drawing.Color.Black
        Me.tvwDownload.tbNodePadding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        'Me.tvwDownload.tbNodeSelectedBackground = Nothing
        Me.tvwDownload.tbNodeShadowColor = System.Drawing.Color.Transparent
        Me.tvwDownload.tbSplit = "1,27,3,3"
        Me.tvwDownload.WrapContents = False
        '
        'frmDownloadCenter
        '
        Me.BackColor = System.Drawing.Color.Silver
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(961, 688)
        Me.Controls.Add(Me.pnlRight)
        Me.Controls.Add(Me.pnlLeft)
        Me.Controls.Add(Me.btn_Visible)
        Me.MinimumSize = New System.Drawing.Size(20, 66)
        Me.Name = "frmDownloadCenter"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_state
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,33"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "下载管理"
        Me.Controls.SetChildIndex(Me.btn_Visible, 0)
        Me.Controls.SetChildIndex(Me.pnlLeft, 0)
        Me.Controls.SetChildIndex(Me.pnlRight, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.menuDownload.ResumeLayout(False)
        Me.pnlLeft.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents menuDownload As tbContextMenuStrip
    Friend WithEvents tsmiOpenExplorer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDeleteItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btn_Visible As tbButton
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents pnlLeft As System.Windows.Forms.Panel
    Friend WithEvents pnlRight As System.Windows.Forms.Panel
    Friend WithEvents btwLoadCount As System.ComponentModel.BackgroundWorker
    Public WithEvents tvwDownload As iTong.Components.tbTreeView

End Class
