﻿Public Class frmTip

    Private Const WS_EX_NOACTIVATE As Integer = &H8000000
    Private mHasInput As Boolean = False

    Public Sub New(ByVal strName As String, ByVal strInfo As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

        'Me.BackColor = Color.FromArgb(22, 22, 22)
        Me.CanbeMove = False
        Me.CanResize = False
        Me.ShowInTaskbar = False
        Me.PNGForm.ShowInTaskbar = False
        Me.TopLevel = True

        Me.SetInfo(strName, strInfo)
    End Sub

    Private Sub mTimerShow_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles mTimerShow.Tick
        Me.mTimerShow.Stop()

        If Me.Visible Then
            Me.Hide()
        End If
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

        Me.mTimerShow.Start()
    End Sub

    Public Sub SetInfo(ByVal strName As String, ByVal strInfo As String)
        Dim img As Image = My.Resources.frm_bg_Tip_down
        Dim bmp As New Bitmap(290, 60)
        Dim g As Graphics = Graphics.FromImage(bmp)
        g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        g.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        g.Clear(Color.FromArgb(0))

        GuiHelper.DrawImage(g, New Rectangle(0, 0, bmp.Width, bmp.Height), New tbSplitStructure(10, 3, 55, 12), img)

        Dim padding As New Padding(5, 3, 5, 12)
        Dim rectName As New Rectangle(padding.Left, padding.Top, bmp.Width - padding.Horizontal, (bmp.Height - padding.Vertical) \ 2)
        Dim rectInfo As New Rectangle(rectName.Left, rectName.Bottom, rectName.Width, rectName.Height)

        '绘越南文有问题
        'GuiHelper.DrawText(g, strName, Me.Font, rectName, Color.FromArgb(123, 93, 3), Color.Transparent, ContentAlignment.MiddleLeft, True, True)
        'GuiHelper.DrawText(g, strInfo, Me.Font, rectInfo, Color.FromArgb(123, 93, 3), Color.Transparent, ContentAlignment.MiddleLeft, True, True)

        GuiHelper.DrawString(g, strName, Me.Font, rectName, Color.FromArgb(123, 93, 3), ContentAlignment.MiddleLeft, True, True)
        GuiHelper.DrawString(g, strInfo, Me.Font, rectInfo, Color.FromArgb(123, 93, 3), ContentAlignment.MiddleLeft, True, True)

        g.Dispose()

        If Me.Size <> bmp.Size Then
            Me.Size = bmp.Size
        End If
        Me.PNGForm.SetPngBackground(bmp, 255)

        bmp.Dispose()
    End Sub

    Public Sub TimerEnalbed(ByVal blnEnabled As Boolean)
        If Me.mTimerShow.Enabled <> blnEnabled Then
            If blnEnabled Then
                Me.mTimerShow.Start()
            Else
                Me.mTimerShow.Stop()
            End If
        End If
    End Sub

    Private Shared mLocker As New Object
    Private Shared mTipForm As frmTip
    Public Shared Sub ShowTip(ByVal strName As String, ByVal strInfo As String, ByVal frmMain As Form)
        SyncLock mLocker
            If mTipForm Is Nothing Then
                mTipForm = New frmTip(strName, strInfo)
            Else
                mTipForm.SetInfo(strName, strInfo)
            End If

            If frmMain IsNot Nothing Then
                mTipForm.StartPosition = FormStartPosition.Manual
                mTipForm.Location = frmMain.PointToScreen(New Point(frmMain.Width - mTipForm.Width - 18, frmMain.Height - mTipForm.Height - 26 - 3))
            Else
                mTipForm.StartPosition = FormStartPosition.Manual

                Dim rect As Rectangle = Screen.PrimaryScreen.WorkingArea
                mTipForm.Location = New Point(rect.Right - mTipForm.Width - 3, rect.Bottom - mTipForm.Height)
                'Else
                'mTipForm.StartPosition = FormStartPosition.CenterScreen
            End If

            If mTipForm.Visible Then
                mTipForm.TimerEnalbed(False)
            End If

            mTipForm.TopMost = True
            mTipForm.Show()

            Debug.Print(mTipForm.Location.ToString() & vbTab & mTipForm.Size.ToString())
            mTipForm.TimerEnalbed(True)
        End SyncLock

    End Sub

    'Protected Overrides ReadOnly Property CreateParams() As CreateParams
    '    Get
    '        Dim cp As CreateParams = MyBase.CreateParams
    '        If Not mHasInput Then
    '            cp.ExStyle = cp.ExStyle Or WS_EX_NOACTIVATE
    '        End If
    '        Return cp
    '    End Get
    'End Property

End Class


Public Class frmTipInfo
    Inherits PNGControlForm

    Friend WithEvents mTimerShow As System.Windows.Forms.Timer

    Public Sub New(ByVal strInfo As String)
        Me.CanbeMove = False
        Me.CanResize = False
        Me.ShowInTaskbar = False
        Me.PNGForm.ShowInTaskbar = False
        Me.TopLevel = True

        mTimerShow = New System.Windows.Forms.Timer
        mTimerShow.Interval = 3000

        Me.SetInfo(strInfo)
    End Sub

    Private Sub mTimerShow_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles mTimerShow.Tick
        Me.mTimerShow.Stop()

        If Me.Visible Then
            Me.Hide()
        End If
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

        Me.mTimerShow.Start()
    End Sub

    Public Sub SetInfo(ByVal strInfo As String)
        Dim padding As New Padding(5, 10, 5, 3)
        Dim gTemp As Graphics = Me.CreateGraphics()
        Dim imgWidth As Integer = gTemp.MeasureString(strInfo, Me.Font).Width + padding.Horizontal + 1

        Dim img As Image = My.Resources.frm_bg_Tip
        Dim bmp As New Bitmap(imgWidth, img.Height)
        Dim g As Graphics = Graphics.FromImage(bmp)
        g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        g.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        g.Clear(Color.FromArgb(0))

        GuiHelper.DrawImage(g, New Rectangle(0, 0, bmp.Width, bmp.Height), New tbSplitStructure(55, 12, 10, 3), img)

        Dim rectInfo As New Rectangle(padding.Left, padding.Top, bmp.Width - padding.Horizontal, (bmp.Height - padding.Vertical))

        '绘越南文有问题
        'GuiHelper.DrawText(g, strInfo, Me.Font, rectInfo, Color.FromArgb(123, 93, 3), Color.Transparent, ContentAlignment.MiddleCenter)
        GuiHelper.DrawString(g, strInfo, Me.Font, rectInfo, Color.FromArgb(123, 93, 3), ContentAlignment.MiddleCenter, True, True)

        g.Dispose()
        gTemp.Dispose()

        If Me.Size <> bmp.Size Then
            Me.Size = bmp.Size
        End If
        Me.PNGForm.SetPngBackground(bmp, 255)

        bmp.Dispose()
    End Sub

    Public Sub TimerEnalbed(ByVal blnEnabled As Boolean)
        If Me.mTimerShow.Enabled <> blnEnabled Then
            If blnEnabled Then
                Me.mTimerShow.Start()
            Else
                Me.mTimerShow.Stop()
            End If
        End If
    End Sub

    Private Shared mLocker As New Object
    Private Shared mTipForm As frmTipInfo
    Public Shared Sub ShowTip(ByVal strInfo As String, ByVal ctl As Control, ByVal align As HorizontalAlignment, Optional ByVal diffY As Integer = 0)
        SyncLock mLocker
            If mTipForm Is Nothing Then
                mTipForm = New frmTipInfo(strInfo)
            Else
                mTipForm.SetInfo(strInfo)
            End If

            If ctl IsNot Nothing Then
                mTipForm.StartPosition = FormStartPosition.Manual
                Select Case align
                    Case HorizontalAlignment.Left
                        Dim pos As Point = ctl.PointToScreen(New Point(0, ctl.Height))
                        mTipForm.Location = New Point(pos.X, pos.Y - diffY)

                    Case HorizontalAlignment.Center
                        Dim pos As Point = ctl.PointToScreen(New Point(0, ctl.Height))

                        mTipForm.Location = New Point(pos.X - 12, pos.Y - diffY)

                    Case HorizontalAlignment.Right
                        Dim pos As Point = ctl.PointToScreen(New Point(ctl.Width, ctl.Height))
                        mTipForm.Location = New Point(pos.X - mTipForm.Width, pos.Y - diffY)

                End Select

            Else
                mTipForm.StartPosition = FormStartPosition.CenterScreen
            End If

            If mTipForm.Visible Then
                mTipForm.TimerEnalbed(False)
            Else
                mTipForm.Show()
            End If

            mTipForm.Tag = ctl
            mTipForm.TopMost = True
            mTipForm.TimerEnalbed(True)

        End SyncLock
    End Sub

    Public Shared Sub HideTip(ByVal ctl As Control)
        SyncLock mLocker
            If mTipForm Is Nothing Then
                Return
            End If

            If mTipForm.Tag Is ctl Then
                mTipForm.Hide()
            End If
        End SyncLock
    End Sub

End Class


