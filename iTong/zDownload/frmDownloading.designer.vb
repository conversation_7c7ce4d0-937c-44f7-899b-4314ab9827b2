﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDownloading
    Inherits tbBaseGuiForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDownloading))
        Me.txtSearch = New iTong.Components.tbSearch()
        Me.pnlMain = New System.Windows.Forms.Panel()
        Me.btnStart = New iTong.Components.tbButton()
        Me.btnStop = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.btnMoveUp = New iTong.Components.tbButton()
        Me.btnMoveDown = New iTong.Components.tbButton()
        Me.btnOpen = New iTong.Components.tbButton()
        Me.menuDownload = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpenExplorer = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiDeleteItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.menuDownloadError = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiShowError = New System.Windows.Forms.ToolStripMenuItem()
        Me.timiTunesDowmload = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiJoinQQ = New System.Windows.Forms.ToolStripMenuItem()
        Me.menuDownload.SuspendLayout()
        Me.pnlTop.SuspendLayout()
        Me.menuDownloadError.SuspendLayout()
        Me.SuspendLayout()
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.ForeColor = System.Drawing.SystemColors.WindowText
        Me.txtSearch.Location = New System.Drawing.Point(485, 5)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "过滤文件名"
        Me.txtSearch.ShowClear = False
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(229, 23)
        Me.txtSearch.TabIndex = 0
        Me.txtSearch.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.7R
        Me.txtSearch.Visible = False
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlMain.Location = New System.Drawing.Point(1, 32)
        Me.pnlMain.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(716, 471)
        Me.pnlMain.TabIndex = 16
        '
        'btnStart
        '
        Me.btnStart.BackColor = System.Drawing.Color.Transparent
        Me.btnStart.BindingForm = Nothing
        Me.btnStart.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStart.Location = New System.Drawing.Point(7, 4)
        Me.btnStart.Name = "btnStart"
        Me.btnStart.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStart.Selectable = True
        Me.btnStart.Size = New System.Drawing.Size(57, 23)
        Me.btnStart.TabIndex = 17
        Me.btnStart.TabStop = False
        Me.btnStart.tbAdriftIconWhenHover = False
        Me.btnStart.tbAutoSize = False
        Me.btnStart.tbAutoSizeEx = True
        Me.btnStart.tbBackgroundImage = Nothing
        Me.btnStart.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStart.tbBadgeNumber = 0
        Me.btnStart.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStart.tbEndEllipsis = False
        Me.btnStart.tbIconHoldPlace = True
        Me.btnStart.tbIconImage = Global.iTong.My.Resources.Resources.btn_play_4
        Me.btnStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStart.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnStart.tbIconMore = False
        Me.btnStart.tbIconMouseDown = Nothing
        Me.btnStart.tbIconMouseHover = Nothing
        Me.btnStart.tbIconMouseLeave = Nothing
        Me.btnStart.tbIconPlaceText = 2
        Me.btnStart.tbIconReadOnly = Nothing
        Me.btnStart.tbImageMouseDown = Nothing
        Me.btnStart.tbImageMouseHover = Nothing
        Me.btnStart.tbImageMouseLeave = Nothing
        Me.btnStart.tbProgressValue = 50
        Me.btnStart.tbReadOnly = False
        Me.btnStart.tbReadOnlyText = False
        Me.btnStart.tbShadow = False
        Me.btnStart.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStart.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStart.tbShowDot = False
        Me.btnStart.tbShowMoreIconImg = CType(resources.GetObject("btnStart.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStart.tbShowNew = False
        Me.btnStart.tbShowProgress = False
        Me.btnStart.tbShowTip = True
        Me.btnStart.tbShowToolTipOnButton = False
        Me.btnStart.tbSplit = "3,3,3,3"
        Me.btnStart.tbText = "开始"
        Me.btnStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStart.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnStart.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnStart.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStart.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStart.tbTextMouseDownPlace = 2
        Me.btnStart.tbToolTip = ""
        Me.btnStart.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStart.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.VisibleEx = True
        '
        'btnStop
        '
        Me.btnStop.BackColor = System.Drawing.Color.Transparent
        Me.btnStop.BindingForm = Nothing
        Me.btnStop.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStop.Location = New System.Drawing.Point(76, 4)
        Me.btnStop.Name = "btnStop"
        Me.btnStop.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStop.Selectable = True
        Me.btnStop.Size = New System.Drawing.Size(57, 23)
        Me.btnStop.TabIndex = 17
        Me.btnStop.TabStop = False
        Me.btnStop.tbAdriftIconWhenHover = False
        Me.btnStop.tbAutoSize = False
        Me.btnStop.tbAutoSizeEx = True
        Me.btnStop.tbBackgroundImage = Nothing
        Me.btnStop.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStop.tbBadgeNumber = 0
        Me.btnStop.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStop.tbEndEllipsis = False
        Me.btnStop.tbIconHoldPlace = True
        Me.btnStop.tbIconImage = Global.iTong.My.Resources.Resources.btn_pause_4
        Me.btnStop.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStop.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnStop.tbIconMore = False
        Me.btnStop.tbIconMouseDown = Nothing
        Me.btnStop.tbIconMouseHover = Nothing
        Me.btnStop.tbIconMouseLeave = Nothing
        Me.btnStop.tbIconPlaceText = 2
        Me.btnStop.tbIconReadOnly = Nothing
        Me.btnStop.tbImageMouseDown = Nothing
        Me.btnStop.tbImageMouseHover = Nothing
        Me.btnStop.tbImageMouseLeave = Nothing
        Me.btnStop.tbProgressValue = 50
        Me.btnStop.tbReadOnly = False
        Me.btnStop.tbReadOnlyText = False
        Me.btnStop.tbShadow = False
        Me.btnStop.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStop.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStop.tbShowDot = False
        Me.btnStop.tbShowMoreIconImg = CType(resources.GetObject("btnStop.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStop.tbShowNew = False
        Me.btnStop.tbShowProgress = False
        Me.btnStop.tbShowTip = True
        Me.btnStop.tbShowToolTipOnButton = False
        Me.btnStop.tbSplit = "3,3,3,3"
        Me.btnStop.tbText = "暂停"
        Me.btnStop.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStop.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnStop.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnStop.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStop.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStop.tbTextMouseDownPlace = 2
        Me.btnStop.tbToolTip = ""
        Me.btnStop.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStop.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStop.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStop.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(145, 4)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(57, 23)
        Me.btnDelete.TabIndex = 17
        Me.btnDelete.TabStop = False
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "3,3,3,3"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'btnMoveUp
        '
        Me.btnMoveUp.BackColor = System.Drawing.Color.Transparent
        Me.btnMoveUp.BindingForm = Nothing
        Me.btnMoveUp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnMoveUp.Location = New System.Drawing.Point(214, 4)
        Me.btnMoveUp.Name = "btnMoveUp"
        Me.btnMoveUp.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnMoveUp.Selectable = True
        Me.btnMoveUp.Size = New System.Drawing.Size(39, 23)
        Me.btnMoveUp.TabIndex = 17
        Me.btnMoveUp.TabStop = False
        Me.btnMoveUp.tbAdriftIconWhenHover = False
        Me.btnMoveUp.tbAutoSize = False
        Me.btnMoveUp.tbAutoSizeEx = True
        Me.btnMoveUp.tbBackgroundImage = Nothing
        Me.btnMoveUp.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnMoveUp.tbBadgeNumber = 0
        Me.btnMoveUp.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMoveUp.tbEndEllipsis = False
        Me.btnMoveUp.tbIconHoldPlace = True
        Me.btnMoveUp.tbIconImage = Nothing
        Me.btnMoveUp.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnMoveUp.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnMoveUp.tbIconMore = False
        Me.btnMoveUp.tbIconMouseDown = Nothing
        Me.btnMoveUp.tbIconMouseHover = Nothing
        Me.btnMoveUp.tbIconMouseLeave = Nothing
        Me.btnMoveUp.tbIconPlaceText = 2
        Me.btnMoveUp.tbIconReadOnly = Nothing
        Me.btnMoveUp.tbImageMouseDown = Nothing
        Me.btnMoveUp.tbImageMouseHover = Nothing
        Me.btnMoveUp.tbImageMouseLeave = Nothing
        Me.btnMoveUp.tbProgressValue = 50
        Me.btnMoveUp.tbReadOnly = False
        Me.btnMoveUp.tbReadOnlyText = False
        Me.btnMoveUp.tbShadow = False
        Me.btnMoveUp.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnMoveUp.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnMoveUp.tbShowDot = False
        Me.btnMoveUp.tbShowMoreIconImg = CType(resources.GetObject("btnMoveUp.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnMoveUp.tbShowNew = False
        Me.btnMoveUp.tbShowProgress = False
        Me.btnMoveUp.tbShowTip = True
        Me.btnMoveUp.tbShowToolTipOnButton = False
        Me.btnMoveUp.tbSplit = "3,3,3,3"
        Me.btnMoveUp.tbText = "上移"
        Me.btnMoveUp.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnMoveUp.tbTextColor = System.Drawing.Color.Black
        Me.btnMoveUp.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnMoveUp.tbTextColorDown = System.Drawing.Color.Black
        Me.btnMoveUp.tbTextColorHover = System.Drawing.Color.Black
        Me.btnMoveUp.tbTextMouseDownPlace = 0
        Me.btnMoveUp.tbToolTip = ""
        Me.btnMoveUp.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMoveUp.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMoveUp.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMoveUp.Visible = False
        Me.btnMoveUp.VisibleEx = True
        '
        'btnMoveDown
        '
        Me.btnMoveDown.BackColor = System.Drawing.Color.Transparent
        Me.btnMoveDown.BindingForm = Nothing
        Me.btnMoveDown.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnMoveDown.Location = New System.Drawing.Point(283, 4)
        Me.btnMoveDown.Name = "btnMoveDown"
        Me.btnMoveDown.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnMoveDown.Selectable = True
        Me.btnMoveDown.Size = New System.Drawing.Size(39, 23)
        Me.btnMoveDown.TabIndex = 17
        Me.btnMoveDown.TabStop = False
        Me.btnMoveDown.tbAdriftIconWhenHover = False
        Me.btnMoveDown.tbAutoSize = False
        Me.btnMoveDown.tbAutoSizeEx = True
        Me.btnMoveDown.tbBackgroundImage = Nothing
        Me.btnMoveDown.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnMoveDown.tbBadgeNumber = 0
        Me.btnMoveDown.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMoveDown.tbEndEllipsis = False
        Me.btnMoveDown.tbIconHoldPlace = True
        Me.btnMoveDown.tbIconImage = Nothing
        Me.btnMoveDown.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnMoveDown.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnMoveDown.tbIconMore = False
        Me.btnMoveDown.tbIconMouseDown = Nothing
        Me.btnMoveDown.tbIconMouseHover = Nothing
        Me.btnMoveDown.tbIconMouseLeave = Nothing
        Me.btnMoveDown.tbIconPlaceText = 2
        Me.btnMoveDown.tbIconReadOnly = Nothing
        Me.btnMoveDown.tbImageMouseDown = Nothing
        Me.btnMoveDown.tbImageMouseHover = Nothing
        Me.btnMoveDown.tbImageMouseLeave = Nothing
        Me.btnMoveDown.tbProgressValue = 50
        Me.btnMoveDown.tbReadOnly = False
        Me.btnMoveDown.tbReadOnlyText = False
        Me.btnMoveDown.tbShadow = False
        Me.btnMoveDown.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnMoveDown.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnMoveDown.tbShowDot = False
        Me.btnMoveDown.tbShowMoreIconImg = CType(resources.GetObject("btnMoveDown.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnMoveDown.tbShowNew = False
        Me.btnMoveDown.tbShowProgress = False
        Me.btnMoveDown.tbShowTip = True
        Me.btnMoveDown.tbShowToolTipOnButton = False
        Me.btnMoveDown.tbSplit = "3,3,3,3"
        Me.btnMoveDown.tbText = "下移"
        Me.btnMoveDown.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnMoveDown.tbTextColor = System.Drawing.Color.Black
        Me.btnMoveDown.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnMoveDown.tbTextColorDown = System.Drawing.Color.Black
        Me.btnMoveDown.tbTextColorHover = System.Drawing.Color.Black
        Me.btnMoveDown.tbTextMouseDownPlace = 0
        Me.btnMoveDown.tbToolTip = ""
        Me.btnMoveDown.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMoveDown.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMoveDown.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMoveDown.Visible = False
        Me.btnMoveDown.VisibleEx = True
        '
        'btnOpen
        '
        Me.btnOpen.BackColor = System.Drawing.Color.Transparent
        Me.btnOpen.BindingForm = Nothing
        Me.btnOpen.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpen.Location = New System.Drawing.Point(352, 4)
        Me.btnOpen.Name = "btnOpen"
        Me.btnOpen.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOpen.Selectable = True
        Me.btnOpen.Size = New System.Drawing.Size(75, 23)
        Me.btnOpen.TabIndex = 20
        Me.btnOpen.TabStop = False
        Me.btnOpen.tbAdriftIconWhenHover = False
        Me.btnOpen.tbAutoSize = False
        Me.btnOpen.tbAutoSizeEx = True
        Me.btnOpen.tbBackgroundImage = Nothing
        Me.btnOpen.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnOpen.tbBadgeNumber = 0
        Me.btnOpen.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpen.tbEndEllipsis = False
        Me.btnOpen.tbIconHoldPlace = True
        Me.btnOpen.tbIconImage = Nothing
        Me.btnOpen.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOpen.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpen.tbIconMore = False
        Me.btnOpen.tbIconMouseDown = Nothing
        Me.btnOpen.tbIconMouseHover = Nothing
        Me.btnOpen.tbIconMouseLeave = Nothing
        Me.btnOpen.tbIconPlaceText = 2
        Me.btnOpen.tbIconReadOnly = Nothing
        Me.btnOpen.tbImageMouseDown = Nothing
        Me.btnOpen.tbImageMouseHover = Nothing
        Me.btnOpen.tbImageMouseLeave = Nothing
        Me.btnOpen.tbProgressValue = 50
        Me.btnOpen.tbReadOnly = False
        Me.btnOpen.tbReadOnlyText = False
        Me.btnOpen.tbShadow = False
        Me.btnOpen.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOpen.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOpen.tbShowDot = False
        Me.btnOpen.tbShowMoreIconImg = CType(resources.GetObject("btnOpen.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOpen.tbShowNew = False
        Me.btnOpen.tbShowProgress = False
        Me.btnOpen.tbShowTip = True
        Me.btnOpen.tbShowToolTipOnButton = False
        Me.btnOpen.tbSplit = "3,3,3,3"
        Me.btnOpen.tbText = "打开文件夹"
        Me.btnOpen.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOpen.tbTextColor = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorDown = System.Drawing.Color.Black
        Me.btnOpen.tbTextColorHover = System.Drawing.Color.Black
        Me.btnOpen.tbTextMouseDownPlace = 0
        Me.btnOpen.tbToolTip = ""
        Me.btnOpen.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpen.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpen.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpen.Visible = False
        Me.btnOpen.VisibleEx = True
        '
        'menuDownload
        '
        Me.menuDownload.AccessibleDescription = "153x70"
        Me.menuDownload.DropShadowEnabled = False
        Me.menuDownload.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.menuDownload.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenExplorer, Me.tsmiDeleteItem})
        Me.menuDownload.Name = "menuInInstalGrid"
        Me.menuDownload.Size = New System.Drawing.Size(131, 48)
        Me.menuDownload.tbBackColor = System.Drawing.Color.White
        Me.menuDownload.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuDownload.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuDownload.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenExplorer
        '
        Me.tsmiOpenExplorer.Name = "tsmiOpenExplorer"
        Me.tsmiOpenExplorer.Size = New System.Drawing.Size(130, 22)
        Me.tsmiOpenExplorer.Text = "打开文件夹"
        '
        'tsmiDeleteItem
        '
        Me.tsmiDeleteItem.Name = "tsmiDeleteItem"
        Me.tsmiDeleteItem.Size = New System.Drawing.Size(130, 22)
        Me.tsmiDeleteItem.Text = "删除"
        '
        'pnlTop
        '
        Me.pnlTop.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.btnOpen)
        Me.pnlTop.Controls.Add(Me.txtSearch)
        Me.pnlTop.Controls.Add(Me.btnStart)
        Me.pnlTop.Controls.Add(Me.btnMoveDown)
        Me.pnlTop.Controls.Add(Me.btnStop)
        Me.pnlTop.Controls.Add(Me.btnDelete)
        Me.pnlTop.Controls.Add(Me.btnMoveUp)
        Me.pnlTop.Location = New System.Drawing.Point(1, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(717, 32)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 21
        Me.pnlTop.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'menuDownloadError
        '
        Me.menuDownloadError.AccessibleDescription = "203x92"
        Me.menuDownloadError.DropShadowEnabled = False
        Me.menuDownloadError.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.menuDownloadError.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiShowError, Me.timiTunesDowmload, Me.tsmiJoinQQ})
        Me.menuDownloadError.Name = "menuDownloadError"
        Me.menuDownloadError.Size = New System.Drawing.Size(203, 70)
        Me.menuDownloadError.tbBackColor = System.Drawing.Color.White
        Me.menuDownloadError.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuDownloadError.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuDownloadError.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiShowError
        '
        Me.tsmiShowError.Name = "tsmiShowError"
        Me.tsmiShowError.Size = New System.Drawing.Size(202, 22)
        Me.tsmiShowError.Text = "查看失败原因"
        '
        'timiTunesDowmload
        '
        Me.timiTunesDowmload.Name = "timiTunesDowmload"
        Me.timiTunesDowmload.Size = New System.Drawing.Size(202, 22)
        Me.timiTunesDowmload.Text = "到iTunes下载该应用程序"
        '
        'tsmiJoinQQ
        '
        Me.tsmiJoinQQ.Name = "tsmiJoinQQ"
        Me.tsmiJoinQQ.Size = New System.Drawing.Size(202, 22)
        Me.tsmiJoinQQ.Text = "加入助手讨论群"
        '
        'frmDownloading
        '
        Me.BackColor = System.Drawing.Color.Silver
        Me.ClientSize = New System.Drawing.Size(718, 503)
        Me.Controls.Add(Me.pnlTop)
        Me.Controls.Add(Me.pnlMain)
        Me.Name = "frmDownloading"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "Download"
        Me.menuDownload.ResumeLayout(False)
        Me.pnlTop.ResumeLayout(False)
        Me.menuDownloadError.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents txtSearch As tbSearch

    Friend WithEvents pnlMain As System.Windows.Forms.Panel
    Friend WithEvents btnStart As tbButton
    Friend WithEvents btnStop As tbButton
    Friend WithEvents btnDelete As tbButton
    Friend WithEvents btnMoveUp As tbButton
    Friend WithEvents btnMoveDown As tbButton
    Friend WithEvents btnOpen As tbButton
    Friend WithEvents menuDownload As tbContextMenuStrip
    Friend WithEvents tsmiOpenExplorer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDeleteItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents menuDownloadError As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiJoinQQ As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents timiTunesDowmload As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShowError As System.Windows.Forms.ToolStripMenuItem

End Class
