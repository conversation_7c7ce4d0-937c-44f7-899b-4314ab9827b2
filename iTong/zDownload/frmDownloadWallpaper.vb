﻿Imports System.Threading
Public Class frmDownloadWallPaper

    Private tssDevice As ToolStripSeparator = Nothing
    Private mImageChecked As Image = Nothing                    '壁纸选中的时候打钩的图标
    Private mImageBackgroud As Image = Nothing                  '壁纸背景图片
    Private mBrushListViewItem As New SolidBrush(Color.FromArgb(255, 206, 226, 252))
    Private mPenListViewItem As New Pen(Color.FromArgb(255, 125, 162, 206))

    Private mIsClearData As Boolean = False
    Private mIsClose As Boolean = False
    Private mSelectedType As WallpaperLoaclType = WallpaperLoaclType.iPhone
    Private mDicWallpaperObject As New Dictionary(Of WallpaperLoaclType, WallpaperLoaclObject)
    Private mfrmPhoto As frmPhoto = Nothing
    Private mUsbDevices As List(Of UsbDevice) = New List(Of UsbDevice)()
    Private mFontArialRegular As Font = Nothing
    Private mFontArialBold As Font = Nothing
    Public Shared FileWatcher As New FileSystemWatcher

    Private mPenSelectedListViewItem As New Pen(Color.FromArgb(255, 23, 120, 205), 2)
    Private mPenUnSelectedListViewItem As New Pen(Color.FromArgb(255, 204, 208, 215))
    Private mimgDefault As Image = My.Resources.file_pic_large
    Private mimgExportOne As Image = My.Resources.btn_photo_import_one_android
    Private mimgDeleteOne As Image = My.Resources.btn_photo_delete_one
    Private mimgShareOne As Image = My.Resources.btn_photo_share_one
    Private mingToolBar As Image = My.Resources.photo_toolbar_bg
    Private mingUnChecked As Image = My.Resources.photo_chk_false
    Private mimgChecked As Image = My.Resources.photo_chk_true
    Private mrectExportOne As Rectangle = New Rectangle()
    Private mrectDeleteOne As Rectangle = New Rectangle()
    Private mrectShareOne As Rectangle = New Rectangle()
    Private mrectToolBar As Rectangle = New Rectangle()
    Private mCurrentHoverItem As ListViewItem = Nothing



    Property photoForm() As frmPhoto
        Get
            Return Me.mfrmPhoto
        End Get
        Set(ByVal value As frmPhoto)
            Me.mfrmPhoto = value
        End Set
    End Property

#Region "--- 初始化 ---"

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice, ByVal functionMapping As FunctionKey)
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mApplication = app
        Me.Language = app.Language
        Me.mDevice = device
        Me.FunctionMappingKey = functionMapping
        Me.InitWallpaperObject()
        InitialFonts()

    End Sub

    Private Sub InitialFonts()
        Try
            Me.mFontArialRegular = New Font("Arial", 9, FontStyle.Regular)
            Me.mFontArialBold = New Font("Arial", 9, FontStyle.Bold)
        Catch ex As Exception
            Me.mFontArialRegular = Common.CreateFont("Arial", 9, FontStyle.Regular)
            Me.mFontArialBold = Common.CreateFont("Arial", 9, FontStyle.Bold)
        End Try

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.mImageChecked = My.Resources.wallpaper_selected_icon
        Me.mImageBackgroud = My.Resources.wallpaper_bg
        Me.tssDevice = New ToolStripSeparator()
        Me.btnImportTo.Enabled = False


        Me.InitFileWatcher()

        Me.tsmiSina.Image = My.Resources.weibo_sina
        Me.tsmiTecent.Image = My.Resources.weibo_tecent
        Me.tsmiFacebook.Image = My.Resources.weibo_facebook
        Me.tsmiTwitter.Image = My.Resources.weibo_twitter

        Me.tsmiShareToSina.Image = Me.tsmiSina.Image
        Me.tsmiShareToTecent.Image = Me.tsmiTecent.Image
        Me.tsmiShareToFacebook.Image = Me.tsmiFacebook.Image
        Me.tsmiShareToTwitter.Image = Me.tsmiTwitter.Image

        If Folder.LangType = LanguageType.zh_CN Then
            Me.tsmiSina.Visible = True
            Me.tsmiTecent.Visible = True
            Me.tsmiFacebook.Visible = False
            Me.tsmiTwitter.Visible = False

            Me.tsmiShareToSina.Visible = True
            Me.tsmiShareToTecent.Visible = True
            Me.tsmiShareToFacebook.Visible = False
            Me.tsmiShareToTwitter.Visible = False
        ElseIf Folder.LangType = LanguageType.vi_VN Then
            Me.tsmiSina.Visible = False
            Me.tsmiTecent.Visible = False
            Me.tsmiFacebook.Visible = True
            Me.tsmiTwitter.Visible = False

            Me.tsmiShareToSina.Visible = False
            Me.tsmiShareToTecent.Visible = False
            Me.tsmiShareToFacebook.Visible = True
            Me.tsmiShareToTwitter.Visible = False
        Else
            Me.tsmiSina.Visible = False
            Me.tsmiTecent.Visible = False
            Me.tsmiFacebook.Visible = True
            Me.tsmiTwitter.Visible = True

            Me.tsmiShareToSina.Visible = False
            Me.tsmiShareToTecent.Visible = False
            Me.tsmiShareToFacebook.Visible = True
            Me.tsmiShareToTwitter.Visible = True
        End If


        If Common.VerIs30() Then
            Me.btnIPhone.tbImageUnCheckedMouseLeave = My.Resources.btn_viewlist_unselected
            Me.btnIPhone.tbImageUnCheckedMouseDown = Nothing
            Me.btnIPhone.tbImageUnCheckedMouseHover = Nothing
            Me.btnIPhone.tbImageCheckedMouseLeave = My.Resources.btn_viewlist_selected
            Me.btnIPhone.tbImageCheckedMouseDown = Nothing
            Me.btnIPhone.tbImageCheckedMouseHover = Nothing
            Me.btnIPhone.Text = "iPhone"
            Me.btniPad.tbImageUnCheckedMouseLeave = My.Resources.btn_viewlist_unselected
            Me.btniPad.tbImageUnCheckedMouseDown = Nothing
            Me.btniPad.tbImageUnCheckedMouseHover = Nothing
            Me.btniPad.tbImageCheckedMouseLeave = My.Resources.btn_viewlist_selected
            Me.btniPad.tbImageCheckedMouseDown = Nothing
            Me.btniPad.tbImageCheckedMouseHover = Nothing
            Me.btniPad.Text = "iPad"
            Me.btnAndroid.Visible = True
        End If
        SwitchDeviceType()
        AddHandler MobileDeviceManager.Instance.ConnectionChanged, AddressOf deviceChangeEvent
        Me.UpdateButtonEnableStatus()

        MainForm.SetButtonStatus(Me.btnImportTo)
        MainForm.SetButtonStatus(Me.btnDelete)
        MainForm.SetButtonStatus(Me.btnShareTo)
        MainForm.SetButtonStatus(Me.btnSelectAll)
        MainForm.SetButtonStatus(Me.btnRefresh)

    End Sub

    Private Sub SwitchDeviceType()
        If MobileDeviceManager.Instance.ConnectedDevices.Count >= 1 Then
            Dim device As iPhoneDevice = MobileDeviceManager.Instance.ConnectedDevices(0)
            If device.ProductType.Contains("iPhone") Or device.ProductType.Contains("iPod") Then
                Me.btnIPhone.Checked = True
                btnIPhone_CheckedChanged(Nothing, Nothing)
                'Me.SwitchWallpaperType(WallpaperLoaclType.iPhone, Nothing)
            Else
                Me.btniPad.Checked = True
                Me.btniPad_CheckedChanged(Nothing, Nothing)
                'Me.SwitchWallpaperType(WallpaperLoaclType.iPad, Nothing)
            End If

        Else

            Me.btnIPhone.Checked = True
            btnIPhone_CheckedChanged(Nothing, Nothing)

        End If
    End Sub

    Private Sub InitWallpaperObject()

        Dim imgiPhone As Image = My.Resources.wallpaper_default_iphone.GetThumbnailImage(145, 218, Nothing, IntPtr.Zero)
        Dim imgiPad As Image = My.Resources.wallpaper_default_ipad.GetThumbnailImage(148, 148, Nothing, IntPtr.Zero)

        '156,255
        Dim iphoneObj As WallpaperLoaclObject = New WallpaperLoaclObject("iPhone", New Size(145, 239), Me.pnl_Container, imgiPhone)
        '156,179
        Dim ipadObj As WallpaperLoaclObject = New WallpaperLoaclObject("iPad", New Size(145, 168), Me.pnl_Container, imgiPad)

        Dim androidObj As WallpaperLoaclObject = New WallpaperLoaclObject("Android", New Size(145, 168), Me.pnl_Container, imgiPad)

        Me.AddListViewHandler(iphoneObj.ListView)
        Me.AddListViewHandler(ipadObj.ListView)
        Me.AddListViewHandler(androidObj.ListView)

        Me.mDicWallpaperObject.Add(WallpaperLoaclType.iPhone, iphoneObj)
        Me.mDicWallpaperObject.Add(WallpaperLoaclType.iPad, ipadObj)
        Me.mDicWallpaperObject.Add(WallpaperLoaclType.Android, androidObj)

        Me.pnl_Container.ContextMenuStrip = Me.menuWallpaper
    End Sub

    Private Sub AddListViewHandler(ByVal lvw As tbListView)
        RemoveHandler lvw.DoubleClick, AddressOf lvw_DoubleClick
        AddHandler lvw.DoubleClick, AddressOf lvw_DoubleClick

        RemoveHandler lvw.ScrollChanged, AddressOf lvw_Scroll
        AddHandler lvw.ScrollChanged, AddressOf lvw_Scroll

        RemoveHandler lvw.DrawItem, AddressOf lvw_DrawItem
        AddHandler lvw.DrawItem, AddressOf lvw_DrawItem

        RemoveHandler lvw.SizeChanged, AddressOf lvw_Resize
        AddHandler lvw.SizeChanged, AddressOf lvw_Resize

        RemoveHandler lvw.SelectedIndexChanged, AddressOf lvw_SelectedIndexChanged
        AddHandler lvw.SelectedIndexChanged, AddressOf lvw_SelectedIndexChanged

        If Common.VerIs30() Then
            RemoveHandler lvw.MouseMove, AddressOf lvw_MouseMove
            AddHandler lvw.MouseMove, AddressOf lvw_MouseMove
            RemoveHandler lvw.MouseClick, AddressOf lvw_MouseClick
            AddHandler lvw.MouseClick, AddressOf lvw_MouseClick
        End If

    End Sub

    Private Sub InitFileWatcher()
        If FileWatcher Is Nothing Then
            FileWatcher = New FileSystemWatcher()
        End If
        FileWatcher.Path = IniSetting.GetDownloadWallpaperFolder
        FileWatcher.Filter = "*.*"
        FileWatcher.IncludeSubdirectories = True
        FileWatcher.EnableRaisingEvents = True

        RemoveHandler FileWatcher.Renamed, AddressOf mFileWatcher_Renamed
        AddHandler FileWatcher.Renamed, AddressOf mFileWatcher_Renamed

        RemoveHandler FileWatcher.Created, AddressOf mFileWatcher_Created
        AddHandler FileWatcher.Created, AddressOf mFileWatcher_Created

        RemoveHandler FileWatcher.Deleted, AddressOf mFileWatcher_Deleted
        AddHandler FileWatcher.Deleted, AddressOf mFileWatcher_Deleted
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.btnImportTo.Text = Me.Language.GetString("Music.Button.ImportTo")                        '"Import to device"
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")                                  '"Delete"
        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh")                         '"刷新"
        Me.tsmiLook.Text = Me.Language.GetString("Photo.Menu.View")                                 '"查看"
        Me.tsmiRefresh.Text = Me.Language.GetString("Common.Button.Refresh")                        '"刷新"
        Me.tsmiOpenExplorer.Text = Me.Language.GetString("Common.Button.OpenLocalFolder")           '"打开文件夹"
        Me.tsmiDelete.Text = Me.Language.GetString("Common.Delete")                                 '"删除"
        Me.lblWallpaperTitle.Text = Me.Language.GetString("Photo.Label.WallPaper")                  '"壁纸"
        Me.lblWallpaperDescription.Text = Me.Language.GetString("Photo.Label.WallpaperDescription") '"您通过 同步壁纸站 下载的壁纸将会自动添加到这里。"
        Me.lblWallpaper.Text = Me.Language.GetString("Photo.Label.DownloadWallpaper")               '"下载壁纸"
        Me.lblGetWallpaper.Text = Me.Language.GetString("Photo.Label.GotoDownlaodWallpaper")        '"到 同步壁纸 中浏览和下载壁纸"
        Me.btnShareTo.Text = String.Format(Me.Language.GetString("WeiBo.Label.ShareTo"), "")
        Me.btnSelectAll.Text = Me.Language.GetString("Common.SelectAll")                            '"全选"

        Me.tsmiShareToSina.Text = Me.Language.GetString("File.Menu.ShareToSina")                    '"分享到新浪微博"
        Me.tsmiShareToTecent.Text = Me.Language.GetString("WeiBo.Button.ShareToTencent")            '"分享到腾讯微博"
        Me.tsmiSina.Text = Me.Language.GetString("WeiBo.Label.Sina")                                '"新浪微博"
        Me.tsmiTecent.Text = Me.Language.GetString("WeiBo.Label.Tencent")                           '"腾讯微博"

        Me.tsmiShareToFacebook.Text = String.Format(Me.Language.GetString("WeiBo.Label.ShareTo"), "Facebook")
        Me.tsmiShareToTwitter.Text = String.Format(Me.Language.GetString("WeiBo.Label.ShareTo"), "Twitter")
        Me.lblFileEmpty.Text = Me.Language.GetString("Media.Label.Empty")   '"这里没有内容"
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Me.mIsClose = True
    End Sub

#End Region

#Region "--- 设备连接事件 ---"

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

        'Me.mDevice = device
        'If Me.mDicWallpaperObject(Me.mSelectedType).ListView.SelectedItems.Count > 0 Then
        '    Me.btnImportTo.Enabled = True
        'End If

        'Me.SwitchDeviceType()
        'if device.
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)

        ''Me.btnImportTo.Enabled = False
        'If device.Identifier = Me.mDevice.Identifier Then
        '    Me.btnImportTo.Enabled = False
        '    mDevice = Nothing
        'End If

        'MyBase.OnDisconnect(device)
    End Sub

    Public Delegate Sub deviceChangeEventHandler(ByVal sender As Object, ByVal e As ConnectionChangedEventArgs)

    Public Sub deviceChangeEvent(ByVal sender As Object, ByVal e As ConnectionChangedEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New deviceChangeEventHandler(AddressOf deviceChangeEvent), New Object() {sender, e})
        Else
            If e.Action = DeviceAction.kDisConnected Then
                If Common.VerIs30() Then
                    Me.UpdateButtonEnableStatus()
                Else
                    If Me.mDevice IsNot Nothing AndAlso e.Device.Identifier = Me.mDevice.Identifier Then
                        Me.mDevice = Nothing
                        Me.btnImportTo.Enabled = False
                    End If
                End If
            End If
        End If

    End Sub

#End Region

#Region "--- ListView事件 ---"

    Private Sub lvw_DrawItem(ByVal sender As Object, ByVal e As System.Windows.Forms.DrawListViewItemEventArgs)
        If Common.VerIs30() Then
            Dim img As Image = e.Item.Tag

            If img Is Nothing Then
                img = Me.mDicWallpaperObject(Me.mSelectedType).DefaultImage
            End If

            If img IsNot Nothing Then
                Dim intDiff As Integer = 8
                Dim intDiff2 As Integer = 7
                Dim intDiff3 As Integer = 1
                Dim intBtnTopDiff As Integer = 24
                Dim intBtnLeftDiff As Integer = 10

                Dim rectImage As New Rectangle(e.Bounds.X + (e.Bounds.Width - img.Width) \ 2, e.Bounds.Y + (e.Bounds.Height - img.Height) \ 2, img.Width, img.Height)
                Dim rectBolder As New Rectangle(rectImage.X - intDiff2, rectImage.Y - intDiff2, rectImage.Width + intDiff2 * 2, rectImage.Height + intDiff2 * 2)

                Dim rectBackground As New Rectangle(rectBolder.X + 1, rectBolder.Y + 1, rectBolder.Width - 1, rectBolder.Height)
                'rectBolder.Offset(0, 1)

                e.Graphics.DrawImage(img, rectBolder, New Rectangle(0, 0, img.Width, img.Height), GraphicsUnit.Pixel)

                Dim rectCheckPic As New Rectangle(rectBolder.Right - intBtnTopDiff, rectBolder.Bottom - intBtnTopDiff, mimgChecked.Width, mimgChecked.Height)
                If Me.mCurrentHoverItem Is e.Item OrElse e.Item.Selected Then
                    mrectExportOne = New Rectangle(rectBolder.X + intBtnLeftDiff, rectBolder.Bottom - intBtnTopDiff, mimgExportOne.Width, mimgExportOne.Height)
                    mrectShareOne = New Rectangle(mrectExportOne.Right + intBtnLeftDiff, rectBolder.Bottom - intBtnTopDiff, mimgDeleteOne.Width, mimgDeleteOne.Height)
                    mrectDeleteOne = New Rectangle(mrectShareOne.Right + intBtnLeftDiff, rectBolder.Bottom - intBtnTopDiff, mimgShareOne.Width, mimgShareOne.Height)
                    mrectToolBar = New Rectangle(rectBolder.X + 1, rectBolder.Bottom - 35, rectBolder.Width - 1, 35)

                    e.Graphics.DrawImage(Me.mingToolBar, mrectToolBar, New Rectangle(0, 0, Me.mingToolBar.Width, Me.mingToolBar.Height), GraphicsUnit.Pixel)
                    e.Graphics.DrawImage(Me.mimgExportOne, mrectExportOne, New Rectangle(0, 0, Me.mimgExportOne.Width, Me.mimgExportOne.Height), GraphicsUnit.Pixel)
                    e.Graphics.DrawImage(Me.mimgDeleteOne, mrectDeleteOne, New Rectangle(0, 0, Me.mimgDeleteOne.Width, Me.mimgDeleteOne.Height), GraphicsUnit.Pixel)
                    e.Graphics.DrawImage(Me.mimgShareOne, mrectShareOne, New Rectangle(0, 0, Me.mimgShareOne.Width, Me.mimgShareOne.Height), GraphicsUnit.Pixel)

                    If Not e.Item.Selected Then
                        e.Graphics.DrawImage(Me.mingUnChecked, rectCheckPic, New Rectangle(0, 0, mingUnChecked.Width, mingUnChecked.Height), GraphicsUnit.Pixel)
                    End If
                End If

                If e.Item.Selected Then
                    e.Graphics.DrawRectangle(mPenSelectedListViewItem, rectBolder)
                    e.Graphics.DrawImage(Me.mimgChecked, rectCheckPic, New Rectangle(0, 0, mimgChecked.Width, mimgChecked.Height), GraphicsUnit.Pixel)
                Else
                    e.Graphics.DrawRectangle(mPenUnSelectedListViewItem, rectBolder)

                End If

            End If
        Else
            Dim img As Image = e.Item.Tag

            If img Is Nothing Then
                img = Me.mDicWallpaperObject(Me.mSelectedType).DefaultImage
            End If

            If img IsNot Nothing Then
                Dim intDiff As Integer = 8
                Dim intDiff2 As Integer = 7
                Dim intDiff3 As Integer = 1

                Dim rectImage As New Rectangle(e.Bounds.X + (e.Bounds.Width - img.Width) \ 2, e.Bounds.Y + (e.Bounds.Height - img.Height) \ 2, img.Width, img.Height)
                Dim rectBolder As New Rectangle(rectImage.X - intDiff2, rectImage.Y - intDiff2, rectImage.Width + intDiff2 * 2, rectImage.Height + intDiff2 * 2)
                Dim rectBackground As New Rectangle(rectBolder.X - intDiff2, rectBolder.Y - intDiff2, rectBolder.Width + intDiff2 * 2, rectBolder.Height + intDiff2 * 2)
                rectBolder.Offset(0, 1)

                If e.Item.Selected Then
                    e.Graphics.FillRectangle(mBrushListViewItem, rectBackground)
                    e.Graphics.DrawRectangle(mPenListViewItem, rectBackground)

                End If

                GuiHelper.DrawImage(e.Graphics, rectBolder, New tbSplitStructure(5, 5, 5, 5), mImageBackgroud)

                Dim rectDraw As New Rectangle(rectImage.X + (rectImage.Width - img.Width) \ 2, rectImage.Y + (rectImage.Height - img.Height) \ 2, img.Width, img.Height)
                e.Graphics.DrawImage(img, rectDraw, New Rectangle(0, 0, img.Width, img.Height), GraphicsUnit.Pixel)

                If e.Item.Selected Then
                    Dim rectCheckPic As New Rectangle(rectBolder.X + intDiff3, rectBolder.Bottom - mImageChecked.Height - intDiff3, mImageChecked.Width, mImageChecked.Height)
                    e.Graphics.DrawImage(Me.mImageChecked, rectCheckPic, New Rectangle(0, 0, mImageChecked.Width, mImageChecked.Height), GraphicsUnit.Pixel)
                End If
            End If
        End If
    End Sub

    Private Sub lvw_Scroll(ByVal sender As Object, ByVal isScrollToLarge As Boolean)
        Me.UpdateThumbnail()
    End Sub

    Private Sub lvw_Resize(ByVal sender As Object, ByVal e As EventArgs)
        If Not Me.mIsClearData And Not Me.mIsClose Then
            Me.UpdateThumbnail()
        End If
    End Sub

    Private Sub lvw_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.OpenItem(sender)
    End Sub

    Private Sub UpdateButtonEnableStatus()
        If Me.mDicWallpaperObject.ContainsKey(Me.mSelectedType) Then
            Dim obj As WallpaperLoaclObject = Me.mDicWallpaperObject(Me.mSelectedType)
            If obj.ListView.SelectedItems.Count > 0 Then
                Me.btnDelete.Enabled = True
                Me.btnShareTo.Enabled = True

                If Common.VerIs30() Then
                    If Me.GetDevices().Count > 0 Then
                        Me.btnImportTo.Enabled = True
                    Else
                        Me.btnImportTo.Enabled = False
                    End If
                Else
                    If Me.mDevice IsNot Nothing Then
                        Me.btnImportTo.Enabled = True
                    Else
                        Me.btnImportTo.Enabled = False
                    End If
                End If
            Else
                Me.btnImportTo.Enabled = False
                Me.btnDelete.Enabled = False
                Me.btnShareTo.Enabled = False
            End If
        End If


    End Sub

    Private Sub lvw_SelectedIndexChanged(ByVal sender As Object, ByVal e As EventArgs)
        'If CType(sender, ListView).SelectedItems.Count > 0 Then
        '    Me.btnDelete.Enabled = True
        '    Me.btnShareTo.Enabled = True

        '    If Me.mDevice IsNot Nothing Then
        '        Me.btnImportTo.Enabled = True
        '    Else
        '        Me.btnImportTo.Enabled = False
        '    End If

        'Else
        '    Me.btnImportTo.Enabled = False
        '    Me.btnDelete.Enabled = False
        '    Me.btnShareTo.Enabled = False
        'End If
        UpdateButtonEnableStatus()
    End Sub

    Private Sub lvw_MouseMove(ByVal sender As System.Object, ByVal e As MouseEventArgs)
        Dim lvw As tbListView = Me.GetCurrentListView()
        Dim item As ListViewItem = lvw.GetItemAt(e.X, e.Y)
        If item IsNot Nothing Then
            mCurrentHoverItem = item
            lvw.Invalidate()
        Else
            mCurrentHoverItem = Nothing
        End If
    End Sub

    Private Sub lvw_MouseClick(ByVal sender As System.Object, ByVal e As MouseEventArgs)
        If mCurrentHoverItem IsNot Nothing Then

            If mrectExportOne.Contains(e.Location) Then
                Dim arrSelectedItems As List(Of ListViewItem) = New List(Of ListViewItem)()
                arrSelectedItems.Add(mCurrentHoverItem)
                Me.DoSync()
                mCurrentHoverItem.Selected = False
                Me.mrectExportOne = Nothing
            ElseIf mrectDeleteOne.Contains(e.Location) Then
                Dim arrSelectedItems As List(Of ListViewItem) = New List(Of ListViewItem)()
                arrSelectedItems.Add(mCurrentHoverItem)
                Me.DoDelete()
                mCurrentHoverItem.Selected = False
                Me.mrectDeleteOne = Nothing
            ElseIf mrectShareOne.Contains(e.Location) Then
                Me.cmsWeibo.Visible = True
                Me.cmsWeibo.Show(mCurrentHoverItem.ListView, e.X, e.Y)
                'mCurrentHoverItem.Selected = False
                Me.mrectShareOne = Nothing
            End If
        End If

    End Sub


#End Region

#Region "--- 顶部菜单事件 ---"

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiDelete.Click, btnDelete.Click
        DoDelete()
    End Sub

    Private Sub DoDelete()

        Dim currentListview As tbListView = GetCurrentListview()

        If currentListview IsNot Nothing AndAlso currentListview.SelectedItems.Count > 0 Then
            Dim strRemind As String
            If currentListview.SelectedItems.Count > 1 Then
                strRemind = String.Format(Me.Language.GetString("Photo.Message.SureDeleteSelectItems"), currentListview.SelectedItems.Count, _
                                          Me.Language.GetString("Common.Info"))
            Else
                Dim strFileName As String = Path.GetFileName(currentListview.SelectedItems(0).Name)
                strRemind = String.Format(Me.Language.GetString("Common.Message.DeleteMessageOneItem"), strFileName)
            End If

            If tbMessageBox.Show(Me, strRemind, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Warning, _
                                 MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then


                Dim listFiles As New List(Of String)

                For Each lvwItem As ListViewItem In currentListview.SelectedItems
                    Dim strPath As String = lvwItem.Name
                    listFiles.Add(strPath)
                Next

                For Each strItem As String In listFiles
                    Try
                        File.Delete(strItem)
                        currentListview.Items.RemoveByKey(strItem)
                    Catch ex As Exception
                    End Try
                Next
                Me.mDicWallpaperObject(Me.mSelectedType).SetNodeNumber()
                Me.SetCount(Me.mDicWallpaperObject(Me.mSelectedType))
                UpdateParentCount()
            End If
        End If
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiRefresh.Click, btnRefresh.Click
        Me.LoadWallpaper(True)
        Me.UpdateButtonEnableStatus()
    End Sub

    Private Sub btnImportTo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImportTo.Click
        DoSync()
    End Sub

    Private Sub DoSync()

        Try
            If Me.mDevice IsNot Nothing OrElse mUsbDevices.Count > 0 Then

                If Common.VerIs30() Then
                    Dim intDeviceCount As Integer = Me.GetDevices().Count
                    If intDeviceCount > 1 Then
                        CreateDeviceMenu(Me, New Point(Me.btnImportTo.Left, Me.btnImportTo.Bottom))
                    ElseIf intDeviceCount = 1 Then
                        Me.SyncToDevice(Me.GetDevices()(0))
                    End If
                Else
                    Dim frmMain As MainForm = Me.mApplication 'ProcForm.GetMainForm()
                    frmMain.GotoItem(ActionFuncType.Photo)

                    If frmMain.WindowState = FormWindowState.Minimized Then
                        frmMain.WindowState = FormWindowState.Normal

                    End If

                    Me.SyncToDevice(Me.mDevice)
                End If
            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

    Private Sub CreateDeviceMenu(ByVal parent As Control, ByVal location As Point)
        Dim menu As New tbContextMenuStrip()
        Dim usbDevices As List(Of UsbDevice) = Me.GetDevices()
        For Each device As UsbDevice In usbDevices
            If TypeOf device.MobileDevice Is iPhoneDevice Then
                Dim iphone As iPhoneDevice = device.MobileDevice
                Dim menuItem As ToolStripItem = Nothing
                If iphone.ProductType.Contains("iPhone") Or iphone.ProductType.Contains("iPod") Then
                    menuItem = menu.Items.Add(iphone.DeviceName, My.Resources.file_icon_iphone)
                Else
                    menuItem = menu.Items.Add(iphone.DeviceName, My.Resources.file_icon_ipad)
                End If
                menuItem.ImageAlign = ContentAlignment.MiddleLeft
                menuItem.Tag = device
            ElseIf TypeOf device.MobileDevice Is AndroidDevice Then
                Dim androidDev As AndroidDevice = device.MobileDevice
                Dim menuItem As ToolStripItem = Nothing
                menuItem = menu.Items.Add(androidDev.DeviceName, My.Resources.file_icon_iphone)
                menuItem.ImageAlign = ContentAlignment.MiddleLeft
                menuItem.Tag = device
            End If
        Next

        AddHandler menu.ItemClicked, AddressOf OnItemMenuSelect
        AddHandler menu.Closed, AddressOf OnItemMenuClosed

        menu.Show(parent, location)
    End Sub
    Private Function GetDevices() As List(Of UsbDevice)
        Dim mainFrm As MainForm = Me.mApplication
        Dim usbDevices As List(Of UsbDevice) = New List(Of UsbDevice)()
        For Each dev As UsbDevice In mainFrm.UsbDevices.Values
            If TypeOf dev.MobileDevice Is iPhoneDevice AndAlso (btnIPhone.Checked OrElse btniPad.Checked) Then
                Dim device As iPhoneDevice = dev.MobileDevice
                If device.IsConnected Then
                    usbDevices.Add(dev)
                End If
            ElseIf TypeOf dev.MobileDevice Is AndroidDevice AndAlso btnAndroid.Checked Then
                Dim device As AndroidDevice = dev.MobileDevice
                If device.IsConnected Then
                    usbDevices.Add(dev)
                End If
            End If
        Next
        Return usbDevices
    End Function

    Private Sub OnItemMenuSelect(ByVal sender As Object, ByVal args As ToolStripItemClickedEventArgs)
        Dim listRows As List(Of DataGridViewRow) = sender.Tag
        Dim device As UsbDevice = args.ClickedItem.Tag
        If TypeOf device.MobileDevice Is AndroidDevice Then
            Dim androidDev As AndroidDevice = device.MobileDevice
            If Not androidDev.IsConnected OrElse androidDev.Summary Is Nothing Then
                Dim mainFrm As MainForm = Me.mApplication
                mainFrm.BringToFront()
                mainFrm.SelectUSBDev(device)
                Return
            End If
        End If
        Me.SyncToDevice(device)
    End Sub

    Private Sub OnItemMenuClosed(ByVal sender As Object, ByVal args As ToolStripDropDownClosedEventArgs)
        Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
        RemoveHandler menu.ItemClicked, AddressOf OnItemMenuSelect
        RemoveHandler menu.Closed, AddressOf OnItemMenuClosed
    End Sub


#End Region

#Region "--- 右键菜单事件 ---"

    Private Sub menuWallpaper_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles menuWallpaper.Opening
        Dim currentListview As tbListView = GetCurrentListview()
        If currentListview.SelectedItems Is Nothing OrElse currentListview.SelectedItems.Count = 0 Then
            e.Cancel = True
        End If

        Me.menuWallpaper.SuspendLayout()
        Me.menuWallpaper.Items.Clear()
        Me.menuWallpaper.Items.AddRange(New ToolStripItem() {Me.tsmiOpenExplorer, Me.tssOpenExplorer, Me.tsmiRefresh, Me.tsmiLook, Me.tssLook, Me.tsmiDelete, Me.tssDelete, _
                                                             Me.tsmiShareToSina, Me.tsmiShareToTecent, Me.tsmiShareToFacebook, Me.tsmiShareToTwitter})

        If Me.mDevice IsNot Nothing Then
            Me.menuWallpaper.Items.AddRange(New ToolStripItem() {Me.tssDevice})
            Dim item As New ToolStripMenuItem(Me.Language.GetString("Music.Button.ImportTo") & " " & Me.mDevice.DeviceName, Me.mDevice.FuncDeviceIcon, New EventHandler(AddressOf tsmiAddWallpaperToDevice))
            item.Tag = iPhone
            Me.menuWallpaper.Items.AddRange(New ToolStripItem() {item})
        End If

        Me.menuWallpaper.ResumeLayout()
    End Sub

    Private Sub tsmiLook_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiLook.Click
        Dim currentListview As tbListView = Me.GetCurrentListview()
        Me.OpenItem(currentListview)
    End Sub

    Private Sub menuItemOpenExplorer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiOpenExplorer.Click
        Dim currentListview As tbListView = GetCurrentListview()
        If currentListview.SelectedItems IsNot Nothing Then
            Dim strPath As String = currentListview.SelectedItems(0).Name
            If Not File.Exists(strPath) Then
                tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.FileNoExist"), _
                                                                Me.Language.GetString("Common.Info"), _
                                                                MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            iTong.CoreFoundation.Common.OpenExplorer(strPath)
        End If
    End Sub

    Private Sub tsmiAddWallpaperToDevice(ByVal sender As Object, ByVal e As EventArgs)
        Try
            If Me.mDevice IsNot Nothing Then

                Dim frmMain As MainForm = Me.mApplication 'ProcForm.GetMainForm()
                frmMain.GotoItem(ActionFuncType.Photo)

                If frmMain.WindowState = FormWindowState.Minimized Then
                    frmMain.WindowState = FormWindowState.Normal

                End If
                Me.SyncToDevice(Me.mDevice)
            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Sub AddItemToListview(ByVal strFilePath As String, ByRef lvw As tbListView)
        Dim item As New ListViewItem()

        item.Name = strFilePath
        lvw.Items.Add(item)
    End Sub

    Private Sub LoadWallpaper(Optional ByVal blnRefresh As Boolean = False)
        Dim lvw As ListView = Me.GetCurrentListview()
        Dim obj As WallpaperLoaclObject = Me.mDicWallpaperObject(Me.mSelectedType)
        If Not obj.Loaded OrElse blnRefresh Then
            obj.Loaded = True

            Me.mIsClearData = True
            For Each item As ListViewItem In lvw.Items
                If item.Tag IsNot Nothing Then
                    CType(item.Tag, Image).Dispose()
                    item.Tag = Nothing
                End If
            Next
            lvw.Items.Clear()

            Dim lstPath As List(Of String) = Me.GetSelectFolder()
            For Each itemPath As String In lstPath
                If Not Directory.Exists(itemPath) Then
                    Continue For
                End If
                For Each item As String In Directory.GetFiles(itemPath)
                    If Me.CheckIfImg(item) Then
                        Me.AddItemToListview(item, lvw)
                    End If
                Next
            Next

            If obj.Node IsNot Nothing Then
                obj.Node.NodeNumber = lvw.Items.Count
            End If

            Me.mIsClearData = False

            If lvw.Items.Count > 0 Then
                lvw.Items(lvw.Items.Count - 1).EnsureVisible()
                lvw.Focus()
            End If
        End If

        Me.UpdateThumbnail()
        Me.UpdateParentCount()
    End Sub

    Protected Function GetItems(ByVal lvw As ListView, Optional ByVal includeAll As Boolean = False) As List(Of ListViewItem)
        Dim list As New List(Of ListViewItem)

        Try
            If lvw.Items.Count = 0 Then
                GoTo DO_EXIT
            End If

            If lvw.Width = 0 Then
                GoTo DO_EXIT
            End If
            Dim itemStart As ListViewItem = Nothing
            Dim diff As Integer = 0
            Do
                itemStart = lvw.GetItemAt(diff, diff * (lvw.Height / lvw.Width))
                diff += 10
            Loop While itemStart Is Nothing AndAlso diff < lvw.Width

            If itemStart Is Nothing Then
                GoTo DO_EXIT
            End If

            Dim rowCount As Integer = Math.Floor(lvw.ClientRectangle.Width / itemStart.Bounds.Width)
            Dim colCount As Integer = Math.Ceiling(lvw.ClientRectangle.Height / itemStart.Bounds.Height)

            Dim maxIndex As Integer = rowCount * colCount + itemStart.Index
            If maxIndex > lvw.Items.Count - 1 Then
                maxIndex = lvw.Items.Count - 1
            End If
            For index As Integer = itemStart.Index To maxIndex
                If lvw.Items(index) IsNot Nothing Then
                    list.Add(lvw.Items(index))
                End If
            Next

            If includeAll Then
                If itemStart.Index > 0 Then
                    For index As Integer = 0 To itemStart.Index - 1
                        If lvw.Items(index) IsNot Nothing Then
                            list.Add(lvw.Items(index))
                        End If
                    Next
                End If

                If maxIndex < lvw.Items.Count - 1 Then
                    For index As Integer = maxIndex + 1 To lvw.Items.Count - 1
                        If lvw.Items(index) IsNot Nothing Then
                            list.Add(lvw.Items(index))
                        End If
                    Next
                End If
            End If
        Catch ex As Exception
        End Try

DO_EXIT:
        Return list
    End Function

    Private Delegate Sub UpdateThumbnailHandler()
    Private Sub UpdateThumbnail()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New UpdateThumbnailHandler(AddressOf UpdateThumbnail))
        Else

            Try
                Dim lvw As tbListView = Me.GetCurrentListview()

                If lvw.Items.Count = 0 Then
                    Return
                End If

                lvw.ScrollBar = True
                lvw.ResponseIndex += 1

                Dim listItems As List(Of ListViewItem) = Nothing
                Dim intResponseIndex As Integer = lvw.ResponseIndex

DO_RELOAD:
                If listItems IsNot Nothing Then
                    listItems.Clear()
                End If
                listItems = Me.GetItems(lvw)
                For Each item As ListViewItem In listItems
                    If intResponseIndex <> lvw.ResponseIndex Then
                        Return
                    End If

                    If item.Tag Is Nothing AndAlso File.Exists(item.Name) Then
                        Dim imgSize As Size = Me.mDicWallpaperObject(Me.mSelectedType).DefaultImage.Size
                        Dim imgSource As Image = Common.ImageFromFile(item.Name)
                        Application.DoEvents()

                        Dim imgThumb As Image = Utility.GetThumbnail(imgSource, imgSize, True, True, False, Color.White)
                        Application.DoEvents()

                        item.Tag = imgThumb

                        imgSource.Dispose()
                        imgSource = Nothing

                        lvw.Invalidate(New Region(item.Bounds))
                        Application.DoEvents()
                    End If
                Next

                If listItems IsNot Nothing AndAlso listItems.Count > 0 Then
                    Dim showCount As Integer = listItems.Count
                    Dim intStart As Integer = listItems(0).Index
                    Dim intEnd As Integer = listItems(showCount - 1).Index

                    For index As Integer = 0 To intStart - showCount * 2
                        If intResponseIndex <> lvw.ResponseIndex Then
                            Return
                        End If

                        Dim item As ListViewItem = lvw.Items(index)
                        If item.Tag IsNot Nothing Then
                            CType(item.Tag, Image).Dispose()
                            item.Tag = Nothing
                        End If
                        Application.DoEvents()
                    Next

                    For index As Integer = intEnd + showCount * 2 To lvw.Items.Count - 1
                        If intResponseIndex <> lvw.ResponseIndex Then
                            Return
                        End If

                        Dim item As ListViewItem = lvw.Items(index)
                        If item.Tag IsNot Nothing Then
                            CType(item.Tag, Image).Dispose()
                            item.Tag = Nothing
                        End If
                        Application.DoEvents()
                    Next

                    listItems.Clear()
                End If
            Catch ex As Exception
            End Try

        End If
    End Sub

    Private Sub LoadFrmImageViewThumbnail(ByVal lvw As ListView)
        Try
            Dim lvwCurrent As ListView = Me.GetCurrentListview()
            Dim listItems As List(Of ListViewItem) = Me.GetItems(lvw)

            For Each item As ListViewItem In listItems
                If Not lvw.LargeImageList.Images.ContainsKey(item.Name) AndAlso File.Exists(item.Name) Then
                    Dim imgSource As Image = Common.ImageFromFile(item.Name)
                    Application.DoEvents()

                    Dim img75x75 As Image = Utility.GetThumbnail(imgSource, lvwCurrent.SmallImageList.ImageSize, True)
                    imgSource.Dispose()
                    imgSource = Nothing
                    Application.DoEvents()

                    lvw.LargeImageList.Images.Add(item.Name, img75x75)
                    img75x75.Dispose()
                    img75x75 = Nothing

                    lvw.Invalidate(New Region(item.Bounds))
                    Application.DoEvents()
                End If
            Next

            If listItems IsNot Nothing Then
                listItems.Clear()
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Function CheckIfImg(ByVal strFileName As String) As Boolean
        Dim blnReturn As Boolean = False

        strFileName = strFileName.ToLower()

        If strFileName.EndsWith(".jpg") OrElse _
           strFileName.EndsWith(".jpeg") OrElse _
           strFileName.EndsWith(".png") Then

            blnReturn = True
        End If

        Return blnReturn
    End Function

    Private Function GetCurrentListview() As tbListView
        Return Me.mDicWallpaperObject(Me.mSelectedType).ListView
    End Function

    Private Function GetSelectFolder() As List(Of String)
        Dim strFolders As New List(Of String)
        Dim strfolder As String = Me.mDicWallpaperObject(Me.mSelectedType).WallpaperFolder
        If Not Directory.Exists(strfolder) Then
            Folder.CheckFolder(strfolder)
        End If
        strFolders.Add(strfolder)

        Return strFolders
    End Function

    Private Function GetMatchListview(ByVal strWallpaperPath As String) As tbListView
        Dim lvw As New tbListView
        Dim strPath As String = Path.GetDirectoryName(strWallpaperPath)

        For Each Item As WallpaperLoaclObject In mDicWallpaperObject.Values
            If strPath.Trim("\").EndsWith(Item.Name) Then
                lvw = Item.ListView
                Exit For
            End If
        Next

        Return lvw
    End Function

    Private Function GetMatchObject(ByVal strWallpaperPath As String) As WallpaperLoaclObject
        Dim obj As WallpaperLoaclObject = Nothing
        Dim strPath As String = Path.GetDirectoryName(strWallpaperPath)

        For Each Item As WallpaperLoaclObject In mDicWallpaperObject.Values
            'If Item.WallpaperFolder.Contains(strPath.Trim("\")) Then
            If strPath.Trim("\").EndsWith(Item.Name) Then
                If Item.WallpaperFolder <> strPath Then
                    Item.Name = Item.Name
                End If
                obj = Item
                Exit For
            End If
        Next

        Return obj
    End Function

    Private Sub SyncToDevice(ByVal dev As UsbDevice)
        Dim mainFrm As MainForm = Me.mApplication

        mainFrm.BringToFront()
        mainFrm.SelectUSBDev(dev)
        If dev.DeviceType = DeviceType.Android Then
            mainFrm.DownloadingWallpaper = True
        End If
        mainFrm.GotoItem(ActionFuncType.Photo)
        If TypeOf dev.MobileDevice Is iPhoneDevice Then
            SyncToDevice(CType(dev.MobileDevice, iPhoneDevice))
        ElseIf TypeOf dev.MobileDevice Is AndroidDevice Then
            SyncToAndroidDevice(dev.MobileDevice)
        End If

    End Sub
    Private Sub SyncToDevice(ByVal device As iPhoneDevice)
        Try
            Dim listFiles As New List(Of String)
            Dim lvw As ListView = Me.GetCurrentListview()

            For Each item As ListViewItem In lvw.SelectedItems
                listFiles.Add(item.Name)
            Next

            If Common.VerIs30() Then

            End If

            If listFiles.Count > 0 Then
                Common.LogException("frmDownloadWallpaper(SyncToDevice)")
                device.FuncAddWallpaper(listFiles)
            End If

        Catch ex As Exception
        End Try
    End Sub
    Private Sub SyncToAndroidDevice(ByVal device As AndroidDevice)
        Try
            Dim listFiles As New List(Of String)
            Dim lvw As ListView = Me.GetCurrentListview()

            For Each item As ListViewItem In lvw.SelectedItems
                listFiles.Add(item.Name)
            Next

            If listFiles.Count > 0 Then
                Common.LogException("frmDownloadWallpaper(SyncToDevice)")
                device.FuncAddWallpaper(listFiles)
            End If

        Catch ex As Exception
        End Try
    End Sub


#End Region

#Region "--- 图片查看器 ---"

    Private Sub WeiboSend(ByVal clientType As ClientType)
        Dim img As Image = Me.cmsWeibo.Tag
        If img Is Nothing Then
            Dim currentListview As tbListView = Me.GetCurrentListview
            If currentListview Is Nothing OrElse currentListview.SelectedItems.Count = 0 Then
                Return
            Else
                img = Common.ImageFromFile(currentListview.SelectedItems(0).Name)
            End If
        End If

        If img Is Nothing Then
            Return
        End If
      
        WeiboHelper.SendMessage(clientType, Me, String.Empty, img, False)
        If Me.cmsWeibo.Tag IsNot Nothing AndAlso TypeOf Me.cmsWeibo.Tag Is Image Then
            CType(Me.cmsWeibo.Tag, Image).Dispose()
            Me.cmsWeibo.Tag = Nothing
        End If
    End Sub

    Private Sub tsmiSina_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiSina.Click, tsmiShareToSina.Click
        Dim currentListview As tbListView = Me.GetCurrentListview
        If currentListview Is Nothing OrElse currentListview.SelectedItems.Count = 0 Then
            Return
        End If

        If currentListview.SelectedItems.Count > 1 Then
            '"壁纸一次只能分享一张。"
            tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.MuchWallpaperForWeibo"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
            Return
        End If

        WeiboSend(ClientType.Sina)
    End Sub

    Private Sub tsmiTecent_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiTecent.Click, tsmiShareToTecent.Click

        Dim currentListview As tbListView = Me.GetCurrentListview
        If currentListview Is Nothing OrElse currentListview.SelectedItems.Count = 0 Then
            Return
        End If

        If currentListview.SelectedItems.Count > 1 Then
            '"壁纸一次只能分享一张。"
            tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.MuchWallpaperForWeibo"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
            Return
        End If


        WeiboSend(ClientType.Tecent)
    End Sub

    Private Sub tsmiFacebook_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiFacebook.Click, tsmiShareToFacebook.Click
        WeiboSend(ClientType.Facebook)
    End Sub

    Private Sub tsmiTwitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiTwitter.Click, tsmiShareToTwitter.Click
        WeiboSend(ClientType.Twitter)
    End Sub

    Private Sub OpenItem(ByVal lvw As ListView)
        If lvw.SelectedItems.Count = 0 Then
            Return
        End If

        Dim item As ListViewItem = lvw.SelectedItems(0)
        Dim frmImageView As New tbImageViewForm()
        frmImageView.Icon = My.Resources.iTong

        frmImageView.ListView.SuspendLayout()

        For Each lvItem As ListViewItem In lvw.Items
            Dim newItem As New ListViewItem()
            newItem.Name = lvItem.Name
            newItem.ImageKey = lvItem.Name
            frmImageView.ListView.Items.Add(newItem)
        Next

        frmImageView.ListView.ResumeLayout()

        frmImageView.ViewType = ViewType.FromPC
        frmImageView.ListView.LargeImageList = lvw.SmallImageList
        frmImageView.SelectItemName = item.Name
        frmImageView.HideButton = ToolBarButtonType.Main

        RemoveHandler frmImageView.ListView.ScrollChanged, AddressOf ImageView_Scroll
        AddHandler frmImageView.ListView.ScrollChanged, AddressOf ImageView_Scroll
        RemoveHandler frmImageView.DeleteClick, AddressOf ImageView_Delete
        AddHandler frmImageView.DeleteClick, AddressOf ImageView_Delete

        RemoveHandler frmImageView.ListView.SelectedIndexChanged, AddressOf ImageView_SelectedIndexChanged
        AddHandler frmImageView.ListView.SelectedIndexChanged, AddressOf ImageView_SelectedIndexChanged

        RemoveHandler frmImageView.WeiboClick, AddressOf frmImageView_WeiboClick
        AddHandler frmImageView.WeiboClick, AddressOf frmImageView_WeiboClick

        frmImageView.Show()

    End Sub

    Public Sub frmImageView_WeiboClick(ByVal sender As Object, ByVal e As ImageEventArgs)
        WeiboHelper.SendMessage(e.Tag, Nothing, "", e.Image, False)
    End Sub

    Private Sub ImageView_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        '从最后一张图片点击下一张的时候到第一张无法显示缩略图片。

        Dim lvw As ListView = sender
        If lvw IsNot Nothing AndAlso lvw.Items.Count > 0 AndAlso lvw.SelectedItems.Count > 0 Then
            If lvw.SelectedItems(0).Index = 0 AndAlso lvw.Items.Count >= 5 Then
                lvw.Items(4).EnsureVisible()
                lvw.Items(0).EnsureVisible()
                Me.LoadFrmImageViewThumbnail(lvw)
            End If
        End If
    End Sub

    Private Sub ImageView_Scroll(ByVal sender As Object, ByVal isScrollToLarge As Boolean)
        Me.LoadFrmImageViewThumbnail(sender)
    End Sub

    Private Sub ImageView_Delete(ByVal sender As Object, ByVal e As ImageEventArgs)
        Try
            Dim lvw As tbListView = Me.GetCurrentListview()
            If lvw Is Nothing Then
                Return
            End If

            Dim item As ListViewItem = lvw.Items(e.ImagePath)
            If item IsNot Nothing Then
                lvw.Items.Remove(item)
                CType(item.Tag, Image).Dispose()
            End If

            If File.Exists(e.ImagePath) Then
                File.Delete(e.ImagePath)
            End If

            Me.UpdateThumbnail()
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 壁纸文件夹监控 ---"

    Private Sub mFileWatcher_Created(ByVal sender As Object, ByVal e As System.IO.FileSystemEventArgs)
        If Me.CheckIfImg(e.FullPath) Then
            Me.CreateWallpaper(e.FullPath)
        End If
    End Sub

    Private Sub mFileWatcher_Renamed(ByVal sender As Object, ByVal e As System.IO.RenamedEventArgs)
        If Me.CheckIfImg(e.FullPath) Then
            If Path.GetExtension(e.OldFullPath) = ".tb" Then
                Me.CreateWallpaper(e.FullPath)
            Else
                Me.RenameWallpaper(e.OldFullPath, e.FullPath)
            End If
        End If
    End Sub

    Private Sub mFileWatcher_Deleted(ByVal sender As Object, ByVal e As System.IO.FileSystemEventArgs)
        If Me.CheckIfImg(e.FullPath) Then
            Me.DeleteWallpaper(e.FullPath)
            Me.SetNonwWallpaperPanel()

            Dim objWallpaper As WallpaperLoaclObject = Me.GetMatchObject(e.FullPath)
            If objWallpaper IsNot Nothing Then
                objWallpaper.SetNodeNumber()
                Me.SetCount(objWallpaper)
            End If
        End If
    End Sub

    Private Delegate Sub RenameWallpaperHandler(ByVal strOldFilePath As String, ByVal strNewFilePath As String)
    Private Sub RenameWallpaper(ByVal strOldFilePath As String, ByVal strNewFilePath As String)
        If Me.InvokeRequired Then
            Me.Invoke(New RenameWallpaperHandler(AddressOf Me.RenameWallpaper), strOldFilePath, strNewFilePath)
        Else
            Dim lvw As tbListView = GetMatchListview(strOldFilePath)
            Dim lvwItem As ListViewItem = lvw.Items(strOldFilePath)
            If lvwItem IsNot Nothing Then
                lvwItem.Name = strNewFilePath
            End If

        End If
    End Sub

    Private Delegate Sub WallpaperHandler(ByVal strWallpaperPath As String)
    Private Sub CreateWallpaper(ByVal strWallpaperPath As String)
        If Me.InvokeRequired Then
            Me.Invoke(New WallpaperHandler(AddressOf Me.CreateWallpaper), strWallpaperPath)
        Else
            Dim lvw As tbListView = Me.GetMatchListview(strWallpaperPath)

            Me.AddItemToListview(strWallpaperPath, lvw)

            lvw.Items(lvw.Items.Count - 1).EnsureVisible()
            'lvw.Focus()

            Me.UpdateThumbnail()
            Me.UpdateParentCount()
            Me.SetNonwWallpaperPanel()

            Dim objWallpaper As WallpaperLoaclObject = Me.GetMatchObject(strWallpaperPath)
            If objWallpaper IsNot Nothing Then
                objWallpaper.SetNodeNumber()
                Me.SetCount(objWallpaper)
            End If
        End If
    End Sub

    Private Sub DeleteWallpaper(ByVal strWallpaperPath As String)
        If Me.InvokeRequired Then
            Me.Invoke(New WallpaperHandler(AddressOf Me.DeleteWallpaper), strWallpaperPath)
        Else
            Dim lvw As tbListView = Me.GetMatchListview(strWallpaperPath)
            lvw.Items.RemoveByKey(strWallpaperPath)
            Me.UpdateThumbnail()
            Me.UpdateParentCount()
        End If
    End Sub

#End Region

#Region "--- 没有壁纸的时Panel事件 ---"

    Private Sub lblGetWallpaper_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblGetWallpaper.Click
        Me.GoToWallpaperSite()
    End Sub

    Private Sub btnGotoSite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGotoSite.Click
        Me.GoToWallpaperSite()
    End Sub

    Private Sub GoToWallpaperSite()
        Dim strAppVer As String = My.Application.Info.Version.ToString(4)
        Dim strLang As String = Me.Language.CurrentLanguage.LangName
        Dim strDeviceID As String = 1
        If Me.mSelectedType = WallpaperLoaclType.iPad Then
            strDeviceID = 2
        End If
        '"http://v2.tongbu.com/wallpaper?ver={0}&lang={1}&deviceid={2}"
        Dim strUrlWallpaper As String = SiteXmlHelper.GetSiteUrlByName("Wallpaper").Replace("#ver#", strAppVer).Replace("#lang#", strLang).Replace("#deviceid#", strDeviceID)

        Me.mApplication.GotoSite("Wallpaper", strUrlWallpaper)
    End Sub

    Private Sub lblGetWallpaper_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblGetWallpaper.SizeChanged
        Me.btnGotoSite.Location = New Point(Me.lblGetWallpaper.Right + 5, Me.btnGotoSite.Top)
    End Sub

    '是否要显示没有壁纸的 palel
    Private Delegate Sub SetNonwWallpaperPanelHandler()
    Private Sub SetNonwWallpaperPanel()
        'Return

        If Me.InvokeRequired Then
            Me.Invoke(New SetNonwWallpaperPanelHandler(AddressOf SetNonwWallpaperPanel))
        Else
            Dim lvw As ListView = Me.GetCurrentListview()
            If lvw.Items.Count = 0 Then
                Me.pnlFileEmpty.Location = New Point(0, 0)
                Me.pnlFileEmpty.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                                                          Or System.Windows.Forms.AnchorStyles.Left) _
                                                          Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)

                Me.pnlFileEmpty.Size = Me.pnl_Container.Size
                Me.pnlFileEmpty.Visible = True
                Me.pnlFileEmpty.BringToFront()

                'Me.pnlDownloadWallpaper.Location = New Point(0, 0)
                'Me.pnlDownloadWallpaper.Size = Me.Size
                'Me.pnlDownloadWallpaper.Visible = True
                'Me.pnlDownloadWallpaper.BringToFront()

            Else
                Me.pnlFileEmpty.Visible = False
                Me.pnlFileEmpty.SendToBack()
                'Me.pnlDownloadWallpaper.Visible = False
                'Me.pnlDownloadWallpaper.SendToBack()
            End If
        End If

    End Sub

#End Region

#Region "--- 公用函数 ---"

    Public Sub SetImportToDeviceStatus(ByVal device As iPhoneDevice)
        If device IsNot Nothing Then
            Me.btnImportTo.Enabled = True
            Me.mDevice = device
        Else
            Me.btnImportTo.Enabled = False
        End If
    End Sub

    Public Function SwitchWallpaperType(ByVal Type As WallpaperLoaclType, ByVal node As tbTreeNode) As Integer
        Me.mSelectedType = Type

        If Not Me.mDicWallpaperObject.ContainsKey(Type) Then
            Return 0
        End If
        Me.GetCurrentListview().BringToFront()
        Me.LoadWallpaper()
        Me.SetNonwWallpaperPanel()
        Me.lvw_SelectedIndexChanged(Me.GetCurrentListview(), Nothing)
        If node IsNot Nothing Then
            Me.mDicWallpaperObject(Type).Node = node
            Me.mDicWallpaperObject(Type).SetNodeNumber()
        End If

        Me.SetCount(Me.mDicWallpaperObject(Type))
    End Function

    Private Delegate Sub LoadPicCountHandler(ByVal wallType As WallpaperLoaclType, ByVal node As tbTreeNode)
    Public Sub LoadPicCount(ByVal wallType As WallpaperLoaclType, ByVal node As tbTreeNode)
        If Me.InvokeRequired Then
            Me.Invoke(New LoadPicCountHandler(AddressOf LoadPicCount), wallType, node)
        Else
            Dim obj As WallpaperLoaclObject = Me.mDicWallpaperObject(wallType)
            Dim intCount As Integer = 0
            If Directory.Exists(obj.WallpaperFolder) Then
                For Each item As String In Directory.GetFiles(obj.WallpaperFolder)
                    If Me.CheckIfImg(item) Then
                        intCount += 1
                    End If
                Next
            End If
            node.NodeNumber = intCount

        End If
    End Sub

#End Region

    Private Sub UpdateParentCount()
        If frmDownloadCenter.Instance IsNot Nothing Then

            Dim iCount As Integer = 0
            Dim listPaths As New List(Of String)
            listPaths.Add("iPhone")
            listPaths.Add("iPad")
            If Common.VerIs30() Then
                listPaths.Add("Android")
            End If
            iCount = frmDownloadWallPaper.WallpaperLoaclObject.GetTotalWallpaperCount(listPaths)
            frmDownloadCenter.Instance.UpdateCompleteCount(DownloadView.Paper, iCount)
        End If
    End Sub


    Public Property SysApplication()
        Get
            Return Me.mApplication
        End Get
        Set(ByVal value)
            Me.mApplication = value
            Dim frm As MainForm = CType(mApplication, MainForm)
            If Common.VerIs30() Then
                mUsbDevices.Clear()
                mUsbDevices.AddRange(frm.UsbDevices.Values)
            Else
                Me.mDevice = frm.mDevice
            End If
            'Me.btnImportTo.Enabled = True
            'me.Device=me.mApplication
        End Set
    End Property

    Public Class WallpaperLoaclObject
        Private mName As String = String.Empty
        'Private mFolder As String = String.Empty
        Private mListView As tbListView = Nothing
        Private mSmallImageList As System.Windows.Forms.ImageList = Nothing
        Private mLargeImageList As System.Windows.Forms.ImageList = Nothing
        Private mSize As New Size(156, 156)
        Private mDefaultImasg As Image = Nothing
        Private mblnLoaded As Boolean = False
        Private mNode As tbTreeNode = Nothing



        Property Name() As String
            Get
                Return Me.mName
            End Get
            Set(ByVal value As String)
                Me.mName = value
            End Set
        End Property

        ReadOnly Property WallpaperFolder() As String
            Get
                Dim strFolder As String = Path.Combine(IniSetting.GetDownloadWallpaperFolder, Me.mName)
                Folder.CheckFolder(strFolder)
                Return strFolder
            End Get
        End Property

        Property ListView() As tbListView
            Get
                Return Me.mListView
            End Get
            Set(ByVal value As tbListView)
                Me.mListView = value
            End Set
        End Property

        Property SmallImageList() As System.Windows.Forms.ImageList
            Get
                Return Me.mSmallImageList
            End Get
            Set(ByVal value As System.Windows.Forms.ImageList)
                Me.mSmallImageList = value
            End Set
        End Property

        Property LargeImageList() As System.Windows.Forms.ImageList
            Get
                Return Me.mLargeImageList
            End Get
            Set(ByVal value As System.Windows.Forms.ImageList)
                Me.mLargeImageList = value
            End Set
        End Property

        Property Size() As Size
            Get
                Return Me.mSize
            End Get
            Set(ByVal value As Size)
                Me.mSize = value
            End Set
        End Property

        Property DefaultImage() As Image
            Get
                Return Me.mDefaultImasg
            End Get
            Set(ByVal value As Image)
                Me.mDefaultImasg = value
            End Set
        End Property

        Property Loaded() As Boolean
            Get
                Return Me.mblnLoaded
            End Get
            Set(ByVal value As Boolean)
                Me.mblnLoaded = value
            End Set
        End Property

        Property Node() As tbTreeNode
            Get
                Return Me.mNode
            End Get
            Set(ByVal value As tbTreeNode)
                Me.mNode = value
            End Set
        End Property

        Sub New(ByVal strName As String, ByVal largeImageSize As Size, ByVal container As Panel, ByVal defalutImg As Image)
            Me.Name = strName
            Folder.CheckFolder(Me.WallpaperFolder)
            Me.Size = largeImageSize
            Me.DefaultImage = defalutImg
            Me.InitImageList(Me.SmallImageList, Me.LargeImageList, Me.Size)
            Me.InitListView(Me.SmallImageList, Me.LargeImageList, container)
        End Sub

        Private Sub InitImageList(ByRef imlSmall As System.Windows.Forms.ImageList, ByRef imlLarge As System.Windows.Forms.ImageList, ByVal imgSize As Size)
            imlSmall = New System.Windows.Forms.ImageList()
            imlLarge = New System.Windows.Forms.ImageList()

            With imlSmall
                .ColorDepth = ColorDepth.Depth32Bit
                .ImageSize = New Size(75, 75)
            End With

            With imlLarge
                .ColorDepth = ColorDepth.Depth32Bit
                .ImageSize = imgSize
            End With
        End Sub

        Private Sub InitListView(ByVal imlSmall As System.Windows.Forms.ImageList, ByVal imlLarge As System.Windows.Forms.ImageList, ByVal contral As Panel)
            Me.ListView = New tbListView
            With Me.ListView
                .Items.Clear()
                .Columns.Clear()
                .GridLines = False
                .FullRowSelect = True
                .HideSelection = False
                .MultiSelect = True
                .Dock = DockStyle.Fill
                .BorderStyle = BorderStyle.None
                .View = View.LargeIcon
                .SmallImageList = imlSmall
                .LargeImageList = imlLarge
                .OwnerDraw = True
                .BackColor = Color.FromArgb(250, 251, 252)
            End With

            contral.Controls.Add(Me.ListView)
            Me.ListView.SetIconSpacing(New Size(0, 20))
        End Sub

        Public Function SetNodeNumber() As Integer
            Dim intCount As Integer = 0
            If Me.Node IsNot Nothing Then
                intCount = Me.ListView.Items.Count
                Me.Node.NodeNumber = Me.ListView.Items.Count
            End If
            Return intCount
        End Function

        Public Shared Function GetTotalWallpaperCount(ByVal listName As List(Of String)) As Integer
            Dim total As Integer = 0
            For Each deviceName As String In listName
                Dim wallpaperFolder As String = Path.Combine(IniSetting.GetDownloadWallpaperFolder, deviceName)
                Folder.CheckFolder(wallpaperFolder)
                total += Directory.GetFiles(wallpaperFolder, "*.jpg").Length
                total += Directory.GetFiles(wallpaperFolder, "*.png").Length
            Next
            Return total
        End Function

    End Class

    Private Sub btnSelectAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectAll.Click
        Try
            For Each Item As ListViewItem In Me.GetCurrentListview.Items
                Item.Selected = True
            Next
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnSelectAll_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectAll.SizeChanged, btnSelectAll.SizeChanged
        'btnSelectAll.Left = Me.btnRefresh.Left - btnSelectAll.Width
        'Me.btnShareTo.Left = btnSelectAll.Left - btnShareTo.Width

        Me.btnSelectAll.Location = New Point(Me.Width - Me.btnSelectAll.Width - 15, Me.btnSelectAll.Top)
        Me.btnShareTo.Location = New Point(Me.btnSelectAll.Left - Me.btnShareTo.Width - 10, Me.btnShareTo.Top)
    End Sub

    Private Sub btnImportTo_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImportTo.SizeChanged, btnDelete.SizeChanged
        '  Me.btnDelete.Location = New Point(Me.btnImportTo.Right, Me.btnDelete.Top)
        Me.btnDelete.Left = Me.btnImportTo.Right + 1 '+ 5
        Me.btnRefresh.Left = Me.btnDelete.Right + 1 '+ 5
    End Sub

    Private Sub SetCount(ByVal obj As WallpaperLoaclObject)
        If Me.mfrmPhoto IsNot Nothing AndAlso obj.Node IsNot Nothing AndAlso obj.Node.Checked AndAlso obj.ListView IsNot Nothing Then
            Me.mfrmPhoto.SetTotalCount(obj.ListView.Items.Count)
        End If
    End Sub

    Private Sub btnShareTo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShareTo.Click
        Dim currentListview As tbListView = Me.GetCurrentListview
        If currentListview Is Nothing OrElse currentListview.SelectedItems.Count = 0 Then
            Return
        End If
        If currentListview.SelectedItems.Count > 1 Then
            '"壁纸一次只能分享一张。"
            tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.MuchWallpaperForWeibo"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
            Return
        Else


            Dim img As Image = Common.ImageFromFile(currentListview.SelectedItems(0).Name)
            If img Is Nothing Then
                Return
            End If

            Me.cmsWeibo.Tag = img
            Me.cmsWeibo.Show(sender, New Point(0, CType(sender, Control).Height))
        End If
    End Sub

    Private Sub btnAndroid_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAndroid.CheckedChanged
        If Me.btnAndroid.Checked Then
            If Not Me.mSelectedType = WallpaperLoaclType.Android Then
                Me.mSelectedType = WallpaperLoaclType.Android
                'Me.LoadWallpaper(True)
                Me.SwitchWallpaperType(mSelectedType, Nothing)
            End If

        End If

    End Sub

    Public Sub SwithToAndroid()
        Me.btnAndroid.Checked = True
    End Sub
    Public Sub SwitchToIphone()
        Me.btnIPhone.Checked = True
    End Sub


    Private Sub btnIPhone_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnIPhone.CheckedChanged
        If Me.btnIPhone.Checked Then
            Me.btnIPhone.Font = Me.mFontArialBold

            Me.mSelectedType = WallpaperLoaclType.iPhone
            Me.SwitchWallpaperType(mSelectedType, Nothing)
        Else
            Me.btnIPhone.Font = Me.mFontArialRegular
        End If
    End Sub

    Private Sub btniPad_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btniPad.CheckedChanged
        If Me.btniPad.Checked Then
            Me.btniPad.Font = Me.mFontArialBold

            Me.mSelectedType = WallpaperLoaclType.iPad
            Me.SwitchWallpaperType(mSelectedType, Nothing)
        Else
            Me.btniPad.Font = Me.mFontArialRegular

        End If
    End Sub

    Private Sub btniPad_FontChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnIPhone.FontChanged, btniPad.FontChanged

    End Sub

    Private Sub frmDownloadWallPaper_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown
        Me.btniPad.Font = Me.mFontArialRegular
        Me.btnIPhone.Font = Me.mFontArialRegular
    End Sub
End Class

'Public Enum WallpaperLoaclType
'    iPhone
'    iPad
'    Android
'End Enum

