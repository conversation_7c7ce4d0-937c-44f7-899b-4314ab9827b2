﻿Imports System.Threading
Imports System.IO

Public Class frmAppDownloaded

    Private mQueuePackages As Queue(Of PackageInfo)
    Private mCurrentPackage As PackageInfo

    Private mTimeInitFormShow As Integer = 20
    Private mDisplaySize As New Size(32, 32)
    Private mlstDownloadFiles As New List(Of String)

#Region "--- 初始化 ---"

    Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mQueuePackages = New Queue(Of PackageInfo)
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong
        Me.tbShowTitleOnForm = True
        'Me.TopLevel = True

        Me.tbGuiBackground = My.Resources.pnl_bg_info
        Me.FilletRadius = 5
        Me.BackColor = Color.FromArgb(179, 178, 171)
        'Me.TopLevel = True
        Me.ForeColor = Color.Black

        Me.lblIPAName.Text = String.Empty

        Me.Location = New Point(Screen.PrimaryScreen.WorkingArea.Width - Me.Width, Screen.PrimaryScreen.WorkingArea.Height - Me.Height - 3)
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.ShowNextPackage()

        If Not Me.CheckDeviceConnection() Then
            Me.TimerToClose.Start()
            Me.lblInstall.Visible = False
            Me.lblCloseTime.Visible = True
        Else
            Me.lblInstall.Visible = True
            Me.lblCloseTime.Visible = False
        End If

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("Download.Cell.DoenloadCompleted")
        Me.lblInstall.Text = Me.Language.GetString("Download.Button.Install")
        Me.lblNext.Text = Me.Language.GetString("File.Button.Next")
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub lblInstall_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblInstall.LinkClicked
        Dim deviceManager As MobileDeviceManager = iTong.Device.MobileDeviceManager.Instance
        Dim listDevice As New List(Of iPhoneDevice)

        '因为设备多次插拔的时候可能有出现空的情况
        For Each device As iPhoneDevice In deviceManager.ConnectedDevices
            If device.Identifier.Length > 0 AndAlso device.IsConnected Then
                listDevice.Add(device)
            End If
        Next

        '如果一台设备就直接安装　多台设备才显示menu
        If listDevice.Count = 1 Then
            Me.InstallSoft2Device(listDevice.Item(0))
            If Me.mQueuePackages.Count = 0 Then
                Me.Close()
            Else
                Me.ShowNextPackage()
            End If
        ElseIf deviceManager.ConnectedDevices.Count > 1 Then
            Me.ShowInstallMenu(False, deviceManager)
        Else
            '"没有设备接入 ..."
            tbMessageBox.Show(Me.Language.GetString("Download.Message.NoDeviceConnected"), _
                                                Me.Language.GetString("Common.Info"), _
                                                MessageBoxButtons.OK, _
                                                MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub ToolStripMenuItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim deviceManager As MobileDeviceManager = iTong.Device.MobileDeviceManager.Instance

        If deviceManager IsNot Nothing Then
            If CType(sender, ToolStripMenuItem).Tag IsNot Nothing AndAlso CType(sender, ToolStripMenuItem).Tag = "AllDevice" Then
                For Each device As iPhoneDevice In deviceManager.ConnectedDevices
                    Me.InstallSoft2Device(device)
                Next
            Else
                For Each device As iPhoneDevice In deviceManager.ConnectedDevices
                    If device.Identifier = CType(sender, ToolStripMenuItem).Tag Then
                        Me.InstallSoft2Device(device)
                        Exit For
                    End If
                Next

            End If

        End If

        If Me.mQueuePackages.Count = 0 Then
            Me.Close()
        Else
            Me.ShowNextPackage()
        End If

    End Sub

    Private Sub lblNext_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblNext.LinkClicked
        If Me.mQueuePackages.Count = 0 Then
            Me.Close()
        Else
            Me.ShowNextPackage()
        End If
    End Sub

    Private Sub TimerToClose_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TimerToClose.Tick
        '"下载完成，{0}秒后自动关闭"
        Me.lblCloseTime.Text = String.Format(Me.Language.GetString("Download.Message.AutoClose"), Me.mTimeInitFormShow)
        Me.mTimeInitFormShow = Me.mTimeInitFormShow - 1

        If Me.mTimeInitFormShow <= 0 Then
            Me.Close()
        End If

    End Sub

    Private Sub SoftwareDownloadNotifyForm_Resize(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Resize
        Utility.SetRegionRect(Me, 1, 1, 2, 2, 0)
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Sub ShowNextPackage()
        If Me.mQueuePackages.Count = 0 Then
            Return
        End If

        Me.mCurrentPackage = Me.mQueuePackages.Dequeue
        If Me.mCurrentPackage Is Nothing Then
            Return
        End If

        If Me.mCurrentPackage.AppIcon Is Nothing Then
            Me.btnIconSmall.tbIconImage = Utility.GetThumbnail(My.Resources.app_icon_noapp, Me.mDisplaySize)
        Else
            Me.btnIconSmall.tbIconImage = Utility.GetThumbnail(Me.mCurrentPackage.AppIcon, Me.mDisplaySize)
        End If

        Me.btnIconSmall.tbBadgeNumber = Me.mQueuePackages.Count + 1

        ApplePackageReader.OperatePackageNameForBT(mCurrentPackage)
        Dim strName As String = IIf(String.IsNullOrEmpty(mCurrentPackage.DisplayName), mCurrentPackage.Name, mCurrentPackage.DisplayName)

        Me.lblIPAName.Text = strName
        If Me.lblIPAName.Text.Length = 0 Then
            Me.lblIPAName.Text = Path.GetFileNameWithoutExtension(Me.mCurrentPackage.PackagePath)
        End If

        If Me.mCurrentPackage.Version.Length > 0 AndAlso Me.mCurrentPackage.FileSizeDisplay.Length > 0 Then
            Me.lblSizeAndVersion.Text = Me.mCurrentPackage.Version & " | " & Me.mCurrentPackage.FileSizeDisplay
        Else
            Me.lblSizeAndVersion.Text = Me.mCurrentPackage.Version & Me.mCurrentPackage.FileSizeDisplay
        End If

        Me.SetNextLabelVisible()
    End Sub

    '当只有一个 ipa 文件的时候下一个按钮不显示
    Private Sub SetNextLabelVisible()
        If Me.InvokeRequired Then
            Me.Invoke(New Threading.ThreadStart(AddressOf SetNextLabelVisible))
        Else
            If Me.btnIconSmall.tbBadgeNumber <= 1 Then
                Me.lblNext.Visible = False
            Else
                Me.lblNext.Visible = True
            End If
        End If
    End Sub

    Private Sub ShowInstallMenu(ByVal isShowAtMousePoint As Boolean, ByVal deviceManager As IMobileDeviceManager)
        '清空
        Me.menuDevice.Items.Clear()

        '获取当前所有设备名称

        If deviceManager IsNot Nothing Then
            Dim menuAllItem As New ToolStripMenuItem(Me.Language.GetString("Download.Message.InstallToAlDevice")) '"安装到所有设备"
            menuAllItem.Tag = "AllDevice"

            RemoveHandler menuAllItem.Click, AddressOf ToolStripMenuItem_Click
            AddHandler menuAllItem.Click, AddressOf ToolStripMenuItem_Click

            Me.menuDevice.Items.Add(menuAllItem)                   '"所有设备"
            Me.menuDevice.Items.Add(New ToolStripSeparator)

            If deviceManager.ConnectedDevices.Count = 0 Then
                Dim mnuItem As ToolStripMenuItem = New ToolStripMenuItem(Me.Language.GetString("Download.Message.NoDeviceConnected")) '"没有设备接入 ..."
                mnuItem.Enabled = False
                Me.menuDevice.Items.Add(mnuItem)
            End If

            For Each device As iPhoneDevice In deviceManager.ConnectedDevices
                '如果设备重启后会使identitier 会变成 ""
                If device.Identifier.Length = 0 Then
                    Continue For
                End If

                Dim menuItem As New ToolStripMenuItem(device.DeviceName)
                menuItem.Image = device.FuncDeviceIcon
                menuItem.Tag = device.Identifier  '保存当前identifier，以便安装的时候可以获取到安装到哪个设备去

                RemoveHandler menuItem.Click, AddressOf ToolStripMenuItem_Click
                AddHandler menuItem.Click, AddressOf ToolStripMenuItem_Click
                Me.menuDevice.Items.Add(menuItem)
            Next
        End If

        If isShowAtMousePoint = False Then
            Dim intCount As Integer = (Me.menuDevice.Items.Count - 1) * Me.menuDevice.Items(0).Height + Me.lblInstall.Height
            menuDevice.Show(Me.lblInstall, New Point(0, -intCount))
        Else
            menuDevice.Show(Windows.Forms.Cursor.Position)
        End If
    End Sub

    Private Sub InstallSoft2Device(ByVal device As iPhoneDevice)
        If Me.mCurrentPackage IsNot Nothing AndAlso Me.mCurrentPackage.PackagePath.Trim.Length > 0 Then
            device.FuncInstallApplication(Me.mCurrentPackage.PackagePath)
            'Me.m_Application.SetMainBalloonTipText(Me.m_CurrentPkg.Name & vbCrLf & Me.Language.GetString("Device.Install.ToolTip.AddInstallQueueSucceed"), 2)
        End If
    End Sub

    '验证当前是否有设备连接
    Private Function CheckDeviceConnection() As Boolean
        Dim blnConnection As Boolean = False

        For Each device As iPhoneDevice In iTong.Device.MobileDeviceManager.Instance().ConnectedDevices
            If device.Identifier.Length > 0 Then
                blnConnection = True
                Exit For
            End If
        Next

        Return blnConnection
    End Function

#End Region

#Region "--- 对外方法 ---"

    '下载完成后添加 Package 到窗体并显示出来
    Public Sub AddPackage(ByVal pkg As PackageInfo)
        If pkg Is Nothing Then
            Return
        End If

        If Me.mlstDownloadFiles.Contains(pkg.PackagePath) Then
            Return
        Else
            Me.mlstDownloadFiles.Add(pkg.PackagePath)
        End If

        Me.mQueuePackages.Enqueue(pkg)
        Me.btnIconSmall.tbBadgeNumber = Me.mQueuePackages.Count + 1
        Me.SetNextLabelVisible()
    End Sub

#End Region

End Class