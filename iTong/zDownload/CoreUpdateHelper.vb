﻿'移到CoreModuleCS
Imports System.Threading

Public Class CoreUpdateHelper
    Implements IDisposable

    Private mLanguage As LanguageInterface
    Private mAppleUpdate As AppleUpdate
    'Private mAppleDownload As AppleDownload
    Private mTdCheckNewCorUpdateInstance As Thread
    Private mDownloadManage As MultiThreadDownload

    Private mTdAnalyse2Download As Thread
    Private mQueueAnalyse As New Queue(Of MultiThreadDownloadItemInfo)

    'Private mTdAutoInstall As Thread
    Private mQueueAutoInstall As New Queue(Of MultiThreadDownloadItem)
    Private mBgwAutoInstall As New System.ComponentModel.BackgroundWorker

    Public Event CheckDownloadEventHandler As EventHandler(Of CheckDownloadArgs)
    Public Event CombineSingling As EventHandler(Of MultiThreadDownloadEventArgs)
    Public Event TaskExist As EventHandler(Of MultiThreadDownloadItem)
    Public Event TaskInstalled As EventHandler(Of MultiThreadDownloadItem)
    Public Event ShowAppleIdLogin As EventHandler(Of ShowAppleIdLoginArgs)
    Public Event ShowAppDownloaded As EventHandler(Of ShowAppDownloadedArgs)

    Private mDeviceMgr As MobileDeviceManager = Nothing
    Private mdictUserAccounts As New Dictionary(Of String, String) '用来存在用户Apple ID和密码


    Private mTdSoftwareAgainPulls As Thread '企业签名包 安装前判断签名是否过期，可以重新下载 2015-07-15 by chenbihai 
    Private mQueueSoftwareAgainPulls As New Queue(Of PackageInfo)

#Region "--- 构造方法 ---"

    Private Sub New()
        Me.mLanguage = LanguageInterface.Instance()
        'Me.mAppleUpdate = Utility.LoadClass(Path.Combine(Folder.AppFolder, "CoreUpdate.dll"), "AppleUpdate", New Object() {})
        'Me.mAppleDownload = Utility.LoadClass(Path.Combine(Folder.AppFolder, "CoreUpdate.dll"), "AppleDownload", New Object() {})
        Me.mAppleUpdate = New AppleUpdate()
        'Me.mAppleDownload = New AppleDownload()

        Me.mDeviceMgr = MobileDeviceManager.Instance()
        Me.mDownloadManage = tbPlugins.DownloadManager

        Me.mBgwAutoInstall.WorkerSupportsCancellation = True
        RemoveHandler mBgwAutoInstall.DoWork, AddressOf OnBgwAutoInstallDoWork
        AddHandler mBgwAutoInstall.DoWork, AddressOf OnBgwAutoInstallDoWork
        RemoveHandler mBgwAutoInstall.RunWorkerCompleted, AddressOf OnOnBgwAutoInstallRunWorkerCompleted
        AddHandler mBgwAutoInstall.RunWorkerCompleted, AddressOf OnOnBgwAutoInstallRunWorkerCompleted

        RemoveHandler Me.mDownloadManage.DownloadItemCompleted, AddressOf OnDownloadItemCompleted
        AddHandler Me.mDownloadManage.DownloadItemCompleted, AddressOf OnDownloadItemCompleted

        RemoveHandler Me.mDownloadManage.TaskStart, AddressOf OnTaskStart
        AddHandler Me.mDownloadManage.TaskStart, AddressOf OnTaskStart

        RemoveHandler Me.mDownloadManage.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed
        AddHandler Me.mDownloadManage.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed

        RemoveHandler Me.mDownloadManage.TaskExist, AddressOf OnTaskExist
        AddHandler Me.mDownloadManage.TaskExist, AddressOf OnTaskExist

        RemoveHandler Me.mDownloadManage.TaskCombine, AddressOf OnTaskCombine
        AddHandler Me.mDownloadManage.TaskCombine, AddressOf OnTaskCombine

        RemoveHandler Me.mDownloadManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
        AddHandler Me.mDownloadManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse

        Me.mTdCheckNewCorUpdateInstance = New Thread(AddressOf Start2CheckNewInstance)
        Me.mTdCheckNewCorUpdateInstance.IsBackground = True
        Me.mTdCheckNewCorUpdateInstance.SetApartmentState(ApartmentState.STA)
        Me.mTdCheckNewCorUpdateInstance.Priority = ThreadPriority.Lowest
        Me.mTdCheckNewCorUpdateInstance.Start()

        '收集用户是否有绑定apple id
        If IniSetting.GetUserAccounts().Count > 0 Then
            ActionCollectHelper.AppleIDAccition(Nothing, ModelKey.SummaryAppleIdBinding, ActionDataType.View)
        Else
            ActionCollectHelper.AppleIDAccition(Nothing, ModelKey.SummaryAppleIdUnBinding, ActionDataType.View)
        End If

        '双重认证
        RemoveHandler AppleDownload.SecurityCode, AddressOf OnSecurityCode
        AddHandler AppleDownload.SecurityCode, AddressOf OnSecurityCode
    End Sub

    Private Sub Start2CheckNewInstance()
        '检查是否有更新的dll文件，如果有的话则下载
    End Sub

#End Region

#Region "--- 单实例 ---"

    Private Shared mInstance As CoreUpdateHelper = Nothing
    Private Shared ReadOnly locker As New Object
    Private Shared m_AndroidDevice As AndroidDevice = Nothing
    Public Shared Function Instance(Optional ByVal mobileDevice As IDevice = Nothing) As CoreUpdateHelper
        If mInstance Is Nothing Then
            SyncLock locker
                If mInstance Is Nothing Then
                    mInstance = New CoreUpdateHelper()
                End If
            End SyncLock
        End If

        If mobileDevice IsNot Nothing AndAlso TypeOf mobileDevice Is AndroidDevice Then
            m_AndroidDevice = CType(mobileDevice, AndroidDevice)
        End If

        Return mInstance
    End Function

#End Region

#Region "--- 对外属性 ---"

    Public ReadOnly Property AppleUpdate() As AppleUpdate
        Get
            Return Me.mAppleUpdate
        End Get
    End Property

#End Region

#Region " IDisposable Support "

    Private disposedValue As Boolean = False        ' 检测冗余的调用

    ' IDisposable
    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If

            ' TODO: 释放共享的非托管资源
            Try
                If Me.mTdCheckNewCorUpdateInstance IsNot Nothing AndAlso Me.mTdCheckNewCorUpdateInstance.ThreadState <> ThreadState.Stopped Then
                    Me.mTdCheckNewCorUpdateInstance.Abort()
                End If
            Catch
            End Try

            Try
                If Me.mTdAnalyse2Download IsNot Nothing AndAlso Me.mTdAnalyse2Download.ThreadState <> ThreadState.Stopped Then
                    Me.mTdAnalyse2Download.Abort()
                End If

                Me.mQueueAnalyse.Clear()
                Me.mQueueAnalyse = Nothing
            Catch
            End Try

            Try
                If Me.mTdSoftwareAgainPulls IsNot Nothing AndAlso Me.mTdSoftwareAgainPulls.ThreadState <> ThreadState.Stopped Then
                    Me.mTdSoftwareAgainPulls.Abort()
                End If

                Me.mQueueSoftwareAgainPulls.Clear()
                Me.mQueueSoftwareAgainPulls = Nothing
            Catch ex As Exception

            End Try

            Try
                If Me.mBgwAutoInstall.IsBusy Then
                    Me.mBgwAutoInstall.CancelAsync()
                End If

                Me.mQueueAutoInstall.Clear()
                Me.mQueueAutoInstall = Nothing
            Catch
            End Try

            Try
                'Utility.tbCallByName(CoreUpdateHelper.mInstance.mAppleDownload, "Dispose", Utility.enmCallType.Method, New Object() {})
            Catch ex As Exception
                Common.LogException(ex.ToString)
            End Try
        End If
        Me.disposedValue = True
    End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

#End Region

#Region "--- 登录appstore ---"

    Public Function CheckLogin(ByVal strAppleId As String) As Boolean
        'Return Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "CheckLogin", Utility.enmCallType.Method, strAppleId)
        Return AppleDownload.CheckLogin(strAppleId)
    End Function

    Public Function AccountInfos() As List(Of AppleAccountInfo)
        'Return Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "AccountInfos", Utility.enmCallType.Method, Nothing)
        Return AppleDownload.AccountInfos()
    End Function

    Public Function FailedAccountDict() As Dictionary(Of String, AppleAccountInfo)
        'Return Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "FailedAccountDict", Utility.enmCallType.Method, Nothing)
        Return AppleDownload.FailedAccountDict()
    End Function

    Public Function Login(ByVal strAppleId As String, ByVal strPassword As String) As SoftDownloadErrorState
        'Dim objLoginAppleId() As Object = New Object() {strAppleId, strPassword}
        'Dim state As SoftDownloadErrorState = Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "LoginAppleId", Utility.enmCallType.Method, objLoginAppleId)
        'objLoginAppleId = Nothing

        Dim state As SoftDownloadErrorState = AppleDownload.LoginAppleId(strAppleId, strPassword)

        If state = SoftDownloadErrorState.Succeed Then
            ActionCollectHelper.AppleIDAccitionLogin(Nothing, ModelKey.SummaryAppleIdLogin, ActionDataType.Click, FunctionSucceed.Succeed, "")
        Else
            '用户登陆失败统计
            Dim strError As String = String.Format("{0}:{1}:{2}", IIf(Common.iTunesIsPE64, "64", "32"), iTunesHelper.iTunesVersionString, Me.LoginFailedXml(strAppleId))
            ActionCollectHelper.AppleIDAccitionLogin(Nothing, ModelKey.SummaryAppleIdLogin, ActionDataType.Click, FunctionSucceed.Failure, strError)

            '发送到服务器，失败原因统计
            Me.UploadLoginFailedInfo(strAppleId, state)

            '弹出失败具体原因窗体
            Me.ShowLoginFailedForm(strAppleId)
        End If

DoExit:
        Return state
    End Function

    Private Function GetAppMsg(ByVal strAppleId As String) As AppleMsg
        Dim msg As AppleMsg = Nothing

        If Not FailedAccountDict.ContainsKey(strAppleId) Then
            GoTo DoExit
        End If

        Dim info As AppleAccountInfo = FailedAccountDict(strAppleId)
        msg = info.AppleMsg

DoExit:
        Return msg
    End Function

    Public Sub ShowLoginFailedForm(ByVal strAppleId As String)
        Try
            Dim aMsg As AppleMsg = Me.GetAppMsg(strAppleId)
            If aMsg Is Nothing OrElse Not aMsg.HasDialog OrElse aMsg.Buttons.Count = 0 Then
                Return
            End If


            Dim btns As MessageBoxButtons = MessageBoxButtons.OK
            If aMsg.Buttons.Count = 2 Then
                btns = MessageBoxButtons.OKCancel
            ElseIf aMsg.Buttons.Count > 2 Then
                Return
            End If

            Dim btnOK As AppleButton = Nothing
            Dim listText As New List(Of String)
            For Each btn As AppleButton In aMsg.Buttons.Values
                listText.Add(btn.Text)
                If btn.Button = AppleButtonType.OK Then
                    btnOK = btn
                End If
            Next


            If tbMessageBox.Show(Me, _
                     aMsg.Message, _
                      LanguageInterface.Instance().GetString("Common.Info"), _
                      btns, _
                      MessageBoxIcon.Information, _
                      MessageBoxDefaultButton.Button1, "", False, listText.ToArray(), aMsg.Explanation) = Windows.Forms.DialogResult.OK Then

                If btnOK.Type = AppleActionType.OpenURL Then
                    Common.OpenExplorer(btnOK.Action.ToString)

                ElseIf aMsg.State = SoftDownloadErrorState.LoginFailedWithAppleIdNeverUseIniTunes Then '2015-07-23 appleid需要检测时 点击确定后 跳链接 打开itunes
                    Common.OpenExplorer(String.Format("https://buy.itunes.apple.com/WebObjects/MZFinance.woa/wa/accountSummary?guid={0}", MachineInfo.CurrentInfo.Guid))

                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "ShowLoginFailedForm")
        End Try
    End Sub

    Private Sub UploadLoginFailedInfo(ByVal strAppleId As String, ByVal state As SoftDownloadErrorState)
        Dim arg As New AppleBySoftStatusArgs(True)
        arg.AppleAccount = strAppleId
        arg.ItemId = "0"
        arg.Type = state

        Dim aMsg As AppleMsg = Me.GetAppMsg(strAppleId)
        If aMsg IsNot Nothing Then
            arg.Info = aMsg.ToString()
        Else
            arg.Info = Me.LoginFailedXml(strAppleId)
        End If

        arg.IsTBAccount = False

        '收集用户的itunes版本号
        Try
            arg.Info = iTunesHelper.iTunesVersionString & " " & MachineInfo.CurrentInfo.Guid & arg.Info
        Catch
        End Try

        HtmlHelper.Start2UploadAppleBySoftStatus(arg)
    End Sub

    '取得登陆账号的列表。
    Public Function AccountDict() As Dictionary(Of String, AppleAccountInfo)
        'Return Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "AccountDict", Utility.enmCallType.Method, Nothing)
        Return AppleDownload.AccountDict()
    End Function

    Public Function ShowLoginFormEx(ByVal dev As iPhoneDevice, ByVal mode As LoginMode) As String
        'Dim frmLogin As New frmiTunesLoginV3(dev, mode)
        'Return Me.ShowLoginFormInvoke(frmLogin)
        Dim args As New ShowAppleIdLoginArgs()
        args.Device = dev
        args.Mode = mode
        RaiseEvent ShowAppleIdLogin(Me, args)
        Return args.Id
    End Function

    Public Function ShowLoginForm(ByVal info As PackageInfo, ByVal mode As LoginMode) As String
        If info IsNot Nothing AndAlso info.Tag Is Nothing AndAlso Not String.IsNullOrEmpty(info.DeviceId) Then
            info.Tag = Me.GetDeviceById(info.DeviceId)
        End If
        'Dim frmLogin As New frmiTunesLoginV3(info, mode)
        'Return Me.ShowLoginFormInvoke(frmLogin)
        Dim args As New ShowAppleIdLoginArgs()
        args.Info = info
        args.Mode = mode
        RaiseEvent ShowAppleIdLogin(Me, args)
        Return args.Id
    End Function

    Public Function LoginFailedXml(ByVal appleID As String) As String
        If AppleDownload.FailedAccountDict().ContainsKey(appleID) Then
            Dim msg As AppleMsg = AppleDownload.FailedAccountDict()(appleID).AppleMsg
            If Not String.IsNullOrEmpty(msg.CustomerMessage) Then
                Return msg.CustomerMessage
            ElseIf Not String.IsNullOrEmpty(msg.Message) Then
                Return msg.Message
            Else
                Return msg.ToString()
            End If
        Else
            Return "Unknown"
        End If
    End Function

#End Region

#Region "--- AppStore购买 ---"

    Private Function GetDeviceById(ByVal deviceID As String) As IDevice
        Dim dev As IDevice = Nothing

        If Not String.IsNullOrEmpty(deviceID) Then
            For Each Item As IDevice In Me.mDeviceMgr.ConnectedDevices
                If deviceID = Item.DeviceID Then
                    dev = Item
                    Exit For
                End If
            Next
        End If

        Return dev
    End Function

    Private mDictReplaceAppleID As New Dictionary(Of String, String)
    Public Function DownloadAppleSoft(ByVal info As PackageInfo, ByVal isFromUpdate As Boolean, Optional ByVal downloadClass As ResourceClass = ResourceClass.Software, Optional ByVal strTongbuUrl As String = "") As Boolean
        Dim isGotoDown As Boolean = False

        Try
            Dim blnOverseasVersion As Boolean = Folder.LangType <> LanguageType.zh_CN           '海外版

            '默认用用户绑定的apple id进行下载
            Dim appleID As String = info.AppleId

            If String.IsNullOrEmpty(appleID) Then
                appleID = IniSetting.GetDefaultAppleID(Me.GetDeviceById(info.DeviceId))
            Else
                If mDictReplaceAppleID.ContainsKey(appleID) Then
                    appleID = mDictReplaceAppleID(appleID)
                End If
            End If

            If String.IsNullOrEmpty(appleID) OrElse Not Me.CheckLogin(appleID) Then
                Dim strLoginedAccount As String = Me.ShowLoginForm(info, IIf(isFromUpdate, LoginMode.AppUpdate, LoginMode.DownShareApp))

                '记住上一次替换的AppleID
                If Not String.IsNullOrEmpty(appleID) AndAlso Not String.IsNullOrEmpty(strLoginedAccount) AndAlso strLoginedAccount <> appleID Then
                    mDictReplaceAppleID(appleID) = strLoginedAccount
                End If

                If Not String.IsNullOrEmpty(strLoginedAccount) Then
                    appleID = strLoginedAccount
                End If

                '海外版如果用户取消登陆就不进行下载，只有登陆成功后才进行下载。
                If blnOverseasVersion Then
                    Dim args As New ShowAppleIdLoginArgs
                    RaiseEvent ShowAppleIdLogin(Me, args)
                    If args.Id.Length > 0 AndAlso args.LoginSucceed Then
                        appleID = args.Id
                    Else
                        Return isGotoDown
                    End If
                End If

                If Me.CheckLogin(appleID) Then
                    isGotoDown = True
                End If
            Else
                isGotoDown = True
            End If

            If Not isGotoDown Then
                '触发下载取消请求
                RaiseEvent CheckDownloadEventHandler(Me, New CheckDownloadArgs(info.Identifier, info.ItemId, info.Version, SoftDownloadErrorState.Nomal))

                Return isGotoDown
            End If

            '指定该任务是否更新，更新的时候不用校验是否收费软件
            If isFromUpdate AndAlso (String.IsNullOrEmpty(info.LoginAppleId) OrElse info.LoginAppleId = appleID) Then
                info.IsUpdate = True
            End If

            info.LoginAppleId = appleID
            '下载
            Dim downInfo As MultiThreadDownloadItemInfo = Me.GetNewDownloadInfoForAppStore(info, downloadClass)
            downInfo.SourceUrl = strTongbuUrl               '如果从iTunes下载的时候把同步下载地址也存进来，如果iTunes出现下载失败的时候就用同步下载地址重新下载。
            Me.mDownloadManage.NewTask(downInfo)

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

        Return isGotoDown
    End Function

    '下载正版软件的接口
    'Public Function DownloadAppleSoftOld(ByVal info As PackageInfo, Optional ByVal downloadClass As ResourceClass = ResourceClass.Software) As Boolean
    '    Dim isGotoDown As Boolean = False

    '    Try
    '        '升级
    '        Dim strLoginAppleId As String = info.AppleId
    '        If Common.VerIs30 Then
    '            '默认用用户绑定的apple id进行下载
    '            strLoginAppleId = Me.DefaultUserAccount

    '            '如果用户没有绑定就引导用户绑定ID
    '            If String.IsNullOrEmpty(strLoginAppleId) Then
    '                Me.ShowLoginForm(info, False)
    '            End If

    '            If Not String.IsNullOrEmpty(strLoginAppleId) Then
    '                If Me.CheckLogin(strLoginAppleId) Then
    '                    isGotoDown = True
    '                Else
    '                    Me.ShowLoginForm(info, True)

    '                    If Me.CheckLogin(strLoginAppleId) Then
    '                        isGotoDown = True
    '                    End If
    '                End If
    '            End If

    '        Else
    '            If info.LoginAppleId.Length > 0 Then
    '                '如果有登录的appleid，则尝试用登录的去下载
    '                strLoginAppleId = info.LoginAppleId
    '            End If

    '            If Not Me.CheckLogin(strLoginAppleId) Then
    '                Dim frmLogin As New frmiTunesLogin(info)
    '                Dim frmOwner As Form = ProcForm.Instance().GetMainForm()

    '                If frmOwner IsNot Nothing Then
    '                    frmLogin.Owner = frmOwner
    '                    frmLogin.StartPosition = FormStartPosition.CenterParent
    '                End If

    '                If frmLogin.ShowDialog() = Windows.Forms.DialogResult.OK Then
    '                    strLoginAppleId = frmLogin.AppleId
    '                    isGotoDown = True
    '                End If
    '            Else
    '                isGotoDown = True
    '            End If

    '        End If

    '        If Not isGotoDown Then
    '            '触发下载取消请求
    '            RaiseEvent CheckDownloadEventHandler(Me, New CheckDownloadArgs(info.Identifier, info.Version, SoftDownloadErrorState.Nomal))

    '            Return isGotoDown
    '        End If

    '        info.LoginAppleId = strLoginAppleId

    '        '下载
    '        Dim downInfo As MultiThreadDownloadItemInfo = Me.GetNewDownloadInfoForAppStore(info)
    '        downInfo.Class = downloadClass
    '        Me.Start2AnalyseApp(downInfo)

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString)
    '    End Try

    '    Return isGotoDown
    'End Function

    '安装应用成功失败回传数据到服务器
    Private Sub SendDownloadInfo2Server(ByVal item As MultiThreadDownloadItem, ByVal type As AppInstallType, Optional state As SoftDownloadErrorState = SoftDownloadErrorState.UnKnow)
        Dim appArg As New AppInstallStatusArgs()
        Try

            If item.ItemInfo.Name.EndsWith(".ipa", StringComparison.InvariantCultureIgnoreCase) AndAlso Not String.IsNullOrEmpty(item.ItemInfo.ItemId) Then
                appArg.appleid = Convert.ToInt32(item.ItemInfo.ItemId)
            Else
                appArg.appleid = 0
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CoreUpdateHelp_SendDownloadInfo2Server")
        End Try

        appArg.AppVer = item.ItemInfo.Version
        appArg.type = "5"
        If item.ItemInfo.Jailbreak Then
            appArg.type = "7"
        End If
        appArg.FilePath = item.ItemInfo.FilePath
        appArg.source = 2
        If item.ItemInfo.Source <> TaskSource.AppleDownload Then
            appArg.source = 1
        End If

        Try
            Dim loadInfo As New AppLoadArgs()
            loadInfo.code = AppLoadArgs.GetCode(type, state)
            loadInfo.msg = item.DownloadInfo.LastErrorMsg
            loadInfo.size = item.DownloadInfo.ResourceSize
            loadInfo.source = appArg.source
            loadInfo.sp = item.DownloadInfo.Speed
            appArg.load.Add(loadInfo)
            HtmlHelper.Start2UploadAppInstallStatus(appArg)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CoreUpdateHelper_SendInstallInfo2Server")
        End Try
    End Sub

    '继续下载软件的接口
    Public Function ContinueToDownload(ByVal downInfo As MultiThreadDownloadItemInfo, ByVal isDownloadFromCacheFirst As Boolean) As Boolean
        Dim info As New PackageInfo
        With info
            .Identifier = downInfo.Identifier
            .Version = downInfo.Version
            .ItemId = downInfo.ItemId
            .Name = downInfo.Name
            .IsDownFromCacheFirst = isDownloadFromCacheFirst
            .AppleId = downInfo.AppleId
            .IsUpdate = True    '只要是继续下载的就可以认定为已经购买过了，走升级的方式,还是要改这个逻辑
        End With

        Return Me.DownloadAppleSoft(info, True)
    End Function

    ''确认购买
    'Public Sub ChargeSoft(ByVal downInfo As MultiThreadDownloadItemInfo)
    '    Dim objMakeAppleDetailInfo() As Object = New Object() {downInfo}
    '    Dim state As SoftDownloadErrorState = Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "CheckChargeCanDownload", Utility.enmCallType.Method, objMakeAppleDetailInfo)
    '    If state = SoftDownloadErrorState.Succeed Then
    '        Me.Download(downInfo)
    '    ElseIf state = SoftDownloadErrorState.NeedSecurityCode Then
    '        ''' 需要输入安全码
    '        'state = Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "CheckSecurityCode", Utility.enmCallType.Method, objMakeAppleDetailInfo)
    '    Else
    '        RaiseEvent CheckDownloadEventHandler(Me, New CheckDownloadArgs(downInfo.Identifier, downInfo.Version, state))
    '    End If
    'End Sub

    ''超过17岁的确认购买
    'Public Sub AgeMustLarge17ChargeSoft(ByVal downInfo As MultiThreadDownloadItemInfo)
    '    Dim objMakeAppleDetailInfo() As Object = New Object() {downInfo}
    '    Dim state As SoftDownloadErrorState = Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "CheckChargeAgeMustLarge17ChargeCanDownload", Utility.enmCallType.Method, objMakeAppleDetailInfo)
    '    If state = SoftDownloadErrorState.Succeed Then
    '        Me.Download(downInfo)
    '        ''ElseIf state = SoftDownloadErrorState.NeedSecurityCode Then
    '        '' 需要输入安全码

    '    Else
    '        RaiseEvent CheckDownloadEventHandler(Me, New CheckDownloadArgs(downInfo.Identifier, downInfo.Version, state))
    '    End If
    'End Sub

    '生成下载对象
    Public Function GetNewDownloadInfoForAppStore(ByVal info As PackageInfo, Optional ByVal downloadClass As ResourceClass = ResourceClass.Software) As MultiThreadDownloadItemInfo
        Dim downInfo As New MultiThreadDownloadItemInfo
        Dim strDownloadName As String = String.Empty
        Dim strName As String = info.DisplayName
        If strName.Length <= 0 Then
            strName = info.Name
        End If

        If TypeOf info Is UpdatePackageInfo AndAlso info.Version.Length = 0 Then
            '下载苹果正版软件时，命名格式（Name Version.ipa）

            '下载普通软件时，命名格式（Name_vVersion.ipa）

            strDownloadName = Common.ReplaceWinIllegalName(String.Format("{0} {1}", strName, CType(info, UpdatePackageInfo).SoftUpdatePara.AppStoreVersion))
        Else
            strDownloadName = Common.ReplaceWinIllegalName(String.Format("{0} {1}", strName, info.Version))
        End If

        With downInfo
            .Identifier = info.Identifier
            .Version = info.Version
            .ItemId = info.ItemId
            If Not info.Name.Contains(".ipa") Then
                .Name = String.Format("{0}.ipa", strDownloadName)
            Else
                .Name = info.Name
            End If

            .SaveFolder = IniSetting.GetDownloadSoftFolder()
            .AppleId = info.LoginAppleId
            .DeviceId = info.DeviceId
            .StoreId = info.StoreId
            .IsUpdate = info.IsUpdate
            .Source = TaskSource.AppleDownload
            .IsNeedTryDown = info.IsDownFromCacheFirst
            .IsRepair = info.IsRepair
            .VersionId = info.VersionId
            .Class = downloadClass

            If .Class = ResourceClass.Software Then
                .Type = ResourceType.IPA
                .Source = TaskSource.AppleDownload
            End If

            '标志下载的账号和软件本身账号不一致
            If String.Compare(info.LoginAppleId, info.AppleId, True) <> 0 Then
                .IsAccountDifferent = True
            End If
        End With

        Return downInfo
    End Function

#End Region

#Region "--- 更新从appstore ---"

    Public Function UpdateSoftInfoFromAppleData(ByVal infoFromTongbu As UpdatePackageInfo, ByVal infoFromApple As UpdatePackageInfo)
        'Return Utility.tbCallByName(CoreUpdateHelper.mInstance.mAppleUpdate, "UpdateSoftInfoFromAppleData", Utility.enmCallType.Method, New Object() {infoFromTongbu, infoFromApple})
    End Function

    Public Function CheckLocalSoftUpdate(ByVal lstPackages As List(Of PackageInfo)) As List(Of UpdatePackageInfo)
        'Return Utility.tbCallByName(CoreUpdateHelper.mInstance.mAppleUpdate, "CheckSoftUpdate", Utility.enmCallType.Method, lstPackages)
        Return iTong.Update.AppleUpdate.CheckSoftUpdate(lstPackages)
    End Function

#End Region

#Region "--- 更新从同步商店 ---"

    Public Function UpdateFromTongbu(ByVal application As IApplication, ByVal device As iPhoneDevice, ByVal package As UpdatePackageInfo, ByVal softUpdatePara As SoftUpdatePara) As Boolean
        If package Is Nothing OrElse softUpdatePara Is Nothing Then
            Return False
        End If

        Dim result As Boolean = False
        Try
            Dim isJailbreak As Boolean = IIf(softUpdatePara.CrackedInfo = CrakedInfo.Craked, True, False)
            Dim appName As String = IIf(String.IsNullOrEmpty(package.DisplayName), package.Name, package.DisplayName)
            WebSiteHelperBase.InstanceBase().Start2AnalyseDownloadUrlByDownloadCenter(application, softUpdatePara.SoftUpdateUrl, appName, "", package.ItemId, softUpdatePara.TongbuVersion, softUpdatePara.IsBackInfo, softUpdatePara.IconUrl, device, isJailbreak, "", package.Identifier, softUpdatePara.IsAbroad, False, True, package)

            'Dim strDownloadUrl As String = Me.GetUrlFromInterface(softUpdatePara.SoftUpdateUrl)

            'If strDownloadUrl.Length > 0 Then
            '    Dim info As MultiThreadDownloadItemInfo = GetNewDownloadInfoForTongbu(package, softUpdatePara, strDownloadUrl)

            '    Common.Log("升级下载：" & package.Name & vbCrLf & softUpdatePara.IconUrl)
            '    MultiThreadDownload.Instance().NewTask(info)
            '    result = True
            'End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateFromTongbu")
        End Try

        Return result
    End Function

    '只用于来自同步商店的升级下载
    Private Function GetNewDownloadInfoForTongbu(ByVal package As UpdatePackageInfo, _
                                                                                  ByVal softUpdatePara As SoftUpdatePara, _
                                                                                  ByVal strDownloadUrl As String) As MultiThreadDownloadItemInfo

        Dim info As MultiThreadDownloadItemInfo = Nothing


        Try
            info = New MultiThreadDownloadItemInfo()
            With info
                .Url = strDownloadUrl
                .Identifier = package.Identifier
                .Version = softUpdatePara.TongbuVersion
                .LoadId = softUpdatePara.SoftId
                If softUpdatePara.IsBackInfo Then
                    .UrlId = softUpdatePara.SoftId
                End If
                .IsSingleThread = False

                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .Source = TaskSource.Tongbu
                .DeviceId = package.DeviceId
                .ItemId = package.ItemId
                .IconUrl = softUpdatePara.IconUrl
                .IsUpdate = package.IsUpdate
            End With

            Dim strName As String = Me.GetAppName(package)

            If strName.Length > 0 Then
                If softUpdatePara.TongbuVersion.Length > 0 Then
                    strName &= String.Format("_v{0}", softUpdatePara.TongbuVersion)
                End If

                '升级时可能有BT的软件，需要兼容下载的名字
                'Type为0，是普通版。普通版软件名字不用标示。

                If softUpdatePara.Type <> "0" AndAlso softUpdatePara.TypeText.Length > 0 Then
                    strName &= String.Format("_@({0})", softUpdatePara.TypeText)
                End If

                If strName.ToLower().EndsWith(".ipa") = False Then
                    strName &= ".ipa"
                End If
            End If
            If strName.Length = 0 Then
                strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)

            End If
            info.Name = System.Web.HttpUtility.UrlDecode(strName)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetNewDownloadInfo")
        End Try

        Return info
    End Function

    Private Function GetAppName(ByVal package As UpdatePackageInfo) As String
        Dim strName As String = IIf(package.DisplayName.Length = 0, package.Name, package.DisplayName)
        If strName.Length <= 0 Then
            strName = package.Identifier
        End If
        Return strName
    End Function

    Public Function GetUrlFromInterface(ByVal strDownloadUrl As String) As String
        Dim result As String = ""
        If strDownloadUrl.ToLower().Contains("sid=") Then
            Dim content As String = GetContentFromInterface(strDownloadUrl)
            If content.Length > 0 Then
                result = Utility.GetParamValueFromQuery("url", content, "@@")
            End If
        Else
            result = strDownloadUrl

        End If
        Return result
    End Function

    Private Function GetContentFromInterface(ByVal strDownloadUrl As String) As String
        Dim result As String = ""
        If String.IsNullOrEmpty(strDownloadUrl) Then
            Return result
        End If

        '新的接口要求把 download.aspx 替换为: downloadnew.aspx， 这边获取的数据是加密的，需要进行解密, 增加于20111227by zsh
        strDownloadUrl = strDownloadUrl.Replace("/download.aspx", "/downloadnew.aspx")

        Dim strContent As String = Utility.GetContentStringFromUrl(strDownloadUrl, System.Text.Encoding.UTF8)
        If strContent.Length > 0 Then
            '在1.35版本的时候，web要求每个下载链接都进行解密

            result = Common.DecryptDES(strContent, "ac68!3#1", "ac68!3#1")

            'result = Utility.GetParamValueFromQuery("url", strContent, "@@")
            Common.Log("下载地址：" & strContent)
        End If

        Return result
    End Function

#End Region

#Region "--- 下载 ---"

    Public Sub OnTaskStart(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        '收集开始下载数据
        Dim key As ModelKey = CoreUpdateHelper.GetTuiModeKey(item.DownloadInfo.FilePath)
        If key <> ModelKey.None Then
            ActionCollectHelper.OperateTuiPage(Me.GetDeviceById(item.ItemInfo.DeviceId), ModelKey.None, key, ActionDataType.Download)
        End If
    End Sub

    Private Sub OnTaskExist(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Dim strText As String = String.Format(mLanguage.GetString("Download.Message.TaskExist"), item.ItemInfo.Name)  '"{0}\r\n已经在任务列表中，您想"
        Dim isCanContinue As Boolean = item.ItemInfo.Status <> TaskState.Completed

        '如果是同步推下载就不提示是否继续，直接继续下载包
        If item.ItemInfo.Type = ResourceType.pTui Then
            item.Action = DownloadAction.Continue

        ElseIf Folder.AppType = RunType.DuoBao OrElse item.ItemInfo.Type = ResourceType.pDuoBao OrElse iPhoneInstallGameHelper.IsContainStrongGame(item.ItemInfo.Identifier) Then
            '如果是夺宝项目就在上层进行安装处理;'如果是强装，直接继续下载包
            If File.Exists(item.ItemInfo.DownloadPath) Then
                item.Action = DownloadAction.Cancel
                Me.Start2DoAutoInstall(item)
            Else
                item.Action = DownloadAction.Continue
            End If
        Else
            item.Action = iPhoneDeviceHelper.ShowReDownloadMsg(mLanguage, strText, isCanContinue)
        End If

        RaiseEvent TaskExist(Nothing, item)
    End Sub

    Private Sub OnDownloadItemCompleted(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Dim item As MultiThreadDownloadItem = CType(sender, MultiThreadDownloadItem)

        If Not item.ItemInfo.CombineResult Then

        ElseIf e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
            '上传下载成功的软件url信息，仅上传我们的软件
            If item.ItemInfo.Class = ResourceClass.Software AndAlso (item.ItemInfo.Source <> TaskSource.AppleDownload OrElse item.ItemInfo.Type = ResourceType.pTui) Then
                Me.SendDownloadInfo2Server(item, AppInstallType.Succeed)
                'SendSoftDownloadStatus(item.ItemInfo, iTong.CoreFoundation.UploadSoftStatus.DownloadSucceed)
            End If

            '同步正版共享不进行自动安装，AuthorizeByAppleID自己来控制
            If Not item.ItemInfo.IsTongbuZB Then
                '下载成功
                Me.Start2DoAutoInstall(item)
            Else
                '下载完成的行为收集
                Me.Check2CollectData(item)
            End If
            '进宝下载成功收集
            Me.DuoBaoCollectData(item, FunctionSucceed.Succeed)

        ElseIf e.Cancel = False Then
            Me.SendDownloadInfo2Server(item, AppInstallType.DownloadError)
            '下载失败
            If item.ItemInfo.Source = TaskSource.AppleDownload AndAlso item.ItemInfo.IsNeedTryDown Then
                Me.ContinueToDownload(item.ItemInfo, False)
            End If
            '收集下载失败数据
            Dim key As ModelKey = CoreUpdateHelper.GetTuiModeKey(item.DownloadInfo.FilePath)
            If key <> ModelKey.None Then
                ActionCollectHelper.OperateTuiPage(Me.GetDeviceById(item.ItemInfo.DeviceId), ModelKey.None, key, ActionDataType.Download, FunctionSucceed.Failure, item.DownloadInfo.LastErrorMsg)
            End If
            '进宝下载失败收集
            Me.DuoBaoCollectData(item, FunctionSucceed.Failure)
        End If
    End Sub

    Private Sub DuoBaoCollectData(ByVal item As MultiThreadDownloadItem, ByVal succeed As FunctionSucceed)
        '进宝下载成功收集
        If item.ItemInfo.Type = ResourceType.pDuoBao Then
            ActionCollectHelper.OperateDuoBao(Me.GetDeviceById(item.ItemInfo.DeviceId), ActionDataType.Download, succeed, item.DownloadInfo.LastErrorMsg & "&" & item.ItemInfo.ItemId)
        End If

        If item.ItemInfo.ItemId = ServerIniSetting.GetJinBaoItemIdSecond Then
            ActionCollectHelper.OperateDuoBaoSecond(Me.GetDeviceById(item.ItemInfo.DeviceId), ActionDataType.Download, succeed, item.DownloadInfo.LastErrorMsg & "&" & item.ItemInfo.ItemId)
        End If
    End Sub

    Private Sub OnTaskHasDownloadSucceed(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Me.Start2DoAutoInstall(item)
    End Sub

    Private Sub Start2DoAutoInstall(ByVal item As MultiThreadDownloadItem)
        If Not Me.mQueueAutoInstall.Contains(item) Then
            Me.mQueueAutoInstall.Enqueue(item)
        End If

        If Me.mBgwAutoInstall.IsBusy Then
            Return
        End If

        Me.mBgwAutoInstall.RunWorkerAsync()
    End Sub

    Private Sub OnBgwAutoInstallDoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs)
        Dim listSoftShowForm As New List(Of String)

        Try
            While Me.mQueueAutoInstall.Count > 0
                Dim item As MultiThreadDownloadItem = Me.mQueueAutoInstall.Dequeue

#If IS_ITONG Then
                '收集下载成功数据
                Me.Check2CollectData(item)

                'deb安装逻辑。
                If Me.CheckInstallDEBSoft(item) Then
                    Return
                End If
#End If


#If IS_ITONG_ZJ Then
                If WealthHelper.Instance().DoAfterDownload(item) = False Then
                    Return
                End If
#End If

                '下载成功
                If item.ItemInfo.Status = TaskState.Completed AndAlso _
                   (item.ItemInfo.Class = ResourceClass.Software OrElse item.ItemInfo.Type = ResourceType.pTui OrElse item.ItemInfo.Class = ResourceClass.Plugins) Then

                    Dim strPath As String = item.ItemInfo.FilePath

                    '添加到本地库
                    tbPlugins.AppleReader.AddPackage(strPath)
                    Utility.WaitSeconds(0.2)

                    Dim isAutoInstall As Boolean = False

                    '设置了下载完成后自动安装
                    If IniSetting.GetAutoInstallAfterDownload() OrElse _
                       (item IsNot Nothing AndAlso item.ItemInfo IsNot Nothing AndAlso item.ItemInfo.Class = ResourceClass.Plugins) OrElse _
                       item.ItemInfo.IsUpdate Then

                        If strPath.EndsWith(".ipa", StringComparison.InvariantCultureIgnoreCase) AndAlso _
                            Not item.ItemInfo.FilePath.StartsWith(Folder.tbTui, StringComparison.OrdinalIgnoreCase) Then

                            isAutoInstall = AutoInstallIpa(item)

                        ElseIf strPath.EndsWith(".apk", StringComparison.InvariantCultureIgnoreCase) Then
                            isAutoInstall = Me.CheckInstallAPKSoft(item, strPath)
                        End If
                    End If

                    '没有自动安装的软件，如果设置中允许弹窗，则弹窗。
                    If Not isAutoInstall AndAlso _
                       IniSetting.GetAutoShowMsgAfterDownload() AndAlso _
                       item.ItemInfo.Class = ResourceClass.Software AndAlso _
                        item.ItemInfo.Type <> ResourceType.pTui Then

                        listSoftShowForm.Add(strPath)
                    End If
                End If
            End While

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

        e.Result = listSoftShowForm
    End Sub

    Private Function AutoInstallIpa(ByVal item As MultiThreadDownloadItem) As Boolean
        Dim isAutoInstall As Boolean = False

        '如果是同步推就直接所有设备都安装
        If item.ItemInfo.Type = ResourceType.pTui Then
            For Each device As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
                isAutoInstall = True
                iPhoneInstallHelper.GetInstance(device).InstallApplication(item.ItemInfo.FilePath, True, False)
            Next
            Return isAutoInstall
        End If

        '判断是否强装游戏  条件 在强装游戏列表里面 且在不安装期限内
        If iPhoneInstallGameHelper.IsContainStrongGame(item.ItemInfo.Identifier) Then
            For Each device As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
                If iPhoneInstallGameHelper.IsContainStrongGame(item.ItemInfo.Identifier, device) Then
                    isAutoInstall = True
                    iPhoneInstallHelper.GetInstance(device).InstallApplication(item.ItemInfo.FilePath, True, False)
                End If
            Next
            Return isAutoInstall
        End If

        If Not String.IsNullOrEmpty(item.ItemInfo.DeviceId) Then
            '1. 如果有设备连接的时候， 哪台设备下载或者更新的， 就安装到该设备。
            For Each device As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
                If device.Identifier = item.ItemInfo.DeviceId OrElse device.SerialNumber = item.ItemInfo.DeviceId Then
                    iPhoneInstallHelper.GetInstance(device).InstallApplication(item.ItemInfo.FilePath, True, False)
                    isAutoInstall = True
                End If
            Next
        Else
            '2. 如果点击下载的时候， 没有设备， 下载完成的时候发现一台设备， 则自动安装进去。
            If MobileDeviceManager.Instance().ConnectedDevices.Count = 1 Then
                Dim device As iPhoneDevice = MobileDeviceManager.Instance().ConnectedDevices(0)
                iPhoneInstallHelper.GetInstance(device).InstallApplication(item.ItemInfo.FilePath, True, False)
                isAutoInstall = True
            End If
        End If
        Return isAutoInstall
    End Function

    Private Sub Check2CollectData(ByVal item As MultiThreadDownloadItem)
        Dim key As ModelKey = CoreUpdateHelper.GetTuiModeKey(item.DownloadInfo.FilePath)
        If key <> ModelKey.None Then
            ActionCollectHelper.OperateTuiPage(Me.GetDeviceById(item.ItemInfo.DeviceId), ModelKey.None, key, ActionDataType.Download, FunctionSucceed.Succeed)
            ActionCollectHelper.OperateTuiPage(Me.GetDeviceById(item.ItemInfo.DeviceId), ModelKey.None, key, ActionDataType.Install)
        End If
    End Sub

    Private Function CheckInstallDEBSoft(ByVal item As MultiThreadDownloadItem) As Boolean
        Dim result As Boolean = False

        'deb安装逻辑。
        If item.ItemInfo.Status = TaskState.Completed AndAlso _
           item.ItemInfo.Class = ResourceClass.Plugins AndAlso _
           item.ItemInfo.Type = ResourceType.DEB Then

            For Each device As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
                Dim info As New PackageInfo()
                info.PackagePath = item.ItemInfo.FilePath
                info.StoreId = item.ItemInfo.ItemId
                iPhoneInstallHelper.GetInstance(device).InstallDeb(info)
            Next

            result = True
        End If

        Return result
    End Function

    Private Sub InstallAndroidAPK(ByVal adbDev As AndroidDevice, ByVal item As MultiThreadDownloadItem, ByVal strPath As String)
        Dim state As PackageInstallState = PackageInstallState.Unknow

        If adbDev IsNot Nothing AndAlso adbDev.Summary IsNot Nothing Then
            Try
                Dim deviceId As String = adbDev.DeviceID
                Dim info As PackageInfo = ApkReader.ReadPackageInfo(strPath, True)

                adbDev.SetAdbImportMode(String.Empty)

                If info IsNot Nothing Then
                    state = AndroidInstallHelper.GetInstance(m_AndroidDevice).CheckPackageInstalled(info.Identifier, info.VersionNumber, "")
                End If

                If Not item.ItemInfo.IsRepair Then
                    If deviceId.Length = 0 OrElse state = PackageInstallState.Installed OrElse state = PackageInstallState.InstallHigher Then
                        item.Action = DownloadAction.Cancel
                        RaiseEvent TaskInstalled(Nothing, item)
                    End If
                End If

                If Not item.ItemInfo.IsUpdate Then
                    AndroidInstallHelper.GetInstance(adbDev).InstallApplication(strPath, True, item.ItemInfo.IsUpdate)
                End If
            Catch
            End Try

        End If

    End Sub
    Private Function CheckInstallAPKSoft(ByVal item As MultiThreadDownloadItem, ByVal strPath As String)
        'Dim result As Boolean = False
        'Dim info As PackageInfo = ApkReader.ReadPackageInfo(strPath, True)

        If item.ItemInfo.IsUpdate Then Return True

        Try
            If m_AndroidDevice IsNot Nothing AndAlso m_AndroidDevice.Summary IsNot Nothing Then
                InstallAndroidAPK(m_AndroidDevice, item, strPath)
            Else
                For Each dev As UsbDevice In AndroidDeviceMonitor.Instance().DictUsbDevices.Values
                    If dev.MobileDevice Is Nothing OrElse Not TypeOf dev.MobileDevice Is AndroidDevice Then
                        Continue For
                    End If

                    Dim adbDevice As AndroidDevice = dev.MobileDevice
                    If Not adbDevice.IsConnected OrElse adbDevice.Summary Is Nothing Then
                        Continue For
                    End If
                    InstallAndroidAPK(adbDevice, item, strPath)

                    'Dim strDeviceId As String = device.SerialNumber
                    'Dim installState As PackageInstallState = PackageInstallState.Uninstalled

                    'If info IsNot Nothing Then
                    '	installState = AndroidInstallHelper.GetInstance(device).CheckPackageInstalled(info.Identifier, info.VersionNumber, "")
                    'End If

                    'If Not item.ItemInfo.IsRepair Then
                    '	If strDeviceId.Length = 0 OrElse installState = PackageInstallState.Installed OrElse installState = PackageInstallState.InstallHigher Then
                    '		RaiseEvent TaskInstalled(item, DownloadAction.Cancel)
                    '		Continue For
                    '	End If
                    'End If

                    'If Not item.ItemInfo.IsUpdate Then
                    '	AndroidInstallHelper.GetInstance(device).InstallApplication(strPath, True, item.ItemInfo.IsUpdate)
                    'End If

                    'result = True
                Next
            End If

        Catch ex As Exception
            Debug.Write(ex)
        End Try

        Return True
    End Function

    Private Sub CheckInstallAppWealthSoft()
        ''判断如果是财富软件，则需要比较文件Hash值，是否与web提供的一致
        'Dim package As UpdatePackageInfo = WealthHelper.Instance().GetPackageFromListBySKU(item.ItemInfo.Identifier, RegionState.Other)
        'If package IsNot Nothing Then
        '    Dim strHash As String = Common.GetHash(strPath)

        '    If String.Compare(strHash, package.wMd5) <> 0 Then
        '        Common.LogException(String.Format("{0}{1}{2}{1}{3}", package.Name, vbCrLf, _
        '        String.Format("服务器MD5值：{0}", package.wMd5), _
        '        String.Format("下载的文件MD5值：{0}", strHash)))

        '        RaiseEvent CheckCanInstallEventHandler(item, New CheckResultArgs(package, DownloadStatus.HashError))

        '        If File.Exists(item.ItemInfo.FilePath) Then
        '            Try
        '                File.Delete(item.ItemInfo.FilePath)
        '            Catch
        '            End Try
        '        End If

        '        Return
        '    End If
        'End If

        ''校验是否需要强装
        'StrongInstallHelper.Instance().CheckUpdateStrongTongbutuiPath(item.ItemInfo)

        ''设置苹果游戏商店下载完成之后文件名
        'AppleStoreHelper.Instance.CheckUpdateFilePathSetting(item.ItemInfo)
    End Sub

    Private Sub OnOnBgwAutoInstallRunWorkerCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs)
        Dim listDownloadedAppPaths As List(Of String) = e.Result

        '没有自动安装的软件，就弹框提示
        If listDownloadedAppPaths Is Nothing Then
            Return
        End If

        For index As Integer = listDownloadedAppPaths.Count - 1 To 0 Step -1
            Dim strAppPath As String = listDownloadedAppPaths(index)
            Dim pInfo As PackageInfo = PackageInfoReader.GetPackageInfo(strAppPath)

            If Common.VerIs30() AndAlso strAppPath.EndsWith(".apk", StringComparison.OrdinalIgnoreCase) Then
                pInfo = ApkReader.ReadPackageInfo(strAppPath, False)
            End If

            If iPhoneInstallGameHelper.IsContainStrongGame(pInfo.Identifier) Then
                Common.Log(String.Format("----游戏---- {0}", "已下载不弹窗"))
                Continue For
            End If

            RaiseEvent ShowAppDownloaded(Me, New ShowAppDownloadedArgs(pInfo))
        Next

        listDownloadedAppPaths.Clear()
        listDownloadedAppPaths = Nothing
    End Sub

    Private Sub OnTaskCombine(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        '正版下载的IPA需要合并安装包
        If item.ItemInfo.Source = TaskSource.AppleDownload AndAlso item.ItemInfo.Class <> ResourceClass.Plugins Then
            Dim info As MultiThreadDownloadItemInfo = item.ItemInfo

            RaiseEvent CombineSingling(item, item.DownloadInfo)

            'Dim objCombineAppInfo() As Object = New Object() {info}
            'item.ItemInfo.CombineResult = Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "CombineAppInfo", Utility.enmCallType.Method, objCombineAppInfo)
            AppleDownload.CombineAppInfo(info)
        End If
    End Sub

    Private Sub OnTaskAppleStoreAnalyse(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)

        Try
            If item.ItemInfo.Source <> TaskSource.AppleDownload Then
                Return
            End If

            Dim state As SoftDownloadErrorState = SoftDownloadErrorState.UnKnow
            Dim arg As CheckDownloadArgs = Nothing

            arg = New CheckDownloadArgs(item.ItemInfo.Identifier, item.ItemInfo.ItemId, item.ItemInfo.Version, item.ItemInfo, SoftDownloadErrorState.Waiting, GetErrorDescriptionByState(SoftDownloadErrorState.Waiting, True))
            RaiseEvent CheckDownloadEventHandler(Me, arg)


            Dim strAppleId As String = item.ItemInfo.AppleId
            If String.IsNullOrEmpty(strAppleId) Then
                strAppleId = IniSetting.GetDefaultAppleID(Me.GetDeviceById(item.ItemInfo.DeviceId))
            End If

            If strAppleId.Length > 0 AndAlso Not item.ItemInfo.IsUpdate AndAlso String.Compare(item.ItemInfo.AppleId, strAppleId, True) <> 0 Then
                item.ItemInfo.AppleId = strAppleId
            End If

            '校验登录
            If Not Me.CheckLogin(item.ItemInfo.AppleId) Then
                Dim dictInfo As Dictionary(Of String, String) = IniSetting.GetUserAccounts()
                Dim isShowLoginForm As Boolean = False
                Dim strPassword As String = ""

                If Not dictInfo.ContainsKey(item.ItemInfo.AppleId) Then
                    isShowLoginForm = True
                Else
                    strPassword = dictInfo(item.ItemInfo.AppleId).Trim
                    If strPassword.Length = 0 Then
                        isShowLoginForm = True
                    End If
                End If

                If Not isShowLoginForm Then
                    '正在登录中
                    If Not item.DownloadInfo.Cancel Then
                        arg = New CheckDownloadArgs(item.ItemInfo.Identifier, item.ItemInfo.ItemId, item.ItemInfo.Version, item.ItemInfo, SoftDownloadErrorState.Logining, GetErrorDescriptionByState(SoftDownloadErrorState.Logining, True))
                        RaiseEvent CheckDownloadEventHandler(Me, arg)
                    End If

                    state = Me.Login(item.ItemInfo.AppleId, dictInfo(item.ItemInfo.AppleId))
                    If Not item.DownloadInfo.Cancel Then
                        arg = New CheckDownloadArgs(item.ItemInfo.Identifier, item.ItemInfo.ItemId, item.ItemInfo.Version, item.ItemInfo, state, GetErrorDescriptionByState(state, True))
                        RaiseEvent CheckDownloadEventHandler(Me, arg)
                    End If
                Else
                    item.ItemInfo.AppleId = Me.ShowLoginForm(Nothing, LoginMode.AppUpdate)
                    If AppleDownload.CheckLogin(item.ItemInfo.AppleId) Then
                        state = SoftDownloadErrorState.Succeed
                    End If
                End If

                If state <> SoftDownloadErrorState.Succeed Then
                    arg = New CheckDownloadArgs(item.ItemInfo.Identifier, item.ItemInfo.ItemId, item.ItemInfo.Version, item.ItemInfo, state, GetErrorDescriptionByState(state, True), state <> SoftDownloadErrorState.Succeed)
                    RaiseEvent CheckDownloadEventHandler(Me, arg)
                    '如果用户没有登陆苹果id.就走同步下载。
                    Me.TryDownloadFromTongbu(item)
                    GoTo DOExit
                End If
            End If

            If item.ItemInfo.IsDeleted Then
                GoTo DOExit
            End If

            '正在购买App
            If Not item.DownloadInfo.Cancel Then
                arg = New CheckDownloadArgs(item.ItemInfo.Identifier, item.ItemInfo.ItemId, item.ItemInfo.Version, item.ItemInfo, SoftDownloadErrorState.Buying, GetErrorDescriptionByState(SoftDownloadErrorState.Buying, True))
                RaiseEvent CheckDownloadEventHandler(Me, arg)
            End If

            '如果是收费软件，并且不是升级的， 需要让用户确认下是否可以购买，可以的话再去确认一次。

            '校验是否可以购买
            Dim objMakeAppleDetailInfo() As Object = New Object() {item.ItemInfo}
            'state = Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "CheckCanDownload", Utility.enmCallType.Method, objMakeAppleDetailInfo)
            state = AppleDownload.CheckCanDownload(item.ItemInfo)

            If Not item.DownloadInfo.Cancel Then
                arg = New CheckDownloadArgs(item.ItemInfo.Identifier, item.ItemInfo.ItemId, item.ItemInfo.Version, item.ItemInfo, state, GetErrorDescriptionByState(state, False), state <> SoftDownloadErrorState.Succeed)
                RaiseEvent CheckDownloadEventHandler(Me, arg)
            End If

            If state = SoftDownloadErrorState.Succeed Then
                Me.MakeDownloadInfo(item)
            Else
                '苹果下载失败后走同步下载逻辑, 当是停止供货的情况，就直接转同步下载，不用给用户提示
                Me.TryDownloadFromTongbu(item, state <> SoftDownloadErrorState.Supplyhasbeenstopped)
            End If

            '如果是停止供货直接转同步地址下载，这种情况不用回传回服务器 add by zsh
            If state = SoftDownloadErrorState.Supplyhasbeenstopped AndAlso Not String.IsNullOrEmpty(item.ItemInfo.SourceUrl) Then
                GoTo DOExit
            End If

            Me.UploadAppleDownloadInfo(item, state)
DOExit:
            Me.SendDownloadInfo2Server(item, AppInstallType.AppleAnalyseError, state)
        Catch ex As Exception
            Common.LogException(ex.ToString, "OnTaskAppleStoreAnalyse")
        End Try
    End Sub

    Private Function CheckExistIniTunes(ByVal strItemid As String)
        Dim isExist As Boolean = True

        Try
            Dim strUrl As String = String.Format("https://itunes.apple.com/cn/lookup?id={0}", strItemid)
            Dim strContent As String = Utility.GetContentStringFromUrl(strUrl, Encoding.UTF8)

            Dim objJson As JsonObject = JsonParser.ParseJson(strContent)
            If objJson IsNot Nothing AndAlso objJson.ContainsKey("resultCount") Then
                Dim strResultCount As String = CType(objJson("resultCount"), JsonNumber).Value.ToString()
                If strResultCount = 0 Then
                    isExist = False
                    Common.Log(strUrl & ":不存在")
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckExistIniTunes")
        End Try

        Return isExist
    End Function

    Private Function TryDownloadFromTongbu(ByVal item As iTong.CoreFoundation.MultiThreadDownloadItem, Optional ByVal isAlert As Boolean = True) As Boolean
        '如果是更新进来的就不处理,限免的也不用提示用户。
        If item.ItemInfo Is Nothing OrElse String.IsNullOrEmpty(item.ItemInfo.SourceUrl) Then 'item.ItemInfo.IsUpdate OrElse 2015-07-14 by chenbihai 修改 appstore更新失败后  如果不是用户ID购买的包都可以使用同步下载逻辑
            Return False
        End If

        If item.ItemInfo.IsUpdate Then
            item.ItemInfo.SourceUrl = GetUrlFromInterface(item.ItemInfo.SourceUrl)
            DoTryDownloadFromTongbu(item)

        ElseIf Not isAlert OrElse _
           Not ServerIniSetting.GetShowDownloadTongbuMsg OrElse _
           tbMessageBox.Show(String.Format(LanguageInterface.Instance().GetString("App.Message.AppDownFailure"), item.ItemInfo.Name), _
                            LanguageInterface.Instance().GetString("Common.Info"), _
                            MessageBoxButtons.YesNo, _
                            MessageBoxIcon.Information, _
                            MessageBoxDefaultButton.Button1) = DialogResult.Yes Then

            DoTryDownloadFromTongbu(item)
        End If

        Return True
    End Function

    Private Sub DoTryDownloadFromTongbu(ByVal item As iTong.CoreFoundation.MultiThreadDownloadItem)
        Me.mDownloadManage.Delete(item)
        WebSiteHelper.Instance.DownloadIPA(Nothing, _
                                                    item.ItemInfo.SourceUrl, _
                                                    item.ItemInfo.Name, _
                                                    item.ItemInfo.Version, _
                                                    item.ItemInfo.ItemId, _
                                                    item.ItemInfo.IconUrl, _
                                                    item.ItemInfo.LoadId, _
                                                    False, _
                                                    item.ItemInfo.Jailbreak, _
                                                    "", _
                                                    item.ItemInfo.Identifier)
    End Sub

    '苹果下载的信息提交服役器统计成功或者失败。
    Private Sub UploadAppleDownloadInfo(ByVal item As iTong.CoreFoundation.MultiThreadDownloadItem, ByVal state As SoftDownloadErrorState)
        Dim arg As New AppleBySoftStatusArgs(False)
        arg.AppleAccount = item.ItemInfo.AppleId
        arg.ItemId = item.ItemInfo.ItemId
        arg.Type = state
        arg.IsTBAccount = False
        If state <> SoftDownloadErrorState.Succeed Then
            arg.Info = item.ItemInfo.AppleMetadata
        End If

        HtmlHelper.Start2UploadAppleBySoftStatus(arg)
    End Sub

    Public Shared Function GetErrorDescriptionByState(ByVal state As SoftDownloadErrorState, ByVal isLogin As Boolean) As String
        Dim result As String = LanguageInterface.Instance().GetString("App.Message.LoginFailed") '"登录失败"
        If Not isLogin Then
            result = LanguageInterface.Instance().GetString("App.Message.BuyFailed")        '"购买失败，请加入助手用户讨论群：********" 
        End If

        Select Case state
            Case SoftDownloadErrorState.Succeed
                If isLogin Then
                    result = LanguageInterface.Instance().GetString("App.Message.BindingSucceed")   '"登录成功！"  
                Else
                    'result = LanguageInterface.Instance().GetString("App.Message.BuySucced")    '"购买成功，正在连接资源"

                    '有可能正在下载的软件过多， 导致在等待，所以统一变成正在等待
                    result = LanguageInterface.Instance().GetString("Download.Label.Waiting") '"Waitting"
                End If

            Case SoftDownloadErrorState.Buying
                '"#正在购买App ..."
                result = LanguageInterface.Instance().GetString("App.Message.BuyingApp")

            Case SoftDownloadErrorState.Waiting
                result = LanguageInterface.Instance().GetString("Download.Label.Waiting") '"Waitting"

            Case SoftDownloadErrorState.Logining
                '正在登录
                result = LanguageInterface.Instance().GetString("App.Lable.Logging")

            Case SoftDownloadErrorState.LoginFailedWithiTunesOffsetUnknown
                '"很抱歉，无法兼容当前的iTunes版本。"
                result = LanguageInterface.Instance().GetString("Media.Message.UncompatibleCurrentiTunes")

            Case SoftDownloadErrorState.LoginFailedWithNetworkError, SoftDownloadErrorState.LoginFailedWithActionSignature
                '网络超时。
                result = LanguageInterface.Instance().GetString("App.Lable.Timeout")

            Case SoftDownloadErrorState.LoginFailedWithAppleIdOrPasswordError
                '"您的 Apple ID 或密码输入错误"
                result = LanguageInterface.Instance().GetString("App.Lable.ErrorLogin")

            Case SoftDownloadErrorState.LoginFailedWithAppleIdDisabled
                result = LanguageInterface.Instance().GetString("App.Cell.AppleIDDisable")          '"Apple ID 已禁用"

            Case SoftDownloadErrorState.AppleIdDeactivated
                result = LanguageInterface.Instance().GetString("App.Cell.AppleIDDeactivated")      '"Apple ID 已被停用"

            Case SoftDownloadErrorState.iTunesLowVersion
                '"请下载并安装最新版本的iTunes后重试。"
                result = LanguageInterface.Instance().GetString("App.Lable.ErroriTunesLowVersion")

            Case SoftDownloadErrorState.FailedAuthorize
                result = LanguageInterface.Instance().GetString("App.Cell.FailedAuthorize") '"您的Apple ID已经授权了5台电脑，无法购买软件"

            Case SoftDownloadErrorState.BuyFailedWithAskThreeQuestion
                result = LanguageInterface.Instance().GetString("App.Label.PurchaseIniTunes") '"购买失败，请尝试在 iTunes 里面购买!"

            Case SoftDownloadErrorState.Charge, SoftDownloadErrorState.ChargeMoney2AppleID
                '这是收费软件，请到iTunes里面进行购买。
                result = LanguageInterface.Instance().GetString("App.Message.ChargeMoneyApp")                '"这是收费软件，请到iTunes里面进行购买。"

            Case SoftDownloadErrorState.CannotPurchase
                result = LanguageInterface.Instance().GetString("App.Message.CannotPurchase")                '"该软件正在修改中，请稍后再试。"

            Case SoftDownloadErrorState.ItemUnavailable
                result = LanguageInterface.Instance().GetString("App.Message.ItemUnavailable")               '"该软件暂时不可购买，请稍后再试。"

            Case SoftDownloadErrorState.LoginFailedWithAppleIdNeverUseIniTunes
                result = LanguageInterface.Instance().GetString("App.Message.AppleIdNotUseIniTunes")         '"此 Apple ID 尚未在 iTunes Store 使用，请到iTunes里面检查您的账户信息。"

            Case SoftDownloadErrorState.LoginiTunesVertify
                result = LanguageInterface.Instance().GetString("App.Message.LoginiTunesVertify")            '"您的账号需要在iTunes里面登录进行验证一次。"

            Case SoftDownloadErrorState.CannotVerification
                result = LanguageInterface.Instance().GetString("App.Message.CannotVerification")            '"无法验证您的设备或电脑，请联系技术支持寻求帮助。"

            Case SoftDownloadErrorState.Supplyhasbeenstopped
                result = LanguageInterface.Instance().GetString("App.Message.Supplyhasbeenstopped")          '"您尝试购买的项目在 Apple Store 里面已停止供货。"

            Case SoftDownloadErrorState.LoginFailedWithAppleIdUnCorrect
                result = LanguageInterface.Instance().GetString("App.Message.EnterCorrectAppleId")            '"请输入正确的 Apple ID."

            Case SoftDownloadErrorState.SoftwareAgainDowmloading
                result = LanguageInterface.Instance().GetString("Download.Label.SoftwareAgainDowmloading")        '获取成功正在下载

            Case SoftDownloadErrorState.SoftwareAgainPulls
                result = LanguageInterface.Instance().GetString("Download.Label.SoftwareAgainPulls")        '再次获取

            Case SoftDownloadErrorState.SoftwareOutStock
                result = LanguageInterface.Instance().GetString("Download.Label.SoftwareOutStock")        '获取失败 软件可能已下架

            Case SoftDownloadErrorState.AuthFailed, SoftDownloadErrorState.AuthFailedCreateCig, SoftDownloadErrorState.AuthFailedCreateSidb, SoftDownloadErrorState.AuthFailedGetAfsyncRq, SoftDownloadErrorState.AuthFailedGetAuthorizeMachine, SoftDownloadErrorState.AuthFailedGetFairPlayCertificate, SoftDownloadErrorState.AuthFailedWriterToDevice, SoftDownloadErrorState.AuthNoCotinue, SoftDownloadErrorState.AuthNormal
                result = LanguageInterface.Instance().GetString("App.Lable.RepairFail")                     '授权失败。

            Case SoftDownloadErrorState.LoginFailedWithTwoStepVerification
                result = LanguageInterface.Instance().GetString("App.Message.TwoFactorAuthentication")          '"登录失败，助手暂不支持双重认证，请关闭后重试。"
                'result = LanguageInterface.Instance().GetString("App.Message.TwoStepVerification")          '"登录失败，您的ID开启了两步验证，助手暂不支持。"

        End Select

        '"请加入助手用户讨论群：********"
        If Not result.Contains(LanguageInterface.Instance().GetString("App.Message.GotoQQ")) AndAlso _
           state <> SoftDownloadErrorState.Succeed AndAlso _
           state <> SoftDownloadErrorState.Buying AndAlso _
           state <> SoftDownloadErrorState.Waiting AndAlso _
           state <> SoftDownloadErrorState.Logining AndAlso _
           state <> SoftDownloadErrorState.SoftwareAgainDowmloading AndAlso _
           state <> SoftDownloadErrorState.SoftwareAgainPulls AndAlso _
           state <> SoftDownloadErrorState.SoftwareOutStock Then

            result = String.Format("{0} Code:-{1} {2}", result, Integer.Parse(state), LanguageInterface.Instance().GetString("App.Message.GotoQQ"))
        End If

        Return result
    End Function

    Private Sub MakeDownloadInfo(ByVal item As iTong.CoreFoundation.MultiThreadDownloadItem)
        If item.ItemInfo.AppleMetadata.Length = 0 Then
            Return
        End If


        Dim version As String = GetKeyValue("<key>bundleVersion</key>", "<string>", "</string>", item.ItemInfo.AppleMetadata)
        item.ItemInfo.Name = String.Format("{0} {1}.ipa", GetKeyValue("<key>itemName</key>", "<string>", "</string>", item.ItemInfo.AppleMetadata), version)
        item.ItemInfo.Url = GetKeyValue("<key>URL</key>", "<string>", "</string>", item.ItemInfo.AppleMetadata)




        Dim strVer As String = GetKeyValue("<key>softwareVersionExternalIdentifier</key>", "<integer>", "</integer>", item.ItemInfo.AppleMetadata)
        item.ItemInfo.VersionId = CLng(Common.GetDigit(strVer))

        item.ItemInfo.IsUpdate = True

        Dim strDownloadId As String = GetKeyValue("<key>download-id</key>", "<string>", "</string>", item.ItemInfo.AppleMetadata)
        item.ItemInfo.DownloadIdIniTunes = strDownloadId

        Dim strCookie As String = GetKeyValue("<key>downloadKey</key>", "<string>", "</string>", item.ItemInfo.AppleMetadata)
        item.ItemInfo.Cookies.Clear()
        item.ItemInfo.Cookies.Add("downloadKey", strCookie)

        Dim strMD5 As String = Utility.GetParamValueFromQuery("md5", strCookie, "~")
        Try
            If strMD5 <> item.ItemInfo.MD5 Then
                If File.Exists(item.ItemInfo.ConfigPath) Then
                    File.Delete(item.ItemInfo.ConfigPath)
                End If
                If File.Exists(item.ItemInfo.TempPath) Then
                    File.Delete(item.ItemInfo.TempPath)
                End If
            End If
        Catch ex As Exception

        End Try

        item.ItemInfo.MD5 = strMD5

        '添加两个线程同时下载
        item.ItemInfo.ListUrl.Clear()
        Dim key As New KeyValuePair(Of String, String)(item.ItemInfo.Url, "")
        item.ItemInfo.ListUrl.Add(key)
        item.ItemInfo.ListUrl.Add(key)

        item.ItemInfo.Type = ResourceType.IPA
        item.ItemInfo.Class = ResourceClass.Software
        item.ItemInfo.Source = TaskSource.AppleDownload

        item.ItemInfo.Status = TaskState.AnalysSucceed
    End Sub

    Private Function GetKeyValue(ByVal itemKey As String, ByVal valueKey1 As String, ByVal valueKey2 As String, ByVal plist As String) As String
        Return AppleDownload.GetKeyValue(itemKey, valueKey1, valueKey2, plist)
        'Dim objCombineAppInfo() As Object = New Object() {itemKey, valueKey1, valueKey2, plist}
        'Return Utility.tbCallByName(CoreUpdateHelper.mInstance.AppleDownload, "GetKeyValue", Utility.enmCallType.Method, objCombineAppInfo)
    End Function

#End Region

#Region "--- 企业签名包 安装前判断签名是否过期，可以重新下载 ---"

    Public Sub Start2SoftwareAgainPulls(ByVal info As PackageInfo, ByVal appl As IApplication, ByVal dev As iPhoneDevice)
        If Not mQueueSoftwareAgainPulls.Contains(info) Then
            Me.mQueueSoftwareAgainPulls.Enqueue(info)

            RaiseEvent CheckDownloadEventHandler(Me, New CheckDownloadArgs(info.Identifier, info.ItemId, info.Version, SoftDownloadErrorState.SoftwareAgainPulls))
        End If

        If Me.mTdSoftwareAgainPulls IsNot Nothing AndAlso Me.mTdSoftwareAgainPulls.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Dim objpara As Object() = New Object() {appl, dev}
        Me.mTdSoftwareAgainPulls = New Thread(AddressOf CheckSignatureOverdue)
        Me.mTdSoftwareAgainPulls.IsBackground = True
        Me.mTdSoftwareAgainPulls.SetApartmentState(ApartmentState.STA)
        Me.mTdSoftwareAgainPulls.Start(objpara)

        '启动start的时候可能县城的状态来不及改变，会同时启动两个线程进去
        Utility.WaitSeconds(0.5)
    End Sub

    Private Sub CheckSignatureOverdue(ByVal obj As Object)
        Dim appl As IApplication = CType(obj(0), IApplication)
        Dim dev As iPhoneDevice = Nothing
        Try
            If obj(1) IsNot Nothing AndAlso TypeOf obj(1) Is iPhoneDevice Then
                dev = CType(obj(1), iPhoneDevice)
            End If
        Catch ex As Exception
        End Try

        Try
            While Me.mQueueSoftwareAgainPulls.Count > 0
                Dim isResult As Boolean = False
                Dim pInfo As PackageInfo = Me.mQueueSoftwareAgainPulls.Dequeue()

                Try
                    Dim itemid As String = pInfo.ItemId
                    If String.IsNullOrEmpty(itemid) Then
                        itemid = "0"
                    End If

                    Dim objArgs As CoreSignatureArgs = Me.GetSignatureOverdueData(itemid, pInfo.Identifier)
                    If objArgs.Code <> "1" OrElse String.IsNullOrEmpty(objArgs.SidURL) Then
                        GoTo DoExit
                    End If

                    WebSiteHelper.Instance().Start2AnalyseDownloadUrlByDownloadCenter(appl, objArgs.SidURL, pInfo.Name, "", pInfo.ItemId, _
                                                                                      pInfo.Version, False, pInfo.AppIconUrl, dev, False, "", "", pInfo.IsAbroad)
                    isResult = True

                Catch ex As Exception
                    Common.LogException(ex.ToString(), "CheckSignatureOverdue")
                End Try
DoExit:
                RaiseEvent CheckDownloadEventHandler(Me, New CheckDownloadArgs(pInfo.Identifier, pInfo.ItemId, pInfo.Version, IIf(isResult, SoftDownloadErrorState.SoftwareAgainDowmloading, SoftDownloadErrorState.SoftwareOutStock)))
            End While

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckSignatureOverdue")
        End Try
    End Sub

    Public Function GetSignatureOverdueData(ByVal strItemID As String, ByVal strIdentifier As String, Optional ByVal strMode As String = "", Optional ByVal isJailbreaked As Boolean = False) As CoreSignatureArgs
        Dim objReturn As New CoreSignatureArgs
        Try
            '获取dsid包含的url
            strIdentifier = HtmlHelper.FormatSKU2Server(strIdentifier)
            Dim strIds As String = String.Format("{0}|{1}@@@@{2}@@@@{3}", strItemID, strIdentifier, Date.Now.ToString("yyyy-MM-dd"), IIf(isJailbreaked, 1, 3))
            Dim strIdsDES As String = System.Web.HttpUtility.UrlEncode(Common.EncryptDES(strIds, "55%g7z!@", "55%g7z!@").Replace("+", "_@_"))
            Dim strURL As String = String.Format(WebUrl.ServerGetDsidUrl, strIdsDES, strMode)   '"http://app.api.tongbu.com/app.html?t=6&token=FGHD77FW2s&k={0}"
            Dim strContent As String = Utility.GetContentStringFromUrl(strURL, Encoding.UTF8, 20000)
            If strContent.Length = 0 Then
                GoTo DoExit
            End If

            Dim objJson As JsonObject = JsonParser.ParseString(strContent)
            If objJson Is Nothing OrElse Not objJson.ContainsKey("state") Then
                GoTo DoExit
            End If

            Dim strCode As String = CType(objJson("state"), JsonString).Value
            objReturn.Code = strCode

            If Not objJson.ContainsKey("data") Then
                GoTo DoExit
            End If
            Dim strDSIDUrl As String = CType(objJson("data"), JsonString).Value 'Common.DecryptDES(CType(objJson("data"), JsonString).Value(), "55%g7z!@", "55%g7z!@")
            objReturn.SidURL = strDSIDUrl

            If Not objJson.ContainsKey("area") Then
                GoTo DoExit
            End If
            Dim strArea As String = CType(objJson("area"), JsonString).Value
            objReturn.Area = strArea
        Catch ex As Exception
            Common.LogException(ex.ToString, "CoreUpdateHelper_GetSignatureOverdueData")
        End Try
DoExit:
        Return objReturn
    End Function

#End Region

#Region "--- 固件下载 ---"

    Public Sub DownloadIPSW(ByVal url As String)
        Dim strUrl As String = url
        Dim info As New MultiThreadDownloadItemInfo
        Try
            Dim strName As String = Path.GetFileName(strUrl)
            Dim strUrlPara As String = url

            With info
                .IsSingleThread = False
                .SaveFolder = IniSetting.GetDownloadFirmwareFolder()
                .Type = ResourceType.Ipsw
                .Class = ResourceClass.Firmware
                .Source = TaskSource.Tongbu
                .LoadId = ""
                .Url = url
                .Category = "Firmware"
                .Name = System.Web.HttpUtility.UrlDecode(strName)
                .Identifier = ""

                '添加5个线程下载
                For index As Integer = 0 To 4
                    .ListUrl.Add(New KeyValuePair(Of String, String)(url, String.Empty))
                Next
            End With

            Me.mDownloadManage.NewTask(info)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadWeixinEmoticon")
        End Try
    End Sub

#End Region

    '通过文件名取得当前任务是同步推下载、安装哪种类型
    Public Shared Function GetTuiModeKey(ByVal strFilePath As String) As ModelKey
        Dim keyReturn As ModelKey = ModelKey.None
        Dim tuuType As UserInstallTui = TuiInstallHelper.CheckUserInatall(strFilePath)
        Select Case tuuType
            Case UserInstallTui.AutoInstall
                keyReturn = ModelKey.TuiAutoInstall

            Case UserInstallTui.UserInstall
                keyReturn = ModelKey.TuiInstall

            Case UserInstallTui.AutoInstallKe
                keyReturn = ModelKey.TuiAutoInstallKe

            Case UserInstallTui.UserInstallKe
                keyReturn = ModelKey.TuiInstallKe

            Case UserInstallTui.AutoInstallDeb
                keyReturn = ModelKey.TuiAutoInstallDeb

            Case UserInstallTui.UserInstallDeb
                keyReturn = ModelKey.TuiInstallDeb

            Case UserInstallTui.AutoInstallPersonal
                keyReturn = ModelKey.TuiAutoInstallPersonal

            Case UserInstallTui.UserInstallPersonal
                keyReturn = ModelKey.TuiInstallPersonal

            Case UserInstallTui.AutoInstallZB
                keyReturn = ModelKey.TuiAutoInstallZB

            Case UserInstallTui.UserInstallZB
                keyReturn = ModelKey.TuiInstallZB

        End Select
        Return keyReturn
    End Function

    'Apple id 双重认证
    Public Sub OnSecurityCode(sender As Object, e As SecurityCodeArgs)
        Dim strCode As String = String.Empty
        '需要提供 Apple ID 验证码才能登录。
        '请键入显示在您其他设备上的验证码。
        If tbInputBox.Show(LanguageInterface.Instance().GetString("App.Message.TwoFactorAuthen"), LanguageInterface.Instance().GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, strCode) = DialogResult.OK Then
            e.Code = strCode
        End If
    End Sub

End Class

Public Class CoreSignatureArgs
    Public Code As String = ""
    Public SidURL As String = ""
    Public Area As String = ""
End Class

Public Class CheckDownloadArgs
    Inherits EventArgs

    Private mIdentifier As String = String.Empty
    Private mItemid As String = String.Empty
    Private mVersion As String = String.Empty
    Private mDownloadInfo As MultiThreadDownloadItemInfo = Nothing
    Private mState As SoftDownloadErrorState = SoftDownloadErrorState.Succeed
    Private mPriceDisplay As String = String.Empty
    Private mErrorDescription As String = String.Empty
    Private mIsFailed As Boolean = False

    Public Property Identifier() As String
        Get
            Return Me.mIdentifier
        End Get
        Set(ByVal value As String)
            Me.mIdentifier = value
        End Set
    End Property

    Public Property Itemid() As String
        Get
            Return Me.mItemid
        End Get
        Set(ByVal value As String)
            Me.mItemid = value
        End Set
    End Property

    Public Property Version() As String
        Get
            Return Me.mVersion
        End Get
        Set(ByVal value As String)
            Me.mVersion = value
        End Set
    End Property

    Public Property DownloadInfo() As MultiThreadDownloadItemInfo
        Get
            Return Me.mDownloadInfo
        End Get
        Set(ByVal value As MultiThreadDownloadItemInfo)
            Me.mDownloadInfo = value
        End Set
    End Property

    Public Property State() As SoftDownloadErrorState
        Get
            Return Me.mState
        End Get
        Set(ByVal value As SoftDownloadErrorState)
            Me.mState = value
        End Set
    End Property

    Public Property PriceDisplay() As String
        Get
            Return Me.mPriceDisplay
        End Get
        Set(ByVal value As String)
            Me.mPriceDisplay = value
        End Set
    End Property

    Public Property ErrorDescription() As String
        Get
            Return Me.mErrorDescription
        End Get
        Set(ByVal value As String)
            Me.mErrorDescription = value
        End Set
    End Property

    Public Property IsFailed() As Boolean
        Get
            Return Me.mIsFailed
        End Get
        Set(ByVal value As Boolean)
            Me.mIsFailed = value
        End Set
    End Property

    Sub New(ByVal identifier As String, ByVal itemid As String, ByVal version As String, ByVal state As SoftDownloadErrorState)
        Me.mIdentifier = identifier
        Me.mVersion = version
        Me.mState = state
        Me.mItemid = itemid
    End Sub

    Sub New(ByVal identifier As String, ByVal itemid As String, ByVal version As String, ByVal downInfo As MultiThreadDownloadItemInfo, ByVal state As SoftDownloadErrorState)
        Me.mIdentifier = identifier
        Me.mVersion = version
        Me.mState = state
        Me.mItemid = itemid
        Me.mDownloadInfo = downInfo
    End Sub

    Sub New(ByVal identifier As String, ByVal itemid As String, ByVal version As String, ByVal downInfo As MultiThreadDownloadItemInfo, ByVal state As SoftDownloadErrorState, ByVal strError As String)
        Me.mIdentifier = identifier
        Me.mVersion = version
        Me.mState = state
        Me.mDownloadInfo = downInfo
        Me.mItemid = itemid
        Me.mErrorDescription = strError
    End Sub

    Sub New(ByVal identifier As String, ByVal itemid As String, ByVal version As String, ByVal downInfo As MultiThreadDownloadItemInfo, ByVal state As SoftDownloadErrorState, ByVal strError As String, ByVal isFailed As Boolean)
        Me.mIdentifier = identifier
        Me.mVersion = version
        Me.mState = state
        Me.mDownloadInfo = downInfo
        Me.mErrorDescription = strError
        Me.mItemid = itemid
        Me.mIsFailed = isFailed
    End Sub

End Class

Public Class ShowAppleIdLoginArgs
    Inherits EventArgs
    Private mDevice As iPhoneDevice = Nothing
    Private mMode As LoginMode = LoginMode.None
    Private mInfo As PackageInfo = Nothing
    Private mId As String = String.Empty
    Private mLoginSucceed As Boolean = False

    Public Property Device() As iPhoneDevice
        Get
            Return Me.mDevice
        End Get
        Set(value As iPhoneDevice)
            Me.mDevice = value
        End Set
    End Property

    Public Property Mode() As LoginMode
        Get
            Return Me.mMode
        End Get
        Set(value As LoginMode)
            Me.mMode = value
        End Set
    End Property

    Public Property Info() As PackageInfo
        Get
            Return Me.mInfo
        End Get
        Set(value As PackageInfo)
            Me.mInfo = value
        End Set
    End Property

    Public Property Id() As String
        Get
            Return Me.mId
        End Get
        Set(value As String)
            Me.mId = value
        End Set
    End Property

    Public Property LoginSucceed() As Boolean
        Get
            Return Me.mLoginSucceed
        End Get
        Set(value As Boolean)
            Me.mLoginSucceed = value
        End Set
    End Property

End Class

Public Class ShowAppDownloadedArgs
    Inherits EventArgs
    Private mInfo As PackageInfo = Nothing

    Public Property Info() As PackageInfo
        Get
            Return Me.mInfo
        End Get
        Set(value As PackageInfo)
            Me.mInfo = value
        End Set
    End Property
    Public Sub New(pInfo As PackageInfo)
        Me.mInfo = pInfo
    End Sub

End Class
