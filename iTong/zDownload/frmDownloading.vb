﻿Imports System.Xml
Imports System.Text
Imports System.IO
Imports System.Data
Imports iTong.CoreFoundation
Imports iTong.Components

Public Delegate Sub DownloadAddHandler(ByRef list As List(Of MultiThreadDownloadItem), ByVal blnInsertBegin As Boolean, ByVal blnLoadFronDB As Boolean)
Public Delegate Sub DownloadAddOneItemHandler(ByVal item As MultiThreadDownloadItem, ByVal index As Integer, ByVal blnLoadFronDB As Boolean)

Public Class frmDownloading

    Private mCoreUpdateHelper As CoreUpdateHelper = Nothing
    Private mDownManager As MultiThreadDownload
    Private mIsLoad As Boolean = False
    Private mSortIndexChanged As Boolean = False
    Private mLstUpdating115Url As New List(Of Long)
    Private mLstSoftID As New List(Of Long)
    Private mIconSize As New Size(40, 40)
    Private mApp As IApplication

    Public Property mApplication() As IApplication
        Get
            Return Me.mApp
        End Get
        Set(ByVal value As IApplication)
            Me.mApp = value
        End Set
    End Property

    Friend WithEvents dgvTask As New tbDataGridViewEx

    Private Enum DownloadState
        Pause = 0
        Downloading = 1
        Waiting = 2
        Failed = 3
    End Enum

#Region "--- 初始化 ---"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong

        Me.Name = ""
        Me.FunctionMappingKey = FunctionKey.Download
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitDataGridView()
        Me.InitDownloadManager()

        Me.mCoreUpdateHelper = CoreUpdateHelper.Instance()
        AddHandler Me.mCoreUpdateHelper.CheckDownloadEventHandler, AddressOf OnCheckDownload
        AddHandler Me.mCoreUpdateHelper.CombineSingling, AddressOf OnCombineSingling

        Me.LoadDownloadData()
        If Me.dgvTask.Rows.Count = 0 Then
            Me.btnDelete.Enabled = False
            Me.btnStart.Enabled = False
            Me.btnStop.Enabled = False
        End If

        MainForm.SetButtonStatus(Me.btnStart)
        MainForm.SetButtonStatus(Me.btnStop)
        MainForm.SetButtonStatus(Me.btnDelete)

        '海外版没有QQ的方式
        If Folder.LangType <> LanguageType.zh_CN Then
            Me.tsmiJoinQQ.Visible = False
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.txtSearch.SearchTipText = Me.Language.GetString("Download.Label.FilterFileName")         '"过滤文件名"
        Me.btnStart.tbText = Me.Language.GetString("Download.Button.Start")                         '"开始"
        Me.btnStop.tbText = Me.Language.GetString("Download.Button.Pause")                          '"暂停"
        Me.btnDelete.tbText = Me.Language.GetString("Common.Delete")                                '"删除"

        Me.tsmiOpenExplorer.Text = Me.Language.GetString("Common.Button.OpenLocalFolder")           '"打开文件夹"
        Me.tsmiDeleteItem.Text = Me.Language.GetString("Common.Delete")                             '"删除" 
        Me.Text = Me.Language.GetString("Main.Button.DownloadCenter")                               '"下载中心

        Me.tsmiJoinQQ.Text = Me.Language.GetString("Common.Button.JoinQQ")           '"加入助手讨论群"
        Me.tsmiShowError.Text = Me.Language.GetString("Common.Button.ShowError")           '"查看失败原因"
        Me.timiTunesDowmload.Text = Me.Language.GetString("Common.Button.iTunesDowmload")           '"到iTunes下载该应用程序"

    End Sub

    Private Sub InitDataGridView()
        With dgvTask
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.ForeColor = Color.Black

            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Common.Label.Name"), _
                                                                                               210, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            colName.DefaultCellStyle = cellStyle

            .ContextMenuStrip = Me.menuDownload

            .SuspendLayout()

            .RowTemplate.Height = 58
            .ScrollBars = System.Windows.Forms.ScrollBars.Both

            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", _
                                                                    26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(colName)
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), _
                                                                     80, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", Me.Language.GetString("Download.Label.Progress"), _
                                                                    200, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))             '"进度"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colControl", Me.Language.GetString("Download.Label.Operate"), _
                                                                    160, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))             '"操作"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colOther", "", 100.0!, True, True, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colurl", "", 1, False, True, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))

            Me.Controls.Add(dgvTask)
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .tbNoDataImage = My.Resources.icon_downnodata_100
            .ResumeLayout()
        End With

        RemoveHandler dgvTask.CellButtonClick, AddressOf dgvTask_CellButtonClick
        AddHandler dgvTask.CellButtonClick, AddressOf dgvTask_CellButtonClick

        RemoveHandler dgvTask.ShowToolTip, AddressOf dgvTask_ShowToolTip
        AddHandler dgvTask.ShowToolTip, AddressOf dgvTask_ShowToolTip

        RemoveHandler dgvTask.SelectionChanged, AddressOf dgvTask_SelectionChanged
        AddHandler dgvTask.SelectionChanged, AddressOf dgvTask_SelectionChanged

        dgvTask.CopyValue = New EventHandler(AddressOf dgvTask_CopyValue)

        Utility.AddForm2Panel(Me.dgvTask, Me.pnlMain)
    End Sub

    Private Sub InitDownloadManager()
        Me.mDownManager = MultiThreadDownload.Instance()

        AddHandler Me.mDownManager.TaskStart, AddressOf OnTaskStart
        AddHandler Me.mDownManager.TaskAdd, AddressOf OnTaskAdd
        AddHandler Me.mDownManager.TaskUpdate, AddressOf OnTaskUpdate
        AddHandler Me.mDownManager.TaskDelete, AddressOf OnTaskDelete
        AddHandler Me.mDownManager.DownloadItemCallBack, AddressOf OnDownloading
        AddHandler Me.mDownManager.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler Me.mDownManager.IconDownloadedEvent, AddressOf OnIconDownloaded
        AddHandler Me.mDownManager.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        RemoveHandler Me.mDownManager.TaskStart, AddressOf OnTaskStart
        RemoveHandler Me.mDownManager.TaskAdd, AddressOf OnTaskAdd
        RemoveHandler Me.mDownManager.TaskUpdate, AddressOf OnTaskUpdate
        RemoveHandler Me.mDownManager.TaskDelete, AddressOf OnTaskDelete
        RemoveHandler Me.mDownManager.DownloadItemCallBack, AddressOf OnDownloading
        RemoveHandler Me.mDownManager.DownloadItemCompleted, AddressOf OnDownloaded
        RemoveHandler Me.mDownManager.IconDownloadedEvent, AddressOf OnIconDownloaded
        RemoveHandler Me.mDownManager.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed

        RemoveHandler Me.mCoreUpdateHelper.CheckDownloadEventHandler, AddressOf OnCheckDownload
        RemoveHandler Me.mCoreUpdateHelper.CombineSingling, AddressOf OnCombineSingling
    End Sub

    Private Sub btnStart_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStart.SizeChanged, btnStop.SizeChanged, btnDelete.SizeChanged
        Me.btnStart.Location = New Point(10, Me.btnStart.Top)
        Me.btnStop.Location = New Point(Me.btnStart.Right + 10, Me.btnStop.Top)
        Me.btnDelete.Location = New Point(Me.btnStop.Right + 10, Me.btnDelete.Top)
    End Sub

#End Region

#Region "--- 按钮事件 ---"

    Private Sub btnMoveUp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveUp.Click
        'Dim itemInfo As MultiThreadDownloadItemInfo = Me.MakeDownloadInfo("同步推.ipa", "http://t.tongbu.com/tbtui/tbtui_v1.092.ipa")

        'Me.DownloadAppInfo(itemInfo)
        'Return

        If Me.dgvTask.SelectedRows.Count = 0 Then
            Return
        End If

        Dim listItems As New List(Of MultiThreadDownloadItem)
        Dim dicRows As New Dictionary(Of Integer, DataGridViewRow)
        Dim lstKeys As New List(Of Integer)
        For Each row As DataGridViewRow In Me.dgvTask.SelectedRows
            If (row.Tag Is Nothing) OrElse (Not TypeOf (row.Tag) Is MultiThreadDownloadItem) Then
                Continue For
            End If
            lstKeys.Add(row.Index)
            dicRows.Add(row.Index, row)
        Next

        lstKeys.Sort()

        Me.dgvTask.SuspendLayout()
        For intI As Integer = 0 To lstKeys.Count - 1
            Dim oldIndex As Integer = lstKeys(intI)
            Dim newIndex As Integer = oldIndex - 1
            Dim row As DataGridViewRow = dicRows(oldIndex)

            If newIndex < 0 Then
                newIndex = Me.dgvTask.Rows.Count - 1

                Me.dgvTask.Rows.Remove(row)
                Me.dgvTask.Rows.Insert(newIndex, row)
                Me.dgvTask.Rows(newIndex).Selected = True

                Exit For
            Else
                Me.dgvTask.Rows.Remove(row)
                Me.dgvTask.Rows.Insert(newIndex, row)
                Me.dgvTask.Rows(oldIndex).Selected = False
                Me.dgvTask.Rows(newIndex).Selected = True
            End If

        Next
        Me.dgvTask.ResumeLayout()

        mSortIndexChanged = True
    End Sub

    Private Sub btnMoveDown_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMoveDown.Click
        If Me.dgvTask.SelectedRows.Count = 0 Then
            Return
        End If

        Dim dicRows As New Dictionary(Of Integer, DataGridViewRow)
        Dim lstKeys As New List(Of Integer)
        For Each row As DataGridViewRow In Me.dgvTask.SelectedRows
            If (row.Tag Is Nothing) OrElse (Not TypeOf (row.Tag) Is MultiThreadDownloadItem) Then
                Continue For
            End If
            lstKeys.Add(row.Index)
            dicRows.Add(row.Index, row)
        Next

        lstKeys.Sort()

        Me.dgvTask.SuspendLayout()
        For intI As Integer = lstKeys.Count - 1 To 0 Step -1
            Dim oldIndex As Integer = lstKeys(intI)
            Dim newIndex As Integer = oldIndex + 1
            Dim row As DataGridViewRow = dicRows(oldIndex)

            If newIndex > Me.dgvTask.Rows.Count - 1 Then
                newIndex = 0

                Me.dgvTask.Rows.Remove(row)
                Me.dgvTask.Rows.Insert(newIndex, row)
                Me.dgvTask.Rows(newIndex).Selected = True

                Exit For
            Else
                Me.dgvTask.Rows.Remove(row)
                Me.dgvTask.Rows.Insert(newIndex, row)
                Me.dgvTask.Rows(oldIndex).Selected = False
                Me.dgvTask.Rows(newIndex).Selected = True
            End If

        Next
        Me.dgvTask.ResumeLayout()

        mSortIndexChanged = True
    End Sub

    Private Sub btnStart_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStart.Click
        If Me.dgvTask Is Nothing OrElse Me.dgvTask.SelectedRows.Count = 0 Then
            '"请选择要开始的任务。"
            tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.SelectDutyToStart"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Dim isDownload As Boolean = True
        Dim lstRows As New List(Of DataGridViewRow)
        For Each rowItem As DataGridViewRow In Me.dgvTask.SelectedRows
            lstRows.Add(rowItem)
        Next

        Me.SortRows(lstRows)

        For Each rowItem As DataGridViewRow In lstRows
            If Not isDownload Then
                Return
            End If

            isDownload = Me.Start2Download(CType(rowItem.Tag, MultiThreadDownloadItem))
        Next
    End Sub

    Private Sub SortRows(ByRef lstRows As List(Of DataGridViewRow), Optional ByVal blnAsc As Boolean = True)
        Dim rowTemp As DataGridViewRow = Nothing
        Dim blnChanged As Boolean = True

        If lstRows Is Nothing OrElse lstRows.Count < 2 Then
            Return
        End If

        While blnChanged
            blnChanged = False
            For index As Integer = 0 To lstRows.Count - 2
                If blnAsc Then
                    If lstRows(index).Index > lstRows(index + 1).Index Then
                        rowTemp = lstRows(index)
                        lstRows(index) = lstRows(index + 1)
                        lstRows(index + 1) = rowTemp
                        blnChanged = True

                    End If
                Else
                    If lstRows(index).Index < lstRows(index + 1).Index Then
                        rowTemp = lstRows(index)
                        lstRows(index) = lstRows(index + 1)
                        lstRows(index + 1) = rowTemp
                        blnChanged = True

                    End If

                End If

            Next

        End While


    End Sub

    Private Function Start2Download(ByVal item As MultiThreadDownloadItem) As Boolean
        Dim isDownload As Boolean = True

        Try
            Dim isWaiting As Boolean = False
            If item Is Nothing OrElse Me.mDownManager.IsWaitingOrDownloading(item, isWaiting) Then
                Return isDownload
            End If

            ''需要重置下载失败的状态，以便失败后能自己重试
            'item.ItemInfo.IsNeedTryDown = True
            Me.mDownManager.Start(item)

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

        Return isDownload
    End Function

    Private Sub btnStop_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStop.Click
        If Me.dgvTask Is Nothing OrElse Me.dgvTask.SelectedRows.Count = 0 Then
            '"请选择要暂停的任务。"
            tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.SelectTaskToPause"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        For Each rowItem As DataGridViewRow In Me.dgvTask.SelectedRows
            'Dim item As MultiThreadDownloadItem = rowItem.Tag
            'If item.ItemInfo.Status = TaskState.Download Then
            Me.mDownManager.Stop(CType(rowItem.Tag, MultiThreadDownloadItem))
            'End If
        Next
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click, tsmiDeleteItem.Click
        If Me.dgvTask Is Nothing OrElse Me.dgvTask.SelectedRows.Count = 0 Then
            '"请选择要删除的任务。"
            tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.SelectTaskToDelete"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        '"您确定删除任务吗？"
        If tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.AreYouSureDeleteTask"), _
                            Me.Language.GetString("Common.Delete"), _
                            MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            '"您确定删除任务吗？"
            '"删除"
            For Each rowItem As DataGridViewRow In Me.dgvTask.SelectedRows
                Dim item As MultiThreadDownloadItem = rowItem.Tag
                If item Is Nothing Then
                    Continue For
                End If
                Me.mDownManager.Delete(item)
            Next
        End If
    End Sub

    Private Sub btnOpen_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOpen.Click, tsmiOpenExplorer.Click
        Try
            If Me.dgvTask Is Nothing OrElse Me.dgvTask.SelectedRows.Count = 0 Then
                '"请选择要打开的文件！"
                tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.SelectFileToOpen"), _
                                                Me.Language.GetString("Common.Info"), _
                                                MessageBoxButtons.OK, MessageBoxIcon.Information)

                Return
            End If

            Dim item As MultiThreadDownloadItem = Me.dgvTask.SelectedRows(0).Tag
            Dim strFilePath As String = String.Empty

            If File.Exists(item.ItemInfo.TempPath) Then
                strFilePath = item.ItemInfo.TempPath
            End If

            If strFilePath = String.Empty AndAlso File.Exists(item.ItemInfo.DownloadPath) Then
                strFilePath = item.ItemInfo.DownloadPath
            End If

            If strFilePath = String.Empty AndAlso File.Exists(item.ItemInfo.FilePath) Then
                strFilePath = item.ItemInfo.FilePath
            ElseIf strFilePath = String.Empty AndAlso File.Exists(item.ItemInfo.DownloadPath) Then
                strFilePath = item.ItemInfo.DownloadPath
            ElseIf strFilePath = String.Empty Then
                strFilePath = Path.GetDirectoryName(item.ItemInfo.DownloadPath)

            End If

            If strFilePath = String.Empty Then
                '"您要打开的文件不存在！"
                tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.FileNoExist"), _
                                                Me.Language.GetString("Common.Info"), _
                                                MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            Common.OpenExplorer(strFilePath)
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub txtSearch_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChangedByTimer
        DoSearch()
    End Sub

    Private Sub txtSearch_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtSearch.KeyDown
        If e.KeyCode = Keys.Enter Then
            DoSearch()
        End If
    End Sub

    Private Sub dgvTask_CellDoubleClick(ByVal sender As Object, ByVal e As DataGridViewCellEventArgs)
        If Me.dgvTask.CurrentRow Is Nothing OrElse e.RowIndex < 0 Then
            Return
        End If

        For Each rowItem As DataGridViewRow In Me.dgvTask.SelectedRows
            Dim item As MultiThreadDownloadItem = rowItem.Tag

            If Me.mDownManager.IsWaitingOrDownloading(item, False) = True Then
                Me.mDownManager.Stop(item)
            Else
                Me.Start2Download(item)
            End If
        Next
    End Sub

    Private Sub btn_Visible_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.Hide()
    End Sub

    Private Sub dgvTask_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvTask.Rows(e.Cell.RowIndex)

            Select Case e.CellButton
                Case CellButtonStyle.Info, CellButtonStyle.Reinstall
                    Me.GotoAppDetail(row)

                Case CellButtonStyle.Backup
                    Dim state As DownloadState = CType(row.Cells("colOther").Tag, DownloadState)
                    Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                    cellControl.tbCellButtonEnable = CellButtonStyle.Uninstall
                    Select Case state
                        Case DownloadState.Downloading, DownloadState.Waiting
                            '变成暂停
                            Me.mDownManager.Stop(CType(row.Tag, MultiThreadDownloadItem))

                        Case DownloadState.Failed, DownloadState.Pause
                            '变成开始
                            Me.Start2Download(CType(row.Tag, MultiThreadDownloadItem))

                    End Select
                Case CellButtonStyle.Uninstall
                    '"您确定删除任务吗？"
                    If tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.AreYouSureDeleteTask"), _
                                        Me.Language.GetString("Common.Delete"), _
                                        MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then

                        Dim item As MultiThreadDownloadItem = Me.dgvTask.Rows(e.Cell.RowIndex).Tag
                        Me.mDownManager.Delete(item)
                    End If

                Case CellButtonStyle.Cancel

                Case CellButtonStyle.BackupFolder

                Case CellButtonStyle.Failed

                Case CellButtonStyle.ProgressSpeed '2015-08-03 by chenbihai 下载失败 点击出现菜单
                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                    If cellProgress.tbProgressMsgClick Then
                        menuDownloadError.Show(System.Windows.Forms.Cursor.Position.X, System.Windows.Forms.Cursor.Position.Y)
                    End If
            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Sub dgvTask_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvTask.Rows(e.Cell.RowIndex)
            Dim pos As Point = Windows.Forms.Cursor.Position
            Dim secPos As Point = Me.PointToClient(pos)
            Dim newPos As New Point(secPos.X + 10, secPos.Y + 10)

            Select Case e.CellButton
                Case CellButtonStyle.Info
                    Me.tipDgvCell.Show(Me.Language.GetString("App.Tip.AppDetail"), Me, newPos) '"查看软件介绍"

                Case CellButtonStyle.Backup
                    Dim state As DownloadState = CType(row.Cells("colOther").Tag, DownloadState)
                    Dim strText As String = ""
                    Select Case state
                        Case DownloadState.Downloading, DownloadState.Waiting
                            strText = Me.Language.GetString("Download.Button.Pause")        '"暂停"
                        Case DownloadState.Failed
                            strText = Me.Language.GetString("Download.Button.Restart")      '"重新开始"
                        Case DownloadState.Pause
                            strText = Me.Language.GetString("Download.Button.Start")         '"开始"
                    End Select
                    Me.tipDgvCell.Show(strText, Me, newPos)
                Case CellButtonStyle.Uninstall
                    Me.tipDgvCell.Show(Me.Language.GetString("Common.Delete"), Me, newPos)  '"删除 "
                Case CellButtonStyle.Cancel

                Case CellButtonStyle.BackupFolder

                Case CellButtonStyle.None
                    Me.tipDgvCell.Hide(Me)

                Case CellButtonStyle.ProgressSpeed
                    Dim strText As String = CType(e.Cell, tbDataGridViewProgressCellEx).tbToolTipText
                    If Not String.IsNullOrEmpty(strText) Then
                        Me.tipDgvCell.Show(strText, Me, newPos)
                    End If

            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Sub dgvTask_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If sender Is Nothing Then
            Return
        End If

        Me.ChangeMenuButtonState()
    End Sub

    Private Sub dgvTask_CopyValue(ByVal sender As System.Object, ByVal e As EventArgs)
        Dim dgv As tbDataGridView = sender
        Try
            Dim sb As New StringBuilder()
            For intI As Integer = dgv.SelectedRows.Count - 1 To 0 Step -1
                Dim row As DataGridViewRow = dgv.SelectedRows(intI)
                If row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is MultiThreadDownloadItem Then
                    Dim item As MultiThreadDownloadItem = row.Tag
                    sb.AppendLine(String.Format("{0}{1}{2}{3}{4}", row.Cells("colName").Value, vbTab, row.Cells("colSize").Value, vbTab, item.ItemInfo.Url))
                End If
            Next

            If sb.Length > 0 Then
                My.Computer.Clipboard.SetText(sb.ToString())
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Sub tsmiJoinQQ_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiJoinQQ.Click
        Common.OpenExplorer("http://qm.qq.com/cgi-bin/qm/qr?k=ueg4XTkcER2TXfIYxLnDEFGr1DDcn_AV") '群链接生成方法  打开要生成链接的QQ群  群应用--更多--分享群--复制加群链接
    End Sub

    Private Sub timiTunesDowmload_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles timiTunesDowmload.Click
        Try
            If Me.dgvTask.CurrentRow Is Nothing Then
                Return
            End If
            Dim downloadItem As MultiThreadDownloadItem = CType(Me.dgvTask.CurrentRow.Tag, MultiThreadDownloadItem)
            iPhoneDeviceHelper.NavigateSoft2iTunes(downloadItem.ItemInfo.ItemId, downloadItem.ItemInfo.StoreId, Me.Language)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "timiiTunesDowmload_Click")
        End Try
    End Sub

    Private Sub tsmiShowError_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiShowError.Click
        Try
            If Me.dgvTask.CurrentRow Is Nothing Then
                Return
            End If

            Dim downloadItem As MultiThreadDownloadItem = CType(Me.dgvTask.CurrentRow.Tag, MultiThreadDownloadItem)
            If downloadItem.ItemInfo.AppleMsg Is Nothing Then
                Return
            End If

            Dim appleMsg As AppleMsg = CType(downloadItem.ItemInfo.AppleMsg, AppleMsg)
            Dim strMsg As String = appleMsg.CustomerMessage

            If String.IsNullOrEmpty(appleMsg.FailureType) = False Then
                strMsg = String.Format("{0}{1}ErrorCode:{2}", appleMsg.CustomerMessage, vbLf, appleMsg.FailureType)
            End If

            tbMessageBox.Show(Me, _
                                                 strMsg, _
                                                  LanguageInterface.Instance().GetString("Common.FailureReason"), _
                                                  MessageBoxButtons.OK, _
                                                  MessageBoxIcon.Warning, _
                                                  MessageBoxDefaultButton.Button1, "", False, Nothing, appleMsg.Explanation)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "tsmiShowError_Click")
        End Try
    End Sub

#Region "---  底部菜单事件  ---"

    'Private Shared frmDown As frmDownload
    'Public Shared Sub ShowForm(ByVal frmMain As Form)
    '    Try
    '        If frmDown Is Nothing Then
    '            frmDown = New frmDownload()
    '            frmDown.StartPosition = FormStartPosition.Manual
    '        End If

    '        Dim pos As Point = frmMain.Location
    '        Dim posNew As Point = New Point(pos.X + (frmMain.Width - frmDown.Width) \ 2, pos.Y + (frmMain.Height - frmDown.Height) \ 2 + 20)
    '        frmDown.Location = posNew
    '        frmDown.mApplication = frmMain
    '        If Not frmDown.Visible Then
    '            frmDown.Show()
    '        End If
    '        frmDown.Activate()
    '    Catch ex As Exception
    '    End Try
    'End Sub

    Private Sub btnApp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.GotoItem(ActionFuncType.App)
    End Sub

    Private Sub btnMusic_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.GotoItem(ActionFuncType.Music)
    End Sub

    Private Sub btnRingtone_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.GotoItem(ActionFuncType.Ringtone)
    End Sub

    Private Sub btnWallpaper_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.GotoItem(ActionFuncType.Photo)
    End Sub

    Private Sub GotoItem(ByVal actType As ActionFuncType)
        If Me.mApplication IsNot Nothing Then
            Me.mApplication.GotoItemLocal(actType)
            Me.Hide()
        End If
    End Sub

#End Region

#End Region

#Region "--- 搜索 ---"

    Private Sub DoSearch()
DO_RETRY:
        Try
            Dim searchText As String = Me.txtSearch.Text.Trim().ToLower()
            For Each row As DataGridViewRow In Me.dgvTask.Rows
                If row.Cells("colName").Value.ToString().ToLower().IndexOf(searchText) >= 0 Then
                    row.Visible = True
                Else
                    row.Visible = False
                End If
            Next
        Catch ex As Exception
            GoTo DO_RETRY
        End Try
    End Sub

#End Region

#Region "--- 下载事件 ---"

    Public Sub LoadDownloadData()
        Try
            If mIsLoad = False Then
                mIsLoad = True

                Dim lstDownload As New List(Of TaskState)
                lstDownload.Add(TaskState.Analysing)
                lstDownload.Add(TaskState.Combining)
                lstDownload.Add(TaskState.Download)
                lstDownload.Add(TaskState.Waiting)
                Me.AddDownloadTask(Me.mDownManager.GetItems(ResourceClass.All, ResourceType.All, lstDownload, ""), True, True)
                lstDownload.Clear()
                lstDownload = Nothing

                Me.AddDownloadTask(Me.mDownManager.GetItems(ResourceClass.All, ResourceType.All, TaskState.Failed), False, True)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub AddDownloadTask(ByRef listTask As List(Of MultiThreadDownloadItem), Optional ByVal blnInsertBegin As Boolean = False, Optional ByVal blnLoadFronDB As Boolean = False)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New DownloadAddHandler(AddressOf AddDownloadTask), New Object() {listTask, blnInsertBegin, blnLoadFronDB})
            Else
                Me.dgvTask.SuspendLayout()
                Dim index As Integer = 0
                For Each item As MultiThreadDownloadItem In listTask
                    If item.ItemInfo.Class = ResourceClass.Plugins Then
                        Continue For
                    End If
                    If blnInsertBegin Then
                        Me.AddDownloadTaskByItem(item, index, blnLoadFronDB)
                        index += 1
                    Else
                        Me.AddDownloadTaskByItem(item, -1, blnLoadFronDB)
                    End If
                    Application.DoEvents()
                Next

                Me.dgvTask.ResumeLayout()

                UpdateParentTreeCount()

            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub AddDownloadTaskByItem(ByVal item As MultiThreadDownloadItem, Optional ByVal index As Integer = -1, Optional ByVal blnLoadFronDB As Boolean = False)
        Try
            Dim info As MultiThreadDownloadItemInfo = item.ItemInfo

            If info.Class = ResourceClass.Plugins Then
                Return
            End If


#If IS_ITONG_ZJ Then
            '如果是插件或者强装的软件下载，则不显示进度
            If StrongInstallHelper.Instance().CheckIsStrongInstall(info.FilePath) Then
                Return
            End If
#End If

            '如果已经存在，则不继续添加
            If Me.mLstSoftID.Contains(info.TaskID) Then
                Return
            End If
            Me.mLstSoftID.Add(info.TaskID)

            Common.Log("下载记录加载到界面:" & item.ItemInfo.Name)

            Dim rowIndex As Integer = -1
            If index >= Me.dgvTask.RowCount Then
                index = -1
            End If

            If index > -1 Then
                Me.dgvTask.Rows.Insert(index, False, _
                                             Me.GetTaskName(info), _
                                              IIf(info.ResourceSize <= 0, "", Utility.FormatFileSize(info.ResourceSize)), _
                                              "", _
                                              "", _
                                              "", _
                                              info.Url)

                rowIndex = index
            Else
                rowIndex = Me.dgvTask.Rows.Add(False, _
                                     Me.GetTaskName(info), _
                                    IIf(info.ResourceSize <= 0, "", Utility.FormatFileSize(info.ResourceSize)), _
                                    "", _
                                    "", _
                                    "", _
                                    info.Url)


            End If
            Dim row As tbDataGridViewRow = Me.dgvTask.Rows(rowIndex)
            If row IsNot Nothing Then
                Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                Dim cellOther As tbDataGridViewProgressCellEx = row.Cells("colOther")
                cellName.tbIconSize = Me.mIconSize
                cellName.tbIconShadow = True
                If item.ItemInfo.Class = ResourceClass.Software Then
                    Dim imageIcon As Image = Me.GetIconImage(item)
                    If imageIcon IsNot Nothing Then
                        cellName.tbIcon = imageIcon.Clone
                        imageIcon.Dispose()
                        imageIcon = Nothing
                    Else
                        cellName.tbIcon = My.Resources.app_icon_default
                    End If
                    cellName.tbShowIconInfo = True
                    '显示跳转到详情页面
                    cellOther.tbPadding = New Padding(0, cellOther.tbPadding.Top, 10, cellOther.tbPadding.Bottom)
                    cellOther.tbProgressStyle = ProgressStyle.Reinstall
                    cellOther.tbTextReInstall = Me.Language.GetString("App.Tip.AppDetail")
#If IS_ITONG Then
                ElseIf item.ItemInfo.Class = ResourceClass.Music Then
                    cellName.tbIcon = My.Resources.download_icon_music

                ElseIf item.ItemInfo.Class = ResourceClass.Ringtone Then
                    cellName.tbIcon = My.Resources.download_icon_ringtone

                ElseIf item.ItemInfo.Class = ResourceClass.Wallpaper Then
                    cellName.tbIcon = My.Resources.download_icon_wallpaper

                ElseIf item.ItemInfo.Class = ResourceClass.Video Then
                    cellName.tbIcon = My.Resources.download_icon_music

                ElseIf item.ItemInfo.Class = ResourceClass.Emoticon Then
                    cellName.tbIcon = My.Resources.download_icon_weixinemoticon

                ElseIf item.ItemInfo.Class = ResourceClass.Firmware Then
                    cellName.tbIcon = My.Resources.download_icon_firmware

                Else
                    '默认显示的icon
                    cellName.tbIcon = My.Resources.download_icon_wallpaper
#End If

                End If

                '暂停状态
                Dim state As DownloadState = DownloadState.Pause
                Dim strText As String = Me.Language.GetString("Download.Button.Pause")                '"暂停"
                Dim imgControl As Image = My.Resources.dgv_4_download_start

                '等待状态
                If Me.mDownManager.IsWaitingOrDownloading(item, True) Then
                    state = DownloadState.Waiting
                    strText = Me.Language.GetString("Download.Label.Waiting")           '"Waiting"
                    imgControl = My.Resources.dgv_4_download_pause
                End If

                Select Case info.Status
                    Case TaskState.Waiting
                        If item.ItemInfo.Source <> TaskSource.AppleDownload Then
                            state = DownloadState.Waiting
                            strText = Me.Language.GetString("Download.Label.Waiting")           '"Waiting"
                            imgControl = My.Resources.dgv_4_download_pause
                        End If

                    Case TaskState.Failed
                        state = DownloadState.Failed
                        strText = Me.Language.GetString("Download.Label.Failed")            '"Failed"
                End Select

                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                cellProgress.tbProgressStyle = ProgressStyle.DownloadProgress
                cellProgress.tbProgressValue = info.Progress
                cellProgress.tbSpeed = strText
                cellProgress.tbTime = ""

                Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                cellControl.tbPadding = New Padding(40, 0, 0, 0)
                cellControl.tbProgressStyle = ProgressStyle.Button
                cellControl.tbShowButtonText = False
                cellControl.tbButtonSize = New Size(18, 18)
                cellControl.tbButtonImgFirst = imgControl
                cellControl.tbButtonImgSecond = My.Resources.dgv_4_delete
                cellControl.tbShowButtonThird = False
                row.Tag = item
                cellOther.Tag = state

            End If

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    'Private Function CheckItemWaitting(ByVal donwItem As MultiThreadDownloadItem) As Boolean
    '    Dim blnReturn As Boolean = False

    '    For Each Item As MultiThreadDownloadItem In Me.mDownManager.WaitingList
    '        If donwItem.DownloadInfo.FilePath = Item.DownloadInfo.FilePath Then
    '            blnReturn = True
    '        End If
    '    Next
    '    Return blnReturn
    'End Function

    Private Sub OnCheckDownload(ByVal sender As Object, ByVal args As CheckDownloadArgs)
        Try
            Dim row As tbDataGridViewRow = Nothing
            For Each rowTemp As DataGridViewRow In Me.dgvTask.Rows
                Dim rowItem As MultiThreadDownloadItem = rowTemp.Tag

                If rowItem.ItemInfo.Source = TaskSource.AppleDownload AndAlso _
                   rowItem.ItemInfo.ItemId = args.Itemid AndAlso _
                   rowItem.ItemInfo.Version = args.Version Then

                    row = rowTemp
                    If args.DownloadInfo IsNot Nothing Then
                        CType(row.Tag, MultiThreadDownloadItem).ItemInfo.AppleMsg = args.DownloadInfo.AppleMsg
                    End If

                    Exit For
                End If
            Next

            If row Is Nothing Then
                Return
            End If


            Select Case args.State
                Case SoftDownloadErrorState.Nomal
                    '开始按钮恢复可以点击
                    Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                    cellControl.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall

                Case Else
                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                    cellProgress.tbSpeed = args.ErrorDescription

                    cellProgress.tbProgressStyle = ProgressStyle.DownloadProgress
                    cellProgress.tbProgressValue = args.DownloadInfo.Progress
                    cellProgress.tbSpeed = args.ErrorDescription
                    cellProgress.tbTime = ""

                    If args.IsFailed Then
                        cellProgress.tbProgressMsgClick = True

                        Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                        cellControl.tbButtonImgFirst = My.Resources.dgv_4_download_start
                        row.Cells("colOther").Tag = DownloadState.Failed
                    End If
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloading_OnCheckDownload")
        End Try
    End Sub

    Private Sub OnCombineSingling(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of MultiThreadDownloadEventArgs)(AddressOf OnCombineSingling), sender, e)
        Else
            If Me Is Nothing OrElse Me.IsDisposed = True Then
                Return
            End If

            Try
                Dim item As MultiThreadDownloadItem = sender
                If item.ItemInfo.ResourceSize = 0 Then
                    Return
                End If

                For Each row As DataGridViewRow In Me.GetDownloadRows()
                    Dim rowItem As MultiThreadDownloadItem = row.Tag
                    If rowItem.ItemInfo.TaskID = item.ItemInfo.TaskID Then
                        Me.dgvTask.SuspendLayout()

                        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                        cellProgress.tbSpeed = Me.Language.GetString("Download.Message.InOperation") ' "正在处理文件 ..."
                        cellProgress.tbTime = ""
                        cellProgress.tbProgressValue = 100
                        row.Cells("colOther").Tag = DownloadState.Downloading

                        Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                        cellControl.tbCellButtonEnable = CellButtonStyle.None

                        Me.dgvTask.ResumeLayout()

                        Exit For
                    End If
                Next

            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub OnTaskStart(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If Me.dgvTask.InvokeRequired Then
            Me.dgvTask.BeginInvoke(New EventHandler(Of MultiThreadDownloadItem)(AddressOf OnTaskStart), sender, item)
        Else
            Try
                For Each row As DataGridViewRow In Me.GetDownloadRows()
                    Dim rowItem As MultiThreadDownloadItem = row.Tag
                    If rowItem.ItemInfo.TaskID <> item.ItemInfo.TaskID Then
                        Continue For
                    End If

                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                    cellProgress.tbProgressValue = rowItem.ItemInfo.Progress
                    cellProgress.tbSpeed = Me.Language.GetString("Download.Label.Waiting")           '"Waiting"
                    cellProgress.tbTime = ""

                    Exit For
                Next

            Catch ex As Exception
                Common.LogException(ex.ToString(), "OnTaskStart")
            End Try
        End If
    End Sub

    Private Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.Class = ResourceClass.Plugins Then
                Return
            End If

            If Me.dgvTask.InvokeRequired Then
                Me.dgvTask.Invoke(New DownloadAddOneItemHandler(AddressOf AddDownloadTaskByItem), New Object() {item, -1, False})
            Else
                Common.Log("触发OnTaskAdd事件:" & item.ItemInfo.Name)

                Me.AddDownloadTaskByItem(item, -1)
            End If
            Me.UpdateParentTreeCount()
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub OnTaskUpdate(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If Me.dgvTask.InvokeRequired Then
            Me.dgvTask.BeginInvoke(New EventHandler(Of MultiThreadDownloadItem)(AddressOf OnTaskUpdate), sender, item)
        Else
            Try

                For Each row As DataGridViewRow In Me.GetDownloadRows()
                    Dim rowItem As MultiThreadDownloadItem = row.Tag
                    If rowItem.ItemInfo.TaskID = item.ItemInfo.TaskID Then
                        ' Dim imgStatus As Image = My.Resources.app_btn_4_ignore
                        Dim imgControl As Image = My.Resources.dgv_4_download_start
                        Dim state As DownloadState = DownloadState.Pause
                        Dim strText As String = Me.Language.GetString("Download.Button.Pause")      '"Pause"
                        Dim isWaiting As Boolean = False

                        If Me.mDownManager.IsWaitingOrDownloading(item, isWaiting) Then
                            imgControl = My.Resources.dgv_4_download_pause

                            If isWaiting Then
                                state = DownloadState.Waiting
                                strText = Me.Language.GetString("Download.Label.Waiting")           '"Waiting"
                            Else
                                state = DownloadState.Downloading
                                strText = ""
                            End If
                        End If

                        Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                        cellControl.tbButtonImgFirst = imgControl
                        cellControl.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall

                        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                        cellProgress.tbProgressValue = rowItem.ItemInfo.Progress
                        cellProgress.tbSpeed = strText
                        cellProgress.tbTime = ""

                        row.Cells("colOther").Tag = state

                        '重新赋值，item的属性可能已经发生变化
                        row.Tag = item

                        Exit For
                    End If
                Next

                '修改菜单按钮的状态
                '暂停变开始，会触发此事件
                Me.ChangeMenuButtonState()

            Catch ex As Exception
                Common.LogException(ex.ToString(), "OnTaskUpdate")
            End Try
        End If
    End Sub

    Private Sub OnTaskDelete(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        'Debug.Print(Me.Name & vbTab & item.ItemInfo.TaskID & vbTab & Me.dgvTask.Rows.Count)
        If Me.dgvTask.InvokeRequired Then
            Me.dgvTask.BeginInvoke(New EventHandler(Of MultiThreadDownloadItem)(AddressOf OnTaskDelete), sender, item)
        Else

            Try
                '上传软件下载失败的软件url信息，仅上传我们的软件
                If item.ItemInfo.Class = ResourceClass.Software AndAlso item.ItemInfo.Source <> TaskSource.AppleDownload Then
                    If item.ItemInfo.Status = TaskState.Failed Then
                        'Me.SendSoftDownloadStatus(item)
                        Me.SendDownloadInfo2Server(item)
                    End If
                End If

                For Each row As DataGridViewRow In Me.GetDownloadRows()
                    Dim rowItem As MultiThreadDownloadItem = row.Tag
                    If rowItem.ItemInfo.TaskID = item.ItemInfo.TaskID Then
                        If Me.mLstSoftID.Contains(item.ItemInfo.TaskID) Then
                            Me.mLstSoftID.Remove(item.ItemInfo.TaskID)
                        End If

                        Me.dgvTask.Rows.Remove(row)
                        Exit For
                    End If
                Next

                UpdateParentTreeCount()

            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub SendDownloadInfo2Server(ByVal item As MultiThreadDownloadItem)
        Dim appArg As New AppInstallStatusArgs()
        appArg.appleid = item.ItemInfo.ItemId
        appArg.AppVer = item.ItemInfo.Version
        appArg.type = "5"
        If item.ItemInfo.Jailbreak Then
            appArg.type = "7"
        End If
        appArg.loadid = Val(item.ItemInfo.LoadId)
        appArg.FilePath = item.ItemInfo.FilePath
        appArg.source = 2
        If item.ItemInfo.Source <> TaskSource.AppleDownload Then
            appArg.source = 1
        End If

        Try
            Dim loadInfo As New AppLoadArgs()
            loadInfo.code = AppLoadArgs.GetCode(AppInstallType.DownloadError)
            loadInfo.msg = item.DownloadInfo.LastErrorMsg
            loadInfo.size = item.DownloadInfo.ResourceSize
            loadInfo.source = appArg.source
            loadInfo.sp = item.DownloadInfo.Speed
            appArg.load.Add(loadInfo)
            HtmlHelper.Start2UploadAppInstallStatus(appArg)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmDownloading_SendInstallInfo2Server")
        End Try
    End Sub

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New EventHandler(Of MultiThreadDownloadEventArgs)(AddressOf OnDownloading), sender, e)
        Else
            If Me Is Nothing OrElse Me.IsDisposed = True Then
                Return
            End If

            Try
                Dim item As MultiThreadDownloadItem = sender
                If item.ItemInfo.ResourceSize = 0 Then
                    Return
                End If

#If IS_ITONG_ZJ Then
                '如果是插件或者强装的软件下载，则不显示进度
                If StrongInstallHelper.Instance().CheckIsStrongInstall(item.ItemInfo.FilePath) Then
                    Return
                End If
#End If

                For Each row As DataGridViewRow In Me.GetDownloadRows()
                    Dim rowItem As MultiThreadDownloadItem = row.Tag
                    If rowItem.ItemInfo.TaskID = item.ItemInfo.TaskID Then
                        Me.dgvTask.SuspendLayout()

                        'With row
                        '.Cells("Status").Value = My.Resources.app_btn_4_ignore
                        '.Cells("Name").Value = rowItem.ItemInfo.Name & vbCrLf & 
                        '.Cells("Progress").Value = e.Progress
                        '.Cells("LeftTime").Value = IIf(e.LeftTime < 0, "", FormatDuration(e.LeftTime))
                        '.Cells("Speed").Value = Utility.FormatFileSize(e.Speed) & "/s"

                        'End With
                        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                        cellProgress.tbProgressValue = e.Progress

                        If e.Progress = 0 AndAlso e.Speed = 0 Then
                            '如果进度为0 且为 0byte  提示改为 加载中，请稍候...
                            cellProgress.tbSpeed = Me.Language.GetString("Download.Label.Loading") '"加载中，请稍候..."
                            cellProgress.tbTime = ""
                        Else
                            cellProgress.tbSpeed = Utility.FormatFileSize(e.Speed) & "/S"
                            cellProgress.tbTime = IIf(e.LeftTime < 0, "--:--:--", Me.FormatDuration(e.LeftTime))
                        End If

                        row.Cells("colSize").Value = IIf(e.ResourceSize <= 0, "", Utility.FormatFileSize(e.ResourceSize))
                        row.Cells("colOther").Tag = DownloadState.Downloading

                        Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                        cellControl.tbButtonImgFirst = My.Resources.dgv_4_download_pause

                        Me.dgvTask.ResumeLayout()

                        Exit For
                    End If
                Next

            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloaded), sender, e)
        Else
            Try
                Dim item As MultiThreadDownloadItem = sender

#If IS_ITONG_ZJ Then
                '如果是插件或者强装的软件下载，则不显示进度
                If StrongInstallHelper.Instance().CheckIsStrongInstall(item.ItemInfo.FilePath) Then
                    Return
                End If
#End If

                If item.ItemInfo.Status = TaskState.Completed AndAlso Me.Name = TaskState.Completed.ToString() Then
                    Me.AddDownloadTaskByItem(item)
                Else
                    For Each row As DataGridViewRow In Me.GetDownloadRows()
                        Dim rowItem As MultiThreadDownloadItem = row.Tag

                        If rowItem.ItemInfo.TaskID = item.ItemInfo.TaskID Then
                            If e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
                                If Me.mLstSoftID.Contains(item.ItemInfo.TaskID) Then
                                    Me.mLstSoftID.Remove(item.ItemInfo.TaskID)
                                End If

                                Me.dgvTask.Rows.Remove(row)
                            Else

                                Me.dgvTask.SuspendLayout()

                                Dim imgControl As Image = My.Resources.dgv_4_download_start
                                Dim strText As String = ""

                                If e.Cancel = False Then
                                    If item.ItemInfo.Source = TaskSource.AppleDownload AndAlso item.ItemInfo.IsNeedTryDown Then
                                        strText = Me.Language.GetString("Download.Label.Retry")         '"Retry"
                                        row.Cells("colOther").Tag = DownloadState.Pause
                                        imgControl = My.Resources.dgv_4_download_start
                                    Else
                                        row.Cells("colOther").Tag = DownloadState.Failed
                                        strText = Me.Language.GetString("Download.Label.Failed")        '"Failed"
                                    End If
                                Else
                                    row.Cells("colOther").Tag = DownloadState.Pause
                                    strText = Me.Language.GetString("Download.Button.Pause")        '"Pause"
                                End If

                                Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                                cellControl.tbButtonImgFirst = imgControl
                                cellControl.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall

                                'If e.ResourceSize > 0 Then
                                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                                cellProgress.tbProgressValue = e.Progress
                                cellProgress.tbSpeed = strText
                                cellProgress.tbTime = ""
                                'End If

                                Me.dgvTask.ResumeLayout()
                            End If

                            Exit For
                        End If
                    Next
                End If

                '修改菜单按钮的状态
                '1、开始变暂停
                '2、下载完成后被移除
                '3、下载失败
                Me.ChangeMenuButtonState()

                UpdateParentTreeCount()

            Catch ex As Exception
                Debug.Print(ex.ToString)
                Common.Log(ex.ToString)
            End Try
        End If
    End Sub

    Private Sub OnIconDownloaded(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If Me.dgvTask.InvokeRequired Then
            Me.dgvTask.BeginInvoke(New EventHandler(Of MultiThreadDownloadItem)(AddressOf OnIconDownloaded), sender, item)
        Else

            Dim imageIcon As Image = Me.GetIconImage(item)
            If imageIcon Is Nothing Then
                Return
            End If

            Try
                For Each row As DataGridViewRow In Me.GetDownloadRows()
                    Dim rowItem As MultiThreadDownloadItem = row.Tag

                    If rowItem.ItemInfo.TaskID = item.ItemInfo.TaskID Then
                        Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                        cellName.tbIconSize = Me.mIconSize
                        cellName.tbIcon = imageIcon.Clone()
                        cellName.tbIconShadow = True
                        'cellName.tbShowIconInfo = True

                        Exit For
                    End If
                Next

            Catch ex As Exception
            End Try

            imageIcon.Dispose()
            imageIcon = Nothing
        End If
    End Sub

    Private Function GetIconImage(ByVal item As iTong.CoreFoundation.MultiThreadDownloadItem) As Image
        Dim imageIcon As Image = Utility.GetImageFormFile(item.ItemInfo.IconCachePath)
        If imageIcon Is Nothing Then
            Return imageIcon
        End If

        imageIcon = Utility.GetThumbnail(imageIcon, Me.mIconSize)
        Return imageIcon
    End Function

    Public Sub OnTaskHasDownloadSucceed(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        '下载的时候如果点击暂停可能会出现任务无法移除的情况，如果任务还在就重新删除
        If Me.InvokeRequired Then
            Me.BeginInvoke(New EventHandler(Of MultiThreadDownloadItem)(AddressOf OnTaskHasDownloadSucceed), sender, item)
        Else
            For Each row As DataGridViewRow In Me.GetDownloadRows()
                Dim rowItem As MultiThreadDownloadItem = row.Tag
                If rowItem.ItemInfo.TaskID = item.ItemInfo.TaskID Then
                    Me.dgvTask.SuspendLayout()
                    Me.dgvTask.Rows.Remove(row)
                    Me.dgvTask.ResumeLayout()
                    Exit For
                End If
            Next
        End If

    End Sub

#End Region

#Region "--- 私有事件 ---"

    Private Function FormatDuration(ByVal timeSeconds As Integer) As String
        Dim ret As String = String.Empty

        Dim hours As Integer = timeSeconds \ 3600
        Dim minutes As Integer = (timeSeconds - hours * 3600) \ 60
        Dim seconds As Integer = timeSeconds - hours * 3600 - minutes * 60

        If hours > 0 Then
            ret += hours & ":"
        Else
            ret += "00:"
        End If

        ret += minutes.ToString().PadLeft(2, "0"c) & ":" & seconds.ToString().PadLeft(2, "0"c)

        Return ret
    End Function

    Private Function GetAllTaskCount() As Integer
        Dim count As Integer = 0

        For Each row As DataGridViewRow In Me.dgvTask.Rows
            If row.Tag IsNot Nothing Then
                count += 1
            End If
        Next

        Return count

    End Function

    Private Sub UpdateParentTreeCount()

        If frmDownloadCenter.Instance IsNot Nothing Then
            Dim iCount As Integer = Me.GetAllTaskCount()
            frmDownloadCenter.Instance.UpdateProcessingCount(iCount)
            frmDownloadCenter.Instance.UpdateDownloadedCount()
        End If

    End Sub

#End Region

#Region "--- 私有方法  ---"
    '兼容展示BT包，分两行展示。
    '举例BT包文件的名字：暗夜之光_v1.0.3_@(通关版).ipa，则展示的效果  
    '暗夜之光_v1.0.3.ipa
    '通关版
    Private Function GetTaskName(ByVal info As MultiThreadDownloadItemInfo) As String
        Dim strResult As String = info.Name

        Try
            Dim strValue As String = String.Empty
            Dim strFileName As String = Path.GetFileNameWithoutExtension(info.Name)
            Dim strKey As String = "_@("
            Dim indexStart As Integer = strFileName.IndexOf(strKey)

            If indexStart > 0 Then
                Dim indexEnd As Integer = strFileName.IndexOf(")", indexStart + 1)
                If indexEnd > indexStart Then
                    strValue = strFileName.Substring(indexStart + strKey.Length, indexEnd - indexStart - strKey.Length)
                End If
            End If

            If strValue.Length > 0 Then
                strResult = strResult.Replace(String.Format("_@({0})", strValue), "")
                strResult = strResult & vbCrLf & strValue
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetTaskName")
        End Try

        Return strResult
    End Function

    Private Function GetGroupTemplate() As Object()
        Return New Object() {False, _
                                          "", _
                                          "", _
                                          "", _
                                          "", _
                                          ""}
    End Function

    Private Function GetDownloadRows() As List(Of DataGridViewRow)
        Dim list As New List(Of DataGridViewRow)
        For Each row As DataGridViewRow In Me.dgvTask.Rows '
            If (row.Tag IsNot Nothing) AndAlso (TypeOf row.Tag Is MultiThreadDownloadItem) Then
                list.Add(row)
            End If
        Next
        Return list

    End Function

    Private Function GetRowByInstallArgs(ByVal args As InstallArgs)
        Return Nothing
    End Function

    Private Sub ChangeMenuButtonState()
        Try
            Dim lstRows As List(Of DataGridViewRow) = Me.dgvTask.SelectedRows

            If lstRows Is Nothing Then
                Return
            End If

            Dim count As Integer = lstRows.Count

            If count = 0 Then
                Me.ChangeMenuButtonState(False, False, False)

            ElseIf count = 1 Then
                Dim row As DataGridViewRow = lstRows(0)
                Dim item As MultiThreadDownloadItem = CType(row.Tag, MultiThreadDownloadItem)
                Dim IsWaitingOrDownloading As Boolean = False

                If item IsNot Nothing Then
                    Dim isWaiting As Boolean = False
                    IsWaitingOrDownloading = Me.mDownManager.IsWaitingOrDownloading(item, isWaiting)
                End If

                Me.ChangeMenuButtonState(Not IsWaitingOrDownloading, IsWaitingOrDownloading, True)

            Else
                Dim IsContainWaitingOrDownloading As Boolean = False
                Dim isContainFailOrPause As Boolean = False

                For Each Item As DataGridViewRow In lstRows
                    Dim DownloadItem As MultiThreadDownloadItem = CType(Item.Tag, MultiThreadDownloadItem)

                    If DownloadItem IsNot Nothing Then
                        Dim isWaiting As Boolean = False

                        If Me.mDownManager.IsWaitingOrDownloading(DownloadItem, isWaiting) Then
                            IsContainWaitingOrDownloading = True
                        Else
                            isContainFailOrPause = True
                        End If
                    End If
                Next

                Me.ChangeMenuButtonState(isContainFailOrPause, IsContainWaitingOrDownloading, True)
            End If
        Catch
        End Try
    End Sub

    Private Delegate Sub ChangeMenuButtonStateHandler(ByVal blnStart As Boolean, ByVal blnPause As Boolean, ByVal blnDelete As Boolean)
    Private Sub ChangeMenuButtonState(ByVal blnStart As Boolean, ByVal blnPause As Boolean, ByVal blnDelete As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New ChangeMenuButtonStateHandler(AddressOf ChangeMenuButtonState), blnStart, blnPause, blnDelete)
        Else
            Me.btnStart.Enabled = blnStart
            Me.btnStop.Enabled = blnPause
            Me.btnDelete.Enabled = blnDelete
        End If
    End Sub

    Private Function CheckArgsProcessing(ByVal item As InstallArgs) As Boolean
        Dim bProcessing As Boolean = False
        If item.InstallState = InstallState.Add2DataGridView OrElse _
               item.InstallState = InstallState.Nomal OrElse _
               item.InstallState = InstallState.Waiting OrElse _
               item.InstallState = InstallState.Transfering OrElse _
               item.InstallState = InstallState.Installing Then
            bProcessing = True
        End If
        Return bProcessing
    End Function

    Private Sub GotoAppDetail(ByVal row As DataGridViewRow)
        '跳转到应用详细页面。
        If row Is Nothing OrElse row.Tag Is Nothing OrElse Not TypeOf row.Tag Is MultiThreadDownloadItem Then
            Return
        End If
        Dim item As MultiThreadDownloadItem = row.Tag
        If Not item.ItemInfo.Class = ResourceClass.Software Then
            Return
        End If
        tbSplashBox.ShowMessage(Me.Language.GetString("WeiBo.Message.Loading"), Me, 3)

        Dim strType As String = ""
        If item.ItemInfo.Type = ResourceType.IPA Then
            strType = "ios"
        ElseIf item.ItemInfo.Type = ResourceType.APK Then
            strType = "android"
        End If
        Dim strPara() As String = {"", "", strType, item.ItemInfo.ItemId}
        ProcForm.Instance().PopMsgGotoDetail(strPara)
    End Sub

#End Region

End Class