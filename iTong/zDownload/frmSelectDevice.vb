﻿Imports System.Xml
Imports System.Text
Imports System.IO
Imports System.Data
Imports iTong.CoreFoundation
Imports iTong.Components


Public Class frmSelectDevice
    Private dgvData As tbDataGridView = Nothing
    Public Device As iPhoneDevice = Nothing
    Public Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        InitialControls()
    End Sub

    Public Sub InitialControls()
        InitialDatagridView()
        InitialDeviceList()
    End Sub

    Private Sub InitialDatagridView()
        dgvData = New tbDataGridView
       
        With dgvData
            .RowTemplate.Height = 45
            .SuspendLayout()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Title", Me.Language.GetString("Welcome.Label.DeviceName"), 200)) '"设备名称"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 100.0!, True, True, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))
            .ResumeLayout()
        End With
        Me.pnlMain.Controls.Add(dgvData)
        dgvData.Dock = DockStyle.Fill

    End Sub

    Private Sub InitialDeviceList()
        For Each device As iPhoneDevice In MobileDeviceManager.Instance.ConnectedDevices
            Dim iRow As Integer = dgvData.Rows.Add(device.DeviceName, "")
            Dim cell As tbDataGridViewTextBoxCell = dgvData.Rows(iRow).Cells("Title")
            dgvData.Rows(iRow).Tag = device
            cell.tbIconSize = New Size(16, 16)
            cell.tbIcon = My.Resources.file_icon_iphone
        Next
    End Sub


    Public Shared Function GetDevice(ByVal frm As Form) As iPhoneDevice
        Dim frmSel As New frmSelectDevice()
        If frmSel.ShowDialog(frm) = Windows.Forms.DialogResult.OK Then
            Return frmSel.Device
        Else
            Return Nothing
        End If

    End Function

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        If Me.dgvData.SelectedRows.Count <> 1 Then
            tbMessageBox.Show(Me, "请选择一个设备", Me.Language.GetString("Common.Error"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        Me.Device = Me.dgvData.SelectedRows(0).Tag
        Me.DialogResult = Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub
End Class