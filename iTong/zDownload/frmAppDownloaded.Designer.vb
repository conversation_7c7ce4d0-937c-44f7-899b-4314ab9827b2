﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAppDownloaded
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Me.lblIPAName = New System.Windows.Forms.Label
        Me.btn_close = New tbButton
        Me.lblViewAll = New System.Windows.Forms.LinkLabel
        Me.lblInstall = New System.Windows.Forms.LinkLabel
        Me.lblNext = New System.Windows.Forms.LinkLabel
        Me.menuDevice = New System.Windows.Forms.ContextMenuStrip(Me.components)
        Me.lblSizeAndVersion = New System.Windows.Forms.Label
        Me.btnIconSmall = New tbButton
        Me.TimerToClose = New System.Windows.Forms.Timer(Me.components)
        Me.lblCloseTime = New System.Windows.Forms.Label
        Me.SuspendLayout()
        '
        'lblIPAName
        '
        Me.lblIPAName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblIPAName.BackColor = System.Drawing.Color.Transparent
        Me.lblIPAName.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblIPAName.ForeColor = System.Drawing.Color.Black
        Me.lblIPAName.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblIPAName.Location = New System.Drawing.Point(93, 29)
        Me.lblIPAName.Name = "lblIPAName"
        Me.lblIPAName.Size = New System.Drawing.Size(158, 17)
        Me.lblIPAName.TabIndex = 5
        Me.lblIPAName.Text = "Awesome Note "
        Me.lblIPAName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(234, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 12
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = My.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'lblViewAll
        '
        Me.lblViewAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblViewAll.BackColor = System.Drawing.Color.Transparent
        Me.lblViewAll.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblViewAll.ForeColor = System.Drawing.Color.Blue
        Me.lblViewAll.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblViewAll.LinkColor = System.Drawing.Color.Blue
        Me.lblViewAll.Location = New System.Drawing.Point(136, 5)
        Me.lblViewAll.Name = "lblViewAll"
        Me.lblViewAll.Size = New System.Drawing.Size(87, 17)
        Me.lblViewAll.TabIndex = 16
        Me.lblViewAll.TabStop = True
        Me.lblViewAll.Text = "查看全部"
        Me.lblViewAll.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lblViewAll.Visible = False
        '
        'lblInstall
        '
        Me.lblInstall.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblInstall.AutoSize = True
        Me.lblInstall.BackColor = System.Drawing.Color.Transparent
        Me.lblInstall.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblInstall.ForeColor = System.Drawing.Color.Black
        Me.lblInstall.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblInstall.LinkColor = System.Drawing.Color.Blue
        Me.lblInstall.Location = New System.Drawing.Point(134, 74)
        Me.lblInstall.Name = "lblInstall"
        Me.lblInstall.Size = New System.Drawing.Size(53, 12)
        Me.lblInstall.TabIndex = 17
        Me.lblInstall.TabStop = True
        Me.lblInstall.Text = "立即安装"
        '
        'lblNext
        '
        Me.lblNext.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblNext.BackColor = System.Drawing.Color.Transparent
        Me.lblNext.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblNext.ForeColor = System.Drawing.Color.White
        Me.lblNext.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblNext.LinkColor = System.Drawing.Color.Blue
        Me.lblNext.Location = New System.Drawing.Point(207, 73)
        Me.lblNext.Name = "lblNext"
        Me.lblNext.Size = New System.Drawing.Size(48, 19)
        Me.lblNext.TabIndex = 18
        Me.lblNext.TabStop = True
        Me.lblNext.Text = "下一个"
        '
        'menuDevice
        '
        Me.menuDevice.Name = "menuDevice"
        Me.menuDevice.Size = New System.Drawing.Size(61, 4)
        '
        'lblSizeAndVersion
        '
        Me.lblSizeAndVersion.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblSizeAndVersion.BackColor = System.Drawing.Color.Transparent
        Me.lblSizeAndVersion.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblSizeAndVersion.ForeColor = System.Drawing.Color.Black
        Me.lblSizeAndVersion.Location = New System.Drawing.Point(88, 49)
        Me.lblSizeAndVersion.Name = "lblSizeAndVersion"
        Me.lblSizeAndVersion.Size = New System.Drawing.Size(158, 17)
        Me.lblSizeAndVersion.TabIndex = 20
        Me.lblSizeAndVersion.Text = " 1.0.1 |3.1 MB"
        Me.lblSizeAndVersion.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnIconSmall
        '
        Me.btnIconSmall.BackColor = System.Drawing.Color.Transparent
        Me.btnIconSmall.BindingForm = Nothing
        Me.btnIconSmall.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnIconSmall.Location = New System.Drawing.Point(42, 26)
        Me.btnIconSmall.Name = "btnIconSmall"
        Me.btnIconSmall.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnIconSmall.Selectable = True
        Me.btnIconSmall.Size = New System.Drawing.Size(42, 42)
        Me.btnIconSmall.TabIndex = 21
        Me.btnIconSmall.tbAdriftIconWhenHover = False
        Me.btnIconSmall.tbAutoSize = False
        Me.btnIconSmall.tbAutoSizeEx = False
        Me.btnIconSmall.tbBackgroundImage = Nothing
        Me.btnIconSmall.tbBackgroundImageState = ImageState.OneState
        Me.btnIconSmall.tbBadgeNumber = 10
        Me.btnIconSmall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnIconSmall.tbEndEllipsis = False
        Me.btnIconSmall.tbIconHoldPlace = True
        Me.btnIconSmall.tbIconImage = Nothing
        Me.btnIconSmall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnIconSmall.tbIconImageState = ImageState.OneState
        Me.btnIconSmall.tbIconMore = False
        Me.btnIconSmall.tbIconMouseDown = Nothing
        Me.btnIconSmall.tbIconMouseHover = Nothing
        Me.btnIconSmall.tbIconMouseLeave = Nothing
        Me.btnIconSmall.tbIconPlaceText = 2
        Me.btnIconSmall.tbIconReadOnly = Nothing
        Me.btnIconSmall.tbImageMouseDown = Nothing
        Me.btnIconSmall.tbImageMouseHover = Nothing
        Me.btnIconSmall.tbImageMouseLeave = Nothing
        Me.btnIconSmall.tbReadOnly = False
        Me.btnIconSmall.tbReadOnlyText = False
        Me.btnIconSmall.tbShadow = False
        Me.btnIconSmall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnIconSmall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnIconSmall.tbShowNew = False
        Me.btnIconSmall.tbShowToolTipOnButton = False
        Me.btnIconSmall.tbSplit = "3,3,3,3"
        Me.btnIconSmall.tbText = ""
        Me.btnIconSmall.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnIconSmall.tbTextColor = System.Drawing.Color.White
        Me.btnIconSmall.tbTextColorDisable = System.Drawing.Color.White
        Me.btnIconSmall.tbTextColorDown = System.Drawing.Color.White
        Me.btnIconSmall.tbTextColorHover = System.Drawing.Color.White
        Me.btnIconSmall.tbTextMouseDownPlace = 0
        Me.btnIconSmall.tbToolTip = ""
        Me.btnIconSmall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnIconSmall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnIconSmall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnIconSmall.VisibleEx = True
        '
        'TimerToClose
        '
        Me.TimerToClose.Interval = 1000
        '
        'lblCloseTime
        '
        Me.lblCloseTime.BackColor = System.Drawing.Color.Transparent
        Me.lblCloseTime.ForeColor = System.Drawing.Color.Blue
        Me.lblCloseTime.Location = New System.Drawing.Point(2, 74)
        Me.lblCloseTime.Name = "lblCloseTime"
        Me.lblCloseTime.Size = New System.Drawing.Size(195, 17)
        Me.lblCloseTime.TabIndex = 23
        Me.lblCloseTime.Text = "Close after {0} seconds"
        Me.lblCloseTime.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'frmAppDownloaded
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(258, 94)
        Me.Controls.Add(Me.btnIconSmall)
        Me.Controls.Add(Me.lblSizeAndVersion)
        Me.Controls.Add(Me.lblNext)
        Me.Controls.Add(Me.lblInstall)
        Me.Controls.Add(Me.lblViewAll)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.lblIPAName)
        Me.Controls.Add(Me.lblCloseTime)
        Me.MinimumSize = New System.Drawing.Size(11, 40)
        Me.Name = "frmAppDownloaded"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.Manual
        Me.tbGuiBackground = My.Resources.pnl_bg_about
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleForeColor = System.Drawing.Color.Black
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents lblIPAName As System.Windows.Forms.Label
    Friend WithEvents btn_close As tbButton
    Friend WithEvents lblViewAll As System.Windows.Forms.LinkLabel
    Friend WithEvents lblInstall As System.Windows.Forms.LinkLabel
    Friend WithEvents lblNext As System.Windows.Forms.LinkLabel
    Friend WithEvents menuDevice As System.Windows.Forms.ContextMenuStrip
    Friend WithEvents lblSizeAndVersion As System.Windows.Forms.Label
    Friend WithEvents btnIconSmall As tbButton
    Friend WithEvents TimerToClose As System.Windows.Forms.Timer
    Friend WithEvents lblCloseTime As System.Windows.Forms.Label
End Class
