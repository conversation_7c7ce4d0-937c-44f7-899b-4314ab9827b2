﻿Imports System.Xml
Imports System.Text
Imports System.IO
Imports System.Data
Imports System.Drawing.Imaging
Imports System.Drawing.Drawing2D
Imports iTong.Device
Imports iTong.CoreFoundation
Imports iTong.Components

#If IS_ITONG Then

Imports SharePodLib
Imports SharePodLib.Databinding
Imports SharePodLib.DataTypes
Imports SharePodLib.Exceptions
Imports SharePodLib.Parsers
Imports SharePodLib.Parsers.Artwork
Imports SharePodLib.Parsers.iTunesDB
Imports SharePodLib.Parsers.iTunesCDB
Imports iTong.Taglib

#End If

Public Class frmDownloaded

    Private mDownloadDB As MultiThreadDownloadDB = Nothing
    Protected WithEvents dgvDetail As tbDataGridViewEx = Nothing

    Private mListSoftware As New List(Of MultiThreadDownloadItemInfo)
    Private mListMusic As New List(Of MultiThreadDownloadItemInfo)
    Private mListRingtone As New List(Of MultiThreadDownloadItemInfo)
    Private mListPeper As New List(Of MultiThreadDownloadItemInfo)
    Private mListFirmware As New List(Of MultiThreadDownloadItemInfo)

    Private mCurrentView As DownloadView = DownloadView.Software
    Private mDictRowToInstall As New Dictionary(Of DataGridViewRow, IInstallHelper)
    Private mDictRowToTask As New Dictionary(Of DataGridViewRow, MultiThreadDownloadItemInfo)
    Private mDictViewToList As New Dictionary(Of DownloadView, List(Of MultiThreadDownloadItemInfo))
    Private mDictPathToRow As New Dictionary(Of String, DataGridViewRow)
    Private mDictViewToSearchKeyword As New Dictionary(Of DownloadView, String)

    Private mDictItemToPackage As New Dictionary(Of MultiThreadDownloadItemInfo, PackageInfo)
    Private mDictPathToPackage As New Dictionary(Of String, PackageInfo)
    Private mDictPathToMusic As New Dictionary(Of String, MusicInfo)
    Private mPackageReader As New ApplePackageReader

    Private mListImportingFile As New List(Of String)   ' 正在导入的数据
    Private mListInstallingFile As New List(Of String)  ' 正在安装的数据
    Private mIsImportingMusic As Boolean = False
    Private mbIsImportingRington As Boolean = False
    Private mIsInstalling As Boolean = False
    Private mIsInitCalled As Boolean = False
    Private mIsRefreshing As Boolean = False
    Private mintDeviceCount As Integer = 0
    Private mAndroidPackageReader As AndroidPackageReader

    Private mCoreUpdate As CoreUpdateHelper
    Private mDownloadMgr As MultiThreadDownload

#Region "--- 初始化 ---"

    Public Sub New(ByVal app As IApplication)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong

        Me.Name = ""
        Me.FunctionMappingKey = FunctionKey.Download
        Me.mApplication = app
        Me.UpdateDeviceCount()
        Me.mAndroidPackageReader = AndroidPackageReader.Instance()

        Me.bgwLoadData.WorkerSupportsCancellation = True

        Me.mCoreUpdate = CoreUpdateHelper.Instance()
        Me.mDownloadMgr = MultiThreadDownload.Instance()

        RemoveHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload
        AddHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        InitDatagridView()
        Me.ShowProgressPanel(False)


        AddHandler Me.mDownloadMgr.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler Me.mDownloadMgr.TaskAdd, AddressOf OnTaskAdd

        ' 无设备，禁止导入和安装
        If Not HasDevice() Then
            Me.btnImport.Enabled = False
            Me.btnInstall.Enabled = False
            Me.tsmiInstallItem.Enabled = False
            EnableRowOperation(False)
        End If

        If Not Common.VerIs30() Then
            AddHandler MobileDeviceManager.Instance().ConnectionChanged, AddressOf OnConnectionChange
        End If
        Me.btnIPhone.Checked = True
        Me.btnIPhone.Visible = Common.VerIs30()
        Me.btnAndroid.Visible = Common.VerIs30()

        SetReloadEnable(False)
        mDownloadDB = MultiThreadDownloadDB.GetInstance()

        ShowLoading(True)

        Me.BeginReloadAll()



        mIsInitCalled = True
        Me.SwitchView(Me.mCurrentView)

        ' 监听所有已连接设备的安装事件
        InitialDeviceListen()

        AddHandler ApplePackageReader.Instance().PackageAdd, AddressOf onPackageUpdate
        'AddHandler SoftManageDB.GetInstance.PackageUpdated, AddressOf onPackageUpdate

        MainForm.SetButtonStatus(Me.btnImport)
        MainForm.SetButtonStatus(Me.btnInstall)
        MainForm.SetButtonStatus(Me.btnDelete)
        MainForm.SetButtonStatus(Me.btnRefresh)
    End Sub

    Private Sub InitialDeviceListen()
        If Common.VerIs30() Then
            InitialDeviceListenV3()
            Return
        End If
        For Each device As iPhoneDevice In MobileDeviceManager.Instance.ConnectedDevices
            Dim helper As iPhoneInstallHelper = iPhoneInstallHelper.GetInstance(device)
            RemoveHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
            AddHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent

        Next
    End Sub

    Private Sub InitialDeviceListenV3()

        Dim lstUsbDevs As List(Of UsbDevice) = GetAllDevices()
        For Each dev As UsbDevice In lstUsbDevs
            If TypeOf dev.MobileDevice Is iPhoneDevice Then
                Dim device As iPhoneDevice = dev.MobileDevice
                Dim helper As iPhoneInstallHelper = iPhoneInstallHelper.GetInstance(device)
                RemoveHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
                AddHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
            ElseIf TypeOf dev.MobileDevice Is AndroidDevice Then
                Dim device As AndroidDevice = dev.MobileDevice
                Dim helper As AndroidInstallHelper = AndroidInstallHelper.GetInstance(device)
                RemoveHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
                AddHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
            End If
        Next
    End Sub

    Private Sub InitDatagridView()
        dgvDetail = New tbDataGridViewEx()
        With dgvDetail

            Dim cellStyle As New DataGridViewCellStyle()
            ' cellStyle.Font = Common.CreateFont("Arial", 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black

            .RowTemplate.Height = 60
            .SuspendLayout()
            .ContextMenuStrip = Me.menuDownload

            .Dock = DockStyle.Fill
            .AllowDrop = True
            .tbShowNoData = False

            .tbMouseHoverResponse = True
            .BackColor = System.Drawing.Color.FromArgb(233, 237, 243)
            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "Title", Me.Language.GetString("Common.Label.Name"), 185, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.True)
            colName.DefaultCellStyle = cellStyle

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "CheckAll", "", 26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(colName) '"名称"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colType", Me.Language.GetString("Common.Label.ColumnType"), _
                              70, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False)) '类型
            '.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewMediaColumn), "FileExist", "", 20))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Duration", Me.Language.GetString("Music.Label.Duration"), 68, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) '"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Version", Me.Language.GetString("App.Column.Version"), 65, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) '"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Size", Me.Language.GetString("Common.Columns.Size"), 60, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) '"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Requirements", Me.Language.GetString("PkgInstaller.Label.MinOSVersion"), 80, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)) '"Require""
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Time", Me.Language.GetString("Download.Column.DownloadTime"), 60, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) '"下载时间"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "Status", "", 190, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) '"操作"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "url", "", 10, False, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) 'url

            Dim colOther As tbDataGridViewTextBoxColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 100.0!, True, True, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)
            colOther.SortMode = DataGridViewColumnSortMode.NotSortable

            .Columns.Add(colOther)
            .ResumeLayout()
        End With

        dgvDetail.Columns("Version").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvDetail.Columns("Duration").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvDetail.Columns("Size").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvDetail.Columns("Time").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
        'dgvDetail.Columns("Requirements").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight


        dgvDetail.Columns("Version").HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvDetail.Columns("Duration").HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvDetail.Columns("Size").HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvDetail.Columns("Time").HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight
        dgvDetail.Columns("Requirements").HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight

        dgvDetail.Columns("Title").MinimumWidth = 185

        dgvDetail.Columns("Duration").Resizable = DataGridViewTriState.False
        dgvDetail.Columns("Version").Resizable = DataGridViewTriState.False
        dgvDetail.Columns("Size").Resizable = DataGridViewTriState.False
        dgvDetail.Columns("Requirements").Resizable = DataGridViewTriState.False
        dgvDetail.Columns("Time").Resizable = DataGridViewTriState.False
        dgvDetail.Columns("Status").Resizable = DataGridViewTriState.False
        dgvDetail.Columns("colOther").Resizable = DataGridViewTriState.False

        Me.pnlBottom.Controls.Add(dgvDetail)
        dgvDetail.Dock = DockStyle.Fill
        AddHandler dgvDetail.CellButtonClick, AddressOf dgvDetail_CellButtonClick
        AddHandler dgvDetail.SelectionChanged, AddressOf dgvDetail_SelctionChange
        AddHandler dgvDetail.ShowToolTip, AddressOf dgvDetail_ShowToolTip
        AddHandler dgvDetail.SortCompare, AddressOf dgvDetail_SortCompare
    End Sub

    Protected Overrides Sub SetInterface()
        Me.btnInstall.Text = Me.Language.GetString("App.Button.Install") ' 安装
        Me.btnImport.Text = Me.Language.GetString("Music.Button.ImportTo") '导入设备
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")
        Me.lblLoading.Text = Me.Language.GetString("File.Label.Loading")    ' 正在加载
        Me.lblCurrentFile.Text = Me.Language.GetString("Media.Message.KeepConnection") ' 请勿断开
        Me.txtSearch.SearchTipText = Me.Language.GetString("Common.Search") ' 过滤文件名
        Me.tsmiOpenExplorer.Text = Me.Language.GetString("App.Button.ShowInFile")
        Me.tsmiInstallItem.Text = Me.Language.GetString("App.Button.Install")
        Me.tsmiDeleteItem.Text = Me.Language.GetString("Common.Delete")
        Me.lblFileEmpty.Text = Me.Language.GetString("Media.Label.Empty")   '"这里没有内容"
        Me.btnGotoDownload.Text = Me.Language.GetString("Download.Button.GotoDownload")   '"去逛逛"
        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh")                         '"刷新"
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnReload_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        SetReloadEnable(False)
        Me.mIsRefreshing = True
        For Each column As DataGridViewColumn In dgvDetail.Columns
            column.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.NotSortable
            column.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Next
        Select Case Me.mCurrentView
            Case DownloadView.Music
                Me.BeginReloadMusic()
            Case DownloadView.Ringtone
                Me.BeginReloadRingtone()
            Case DownloadView.Software
                Me.BeginReloadSoftware()
            Case DownloadView.Firmware
                Me.BeginReloadFirmware()
        End Select
    End Sub

    Private Sub btnIPhone_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnIPhone.CheckedChanged
        Me.SwitchView(Me.mCurrentView)
    End Sub

    Private Sub btnAndroid_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAndroid.CheckedChanged
        Me.SwitchView(Me.mCurrentView)
    End Sub

    Private Sub btnInstall_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnInstall.Click, tsmiInstallItem.Click
        Dim listRows As New List(Of DataGridViewRow)
        For Each row As DataGridViewRow In Me.dgvDetail.SelectedRows
            ' 过滤掉被删除的文件
            Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
            If System.IO.File.Exists(item.DownloadPath) Then
                listRows.Add(row)
            Else
                Dim cellStatus As tbDataGridViewProgressCellEx = CType(row.Cells("Status"), tbDataGridViewProgressCellEx)
                'CType(row.Cells("FileExist"), tbDataGridViewMediaCell).tbCellStatus = CellButtonStyle.FileNoExist
                cellStatus.tbProgressStyle = ProgressStyle.Text
                cellStatus.tbText = Me.Language.GetString("Media.Message.FileNoExist") '"文件不存在"
            End If
        Next


        If Not HasDevice() Then
            Dim strPleaseConnectDevice As String = Me.Language.GetString("Welcome.Label.AccessDevice")
            Dim strErr As String = Me.Language.GetString("Common.Error")
            If Me.mCurrentView = DownloadView.Software Then
                tbMessageBox.Show(Me, strPleaseConnectDevice, strErr, MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
            Return
        ElseIf GetDeviceCount() = 1 Then
            ' 只有一个设备，直接安装
            InstallToDevice(GetFirstDevice(), listRows)
        Else
            ' 多个设备则弹出设备选择菜单
            Me.CreateDeviceMenu(listRows, Me.btnInstall, New Point(0, Me.btnInstall.Bottom))

        End If
    End Sub

    Private Sub btn_Visible_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.Hide()
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click, tsmiDeleteItem.Click
        Try

            If Me.dgvDetail.SelectedRows.Count = 0 Then
                'tbSplashBox.ShowMessage(Me, "未选中任何项", frmDownloadCenter.Instance)
                Return
            End If
            Dim strDelText As String = ""
            Dim bDelFile As Boolean = True
            Dim iCount As Integer = 0
            Dim strFirstFileName As String = ""

            Try
                For Each row As DataGridViewRow In Me.dgvDetail.SelectedRows
                    Dim item As MultiThreadDownloadItemInfo = mDictRowToTask(row)

                    If item.Class = ResourceClass.Software AndAlso Me.mDictItemToPackage.ContainsKey(item) Then
                        Dim app As PackageInfo = Me.mDictItemToPackage(item)
                        strFirstFileName = app.Name
                    Else
                        strFirstFileName = item.Name
                    End If

                    iCount += 1
                Next
            Catch exc As Exception
                Debug.Write(exc)
            End Try

            'strDelText.TrimEnd(vbCrLf)
            Dim strFile As String = Me.Language.GetString("Common.File")
            If Me.dgvDetail.SelectedRows.Count > 1 Then
                ' 您确定要删除 {0} 等{1}个{2}吗？
                Dim strDelMultiFormat As String = Me.Language.GetString("Common.Message.DeleteMessageMultiItem")

                strDelText = String.Format(strDelMultiFormat, strFirstFileName, iCount, strFile)
                'strDelText += strFirstFileName & " 等" & iCount & "个文件吗？"
            Else
                '您确定要删除 {0} 吗？
                Dim strDelSingle As String = Me.Language.GetString("Common.Message.DeleteMessageOneItem")

                strDelText = String.Format(strDelSingle, strFirstFileName)
                'strDelText += strFirstFileName & " 吗？"
            End If


            If tbMessageBox.Show(Me, strDelText, Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, Me.Language.GetString("App.CheckBox.Delete"), bDelFile) <> Windows.Forms.DialogResult.OK Then
                Return
            End If

            Dim listDelete As New List(Of MultiThreadDownloadItemInfo)
            Dim listRow As New List(Of DataGridViewRow)
            Dim listLocal As List(Of MultiThreadDownloadItemInfo) = Me.mDictViewToList(Me.mCurrentView)
            For Each row As DataGridViewRow In Me.dgvDetail.SelectedRows

                If Me.mDictRowToTask.ContainsKey(row) Then

                    Dim item As MultiThreadDownloadItemInfo = mDictRowToTask(row)
                    If Not Me.mListInstallingFile.Contains(item.DownloadPath) AndAlso _
                       Not Me.mListImportingFile.Contains(item.DownloadPath) Then
                        RemoveItemCache(item.DownloadPath.ToLower())
                        listDelete.Add(item)
                        listLocal.Remove(item)
                        listRow.Add(row)

                        If bDelFile Then
                            Try
                                System.IO.File.Delete(item.DownloadPath)
                            Catch ex As Exception

                            End Try
                        End If

                    End If

                End If

            Next

            mDownloadDB.Delete_Task(listDelete)

            For Each row As DataGridViewRow In listRow
                Me.dgvDetail.Rows.Remove(row)
            Next


            Me.UpdateParentCount()
            Me.CheckCanEnableImportDelete()
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.btnDelete_Click")
        End Try


    End Sub

    Private Sub Delete()

    End Sub

    Private Sub btnImport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.Click, tsmiInstallItem.Click
        Try

            If Me.mCurrentView = DownloadView.Music Or Me.mCurrentView = DownloadView.Ringtone Then
                If Not HasDevice() Then
                    ' 错误！  请连接设备
                    tbMessageBox.Show(Me, Me.Language.GetString("Welcome.Label.AccessDevice"), Me.Language.GetString("Common.Error"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

                Dim listRows As New List(Of DataGridViewRow)
                For Each row As DataGridViewRow In Me.dgvDetail.SelectedRows
                    Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
                    If System.IO.File.Exists(item.DownloadPath) Then
                        listRows.Add(row)
                    Else
                        Dim cellStatus As tbDataGridViewProgressCellEx = CType(row.Cells("Status"), tbDataGridViewProgressCellEx)
                        'CType(row.Cells("FileExist"), tbDataGridViewMediaCell).tbCellStatus = CellButtonStyle.FileNoExist
                        cellStatus.tbProgressStyle = ProgressStyle.Text
                        cellStatus.tbText = Me.Language.GetString("Media.Message.FileNoExist") '"文件不存在"
                    End If

                Next

                If GetDeviceCount() = 1 Then
                    Dim firstDev As UsbDevice = GetFirstDevice()
                    If firstDev.DeviceType = DeviceType.Android AndAlso Me.mCurrentView = DownloadView.Ringtone Then
                        Me.CreateDeviceRingtoneMenu(listRows, Me.btnImport, New Point(0, Me.btnImport.Height))
                    Else
                        ImportItemToDevice(GetFirstDevice(), listRows)
                    End If
                Else

                    Me.CreateDeviceMenu(listRows, Me.btnImport, New Point(0, Me.btnImport.Height))
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.btnImport_Click")
        End Try
    End Sub


    Private Sub txtSearch_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.TextChangedByTimer
        Me.mDictViewToSearchKeyword(Me.mCurrentView) = txtSearch.Text
        DoSearch()
    End Sub

    Private Sub DoSearch()

        Dim strText As String = Me.txtSearch.Text.ToLower()
        If String.IsNullOrEmpty(strText) Then
            ShowAllRows()
            Return
        End If

        For Each row As DataGridViewRow In Me.dgvDetail.Rows
            'If Me.mCurrentView = DownloadView.Software Then
            If row.Tag Is Nothing Then
                row.Visible = False
                Continue For
            End If

            Dim args As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
            If args.Class <> Me.GetCurrentDownloadClass Then
                row.Visible = False
                Continue For
            End If

            Dim strPinyin As String = PinYinClass.MakePinYin(args.Name, PinYinOptions.FirstCharacterOnly)
            Dim strPinyinFull As String = PinYinClass.MakePinYin(args.Name, PinYinOptions.Default)
            Dim appInfo As PackageInfo = Nothing
            Dim strAppName As String = String.Empty
            Dim strAppPinyin As String = String.Empty
            Dim strAppPinyinFull As String = String.Empty

            If mDictItemToPackage.ContainsKey(args) Then
                appInfo = Me.mDictItemToPackage(args)
                strAppName = appInfo.Name
                strAppPinyin = PinYinClass.MakePinYin(strAppName, PinYinOptions.FirstCharacterOnly)
                strAppPinyinFull = PinYinClass.MakePinYin(strAppName, PinYinOptions.Default)
            End If
            Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
            If IsContain(args.Name.ToLower(), strText) OrElse IsContain(strPinyin.ToLower(), strText) OrElse IsContain(strPinyinFull.ToLower(), strText) _
               OrElse IsContain(strAppName.ToLower(), strText) OrElse IsContain(strAppPinyin.ToLower(), strText) OrElse IsContain(strAppPinyinFull.ToLower(), strText) Then
                row.Visible = True
                '如果当前是app就要判断是不是apk
                If Me.btnAndroid.Checked Then
                    row.Visible = IsAPK(item)
                Else
                    row.Visible = IsIPA(item)
                End If

                '如果不是app的就不用了
                If args.Class <> ResourceClass.Software Then
                    row.Visible = True
                End If
            Else
                row.Visible = False
            End If


        Next
    End Sub

    '搜索词的所有字符是否包含在内容中 by luyanjun
    Private Shared Function IsContain(ByVal str As String, ByVal strSearchKey As String) As Boolean
        'strSearchKey = strSearchKey.Replace(" ", String.Empty)
        'For Each chrWord As Char In strSearchKey
        '    If Not str.ToLower().Contains(Char.ToLower(chrWord)) AndAlso Not str.ToUpper().Contains(Char.ToUpper(chrWord)) Then
        '        Return False
        '    End If
        'Next
        'Return True

        Return str.ToLower().Contains(strSearchKey.ToLower())
    End Function

    Private Sub txtSearch_ClearClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.ClearClick
        ShowAllRows()
    End Sub

    Private Sub dgvDetail_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvDetail.Rows(e.Cell.RowIndex)
            Dim appInfo As MultiThreadDownloadItemInfo = row.Tag

            Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("Status")

            Select Case e.CellButton
                Case CellButtonStyle.Backup
                    If cellProgress.tbButtonImgFirst IsNot Nothing Then
                        Dim listFiles As New List(Of DataGridViewRow)
                        If System.IO.File.Exists(appInfo.DownloadPath) Then
                            listFiles.Add(row)
                        Else
                            Dim cellStatus As tbDataGridViewProgressCellEx = CType(row.Cells("Status"), tbDataGridViewProgressCellEx)
                            cellStatus.tbProgressStyle = ProgressStyle.Text
                            cellStatus.tbText = Me.Language.GetString("Media.Message.FileNoExist") '"文件不存在"
                        End If

                        If appInfo.Class = ResourceClass.Music Or appInfo.Class = ResourceClass.Ringtone Then
                            If Not HasDevice() Then
                                ' 请连接设备
                                tbMessageBox.Show(Me, Me.Language.GetString("Welcome.Label.AccessDevice"))
                            ElseIf GetDeviceCount() = 1 Then
                                Dim firstDev As UsbDevice = GetFirstDevice()

                                If firstDev.DeviceType = DeviceType.Android AndAlso Me.mCurrentView = DownloadView.Ringtone Then
                                    Dim rect As Rectangle = Me.dgvDetail.GetCellDisplayRectangle(e.Cell.ColumnIndex, e.Cell.RowIndex, False)
                                    Dim point As Point = New Point(rect.X + 40, rect.Bottom - 20)
                                    Me.CreateDeviceRingtoneMenu(listFiles, Me.dgvDetail, point)
                                Else
                                    ImportItemToDevice(GetFirstDevice(), listFiles)
                                End If

                            Else
                                Dim rect As Rectangle = Me.dgvDetail.GetCellDisplayRectangle(e.Cell.ColumnIndex, e.Cell.RowIndex, False)
                                Dim point As Point = New Point(rect.X + 40, rect.Bottom - 20)
                                Me.CreateDeviceMenu(listFiles, Me.dgvDetail, point)
                            End If


                        ElseIf appInfo.Class = ResourceClass.Software Then
                            If Not HasDevice() Then
                                ' 请连接设备
                                tbMessageBox.Show(Me, Me.Language.GetString("Welcome.Label.AccessDevice"))
                            ElseIf GetDeviceCount() = 1 Then
                                Me.InstallToDevice(GetFirstDevice(), listFiles)
                            Else
                                Dim rect As Rectangle = Me.dgvDetail.GetCellDisplayRectangle(e.Cell.ColumnIndex, e.Cell.RowIndex, False)
                                Dim point As Point = New Point(rect.X + 40, rect.Bottom - 18)
                                Me.CreateDeviceMenu(listFiles, Me.dgvDetail, point)
                            End If

                        End If
                        'End If
                    End If


                Case CellButtonStyle.Document
                    If System.IO.File.Exists(appInfo.DownloadPath) Then
                        Common.OpenExplorer(appInfo.DownloadPath)
                    Else
                        tbMessageBox.Show(Me, Me.Language.GetString("Media.Message.FileNoExist"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                    End If
                Case CellButtonStyle.Info
                    If appInfo.Class = ResourceClass.Software Then
                        Dim downloadItem As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
                        Dim item As PackageInfo = Nothing
                        If Me.mDictItemToPackage.ContainsKey(downloadItem) Then
                            item = mDictItemToPackage(downloadItem)
                            If item.ItemId.Length = 0 Then
                                Me.GetPackageItemId(item)
                            End If
                            iPhoneDeviceHelper.ShowSoftDetail(item, Me.mApplication.Language, False, Me)
                        End If

                    End If
                Case CellButtonStyle.Failed
                    '卸载并重装
                    If cellProgress.tbText = Me.Language.GetString("App.Button.FocedInstall") Then
                        Dim listFiles As New List(Of DataGridViewRow)
                        listFiles.Add(row)
                        Me.InstallToDevice(GetFirstDevice(), listFiles, True)
                    Else
                        Common.OpenExplorer(WebUrl.PageInstallFailed)
                    End If

                Case CellButtonStyle.Cancel
                    CancelInstallation(row, appInfo)
                Case CellButtonStyle.OneRetryButton
                    Dim pInfo As PackageInfo = PackageInfoReader.GetPackageInfo(appInfo.DownloadPath)
                    CoreUpdateHelper.Instance().Start2SoftwareAgainPulls(pInfo, mApplication, Me.mDevice)
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloadded.dgvDetail_CellButtonClick")
        End Try


    End Sub

    Private Sub CancelInstallation(ByVal row As DataGridViewRow, ByVal appInfo As MultiThreadDownloadItemInfo)

        Dim temphelper As IInstallHelper = Nothing
        Dim tempPackage As PackageInfo = Nothing
        If Me.mDictRowToInstall.ContainsKey(row) AndAlso Me.mDictItemToPackage.ContainsKey(appInfo) Then
            temphelper = Me.mDictRowToInstall(row)
            tempPackage = Me.mDictItemToPackage(appInfo)
            If IsAPK(appInfo) Then
                CType(temphelper, AndroidInstallHelper).CancelInstallApp(tempPackage)
            Else
                CType(temphelper, iPhoneInstallHelper).CancelInstallApp(tempPackage)
            End If
        End If
    End Sub

    Private Sub dgvDetail_SelctionChange(ByVal sender As Object, ByVal e As System.EventArgs)
        CheckCanEnableImportDelete()
    End Sub

    Private intRowIndex As Integer = -1
    Private Sub dgvDetail_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Dim row As DataGridViewRow = Me.dgvDetail.Rows(e.Cell.RowIndex)
        Dim pos As Point = Windows.Forms.Cursor.Position
        Dim secPos As Point = Me.PointToClient(pos)
        Dim newPos As New Point(secPos.X + 10, secPos.Y + 10)
        Try
            Select Case e.CellButton
                Case CellButtonStyle.Document
                    Me.tpDgvDetail.Show(Me.Language.GetString("App.Cell.ShowInFile"), Me, newPos) '打开文件目录
                Case CellButtonStyle.None
                    Me.tpDgvDetail.Hide(Me)
                    Me.tipDgvCell.Hide(Me)

                Case CellButtonStyle.ShowAppleID
                    Dim bakObject As MultiThreadDownloadItemInfo = row.Tag
                    Dim bakInfo As PackageInfo = Nothing
                    If Me.mDictItemToPackage.ContainsKey(bakObject) Then
                        bakInfo = Me.mDictItemToPackage(bakObject)
                    End If
                    If bakInfo IsNot Nothing Then
                        Dim strTip As New StringBuilder
                        strTip.AppendLine(Me.Language.GetString("Common.Label.Name") & "：" & bakInfo.Name)        '"名称："
                        If bakObject.AppleId.Length > 0 Then
                            strTip.AppendLine("AppleID：" & bakObject.AppleId)        '"AppleID："
                        End If
                        Me.tipDgvCell.Show(strTip.ToString(), Me, newPos)
                    End If
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "dgvDetail_ShowToolTip")
        End Try

    End Sub

    Private Sub dgvDetail_SortCompare(ByVal sender As Object, ByVal args As DataGridViewSortCompareEventArgs)
        If args.Column.Name = "Time" Then

            Dim row1 As DataGridViewRow = Me.dgvDetail.Rows(args.RowIndex1)
            Dim row2 As DataGridViewRow = Me.dgvDetail.Rows(args.RowIndex2)
            Dim item1 As MultiThreadDownloadItemInfo = row1.Tag
            Dim item2 As MultiThreadDownloadItemInfo = row2.Tag
            args.SortResult = item1.CompletedTime.CompareTo(item2.CompletedTime) 'item1.CompletedTime.ToBinary.CompareTo(item2.CompletedTime.ToBinary)
            args.Handled = True
        ElseIf args.Column.Name = "Size" Then
            args.Handled = True
        Else
            args.Handled = False
        End If
    End Sub

    Private Sub frmDownloaded_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing

    End Sub

    Private Sub btnInstall_EnableChange(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnInstall.EnabledChanged
        Me.tsmiInstallItem.Enabled = btnInstall.Enabled
    End Sub

    Private Sub btnGotoDownload_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnGotoDownload.Click
        Try
            Dim usbDevices As List(Of UsbDevice) = Me.GetDevices()

            Dim iosDevices As New List(Of UsbDevice)
            For Each ditem As UsbDevice In usbDevices
                If ditem.DeviceType = DeviceType.iOS Then
                    iosDevices.Add(ditem)
                End If
            Next

            Dim frmMain As MainForm = Me.mApplication
            If Me.mDevice IsNot Nothing Then
                frmMain.SelectDevice(Me.mDevice.DeviceID)
                frmMain.GotoSite(ActionFuncType.Jailbreak, "DowmloadFirmware", Me.mDevice.DeviceID)
            ElseIf iosDevices.Count > 0 Then
                If iosDevices.Count > 1 Then
                    CreateDeviceMenu(Me.btnGotoDownload)
                ElseIf iosDevices.Count = 1 Then
                    frmMain.SelectDevice(iosDevices(0).DeviceID)
                    frmMain.GotoSite(ActionFuncType.Jailbreak, "DowmloadFirmware", iosDevices(0).DeviceID)
                End If
            ElseIf usbDevices.Count <= 0 Then
                frmMain.GotoSite(ActionFuncType.Jailbreak, "DowmloadFirmware", "")
            Else
                tbMessageBox.Show(Me, Me.Language.GetString("Common.Message.NotSupportedAndroid"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Asterisk)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnGotoDownload_Click")
        End Try
    End Sub

#End Region

#Region "--- 安装功能及安装事件回调 ---"
    Private Sub InstallToDevice(ByVal device As UsbDevice, ByVal listRows As List(Of DataGridViewRow), Optional ByVal blnInstallForced As Boolean = False)
        Try
            Dim installHelper As IInstallHelper = Nothing
            If TypeOf device.MobileDevice Is iPhoneDevice Then
                installHelper = iPhoneInstallHelper.GetInstance(device.MobileDevice)
            ElseIf TypeOf device.MobileDevice Is AndroidDevice Then
                installHelper = AndroidInstallHelper.GetInstance(device.MobileDevice)
            End If
            If installHelper Is Nothing Then
                Return
            End If

            If Me.dgvDetail.SelectedRows.Count <= 0 Then
                Return
            End If

            RemoveHandler installHelper.InstallEventHandler, AddressOf OnInstallEvent
            AddHandler installHelper.InstallEventHandler, AddressOf OnInstallEvent

            For Each row As DataGridViewRow In listRows
                Me.mDictRowToInstall(row) = installHelper
                If row.Tag Is Nothing Then
                    Continue For
                End If
                Dim downloadItem As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
                Dim item As PackageInfo = Nothing
                If Me.mDictItemToPackage.ContainsKey(downloadItem) Then
                    item = mDictItemToPackage(downloadItem)
                End If
                If TypeOf device.MobileDevice Is iPhoneDevice AndAlso Not Me.CheckPackageCrackedFit(device.MobileDevice, item) Then
                    Dim strMsg As String = Me.Language.GetString("App.Message.CrackedNotFit")
                    ShowErrorMsg(row, strMsg)
                    Continue For
                End If

                If TypeOf device.MobileDevice Is iPhoneDevice AndAlso IsAPK(downloadItem) _
                    OrElse TypeOf device.MobileDevice Is AndroidDevice AndAlso IsIPA(downloadItem) Then
                    Dim strMsg As String = Me.Language.GetString("App.Cell.AppUnsuited")
                    ShowErrorMsg(row, strMsg)
                    Continue For
                End If

                If installHelper.IsInstalling(downloadItem.DownloadPath) Then
                    Continue For
                End If

                mListInstallingFile.Add(downloadItem.DownloadPath)
                Me.mIsInstalling = True

                If item IsNot Nothing Then
                    If IsIPA(downloadItem) Then
                        installHelper.InstallApplication(item, blnInstallForced)
                    Else
                        installHelper.InstallApplication(item, False)
                    End If

                End If
            Next

            Me.CheckCanEnableRefresh()
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.InstallToDevice")
        End Try

    End Sub

    Private Sub ShowErrorMsg(ByVal row As DataGridViewRow, ByVal strMsg As String)

        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("Status")
        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
        cellProgress.tbText = strMsg
        cellProgress.tbButtonImgFirst = Nothing
        cellProgress.tbProgressStyle = ProgressStyle.Text
        CheckCanEnableRefresh()
    End Sub

    Private Function CheckPackageCrackedFit(ByVal device As iPhoneDevice, ByVal pInfo As PackageInfo) As Boolean
        Dim isFit As Boolean = True

        If device.Jailbreaked Then
            Return isFit
        End If

        '设备未越狱，不能安装破解软件
        If pInfo.CrackedInfo = CrakedInfo.Craked Then
            isFit = False
        End If

        Return isFit
    End Function

    Private Delegate Sub OnInstallEventHandler(ByVal sender As Object, ByVal args As InstallArgs)
    Private Sub OnInstallEvent(ByVal sender As Object, ByVal args As InstallArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New OnInstallEventHandler(AddressOf OnInstallEvent), New Object() {sender, args})
        Else
            Try
                If Me.GetDeviceCount() = 0 Then
                    Exit Sub
                End If

                Dim packPath As String = ""
                If args IsNot Nothing AndAlso args.PackageInfo IsNot Nothing Then
                    packPath = args.PackageInfo.PackagePath
                End If

                '''args.PackageInfo IsNot Nothing AndAlso Me.mDictPathToRow.ContainsKey(args.PackageInfo.PackagePath.ToLower)
                If Not String.IsNullOrEmpty(packPath) AndAlso mDictPathToRow.ContainsKey(packPath.ToLower()) Then
                    Dim row As DataGridViewRow = Me.mDictPathToRow(packPath.ToLower())   '''args.PackageInfo.PackagePath.ToLower

                    If Not Me.mDictRowToInstall.ContainsKey(row) Then
                        mDictRowToInstall(row) = sender
                    End If
                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("Status")
                    cellProgress.tbPadding = New Padding(40, 0, 0, 0)

                    If args.InstallState = InstallState.Transfering Then
                        cellProgress.tbShowCancelButton = (args.TransferProgress < 100)
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbText = Me.Language.GetString("File.Label.Uploading") '"正在上传"
                        cellProgress.tbProgressStyle = ProgressStyle.Progress
                        cellProgress.tbProgressValue = args.TransferProgress
                        Me.mIsInstalling = True
                        CheckCanEnableRefresh()
                    ElseIf args.InstallState = InstallState.AlreadyInstall Then
                        cellProgress.tbTextBackup = Me.Language.GetString("App.Cell.Installed") '"已安装 "
                        cellProgress.tbButtonImgFirst = Nothing
                        cellProgress.tbProgressStyle = ProgressStyle.Button
                        RemovePathFromInstalling(packPath)
                        CheckCanEnableRefresh()
                    ElseIf args.InstallState = InstallState.Waiting Then
                        cellProgress.tbShowCancelButton = True
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbText = Me.Language.GetString("App.Cell.WaitingInstall") '"等待安装"
                        cellProgress.tbProgressStyle = ProgressStyle.Text
                        Me.mIsInstalling = True
                        CheckCanEnableRefresh()
                    ElseIf args.InstallState = InstallState.Installing Then
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbText = Me.Language.GetString("App.Cell.Installing") '"正在安装"
                        If Me.IsIPA(args.PackageInfo) Then
                            cellProgress.tbProgressStyle = ProgressStyle.Progress
                            cellProgress.tbProgressValue = args.TransferProgress
                        Else
                            cellProgress.tbProgressStyle = ProgressStyle.UnsureProgress
                        End If

                        Me.mIsInstalling = True
                        CheckCanEnableRefresh()
                    ElseIf args.InstallState = InstallState.Succeed Then
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbTextBackup = Me.Language.GetString("App.Cell.InstallSucceed") '"安装成功"
                        cellProgress.tbButtonImgFirst = Nothing
                        cellProgress.tbProgressStyle = ProgressStyle.Button
                        RemovePathFromInstalling(packPath)
                        CheckCanEnableRefresh()
                    ElseIf args.InstallState = InstallState.HaveNotInstall Then

                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbTextBackup = IIf(String.IsNullOrEmpty(args.ErrorMsg), Me.Language.GetString("Download.Label.Failed"), args.ErrorMsg)
                        cellProgress.tbButtonImgFirst = Nothing
                        cellProgress.tbProgressStyle = ProgressStyle.Button
                        RemovePathFromInstalling(packPath)
                        CheckCanEnableRefresh()

                        'cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        ''cellProgress.tbTextFail = args.ErrorMsg '
                        'cellProgress.tbProgressStyle = ProgressStyle.Failed
                        'cellProgress.tbText = args.ErrorMsg ' ""
                        'cellProgress.Enabled = True
                        'cellProgress.tbTextFail = Me.Language.GetString("Download.Label.Failed") '"Failed"
                        'RemovePathFromInstalling(packPath)
                        'CheckCanEnableRefresh()
                    ElseIf args.InstallState = InstallState.UnSucceed Then
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        'cellProgress.tbTextFail = args.ErrorMsg '
                        cellProgress.tbText = IIf(String.IsNullOrEmpty(args.ErrorMsg), Me.Language.GetString("Download.Label.Failed"), GetFriendlyErrorText(args.ErrorMsg))
                        '
                        cellProgress.tbProgressStyle = ProgressStyle.Failed
                        RemovePathFromInstalling(packPath)
                        CheckCanEnableRefresh()

                        If args.ErrorMsg = Me.Language.GetString("App.Message.UninstallThenReinstall") Then
                            cellProgress.tbText = Me.Language.GetString("App.Button.FocedInstall")          '"卸载并重装"
                            cellProgress.tbTextFail = args.ErrorMsg
                        End If

                    ElseIf args.InstallState = InstallState.FailRetry OrElse args.InstallState = InstallState.UnSucceedCraked Then
                        cellProgress.tbProgressStyle = ProgressStyle.OneRetryButton
                        cellProgress.tbText = IIf(args.InstallState = InstallState.FailRetry, Me.Language.GetString("Download.Button.Redownload"), Me.Language.GetString("Download.Button.Genuine")) '重新下载"下载正版"
                        'cell.tbFailRetry = True
                        cellProgress.Enabled = True
                        cellProgress.tbTextFail = IIf(args.InstallState = InstallState.FailRetry, Me.Language.GetString("PkgInstaller.Label.InstallFailCompatible"), Me.Language.GetString("PkgInstaller.Label.UnSucceedCraked")) '"#安装失败,软件已过期" '"设备未越狱,无法安装此越狱包"args.ErrorMsg
                        If cellProgress.tbText.Contains(cellProgress.tbTextFail) Then
                            cellProgress.tbTextFail = String.Empty
                        End If
                        cellProgress.tbShowCancelButton = False
                        cellProgress.tbForColor = Color.Blue


                    ElseIf args.InstallState = InstallState.CancelInstall Then
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbTextBackup = Me.Language.GetString("App.Cell.InstalCancel") '"安装取消"
                        cellProgress.tbButtonImgFirst = Nothing
                        cellProgress.tbProgressStyle = ProgressStyle.Button
                        RemovePathFromInstalling(packPath)
                        CheckCanEnableRefresh()
                    ElseIf args.InstallState = InstallState.BackupRecord Then
                        cellProgress.tbShowCancelButton = (args.TransferProgress < 100)
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbText = Me.Language.GetString("App.Cell.BackupRecord")        '"正在备份记录"
                        cellProgress.tbProgressStyle = ProgressStyle.Progress
                        cellProgress.tbProgressValue = args.TransferProgress
                        Me.mIsInstalling = True
                        CheckCanEnableRefresh()
                    End If
                End If

                '兼容在安装过程中重新下载的情况
                If args.InstallState = InstallState.AllInstallCompleted Then
                    mListInstallingFile.Clear()
                    mIsInstalling = False
                    Me.CheckCanEnableRefresh()
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "frmDownloaded.OnInstallEventHandler")
            End Try

        End If

    End Sub

    'TODO 与frmCommonApp中的重复代码整合 'by luyanjun
    Private Function GetFriendlyErrorText(ByVal strErrorCode As String) As String
        Select Case strErrorCode
            Case AndroidInstallErrorCode.INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES
                Return Me.Language.GetString("Download.Message.SignError")              '"签名错误"
            Case AndroidInstallErrorCode.INSTALL_FAILED_ALREADY_EXISTS
                Return Me.Language.GetString("App.Cell.AlreadyInstall")
            Case AndroidInstallErrorCode.INSTALL_FAILED_INSUFFICIENT_STORAGE, AndroidInstallErrorCode.INSTALL_FAILED_DEXOPT
                Return Me.Language.GetString("Download.Message.LackOfSpace")            '"没有足够的存储空间或者不允许安装到目标位置"
            Case AndroidInstallErrorCode.INSTALL_FAILED_MEDIA_UNAVAILABLE
                Return Me.Language.GetString("Download.Message.NoSDCard")               '"SD卡不存在"
            Case AndroidInstallErrorCode.INSTALL_FAILED_UID_CHANGED
                Return Me.Language.GetString("Download.Message.AppConflict")            '"应用冲突"
            Case AndroidInstallErrorCode.INSTALL_FAILED_UPDATE_INCOMPATIBLE
                Return Me.Language.GetString("Download.Message.IncompatibleVersion")    '"版本不兼容"
            Case AndroidInstallErrorCode.INSTALL_FAILED_NO_CERTIFICATES
                Return Me.Language.GetString("Download.Message.NoCertifcates")          '"应用未验证"
            Case AndroidInstallErrorCode.INSTALL_FAILED_INVALID_APK
                Return Me.Language.GetString("Download.Message.FailedInvalidApk")       '"解析包出错"
            Case AndroidInstallErrorCode.INSTAL_FAILED_CANCELED_BY_USER
                Return Me.Language.GetString("Download.Message.CanceledByUser")         '"需手动安装"
            Case AndroidInstallErrorCode.INSTALL_FAILED_UNKNOW_MESSAGE
                Return Me.Language.GetString("Download.Message.UnknowMessage")          '"未知结果"
            Case AndroidInstallErrorCode.INSTALL_FAILED_INTERNAL_ERROR
                Return "安卓设备系统问题导致安装失败"
            Case AndroidInstallErrorCode.INSTALL_FAILED_INVALID_INSTALL_LOCATION
                Return "应用安装位置错误"
            Case AndroidInstallErrorCode.INSTALL_FAILED_SHARED_USER_INCOMPATIBLE
                Return "应用不兼容"
            Case AndroidInstallErrorCode.INSTALL_PARSE_FAILED_UNEXPECTED_EXCEPTION
                Return "应用签名失败或没有签名"
            Case Else
                Return strErrorCode
        End Select
    End Function

#End Region

#Region "--- 导入功能及导入事件回调 --"

    Private Sub ImportItemToDevice(ByVal device As UsbDevice, ByVal listFiles As List(Of DataGridViewRow), Optional ByVal type As ImportType = ImportType.Ringtone)
#If IS_ITONG Then
        Try
            Dim isRingtone As Boolean = IIf(Me.mCurrentView = DownloadView.Ringtone, True, False)
            Dim listMusics As New List(Of String)

            For Each row As DataGridViewRow In listFiles
                Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)

                If mListImportingFile.Contains(item.DownloadPath) Then
                    Continue For
                End If
                Me.mIsImportingMusic = IIf(Me.mCurrentView = DownloadView.Music, True, False)
                Me.mbIsImportingRington = isRingtone

                listMusics.Add(item.DownloadPath)

                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("Status")
                cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbText = Me.Language.GetString("Download.Label.Waiting") '"等待"
            Next

            Dim helper As New iPhoneImportHelper(device, Me.mApplication, Me)
            AddHandler helper.ImportProgress, AddressOf OnImportProgress
            AddHandler helper.ImportCompelete, AddressOf OnImportComplete
            Me.mListImportingFile.AddRange(listMusics)

            Dim iType As ImportType = ImportType.Notification
            Select Case Me.mCurrentView
                Case DownloadView.Music
                    iType = ImportType.Music

                Case DownloadView.Ringtone
                    iType = type

            End Select

            helper.StartImport(iType, listMusics, Me)

            Me.SetReloadEnable(False)
            Me.ShowProgressPanel(True)
            Me.CheckCanEnableRefresh()
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.ImportItemToDevice")
        End Try
#End If
    End Sub

    Private Delegate Sub OnImportProgressHandler(ByVal sender As Object, ByVal args As ImportProgressArgs)
    Private Sub OnImportProgress(ByVal sender As Object, ByVal args As ImportProgressArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New OnImportProgressHandler(AddressOf OnImportProgress), New Object() {sender, args})
        Else
            Try
                If GetDeviceCount() = 0 Then
                    Exit Sub
                End If
                Dim row As DataGridViewRow = Me.GetRowByFileName(args.LocalFileName)
                If row IsNot Nothing Then
                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("Status")
                    If args.CurrentFileTransfer = args.CurrentFileLength Then
                        cellProgress.tbProgressStyle = args.ProgressStyle
                        cellProgress.tbTextBackup = args.ProgressString
                        cellProgress.tbButtonImgFirst = Nothing
                    Else
                        cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                        cellProgress.tbProgressStyle = args.ProgressStyle
                        cellProgress.tbProgressValue = 100.0 * args.CurrentFileTransfer / args.CurrentFileLength
                        cellProgress.tbText = args.ProgressString
                    End If

                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "frmDownloaded.OnImportProgress")
            End Try

        End If
    End Sub

    Private Delegate Sub OnImportCompleteHandler(ByVal sender As Object, ByVal args As ImportCompleteArgs)
    Private Sub OnImportComplete(ByVal sender As Object, ByVal args As ImportCompleteArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New OnImportCompleteHandler(AddressOf OnImportComplete), New Object() {sender, args})
        Else
            Try
                If args.Type = ImportType.Music Then
                    Me.mIsImportingMusic = False
                ElseIf args.Type = ImportType.Ringtone OrElse args.Type = ImportType.Notification OrElse args.Type = ImportType.Alarm Then
                    Me.mbIsImportingRington = False
                End If

                ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                ''' Added by Utmost20150702
                If args.CancelFlag Then
                    '' 取消导入
                    For Each fItem As String In args.ListFiles

                        Dim rowArg As DataGridViewRow = Nothing

                        For Each row As DataGridViewRow In Me.dgvDetail.Rows
                            If row.Tag Is Nothing Then Continue For
                            Dim downloadItem As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
                            If String.Equals(downloadItem.DownloadPath, fItem, StringComparison.InvariantCultureIgnoreCase) Then

                                rowArg = row
                                Exit For
                            End If
                        Next

                        If rowArg IsNot Nothing Then
                            Dim cellProgress As tbDataGridViewProgressCellEx = rowArg.Cells("Status")
                            cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                            cellProgress.tbProgressStyle = ProgressStyle.Button
                            cellProgress.tbShowButtonSecond = False
                            cellProgress.tbShowButtonThird = True
                            cellProgress.tbShowCancelButton = False
                            cellProgress.tbTextBackup = Me.Language.GetString("File.Button.UploadToDevice") '"导入"
                            cellProgress.tbTextUnInstall = Me.Language.GetString("App.Button.Uninstall") '"卸载"
                        End If

                    Next

                End If
                '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

                mListImportingFile.Clear()
                If Me.mCurrentView = DownloadView.Music Or Me.mCurrentView = DownloadView.Ringtone Then
                    Me.SetReloadEnable(True)
                    Me.ShowProgressPanel(False)
                End If

                CheckCanEnableRefresh()
            Catch ex As Exception
                Common.LogException(ex.ToString, "frmDownloaded.OnImportComplete")
            End Try


        End If

    End Sub

#End Region

#Region "--- 包解析完成事件 ---"

    Private Delegate Sub onPackageUpdateHandler(ByVal listPackage As List(Of PackageInfo))
    Private Sub onPackageUpdate(ByVal listPackage As List(Of PackageInfo))
        If listPackage.Count = 1 Then
            Dim threadRepace As New Threading.Thread(New Threading.ParameterizedThreadStart(AddressOf DoReplacePackage))
            threadRepace.IsBackground = True
            threadRepace.Start(listPackage(0))
        End If


    End Sub

    Private Sub DoReplacePackage(ByVal args As Object)
        Try
            Dim package As PackageInfo = args
            Dim iTryCount As Integer = 0
            Dim row As DataGridViewRow = Nothing
            For iTryCount = 0 To 5
                If Me.mDictPathToRow.ContainsKey(package.PackagePath.ToLower) Then
                    row = Me.mDictPathToRow(package.PackagePath.ToLower)
                    Exit For
                End If
                Threading.Thread.Sleep(200)
            Next

            If row IsNot Nothing Then
                DoUpdateCrackState(row, package)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub DoUpdateCrackStateHandler(ByVal row As DataGridViewRow, ByVal package As PackageInfo)
    Private Sub DoUpdateCrackState(ByVal row As DataGridViewRow, ByVal package As PackageInfo)
        If Me.InvokeRequired Then
            Me.Invoke(New DoUpdateCrackStateHandler(AddressOf DoUpdateCrackState), New Object() {row, package})
        Else
            If Me.dgvDetail.Rows.Contains(row) Then
                Dim downloadInfo As MultiThreadDownloadItemInfo = row.Tag
                Dim cellTitle As tbDataGridViewTextBoxCellEx = row.Cells("Title")
                Me.mDictItemToPackage(downloadInfo) = package
                cellTitle.CrackedInfo = package.CrackedInfo
                cellTitle.ShowCrackedIcon = False
                cellTitle.Invalidate()
                My.Application.DoEvents()
                My.Application.DoEvents()
                My.Application.DoEvents()
                System.Diagnostics.Debug.Print("更新CraceState" + vbCrLf)
            End If
        End If

    End Sub



#End Region

#Region "--- 下载完成事件 ---"
    ''' <summary>
    ''' 下载完成回调
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    ''' <remarks></remarks>
    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloaded), sender, e)
        Else
            If e.ReceiveSize > 0 AndAlso e.ReceiveSize = e.ResourceSize Then
                UpdateDownloaded(e.FilePath)
            End If
        End If
    End Sub

    Private Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.Class = ResourceClass.Plugins Then
                Return
            End If

            RemoveExistedRow(sender, item)

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub RemoveExistedRow(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of MultiThreadDownloadItem)(AddressOf RemoveExistedRow), sender, item)
            Else
                Dim strDownloadFilePath As String = item.ItemInfo.DownloadPath.ToLower
                If mDictPathToRow.ContainsKey(strDownloadFilePath) Then
                    Dim row As DataGridViewRow = Me.mDictPathToRow(strDownloadFilePath)
                    Me.CancelInstallation(row, row.Tag)
                    RemoveItemCache(strDownloadFilePath)
                    If Me.dgvDetail.Rows.Contains(row) Then
                        Me.dgvDetail.Rows.Remove(row)
                        Me.UpdateParentCount()
                    End If
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub RemoveItemCache(ByVal strDownloadFilePath As String)

        If Me.mDictPathToRow.ContainsKey(strDownloadFilePath) Then
            Dim row As DataGridViewRow = Me.mDictPathToRow(strDownloadFilePath)
            Dim downItem As MultiThreadDownloadItemInfo = row.Tag
            If Me.mDictItemToPackage.ContainsKey(downItem) Then
                Me.mDictItemToPackage.Remove(downItem)
            End If
            If Me.mDictPathToPackage.ContainsKey(strDownloadFilePath) Then
                Me.mDictPathToPackage.Remove(strDownloadFilePath)
            End If
            If Me.mDictPathToMusic.ContainsKey(strDownloadFilePath) Then
                Me.mDictPathToMusic.Remove(strDownloadFilePath)
            End If
            Me.mDictPathToRow.Remove(strDownloadFilePath)
        End If
    End Sub

#End Region

#Region "--- 数据加载和页面切换 ---"

    Private Sub BeginReloadAll()
        If Not bgwLoadData.IsBusy Then
            Me.mIsRefreshing = True
            Me.ClearAllData()
            Dim listDownViews As New List(Of DownloadView)
            listDownViews.Add(DownloadView.Software)
            listDownViews.Add(DownloadView.Music)
            listDownViews.Add(DownloadView.Ringtone)
            listDownViews.Add(DownloadView.Firmware)
            Me.bgwLoadData.RunWorkerAsync(listDownViews)
        End If
    End Sub

    Private Sub BeginReloadMusic()
        Me.RemoveDownloadViewData(DownloadView.Music)
        Dim listDownViews As New List(Of DownloadView)
        listDownViews.Add(DownloadView.Music)
        Me.bgwLoadData.RunWorkerAsync(listDownViews)
    End Sub

    Private Sub BeginReloadRingtone()
        RemoveDownloadViewData(DownloadView.Ringtone)
        Dim listDownViews As New List(Of DownloadView)
        listDownViews.Add(DownloadView.Ringtone)
        Me.bgwLoadData.RunWorkerAsync(listDownViews)
    End Sub

    Private Sub BeginReloadSoftware()
        RemoveDownloadViewData(DownloadView.Software)
        Dim listDownViews As New List(Of DownloadView)
        listDownViews.Add(DownloadView.Software)
        Me.bgwLoadData.CancelAsync()
        Me.bgwLoadData.RunWorkerAsync(listDownViews)
    End Sub

    Private Sub BeginReloadFirmware()
        RemoveDownloadViewData(DownloadView.Firmware)
        Dim listDownViews As New List(Of DownloadView)
        listDownViews.Add(DownloadView.Firmware)
        Me.bgwLoadData.CancelAsync()
        Me.bgwLoadData.RunWorkerAsync(listDownViews)
    End Sub

    Private Sub bgwLoadData_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwLoadData.DoWork

        Try
            e.Result = e.Argument
            mDownloadDB.Reload()
            Dim listViews As List(Of DownloadView) = e.Argument

            For Each view As DownloadView In listViews
                ReloadDownloadData(view)
                Common.Log("End rReloadDownloadData " & view.ToString())
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.bgwLoadData_DoWork")
        End Try

    End Sub

    Private Sub bgwLoadData_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLoadData.RunWorkerCompleted

        Try
            Dim listViews As List(Of DownloadView) = e.Result
            Common.Log("End get Downloaded")
            Me.dgvDetail.SuspendLayout()

            For Each view As DownloadView In listViews
                Me.RemoveDownloadViewRow(view)
                Common.Log("End removed Downloaded " & view.ToString())

                If Me.mDictViewToList.ContainsKey(view) Then
                    Dim list As List(Of MultiThreadDownloadItemInfo) = mDictViewToList(view)
                    Me.ShowItems(list)
                    Common.Log("End show Downloaded " & view.ToString())
                End If
                Common.Log("End load Downloaded " & view.ToString())
            Next
            dgvDetail.ResumeLayout()
            SwitchView(Me.mCurrentView)
            Common.Log("End SwitchView " & Me.mCurrentView.ToString())

            SetReloadEnable(True)
            Common.Log("End SetReloadEnable ")

            Me.UpdateParentCount()
            Common.Log("End UpdateParentCount ")

            ShowLoading(False)

            If Not HasDevice() Then
                Me.btnImport.Enabled = False
                Me.btnInstall.Enabled = False
                EnableRowOperation(False)
            End If
            Me.mIsRefreshing = False
            Me.txtSearch.Text = ""
            Me.CheckCanEnableImportDelete()
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try

    End Sub

    Private Sub ClearAllData()
        Me.mDictPathToMusic.Clear()
        Me.mDictPathToPackage.Clear()
        Me.mDictPathToRow.Clear()
        Me.mDictRowToInstall.Clear()
        Me.dgvDetail.Rows.Clear()

        Me.mListMusic.Clear()
        Me.mListRingtone.Clear()
        Me.mListSoftware.Clear()
    End Sub

    ' 移除某一个视图的数据，用于重新加载
    Private Sub RemoveDownloadViewData(ByVal view As DownloadView)
        Me.RemoveDownloadViewRow(view)

        If Me.mDictViewToList.ContainsKey(view) Then
            Dim list As List(Of MultiThreadDownloadItemInfo) = mDictViewToList(view)
            For Each item As MultiThreadDownloadItemInfo In list
                If mDictPathToMusic.ContainsKey(item.DownloadPath.ToLower) Then
                    mDictPathToMusic.Remove(item.DownloadPath.ToLower)
                ElseIf Me.mDictPathToPackage.ContainsKey(item.DownloadPath.ToLower) Then
                    mDictPathToPackage.Remove(item.DownloadPath.ToLower)
                End If
            Next
            list.Clear()
        End If

    End Sub

    ' 移除某一视图的数据行，在加入新行之前调用
    Private Sub RemoveDownloadViewRow(ByVal view As DownloadView)
        If Me.mDictViewToList.ContainsKey(view) Then
            Dim list As List(Of MultiThreadDownloadItemInfo) = mDictViewToList(view)
            'Me.dgvDetail.SuspendLayout()

            For Each item As MultiThreadDownloadItemInfo In list
                If Me.mDictPathToRow.ContainsKey(item.DownloadPath.ToLower) Then
                    Dim row As DataGridViewRow = mDictPathToRow(item.DownloadPath.ToLower)
                    'Me.dgvDetail.Rows.Remove(row)
                    Me.mDictPathToRow.Remove(item.DownloadPath.ToLower)
                End If
            Next

            Dim tarClass As ResourceClass = GetResClassByView(view)

            Dim arrRowsToRemove As List(Of DataGridViewRow) = New List(Of DataGridViewRow)()
            For Each row As DataGridViewRow In Me.dgvDetail.Rows
                Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
                If item.Class = tarClass AndAlso dgvDetail.Rows.Contains(row) Then
                    arrRowsToRemove.Add(row)
                    If Me.mDictPathToRow.ContainsKey(item.DownloadPath.ToLower) Then
                        Me.mDictPathToRow.Remove(item.DownloadPath.ToLower)
                    End If
                End If
            Next

            For Each row As DataGridViewRow In arrRowsToRemove
                dgvDetail.Rows.Remove(row)
            Next
            'Me.dgvDetail.ResumeLayout()

        End If
    End Sub

    ' 重新加载指定类型的数据
    Private Sub ReloadDownloadData(ByVal view As DownloadView)
        Try
            Dim list As New List(Of MultiThreadDownloadItemInfo)

            Me.RemoveDownloadViewRow(view)
            Select Case view
                Case DownloadView.Software
                    Me.mListSoftware = mDownloadDB.GetAll_Task(ResourceClass.Software, ResourceType.All, TaskState.Completed, "")
                    list = mListSoftware
                    FilterExistFiles(mListSoftware)
                    mDictViewToList(DownloadView.Software) = Me.mListSoftware

                    ' 缓存软件包信息
                    CachePackageInfo(list)

                Case DownloadView.Music
                    Me.mListMusic = mDownloadDB.GetAll_Task(ResourceClass.Music, ResourceType.All, TaskState.Completed, "")
                    list = mListMusic
                    mDictViewToList(DownloadView.Music) = Me.mListMusic

                    ' 缓存音乐信息
                    CacheMusicInfo(list)

                Case DownloadView.Ringtone
                    Me.mListRingtone = mDownloadDB.GetAll_Task(ResourceClass.Ringtone, ResourceType.All, TaskState.Completed, "")
                    list = Me.mListRingtone
                    mDictViewToList(DownloadView.Ringtone) = Me.mListRingtone
                Case DownloadView.Paper
                    Me.mListPeper = mDownloadDB.GetAll_Task(ResourceClass.Wallpaper, ResourceType.All, TaskState.Completed, "")
                    list = Me.mListPeper
                    mDictViewToList(DownloadView.Paper) = Me.mListPeper
                Case DownloadView.Firmware
                    Me.mListFirmware = mDownloadDB.GetAll_Task(ResourceClass.Firmware, ResourceType.Ipsw, TaskState.Completed, "")
                    list = Me.mListFirmware
                    mDictViewToList(DownloadView.Firmware) = Me.mListFirmware

                Case Else
                    Throw New Exception("Invalid DownloadView Type")
            End Select

            'If list.Count > 0 Then
            '    list.Add(list(0))
            'End If

            ' 过滤掉不存在的文件
            FilterExistFiles(list)

            ' 按时间倒序进行排序
            list.Sort(New DownloadItemComparer())

            If Not Me.InvokeRequired Then
                UpdateParentCount()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.ReloadDownloadType")
        End Try


    End Sub

    ' 将list中数据显示在Datagridview中
    Private Sub ShowItems(ByVal listData As List(Of MultiThreadDownloadItemInfo))
        Try
            For Each item As MultiThreadDownloadItemInfo In listData
                AddItemToDatagridView(item)
            Next
            Me.DoSearch()
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.ShowItems")
        End Try

    End Sub

    ' 添加单个下载数据到Datagridview中，对已存在的项目，将返回nothing，注意判断
    Private Function AddItemToDatagridView(ByVal item As MultiThreadDownloadItemInfo, Optional ByVal isInsertTop As Boolean = False) As DataGridViewRow
        Dim row As DataGridViewRow = Nothing
        Try
            Dim itemKey As String = item.DownloadPath.ToLowerInvariant()

            'If mDictPathToRow Is Nothing OrElse _
            '   Me.mDictPathToRow.ContainsKey(itemKey) OrElse _
            '   mDictItemToPackage Is Nothing OrElse _
            '   mDictItemToPackage.Count = 0 OrElse _
            '   Not mDictItemToPackage.ContainsKey(item) Then
            '    Return Nothing
            'End If
            '铃声 音乐 固件 加载
            If Me.mDictPathToRow.ContainsKey(item.DownloadPath.ToLower) Then
                Return Nothing
            End If


            Dim dateNow As DateTime = DateTime.Now
            Dim strDateString As String = ""

            If dateNow.Date = item.CompletedTime.Date Then
                Dim span As TimeSpan = dateNow - item.CompletedTime
                If span.TotalMinutes < 1 Then
                    strDateString = Me.Language.GetString("Download.Message.JustNow") '"刚刚"
                Else
                    strDateString = Me.Language.GetString("Download.Message.Today") '"今天"
                End If
            Else
                strDateString = item.CompletedTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDay))
            End If

            Dim index As Integer = 0

            Dim package As PackageInfo = Nothing
            If Me.mDictItemToPackage.ContainsKey(item) Then
                package = Me.mDictItemToPackage(item)
            End If

            Dim statue As CrakedInfo = CrakedInfo.Unknow
            If package IsNot Nothing Then
                statue = package.CrackedInfo
            End If

            Dim colorType As Color = Color.Empty
            Dim sizeType As SizeF = New SizeF(0.0, 0.0)
            Dim strStatue As String = iPhoneInstallHelper.GetPackageInfoType(statue, colorType, sizeType)

            If isInsertTop Then
                index = 0
                Me.dgvDetail.Rows.Insert(0, New Object() {False, item.Name, strStatue, "-", "", Utility.FormatFileSize(item.ResourceSize), "", strDateString, "", item.Url & "", ""})
            Else
                index = Me.dgvDetail.Rows.Add(False, item.Name, strStatue, "-", "", Utility.FormatFileSize(item.ResourceSize), "", strDateString, "", item.Url & "", "")
            End If

            row = Me.dgvDetail.Rows(index)
            mDictRowToTask(row) = item
            mDictPathToRow(itemKey) = row

            Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("Status")
            Dim cellRequirements As tbDataGridViewTextBoxCell = row.Cells("Requirements")

            Dim cellType As tbDataGridViewTextBoxCell = row.Cells("colType")
            cellType.tbTextBackgroundSize = sizeType
            cellType.tbTextBackgroundColor = colorType
            If colorType <> Color.Empty Then
                cellType.tbForeColor = Color.FromArgb(255, 255, 255)
            End If

            row.Tag = item
            row.Visible = False

            Dim cellTitle As tbDataGridViewTextBoxCellEx = row.Cells("Title")
            cellTitle.tbTextToolTip = False

            If HasDevice() Then
                cellProgress.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Cancel Or CellButtonStyle.Document Or CellButtonStyle.Failed

            Else
                cellProgress.tbCellButtonEnable = CellButtonStyle.Cancel Or CellButtonStyle.Document Or CellButtonStyle.Failed
            End If

            ' 如果是软件包，额外解析软件信息并填写版本
            If item.Class = ResourceClass.Software Then
                Common.Log(String.Format("正在添加软件数据:{0}", item.FilePath))
                Dim csllVersion As tbDataGridViewTextBoxCell = row.Cells("Version")
                Dim info As PackageInfo = Nothing
                If Me.mDictItemToPackage.ContainsKey(item) AndAlso Not isInsertTop Then  '新下载的包总是读取新的信息 by luyanjun
                    info = Me.mDictItemToPackage(item)
                Else
                    If IsIPA(item) Then
                        info = PackageInfoReader.GetPackageInfo(item.DownloadPath, False, True)
                    ElseIf IsAPK(item) Then
                        info = ApkReader.GetPackageInfo(item.DownloadPath)
                    End If
                End If
                mDictItemToPackage(item) = info

                If IsIPA(item) Then
                    cellTitle.CrackedInfo = info.CrackedInfo
                    cellTitle.ShowCrackedIcon = False
                ElseIf IsAPK(item) Then
                    cellTitle.CrackedInfo = CrakedInfo.Unknow
                    cellTitle.ShowCrackedIcon = False
                End If

                If info IsNot Nothing AndAlso info.AppIcon IsNot Nothing Then
                    cellTitle.tbIcon = info.AppIcon.Clone()
                    cellTitle.tbIconShadow = True
                Else
                    cellTitle.tbIcon = My.Resources.app_icon_default
                    cellTitle.tbIconShadow = True
                End If
                If info IsNot Nothing Then
                    ApplePackageReader.OperatePackageNameForBT(info)
                    Dim strName As String = IIf(String.IsNullOrEmpty(info.DisplayName), info.Name, info.DisplayName)
                    'If Not String.IsNullOrEmpty(info.AppleId) Then
                    'cellTitle.Value = strName + vbCrLf + info.AppleId
                    'Else
                    cellTitle.Value = strName
                    'End If

                    csllVersion.Value = info.Version

                    cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                    cellProgress.tbProgressStyle = ProgressStyle.Button
                    cellProgress.tbShowButtonSecond = False
                    cellProgress.tbShowButtonThird = True
                    cellProgress.tbShowCancelButton = False
                    cellProgress.tbTextBackup = Me.Language.GetString("App.Button.Install") '"安装"
                    cellProgress.tbTextUnInstall = Me.Language.GetString("App.Button.Uninstall") '"卸载"
                    Select Case info.UIDeviceFamily
                        Case UIDeviceFamily.iPhone
                            If IsAPK(item) Then
                                cellRequirements.tbIcon = My.Resources.app_product_iPhone
                            Else
                                cellRequirements.tbIcon = My.Resources.app_product_iPhone
                            End If
                        Case UIDeviceFamily.iPad
                            cellRequirements.tbIcon = My.Resources.app_product_iPad
                        Case UIDeviceFamily.iPhoneAndiPad
                            cellRequirements.tbIcon = My.Resources.app_product_all
                    End Select

                    cellRequirements.Value = info.MinimumOSVersion
                    cellRequirements.tbIconSize = cellRequirements.tbIcon.Size
                    cellRequirements.tbPadding = New Padding(20, 1, 2, 2)

                    If Folder.LangType = LanguageType.zh_CN Then
                        If info.PackageType = PackageType.Apk Then
                            cellTitle.tbShowIconInfo = False
                        Else
                            cellTitle.tbShowIconInfo = True
                        End If
                    Else
                        cellTitle.tbShowIconInfo = False
                    End If

                    'cellTitle.CrackedInfo = info.CrackedInfo
                    'row.Tag = info
                End If


                'cellRequirements.tbIcon=

                ' 铃声或者音乐，需要额外解析播放时长
            ElseIf item.Class = ResourceClass.Firmware Then
#If IS_ITONG Then
                cellTitle.tbIcon = My.Resources.download_icon_firmware
#End If


            ElseIf item.Class = ResourceClass.Music Or item.Class = ResourceClass.Ringtone Then
                'cellTitle.tbIcon = My.Resources.icon_listMusic
                'cellTitle.tbIconSize = New Size(14, 14)
                Dim info As MusicInfo = Nothing

                If Me.mDictPathToMusic.ContainsKey(itemKey) Then
                    info = mDictPathToMusic(itemKey)
                Else
                    info = New MusicInfo(item.DownloadPath)
                    mDictPathToMusic(itemKey) = info
                    MusicDB.Instance.Insert_MusicInfo(info)
                End If
                Dim cellDuration As tbDataGridViewTextBoxCell = row.Cells("Duration")
                cellDuration.Value = Utility.FormatDuration(info.Duration)

                cellProgress.tbPadding = New Padding(40, 0, 0, 0)
                cellProgress.tbProgressStyle = ProgressStyle.Button
                cellProgress.tbShowButtonSecond = False
                cellProgress.tbShowButtonThird = True
                cellProgress.tbShowCancelButton = False
                cellProgress.tbTextBackup = Me.Language.GetString("File.Button.UploadToDevice") '"导入"
                cellProgress.tbTextUnInstall = Me.Language.GetString("App.Button.Uninstall") '"卸载"
            Else
                cellProgress.tbProgressStyle = ProgressStyle.None
            End If


        Catch ex As Exception
            row = Nothing
            Common.LogException(ex.ToString, "frmDownloaded.AddItemToDatagridView")
        End Try

        Return row
    End Function

    ' 根据数据类型过滤Datagridview中的行
    Private Sub FilterItemsByType(ByVal type As DownloadView)
        Try

            Dim tarClass As ResourceClass = GetResClassByView(type)
            For Each row As DataGridViewRow In Me.dgvDetail.Rows
                Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
                If item.Class = tarClass Then
                    If item.Class = ResourceClass.Software Then
                        If Me.btnAndroid.Checked Then
                            row.Visible = IsAPK(item)
                        Else
                            row.Visible = IsIPA(item)
                        End If
                    Else
                        row.Visible = True
                    End If

                Else
                    row.Visible = False
                End If
            Next

            If type = DownloadView.Software Then
                Me.btnIPhone.Visible = Common.VerIs30()
                Me.btnAndroid.Visible = Common.VerIs30()
                EnableRowOperation(HasDevice())
            Else
                Me.btnIPhone.Visible = False
                Me.btnAndroid.Visible = False
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.FilterItemsByType")
        End Try

    End Sub

    Private Function GetResClassByView(ByVal type As DownloadView) As ResourceClass

        Dim tarClass As ResourceClass = ResourceClass.Software
        Select Case type
            Case DownloadView.Software
                tarClass = ResourceClass.Software
            Case DownloadView.Music
                tarClass = ResourceClass.Music
            Case DownloadView.Ringtone
                tarClass = ResourceClass.Ringtone
            Case DownloadView.Paper
                tarClass = ResourceClass.Wallpaper
            Case DownloadView.Firmware
                tarClass = ResourceClass.Firmware
        End Select
        Return tarClass
    End Function

    ''' 去除不存在,及重复的文件
    Private Sub FilterExistFiles(ByRef list As List(Of MultiThreadDownloadItemInfo))
        Dim listCopy As MultiThreadDownloadItemInfo() = list.ToArray
        Dim arrResult As List(Of MultiThreadDownloadItemInfo) = New List(Of MultiThreadDownloadItemInfo)()
        Dim dictCheckOverlap As New Dictionary(Of String, Object)

        For Each listItem As MultiThreadDownloadItemInfo In listCopy

            ' 过滤已存在的记录
            If dictCheckOverlap.ContainsKey(listItem.DownloadPath.ToLower) Then
                list.Remove(listItem)
                Common.Log("overlap" & listItem.DownloadPath)
                Continue For
            End If

            dictCheckOverlap(listItem.DownloadPath.ToLower) = Nothing
            If Not System.IO.File.Exists(listItem.DownloadPath) Then
                list.Remove(listItem)
                Common.Log("NoFound" & listItem.DownloadPath)
                Continue For
            End If

            If listItem.Class = ResourceClass.Software Then
                If Not IsApp(listItem) Then
                    list.Remove(listItem)
                    Common.Log("NotIPA" & listItem.DownloadPath)
                    Continue For
                End If
            End If
            Common.Log("add" & listItem.DownloadPath)
            arrResult.Add(listItem)
        Next

        list = arrResult
        dictCheckOverlap.Clear()
        dictCheckOverlap = Nothing
    End Sub

    Private Function IsApp(ByVal listItem As MultiThreadDownloadItemInfo) As Boolean
        If Common.VerIs30() Then
            Return IsIPA(listItem) OrElse IsAPK(listItem)
        Else
            Return IsIPA(listItem)
        End If
    End Function

    Private Function IsAPK(ByVal listItem As MultiThreadDownloadItemInfo) As Boolean

        Return listItem.DownloadPath.ToLower.EndsWith(".apk")
    End Function

    Private Function IsIPA(ByVal listItem As MultiThreadDownloadItemInfo) As Boolean

        Return listItem.DownloadPath.ToLower.EndsWith(".ipa")
    End Function

    Private Function IsIPA(ByVal app As PackageInfo) As Boolean

        Return app.PackagePath.ToLower.EndsWith(".ipa")
    End Function


    ''' 过滤某个类型的文件
    Private Sub FilterFiles(ByVal list As List(Of MultiThreadDownloadItemInfo), ByVal fileType As ResourceClass)
        Dim listCopy As MultiThreadDownloadItemInfo() = list.ToArray
        For Each listItem As MultiThreadDownloadItemInfo In listCopy
            If Not System.IO.File.Exists(listItem.DownloadPath) Then
                list.Remove(listItem)
                Continue For
            End If

            If listItem.Class <> fileType Then

                list.Remove(listItem)
            End If
        Next

    End Sub

    ''' 缓存软件包信息（从数据库中获取，如果没有重新解析）
    Private Sub CachePackageInfo(ByVal list As List(Of MultiThreadDownloadItemInfo))
        Dim dictCache As Dictionary(Of String, PackageInfo) = SoftManageDB.GetInstance().Get_AllSoft_info(False)
        For Each pair As KeyValuePair(Of String, PackageInfo) In dictCache
            mDictPathToPackage(pair.Key.ToLower) = pair.Value
        Next

        If Common.VerIs30() Then
            dictCache = SoftManageDB.GetInstance().Get_AllSoft_info(True)
            For Each pair As KeyValuePair(Of String, PackageInfo) In dictCache
                mDictPathToPackage(pair.Key.ToLower) = pair.Value
            Next
        End If

        For Each item As MultiThreadDownloadItemInfo In list
            Dim strDownloadPath As String = item.DownloadPath.ToLower()

            If item.Class = ResourceClass.Software Then
                If Not IsApp(item) Then
                    Continue For
                End If

                Dim package As PackageInfo = Nothing
                If Me.mDictPathToPackage.ContainsKey(strDownloadPath) Then
                    package = Me.mDictPathToPackage(strDownloadPath)
                Else
                    If IsIPA(item) Then
                        package = PackageInfoReader.GetPackageInfo(strDownloadPath, False, True)
                    ElseIf IsAPK(item) Then
                        package = ApkReader.GetPackageInfo(strDownloadPath)
                        mAndroidPackageReader.AddPackage(strDownloadPath)
                    End If

                    mDictPathToPackage(strDownloadPath) = package
                End If

                mDictItemToPackage(item) = package
            End If
        Next
    End Sub

    ''' 缓存音乐信息
    Private Sub CacheMusicInfo(ByVal list As List(Of MultiThreadDownloadItemInfo))
        Dim listMusics As List(Of MusicInfo) = MusicDB.Instance.GetAll_MusicInfo(MusicState.Info_Show, FileState.Exist, "", New List(Of String), "", 0, PlaylistState.All, DateTime.MinValue, "", "", Media_Kind.AAC Or Media_Kind.M4A Or Media_Kind.M4R Or Media_Kind.MP3 Or Media_Kind.Ringtone, False)
        'listMusics.AddRange(MusicDB.Instance.get
        For Each item As MusicInfo In listMusics
            Me.mDictPathToMusic(item.FilePath.ToLower) = item
        Next
    End Sub

    ''' 通过文件名获取ipa的信息
    Private Function GetPackInfoByFileName(ByVal strPath As String) As PackageInfo
        Dim dictAllSoft As Dictionary(Of String, PackageInfo) = SoftManageDB.GetInstance().Get_AllSoft_info(True)
        Dim info As PackageInfo = New PackageInfo()

        If dictAllSoft.ContainsKey(strPath.ToLower) Then
            info = dictAllSoft(strPath.ToLower)
        Else
            If strPath.ToLower.Contains(".ipa") Then
                info = PackageInfoReader.GetPackageInfo(strPath)

                If info IsNot Nothing Then
                    SoftManageDB.GetInstance().Insert_soft_info(info)
                End If

            End If
        End If
        Return info
    End Function

    ''' 通过文件名获取对应的datagridview行
    Private Function GetRowByFileName(ByVal strPath As String) As DataGridViewRow
        Dim resRow As DataGridViewRow = Nothing
        For Each row As DataGridViewRow In Me.dgvDetail.Rows
            If row.Tag Is Nothing Then Continue For

            Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
            If item.DownloadPath = strPath Then
                resRow = row
                Exit For
            End If
        Next
        Return resRow
    End Function

    ''' <summary>
    ''' 切换已下载的数据显示
    ''' </summary>
    ''' <param name="view"></param>
    ''' <remarks></remarks>
    Public Sub SwitchView(ByVal view As DownloadView)
        Try
            Me.mCurrentView = view

            If Me.IsHandleCreated Then
                Dim listData As New List(Of MultiThreadDownloadItemInfo)
                Dim colVersion As DataGridViewColumn = Me.dgvDetail.Columns("Version")
                Dim colDuration As DataGridViewColumn = Me.dgvDetail.Columns("Duration")
                Dim colRequirements As DataGridViewColumn = Me.dgvDetail.Columns("Requirements")
                Dim colDownloadTime As DataGridViewColumn = Me.dgvDetail.Columns("Time")
                Dim colType As DataGridViewColumn = Me.dgvDetail.Columns("colType")
                Me.tsmiInstallItem.Text = Me.Language.GetString("File.Button.UploadToDevice") '"导入"
                Select Case view
                    Case DownloadView.Software
                        Me.tsmiInstallItem.Text = Me.Language.GetString("App.Button.Install") '"安装"
                        listData = mListSoftware
                        Me.btnInstall.Visible = True
                        Me.btnImport.Visible = False
                        colVersion.Visible = True
                        colDuration.Visible = False
                        colRequirements.Visible = True
                        If Me.btnAndroid.Checked Then
                            colType.Visible = False
                        Else
                            colType.Visible = True
                        End If

                        colDownloadTime.Width = 90
                        Me.ShowProgressPanel(False)

                        Me.UpdateParentCount()
                        If Me.mIsInstalling Then
                            Me.SetReloadEnable(False)
                        Else '
                            Me.SetReloadEnable(True)
                        End If
                    Case DownloadView.Music
                        listData = mListMusic
                        Me.btnInstall.Visible = False
                        Me.btnImport.Visible = True
                        colVersion.Visible = False
                        colDuration.Visible = True
                        colRequirements.Visible = False
                        colType.Visible = False
                        colDownloadTime.Width = 189

                        If Me.mIsImportingMusic Then
                            Me.SetReloadEnable(False)
                        Else '
                            Me.SetReloadEnable(True)
                        End If
                    Case DownloadView.Ringtone
                        listData = Me.mListRingtone
                        Me.btnInstall.Visible = False
                        Me.btnImport.Visible = True
                        colVersion.Visible = False
                        colDuration.Visible = True
                        colRequirements.Visible = False
                        colType.Visible = False
                        colDownloadTime.Width = 189

                        If Me.mbIsImportingRington Then
                            Me.SetReloadEnable(False)
                        Else '
                            Me.SetReloadEnable(True)
                        End If

                    Case DownloadView.Paper
                        listData = mListPeper
                        Me.btnInstall.Visible = False
                        Me.btnImport.Visible = True
                        colVersion.Visible = False
                        colDuration.Visible = False

                    Case DownloadView.Firmware
                        listData = Me.mListFirmware
                        Me.btnInstall.Visible = False
                        Me.btnImport.Visible = True
                        colVersion.Visible = False
                        colDuration.Visible = False
                        colRequirements.Visible = False
                        colType.Visible = False

                End Select


                'If view = DownloadView.Software Then
                '    colVersion.Visible = True
                'Else
                '    colVersion.Visible = False
                'End If
                If mDictViewToSearchKeyword.ContainsKey(view) Then
                    Me.txtSearch.Text = mDictViewToSearchKeyword(view)
                Else
                    Me.txtSearch.Text = String.Empty
                End If
                Me.DoSearch()
                FilterItemsByType(view)

                Me.SwitchViewNoDataInfo()
            End If

            Me.CheckCanEnableImportDelete()
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmdownloaded.SwitchView")
        End Try

    End Sub


    Private Sub UpdateParentCount()
        If frmDownloadCenter.Instance IsNot Nothing Then
            frmDownloadCenter.Instance.UpdateCompleteCount(DownloadView.Software, GetRowCount(DownloadView.Software))

#If IS_ITONG Then
            ' 壁纸的更新要单独做
            Dim listPaths As New List(Of String)
            listPaths.Add("iPhone")
            listPaths.Add("iPad")
            If Common.VerIs30() Then
                listPaths.Add("Android")
            End If

            frmDownloadCenter.Instance.UpdateCompleteCount(DownloadView.Music, GetRowCount(DownloadView.Music))
            frmDownloadCenter.Instance.UpdateCompleteCount(DownloadView.Ringtone, GetRowCount(DownloadView.Ringtone))
            frmDownloadCenter.Instance.UpdateCompleteCount(DownloadView.Paper, frmDownloadWallPaper.WallpaperLoaclObject.GetTotalWallpaperCount(listPaths))
            frmDownloadCenter.Instance.UpdateCompleteCount(DownloadView.Firmware, GetRowCount(DownloadView.Firmware))
#End If
        End If

        '当数量变化时，"这里没有内容"，相应的显示与隐藏
        Me.SwitchViewNoDataInfo()
    End Sub

    Private Function GetRowCount(ByVal view As DownloadView) As Integer
        Dim tarClass As ResourceClass = GetResClassByView(view)
        Dim intRowCount As Integer = 0

        Dim arrRowsToRemove As List(Of DataGridViewRow) = New List(Of DataGridViewRow)()
        For Each row As DataGridViewRow In Me.dgvDetail.Rows
            Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
            If item.Class = tarClass AndAlso dgvDetail.Rows.Contains(row) Then
                intRowCount += 1
            End If
        Next
        Return intRowCount
    End Function

    ''' <summary>
    ''' 更新已下载的本地数据
    ''' </summary>
    ''' <param name="strFilePath"></param>
    ''' <remarks></remarks>
    Private Sub UpdateDownloaded(ByVal strFilePath As String)
        Try
            Dim listType As New List(Of ResourceClass)


            Dim strExt As String = Path.GetExtension(strFilePath).ToLower
            Dim listTypes As New List(Of DownloadView)
            Select Case strExt
                Case ".png"
                    listType.Add(ResourceClass.Wallpaper)
                Case ".jpg"
                    listType.Add(ResourceClass.Wallpaper)
                Case ".mp3", ".m4a"
                    listType.Add(ResourceClass.Music)
                    listType.Add(ResourceClass.Ringtone)
                Case ".m4r"
                    listType.Add(ResourceClass.Ringtone)
                Case ".ipa"
                    listType.Add(ResourceClass.Software)
                Case ".apk"
                    listType.Add(ResourceClass.Software)
                Case ".ipsw"
                    listType.Add(ResourceClass.Firmware)

            End Select
            Dim listTasks As New List(Of MultiThreadDownloadItemInfo)
            For Each typeItem As ResourceClass In listType
                listTasks.AddRange(Me.mDownloadDB.GetAll_Task(typeItem, ResourceType.All, TaskState.All, ""))
            Next

            Dim targetItem As MultiThreadDownloadItemInfo = Nothing
            For Each downloadItem As MultiThreadDownloadItemInfo In listTasks
                If downloadItem.DownloadPath = strFilePath Then
                    targetItem = downloadItem
                    Exit For
                End If
            Next

            If targetItem IsNot Nothing Then
                Dim downloadView As DownloadView
                Select Case targetItem.Class
                    Case ResourceClass.Music
                        downloadView = downloadView.Music
                        Me.mListMusic = listTasks
                        'Me.mListMusic.Add(targetItem)
                        Me.FilterExistFiles(mListMusic)
                        FilterFiles(mListMusic, targetItem.Class)

                    Case ResourceClass.Ringtone
                        downloadView = downloadView.Ringtone
                        Me.mListRingtone = listTasks
                        'Me.mListRingtone.Add(targetItem)
                        Me.FilterExistFiles(mListRingtone)
                        FilterFiles(mListRingtone, targetItem.Class)
                    Case ResourceClass.Software
                        downloadView = downloadView.Software
                        Me.mListSoftware = listTasks
                        'Me.mListSoftware.Add(targetItem)
                        Me.FilterExistFiles(mListSoftware)
                        Me.CachePackageInfo(mListSoftware)
                    Case ResourceClass.Firmware
                        downloadView = downloadView.Firmware
                        Me.mListFirmware = listTasks
                        'Me.mListSoftware.Add(targetItem)
                        Me.FilterExistFiles(mListFirmware)
                        FilterFiles(mListFirmware, targetItem.Class)

                End Select
                Dim row As DataGridViewRow = AddItemToDatagridView(targetItem, True)

                If row IsNot Nothing AndAlso targetItem IsNot Nothing AndAlso targetItem.Class <> ResourceClass.Wallpaper Then
                    Me.SwitchView(Me.mCurrentView)

                    If Me.mCurrentView = downloadView Then
                        For i As Integer = 0 To dgvDetail.RowCount
                            If dgvDetail.Rows(i).Visible Then
                                dgvDetail.FirstDisplayedScrollingRowIndex = i
                                Exit For
                            End If
                        Next
                    End If

                End If
                Me.UpdateParentCount()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.UpdateDownloaded")
        End Try

    End Sub

#End Region

#Region "--- 界面控制 ---"

    Private Sub ShowLoading(ByVal isShow As Boolean)
        If isShow Then
            Me.pnlLoading.Parent = Me
            Me.pnlLoading.Dock = DockStyle.Fill
            Me.pnlLoading.Visible = True
            pnlLoading.BringToFront()
        Else
            Me.pnlLoading.Visible = False
        End If
    End Sub

    Private Sub EnableRowOperation(ByVal isEnable As Boolean)
        Me.BeginInvoke(New DoEnableRowOperationHandler(AddressOf DoEnableRowOperation), New Object() {isEnable})
    End Sub

    Private Delegate Sub DoEnableRowOperationHandler(ByVal isEnable As Boolean)
    Private Sub DoEnableRowOperation(ByVal isEnable As Boolean)
        For Each row As DataGridViewRow In Me.dgvDetail.Rows

            Dim item As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)

            Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("Status")
            If isEnable Then
                cellProgress.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Cancel Or CellButtonStyle.Document Or CellButtonStyle.Failed

            Else
                cellProgress.tbCellButtonEnable = CellButtonStyle.Cancel Or CellButtonStyle.Document Or CellButtonStyle.Failed
            End If

        Next
    End Sub

    Private Sub SetReloadEnable(ByVal isEnable As Boolean)
        Me.btnRefresh.Enabled = isEnable
    End Sub

    Private Sub ShowProgressPanel(ByVal isShow As Boolean)
        'Return
        If isShow Then
            Me.pnlProgress.Visible = True
            Me.pnlBottom.Height = Me.Height - Me.pnlBottom.Top - Me.pnlProgress.Height

        Else
            Me.pnlProgress.Visible = False
            Me.pnlBottom.Height = Me.Height - Me.pnlBottom.Top
        End If
    End Sub

    Private Sub ShowAllRows()
        Try

            For Each row As DataGridViewRow In Me.dgvDetail.Rows
                Dim args As MultiThreadDownloadItemInfo = CType(row.Tag, MultiThreadDownloadItemInfo)
                If args.Class = GetCurrentDownloadClass() Then
                    row.Visible = True
                Else
                    row.Visible = False
                End If
            Next

            Me.FilterItemsByType(Me.mCurrentView)

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.ShowAllRows")
        End Try

    End Sub

    Public Sub SwithToAndroid()
        Me.btnAndroid.Checked = True
    End Sub

    Public Sub SwitchToIphone()
        Me.btnIPhone.Checked = True
    End Sub

    Private Sub OnCheckDownload(ByVal sender As Object, ByVal args As CheckDownloadArgs)
        Dim row As tbDataGridViewRow = Me.GetRowByIdentifier(args.Identifier)
        If row Is Nothing Then
            Return
        End If

        Dim cell As tbDataGridViewProgressCellEx = row.Cells("Status")
        Select Case args.State
            Case SoftDownloadErrorState.SoftwareAgainDowmloading, SoftDownloadErrorState.SoftwareOutStock, SoftDownloadErrorState.SoftwareAgainPulls
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = CoreUpdateHelper.GetErrorDescriptionByState(args.State, False)
                cell.tbTextFail = ""
        End Select
    End Sub

    Private Function GetRowByIdentifier(ByVal appIdentifier As String) As DataGridViewRow
        Dim row As DataGridViewRow = Nothing
        Try
            For Each item As DataGridViewRow In Me.dgvDetail.Rows
                Dim downloadItem As MultiThreadDownloadItemInfo = CType(item.Tag, MultiThreadDownloadItemInfo)
                Dim packageInfo As PackageInfo = Nothing
                If Me.mDictItemToPackage.ContainsKey(downloadItem) Then
                    packageInfo = mDictItemToPackage(downloadItem)
                    If item IsNot Nothing AndAlso packageInfo.Identifier = appIdentifier Then
                        row = item
                        Exit For
                    End If
                End If

            Next
        Catch ex As Exception

        End Try
        Return row
    End Function
#End Region

#Region "--- 设备连接事件 ---"

    Public Overrides Sub OnConnect(ByVal device As IDevice)

        If device Is Nothing Then
            Return
        End If

        If TypeOf device Is iPhoneDevice Then
            Dim helper As iPhoneInstallHelper = iPhoneInstallHelper.GetInstance(device)
            RemoveHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
            AddHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
        ElseIf TypeOf device Is AndroidDevice Then
            Dim helper As AndroidInstallHelper = AndroidInstallHelper.GetInstance(device)
            RemoveHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
            AddHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
        End If

        Me.btnImport.Enabled = True
        Me.btnInstall.Enabled = True
        If Me.mCurrentView = DownloadView.Software Then
            Me.SwitchView(Me.mCurrentView)
        Else
            EnableRowOperation(True)
        End If
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        RefreshDevice()
    End Sub

    Private Delegate Sub OnConnectionChangeHandler(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
    Private Sub OnConnectionChange(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New OnConnectionChangeHandler(AddressOf OnConnectionChange), New Object() {sender, args})
        Else
            UpdateDeviceCount()
            If args.Action = DeviceAction.kDisConnected Then
                RefreshDevice()
            End If
        End If
    End Sub

    Private Sub RefreshDevice()

        If Not HasDevice() Then
            Me.btnImport.Enabled = False
            Me.btnInstall.Enabled = False
            Me.ShowProgressPanel(False)
            EnableRowOperation(False)
            Me.BeginReloadAll()
        End If
    End Sub

    Private Sub UpdateDeviceCount()

        mintDeviceCount = 0
        For Each dev As iPhoneDevice In MobileDeviceManager.Instance.ConnectedDevices
            If dev.IsConnected Then
                mintDeviceCount += 1
            End If
        Next
    End Sub

#End Region

#Region "--- 设备菜单 ---"

    Private Sub CreateDeviceMenu(ByVal listRows As List(Of DataGridViewRow), ByVal parent As Control, ByVal location As Point)
        Dim menu As New tbContextMenuStrip()
        Dim usbDevices As List(Of UsbDevice) = Me.GetDevices()
        For Each device As UsbDevice In usbDevices
            If TypeOf device.MobileDevice Is iPhoneDevice Then
                Dim iphone As iPhoneDevice = device.MobileDevice
                Dim menuItem As ToolStripItem = Nothing
                If iphone.ProductType.Contains("iPhone") Or iphone.ProductType.Contains("iPod") Then
                    menuItem = menu.Items.Add(iphone.DeviceName, My.Resources.file_icon_iphone)
                Else
                    menuItem = menu.Items.Add(iphone.DeviceName, My.Resources.file_icon_ipad)
                End If
                menuItem.ImageAlign = ContentAlignment.MiddleLeft
                menuItem.Tag = device

            ElseIf TypeOf device.MobileDevice Is AndroidDevice Then
                Dim androidDev As AndroidDevice = device.MobileDevice
                Dim menuItem As ToolStripDropDownItem = Nothing
                menuItem = menu.Items.Add(androidDev.DeviceName, My.Resources.file_icon_iphone)
                menuItem.ImageAlign = ContentAlignment.MiddleLeft
                menuItem.Tag = device

                If Me.mCurrentView = DownloadView.Ringtone Then
                    Dim tsmiRington As ToolStripItem = Nothing
                    tsmiRington = menuItem.DropDownItems.Add(Me.Language.GetString("Download.Label.Ring"))              '"来电铃声"
                    tsmiRington.ImageAlign = ContentAlignment.MiddleLeft
                    tsmiRington.Tag = ImportType.Ringtone

                    Dim tsmiAlarm As ToolStripItem = Nothing
                    tsmiAlarm = menuItem.DropDownItems.Add(Me.Language.GetString("Download.Label.Alarm"))               '"闹钟铃声"
                    tsmiAlarm.ImageAlign = ContentAlignment.MiddleLeft
                    tsmiAlarm.Tag = ImportType.Alarm

                    Dim tsmiNotification As ToolStripItem = Nothing
                    tsmiNotification = menuItem.DropDownItems.Add(Me.Language.GetString("Download.Label.Notification"))   '"通知铃声"
                    tsmiNotification.ImageAlign = ContentAlignment.MiddleLeft
                    tsmiNotification.Tag = ImportType.Notification

                    RemoveHandler menu.ItemClicked, AddressOf OnItemMenuSelect
                    AddHandler menuItem.DropDownItemClicked, AddressOf OnRingtoneMenuSelect

                End If
            End If

        Next
        menu.Tag = listRows

        AddHandler menu.ItemClicked, AddressOf OnItemMenuSelect
        AddHandler menu.Closed, AddressOf OnItemMenuClosed

        menu.Show(parent, location)
    End Sub

    Private Sub CreateDeviceRingtoneMenu(ByVal listRows As List(Of DataGridViewRow), ByVal parent As Control, ByVal location As Point)
        Dim menu As New tbContextMenuStrip()
        Dim tsmiRington As ToolStripItem = Nothing
        tsmiRington = menu.Items.Add(Me.Language.GetString("Download.Label.Ring"))              '"来电铃声"
        tsmiRington.ImageAlign = ContentAlignment.MiddleLeft
        tsmiRington.Tag = ImportType.Ringtone

        Dim tsmiAlarm As ToolStripItem = Nothing
        tsmiAlarm = menu.Items.Add(Me.Language.GetString("Download.Label.Alarm"))               '"闹钟铃声"
        tsmiAlarm.ImageAlign = ContentAlignment.MiddleLeft
        tsmiAlarm.Tag = ImportType.Alarm

        Dim tsmiNotification As ToolStripItem = Nothing
        tsmiNotification = menu.Items.Add(Me.Language.GetString("Download.Label.Notification"))         '"通知铃声"
        tsmiNotification.ImageAlign = ContentAlignment.MiddleLeft
        tsmiNotification.Tag = ImportType.Notification


        menu.Tag = listRows
        AddHandler menu.ItemClicked, AddressOf OnRingtoneMenuSelect
        AddHandler menu.Closed, AddressOf OnItemMenuClosed

        menu.Show(parent, location)
    End Sub


    Private Sub OnItemMenuSelect(ByVal sender As Object, ByVal args As ToolStripItemClickedEventArgs)
        If TypeOf args.ClickedItem Is ToolStripDropDownItem Then
            If CType(args.ClickedItem, ToolStripDropDownItem).HasDropDownItems Then
                Return
            End If
        End If

        Dim listRows As List(Of DataGridViewRow) = sender.Tag
        Dim device As UsbDevice = args.ClickedItem.Tag
        If TypeOf device.MobileDevice Is AndroidDevice Then
            Dim androidDev As AndroidDevice = device.MobileDevice
            If Not androidDev.IsConnected OrElse androidDev.Summary Is Nothing Then
                Dim mainFrm As MainForm = Me.mApplication
                mainFrm.BringToFront()
                mainFrm.SelectUSBDev(device)
                Return
            End If
        End If
        If Me.mCurrentView = DownloadView.Firmware Then
            Dim frmMain As MainForm = Me.mApplication
            frmMain.SelectDevice(device.DeviceID)
            frmMain.GotoSite(ActionFuncType.Jailbreak, "Firmware", device.DeviceID)
        ElseIf Me.mCurrentView = DownloadView.Software Then
            Me.InstallToDevice(device, listRows)
        Else
            Me.ImportItemToDevice(device, listRows)
        End If
    End Sub

    Private Sub OnRingtoneMenuSelect(ByVal sender As Object, ByVal args As ToolStripItemClickedEventArgs)
        Dim listRows As List(Of DataGridViewRow) = New List(Of DataGridViewRow)()
        Dim device As UsbDevice = Nothing

        If TypeOf sender.Tag Is List(Of DataGridViewRow) Then
            listRows = sender.Tag
            Dim tsiParent As ToolStripItem = args.ClickedItem.OwnerItem
            If tsiParent Is Nothing OrElse tsiParent.Tag Is Nothing Then
                device = GetFirstDevice()
            Else
                device = tsiParent.Tag
            End If
        Else
            device = sender.Tag
            Dim tsiParent As ToolStripItem = args.ClickedItem.OwnerItem
            If tsiParent IsNot Nothing AndAlso tsiParent.Owner IsNot Nothing AndAlso tsiParent.Owner.Tag IsNot Nothing Then
                listRows = tsiParent.Owner.Tag
            End If
        End If


        If TypeOf device.MobileDevice Is AndroidDevice Then
            Dim androidDev As AndroidDevice = device.MobileDevice
            If Not androidDev.IsConnected OrElse androidDev.Summary Is Nothing Then
                Dim mainFrm As MainForm = Me.mApplication
                mainFrm.BringToFront()
                mainFrm.SelectUSBDev(device)
                Return
            End If
        End If
        Me.ImportItemToDevice(device, listRows, args.ClickedItem.Tag)
    End Sub


    Private Sub OnItemMenuClosed(ByVal sender As Object, ByVal args As ToolStripDropDownClosedEventArgs)
        Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
        RemoveHandler menu.ItemClicked, AddressOf OnItemMenuSelect
        RemoveHandler menu.Closed, AddressOf OnItemMenuClosed
    End Sub

#End Region

#Region "--- 辅助功能 ---"

    Private Sub DeleteItemFromLocal(ByVal data As MultiThreadDownloadItemInfo)
        Dim list As List(Of MultiThreadDownloadItemInfo) = Nothing
        Select Case Me.mCurrentView
            Case DownloadView.Software
                list = Me.mListSoftware
            Case DownloadView.Music
                list = Me.mListSoftware
            Case DownloadView.Book
            Case DownloadView.Video
            Case DownloadView.Firmware
                list = Me.mListFirmware
        End Select

    End Sub

    Private Sub RemovePathFromInstalling(ByVal strPath As String)
        If String.IsNullOrEmpty(strPath) Then Exit Sub

        If Me.mListInstallingFile.Contains(strPath) Then
            mListInstallingFile.Remove(strPath)
        End If

        If mListInstallingFile.Count = 0 Then
            Me.mIsInstalling = False
        End If
    End Sub

    Private Sub CheckCanEnableRefresh()
        If Me.mCurrentView = DownloadView.Software Then
            If Me.mIsInstalling Then
                Me.btnRefresh.Enabled = False
            Else
                Me.btnRefresh.Enabled = True
            End If
        ElseIf Me.mCurrentView = DownloadView.Music Then
            If Me.mIsImportingMusic Then
                Me.btnRefresh.Enabled = False
            Else
                Me.btnRefresh.Enabled = True
            End If
        ElseIf Me.mCurrentView = DownloadView.Ringtone Then
            If Me.mbIsImportingRington Then
                Me.btnRefresh.Enabled = False
            Else
                Me.btnRefresh.Enabled = True
            End If
        End If
    End Sub

    Private Sub CheckCanEnableImportDelete()
        If Not Me.mIsInitCalled Then
            Return
        End If

        If Me.mIsRefreshing Then
            Return
        End If

        If Me.dgvDetail.SelectedRows.Count > 0 Then
            Me.btnDelete.Enabled = True
            Dim blnHasDevice As Boolean = Me.HasDevice()
            If blnHasDevice Then
                Me.btnImport.Enabled = True
                Me.btnInstall.Enabled = True
            End If

            If Me.mCurrentView = DownloadView.Firmware Then
                Me.btnImport.Enabled = False
                Me.btnInstall.Enabled = False
            End If
        Else
            Me.btnDelete.Enabled = False
            Me.btnImport.Enabled = False
            Me.btnInstall.Enabled = False

        End If
    End Sub

    Private Function HasDevice() As Boolean

        Return GetDeviceCount() > 0
    End Function

    Private Function GetDeviceCount() As Integer
        Return GetDevices().Count
    End Function

    Private Function GetAllDevices() As List(Of UsbDevice)
        Dim mainFrm As MainForm = Me.mApplication
        Dim usbDevices As List(Of UsbDevice) = New List(Of UsbDevice)()
        If Common.VerIs30() Then
            For Each dev As UsbDevice In mainFrm.UsbDevices.Values
                If TypeOf dev.MobileDevice Is iPhoneDevice Then
                    Dim device As iPhoneDevice = dev.MobileDevice
                    If device.IsConnected Then
                        usbDevices.Add(dev)
                    End If
                ElseIf TypeOf dev.MobileDevice Is AndroidDevice Then
                    Dim device As AndroidDevice = dev.MobileDevice
                    If device.IsConnected Then
                        usbDevices.Add(dev)
                    End If
                End If
            Next
        Else
            For Each dev As iPhoneDevice In MobileDeviceManager.Instance.ConnectedDevices
                If dev.IsConnected Then
                    usbDevices.Add(New UsbDevice(dev.SerialNumber, "vid_05ac", dev, DeviceType.iOS))
                    mintDeviceCount += 1
                End If
            Next

        End If
        Return usbDevices
    End Function

    Private Function GetDevices() As List(Of UsbDevice)
        Dim mainFrm As MainForm = Me.mApplication
        Dim usbDevices As List(Of UsbDevice) = New List(Of UsbDevice)()
        If Common.VerIs30() Then
            For Each dev As UsbDevice In mainFrm.UsbDevices.Values
                If Me.mCurrentView = DownloadView.Software Then
                    If TypeOf dev.MobileDevice Is iPhoneDevice Then
                        Dim device As iPhoneDevice = dev.MobileDevice
                        If device.IsConnected AndAlso Me.btnIPhone.Checked Then
                            usbDevices.Add(dev)
                        End If
                    ElseIf TypeOf dev.MobileDevice Is AndroidDevice Then
                        Dim device As AndroidDevice = dev.MobileDevice
                        If device.IsConnected AndAlso Me.btnAndroid.Checked Then
                            usbDevices.Add(dev)
                        End If
                    End If
                Else
                    If TypeOf dev.MobileDevice Is iPhoneDevice Then
                        Dim device As iPhoneDevice = dev.MobileDevice
                        If device.IsConnected Then
                            usbDevices.Add(dev)
                        End If
                    ElseIf TypeOf dev.MobileDevice Is AndroidDevice Then
                        Dim device As AndroidDevice = dev.MobileDevice
                        If device.IsConnected Then
                            usbDevices.Add(dev)
                        End If
                    End If
                End If
            Next
        Else
            For Each dev As iPhoneDevice In MobileDeviceManager.Instance.ConnectedDevices
                If dev.IsConnected Then
                    usbDevices.Add(New UsbDevice(dev.SerialNumber, "vid_05ac", dev, DeviceType.iOS))
                    mintDeviceCount += 1
                End If
            Next

        End If
        Return usbDevices
    End Function

    Private Function GetFirstDevice() As UsbDevice
        If GetDeviceCount() = 1 Then
            For Each dev As UsbDevice In GetDevices()
                Return dev
            Next
        End If
        Return Nothing
    End Function

    Private Function GetCurrentDownloadClass()
        Dim res As ResourceClass
        Select Case Me.mCurrentView
            Case DownloadView.Music
                res = ResourceClass.Music
            Case DownloadView.Ringtone
                res = ResourceClass.Ringtone
            Case DownloadView.Software
                res = ResourceClass.Software
            Case DownloadView.Firmware
                res = ResourceClass.Firmware
        End Select
        Return res
    End Function

    Private Sub GetPackageItemId(ByVal pkgInfo As PackageInfo)
        If pkgInfo Is Nothing Then
            Return
        End If

        Dim lstPackages As New List(Of PackageInfo)
        lstPackages.Add(pkgInfo)

        Me.GetPackageItemId(lstPackages)
    End Sub

    Private Sub GetPackageItemId(ByRef lstPackages As List(Of PackageInfo))
        If lstPackages Is Nothing OrElse lstPackages.Count <= 0 Then
            Return
        End If

        Dim lst As New List(Of String)
        For Each Item As PackageInfo In lstPackages
            If Item.ItemId.Length = 0 OrElse Val(Item.ItemId) = 0 Then
                '从本地资料库中取得ItemId
                Dim pkgInfo As PackageInfo = SoftManageDB.GetInstance.Get_SKU_ITEM(Item.Identifier)
                If pkgInfo IsNot Nothing AndAlso pkgInfo.ItemId.Length > 0 AndAlso Val(pkgInfo.ItemId) <> 0 Then
                    Item.ItemId = pkgInfo.ItemId
                Else
                    If Item.Identifier.Length > 0 Then
                        lst.Add(Item.Identifier)
                    End If

                End If
            End If
        Next

        '从服务器取得软件信息。

        If lst.Count > 0 Then
            Dim dic As Dictionary(Of String, FileSharingPackageInfo) = HtmlHelper.GetAppInfoFromServer(lst, "", False)
            If dic IsNot Nothing Then
                For Each Item As FileSharingPackageInfo In dic.Values
                    Dim package As PackageInfo = Me.GetPackageFromList(lstPackages, Item.Identifier)

                    If package IsNot Nothing Then
                        package.ItemId = Item.ItemId
                        package.GenreId = Item.GenreId
                        package.Genre = Item.Genre

                        SoftManageDB.GetInstance.Insert_SKU_ITEM(Item.Identifier, Item.ItemId, Item.GenreId, Item.Genre, Item.SoftwareType)
                    End If
                Next
            End If
        End If

    End Sub

    Private Function GetPackageFromList(ByVal lstPackages As List(Of PackageInfo), ByVal strIdentifier As String) As PackageInfo
        If lstPackages Is Nothing OrElse lstPackages.Count = 0 Then
            Return Nothing
        End If

        Dim result As PackageInfo = Nothing
        For Each Item As PackageInfo In lstPackages
            If String.Compare(Item.Identifier, strIdentifier, True) = 0 Then
                result = Item
                Exit For
            End If
        Next

        Return result
    End Function

    Private Function ResourceClassToDownType(ByVal resclass As ResourceClass)
        Dim targetType As DownloadView
        Select Case resclass
            Case ResourceClass.Music
                targetType = DownloadView.Music
            Case ResourceClass.Ringtone
                targetType = DownloadView.Ringtone
            Case ResourceClass.Software
                targetType = DownloadView.Software
            Case ResourceClass.Firmware
                targetType = DownloadView.Firmware
        End Select
        Return targetType
    End Function

    '展示“这里没有内容”
    Private Sub SwitchViewNoDataInfo()
        'Dim blnShow As Boolean = True
        'For Each row As DataGridViewRow In Me.dgvDetail.Rows
        '    If row.Visible Then
        '        blnShow = False
        '        Exit For
        '    End If
        'Next
        Dim count As Integer = Me.GetRowCount(Me.mCurrentView)

        If count > 0 Then
            Me.pnlFileEmpty.SendToBack()
            Me.pnlFileEmpty.Visible = False

        Else
            Me.pnlFileEmpty.BringToFront()
            Me.pnlFileEmpty.Visible = True

            If Me.mCurrentView = DownloadView.Firmware Then
                Me.btnGotoDownload.Visible = True
                Me.lblFileEmpty.Location = New Point((Me.pnlFileEmpty.Width - Me.lblFileEmpty.Width - Me.btnGotoDownload.Width - 10) / 2, Me.lblFileEmpty.Top)
                Me.btnGotoDownload.Location = New Point(Me.lblFileEmpty.Right - 3, Me.lblFileEmpty.Top)
                Me.picFileEmpty.Image = My.Resources.icon_firmwarenodata_100

            Else
                Me.btnGotoDownload.Visible = False
                Me.lblFileEmpty.Location = New Point((Me.pnlFileEmpty.Width - Me.lblFileEmpty.Width) / 2, Me.lblFileEmpty.Top)
                Select Case Me.mCurrentView
                    Case DownloadView.Software
                        Me.picFileEmpty.Image = My.Resources.icon_appnodata_100

                    Case DownloadView.Music
                        Me.picFileEmpty.Image = My.Resources.icon_musicnodata_100

                    Case DownloadView.Ringtone
                        Me.picFileEmpty.Image = My.Resources.icon_ringnodata_100


                End Select
            End If
        End If
    End Sub

#End Region

    Public Property Application() As IApplication
        Get
            Return Me.mApplication
        End Get
        Set(ByVal value As IApplication)
            Me.mApplication = value

        End Set
    End Property

    Private Sub btnImport_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImport.SizeChanged, btnDelete.SizeChanged
        Me.btnDelete.Left = Me.btnImport.Right + 1 '+ 5
        Me.btnRefresh.Left = Me.btnDelete.Right + 1 '+ 5
    End Sub

    Private Sub tsmiOpenExplorer_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiOpenExplorer.Click
        Try
            If Me.dgvDetail.SelectedRows IsNot Nothing AndAlso Me.dgvDetail.SelectedRows.Count > 0 Then
                Dim item As MultiThreadDownloadItemInfo = CType(Me.dgvDetail.SelectedRows(0).Tag, MultiThreadDownloadItemInfo)
                Common.OpenExplorer(item.DownloadPath)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub CreateDeviceMenu(ByVal parent As Control)
        Dim menu As New tbContextMenuStrip()
        Dim usbDevices As List(Of UsbDevice) = Me.GetDevices()
        For Each device As UsbDevice In usbDevices
            If TypeOf device.MobileDevice Is iPhoneDevice Then
                Dim iphone As iPhoneDevice = device.MobileDevice
                Dim menuItem As ToolStripItem = Nothing
                If iphone.ProductType.Contains("iPhone") Or iphone.ProductType.Contains("iPod") Then
                    menuItem = menu.Items.Add(iphone.DeviceName, My.Resources.file_icon_iphone)
                Else
                    menuItem = menu.Items.Add(iphone.DeviceName, My.Resources.file_icon_ipad)
                End If
                menuItem.ImageAlign = ContentAlignment.MiddleLeft
                menuItem.Tag = device
            End If
        Next

        AddHandler menu.ItemClicked, AddressOf OnItemMenuSelect
        AddHandler menu.Closed, AddressOf OnItemMenuClosed

        menu.Show(parent, New Point(0, parent.Height))
    End Sub


End Class

Public Class iPhoneImportHelper
    Private mDevice As iPhoneDevice = Nothing
    Private mAndroidDevice As AndroidDevice = Nothing
    Private mThread As Threading.Thread = Nothing
    Public mListFiles As List(Of String) = Nothing
    Public mListCancelFiles As New List(Of String)
    Private mDictRingtone As New Dictionary(Of String, String)
    Private mbUmPromp As Boolean = False
    Private mIsCancel As Boolean = False
    Private mDefaultOp As FileOperate
    Private mParent As Form
    Public mTotalFileSize As Integer
    Public mTotalFileTransfer As Integer
    Private mImportType As ImportType
    Private mApplication As IApplication = Nothing
    Private mFrmParent As Form
    Private mUsbHardwareId As String  '' 锤子手机导入音乐方式不同，需要使用usb设备的硬件ID来判别设备——Added by Utmost20150714
    Private mStatus As ImportStatus = ImportStatus.None

    Public Property Status() As ImportStatus
        Get
            Return Me.mStatus
        End Get
        Set(ByVal value As ImportStatus)
            Me.mStatus = value
        End Set
    End Property

    Public Sub New(ByVal device As UsbDevice, ByVal app As IApplication, ByVal parent As Form)

        If device.MobileDevice Is Nothing Then
            mDevice = Nothing
            mAndroidDevice = Nothing
        ElseIf TypeOf device.MobileDevice Is iPhoneDevice Then
            mDevice = device.MobileDevice
            mAndroidDevice = Nothing
        ElseIf TypeOf device.MobileDevice Is AndroidDevice Then
            mUsbHardwareId = device.HardwareId.ToLower()
            mDevice = Nothing
            mAndroidDevice = device.MobileDevice
        End If
        mApplication = app
        mFrmParent = parent

    End Sub

#If IS_ITONG Then
    Protected mIPod As IPod = Nothing

    Private Sub OnSyncEnd(ByVal sender As Object, ByVal e As EventArgs)
        Dim args As New ImportCompleteArgs()
        args.Type = Me.mImportType
        args.ListFiles = Me.mListFiles
        RaiseEvent ImportCompelete(Me, args)

        RemoveHandler mIPod.SyncEnd, AddressOf OnSyncEnd
        RemoveHandler mIPod.SyncBegin, AddressOf OnSyncBegin
        RemoveHandler mIPod.SyncProcess, AddressOf OnSyncProgress
        Me.mStatus = ImportStatus.ImportCompleted
    End Sub

    Private Sub OnSyncBegin(ByVal sender As Object, ByVal e As EventArgs)

    End Sub

    Private Sub OnSyncProgress(ByVal sender As Object, ByVal e As SyncMediaEventArgs)
        If e.Action = SyncAction.UpdateDatabase Then


        ElseIf e.Action = SyncAction.Transfer Then

            Dim strFileName As String = e.Current.FilePathOnPC
            If Me.mDictRingtone.ContainsKey(e.Current.FilePathOnPC) Then
                strFileName = mDictRingtone(e.Current.FilePathOnPC)
            End If

            Dim args As New ImportProgressArgs(Me.mDevice, ImportType.Music, strFileName, e.TranSize, e.Current.FileSize.ByteCount)
            If e.TranSize = e.Current.FileSize.ByteCount Then
                args.ProgressStyle = ProgressStyle.Button
                args.ProgressString = Me.mApplication.Language.GetString("Media.Message.Finished") '"完成"
            Else
                args.ProgressStyle = ProgressStyle.Progress
                args.ProgressString = Me.mApplication.Language.GetString("File.Label.Uploading")  '"正在上传"
            End If

            RaiseEvent ImportProgress(Me, args)
        ElseIf e.Action = SyncAction.Completed Then
            If e.Current IsNot Nothing Then
                Dim strFileName As String = e.Current.FilePathOnPC
                If Me.mDictRingtone.ContainsKey(e.Current.FilePathOnPC) Then
                    strFileName = mDictRingtone(e.Current.FilePathOnPC)
                End If
                Dim args As New ImportProgressArgs(Me.mDevice, ImportType.Music, strFileName, e.Current.FileSize.ByteCount, e.Current.FileSize.ByteCount)
                args.ProgressStyle = ProgressStyle.Button
                args.ProgressString = Me.mApplication.Language.GetString("Media.Message.Finished") ' "完成"
                RaiseEvent ImportProgress(Me, args)
            End If
        End If
        Me.mStatus = ImportStatus.Importing
    End Sub

    Public Sub StartImport(ByVal type As ImportType, ByVal listpath As List(Of String), ByVal mainForm As Form)
        mListFiles = listpath
        mParent = mainForm
        mImportType = type

        Me.mTotalFileSize = 0
        Me.mTotalFileTransfer = 0

        ' 计算传输的文件总大小
        For Each fileItem As String In mListFiles
            Try
                Dim fileInfo As New FileInfo(fileItem)
                mTotalFileSize += fileInfo.Length
            Catch ex As Exception
                Common.LogException(ex.ToString, "AndroidImportHelper.StartImport")
            End Try
        Next

        Dim strPanda As String
        If Utility.IsPanda() Then
            strPanda = "/panda"
        Else
            strPanda = "/tongbu"
        End If

        If mAndroidDevice IsNot Nothing Then
            strPanda = mAndroidDevice.SDPath.TrimEnd("/") + strPanda
        End If

        Select Case type
            Case ImportType.Music
                If mDevice IsNot Nothing Then
                    mThread = New Threading.Thread(New System.Threading.ParameterizedThreadStart(AddressOf DoImportMusic))
                    mThread.IsBackground = True
                    mThread.Start(False)
                ElseIf mAndroidDevice IsNot Nothing Then
                    Dim task As New ImportMusicTask(mAndroidDevice, listpath, strPanda + "/music/", mFrmParent, AndroidMediaTypes.Music, Me.mApplication)
                    AddHandler task.ImportProgress, AddressOf OnImportProgress
                    AddHandler task.ImportCompelete, AddressOf OnImportComplete

                    task.Tag = mAndroidDevice
                    task.RunTaskAsync(mUsbHardwareId)
                End If
            Case ImportType.Ringtone
                If mDevice IsNot Nothing Then
                    mThread = New Threading.Thread(New System.Threading.ParameterizedThreadStart(AddressOf DoImportMusic))
                    mThread.IsBackground = True
                    mThread.Start(True)
                ElseIf mAndroidDevice IsNot Nothing Then
                    Dim task As New ImportMusicTask(mAndroidDevice, listpath, strPanda + "/ringtone/", mFrmParent, AndroidMediaTypes.Ringtone, Me.mApplication)
                    AddHandler task.ImportProgress, AddressOf OnImportProgress
                    AddHandler task.ImportCompelete, AddressOf OnImportComplete

                    task.Tag = mAndroidDevice
                    task.RunTaskAsync(mUsbHardwareId)
                End If
            Case ImportType.Notification
                Dim task As New ImportMusicTask(mAndroidDevice, listpath, strPanda + "/ringtone/", mFrmParent, AndroidMediaTypes.NotifyRing, Me.mApplication)
                AddHandler task.ImportProgress, AddressOf OnImportProgress
                AddHandler task.ImportCompelete, AddressOf OnImportComplete

                task.Tag = mAndroidDevice
                task.RunTaskAsync(mUsbHardwareId)
            Case ImportType.Alarm
                Dim task As New ImportMusicTask(mAndroidDevice, listpath, strPanda + "/ringtone/", mFrmParent, AndroidMediaTypes.AlarmRing, Me.mApplication)
                AddHandler task.ImportProgress, AddressOf OnImportProgress
                AddHandler task.ImportCompelete, AddressOf OnImportComplete

                task.Tag = mAndroidDevice
                task.RunTaskAsync(mUsbHardwareId)
        End Select
    End Sub

    Private Function AddVideo(ByVal file As String, ByVal type As MediaType) As NewTrack
        Dim newTrack As New NewTrack()

        newTrack.FilePath = file
        newTrack.MediaType = type

        If type = MediaType.Video Then
            newTrack.IsVideo = True
        Else
            newTrack.IsVideo = False
        End If

#If IS_ITONG Then
        Try
            Dim info As Taglib.File = Taglib.File.Create(file)
            newTrack.Title = info.OwnerTag.Title
            newTrack.Album = info.OwnerTag.Album
            newTrack.Artist = info.OwnerTag.FirstPerformer
            newTrack.AlbumArtist = info.OwnerTag.FirstAlbumArtist
            newTrack.Composer = info.OwnerTag.FirstComposer
            newTrack.Comments = info.OwnerTag.Comment
            newTrack.Genre = info.OwnerTag.FirstGenre

            newTrack.TrackNumber = Val(info.OwnerTag.Track)
            newTrack.DiscNumber = Val(info.OwnerTag.Disc)
            newTrack.TotalDiscCount = Val(info.OwnerTag.DiscCount)
            newTrack.AlbumTrackCount = Val(info.OwnerTag.TrackCount)

            newTrack.HasLyrics = (Not String.IsNullOrEmpty(info.OwnerTag.Lyrics))

            newTrack.Bitrate = info.Properties.AudioBitrate
            newTrack.SampleRate = info.Properties.AudioSampleRate
            newTrack.Length = info.Properties.Duration.TotalMilliseconds

            If info.OwnerTag.Pictures.Length > 0 Then
                Try
                    Dim objValue As Object = info.OwnerTag.Pictures.GetValue(0)
                    If TypeOf objValue Is Taglib.Picture Then
                        Dim pic As Taglib.Picture = objValue
                        Dim coverFile As String = Path.Combine(Folder.TempFolder, Guid.NewGuid().ToString("N") & ".jpg")
                        Dim msImage As New MemoryStream(pic.Data.Datas)
                        Dim imgCover As Image = Image.FromStream(msImage)

                        imgCover.Save(coverFile, ImageFormat.Jpeg)
                        imgCover.Dispose()
                        msImage.Close()

                        newTrack.ArtworkFile = coverFile
                    ElseIf TypeOf objValue Is Taglib.Id3v2.Frames.AttachedPictureFrame Then
                        Dim pic As Taglib.Id3v2.Frames.AttachedPictureFrame = objValue
                        Dim coverFile As String = Path.Combine(Folder.TempFolder, Guid.NewGuid().ToString("N") & ".jpg")
                        Dim msImage As New MemoryStream(pic.Data.Datas)
                        Dim imgCover As Image = Image.FromStream(msImage)

                        imgCover.Save(coverFile, ImageFormat.Jpeg)
                        imgCover.Dispose()
                        msImage.Close()

                        newTrack.ArtworkFile = coverFile
                    End If
                Catch ex As Exception
                End Try
            End If


        Catch ex As Exception
            'Me.GetMediaInfo(newTrack)
        Finally
            ' Me.GetVideoInfo(newTrack)
        End Try
#End If

        If String.IsNullOrEmpty(newTrack.Title) Then
            newTrack.Title = System.IO.Path.GetFileNameWithoutExtension(file)
        End If
        newTrack.IsDownCenter = True
        Return newTrack
    End Function

    Private Function AddAudio(ByVal file As String) As NewTrack
        Dim newTrack As New NewTrack()

        Try
            Dim info As New Mp3Info(file, True)

            newTrack.FilePath = info.FilePath
            newTrack.Title = info.Title
            newTrack.Album = info.Album
            newTrack.Artist = info.Artist
            newTrack.AlbumArtist = info.AlbumArtist
            newTrack.Composer = info.Composer
            newTrack.Comments = info.Comment
            newTrack.Genre = info.Genre

            newTrack.Year = Val(info.Year)
            newTrack.TrackNumber = Val(info.TrackNumber)
            newTrack.DiscNumber = Val(info.DiskNumber)
            newTrack.TotalDiscCount = Val(info.DiskCount)
            newTrack.AlbumTrackCount = Val(info.TrackCount)

            newTrack.Bitrate = info.BitRate \ 1000
            newTrack.SampleRate = info.Sampling
            newTrack.Length = info.Duration * 1000

            newTrack.IsVideo = False
            newTrack.HasLyrics = info.HasLyrics
            newTrack.MediaType = MediaType.Audio

            If String.IsNullOrEmpty(newTrack.Title) Then
                newTrack.Title = System.IO.Path.GetFileNameWithoutExtension(info.FilePath)
            End If

            If info.AlbumImage IsNot Nothing AndAlso info.AlbumImage.Count > 0 Then
                Dim coverFile As String = Path.Combine(Folder.TempFolder, Guid.NewGuid().ToString("N") & ".jpg")
                info.AlbumImage.Pictures(0).Value.Save(coverFile, System.Drawing.Imaging.ImageFormat.Jpeg)
                newTrack.ArtworkFile = coverFile
            End If

            info.Dispose()
        Catch ex As Exception
        End Try
        newTrack.IsDownCenter = True
        Return newTrack
    End Function

    Private Function CreateRingtone(ByVal mp3File As String) As String
        Dim strReturn As String = String.Empty
        Dim master As New MediaTask(Me.mDevice.Identifier)
        Dim strName As String = Path.GetFileNameWithoutExtension(mp3File)

        AddHandler master.ConvertBegin, AddressOf ConvertBegin
        AddHandler master.ConvertEnd, AddressOf ConvertEnd
        AddHandler master.ConvertProgress, AddressOf ConvertProgress

        strReturn = frmMediaBase.DoCreateRingtone(mp3File, master, strName)

        RemoveHandler master.ConvertBegin, AddressOf ConvertBegin
        RemoveHandler master.ConvertEnd, AddressOf ConvertEnd
        RemoveHandler master.ConvertProgress, AddressOf ConvertProgress

        Return strReturn

    End Function

    Public Sub ConvertBegin(ByVal sender As Object, ByVal e As ConvertProgressArgs)

    End Sub

    Public Sub ConvertEnd(ByVal sender As Object, ByVal e As ConvertProgressArgs)
        Dim args As New ImportProgressArgs(Me.mDevice, ImportType.Ringtone, e.FilePath, 100, 100)
        args.ProgressString = Me.mApplication.Language.GetString("Download.Message.WaitUpload") '"等待导入"
        args.ProgressStyle = ProgressStyle.Button
        RaiseEvent ImportProgress(Me, args)
    End Sub

    Public Sub ConvertProgress(ByVal sender As Object, ByVal e As ConvertProgressArgs)
        Dim args As New ImportProgressArgs(Me.mDevice, ImportType.Ringtone, e.FilePath, e.ProgressValue, 100)
        args.ProgressString = Me.mApplication.Language.GetString("Download.Message.Conversing") '"正在转换"
        args.ProgressStyle = ProgressStyle.Progress
        RaiseEvent ImportProgress(Me, args)

    End Sub

    Private Function CheckMusicExist(ByVal name As String, ByVal type As MediaType) As Boolean

        Dim isExist As Boolean = False

        For Each trackItem As Track In Me.mIPod.Tracks.Items
            If trackItem.Title = name AndAlso trackItem.MediaType = type Then
                isExist = True
                Exit For
            End If
        Next

        Return isExist
    End Function

    Private Sub DoImportMusic(ByVal isRingtone As Object)
        Try
            Me.mStatus = ImportStatus.ImportStart
            mIPod = IPod.Instance(Me.mDevice, False)
            AddHandler mIPod.SyncBegin, AddressOf OnSyncBegin
            AddHandler mIPod.SyncEnd, AddressOf OnSyncEnd
            AddHandler mIPod.SyncProcess, AddressOf OnSyncProgress

            Dim isUpdate As Boolean = False
            Dim importType As ImportType = IIf(isRingtone, importType.Ringtone, importType.Music)
            Dim mediaType As MediaType = IIf(isRingtone, mediaType.Ringtone, mediaType.Audio)

            For Each fileName As String In Me.mListFiles
                If Not System.IO.File.Exists(fileName) Then
                    Continue For
                End If
                Dim strExt As String = Path.GetExtension(fileName)
                Dim track As Track = Nothing
                Dim newTrack As NewTrack = Nothing
                Dim fileInfo As New FileInfo(fileName)
                Dim newMusicInfo As Mp3Info = New Mp3Info(fileName)

                Dim strTitle As String = newMusicInfo.Title

                If fileInfo Is Nothing Then
                    Continue For
                End If

                If Not CheckMusicExist(strTitle, mediaType) Then
                    isUpdate = True



                    If isRingtone Then

                        If strExt = ".mp3" Or strExt = ".wav" Then


                            Dim strNewName As String = Me.CreateRingtone(fileName)

                            Dim args As New ImportProgressArgs(Me.mDevice, importType.Ringtone, fileName, 0, 100)
                            args.ProgressString = Me.mApplication.Language.GetString("Download.Message.WaitConvert") '"等待导入"
                            args.ProgressStyle = ProgressStyle.Button

                            mDictRingtone(strNewName) = fileName
                            fileName = strNewName
                        End If
                        newTrack = frmMediaBase.AddVideo(fileName, mediaType.Ringtone, Nothing, True)
                    Else
                        newTrack = frmMediaBase.AddAudio(fileName, True)
                    End If

                    Me.mIPod.CheckInSync()
                    Dim itunesDbTrack As Track = Me.mIPod.Tracks.Add(newTrack)
                    Try
                        If itunesDbTrack IsNot Nothing AndAlso itunesDbTrack.IsModify AndAlso Not Me.mListCancelFiles.Contains(itunesDbTrack.FilePathOnPC) Then
                            Me.mListCancelFiles.Add(itunesDbTrack.FilePathOnPC)
                        End If
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "frmDownloaded.DoImportMusic")
                    End Try


                Else
                    Dim strNewname As String = fileName
                    If Me.mDictRingtone.ContainsKey(fileName) Then
                        strNewname = Me.mDictRingtone(fileName)
                    End If
                    Dim args As New ImportProgressArgs(Me.mDevice, importType, strNewname, 0, 0)
                    args.ProgressStyle = ProgressStyle.Button
                    args.ProgressString = Me.mApplication.Language.GetString("Download.Message.FileExist") '"已存在"
                    RaiseEvent ImportProgress(Me, args)
                End If


            Next
            If isUpdate Then
                Me.mIPod.Sync()
            Else
                Dim completeargs As New ImportCompleteArgs()
                completeargs.Type = Me.mImportType
                completeargs.ListFiles = Me.mListFiles
                RaiseEvent ImportCompelete(Me, completeargs)
                Me.mStatus = ImportStatus.ImportCompleted
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.DoImportMusic")
        End Try
    End Sub
    Public Event ImportProgress(ByVal sender As Object, ByVal args As ImportProgressArgs)
    Public Event ImportCompelete(ByVal sender As Object, ByVal args As ImportCompleteArgs)

    Protected Sub DataTransferProgress(ByVal e As DataTransferEventArgs)
        mTotalFileTransfer += e.LastTransSize
        Dim args As New ImportProgressArgs(Me.mDevice, ImportType.Music, e.FileOnPC, e.TransSize, e.FileSize)
        args.TotalFileSize = Me.mTotalFileSize
        args.TotalFileTransfer = Me.mTotalFileTransfer
        RaiseEvent ImportProgress(Me, args)
    End Sub

    Private Sub OnImportProgress(ByVal sender As Object, ByVal e As ImportProgressArgs)
        RaiseEvent ImportProgress(Me, e)

    End Sub

    Private Sub OnImportComplete(ByVal sender As Object, ByVal e As ImportCompleteArgs)
        RaiseEvent ImportCompelete(Me, e)

    End Sub

#End If

End Class

Public Enum DownloadView
    Software
    Music
    Ringtone
    Video
    Book
    Paper
    Firmware
End Enum

Public Class DownloadItemComparer
    Inherits System.Collections.Generic.Comparer(Of MultiThreadDownloadItemInfo)

    Public Overrides Function Compare(ByVal x As MultiThreadDownloadItemInfo, ByVal y As MultiThreadDownloadItemInfo) As Integer

        Return y.CompletedTime.CompareTo(x.CompletedTime) 'item1.CompletedTime.ToBinary.CompareTo(item2.CompletedTime.ToBinary)
    End Function
End Class

Public Enum ImportStatus
    None
    ImportStart
    Importing
    ImportCompleted
End Enum