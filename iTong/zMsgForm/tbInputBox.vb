﻿Imports System.Collections

Public Class tbInputBox
    Inherits tbBaseGuiForm

    'MessageBox参数
    Private mMsgText As String = ""
    Private mButtons As MessageBoxButtons = MessageBoxButtons.OK
    Private mIcon As MessageBoxIcon = MessageBoxIcon.None
    Private mDefaultButton As MessageBoxDefaultButton = MessageBoxDefaultButton.Button1

    Private mStrTutorial As String = ""
    Private mStrTutorialUrl As String = ""

    Private mStrErrorMsg As String = ""

    '边距和区域大小 
    Private mRectIcon As Rectangle = Rectangle.Empty
    Private mRectPic As Rectangle = Rectangle.Empty
    Private mRectMessage As Rectangle = Rectangle.Empty
    Private mSizeIcon As New Size(35, 35)
    Private mSizeBtn As New Size(75, 25)
    Private mSizeFormMin As New Size(320, 120)           '窗体最小Size
    Private mMsgIcon As Image = Nothing
    Private mTextBox As tbTextBox = Nothing
    Private mListBtn As New List(Of tbButton)
    Private mPicBottom As New PictureBox

    Private mlklblTutorial As LinkLabel
    Private mlblErrorMsg As Label

    Private mTextFont As Font = Nothing
    Private mToolTip As ToolTip
    Private mIsPwdMode As Boolean = False

    Public ReadOnly Property InputText() As String
        Get
            Return Me.mTextBox.Text
        End Get
    End Property

    Private Sub InitializeComponent()
        Me.TopMost = False
        Me.ShowInTaskbar = True
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.Icon = My.Resources.iTong
        Me.BackColor = Color.FromArgb(156, 156, 156)
        'Me.SizeGripStyle = Windows.Forms.SizeGripStyle.Hide


        Me.tbShowWatermark = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,6,6,6"
        Me.tbGuiBackground = My.Resources.pnl_bg_info
        Me.tbTitleBackColor = System.Drawing.Color.White
        Me.tbTitleFont = Common.CreateFont("Arial", 9.0!, FontStyle.Regular)
        Me.tbTitleForeColor = System.Drawing.Color.Black
        Me.FilletRadius = 5


        Dim btnClose As New tbButton()
        With btnClose
            .Name = "btn_close"
            .tbBackgroundImage = My.Resources.Resources.btn_close1
            .tbBackgroundImageState = ImageState.ThreeState
            .tbAutoSize = True
            .tbSplit = "3,3,3,3"
        End With

        Me.Controls.Add(btnClose)

        Me.mTextFont = Common.CreateFont("Arial", 9, FontStyle.Regular)
    End Sub

    Public Sub New(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton)
        Me.New([text], caption, buttons, icon, defaultButton, String.Empty, Nothing)
    End Sub

    Public Sub New(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String)
        Me.New([text], caption, buttons, icon, defaultButton, strInputText, Nothing)
    End Sub

    Public Sub New(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal arrButtonText() As String)
        Me.New([text], caption, buttons, icon, defaultButton, strInputText, arrButtonText, False, Nothing, "", "", "")
    End Sub

    Public Sub New(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal arrButtonText() As String, ByVal strTutorial As String, ByVal strTutorialUrl As String)
        Me.New([text], caption, buttons, icon, defaultButton, strInputText, arrButtonText, False, Nothing, strTutorial, strTutorialUrl, "")
    End Sub

    Public Sub New(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal arrButtonText() As String, ByVal isPwdMode As Boolean, ByVal imgInMessageBootom As Image, ByVal strTutorial As String, ByVal strTutorialUrl As String, ByVal strErrorMsg As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Text = caption
        Me.mMsgText = [text]
        Me.mButtons = buttons
        Me.mIcon = icon ' MessageBoxIcon.None
        Me.mDefaultButton = defaultButton
        Me.mIsPwdMode = isPwdMode

        Me.mStrTutorial = strTutorial
        Me.mStrTutorialUrl = strTutorialUrl
        Me.mStrErrorMsg = strErrorMsg

        If Me.Text.Length = 0 Then
            Me.Text = Me.Language.GetString("Common.Info")
        End If

        If imgInMessageBootom IsNot Nothing Then
            Me.mPicBottom.Image = imgInMessageBootom.Clone
            Me.mPicBottom.Size = imgInMessageBootom.Size
            Me.mPicBottom.BackColor = Color.Transparent
        End If

        'If Me.mIcon = MessageBoxIcon.None Then
        '    Me.mIcon = MessageBoxIcon.Information
        'End If
        'Me.mIcon = MessageBoxIcon.Information

        Me.InitButtons(arrButtonText)
        Me.InitFormSize()

        Me.mMsgIcon = Me.GetMsgIcon()
    End Sub

    Private Function GetMsgIcon() As Image
        Dim imgIcon As Image = Nothing
        Select Case Me.mIcon
            Case MessageBoxIcon.None
                imgIcon = Nothing
            Case MessageBoxIcon.Asterisk
                imgIcon = My.Resources.icon_ok
            Case MessageBoxIcon.Error
                imgIcon = My.Resources.icon_error
            Case MessageBoxIcon.Exclamation
                imgIcon = My.Resources.icon_warning1
            Case MessageBoxIcon.Hand
                imgIcon = My.Resources.icon_ok
            Case MessageBoxIcon.Information
                imgIcon = My.Resources.icon_warning
            Case MessageBoxIcon.Question
                imgIcon = My.Resources.icon_question
            Case MessageBoxIcon.Stop
                imgIcon = My.Resources.icon_error
            Case MessageBoxIcon.Warning
                imgIcon = My.Resources.icon_warning

        End Select
        Return imgIcon
    End Function

    Private Function CreateButton(ByVal strText As String, ByVal foreColor As Color, ByVal bgImage As Image, ByVal imgState As ImageState, ByVal type As DialogResult) As tbButton
        Dim btn As New tbButton
        With btn
            .tbText = strText
            .tbTextColor = foreColor
            .Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
            .tbBackgroundImage = bgImage
            .tbBackgroundImageState = imgState
            .Tag = type
            .tbSplit = "13, 11, 13, 11"
            .Size = Me.mSizeBtn

            AddHandler .Click, AddressOf btn_Click
        End With

        Me.mListBtn.Add(btn)
        Me.Controls.Add(btn)

        Return btn
    End Function

    Private Sub txt_KeyDown(ByVal sender As Object, ByVal e As KeyEventArgs)
        If e.KeyCode = Keys.Enter AndAlso CType(sender, tbTextBox).Text.Trim().Length > 0 Then
            Me.DialogResult = Windows.Forms.DialogResult.OK
            Me.Close()
        End If
    End Sub

    Private Sub btn_Click(ByVal sender As Object, ByVal e As System.EventArgs)
        If Me.mIsPwdMode AndAlso String.IsNullOrEmpty(Me.mTextBox.Text) Then
            tbMessageBox.Show(Me, Me.Language.GetString("WeiBo.Message.TypeInPassword"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None)
            Return
        End If
        Dim btn As tbButton = sender
        If btn IsNot Nothing AndAlso btn.Tag IsNot Nothing Then
            Me.DialogResult = btn.Tag
            Me.Close()
        End If
    End Sub

    Private Sub InitButtons(ByVal arrText() As String)

        '设置button位置
        Select Case Me.mButtons
            Case MessageBoxButtons.AbortRetryIgnore
                Me.CreateButton(Me.Language.GetString("Common.Abort"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.Abort)
                Me.CreateButton(Me.Language.GetString("Common.Retry"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.Retry)
                Me.CreateButton(Me.Language.GetString("Common.Ignore"), Color.Black, My.Resources.btn_4_white, ImageState.FourState, Windows.Forms.DialogResult.Ignore)

            Case MessageBoxButtons.OK
                Me.CreateButton(Me.Language.GetString("Common.OK"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.OK)


            Case MessageBoxButtons.OKCancel
                Me.CreateButton(Me.Language.GetString("Common.OK"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.OK)
                Me.CreateButton(Me.Language.GetString("Common.Cancel"), Color.Black, My.Resources.btn_4_white, ImageState.FourState, Windows.Forms.DialogResult.Cancel)

            Case MessageBoxButtons.RetryCancel
                Me.CreateButton(Me.Language.GetString("Common.Retry"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.Retry)
                Me.CreateButton(Me.Language.GetString("Common.Cancel"), Color.Black, My.Resources.btn_4_white, ImageState.FourState, Windows.Forms.DialogResult.Cancel)

            Case MessageBoxButtons.YesNo
                Me.CreateButton(Me.Language.GetString("Common.Yes"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.Yes)
                Me.CreateButton(Me.Language.GetString("Common.No"), Color.Black, My.Resources.btn_4_white, ImageState.FourState, Windows.Forms.DialogResult.No)

            Case MessageBoxButtons.YesNoCancel
                Me.CreateButton(Me.Language.GetString("Common.Yes"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.Yes)
                Me.CreateButton(Me.Language.GetString("Common.No"), Color.White, My.Resources.btn_4_blue, ImageState.FourState, Windows.Forms.DialogResult.No)
                Me.CreateButton(Me.Language.GetString("Common.Cancel"), Color.Black, My.Resources.btn_4_white, ImageState.FourState, Windows.Forms.DialogResult.Cancel)

        End Select

        If arrText IsNot Nothing AndAlso arrText.Length > 0 Then
            For index As Integer = 0 To Me.mListBtn.Count - 1
                If index >= arrText.Length Then
                    Exit For
                End If

                Me.mListBtn(index).tbText = arrText(index)
                Me.mListBtn(index).tbAutoSizeEx = True
            Next
        End If
    End Sub

    Private Sub InitFormSize()
        Dim g As Graphics = Me.CreateGraphics()

        Dim intWidth As Integer = 0
        Dim intHeight As Integer = 0

        Dim msgSize As SizeF = g.MeasureString(Me.mMsgText, Me.mTextFont, 800)
        Dim intTextWidth As Integer = Math.Ceiling(msgSize.Width)
        Dim intTextHeight As Integer = Math.Ceiling(msgSize.Height)
        Dim intIconDiffX As Integer = 10
        Dim intIconDiffY As Integer = 10

        g.Dispose()

        Me.Padding = New System.Windows.Forms.Padding(35, 35, 35, 25)

        '窗体上显示的图片
        If Me.mPicBottom.Image IsNot Nothing Then
            Me.mRectPic = New Rectangle(Me.Padding.Top, Me.Padding.Bottom + intIconDiffY, Me.mPicBottom.Width, Me.mPicBottom.Height)
            Me.Controls.Add(Me.mPicBottom)
            intHeight = Me.mRectPic.Height + intIconDiffY
            If intTextWidth < Me.mRectPic.Width Then
                intTextWidth = Me.mRectPic.Width
            End If
        End If

        '窗体上的icon
        If Me.mIcon = MessageBoxIcon.None Then
            intWidth += Me.Padding.Horizontal + intTextWidth
            intHeight += Me.Padding.Vertical + intTextHeight + (Me.mSizeBtn.Height + intIconDiffY)
        Else
            If Me.mSizeBtn.Height > intTextHeight Then
                intTextHeight = Me.mSizeBtn.Height
            End If

            intWidth += Me.Padding.Horizontal + intTextWidth + (Me.mSizeIcon.Width + intIconDiffX)
            intHeight += Me.Padding.Vertical + intTextHeight + (Me.mSizeBtn.Height + intIconDiffY)
        End If

        Dim intWidthBtn As Integer = Me.Padding.Horizontal + Me.mListBtn.Count * Me.mSizeBtn.Width + (Me.mListBtn.Count - 1) * 5

        If intWidthBtn > intWidth Then
            intTextWidth += (intWidthBtn - intWidth)
            intWidth = intWidthBtn
        End If

        If Me.mSizeFormMin.Width > intWidth Then
            intTextWidth += (Me.mSizeFormMin.Width - intWidth)
            intWidth = Me.mSizeFormMin.Width
        End If

        If Me.mSizeFormMin.Height > intHeight Then
            intTextHeight += (Me.mSizeFormMin.Height - intHeight)
            intHeight = Me.mSizeFormMin.Height
        End If

        Dim intLeft As Integer = Me.Padding.Left
        Dim intTop As Integer = Me.Padding.Top

        If Me.mPicBottom.Image IsNot Nothing Then
            intTop += Me.mPicBottom.Height + intIconDiffY
        End If

        If Me.mIcon <> MessageBoxIcon.None Then
            Me.mRectIcon = New Rectangle(intLeft, intTop + (intTextHeight - Me.mSizeIcon.Height) \ 2, Me.mSizeIcon.Width, Me.mSizeIcon.Height)
            Me.mRectMessage = New Rectangle(Me.mRectIcon.Right + intIconDiffX, intTop, intTextWidth, intTextHeight)
        Else
            '让字显示在窗体中间
            Me.mRectMessage = New Rectangle(intLeft, intTop, intWidth - Me.Padding.Horizontal, intTextHeight)
        End If

        Me.mTextBox = New tbTextBox()
        Me.Controls.Add(Me.mTextBox)

        With mTextBox
            .Width = Me.mRectMessage.Width
            .Anchor = AnchorStyles.None 'AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right 2015-07-02 by chenbihai 修改输入框 超过窗体
            .BackColor = Color.WhiteSmoke
            If Me.mIsPwdMode Then
                .PasswordChar = "*"
            End If

            intHeight += .Height + 6

            AddHandler .KeyDown, AddressOf txt_KeyDown
        End With

        Me.mlklblTutorial = New LinkLabel()
        RemoveHandler Me.mlklblTutorial.Click, AddressOf lklblTutorial_Click
        AddHandler Me.mlklblTutorial.Click, AddressOf lklblTutorial_Click
        With Me.mlklblTutorial
            .ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
            .Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
            .BackColor = System.Drawing.Color.Transparent
            .Cursor = System.Windows.Forms.Cursors.Hand
            .ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
            .LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
            .LinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
            .Name = "lklblTutorial"
            .AutoSize = True
            .TextAlign = System.Drawing.ContentAlignment.TopLeft
            .VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
            .Visible = True
            .Text = Me.mStrTutorial
        End With
        Me.Controls.Add(Me.mlklblTutorial)

        Me.mlblErrorMsg = New Label()
        With Me.mlblErrorMsg
            .ForeColor = Color.Red
            .Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
            .BackColor = System.Drawing.Color.Transparent
            .Cursor = System.Windows.Forms.Cursors.Hand
            .Name = "lblErrorMsg"
            .AutoSize = True
            .TextAlign = System.Drawing.ContentAlignment.TopLeft
            .Visible = True
            .Text = Me.mStrErrorMsg
        End With
        Me.Controls.Add(Me.mlblErrorMsg)

        Me.Size = New Size(intWidth, intHeight)
    End Sub

    Private Sub lklblTutorial_Click(sender As Object, e As EventArgs)
        If Me.mStrTutorialUrl.Length > 0 Then
            Common.OpenExplorer(Me.mStrTutorialUrl)
            Me.Close()
        End If
    End Sub

    Private Sub InitLocation()
        Dim intY As Integer = Me.Height - 20
        Dim intRight As Integer = Me.Width - 35
        Dim intDiff As Integer = 5

        For intI As Integer = Me.mListBtn.Count - 1 To 0 Step -1
            Dim btn As tbButton = Me.mListBtn(intI)

            btn.Location = New Point(intRight - btn.Width, intY - btn.Height)

            intRight = intRight - btn.Width - intDiff
        Next
        If Me.mPicBottom.Image IsNot Nothing Then
            Me.mPicBottom.Location = New Point(Me.mRectPic.Left, Me.mRectPic.Top)
        End If

        If Me.mTextBox IsNot Nothing Then
            Me.mTextBox.Location = New Point(Me.mRectMessage.Left, Me.mRectMessage.Bottom + 3)
        End If

        If Me.mlklblTutorial IsNot Nothing Then
            Me.mlklblTutorial.Location = New Point(Me.mTextBox.Left, Me.mTextBox.Bottom + 3)
        End If

        If Me.mlblErrorMsg IsNot Nothing Then
            Me.mlblErrorMsg.Location = New Point(Me.mTextBox.Left, intY - Me.mlblErrorMsg.Height - 10)
        End If
    End Sub

    Protected Overrides Sub OnFontChanged(ByVal e As System.EventArgs)
        MyBase.OnFontChanged(e)

        Me.InitFormSize()
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        MyBase.OnResize(e)

        Me.InitLocation()
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

        Dim index As Integer = 0

        '设置默认选中的button
        Select Case Me.mDefaultButton
            Case MessageBoxDefaultButton.Button1
                index = 0

            Case MessageBoxDefaultButton.Button2
                index = 1

            Case MessageBoxDefaultButton.Button1
                index = 2

        End Select

        Me.mTextBox.Focus()
    End Sub

    Protected Overrides Sub OnPaint(ByVal pe As System.Windows.Forms.PaintEventArgs)
        MyBase.OnPaint(pe)
        '在此处添加自定义绘制代码
        Dim g As Graphics = pe.Graphics

        If Not String.IsNullOrEmpty(Me.mMsgText) Then
            GuiHelper.DrawString(g, Me.mMsgText, Me.mTextFont, Me.mRectMessage, Color.Black, ContentAlignment.MiddleLeft)
        End If

        If Me.mMsgIcon IsNot Nothing AndAlso Me.mIcon <> MessageBoxIcon.None Then
            g.DrawImage(Me.mMsgIcon, Me.mRectIcon, New Rectangle(0, 0, Me.mMsgIcon.Width, Me.mMsgIcon.Height), GraphicsUnit.Pixel)
        End If
    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)
        '当AbortRetryIgnore｜YesNo　的时候，如果用户关闭窗体就把窗体的默认返回值　设置为默认值

        If Me.DialogResult = Windows.Forms.DialogResult.Cancel Then
            If Me.mButtons = MessageBoxButtons.AbortRetryIgnore Then
                Me.DialogResult = Windows.Forms.DialogResult.Ignore
            ElseIf Me.mButtons = MessageBoxButtons.YesNo Then
                Me.DialogResult = Windows.Forms.DialogResult.No
            End If
        End If

    End Sub

#Region "－－系统MessageBox默认的方法－－"

    '---CheckBox---------------------------------
    Public Overloads Shared Function Show(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, Optional ByVal isPwdMode As Boolean = False) As DialogResult
        Return tbInputBox.ShowCore(Nothing, [text], caption, buttons, icon, defaultButton, strInputText, Nothing, isPwdMode, Nothing, "", "", "")
    End Function

    Public Overloads Shared Function Show(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, Optional ByVal isPwdMode As Boolean = False, Optional ByVal strErrorMsg As String = "") As DialogResult
        Return tbInputBox.ShowCore(Nothing, [text], caption, buttons, icon, defaultButton, strInputText, Nothing, isPwdMode, Nothing, "", "", strErrorMsg)
    End Function

    Public Overloads Shared Function Show(ByVal owner As Object, ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal isPwdMode As Boolean, ByVal imgInMessageBootom As Image) As DialogResult
        Return tbInputBox.ShowCore(owner, [text], caption, buttons, icon, defaultButton, strInputText, Nothing, isPwdMode, imgInMessageBootom, "", "", "")
    End Function

    Public Overloads Shared Function Show(ByVal owner As Object, ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal isPwdMode As Boolean, ByVal imgInMessageBootom As Image, ByVal strTutorial As String, ByVal strTutorialUrl As String) As DialogResult
        Return tbInputBox.ShowCore(owner, [text], caption, buttons, icon, defaultButton, strInputText, Nothing, isPwdMode, imgInMessageBootom, strTutorial, strTutorialUrl, "")
    End Function

    Public Overloads Shared Function Show(ByVal owner As Object, ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal isPwdMode As Boolean, ByVal imgInMessageBootom As Image, ByVal strTutorial As String, ByVal strTutorialUrl As String, ByVal strErrorMsg As String) As DialogResult
        Return tbInputBox.ShowCore(owner, [text], caption, buttons, icon, defaultButton, strInputText, Nothing, isPwdMode, imgInMessageBootom, strTutorial, strTutorialUrl, strErrorMsg)
    End Function

    Public Overloads Shared Function Show(ByVal owner As Object, ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, Optional ByVal isPwdMode As Boolean = False) As DialogResult
        Return tbInputBox.ShowCore(owner, [text], caption, buttons, icon, defaultButton, strInputText, Nothing, isPwdMode, Nothing, "", "", "")
    End Function


    '---Button Texts---------------------------------
    Public Overloads Shared Function Show(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal isPwdMode As Boolean, ByVal imgInMessageBootom As Image) As DialogResult
        Return tbInputBox.ShowCore(Nothing, [text], caption, buttons, icon, defaultButton, strInputText, Nothing, isPwdMode, imgInMessageBootom, "", "", "")
    End Function

    Public Overloads Shared Function Show(ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal arrButtonText() As String, Optional ByVal isPwdMode As Boolean = False) As DialogResult
        Return tbInputBox.ShowCore(Nothing, [text], caption, buttons, icon, defaultButton, strInputText, arrButtonText, isPwdMode, Nothing, "", "", "")
    End Function

    Public Overloads Shared Function Show(ByVal owner As Object, ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal arrButtonText() As String, Optional ByVal isPwdMode As Boolean = False) As DialogResult
        Return tbInputBox.ShowCore(owner, [text], caption, buttons, icon, defaultButton, strInputText, arrButtonText, isPwdMode, Nothing, "", "", "")
    End Function
    '------------------------------------------------------------

    Private Shared Function ShowCore(ByVal owner As Object, ByVal [text] As String, ByVal caption As String, ByVal buttons As MessageBoxButtons, ByVal icon As MessageBoxIcon, ByVal defaultButton As MessageBoxDefaultButton, ByRef strInputText As String, ByVal arrButtonText() As String, ByVal isPwdMode As Boolean, ByVal imgInMessageBootom As Image, ByVal strTutorial As String, ByVal strTutorialUrl As String, ByVal strErrorMsg As String) As DialogResult
        Dim result As DialogResult
        Dim frm As New tbInputBox([text], caption, buttons, icon, defaultButton, strInputText, arrButtonText, isPwdMode, imgInMessageBootom, strTutorial, strTutorialUrl, strErrorMsg)

        If owner Is Nothing Then
            For Each frmOpen As Form In Application.OpenForms
                If frmOpen.Name = "MainForm" AndAlso frmOpen.Visible Then
                    owner = frmOpen
                    Exit For
                End If
            Next
        End If

        If owner IsNot Nothing AndAlso TypeOf owner Is Form AndAlso Not CType(owner, Form).InvokeRequired AndAlso CType(owner, Form).WindowState <> FormWindowState.Minimized Then
            frm.Tag = owner
            frm.StartPosition = FormStartPosition.CenterParent
        Else
            frm.StartPosition = FormStartPosition.CenterScreen
        End If

        For Each buttonItem As tbButton In frm.mListBtn
            buttonItem.tbBackgroundImage = My.Resources.btn_4_blue
            buttonItem.tbTextColor = Color.White
        Next

        If frm.mListBtn.Count > 1 Then
            frm.mListBtn(frm.mListBtn.Count - 1).tbBackgroundImage = My.Resources.btn_4_white
            frm.mListBtn(frm.mListBtn.Count - 1).tbTextColor = Color.Black
        End If


        result = ShowForm(frm, strInputText)

        Return result
    End Function

    Private Delegate Function ShowFormHandler(ByVal frm As tbInputBox, ByRef strInputText As String) As DialogResult
    Private Shared Function ShowForm(ByVal frm As tbInputBox, ByRef strInputText As String) As DialogResult
        If frm.InvokeRequired Then
            frm.Invoke(New ShowFormHandler(AddressOf ShowForm), frm, strInputText)
        Else
            Dim result As DialogResult = Windows.Forms.DialogResult.None
            Try
                If frm.Tag IsNot Nothing AndAlso TypeOf frm.Tag Is Form Then
                    result = frm.ShowDialog(frm.Tag)
                Else
                    result = frm.ShowDialog()
                End If

                strInputText = frm.InputText
            Catch ex As Exception
                Common.LogException("Messagebox error:" & ex.ToString())
            End Try
            Return result
        End If
    End Function

#End Region

End Class
