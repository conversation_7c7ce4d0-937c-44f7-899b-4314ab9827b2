﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmFileExist
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmFileExist))
        Me.btnCancel = New iTong.Components.tbButton
        Me.btnSkip = New iTong.Components.tbButton
        Me.pnlMain = New iTong.Components.tbPanel
        Me.lblPath = New iTong.Components.tbLabel
        Me.btnCopyNew = New iTong.Components.tbButton
        Me.btnNoCopy = New iTong.Components.tbButton
        Me.btnCopyAndReplace = New iTong.Components.tbButton
        Me.Label2 = New System.Windows.Forms.Label
        Me.Label1 = New System.Windows.Forms.Label
        Me.btn_close = New iTong.Components.tbButton
        Me.cbxUnPrompt = New iTong.Components.tbCheckBox
        Me.pnlMain.SuspendLayout()
        Me.SuspendLayout()
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.Location = New System.Drawing.Point(394, 382)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(77, 25)
        Me.btnCancel.TabIndex = 6
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'btnSkip
        '
        Me.btnSkip.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSkip.BackColor = System.Drawing.Color.Transparent
        Me.btnSkip.BindingForm = Nothing
        Me.btnSkip.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSkip.Location = New System.Drawing.Point(302, 382)
        Me.btnSkip.Name = "btnSkip"
        Me.btnSkip.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSkip.Selectable = True
        Me.btnSkip.Size = New System.Drawing.Size(77, 25)
        Me.btnSkip.TabIndex = 5
        Me.btnSkip.tbAdriftIconWhenHover = False
        Me.btnSkip.tbAutoSize = False
        Me.btnSkip.tbAutoSizeEx = False
        Me.btnSkip.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnSkip.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSkip.tbBadgeNumber = 0
        Me.btnSkip.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSkip.tbEndEllipsis = False
        Me.btnSkip.tbIconHoldPlace = True
        Me.btnSkip.tbIconImage = Nothing
        Me.btnSkip.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSkip.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSkip.tbIconMore = False
        Me.btnSkip.tbIconMouseDown = Nothing
        Me.btnSkip.tbIconMouseHover = Nothing
        Me.btnSkip.tbIconMouseLeave = Nothing
        Me.btnSkip.tbIconPlaceText = 2
        Me.btnSkip.tbIconReadOnly = Nothing
        Me.btnSkip.tbImageMouseDown = Nothing
        Me.btnSkip.tbImageMouseHover = Nothing
        Me.btnSkip.tbImageMouseLeave = Nothing
        Me.btnSkip.tbReadOnly = False
        Me.btnSkip.tbReadOnlyText = False
        Me.btnSkip.tbShadow = False
        Me.btnSkip.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnSkip.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnSkip.tbShowDot = False
        Me.btnSkip.tbShowNew = False
        Me.btnSkip.tbShowToolTipOnButton = False
        Me.btnSkip.tbSplit = "13,11,13,11"
        Me.btnSkip.tbText = "跳过"
        Me.btnSkip.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSkip.tbTextColor = System.Drawing.Color.Black
        Me.btnSkip.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnSkip.tbTextColorDown = System.Drawing.Color.Black
        Me.btnSkip.tbTextColorHover = System.Drawing.Color.Black
        Me.btnSkip.tbTextMouseDownPlace = 0
        Me.btnSkip.tbToolTip = ""
        Me.btnSkip.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSkip.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSkip.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSkip.VisibleEx = True
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.Transparent
        Me.pnlMain.Controls.Add(Me.lblPath)
        Me.pnlMain.Controls.Add(Me.btnCopyNew)
        Me.pnlMain.Controls.Add(Me.btnNoCopy)
        Me.pnlMain.Controls.Add(Me.btnCopyAndReplace)
        Me.pnlMain.Controls.Add(Me.Label2)
        Me.pnlMain.Controls.Add(Me.Label1)
        Me.pnlMain.Location = New System.Drawing.Point(1, 30)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(481, 345)
        Me.pnlMain.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlMain.TabIndex = 4
        Me.pnlMain.tbBackgroundImage = Nothing
        Me.pnlMain.tbShowWatermark = False
        Me.pnlMain.tbSplit = "8,22,8,9"
        Me.pnlMain.tbWatermark = Nothing
        Me.pnlMain.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMain.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblPath
        '
        Me.lblPath.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblPath.AutoEllipsis = True
        Me.lblPath.Location = New System.Drawing.Point(41, 313)
        Me.lblPath.Name = "lblPath"
        Me.lblPath.Size = New System.Drawing.Size(402, 19)
        Me.lblPath.TabIndex = 8
        Me.lblPath.tbAdriftWhenHover = False
        Me.lblPath.tbAutoEllipsis = True
        Me.lblPath.tbAutoSize = False
        Me.lblPath.tbHideImage = False
        Me.lblPath.tbIconImage = Nothing
        Me.lblPath.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblPath.tbIconPlaceText = 5
        Me.lblPath.tbShadow = False
        Me.lblPath.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblPath.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblPath.tbShowScrolling = False
        Me.lblPath.Text = "Message"
        Me.lblPath.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnCopyNew
        '
        Me.btnCopyNew.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCopyNew.BackColor = System.Drawing.Color.Transparent
        Me.btnCopyNew.BindingForm = Nothing
        Me.btnCopyNew.Font = New System.Drawing.Font("宋体", 15.0!)
        Me.btnCopyNew.Location = New System.Drawing.Point(44, 234)
        Me.btnCopyNew.Name = "btnCopyNew"
        Me.btnCopyNew.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCopyNew.Selectable = True
        Me.btnCopyNew.Size = New System.Drawing.Size(420, 65)
        Me.btnCopyNew.TabIndex = 7
        Me.btnCopyNew.tbAdriftIconWhenHover = False
        Me.btnCopyNew.tbAutoSize = False
        Me.btnCopyNew.tbAutoSizeEx = False
        Me.btnCopyNew.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_file_copy
        Me.btnCopyNew.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnCopyNew.tbBadgeNumber = 0
        Me.btnCopyNew.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopyNew.tbEndEllipsis = False
        Me.btnCopyNew.tbIconHoldPlace = True
        Me.btnCopyNew.tbIconImage = Global.iTong.My.Resources.Resources.btn_file_arrow
        Me.btnCopyNew.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyNew.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCopyNew.tbIconMore = False
        Me.btnCopyNew.tbIconMouseDown = Nothing
        Me.btnCopyNew.tbIconMouseHover = Nothing
        Me.btnCopyNew.tbIconMouseLeave = Global.iTong.My.Resources.Resources.btn_file_arrow
        Me.btnCopyNew.tbIconPlaceText = 2
        Me.btnCopyNew.tbIconReadOnly = Nothing
        Me.btnCopyNew.tbImageMouseDown = Nothing
        Me.btnCopyNew.tbImageMouseHover = Nothing
        Me.btnCopyNew.tbImageMouseLeave = Nothing
        Me.btnCopyNew.tbReadOnly = False
        Me.btnCopyNew.tbReadOnlyText = False
        Me.btnCopyNew.tbShadow = False
        Me.btnCopyNew.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopyNew.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopyNew.tbShowDot = False
        Me.btnCopyNew.tbShowNew = False
        Me.btnCopyNew.tbShowToolTipOnButton = True
        Me.btnCopyNew.tbSplit = "2,2,2,2"
        Me.btnCopyNew.tbText = "复制，但保留这两个文件"
        Me.btnCopyNew.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyNew.tbTextColor = System.Drawing.Color.MidnightBlue
        Me.btnCopyNew.tbTextColorDisable = System.Drawing.Color.MidnightBlue
        Me.btnCopyNew.tbTextColorDown = System.Drawing.Color.MidnightBlue
        Me.btnCopyNew.tbTextColorHover = System.Drawing.Color.MidnightBlue
        Me.btnCopyNew.tbTextMouseDownPlace = 0
        Me.btnCopyNew.tbToolTip = "将不会更改任何文件。将此文件保留在目标文件夹中"
        Me.btnCopyNew.tbToolTipColor = System.Drawing.Color.MidnightBlue
        Me.btnCopyNew.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopyNew.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyNew.VisibleEx = True
        '
        'btnNoCopy
        '
        Me.btnNoCopy.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnNoCopy.BackColor = System.Drawing.Color.Transparent
        Me.btnNoCopy.BindingForm = Nothing
        Me.btnNoCopy.Font = New System.Drawing.Font("宋体", 15.0!)
        Me.btnNoCopy.Location = New System.Drawing.Point(44, 144)
        Me.btnNoCopy.Name = "btnNoCopy"
        Me.btnNoCopy.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnNoCopy.Selectable = True
        Me.btnNoCopy.Size = New System.Drawing.Size(420, 65)
        Me.btnNoCopy.TabIndex = 5
        Me.btnNoCopy.tbAdriftIconWhenHover = False
        Me.btnNoCopy.tbAutoSize = False
        Me.btnNoCopy.tbAutoSizeEx = False
        Me.btnNoCopy.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_file_copy
        Me.btnNoCopy.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnNoCopy.tbBadgeNumber = 0
        Me.btnNoCopy.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnNoCopy.tbEndEllipsis = False
        Me.btnNoCopy.tbIconHoldPlace = True
        Me.btnNoCopy.tbIconImage = Global.iTong.My.Resources.Resources.btn_file_arrow
        Me.btnNoCopy.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnNoCopy.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnNoCopy.tbIconMore = False
        Me.btnNoCopy.tbIconMouseDown = Nothing
        Me.btnNoCopy.tbIconMouseHover = Nothing
        Me.btnNoCopy.tbIconMouseLeave = Global.iTong.My.Resources.Resources.btn_file_arrow
        Me.btnNoCopy.tbIconPlaceText = 2
        Me.btnNoCopy.tbIconReadOnly = Nothing
        Me.btnNoCopy.tbImageMouseDown = Nothing
        Me.btnNoCopy.tbImageMouseHover = Nothing
        Me.btnNoCopy.tbImageMouseLeave = Nothing
        Me.btnNoCopy.tbReadOnly = False
        Me.btnNoCopy.tbReadOnlyText = False
        Me.btnNoCopy.tbShadow = False
        Me.btnNoCopy.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnNoCopy.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnNoCopy.tbShowDot = False
        Me.btnNoCopy.tbShowNew = False
        Me.btnNoCopy.tbShowToolTipOnButton = True
        Me.btnNoCopy.tbSplit = "2,2,2,2"
        Me.btnNoCopy.tbText = "不要复制"
        Me.btnNoCopy.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnNoCopy.tbTextColor = System.Drawing.Color.MidnightBlue
        Me.btnNoCopy.tbTextColorDisable = System.Drawing.Color.MidnightBlue
        Me.btnNoCopy.tbTextColorDown = System.Drawing.Color.MidnightBlue
        Me.btnNoCopy.tbTextColorHover = System.Drawing.Color.MidnightBlue
        Me.btnNoCopy.tbTextMouseDownPlace = 0
        Me.btnNoCopy.tbToolTip = "将不会更改任何文件。将此文件保留在目标文件夹中"
        Me.btnNoCopy.tbToolTipColor = System.Drawing.Color.MidnightBlue
        Me.btnNoCopy.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnNoCopy.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnNoCopy.VisibleEx = True
        '
        'btnCopyAndReplace
        '
        Me.btnCopyAndReplace.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCopyAndReplace.BackColor = System.Drawing.Color.Transparent
        Me.btnCopyAndReplace.BindingForm = Nothing
        Me.btnCopyAndReplace.Font = New System.Drawing.Font("宋体", 15.0!)
        Me.btnCopyAndReplace.ForeColor = System.Drawing.SystemColors.MenuHighlight
        Me.btnCopyAndReplace.Location = New System.Drawing.Point(44, 54)
        Me.btnCopyAndReplace.Name = "btnCopyAndReplace"
        Me.btnCopyAndReplace.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCopyAndReplace.Selectable = True
        Me.btnCopyAndReplace.Size = New System.Drawing.Size(420, 65)
        Me.btnCopyAndReplace.TabIndex = 3
        Me.btnCopyAndReplace.tbAdriftIconWhenHover = False
        Me.btnCopyAndReplace.tbAutoSize = False
        Me.btnCopyAndReplace.tbAutoSizeEx = False
        Me.btnCopyAndReplace.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_file_copy
        Me.btnCopyAndReplace.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnCopyAndReplace.tbBadgeNumber = 0
        Me.btnCopyAndReplace.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopyAndReplace.tbEndEllipsis = False
        Me.btnCopyAndReplace.tbIconHoldPlace = True
        Me.btnCopyAndReplace.tbIconImage = Global.iTong.My.Resources.Resources.btn_file_arrow
        Me.btnCopyAndReplace.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyAndReplace.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCopyAndReplace.tbIconMore = False
        Me.btnCopyAndReplace.tbIconMouseDown = Nothing
        Me.btnCopyAndReplace.tbIconMouseHover = Nothing
        Me.btnCopyAndReplace.tbIconMouseLeave = Global.iTong.My.Resources.Resources.btn_file_arrow
        Me.btnCopyAndReplace.tbIconPlaceText = 2
        Me.btnCopyAndReplace.tbIconReadOnly = Nothing
        Me.btnCopyAndReplace.tbImageMouseDown = Nothing
        Me.btnCopyAndReplace.tbImageMouseHover = Nothing
        Me.btnCopyAndReplace.tbImageMouseLeave = Nothing
        Me.btnCopyAndReplace.tbReadOnly = False
        Me.btnCopyAndReplace.tbReadOnlyText = False
        Me.btnCopyAndReplace.tbShadow = False
        Me.btnCopyAndReplace.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopyAndReplace.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopyAndReplace.tbShowDot = False
        Me.btnCopyAndReplace.tbShowNew = False
        Me.btnCopyAndReplace.tbShowToolTipOnButton = True
        Me.btnCopyAndReplace.tbSplit = "2,2,2,2"
        Me.btnCopyAndReplace.tbText = "复制和替换"
        Me.btnCopyAndReplace.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyAndReplace.tbTextColor = System.Drawing.Color.MidnightBlue
        Me.btnCopyAndReplace.tbTextColorDisable = System.Drawing.Color.MidnightBlue
        Me.btnCopyAndReplace.tbTextColorDown = System.Drawing.Color.MidnightBlue
        Me.btnCopyAndReplace.tbTextColorHover = System.Drawing.Color.MidnightBlue
        Me.btnCopyAndReplace.tbTextMouseDownPlace = 0
        Me.btnCopyAndReplace.tbToolTip = "使用正在复制的文件替换目标文件夹中的文件"
        Me.btnCopyAndReplace.tbToolTipColor = System.Drawing.Color.MidnightBlue
        Me.btnCopyAndReplace.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopyAndReplace.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopyAndReplace.VisibleEx = True
        '
        'Label2
        '
        Me.Label2.AutoSize = True
        Me.Label2.Location = New System.Drawing.Point(23, 27)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(113, 12)
        Me.Label2.TabIndex = 2
        Me.Label2.Tag = ""
        Me.Label2.Text = "请单击要保留的文件"
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(72, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(101, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(22, 8)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(212, 16)
        Me.Label1.TabIndex = 1
        Me.Label1.Text = "此位置已经包含同名文件。"
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(459, 0)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 7
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close1
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'cbxUnPrompt
        '
        Me.cbxUnPrompt.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.cbxUnPrompt.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxUnPrompt.BackColor = System.Drawing.Color.Transparent
        Me.cbxUnPrompt.ForeColor = System.Drawing.Color.Black
        Me.cbxUnPrompt.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.cbxUnPrompt.Location = New System.Drawing.Point(12, 381)
        Me.cbxUnPrompt.Name = "cbxUnPrompt"
        Me.cbxUnPrompt.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxUnPrompt.Size = New System.Drawing.Size(212, 27)
        Me.cbxUnPrompt.TabIndex = 16
        Me.cbxUnPrompt.tbAdriftIconWhenHover = False
        Me.cbxUnPrompt.tbAutoSize = False
        Me.cbxUnPrompt.tbAutoSizeEx = True
        Me.cbxUnPrompt.tbIconChecked = CType(resources.GetObject("cbxUnPrompt.tbIconChecked"), System.Drawing.Image)
        Me.cbxUnPrompt.tbIconCheckedMouseDown = Nothing
        Me.cbxUnPrompt.tbIconCheckedMouseHover = Nothing
        Me.cbxUnPrompt.tbIconCheckedMouseLeave = Nothing
        Me.cbxUnPrompt.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxUnPrompt.tbIconHoldPlace = True
        Me.cbxUnPrompt.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxUnPrompt.tbIconIndeterminate = CType(resources.GetObject("cbxUnPrompt.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxUnPrompt.tbIconIndeterminateMouseDown = Nothing
        Me.cbxUnPrompt.tbIconIndeterminateMouseHover = Nothing
        Me.cbxUnPrompt.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxUnPrompt.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxUnPrompt.tbIconPlaceText = 1
        Me.cbxUnPrompt.tbIconUnChecked = CType(resources.GetObject("cbxUnPrompt.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxUnPrompt.tbIconUnCheckedMouseDown = Nothing
        Me.cbxUnPrompt.tbIconUnCheckedMouseHover = Nothing
        Me.cbxUnPrompt.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxUnPrompt.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxUnPrompt.tbImageBackground = Nothing
        Me.cbxUnPrompt.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxUnPrompt.tbImageCheckedMouseDown = Nothing
        Me.cbxUnPrompt.tbImageCheckedMouseHover = Nothing
        Me.cbxUnPrompt.tbImageCheckedMouseLeave = Nothing
        Me.cbxUnPrompt.tbImageUnCheckedMouseDown = Nothing
        Me.cbxUnPrompt.tbImageUnCheckedMouseHover = Nothing
        Me.cbxUnPrompt.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxUnPrompt.tbReadOnly = False
        Me.cbxUnPrompt.tbShadow = False
        Me.cbxUnPrompt.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxUnPrompt.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxUnPrompt.tbSplit = "3,3,3,3"
        Me.cbxUnPrompt.tbToolTip = ""
        Me.cbxUnPrompt.Text = "为本次任务的所有文件执行此操作"
        Me.cbxUnPrompt.UseVisualStyleBackColor = False
        '
        'frmFileExist
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(483, 419)
        Me.Controls.Add(Me.cbxUnPrompt)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnSkip)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.btn_close)
        Me.MinimumSize = New System.Drawing.Size(12, 12)
        Me.Name = "frmFileExist"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.pnl_bg_info
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,6,6,6"
        Me.tbTitleBackColor = System.Drawing.Color.White
        Me.tbTitleForeColor = System.Drawing.Color.Black
        Me.Text = "提示"
        Me.pnlMain.ResumeLayout(False)
        Me.pnlMain.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents btnCancel As tbButton
    Friend WithEvents btnSkip As tbButton
    Friend WithEvents pnlMain As tbPanel
    Friend WithEvents btn_close As tbButton
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents btnCopyAndReplace As tbButton
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents btnCopyNew As tbButton
    Friend WithEvents btnNoCopy As tbButton
    Friend WithEvents lblPath As tbLabel
    Friend WithEvents cbxUnPrompt As tbCheckBox
End Class
