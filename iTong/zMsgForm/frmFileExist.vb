﻿Public Class frmFileExist


    Private _fileOperate As FileOperate = FileOperate.Cancel
    Private _blnUnPrompt As Boolean = False
    Private _application As IApplication

    Public Sub New(ByVal app As IApplication)

        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me._application = app
        Me.Language = Me._application.Language
        Me.FilletRadius = 5
        Me.Icon = My.Resources.iTong
    End Sub

    Public Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        Me.FilletRadius = 5
        Me.Icon = My.Resources.iTong
        Me.Language = LanguageInterface.Instance(System.Globalization.CultureInfo.CurrentCulture)
        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub


    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Common.Info")                                                      '"提示"
        Me.Label1.Text = Me.Language.GetString("MessageForm.Label.IncludeSameName")                         '"此位置已经包含同名文件。"
        Me.Label2.Text = Me.Language.GetString("MessageForm.Label.ClickFile")                               '"请单击要保留的文件"
        Me.btnCopyAndReplace.tbText = Me.Language.GetString("MessageForm.Button.CopyAndReplace")            '"复制和替换"
        Me.btnCopyAndReplace.tbToolTip = Me.Language.GetString("MessageForm.Button.CopyAndReplaceToolTip")  '"使用正在复制的文件替换目标文件夹中的文件"
        Me.btnNoCopy.tbText = Me.Language.GetString("MessageForm.Button.Uncopy")                            '"不要复制"
        Me.btnNoCopy.tbToolTip = Me.Language.GetString("MessageForm.Button.CopyNewToolTip")                 '"将不会更改任何文件。将此文件保留在目标文件夹中"
        Me.btnCopyNew.tbText = Me.Language.GetString("MessageForm.Button.CopyNew")                          '"复制，但保留这两个文件"
        Me.btnCopyNew.tbToolTip = Me.Language.GetString("MessageForm.Button.CopyNewToolTip")                '"将不会更改任何文件。将此文件保留在目标文件夹中"
        Me.cbxUnPrompt.Text = Me.Language.GetString("MessageForm.CheckBox.ExecuteSameOperation")            '"为本次任务的所有文件执行此操作"
        Me.btnSkip.tbText = Me.Language.GetString("MessageForm.Button.Jump")                                '"跳过"
        Me.btnCancel.tbText = Me.Language.GetString("Common.Cancel")                                        '"取消"

    End Sub

    Public Property Path() As String
        Get
            Return lblPath.Text
        End Get
        Set(ByVal value As String)
            lblPath.Text = Me.Language.GetString("Components.ImageView.Label.Location") & Me.Language.GetString("Common.Symbol.Colon") & value '"路径："
        End Set
    End Property

    Public Property Operate() As FileOperate
        Get
            Return _fileOperate
        End Get
        Set(ByVal value As FileOperate)
            _fileOperate = value
        End Set
    End Property

    Public Property UnPrompt() As Boolean
        Get
            Return _blnUnPrompt
        End Get
        Set(ByVal value As Boolean)
            _blnUnPrompt = value
        End Set
    End Property

    Private Sub btnCopyAndReplace_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopyAndReplace.Click
        Me._fileOperate = FileOperate.CopyAndReplace
        _blnUnPrompt = cbxUnPrompt.Checked
        Me.Close()
    End Sub

    Private Sub btnNoCopy_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNoCopy.Click
        Me._fileOperate = FileOperate.Skip
        _blnUnPrompt = cbxUnPrompt.Checked
        Me.Close()
    End Sub

    Private Sub btnCopyNew_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopyNew.Click
        Me._fileOperate = FileOperate.CopyNew
        _blnUnPrompt = cbxUnPrompt.Checked
        Me.Close()
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        _fileOperate = FileOperate.Cancel
        _blnUnPrompt = cbxUnPrompt.Checked
        Me.Close()
    End Sub

    Private Sub btnSkip_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSkip.Click
        _blnUnPrompt = cbxUnPrompt.Checked
        _fileOperate = FileOperate.Skip
        Me.Close()
    End Sub

End Class

Public Enum FileOperate
    None = 0
    CopyAndReplace = 1
    CopyNew = 3
    Skip = 4
    Cancel = 5
End Enum