﻿Imports System.Runtime.InteropServices

Public Class tbSplashBox

    Private mIsModal As Boolean = False
    Private mLocation As Point = Point.Empty


    Private Sub Init(ByVal message As String, ByVal splashTime As Integer, ByVal isOk As Boolean)
        Me.ShowInTaskbar = False
        Me.PNGForm.ShowInTaskbar = False
        Me.mIsModal = isOk

        Me.Text = ""
        Me.lblMessage.Text = message
        Me.lblMessage.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.tmrClose.Interval = splashTime * 1000
        Me.tmrClose.Start()
        Me.Width = lblMessage.Width + 20
        lblMessage.Left = 10
    End Sub

    Private Sub New(ByVal message As String, ByVal pos As Point, Optional ByVal splashTime As Integer = 3, Optional ByVal isOk As Boolean = True)
        Me.InitializeComponent()

        Me.mLocation = pos
        Me.Init(message, splashTime, isOk)
    End Sub

    Private Sub New(ByVal message As String, ByVal parentdlg As Form, Optional ByVal splashTime As Integer = 3, Optional ByVal isOk As Boolean = True)
        'Dim lwindowlong As Int32 = GetWindowLong(Me.Handle, GWL_EXSTYLE)
        'lwindowlong = lwindowlong And (Not WS_EX_APPWINDOW)
        'lwindowlong = lwindowlong Or WS_EX_TOOLWINDOW
        'Dim res As Int32 = SetWindowLong(Me.Handle, GWL_EXSTYLE, lwindowlong)

        Me.InitializeComponent()

        Me.Owner = parentdlg
        Me.Init(message, splashTime, isOk)
    End Sub

    Private Sub onParentMouseclice(ByVal sender As Object, ByVal param As MouseEventArgs)
        If Me.IsHandleCreated Then
            Me.Close()
        End If
    End Sub

    Private Sub onParentActivate(ByVal sender As Object, ByVal e As EventArgs)
        If Me.IsHandleCreated Then
            Me.Close()
        End If
    End Sub

    Private Sub ShowMessageDialog()
        If Me.mIsModal Then
            If Me.Owner IsNot Nothing Then
                Me.StartPosition = FormStartPosition.CenterParent
                ShowDialog(Me.Owner)
            Else
                Me.StartPosition = FormStartPosition.CenterScreen
                ShowDialog()
            End If

        Else
            NativeMethods.ShowWindow(Me.Handle, SW_CMD.SW_SHOWNOACTIVATE)
            If Me.Owner IsNot Nothing Then
                Me.StartPosition = FormStartPosition.CenterParent
                Me.CenterToParent()

                AddHandler Me.Owner.MouseClick, AddressOf onParentMouseclice
                AddHandler Me.Owner.Activated, AddressOf onParentActivate

            Else
                Me.StartPosition = FormStartPosition.Manual
                Me.Location = Me.mLocation
            End If

            'Me.Show()
            'Me.TopMost = True
            'Dim loc As Point = Me.Owner.Location
            'loc.X = loc.X + (Me.Owner.Width - Me.Width) / 2
            'loc.Y = loc.Y + (Me.Owner.Height - Me.Height) / 2
            'Me.Location = loc
        End If

    End Sub

    Public Shared Sub ShowMessage(ByVal message As String, ByVal pos As Point, Optional ByVal splashTime As Integer = 1, Optional ByVal isOk As Boolean = False)
        Dim obj As New tbSplashBox(message, pos, splashTime, isOk)
        obj.ShowMessageDialog()
    End Sub


    Public Shared Sub ShowMessage(ByVal message As String, ByVal parentdlg As Form, Optional ByVal splashTime As Integer = 1, Optional ByVal isOk As Boolean = False)
        Dim obj As New tbSplashBox(message, parentdlg, splashTime, isOk)
        obj.ShowMessageDialog()
    End Sub


    Private Sub tmrClose_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrClose.Tick
        Me.tmrClose.Stop()
        Me.Close()
    End Sub

    Private Sub tbSplashMessageBox_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        'Me.ShowInTaskbar = False
    End Sub
End Class