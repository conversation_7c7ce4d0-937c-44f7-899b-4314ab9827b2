﻿Public Class LiveUpdateHelper

    Public Shared Sub StartLiveUpdateExe(ByVal zipFile As String, _
                                        ByVal upgradeFolder As String, _
                                        ByVal softPath As String, _
                                        ByVal softVersion As String)
        Try
            Dim blnNewIncrease As Boolean = ServerIniSetting.GetIsZSNewIncrease()
            '增量更新时没有app.zip的压缩包
            If blnNewIncrease = False Then
                If Utility.unzip(zipFile, upgradeFolder) <= 0 Then
                    If File.Exists(zipFile) Then
                        File.Delete(zipFile)
                    End If

                    Return
                End If
            End If

            Dim strLiveUpdate As String = ""
            For Each Item As String In System.IO.Directory.GetFiles(upgradeFolder, "*.exe", System.IO.SearchOption.TopDirectoryOnly)
                If System.IO.Path.GetFileName(Item).ToLower = "liveupdate.exe" Then
                    strLiveUpdate = Item

                    Exit For
                End If
            Next

            Dim strAppZip As String = System.IO.Path.Combine(upgradeFolder, "app.zip")

            '防止路径中有空格，所以需要加双引号

            Dim str As String = """" & softPath & """ " & _
                                """" & strAppZip & """ " & _
                                """" & softVersion & """"
            If blnNewIncrease Then
                str = """" & softPath & """ " & _
                      """" & upgradeFolder & """ " & _
                      """" & softVersion & """ " & _
                      """" & "0" & """ " & _
                      """" & "1" & """ " & _
                      """" & "1" & """"
            End If
            Common.Log(strLiveUpdate & " " & str)
            If strLiveUpdate.Length > 0 AndAlso System.IO.File.Exists(strLiveUpdate) Then
                System.Diagnostics.Process.Start(strLiveUpdate, str)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "StartLiveUpdateExe")
        End Try
    End Sub

#Region "--- 检测升级 ---"

    Private Shared _FolderUpgrade As String = ""
    Private Shared _strUpdatePackageFile As String = ""
    Private Shared _FirstRunFromUpgrade As Boolean = False     '是否是升级后第一次运行

    Public Shared Sub CheckUpgradFile()
        _strUpdatePackageFile = Path.Combine(Folder.AppFolder, "Upgrade.dat")
        _FolderUpgrade = Path.Combine(Path.GetTempPath(), "tbUpgrade")
#If IS_WECHAT Then
        If Folder.AppType = RunType.iDataRecovery Then
            _FolderUpgrade = Path.Combine(Path.GetTempPath(), "iDataRecoveryUpgrade")
        Else
            _FolderUpgrade = Path.Combine(Path.GetTempPath(), "wechatMasterUpgrade")
        End If
#End If
        If Folder.AppType = RunType.iWeChatMigration Then
            _FolderUpgrade = Path.Combine(Path.GetTempPath(), "iWeChatMigrationUpgrade")
        ElseIf Folder.AppType = RunType.iDataRecovery OrElse Folder.AppType = RunType.iDataRecovery_Abroad Then
            _FolderUpgrade = Path.Combine(Path.GetTempPath(), "iDataRecoveryUpgrade")
        End If
    End Sub

    Public Shared Sub CheckUpgradeFileExist()
        Dim strPathUpgradeSuceed As String = Path.Combine(Folder.AppFolder, "UpgradeSuceed.dll")

        CheckUpgradFile()

        Dim blnNewIncrease As Boolean = ServerIniSetting.GetIsZSNewIncrease()
        If File.Exists(_strUpdatePackageFile) Then

            '助手升级增量更新时，不需要删除tbUpgrade目录
            If blnNewIncrease = False Then
                Try
                    '删除临时目录中的tbUpgrade目录,以免以前的升级程序失败后会遗留文件
                    If Directory.Exists(_FolderUpgrade) Then
                        Directory.Delete(_FolderUpgrade, True)
                    End If
                Catch
                End Try
            End If


            If File.Exists(strPathUpgradeSuceed) Then

                _FirstRunFromUpgrade = True

                '升级成功后清理升级产生的临时文件
                Try
                    File.Delete(strPathUpgradeSuceed)
                Catch
                End Try

                Try
                    File.Delete(_strUpdatePackageFile)
                Catch
                End Try

                If Not _FirstRunFromUpgrade Then
                    Return
                End If

                Try
                    Dim strFileTag As String = Path.Combine(My.Application.Info.DirectoryPath.TrimEnd("\"), "OpenOK.dll")

                    If Not File.Exists(strFileTag) Then
                        Dim fileStream As FileStream = File.Create(strFileTag)
                        fileStream.Close()
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "LiveUpdateHelper.OpenOKException")
                End Try

            Else
                If Not Folder.LangType = LanguageType.en_US Then

                    '助手升级增量更新时，每个下载的文件都会先比较md5，所以这边不需要再比较
                    If blnNewIncrease = False Then
                        '如果包不完整则删除
                        Dim strServerFileLength As String = IniSetting.GetUpdateFileLength()
                        If Not CheckUpdateFileIsFull(_strUpdatePackageFile, strServerFileLength) Then
                            Try
                                File.Delete(_strUpdatePackageFile)
                            Catch
                            End Try

                            Return
                        End If
                    ElseIf Not Directory.Exists(_FolderUpgrade) Then
                        Return
                    End If

                    '升级文件已经下载完毕，但是用户没有立刻升级，所以启动的时候需要程序自动升级
                    LiveUpdateHelper.StartLiveUpdateExe(_strUpdatePackageFile, _FolderUpgrade, Application.ExecutablePath, "")
                End If
            End If
        End If
    End Sub

    Public Shared Function CheckUpdateFileIsFull(ByVal strUpdateFile As String, ByVal strServerFileLength As String) As Boolean
        Dim blnFull As Boolean = False

        If File.Exists(strUpdateFile) Then
            Try
                Dim lngFileLength As Long = New FileInfo(strUpdateFile).Length
                If String.Compare(lngFileLength.ToString(), strServerFileLength) = 0 Then
                    blnFull = True
                End If
            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try
        End If

        Return blnFull
    End Function

#End Region
End Class
