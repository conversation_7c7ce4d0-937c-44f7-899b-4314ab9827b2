﻿Imports System.IO

Public Class LiveUpdateForm

    Private _softPath As String = ""
    Private _zipFile As String = ""
    Private _UpgradeFolder As String = ""
    Private _strVersion As String = ""
    'Private _Application As IApplication = Nothing

    Sub New(ByVal softPath As String, _
            ByVal strVersion As String, _
            ByVal strRtfFile As String, _
            ByVal zipFile As String, _
            ByVal UpgradeFolder As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbSplit = "10,33,10,33"
        Me.BackColor = System.Drawing.Color.FromArgb(233, 237, 243)
        Me.FilletRadius = 5
        Me.AutoSetFont = False

        Me.lblInfo.Text = String.Format(Me.Language.GetString("Main.Upgrade.Label.UpgradeInfo"), _
                                        Path.GetFileNameWithoutExtension(softPath), strVersion)
        '"我们一直努力在改进我们的软件，以便它能够给您带来更好的体验以及更好的服务于所有的用户。我们很高兴的通知您新的升级已准备就绪：" & _
        '"{0}您正在使用的 {1} 已经有了一个新的版本：{2}。"

        With Me.rtfHistory
            .ReadOnly = True
            .BackColor = Color.White
            If System.IO.File.Exists(strRtfFile) Then
                .LoadFile(strRtfFile)
            End If
        End With

        Me._softPath = softPath
        Me._zipFile = zipFile
        Me._UpgradeFolder = UpgradeFolder
        Me._strVersion = strVersion
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.btnCancel.tbText = Me.Language.GetString("Main.Upgrade.Button.UpgradeLater") '"稍后再说"
        Me.btnLogin.tbText = Me.Language.GetString("Main.Upgrade.Button.UpgradeAtOnce") '"立刻升级"
        Me.Text = Me.Language.GetString("Main.Upgrade.Text") '"有新的版本可以升级"

    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click, btn_close.Click
        Try
            ''删除下载的zip包
            'File.Delete(Me._zipFile)

            Directory.Delete(Me._UpgradeFolder, True)
        Catch
        End Try

        Me.Close()
    End Sub

    Private Sub btnLogin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLogin.Click
        LiveUpdateHelper.StartLiveUpdateExe(Me._zipFile, Me._UpgradeFolder, Me._softPath, Me._strVersion)

        Me.Close()
    End Sub

    Private Sub LiveUpdateForm_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        Me.Dispose()
    End Sub

    Private Sub rtfHistory_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkClickedEventArgs) Handles rtfHistory.LinkClicked
        If e.LinkText.Length > 0 Then
            Common.OpenExplorer(e.LinkText)
        End If
    End Sub

End Class
