﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class LiveUpdateForm
    Inherits tbBaseGuiForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btn_close = New tbButton
        Me.pnlMain = New tbPanel
        Me.PictureBox1 = New System.Windows.Forms.PictureBox
        Me.Panel1 = New System.Windows.Forms.Panel
        Me.rtfHistory = New System.Windows.Forms.RichTextBox
        Me.lblInfo = New System.Windows.Forms.Label
        Me.btnCancel = New tbButton
        Me.btnLogin = New tbButton
        Me.pnlMain.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(487, 0)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 10
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = My.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.White
        Me.pnlMain.Controls.Add(Me.PictureBox1)
        Me.pnlMain.Controls.Add(Me.Panel1)
        Me.pnlMain.Controls.Add(Me.lblInfo)
        Me.pnlMain.Location = New System.Drawing.Point(1, 31)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(509, 292)
        Me.pnlMain.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlMain.TabIndex = 11
        Me.pnlMain.tbBackgroundImage = Nothing
        Me.pnlMain.tbShowWatermark = False
        Me.pnlMain.tbSplit = "8,22,8,9"
        Me.pnlMain.tbWatermark = Nothing
        Me.pnlMain.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMain.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = My.Resources.icon_upgrade
        Me.PictureBox1.Location = New System.Drawing.Point(18, 23)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(48, 48)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBox1.TabIndex = 14
        Me.PictureBox1.TabStop = False
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(175, Byte), Integer), CType(CType(188, Byte), Integer), CType(CType(207, Byte), Integer))
        Me.Panel1.Controls.Add(Me.rtfHistory)
        Me.Panel1.Location = New System.Drawing.Point(25, 88)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Padding = New System.Windows.Forms.Padding(1)
        Me.Panel1.Size = New System.Drawing.Size(460, 185)
        Me.Panel1.TabIndex = 2
        '
        'rtfHistory
        '
        Me.rtfHistory.BackColor = System.Drawing.Color.White
        Me.rtfHistory.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtfHistory.Dock = System.Windows.Forms.DockStyle.Fill
        Me.rtfHistory.Location = New System.Drawing.Point(1, 1)
        Me.rtfHistory.Name = "rtfHistory"
        Me.rtfHistory.ReadOnly = True
        Me.rtfHistory.ScrollBars = System.Windows.Forms.RichTextBoxScrollBars.ForcedVertical
        Me.rtfHistory.ShowSelectionMargin = True
        Me.rtfHistory.Size = New System.Drawing.Size(458, 183)
        Me.rtfHistory.TabIndex = 1
        Me.rtfHistory.Text = ""
        '
        'lblInfo
        '
        Me.lblInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(75, Byte), Integer), CType(CType(100, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.lblInfo.Location = New System.Drawing.Point(78, 24)
        Me.lblInfo.Name = "lblInfo"
        Me.lblInfo.Size = New System.Drawing.Size(407, 56)
        Me.lblInfo.TabIndex = 0
        Me.lblInfo.Text = "我们一直努力在改进我们的软件，以便它能够给您带来更好的体验以及更好的服务于所有的用户。我们很高兴的通知您新的升级已准备就绪：" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "您正在使用的 {product" & _
            "Name} 已经有了一个新的版本：{newVersion}。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.Location = New System.Drawing.Point(399, 327)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(89, 25)
        Me.btnCancel.TabIndex = 13
        Me.btnCancel.TabStop = False
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "稍后再说"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnLogin
        '
        Me.btnLogin.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnLogin.BackColor = System.Drawing.Color.Transparent
        Me.btnLogin.BindingForm = Nothing
        Me.btnLogin.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnLogin.Location = New System.Drawing.Point(291, 327)
        Me.btnLogin.Name = "btnLogin"
        Me.btnLogin.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnLogin.Selectable = True
        Me.btnLogin.Size = New System.Drawing.Size(102, 25)
        Me.btnLogin.TabIndex = 12
        Me.btnLogin.TabStop = False
        Me.btnLogin.tbAdriftIconWhenHover = False
        Me.btnLogin.tbAutoSize = False
        Me.btnLogin.tbAutoSizeEx = False
        Me.btnLogin.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnLogin.tbBackgroundImageState = ImageState.FourState
        Me.btnLogin.tbBadgeNumber = 0
        Me.btnLogin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnLogin.tbEndEllipsis = False
        Me.btnLogin.tbIconHoldPlace = True
        Me.btnLogin.tbIconImage = Nothing
        Me.btnLogin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.tbIconImageState = ImageState.OneState
        Me.btnLogin.tbIconMore = False
        Me.btnLogin.tbIconMouseDown = Nothing
        Me.btnLogin.tbIconMouseHover = Nothing
        Me.btnLogin.tbIconMouseLeave = Nothing
        Me.btnLogin.tbIconPlaceText = 2
        Me.btnLogin.tbIconReadOnly = Nothing
        Me.btnLogin.tbImageMouseDown = Nothing
        Me.btnLogin.tbImageMouseHover = Nothing
        Me.btnLogin.tbImageMouseLeave = Nothing
        Me.btnLogin.tbReadOnly = False
        Me.btnLogin.tbReadOnlyText = False
        Me.btnLogin.tbShadow = False
        Me.btnLogin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnLogin.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnLogin.tbShowNew = False
        Me.btnLogin.tbShowToolTipOnButton = False
        Me.btnLogin.tbSplit = "13,11,13,11"
        Me.btnLogin.tbText = "立刻升级"
        Me.btnLogin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.tbTextColor = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDisable = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDown = System.Drawing.Color.White
        Me.btnLogin.tbTextColorHover = System.Drawing.Color.White
        Me.btnLogin.tbTextMouseDownPlace = 0
        Me.btnLogin.tbToolTip = ""
        Me.btnLogin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnLogin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnLogin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'LiveUpdateForm
        '
        Me.ClientSize = New System.Drawing.Size(511, 374)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnLogin)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.btn_close)
        Me.MinimumSize = New System.Drawing.Size(45, 140)
        Me.Name = "LiveUpdateForm"
        Me.tbShowTitleOnForm = True
        Me.tbShowWatermark = True
        Me.Text = "有新的版本可以升级"
        Me.pnlMain.ResumeLayout(False)
        Me.pnlMain.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel1.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As tbButton
    Friend WithEvents pnlMain As tbPanel
    Friend WithEvents btnCancel As tbButton
    Friend WithEvents btnLogin As tbButton
    Friend WithEvents lblInfo As System.Windows.Forms.Label
    Friend WithEvents rtfHistory As System.Windows.Forms.RichTextBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox

End Class
