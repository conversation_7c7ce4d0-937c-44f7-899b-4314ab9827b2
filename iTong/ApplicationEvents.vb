﻿Imports Microsoft.Win32
Imports System.Windows.Forms
Imports iTong.CoreFoundation.FlashInfo

Namespace My

    ' 以下事件可用于 MyApplication:
    ' 
    ' Startup: 应用程序启动时在创建启动窗体之前引发。
    ' Shutdown: 在关闭所有应用程序窗体后引发。如果应用程序异常终止，则不会引发此事件。
    ' UnhandledException: 在应用程序遇到未处理的异常时引发。
    ' StartupNextInstance: 在启动单实例应用程序且应用程序已处于活动状态时引发。
    ' NetworkAvailabilityChanged: 在连接或断开网络连接时引发。
    Partial Friend Class MyApplication

        Protected Overrides Function OnStartup(ByVal e As Microsoft.VisualBasic.ApplicationServices.StartupEventArgs) As Boolean

            '启动前判断是否是以管理员身份允许，是否在 Vista UAC 下以管理员身份运行
            If Me.CheckAuthority() = False Then
                '失败后停止加载程序
                e.Cancel = True

                Return False
                'ElseIf RegistHelper.CheckExpire() Then
                '    e.Cancel = True

                '    Me.InitLang()
                '    '"很抱歉，iTong的试用版已过期，请升级后继续使用。"
                '    If tbMessageBox.Show(LanguageInterface.Instance.GetString("Main.Message.PastDue"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = DialogResult.OK Then
                '        Dim frm As New frmSoftUpdate()
                '        frm.ShowDialog()
                '    End If
                '    Return False
            End If

            'If OSHelper.OverWin7 AndAlso SetRunInWinXP(Folder.AppFile) Then
            '    e.Cancel = True

            '    Return False
            'End If

            Return MyBase.OnStartup(e)
        End Function

        Public Shared Function SetRunInWinXP(ByVal EXEName As String) As Boolean
            Dim blnResult As Boolean = False

            Try
                Dim key As RegistryKey = Registry.CurrentUser.OpenSubKey("SoftWare\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers", True)
                '打开注册表子项
                If key Is Nothing Then
                    '如果该项不存在的话，则创建该子项
                    key = Registry.LocalMachine.CreateSubKey("SoftWare\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers")
                End If

                '如果不存在该值，则设置该值，重启程序应用设置
                Dim strValue As String = key.GetValue(EXEName) & ""
                If String.IsNullOrEmpty(strValue) OrElse Not strValue.Contains("WINXPSP3") Then
                    'Dim strNewValue As String = "WINXPSP3 " & strValue.Trim()
                    'key.SetValue(EXEName, strNewValue.Trim())
                    blnResult = True

                    HelperExeManager.RunAppInWinXP(EXEName)
                End If

                If key IsNot Nothing Then
                    key.Close()
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetRunInWinXP")
            End Try

            Return blnResult
        End Function

        Private Sub InitLang()
            Dim cultureInfo As System.Globalization.CultureInfo = System.Globalization.CultureInfo.CurrentCulture
            Dim langName As String = IniSetting.GetLanguage()
            If langName.Length > 0 Then
                Try
                    cultureInfo = New System.Globalization.CultureInfo(langName)
                Catch ex As Exception
                End Try
            End If

            Common.CultureName = LanguageInterface.Instance(cultureInfo).CurrentLanguage.LangName
        End Sub

        '判断是否是管理员权限运行！
        Public Function CheckAuthority() As Boolean
            Return True
            'Dim id As System.Security.Principal.WindowsIdentity = System.Security.Principal.WindowsIdentity.GetCurrent
            'Dim principal As System.Security.Principal.WindowsPrincipal = New System.Security.Principal.WindowsPrincipal(id)

            'If principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator) = False Then
            '    Dim frmAuthority As New AuthorityWarningForm

            '    frmAuthority.ShowDialog()

            '    Return False
            'Else
            '    Return True
            'End If

        End Function

        Protected Overrides Sub OnStartupNextInstance(ByVal eventArgs As Microsoft.VisualBasic.ApplicationServices.StartupNextInstanceEventArgs)
            If eventArgs.CommandLine.Count > 0 Then
                MyBase.OnStartupNextInstance(eventArgs)
                Try
                    '一键安装，如果已经打开了主窗体，则把参数传递给已经打开的ProcForm
                    Dim frmMain As Form = Me.MainForm

                    Dim strCommand As String = eventArgs.CommandLine.Item(0).Trim


                    'If strCommand.StartsWith("FlashProgress") Then
                    '    strCommand = strCommand.Replace("FlashProgress", "")
                    '    RestoreProgress(strCommand)    '  CType(frmMain, ProcForm).RestoreProgress(strCommand)
                    'Else
                    CType(frmMain, ProcForm).ProcessAutoInstallArgs(strCommand)
                    'End If

                Catch ex As Exception
                    Common.LogException(ex.ToString)
                End Try
            Else
                Me.SendArgs(System.IO.Path.GetFileNameWithoutExtension(System.Windows.Forms.Application.ExecutablePath), "ReActive")
                MyBase.OnStartupNextInstance(eventArgs)
            End If
        End Sub

        Protected Overrides Function OnUnhandledException(ByVal e As Microsoft.VisualBasic.ApplicationServices.UnhandledExceptionEventArgs) As Boolean
            '程序发生未知错误
            Common.LogException(e.Exception.ToString)
        End Function

#Region "--- 重新打开应用程序的exe文件时激活MainForm ---"

        Public Const SC_RESTORE As Int32 = &HF120
        Public Const WM_COPYDATA As Integer = &H4A        '在 SendMessage 的时候传值 WM_COPYDATA 必须为 [&H4A]
        Private Declare Function SendMessage Lib "user32.dll" Alias "SendMessageA" (ByVal hwnd As Int32, ByVal wMsg As Int32, ByVal wParam As Int32, ByRef lParam As COPYDATASTRUCT) As Int32
        Private Declare Function FindWindow Lib "user32" Alias "FindWindowA" (ByVal lpClassName As String, ByVal lpWindowName As String) As Int32

        Public Function SendArgs(ByVal sProName As String, ByVal strKey As String) As Boolean
            Try
                '取得正在运行的进程。
                Dim Processes As Process() = Diagnostics.Process.GetProcessesByName(sProName)
                For Each Item As Process In Processes
                    Dim frmhwnd As Integer = 0

                    For Each frmOpen As Form In Application.OpenForms
                        Dim fName As String = frmOpen.Name

                        If TypeOf frmOpen Is ProcForm Then
                            frmhwnd = frmOpen.Handle
                            Exit For
                        ElseIf fName.Equals("DaemonForm") Then
                            Return False
                        End If
                    Next

                    If frmhwnd <> 0 Then
                        Dim byteFilePath() As Byte = System.Text.Encoding.Default.GetBytes(strKey)
                        Dim cdsDataStruct As COPYDATASTRUCT
                        cdsDataStruct.dwData = CType(100, IntPtr)
                        cdsDataStruct.lpData = strKey '"ReActive"
                        cdsDataStruct.cbData = (byteFilePath.Length + 1)
                        SendMessage(frmhwnd, WM_COPYDATA, SC_RESTORE, cdsDataStruct)
                        Return True
                    End If
                Next

            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try

            Return False
        End Function

        Public Function SendArgsToProcess(ByVal sProName As String, ByVal strKey As String) As Boolean
            Try
                '取得正在运行的进程。
                Dim Processes As Process() = Diagnostics.Process.GetProcessesByName(sProName)
                For Each Item As Process In Processes
                    Dim frmhwnd As Integer = Item.MainWindowHandle
                    If frmhwnd <> 0 Then
                        Dim byteFilePath() As Byte = System.Text.Encoding.Default.GetBytes(strKey)
                        Dim cdsDataStruct As COPYDATASTRUCT
                        cdsDataStruct.dwData = CType(100, IntPtr)
                        cdsDataStruct.lpData = strKey '"ReActive"
                        cdsDataStruct.cbData = (byteFilePath.Length + 1)
                        SendMessage(frmhwnd, WM_COPYDATA, SC_RESTORE, cdsDataStruct)
                        Return True
                    End If
                Next

            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try

            Return False
        End Function



#End Region




    End Class

End Namespace