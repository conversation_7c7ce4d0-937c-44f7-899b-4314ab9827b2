﻿Imports System.ComponentModel

Public Class frmPandaControlTip

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong

        ' Add any initialization after the InitializeComponent() call.
        If Me.Language.CurrentLanguage.LangName = "zh-CN" Then
            Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.pic_pandacontrol_ad
        Else
            Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.pic_pandacontrol_ad_eng
        End If

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.cbxNotShow.Text = Me.Language.GetString("PandaControl.Label.NotShow")       '7天内不再提示
        Me.btnOK.Text = Me.Language.GetString("PandaControl.Label.Experience")           '立即体验
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Dim strPath As String = Path.Combine(Application.StartupPath, "PandaControl\Launcher.exe")
        If File.Exists(strPath) Then
            Common.OpenFile(strPath)
        Else
            Common.OpenExplorer("https://www.pandacontrol.vip/")
        End If
        Me.Close()
    End Sub

    Protected Overrides Sub OnClosing(e As CancelEventArgs)
        MyBase.OnClosing(e)

        If cbxNotShow.Checked Then
            IniSetting.SetShowPandaControl(True)
        End If
    End Sub
End Class