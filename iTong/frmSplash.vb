﻿Public Class frmSplash

    Property Status() As String
        Get
            Return Me.lblStatus.Text
        End Get
        Set(ByVal value As String)
            Me.lblStatus.Text = value
        End Set
    End Property

    Private Sub frmSplash_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Dim strVer As String = String.Empty
        If My.Application.Info.Version.MinorRevision = 0 Then
            strVer = My.Application.Info.Version.ToString(3)
        Else
            strVer = My.Application.Info.Version.ToString(4)
        End If

        Me.lblVersion.Text = strVer
        Me.lblStatus.Text = LanguageInterface.Instance.GetString("App.Cell.Loading")
        Me.Text = Me.lblStatus.Text
        Me.Icon = My.Resources.iTong
        'If Not Folder.LangType = LanguageType.en_US Then
        '    Me.BackgroundImage = My.Resources.splash_bg_cn
        'End If

        'Me.lblVersion.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        'Me.lblStatus.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)

        '' Added by Utmost20140603
        '' 建议使用临时变量保存，这样只创建一次字体
        Dim fnt As Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.lblVersion.Font = fnt
        Me.lblStatus.Font = fnt

    End Sub

End Class