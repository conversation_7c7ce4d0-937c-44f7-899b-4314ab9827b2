﻿Imports System.Threading

Public Class frmWhatsApp

    Private Enum LoadDataType
        Normal = 0              '普通展示
        GetNews = 1             '查看更多消息
        Search = 2              '搜索
        TimeLocate = 3          '时间定位
        GetContextNews = 4      '查看前后消息
    End Enum

    Private Enum WhatsAppStatus
        ShowError
        Loading
        Message
    End Enum

    Private mHelper As IMExportHelper

    Private mBackupHelper As BackupHelper = Nothing                                 '备份类
    Private dgvSession As tbDataGridViewEx = Nothing
    Private mWhatsAppHelper As WhatsAppHelper = Nothing
    Private mThrLoadSession As Thread = Nothing
    Private mThreadLoadMessage As Thread = Nothing
    Private mThreadSearchMessage As Thread = Nothing
    Private mThreadBackupDevice As Thread = Nothing
    Private mSessionIconSize As New Size(44, 44)
    Private mDefaultHeadIcon As Image = My.Resources.weixin_icon_default.Clone
    Private mPathWhatsAppOnPC_Cache As String = String.Empty                        '保存头像
    Private mPathWhatsAppOnPC_Temp As String = String.Empty
    Private mBackupPath As String = String.Empty
    Private mShowMessageCount As Integer = 25
    Private mBackupDeviceUDID As String = ""
    Private mNewBackup As Boolean = False
    Private mChargeHelper As ChargeHelper = Nothing
    Private mSelectAllClick As Boolean = False
    Private mIsAddRow As Boolean = False
    Private mPlayer As IPluginPlayer
    Private mBackupInfo As BackupInfo

    Private mWhatsAppSKU As String = "net.whatsapp.WhatsApp"

    Private mFrmProgress As frmExportProgress = Nothing

    Private mCurrentDataGridViewRow As DataGridViewRow = Nothing
    Private mIsFirstLoad As Boolean = True

#Region "----初始化----"

    Public Sub New(ByVal app As IApplication, ByVal dev As iPhoneDevice, ByVal blnNewBackup As Boolean)
        Me.mApplication = app
        Me.Language = app.Language
        ' 此调用是设计器所必需的。
        InitializeComponent()
        Me.mDevice = dev
        Me.mNewBackup = blnNewBackup
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Public Sub New(ByVal app As IApplication, ByVal backInfo As BackupInfo)
        Me.mApplication = app
        Me.Language = app.Language
        ' 此调用是设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mBackupPath = backInfo.FolderPath
        Me.mBackupInfo = backInfo

        Me.BackColor = Color.FromArgb(233, 233, 233)
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.mBackupHelper = BackupHelper.Instance(Me.mDevice)
        AddHandler Me.mBackupHelper.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged

        Dim para As New IMInstancePara()
        para.iPhone = Me.mDevice
        para.AppType = IMAppType.WhatsApp
        para.IsFullBackup = Me.mNewBackup
        para.BackupHelper = Me.mBackupHelper

        Me.mHelper = IMExportHelper.Instance(para)

        If Me.Parent IsNot Nothing Then
            Me.tblayoutMain.Location = New Point(0, 0)
            Me.tblayoutMain.Height = Me.tblayoutMain.Height + 32
            Me.tblayoutMain.Width = Me.tblayoutMain.Width + 2
            Me.tbGuiBackground = Nothing
            Me.Text = ""
        Else
            Me.Icon = My.Resources.iTong
            Me.tbShowIconOnForm = True
            Me.tbShowTitleOnForm = True
            Me.Size = New Size(970, 650)
        End If

        Me.SetViewStyle(WhatsAppStatus.Loading)

        Me.InitDataGridView()
        Me.mPathWhatsAppOnPC_Temp = Folder.GetTempFilePath()
        Me.mPathWhatsAppOnPC_Cache = Path.Combine(Me.mPathWhatsAppOnPC_Temp, "WhatsApp")
        Folder.CheckFolder(Me.mPathWhatsAppOnPC_Temp)
        Folder.CheckFolder(Me.mPathWhatsAppOnPC_Cache)
        Me.LoadBackupInfo()

        Me.mChargeHelper = ChargeHelper.Instance(ChargeProjectType.WhatApps)

        Me.InitMusicPlayer()
        If Not Me.CheckChargeOverdue(False) Then
            Me.lblRegister.Visible = False
        End If

        If Me.Language.CurrentLanguage.LangName.ToLower() = "en-us" Then
            Me.btnExport.Size = New Size(190, 38)
        Else
            Me.btnExport.Size = New Size(130, 38)
        End If
        Me.btnExport.Location = New Point(Me.btnSelectExportType.Location.X - Me.btnExport.Size.Width, 18)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Dim fontF As Font = Common.CreateFont("Arial", 10.0!, FontStyle.Regular)
        Me.lblRegister.ColorTextList.Clear()
        Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("MainVip.Button.Register"), 1, System.Drawing.Color.FromArgb(0, 169, 255), fontF, False, True) '"激活"
        Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("WhatsApp.Message.RegisterWarn"), 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False) '"试用部分内容用*号代替，请点击右上角"

        Me.lblRegister.TextAlign = ContentAlignment.MiddleCenter


        'Me.lblTopTip.Text = Me.Language.GetString("WhatsApp.Label.TrialTopTip")
        Me.btnExport.Text = Me.Language.GetString("WhatsApp.Label.ExportRecover")
        If Me.mBackupInfo IsNot Nothing Then
            Dim strDeviceName As String = Me.mBackupInfo.DisplayName
            If strDeviceName.Length > 10 Then
                strDeviceName = strDeviceName.Substring(0, 8) & "..."
            End If

            Me.lblBackupInfo.Text = String.Format(Me.Language.GetString("WhatsApp.Message.BackupInfo"), strDeviceName, Me.mBackupInfo.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute))) '"当前数据是【{0}-{1}】备份文件中读取。"
            Me.btnGetNewBackup.Visible = True
            If Me.GetCurrentDevice() Is Nothing Then
                Me.btnGetNewBackup.Visible = False
            Else
                Me.btnGetNewBackup.Visible = True
            End If
        ElseIf Me.mDevice IsNot Nothing Then
            Me.lblBackupInfo.Visible = False
            Me.btnGetNewBackup.Visible = False
        End If
        Me.btnGetNewBackup.Text = Me.Language.GetString("Common.Backup.BackupNewData")

        Me.btnExportSetting.Text = Me.Language.GetString("WhatsApp.Label.ExportMessageSetting") '导出设置
        Dim fontFError As Font = Common.CreateFont("Arial", 9.0!, FontStyle.Regular)
        Me.lblReadError.ColorTextList.Clear()
        Me.lblReadError.ColorTextList.AddItem(Me.Language.GetString("WhatsApp.Message.LoadErrorPlease"), 0, System.Drawing.Color.FromArgb(106, 106, 106), fontFError, False) '读取失败，请
        Me.lblReadError.ColorTextList.AddItem(Me.Language.GetString("Common.Retry"), 1, System.Drawing.Color.FromArgb(0, 169, 255), fontFError, False, True)  '重试
        Me.lblReadError.ColorTextList.AddItem(Me.Language.GetString("WhatsApp.Message.TryOtherRecoveryMode"), 2, System.Drawing.Color.FromArgb(106, 106, 106), fontFError, False) '或尝试其他恢复模式
        AddHandler Me.lblReadError.TextClick, AddressOf lblReadError_TextClick
        AddHandler Me.lblReadError.SizeChange, AddressOf lblReadError_SizeChange
        Me.lblReadError.TextAlign = ContentAlignment.MiddleCenter

        Me.cbxSelectAll.Text = Me.Language.GetString("WhatsApp.Label.SelectAll") '全选

        Me.tsmiPicEx.Text = Me.Language.GetString("Weixin.Lable.ExportPic")                               '"导出图片"
        Me.tsmiAudioEx.Text = Me.Language.GetString("Weixin.Lable.ExportAudio")                           '"导出语音"
        Me.tsmiVideoEx.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideo")                      '"导出小视频"
        Me.tsmiAllEx.Text = Me.Language.GetString("Contact.Menu.ExportAll")                               '导出全部

        Me.tsmiShowAll.Text = Me.Language.GetString("WhatsApp.MenuItem.ShowAll") '显示全部
        Me.btnSelect.Text = Me.Language.GetString("WhatsApp.MenuItem.ShowAll") '显示全部
        Me.tsmiShowDelete.Text = Me.Language.GetString("WhatsApp.MenuItem.ShowDelect") '只显示删除文件
        Me.tsmiShowNotDelete.Text = Me.Language.GetString("WhatsApp.MenuItem.ShowExist") '显示已存在文件
    End Sub

    Private Delegate Sub SetViewStyleHandler(ByVal status As WhatsAppStatus)
    Private Sub SetViewStyle(ByVal status As WhatsAppStatus)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), status)
        Else
            Me.tblayoutMain.SuspendLayout()
            Select Case status
                Case WhatsAppStatus.ShowError
                    Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(0).Width = 100
                    Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(1).Width = 0
                    Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(2).Width = 0

                Case WhatsAppStatus.Loading
                    Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(0).Width = 0
                    Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(1).Width = 100
                    Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(2).Width = 0

                Case WhatsAppStatus.Message
                    Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(0).Width = 0
                    Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(1).Width = 0
                    Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                    Me.tblayoutMain.ColumnStyles(2).Width = 100

            End Select
            Me.tblayoutMain.ResumeLayout()
        End If
    End Sub

    Private Sub InitMusicPlayer()
        Me.mPlayer = PluginPlayer.Instance()
        Me.mPlayer.PlayMode = UserPlayMode.OrderPlay

        If Me.mPlayer.GetUserPlaylist(Me.Name) Is Nothing Then
            Me.mPlayer.AddUserPlaylist(Me.Name)
        End If

        AddHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange
    End Sub

#End Region

#Region "----从设备备份数据----"

    Private Sub LoadBackupInfo()
        If Me.mDevice Is Nothing Then
            Me.LoadSession()
        Else
            Me.BackupDevice()
        End If
    End Sub

    Private Sub UnInstalledCloseMe()
#If IS_RECOVERY Then
        Try
            Dim isInstalled As Boolean = False
            Try
                '判断是否 安装软件
                If Me.mDevice IsNot Nothing Then
                    '从设备读取  直接判断设备是否安装
                    Dim dictApp As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User)
                    If dictApp.ContainsKey(Me.mWhatsAppSKU) Then
                        isInstalled = True
                    End If

                Else
                    '从备份读取判断备份是否存在
                    Dim lstMBFileRecord As List(Of MBFileRecord) = mbdb.Reload(Me.mBackupPath, True)
                    If lstMBFileRecord.Count > 0 Then
                        For Each item As MBFileRecord In lstMBFileRecord
                            If item.PathOnPhone IsNot Nothing AndAlso item.PathOnPhone.Contains("/var/mobile/Applications/" & Me.mWhatsAppSKU) Then
                                isInstalled = True
                                Exit For
                            End If
                        Next
                    End If
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "UnInstalledCloseMe")
            End Try

            If Not isInstalled Then
                tbMessageBox.Show(Me, String.Format(Me.Language.GetString("WhatsApp.Message.UnInstalled"), "WhatsApp"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                CType(Me.mApplication, MainFormDataRecovery).RecoveryListBack()
                Me.DoExit()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UnInstalledCloseMe")
        End Try
#End If
    End Sub


    Private Sub BackupDevice()
        Try
            Utility.AbortThread(Me.mThreadBackupDevice)
            Me.mThreadBackupDevice = New Thread(AddressOf BackupDeviceThread)
            With Me.mThreadBackupDevice
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_BackupDevice")
        End Try
    End Sub

    Private Sub BackupDeviceThread()
        Try
            Me.UnInstalledCloseMe()

            '如果备份过就不用重新备份了
            If Me.mBackupHelper.iDataRecoveryBackupFolder.Length > 0 Then
                Me.mBackupPath = Me.mBackupHelper.iDataRecoveryBackupFolder
                Me.LoadSession()
                Return
            End If

            Me.mBackupPath = BackupHelper.GetBackUpFolder(Me.mNewBackup, Me.mDevice.Identifier)
            Dim callback As New BackupRestoreHandler(AddressOf mb2_Backup_BackupRestore)
            If Not Me.mDevice.mb2_Backup(Me.mBackupPath, callback, Me.mNewBackup) Then
                '备份失败
                Me.mBackupHelper.iDataRecoveryBackupFolder = String.Empty
                Me.SetViewStyle(WhatsAppStatus.ShowError)

            Else
                Me.mBackupHelper.AddLstFullBackupList(Me.mBackupPath)
                '备份成功后显示聊天记录
                Me.mBackupHelper.iDataRecoveryBackupFolder = Me.mBackupPath
                Me.LoadSession()
                Me.mBackupHelper.GetFullBackupList(True)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_BackupDeviceThread")
        End Try
    End Sub

    Private Sub OnDecryptProgressChanged(sender As Object, args As BackupDecryptArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupDecryptArgs)(AddressOf OnDecryptProgressChanged), sender, args)
        Else
            Me.lblLoading.Text = String.Format("{0}{1}%", Me.Language.GetString("Backup.Message.Decrypteding"), args.intProgress)
        End If
    End Sub

    Private Delegate Sub mb2_Backup_BackupRestoreHandler(ByVal e As BackupRestoreEventArgs)
    Private Sub mb2_Backup_BackupRestore(ByVal e As BackupRestoreEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New mb2_Backup_BackupRestoreHandler(AddressOf mb2_Backup_BackupRestore), e)
        Else
            Dim sType As BackupRestoreErrorType = BackupRestoreErrorType.None
            If e.ErrorMsg.Length > 0 Then
                Dim args As New BackupRestoreEventArgs
                args.ErrorMsg = e.ErrorMsg
                sType = BackupHelper.GetErrotType(BackupRestore.Backup, args)

                Try
                    If sType = BackupRestoreErrorType.DiskSpaceNotEnough Then
                        Me.lblError.Text = Me.Language.GetString("Common.Message.DiskSpaceNotEnough")
                    Else
                        Me.lblError.Text = args.ErrorMsg
                    End If
                Catch
                End Try
            Else
                Dim strProgress As Integer = CInt(e.Progress).ToString().PadLeft(2, " "c)
                Me.lblLoading.Text = String.Format("{0}{1}%", Me.Language.GetString("Backup.Message.BeinBackuping"), strProgress) ' "正在备份中..."
            End If
        End If
    End Sub

#End Region

#Region "----窗体事件----"
    Private Sub lblRegister_TextClick(sender As Object, e As EventArgs) Handles lblRegister.TextClick
        Me.mChargeHelper.ShowChargeForm(Me)
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs)
        'Me.SetViewStyle(WhatsAppStatus.Loading)
        'ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        'Me.LoadSession()
    End Sub

    Private Sub wbChat_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs) Handles wbsChat.Navigating
        Dim url As String = e.Url.ToString
        'Debug.Print("微信weburl:" & url)

        If url.StartsWith("weixin://getnews", StringComparison.OrdinalIgnoreCase) Then
            Me.GetNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://viewphoto", StringComparison.OrdinalIgnoreCase) Then
            Me.ShowPhotoAndLocation(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://copy", StringComparison.OrdinalIgnoreCase) Then
            Me.CopyNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportphoto", StringComparison.OrdinalIgnoreCase) Then
            Me.ExportMedia(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportvideo", StringComparison.OrdinalIgnoreCase) Then
            Me.ExportMedia(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportvoice", StringComparison.OrdinalIgnoreCase) Then
            'Me.ExportMedia(url)
            Me.ExportVoice(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://playvoice", StringComparison.OrdinalIgnoreCase) Then
            Me.PlayVoice(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://playvideo", StringComparison.OrdinalIgnoreCase) Then

            e.Cancel = True

        ElseIf url.StartsWith("weixin://getcontextnews", StringComparison.OrdinalIgnoreCase) Then
            'Me.GetContextNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase) Then
            Me.Navigate(url)
            e.Cancel = True

        End If
    End Sub

    '查看新消息
    Private Sub GetNews(ByVal url As String)
        Dim strUsrName As String = Utility.GetParamValueFromQuery("friend", url)
        Dim strID As String = Utility.GetParamValueFromQuery("id", url)

        If String.IsNullOrEmpty(strUsrName) OrElse String.IsNullOrEmpty(strID) Then
            Return
        End If
        Dim session As WhatsAppSession = Me.mWhatsAppHelper.GetSessions(strUsrName)
        Me.LoadMessage(session, strID, LoadDataType.GetNews)
    End Sub

    '查看前后消息
    Private Sub GetContextNews(ByVal url As String)
        Dim message As WhatsAppMessage = Me.GetMessageForWeb(url)
        Dim strID As String = Utility.GetParamValueFromQuery("id", url)

        If message IsNot Nothing Then
            ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
            Me.LoadMessage(message.ChatSession, strID, LoadDataType.GetContextNews)
        End If
    End Sub

    'URL跳转
    Private Sub Navigate(ByVal url As String)
        Dim startText As String = "?url="
        Dim startIndex As Integer = url.IndexOf(startText)
        Dim strUrl As String = url.Substring(startIndex + startText.Length).Trim()

        If Not String.IsNullOrEmpty(strUrl) Then
            Dim strPrefix As String = "http://"
            If Not strUrl.StartsWith(strPrefix) Then
                strUrl = strPrefix & strUrl
            End If
            Common.OpenExplorer(strUrl)
        End If
    End Sub

    'copy文本
    Private Sub CopyNews(ByVal url As String)
        If Me.CheckChargeOverdue Then
            Return
        End If

        Dim message As WhatsAppMessage = Me.GetMessageForWeb(url)
        If message Is Nothing Then
            Return
        End If
        Dim strContent As String = message.Text
        Try
            Clipboard.SetText(strContent)
            Me.ChangeStateText(Me.Language.GetString("Welcome.Message.CopySucceed"))
        Catch
            Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CopyFail")) ' "复制失败！"
        End Try
    End Sub


    Private Function GetMessageForWeb(ByVal url As String) As WhatsAppMessage
        Dim result As WhatsAppMessage = Nothing
        Dim strUsrName As String = Utility.GetParamValueFromQuery("friend", url)
        Dim strID As String = Utility.GetParamValueFromQuery("id", url)

        If String.IsNullOrEmpty(strUsrName) OrElse String.IsNullOrEmpty(strID) Then
            Return result
        End If
        result = Me.mWhatsAppHelper.GetMessage(strUsrName, strID)
        Return result
    End Function


    '导出图片视频
    Private Sub ExportMedia(ByVal url As String)
        If Me.CheckChargeOverdue() Then
            Return
        End If

        Dim message As WhatsAppMessage = Me.GetMessageForWeb(url)
        If message Is Nothing Then
            Return
        End If
        If message.MessageType = SociaChatType.Picture Then
            Me.DoExportMedia(message, Me.Language.GetString("Common.OpenFileDialog.PicFile") & " (*.png)|*.png")
        ElseIf message.MessageType = SociaChatType.Video Then
            Me.DoExportMedia(message, Me.Language.GetString("Setting.Label.VideoFile") & " (*.mp4)|*.mp4")
        ElseIf message.MessageType = SociaChatType.Audio Then
            Me.DoExportMedia(message, Me.Language.GetString("Weixin.OpenFileDialog.AmrFile") & " (*.opus)|*.opus")
        End If
    End Sub

    Private Sub ExportVoice(ByVal url As String)
        Try
            If Me.CheckChargeOverdue Then
                Return
            End If

            Dim message As WhatsAppMessage = Me.GetMessageForWeb(url)
            If message Is Nothing Then
                Return
            End If

            Dim fileDlg As SaveFileDialog = New SaveFileDialog()
            fileDlg.InitialDirectory = IniSetting.GetWeixinSaveFolder
            fileDlg.Filter = Me.Language.GetString("Weixin.OpenFileDialog.AmrFile") & " (*.wav)|*.wav"
            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = message.MessageDate.ToString("yyyyMMdd_HHmmss")
            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName
                '获取图片路径
                Dim imgPath As String = Me.mWhatsAppHelper.GetMediaPath(message)
                '拷贝到指定的目录
                If File.Exists(imgPath) Then
                    'Dim strFolderTempOnPC As String = String.Format("{0}", Folder.GetTempFilePath())
                    'Folder.CheckFolder(strFolderTempOnPC)
                    Dim strFileOpusOnPc As String = Path.Combine(Me.mPathWhatsAppOnPC_Temp, Path.GetFileNameWithoutExtension(imgPath) & ".opus")
                    File.Copy(imgPath, strFileOpusOnPc, True)

                    If Not File.Exists(strFilePath) Then
                        MediaMaster.ConvertWhatsappMedia(strFileOpusOnPc, strFilePath)
                    End If

                    Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                    Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！
                    If Not File.Exists(strFilePath) Then
                        strText = strFileName & Me.Language.GetString("Contact.Message.ExportFailure")   '导出失败！
                    End If

                    Me.ChangeStateText(strText)
                Else
                    Me.ChangeStateText(Me.Language.GetString("Contact.Message.ExportFailure")) '"导出失败"
                End If


            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportVoice")
        End Try
    End Sub

    Private Sub DoExportMedia(ByVal chat As WhatsAppMessage, ByVal strFilter As String)
        If chat IsNot Nothing Then
            Dim fileDlg As SaveFileDialog = New SaveFileDialog()
            fileDlg.InitialDirectory = IniSetting.GetWeixinSaveFolder
            fileDlg.Filter = strFilter
            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = chat.MessageDate.ToString("yyyyMMdd_HHmmss")

            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName
                '获取图片路径
                Dim imgPath As String = Me.mWhatsAppHelper.GetMediaPath(chat)
                '拷贝到指定的目录
                If File.Exists(imgPath) Then
                    File.Copy(imgPath, strFilePath, True)
                    Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                    Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！
                    Me.ChangeStateText(strText)
                Else
                    Me.ChangeStateText(Me.Language.GetString("Contact.Message.ExportFailure")) '"导出失败"
                End If
            End If
        End If
    End Sub

    '显示大图和地理位置
    Private Sub ShowPhotoAndLocation(ByVal url As String)
        If Me.CheckChargeOverdue() Then
            Return
        End If

        Dim message As WhatsAppMessage = Me.GetMessageForWeb(url)
        If message IsNot Nothing AndAlso message.MessageType = SociaChatType.Picture Then
            Dim imgPath As String = Me.mWhatsAppHelper.GetMediaPath(message)
            '没有可供预览的大图
            If File.Exists(imgPath) Then
                '展示图片
                Dim showPicFrm As New tbImageViewForm()
                showPicFrm.SrcPicture = Utility.GetImageFormFile(imgPath)
                showPicFrm.Icon = My.Resources.iTong
                showPicFrm.ViewType = ViewType.FromPC
                showPicFrm.ShowListView = False
                showPicFrm.HideButton = ToolBarButtonType.Main Or ToolBarButtonType.Delete Or ToolBarButtonType.WeiBo Or ToolBarButtonType.Prev Or ToolBarButtonType.Next
                showPicFrm.ShowDialog()
            Else
                Me.ChangeStateText(Me.Language.GetString("Weixin.Message.NoLargeImage"))  '"没有可供预览的大图"
            End If
        ElseIf message IsNot Nothing AndAlso message.MessageType = SociaChatType.Location Then
            'If message.Medias Is Nothing OrElse message.Medias.Count <= 0 Then
            '    Return
            'End If
            'Dim strURL As String = String.Format("http://map.baidu.com/?latlng={0},{1}&title=位置&content=位置&autoOpen=true", message.Medias(0).Latitude, message.Medias(0).Longitude)
            'Common.OpenExplorer(strURL)
        End If
    End Sub

    Private Delegate Sub ChangeStateHandler(ByVal Text As String)
    Private Sub ChangeStateText(ByVal Text As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ChangeStateHandler(AddressOf ChangeStateText), Text)
            Else
                Me.lblState.Text = Text
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_ChangeStateText")
        End Try
    End Sub

    Public Overrides Sub OnDisconnect(device As IDevice)
        '如果是从设备中进行查看断开设备就关闭这个窗体
        'If Me.mDevice IsNot Nothing AndAlso Me.mDevice.Identifier = device.DeviceID Then
        '    Me.Close()
        'End If
        MyBase.OnDisconnect(device)
        If Me.GetCurrentDevice() Is Nothing Then
            Me.btnGetNewBackup.Visible = False
        Else
            Me.btnGetNewBackup.Visible = True
        End If
    End Sub

    Public Overrides Sub OnConnect(device As CoreFoundation.IDevice)
        MyBase.OnConnect(device)
        If Me.GetCurrentDevice() Is Nothing Then
            Me.btnGetNewBackup.Visible = False
        Else
            Me.btnGetNewBackup.Visible = True
        End If
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            '结束备份线程
            Utility.AbortThread(Me.mThreadBackupDevice)
            Utility.AbortThread(Me.mThrLoadSession)
            Utility.AbortThread(Me.mThreadLoadMessage)
            Utility.AbortThread(Me.mThreadSearchMessage)

            If Me.mBackupHelper IsNot Nothing Then
                RemoveHandler Me.mBackupHelper.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged

                If Me.mBackupHelper.iDataRecoveryBackupFolder.Length <= 0 Then
                    Me.mBackupHelper.CancelBackup()
                End If
            End If

            If Me.mPlayer IsNot Nothing Then
                RemoveHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange
            End If
        Catch

        End Try
    End Sub

    Private Sub btnReloadData_Click(sender As Object, e As EventArgs)
        Me.LoadBackupInfo()
    End Sub

    Private Sub txtSearch_ClearClick(sender As Object, e As EventArgs) Handles txtSearch.ClearClick
        If Me.btnSelect.Text = Me.tsmiShowDelete.Text Then
            Me.dgvSession.Rows.Clear()
            Return
        End If
        Me.Search()
    End Sub

    Private Sub txtSearch_KeyDown(sender As Object, e As KeyEventArgs) Handles txtSearch.KeyDown
        If e.KeyCode <> Keys.Enter Then
            Return
        End If

        If Me.btnSelect.Text = Me.tsmiShowDelete.Text Then
            Me.dgvSession.Rows.Clear()
            Return
        End If
        Me.Search()
    End Sub

    Private Sub btnBack_Click(sender As Object, e As EventArgs) Handles btnBack.Click, btnBackLoading.Click
        Me.Close()
    End Sub

    Private Sub cbxSelectAll_CheckedChanged(sender As Object, e As EventArgs) Handles cbxSelectAll.CheckedChanged
        If Me.mSelectAllClick = True Then
            Return
        End If

        Me.mSelectAllClick = True
        For Each item As DataGridViewRow In Me.dgvSession.Rows
            If item.Selected <> Me.cbxSelectAll.Checked Then
                item.Selected = Me.cbxSelectAll.Checked
                item.Selected = Me.cbxSelectAll.Checked
            End If
        Next
        Me.mSelectAllClick = False
    End Sub

    Private Sub btnExportSetting_Click(sender As Object, e As EventArgs) Handles btnExportSetting.Click
        Dim frm As New frmWeChatSetting()
        frm.ShowDialog(Me)
    End Sub

    Private Sub btnSelect_Click(sender As Object, e As EventArgs) Handles btnSelect.Click
        Me.cmsShowSession.Show(Me.btnSelect, 0, Me.btnSelect.Height + 3)
    End Sub

    Private Sub tsmiShowAll_Click(sender As Object, e As EventArgs) Handles tsmiShowAll.Click
        Me.LoadSession()
        Me.btnSelect.Text = Me.tsmiShowAll.Text
    End Sub

    Private Sub tsmiShowDelete_Click(sender As Object, e As EventArgs) Handles tsmiShowDelete.Click
        Me.dgvSession.Rows.Clear()
        Me.btnSelect.Text = Me.tsmiShowDelete.Text
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.ShowClickSessionMessage(True, Me.Language.GetString("Weixin.Lable.InfoToSelect"), False)       '查看消息，请选中左侧联系人
    End Sub

    Private Sub tsmiShowNotDelete_Click(sender As Object, e As EventArgs) Handles tsmiShowNotDelete.Click
        Me.LoadSession()
        Me.btnSelect.Text = Me.tsmiShowNotDelete.Text
    End Sub

    Private Sub lblBackupInfo_SizeChanged(sender As Object, e As EventArgs) Handles lblBackupInfo.SizeChanged
        Me.btnGetNewBackup.Left = Me.lblBackupInfo.Right
    End Sub

    Private Sub DoExit()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf DoExit))
        Else
            Me.Close()
        End If
    End Sub

#End Region

#Region "----DataGrid----"

    Private Sub InitDataGridView()
        Me.dgvSession = New tbDataGridViewEx
        With Me.dgvSession
            .SuspendLayout()

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 58
            .ColumnHeadersVisible = False
            .tbShowNoData = False
            .MultiSelect = True
            .RowsDefaultCellStyle.BackColor = Color.White
            .AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(251, 251, 251)
            .RowsDefaultCellStyle.SelectionBackColor = Color.FromArgb(226, 244, 251)
            .BackgroundColor = Color.White
            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            '1、用户名（key）
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colBlank", "", 15, False, False, DataGridViewContentAlignment.MiddleLeft, True))

            '2、昵称（显示）
            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", "", _
                                                                                                                                                                      165, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            '设置样式
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.Font = Common.CreateFont(Me.Font.Name, 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black
            colName.DefaultCellStyle = cellStyle

            .Columns.Add(colName)

            '3、时间
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colTime", "", _
                                                                                                          50, True, True, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colMD5", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))

            .ResumeLayout()
        End With

        RemoveHandler dgvSession.SelectionChanged, AddressOf dgvSession_SelectionChanged
        AddHandler dgvSession.SelectionChanged, AddressOf dgvSession_SelectionChanged

        Utility.AddForm2Panel(Me.dgvSession, Me.pnlSession)
        Me.dgvSession.BringToFront()
    End Sub

    Private Sub dgvSession_SelectionChanged(ByVal sender As Object, ByVal e As EventArgs)
        If Me.mSelectAllClick = True Then
            Return
        End If

        Dim lstRows As List(Of DataGridViewRow) = Me.dgvSession.SelectedRows()
        If lstRows.Count <= 0 Then
            Me.mCurrentDataGridViewRow = Nothing
            Return
        End If
        Dim row As DataGridViewRow = lstRows(0)

        Try
            If Me.mCurrentDataGridViewRow IsNot Nothing AndAlso Me.mCurrentDataGridViewRow.Tag IsNot Nothing Then
                Dim newInfo As WhatsAppSession = CType(row.Tag, WhatsAppSession)
                Dim curInfo As WhatsAppSession = CType(Me.mCurrentDataGridViewRow.Tag, WhatsAppSession)

                If newInfo IsNot Nothing AndAlso curInfo IsNot Nothing AndAlso newInfo.ChatName = curInfo.ChatName Then
                    Return
                End If
            End If
            Me.mCurrentDataGridViewRow = row
        Catch ex As Exception

        End Try

        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        If Me.dgvSession.RowCount <= 0 Then
            Me.ShowClickSessionMessage(True, Me.Language.GetString("Weixin.Message.SearchNoResult"), True)   ' '"搜索不到结果"
            Return
        End If

        If lstRows.Count <= 0 Then
            Me.ShowClickSessionMessage(True, Me.Language.GetString("Weixin.Lable.InfoToSelect"), False)       '查看消息，请选中左侧联系人
            Return
        End If

        Me.SetWebBackColor()
        Me.mCurrentDataGridViewRow = row
        Me.LoadMessage(row)
        Me.mSelectAllClick = True
        If Not Me.mIsAddRow AndAlso Me.dgvSession.Rows.Count > 0 AndAlso Me.dgvSession.SelectedRows IsNot Nothing AndAlso Me.dgvSession.SelectedRows.Count = Me.dgvSession.Rows.Count Then
            Me.cbxSelectAll.Checked = True
        Else
            Me.cbxSelectAll.Checked = False
        End If
        Me.mSelectAllClick = False
    End Sub

    Private Sub SetWebBackColor()
        Dim writer As New iTong.CoreFoundation.JsonWriter()
        Dim dicColor As New JsonObject
        dicColor.Add("bgcolor", "#fff")
        Dim dicConfig As New JsonObject
        dicConfig.Add("config", dicColor)
        dicConfig.Write(writer)
        ChatWebPage.ShowChatOnWebPage(Me.wbsChat, writer.ToString())
    End Sub

#End Region

#Region "----加载聊天列表----"

    Private Sub LoadSession()
        Try
            Utility.AbortThread(Me.mThrLoadSession)
            Me.mThrLoadSession = New Thread(AddressOf LoadSessionThread)
            With Me.mThrLoadSession
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_LoadSession")
        End Try
    End Sub

    Private Sub LoadSessionThread()
        Try
            If Me.mIsFirstLoad Then
                ChatWebPage.InitChatWebPage(False)
                Me.wbsChat.WebBrowserShortcutsEnabled = True
                Me.wbsChat.Navigate(ChatWebPage.WebPagePath)
                Me.mIsFirstLoad = False
            End If

#If IS_RECOVERY Then
            Dim frmMain As MainFormDataRecovery = CType(Me.mApplication, MainFormDataRecovery)
            Dim info As BackupInfo = Me.mBackupInfo
            If Me.mBackupInfo Is Nothing Then
                info = New BackupInfo(Me.mBackupPath)
            End If
            Dim isCancel As Boolean = False
            If Not frmMain.CheckIsEncrypted(info, isCancel, False, "", Me.mBackupHelper) Then
                If isCancel Then
                    Me.DoExit()
                Else
                    Me.SetViewStyle(WhatsAppStatus.ShowError)
                End If
                Return
            End If

            Dim strEncryptedBackup As String = ""
            If info.IsEncrypted AndAlso Directory.Exists(Path.Combine(info.FolderPath, iPhoneDevice.Unback_Dir)) Then
                info = New BackupInfo(Path.Combine(info.FolderPath, iPhoneDevice.Unback_Dir))
                Me.mBackupPath = info.FolderPath
                Me.mBackupInfo = info
            End If
#End If
            Me.UnInstalledCloseMe()

            If Me.mWhatsAppHelper Is Nothing Then
                Me.mWhatsAppHelper = New WhatsAppHelper(Me.mBackupPath)
            End If
            Dim lsrSession As List(Of WhatsAppSession) = Me.mWhatsAppHelper.GetSessions()
            Me.AddDictToDataGridView(lsrSession)
            Me.SetViewStyle(WhatsAppStatus.Message)
            Me.LoadSessionIcon()
            Me.ShowClickSessionMessage(True, Me.Language.GetString("Weixin.Lable.InfoToSelect"), False)       '查看消息，请选中左侧联系人
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_LoadSessionThread")
        End Try
    End Sub

    Private Delegate Function AddDictToDataGridViewHandler(ByVal listSession As List(Of WhatsAppSession)) As List(Of tbDataGridViewRow)
    Private Function AddDictToDataGridView(ByVal listSession As List(Of WhatsAppSession)) As List(Of tbDataGridViewRow)
        Dim rowFindData As New List(Of tbDataGridViewRow)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New AddDictToDataGridViewHandler(AddressOf AddDictToDataGridView), New Object() {listSession})
            Else
                Me.dgvSession.SuspendLayout()
                Me.dgvSession.Rows.Clear()
                For Each item As WhatsAppSession In listSession
                    Try
                        Dim row As New tbDataGridViewRow()
                        row.Height = 58

                        Dim strName As String = item.ChatName
                        Dim message As WhatsAppMessage = Me.mWhatsAppHelper.GetMessage(item.SessionIdZPK, item.LastMsgId)
                        Dim strLastMessage As String = String.Empty
                        If message IsNot Nothing Then
                            strLastMessage = message.Text
                        End If

                        row.CreateCells(Me.dgvSession, _
                                        False, _
                                        "", _
                                        strName & vbCrLf & Common.StringFormat(strLastMessage, 15, True), _
                                        Me.GetLastMessageTime(item.LastMessageDate) & vbCrLf & " ", _
                                        "", _
                                        item.SessionIdZPK)
                        Me.mIsAddRow = True
                        Me.dgvSession.Rows.Add(row)
                        Me.mIsAddRow = False

                        Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                        cellName.tbIconShadow = True
                        cellName.tbIconSize = Me.mSessionIconSize
                        cellName.tbIcon = Me.mDefaultHeadIcon
                        Dim isLock As Boolean = False
                        cellName.ShowWechatMessageLock = isLock
                        row.Tag = item
                        Application.DoEvents()
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "AddDictToDataGridView")
                    End Try
                Next
                Me.dgvSession.ResumeLayout()
                Me.mCurrentDataGridViewRow = Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
        Return rowFindData
    End Function

    Private Function GetLastMessageTime(ByVal LastChatTime As DateTime) As String
        Dim strTime As String = String.Empty
        Dim ts As TimeSpan = Now.Date.Subtract(LastChatTime.Date) '只需比较日期就好
        Try
            Select Case ts.Days
                Case 0
                    strTime = LastChatTime.ToString("HH:mm")
                Case 1
                    strTime = Me.Language.GetString("Note.Label.Yesterday") '"昨天"
                Case Else
                    Dim strYear As String = LastChatTime.Year.ToString().Substring(2, 2)
                    Dim strMonth As String = LastChatTime.Month.ToString()
                    Dim strDay As String = LastChatTime.Day.ToString()
                    strTime = String.Format("{0}-{1}-{2}", strMonth, strDay, strYear)
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetLastChatTime")
        End Try
        Return strTime
    End Function

    Private Sub LoadSessionIcon()
        For Each item As tbDataGridViewRow In dgvSession.Rows
            If item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is WhatsAppSession Then
                Dim session As WhatsAppSession = CType(item.Tag, WhatsAppSession)
                session.ChatImagePath = Me.mWhatsAppHelper.GetImageBySession(session)
                Dim cellName As tbDataGridViewTextBoxCellEx = item.Cells("colName")
                If File.Exists(session.ChatImagePath) Then
                    Try
                        Dim img As Image = Utility.GetImageFormFile(session.ChatImagePath)
                        cellName.tbIcon = img
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "frmWhatsApp_LoadSessionIcon")
                    End Try
                End If

            End If
        Next
    End Sub

    Private Delegate Sub ShowClickSessionMessageHandler(ByVal blnShow As Boolean, ByVal strText As String, ByVal isNoSearchContent As Boolean)
    Private Sub ShowClickSessionMessage(ByVal blnShow As Boolean, ByVal strText As String, Optional ByVal isNoSearchContent As Boolean = False)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ShowClickSessionMessageHandler(AddressOf ShowClickSessionMessage), New Object() {blnShow, strText, isNoSearchContent})
            Else

                Me.lblClickSession.Visible = blnShow
                Me.lblClickSession.BringToFront()
                Me.lblClickSession.Text = strText
                If isNoSearchContent Then
                    Me.picNoSearchContent.Visible = True
                    Me.picNoSearchContent.BringToFront()
                Else
                    Me.picNoSearchContent.Visible = False
                End If

                If Not Me.picNoSearchContent.Visible Then
                    Me.lblClickSession.Location = New Point((Me.wbsChat.Width - Me.lblClickSession.Width) / 2 + Me.pnlSession.Width, (Me.wbsChat.Height - Me.lblClickSession.Height) / 2)
                Else
                    Me.picNoSearchContent.Location = New Point((Me.wbsChat.Width - Me.picNoSearchContent.Width) / 2 + Me.pnlSession.Width, (Me.wbsChat.Height - Me.picNoSearchContent.Height + Me.lblClickSession.Height) / 2)
                    Me.lblClickSession.Location = New Point((Me.wbsChat.Width - Me.lblClickSession.Width) / 2 + Me.pnlSession.Width, Me.picNoSearchContent.Bottom)
                End If

            End If
        Catch ex As Exception

        End Try
    End Sub

#End Region

#Region "----加载消息----"

    Private Sub LoadMessage(ByVal row As DataGridViewRow)
        If row Is Nothing Then
            Return
        End If

        Dim Item As WhatsAppSession = CType(row.Tag, WhatsAppSession)
        Me.LoadMessage(Item, -1, LoadDataType.Normal)
    End Sub

    Private Sub LoadMessage(ByVal item As WhatsAppSession, ByVal intIndex As Integer, ByVal type As LoadDataType)
        Try
            Utility.AbortThread(Me.mThreadLoadMessage)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_LoadMessage")
        End Try

        Dim objPara() As Object = New Object() {item, intIndex, type}
        Me.mThreadLoadMessage = New Thread(New ParameterizedThreadStart(AddressOf LoadMessageThread))
        With Me.mThreadLoadMessage
            .IsBackground = True
            .Start(objPara)
        End With
    End Sub

    Private Sub LoadMessageThread(ByVal objPara As Object)
        Try
            Dim session As WhatsAppSession = objPara(0)
            Dim intIndes As Integer = objPara(1)
            Dim type As LoadDataType = objPara(2)
            Me.ShowMessageOnWeb(session, intIndes, type)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_LoadMessageThread")
        End Try
    End Sub

    Private Sub ShowMessageOnWeb(ByVal session As WhatsAppSession, ByVal intIndex As Integer, ByVal type As LoadDataType)
        If session Is Nothing Then
            Return
        End If
        Dim lstMessage As List(Of WhatsAppMessage) = Me.mWhatsAppHelper.GetMessage(session.SessionIdZPK, intIndex, Me.mShowMessageCount, session.SearchText)
        Dim strJson As String = Me.GetMessageJson(session, lstMessage, type)
        Me.ShowMessageOnWebPage(strJson)
        If type = LoadDataType.GetNews OrElse type = LoadDataType.Normal Then
            If session.FirstMessage IsNot Nothing AndAlso session.FirstMessage.MessageIdZPK >= intIndex AndAlso intIndex > 0 Then
                ChatWebPage.ShowMoreButton(Me.wbsChat, False)

            ElseIf lstMessage.Count < Me.mShowMessageCount Then
                ChatWebPage.ShowMoreButton(Me.wbsChat, False)

            Else
                ChatWebPage.ShowMoreButton(Me.wbsChat, True)

            End If
        End If
        Me.ShowClickSessionMessage(False, "")
    End Sub

    Private Function GetMessageJson(ByVal session As WhatsAppSession, ByVal lstMessage As List(Of WhatsAppMessage), ByVal type As LoadDataType) As String
        Dim writer As New iTong.CoreFoundation.JsonWriter()
        Try
            Dim arrChats As New JsonArray
            Dim dicChat As JsonObject

            Dim strFromForWeb As String = String.Empty   '0：对方，1：自己
            Dim strTypeForWeb As String = String.Empty    '0：文本，1：语音，2：图片，3：网页，4：系统消息
            Dim iconPath As String = String.Empty
            Dim strNickName As String = String.Empty
            Dim strMsgForWeb As String = String.Empty
            Dim strMediaMarkTitle As String = String.Empty
            Dim arrHtml5ContentForWeb As JsonArray
            Dim strTimeForWeb As String = String.Empty
            Dim startTime As DateTime = DateTime.MinValue
            Dim isChatRoom As Boolean = session.GroupInfoId     '判断是否为群组

            '倒叙
            For Each Item As WhatsAppMessage In lstMessage
                '1、来源与用户名
                strFromForWeb = "0"
                strNickName = String.Empty
                strMsgForWeb = Item.Text  '群组的信息里面包括联系人名称
                strMediaMarkTitle = String.Empty

                If Item.IsFromMe = 1 Then
                    strFromForWeb = "1"
                Else
                    strFromForWeb = "0"
                    '如果是群组，则需要取每一个人的用户名
                    If isChatRoom Then
                        Dim values As String() = strMsgForWeb.Split(New Char() {vbCr, vbLf})

                        If strMsgForWeb.Contains("videomsg") Then
                            Try
                                If values.Length > 2 Then
                                    Dim strVideomsg As String = values(2)
                                    If strVideomsg.Contains("fromusername=") Then
                                        Dim strTemp As String = strVideomsg.Substring(strVideomsg.IndexOf("fromusername="), strVideomsg.Length - strVideomsg.IndexOf("fromusername="))
                                        strTemp = strTemp.Substring(strTemp.IndexOf("""") + 1)
                                    End If
                                End If
                            Catch
                            End Try
                        End If
                        If values.Length > 1 Then
                            Dim strMsgTemp As String = String.Empty
                            For index As Integer = 1 To values.Length - 1
                                If String.IsNullOrEmpty(strMsgTemp) Then
                                    strMsgTemp = values(index)
                                Else
                                    strMsgTemp = strMsgTemp & vbCrLf & values(index)
                                End If
                            Next

                            If strMsgTemp.Length > 0 Then
                                strMsgForWeb = strMsgTemp
                            End If
                        End If
                    End If
                End If

                '2、昵称与头像
                iconPath = Me.GetHeadIconPathFromPC(Item)
                strNickName = Item.PushName

                '3、消息类型与内容
                'web页 0文本，1语音，2图片，3网页
                strTypeForWeb = "0"
                arrHtml5ContentForWeb = Nothing

                Select Case Item.MessageType
                    Case SociaChatType.Text
                        strTypeForWeb = "0"
                        '检查是否有url
                        Dim strCheckIsContainUrl As String = strMsgForWeb
                        Dim blnHighlight As Boolean = True
                        If String.Compare(strCheckIsContainUrl, strMsgForWeb) <> 0 Then
                            blnHighlight = False
                            strMsgForWeb = strCheckIsContainUrl
                        End If

                        '表情符号转换成表情图片
                        strMsgForWeb = Me.OperateForExpression(strMsgForWeb)

                        '搜索加高亮效果（搜索只搜索文本类型），数据库搜索不区分大小写
                        If Not String.IsNullOrEmpty(session.SearchText) Then
                            strMsgForWeb = strMsgForWeb.Replace(session.SearchText, "~" & session.SearchText & "~(background-color:#ffff00;color:#fffff;)")
                        End If

                    Case SociaChatType.Audio
                        strTypeForWeb = "1"
                        strMsgForWeb = Item.Medias(0).Duration

                    Case SociaChatType.Picture
                        strTypeForWeb = "6"
                        strMsgForWeb = Me.GetMediaThemb(Item)  '图片路径
                        strMediaMarkTitle = Me.GetMediaMarkTitle(Item)
                        If String.IsNullOrEmpty(strMediaMarkTitle) Then
                            strTypeForWeb = "2"
                        End If

                    Case SociaChatType.Video
                        strTypeForWeb = "7"
                        strMsgForWeb = Me.GetMediaThemb(Item)
                        strMediaMarkTitle = Me.GetMediaMarkTitle(Item)
                        If String.IsNullOrEmpty(strMediaMarkTitle) Then
                            strTypeForWeb = "5"
                        End If

                    Case SociaChatType.NameCard
                        strMsgForWeb = Me.GetMediaVcardName(Item) & vbCrLf & "[" & Me.Language.GetString("Weixin.Item.BusinessCard") & "]"        '"[名片]"

                    Case SociaChatType.Location
                        strTypeForWeb = "2"
                        strMsgForWeb = Me.GetMediaThemb(Item)

                    Case SociaChatType.Linkman
                        strTypeForWeb = "4"
                        strMsgForWeb = Item.Text

                    Case SociaChatType.File
                        strTypeForWeb = "0"
                        strMsgForWeb = "[文件]" & Item.Text

                    Case SociaChatType.StartChat
                        Continue For

                End Select

                dicChat = New JsonObject()
                dicChat.Add("from", strFromForWeb)
                dicChat.Add("type", strTypeForWeb)
                dicChat.Add("user", strNickName)
                dicChat.Add("icon", iconPath)

                If arrHtml5ContentForWeb IsNot Nothing Then
                    dicChat.Add("content", arrHtml5ContentForWeb)
                Else
                    If Not String.IsNullOrEmpty(strMediaMarkTitle) AndAlso Not String.IsNullOrEmpty(strMsgForWeb) Then
                        Dim dicContent As JsonObject = New JsonObject()
                        dicContent.Add("text", strMediaMarkTitle)
                        dicContent.Add("img", strMsgForWeb)
                        dicChat.Add("content", dicContent)
                    Else
                        dicChat.Add("content", strMsgForWeb)
                    End If
                End If

                strTimeForWeb = String.Empty
                If startTime = DateTime.MinValue OrElse Item.MessageDate.Subtract(startTime).TotalMinutes > 5 Then
                    startTime = Item.MessageDate
                    strTimeForWeb = Item.MessageDate.ToString("MM-dd HH:mm:ss")
                End If

                dicChat.Add("time", strTimeForWeb)
                dicChat.Add("friend", session.SessionIdZPK)
                dicChat.Add("id", Item.MessageIdZPK)
                '标志是否删除 1：是  0：否  
                Dim intDelete = 0
                If Item.DeleteType = WhatsAppDeleteType.DeleteMessage Then
                    intDelete = 1
                End If
                dicChat.Add("delete", intDelete)
                arrChats.Add(dicChat)
            Next

            '添加语言包
            Dim dicLanguage As New JsonObject
            dicLanguage.Add("weixincopy", Me.Language.GetString("Common.Button.Copy"))
            dicLanguage.Add("weixinexport", Me.Language.GetString("Media.Button.Export"))
            dicLanguage.Add("weixingetmore", Me.Language.GetString("Weixin.Button.GetMoreNews"))
            dicLanguage.Add("weixingetcontext", Me.Language.GetString("Weixin.Button.GetContextNews"))

            Dim dicOption As New JsonArray
            dicOption.Add("weixincopy")
            dicOption.Add("weixinexport")

            Dim dicConfig As New JsonObject
            dicConfig.Add("bgcolor", "#fff")

            Dim dicChats As New JsonObject
            dicChats.Add("type", type.GetHashCode.ToString())
            dicChats.Add("data", arrChats)
            dicChats.Add("language", dicLanguage)
            dicChats.Add("msgOption", dicOption)
            dicChats.Add("config", dicConfig)
            dicChats.Write(writer)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetChatJson")
        End Try
        Return writer.ToString()
    End Function

    'Private Function GetLinkManString(ByVal message As WhatsAppMessage) As String
    '    Dim strReturn As String = String.Empty
    '    If message Is Nothing OrElse message.GroupEventType = WhatsAppGroupEventType.None Then
    '        Return strReturn
    '    End If
    '    Select Case message.GroupEventType
    '        Case WhatsAppGroupEventType.Add
    '            Dim strAdmin As String = "您"
    '            Dim strInvitation As String = "您"
    '            Dim memberAdmin As WhatsAppGroupMember = message.GetMember(message.Text)
    '            If memberAdmin IsNot Nothing Then
    '                strAdmin = memberAdmin.PushName
    '            End If
    '            If message.Member IsNot Nothing Then
    '                strInvitation = message.Member.PushName
    '                If strInvitation.Length <= 0 Then
    '                    strInvitation = message.Member.MemberJid
    '                End If
    '            End If
    '            strReturn = String.Format("{0} 添加了 {1}", strAdmin, strInvitation)

    '        Case WhatsAppGroupEventType.Quit
    '            If message.Member IsNot Nothing Then
    '                strReturn = message.Member.PushName
    '                If String.IsNullOrEmpty(strReturn) Then
    '                    strReturn = message.Member.MemberJid
    '                End If
    '            End If
    '            strReturn = String.Format("{0} 退出了", strReturn)

    '        Case WhatsAppGroupEventType.CreateGroup
    '            If message.Member IsNot Nothing Then
    '                strReturn = String.Format("{0} 创建了 ""{1}"" 群组", message.Member.PushName, message.Text)
    '            Else
    '                strReturn = String.Format("您 创建了 ""{0}"" 群组", message.Text)
    '            End If

    '        Case WhatsAppGroupEventType.Invitation
    '            If message.Member IsNot Nothing Then
    '                strReturn = String.Format("{0} 使用了群组邀请连结加入群组", message.Member.PushName)
    '            End If

    '    End Select


    '    Return strReturn
    'End Function

    Private Function GetMediaThemb(ByVal Item As WhatsAppMessage) As String
        Dim strReturn As String = ""
        If Item IsNot Nothing AndAlso Item.Medias IsNot Nothing AndAlso Item.Medias.Count >= 1 Then
            strReturn = Me.mWhatsAppHelper.GetImageByJID(Item.Medias(0).ThumbPathOnPhone)
        End If
        Return strReturn
    End Function

    Private Function GetMediaMarkTitle(ByVal Item As WhatsAppMessage) As String
        Dim strReturn As String = ""
        If Item IsNot Nothing AndAlso Item.Medias IsNot Nothing AndAlso Item.Medias.Count >= 1 Then
            strReturn = Item.Medias(0).MarkTitle
        End If
        Return strReturn
    End Function

    Private Function GetMediaVcardName(ByVal Item As WhatsAppMessage) As String
        Dim strReturn As String = ""
        If Item IsNot Nothing AndAlso Item.Medias IsNot Nothing AndAlso Item.Medias.Count >= 1 Then
            strReturn = Item.Medias(0).VcardName
        End If
        Return strReturn
    End Function

    '获取头像（从本地）
    Private Function GetHeadIconPathFromPC(ByVal item As WhatsAppMessage) As String
        Dim imgPath As String = String.Format("{0}\default.pic_usr", Me.mPathWhatsAppOnPC_Cache)
        '释放默认图片
        If Not File.Exists(imgPath) Then
            Try
                '暂时无法解决， 这个图片有问题， save的时候会异常
                Me.mDefaultHeadIcon.Clone.Save(imgPath)
            Catch
            End Try
        End If

        '取得头相名称
        Dim strJID As String = String.Empty
        If item IsNot Nothing AndAlso item.Member IsNot Nothing Then
            strJID = item.Member.MemberJid
        End If

        If item.IsFromMe <> 1 AndAlso String.IsNullOrEmpty(strJID) Then
            strJID = item.FromJID
        End If

        '本要头相默认值
        If item.IsFromMe = 1 Then
            strJID = "Photo."
        End If

        If String.IsNullOrEmpty(strJID) Then
            Return imgPath
        End If

        Dim imgPathTemp As String = String.Format("{0}\{1}.pic_usr", Me.mPathWhatsAppOnPC_Cache, strJID)
        Dim strIconPathOnPhone As String = Me.mWhatsAppHelper.GetIconPath(strJID)
        Dim strIconPath As String = String.Empty
        If strIconPathOnPhone.Length > 0 Then
            strIconPath = Me.mWhatsAppHelper.GetImageByJID(strIconPathOnPhone)
        Else
            strIconPath = Me.mWhatsAppHelper.GetImageByJID(strJID)
        End If

        Try
            If Not File.Exists(imgPathTemp) AndAlso File.Exists(strIconPath) Then
                File.Copy(strIconPath, imgPathTemp)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_GetHeadIconPathFromPC")
        End Try

        If File.Exists(imgPathTemp) Then
            imgPath = imgPathTemp
        End If
        Return imgPath
    End Function

    Private Function OperateForExpression(ByVal strContent As String) As String
        'Dim regular As String = "\[(?<expression>.*?)\]"
        'Dim dt As DataTable = Utility.GetMatchStringByRegularExpressions(strContent, New String(0) {regular}, New String(0) {"expression"})

        'If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
        '    For Each dr As DataRow In dt.Rows
        '        Dim strKey As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("expression").ToString()))
        '        strKey = "[" & strKey & "]"

        '        Dim strExpressionPath As String = Me.GetExpressionPath(strKey)

        '        If Not String.IsNullOrEmpty(strExpressionPath) Then
        '            strContent = strContent.Replace(strKey, strExpressionPath)
        '        End If
        '    Next
        'End If

        Return strContent
    End Function

    'web页聊天信息的展示
    Private Sub ShowMessageOnWebPage(ByVal strJson As String)
        ChatWebPage.ShowChatOnWebPage(Me.wbsChat, strJson)
        ChatWebPage.ShowMoreButton(Me.wbsChat, False)
    End Sub

#End Region

#Region "----导出消息----"


    Private Sub tsmiExcelEx_Click(sender As Object, e As EventArgs) Handles tsmiExcelEx.Click
        Me.NewExport(ExportType.ExportToExcel)
    End Sub

    Private Sub tsmiTxtEx_Click(sender As Object, e As EventArgs) Handles tsmiTxtEx.Click
        Me.NewExport(ExportType.ExportToTxt)
    End Sub

    Private Sub tsmiHtml_Click(sender As Object, e As EventArgs) Handles tsmiHtml.Click
        Me.NewExport(ExportType.ExportToHtml)
    End Sub

    Private Sub NewExport(eType As ExportType, Optional ByVal blnRecoveryData As Boolean = False)
        If Me.CheckChargeOverdue() Then
            Return
        End If

        'Dim lstRows As List(Of DataGridViewRow) = Nothing
        'Dim lstTime As New List(Of DateTime)
        'Dim strDeviceName As String = ""
        'Dim obj As Object() = Nothing

        'If Not CheckExport(lstRows, lstTime, strDeviceName, obj, blnRecoveryData) Then
        '    Return
        'End If

        'Dim strPath As String = Me.mWhatsAppImportOrExportHelper.ExportHtml(lstRows, Nothing, lstTime, Me.mWhatsAppHelper.GetUserName(), strDeviceName, eType, obj, blnRecoveryData, Me.mBackupDeviceUDID)

        Dim para As IMExportPara = Me.CheckExport()
        If para Is Nothing Then
            Return
        End If

        Me.ShowExportProgressNew(para)
    End Sub

    Private Function CheckExport(Optional blnRecoveryData As Boolean = False) As IMExportPara
        Dim para As IMExportPara = Nothing

        Try
            If Me.dgvSession.Rows.Count = 0 Then
                '联系人为空。
                tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                GoTo DoExit
            End If

            If Me.dgvSession Is Nothing OrElse Me.dgvSession.SelectedRows.Count <= 0 Then
                tbMessageBox.Show(Me.mApplication, _
                                 Me.Language.GetString("WeChat.Button.SelectContact"), _
                                  Me.Language.GetString("Common.Info"), _
                                  MessageBoxButtons.OK, _
                                   MessageBoxIcon.Warning)
                '"请选择要导出的联系人"
                GoTo DoExit
            End If

            para = New IMExportPara()

            '查询时间
            'para.DateStart = Me.dtpStart.Value.ToString("yyyy-MM-dd 00:00:01")
            'para.DateEnd = Me.dtpEnd.Value.ToString("yyyy-MM-dd 23:59:59")

            For Each row As DataGridViewRow In Me.dgvSession.SelectedRows
                para.ListExport.Add(row.Tag)
            Next

            'obj = New Object() {String.Empty, False, 0, True, False, "", Nothing, String.Empty, String.Empty, Me.mBackupPath, Nothing, Me.mWhatsAppHelper.WhatsAppRecodes}

        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_CheckExport")
        End Try

DoExit:
        Return para
    End Function

    Private Sub tsmiPicEx_Click(sender As Object, e As EventArgs) Handles tsmiPicEx.Click
        Me.ExportAllMedia("Picture")
    End Sub

    Private Sub tsmiAudioEx_Click(sender As Object, e As EventArgs) Handles tsmiAudioEx.Click
        Me.ExportAllMedia("Audio")
    End Sub

    Private Sub tsmiVideoEx_Click(sender As Object, e As EventArgs) Handles tsmiVideoEx.Click
        Me.ExportAllMedia("Video")
    End Sub

    Private Sub tsmiAllEx_Click(sender As Object, e As EventArgs) Handles tsmiAllEx.Click, btnExport.Click
        Try
            'Me.NewExport(ExportType.ExportToExcel)
            'Me.NewExport(ExportType.ExportToTxt)
            'Me.NewExport(ExportType.ExportToHtml)
            If Me.CheckChargeOverdue() Then
                Return
            End If

            'Dim lstRows As List(Of DataGridViewRow) = Nothing
            'Dim lstTime As New List(Of DateTime)
            'Dim strDeviceName As String = ""
            'Dim obj As Object() = Nothing

            'If Not CheckExport(lstRows, lstTime, strDeviceName, obj, False) Then
            '    Return
            'End If

            'Dim strPath As String = Me.mWhatsAppImportOrExportHelper.ExportWhatsAppAllInfo(lstRows, Nothing, lstTime, Me.mWhatsAppHelper.GetUserName(), strDeviceName, Nothing, obj, False, Me.mBackupDeviceUDID)
            'If Not String.IsNullOrEmpty(strPath) Then
            'ShowExportProgressNew(strPath)
            'End If

            Dim para As IMExportPara = Me.CheckExport()
            If para Is Nothing Then
                Return
            End If

            Me.ShowExportProgressNew(para)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "tsmiAllEx_Click")
        End Try
    End Sub

    Private Sub ExportAllMedia(ByVal strType As String)

        If Me.CheckChargeOverdue() Then
            Return
        End If

        'Dim lstRows As List(Of DataGridViewRow) = Nothing
        'Dim lstTime As New List(Of DateTime)
        'Dim strDeviceName As String = ""
        'Dim obj As Object() = Nothing

        'If Not CheckExport(lstRows, lstTime, strDeviceName, obj) Then
        '    Return
        'End If

        'Dim strPath As String = Me.mWhatsAppImportOrExportHelper.ExportMedia(lstRows, lstTime, strDeviceName, strType, Me.mWhatsAppHelper.GetUserName(), obj, Nothing)
        'If Not String.IsNullOrEmpty(strPath) Then
        '    ShowExportProgressNew(strPath)
        'End If

        Dim para As IMExportPara = Me.CheckExport()
        If para Is Nothing Then
            Return
        End If

        Me.ShowExportProgressNew(para)
    End Sub

    Private Sub ShowExportProgressNew(para As IMExportPara)
        Try
            Dim frmExport As New frmExportProgress(Me.mApplication, Me.mHelper, para)
            frmExport.ShowDialog(Me)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowExportProgress")
        End Try
    End Sub

#End Region

#Region "----查找----"

    Private Sub Search()
        Try
            Utility.AbortThread(Me.mThreadSearchMessage)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWhatsApp_Search")
        End Try

        Me.mThreadSearchMessage = New Thread(New ThreadStart(AddressOf SearchThread))
        With Me.mThreadSearchMessage
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub SearchThread()
        Dim lsrSession As List(Of WhatsAppSession) = Me.mWhatsAppHelper.SearchSessions(Me.txtSearch.Text)
        Me.AddDictToDataGridView(lsrSession)
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.SetViewStyle(WhatsAppStatus.Message)
        If lsrSession Is Nothing OrElse lsrSession.Count = 0 Then
            Me.ShowClickSessionMessage(True, Me.Language.GetString("Weixin.Message.SearchNoResult"), True)   ' '"搜索不到结果"
            '搜索不到结果
            Return
        Else
            Me.LoadSessionIcon()
            Me.ShowClickSessionMessage(True, Me.Language.GetString("Weixin.Lable.InfoToSelect"), False)       '查看消息，请选中左侧联系人
        End If
    End Sub

#End Region

#Region "----收费逻辑----"

    Private Function CheckChargeOverdue(Optional ByVal showMsg As Boolean = True) As Boolean
        Return Not Me.mChargeHelper.CheckDeviceRegistered(Me.mBackupInfo.Identifier)
    End Function

#End Region

#Region "--- 语音---"

    Private Sub PlayVoice(ByVal url As String)
        Try
            If Me.CheckChargeOverdue Then
                Return
            End If

            Dim message As WhatsAppMessage = Me.GetMessageForWeb(url)
            If message Is Nothing Then
                Return
            End If
            Dim voicePath As String = Me.mWhatsAppHelper.GetMediaPath(message)
            If Not File.Exists(voicePath) Then
                Dim strError As String = Me.Language.GetString("Weixin.Message.PlayFail")
                Me.ChangeStateText(strError) '"播放语音失败！"
                Return
            End If


            Dim outPutFile As String = Path.Combine(Me.mPathWhatsAppOnPC_Temp, message.MessageIdZPK & ".wav")
            If Not File.Exists(outPutFile) Then
                MediaMaster.ConvertWhatsappMedia(voicePath, outPutFile)
            End If

            If File.Exists(outPutFile) Then
                Dim blnNeedPlay As Boolean = True

                If Me.mPlayer.PlayState = WMPPlayState.wmppsPlaying Then
                    Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL

                    'web页面暂停之前的
                    If String.Compare(strFilePlaying, outPutFile, True) = 0 Then
                        blnNeedPlay = False         '如果正在播放，播放的是同一首歌，则暂停
                    Else
                        blnNeedPlay = True          '如果是新的语音，则播放新的语音
                    End If

                    Me.mPlayer.Pause()
                    Me.ShowAudioWebView(strFilePlaying, False)
                End If

                If blnNeedPlay Then
                    'Debug.Print("播放")
                    Me.mPlayer.GetUserPlaylist(Me.Name).Clear()
                    Me.mPlayer.GetUserPlaylist(Me.Name).Add(outPutFile)
                    'Me.mPlayer.GetUserPlaylist(Me.Name).SetItem(0, strWavFile)
                    Me.mPlayer.CurrentUserPlaylist = Me.mPlayer.GetUserPlaylist(Me.Name)
                    Me.mPlayer.Play()
                    Me.ShowAudioWebView(message.MessageIdZPK, True)
                End If
            Else
                Dim strError As String = Me.Language.GetString("Weixin.Message.PlayFail") & " " & String.Format(Me.Language.GetString("Download.Message.ContactMe"), Utility.TongbuContact, Utility.TongbuTel)     '"如有疑问，请联系客服QQ：{0}    电话：{1}"
                Me.ChangeStateText(strError) '"播放语音失败！"
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "PlayVoice")
        End Try
    End Sub

    '播放语音停止事件
    Private Sub OnPlayStateChange(ByVal sender As Object, ByVal e As MediaPlayStateChangedEventArgs)
        If e.NewPlayState = WMPPlayState.wmppsStopped Then
            If Me.mPlayer.CurrentMedia IsNot Nothing Then
                Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL
                Me.mPlayer.GetUserPlaylist(Me.Name).Remove(strFilePlaying)
                Me.ShowAudioWebView(strFilePlaying, False)
            End If
        End If
    End Sub

    Private Sub ShowAudioWebView(ByVal strWavFile As String, ByVal blnPlay As Boolean)
        If String.IsNullOrEmpty(strWavFile) Then
            Return
        End If

        Dim strID As String = Path.GetFileNameWithoutExtension(strWavFile)

        ChatWebPage.ControlVoiceView(Me.wbsChat, strID, blnPlay)
    End Sub
#End Region

    Private Function GetCurrentDevice() As iPhoneDevice
        Dim dev As iPhoneDevice = Nothing
#If IS_RECOVERY Then
        Dim frmMain As MainFormDataRecovery = CType(Me.mApplication, MainFormDataRecovery)
        If frmMain.Device IsNot Nothing AndAlso frmMain.Device.IsConnected Then
            dev = frmMain.Device
        End If
#End If
        Return dev
    End Function

    Private Sub btnGetNewBackup_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles btnGetNewBackup.LinkClicked
#If IS_RECOVERY Then
        Dim frmMain As MainFormDataRecovery = CType(Me.mApplication, MainFormDataRecovery)
        Dim dev As iPhoneDevice = frmMain.Device
        If dev Is Nothing Then
            Return
        End If

        If tbMessageBox.Show(Me, Me.Language.GetString("Common.Backup.Exit"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) <> Windows.Forms.DialogResult.OK Then
            Return
        End If

        If dev.Identifier <> Me.mBackupInfo.Identifier Then
            tbMessageBox.Show(Me, Me.Language.GetString("Common.Backup.DataExplainEx"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning) '"当前连接设备与备份文件的设备不一致，无法获取最新。"
            Return
        End If
        frmMain.StartBackpuDevice()
#End If
    End Sub

    Private Sub btnExportSetting_SizeChanged(sender As Object, e As EventArgs) Handles btnExportSetting.SizeChanged
        Me.btnExportSetting.Left = Me.txtSearch.Left - Me.btnExportSetting.Width - 10
    End Sub

    Private Sub lblReadError_TextClick(sender As Object, e As EventArgs) Handles lblReadError.TextClick
        Me.SetViewStyle(WhatsAppStatus.Loading)
        Me.LoadBackupInfo()
    End Sub

    Private Sub btnSelectExportType_Click(sender As Object, e As EventArgs) Handles btnSelectExportType.Click
        If Me.dgvSession.Rows.Count = 0 Then
            '联系人为空。
            tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        'Me.cmsExportNews.Show(Me.btnExport, 0, Me.btnExport.Height + 3)
        Me.cmsExport.Show(Me.btnSelectExportType, Me.btnSelectExportType.Width - Me.cmsExport.Width, Me.btnSelectExportType.Height + 3)
    End Sub

    Private Sub lblReadError_SizeChange(sender As Object, e As EventArgs)
        Me.lblReadError.Size = New Size(Me.lblReadError.mMeWidth + 10, Me.lblReadError.Size.Height)
        Me.lblReadError.Location = New Point((Me.pnlError.Width - Me.lblReadError.Size.Width) / 2, Me.lblReadError.Location.Y)
    End Sub

End Class