﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWhatsApp
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWhatsApp))
        Me.tblayoutMain = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.TbPanel2 = New iTong.Components.tbPanel()
        Me.lblBackupInfo = New System.Windows.Forms.Label()
        Me.btnGetNewBackup = New System.Windows.Forms.LinkLabel()
        Me.btnSelectExportType = New iTong.Components.tbButton()
        Me.btnExport = New iTong.Components.tbButton()
        Me.cbxSelectAll = New iTong.Components.tbCheckBox()
        Me.lblState = New iTong.Components.tbLabel()
        Me.lblClickSession = New System.Windows.Forms.Label()
        Me.picNoSearchContent = New System.Windows.Forms.PictureBox()
        Me.wbsChat = New iTong.Components.tbWebBrowserEx()
        Me.pnlSession = New iTong.Components.tbPanel()
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnExportSetting = New iTong.Components.tbButton()
        Me.lblRegister = New iTong.tbBattery()
        Me.btnSelect = New iTong.Components.tbButton()
        Me.btnBack = New iTong.Components.tbButton()
        Me.txtSearch = New iTong.Components.tbSearch()
        Me.pblLoading = New iTong.Components.tbPanel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.pnlError = New iTong.Components.tbPanel()
        Me.lblReadError = New iTong.tbBattery()
        Me.lblError = New System.Windows.Forms.Label()
        Me.picError = New System.Windows.Forms.PictureBox()
        Me.TbPanel1 = New iTong.Components.tbPanel()
        Me.pnlTopLoading = New iTong.Components.tbPanel()
        Me.btnBackLoading = New iTong.Components.tbButton()
        Me.cmsExportNews = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiExcelEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTxtEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiHtml = New System.Windows.Forms.ToolStripMenuItem()
        Me.cmsShowSession = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiShowAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShowDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShowNotDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.cmsExport = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiPicEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAudioEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiVideoEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator1 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiAllEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tblayoutMain.SuspendLayout()
        Me.pnlContainer.SuspendLayout()
        Me.TbPanel2.SuspendLayout()
        CType(Me.picNoSearchContent, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTop.SuspendLayout()
        Me.pblLoading.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlError.SuspendLayout()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTopLoading.SuspendLayout()
        Me.cmsExportNews.SuspendLayout()
        Me.cmsShowSession.SuspendLayout()
        Me.cmsExport.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(1468, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(1444, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(1420, 0)
        '
        'tblayoutMain
        '
        Me.tblayoutMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tblayoutMain.BackColor = System.Drawing.Color.White
        Me.tblayoutMain.ColumnCount = 3
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 23.07692!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 24.27229!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 52.65079!))
        Me.tblayoutMain.Controls.Add(Me.pnlContainer, 2, 2)
        Me.tblayoutMain.Controls.Add(Me.pnlTop, 2, 0)
        Me.tblayoutMain.Controls.Add(Me.pblLoading, 1, 2)
        Me.tblayoutMain.Controls.Add(Me.pnlError, 0, 2)
        Me.tblayoutMain.Controls.Add(Me.TbPanel1, 0, 1)
        Me.tblayoutMain.Controls.Add(Me.pnlTopLoading, 0, 0)
        Me.tblayoutMain.Location = New System.Drawing.Point(2, 32)
        Me.tblayoutMain.Margin = New System.Windows.Forms.Padding(0)
        Me.tblayoutMain.Name = "tblayoutMain"
        Me.tblayoutMain.RowCount = 3
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 39.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 96.0!))
        Me.tblayoutMain.Size = New System.Drawing.Size(1489, 666)
        Me.tblayoutMain.TabIndex = 26
        '
        'pnlContainer
        '
        Me.pnlContainer.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(233, Byte), Integer), CType(CType(233, Byte), Integer))
        Me.pnlContainer.Controls.Add(Me.TbPanel2)
        Me.pnlContainer.Controls.Add(Me.lblClickSession)
        Me.pnlContainer.Controls.Add(Me.picNoSearchContent)
        Me.pnlContainer.Controls.Add(Me.wbsChat)
        Me.pnlContainer.Controls.Add(Me.pnlSession)
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(704, 40)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(785, 626)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 15
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "3,3,3,3"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel2
        '
        Me.TbPanel2.BackColor = System.Drawing.Color.White
        Me.TbPanel2.Controls.Add(Me.lblBackupInfo)
        Me.TbPanel2.Controls.Add(Me.btnGetNewBackup)
        Me.TbPanel2.Controls.Add(Me.btnSelectExportType)
        Me.TbPanel2.Controls.Add(Me.btnExport)
        Me.TbPanel2.Controls.Add(Me.cbxSelectAll)
        Me.TbPanel2.Controls.Add(Me.lblState)
        Me.TbPanel2.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.TbPanel2.Location = New System.Drawing.Point(0, 543)
        Me.TbPanel2.Name = "TbPanel2"
        Me.TbPanel2.Size = New System.Drawing.Size(785, 83)
        Me.TbPanel2.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel2.TabIndex = 87
        Me.TbPanel2.tbBackgroundImage = Nothing
        Me.TbPanel2.tbShowWatermark = False
        Me.TbPanel2.tbSplit = "0,0,0,0"
        Me.TbPanel2.tbWatermark = Nothing
        Me.TbPanel2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel2.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblBackupInfo
        '
        Me.lblBackupInfo.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblBackupInfo.AutoSize = True
        Me.lblBackupInfo.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.lblBackupInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblBackupInfo.Location = New System.Drawing.Point(114, 13)
        Me.lblBackupInfo.Name = "lblBackupInfo"
        Me.lblBackupInfo.Size = New System.Drawing.Size(51, 16)
        Me.lblBackupInfo.TabIndex = 94
        Me.lblBackupInfo.Text = "Label1"
        '
        'btnGetNewBackup
        '
        Me.btnGetNewBackup.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(190, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnGetNewBackup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnGetNewBackup.AutoSize = True
        Me.btnGetNewBackup.BackColor = System.Drawing.Color.Transparent
        Me.btnGetNewBackup.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.btnGetNewBackup.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnGetNewBackup.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnGetNewBackup.Location = New System.Drawing.Point(169, 13)
        Me.btnGetNewBackup.Name = "btnGetNewBackup"
        Me.btnGetNewBackup.Size = New System.Drawing.Size(64, 16)
        Me.btnGetNewBackup.TabIndex = 93
        Me.btnGetNewBackup.TabStop = True
        Me.btnGetNewBackup.Text = "获取最新"
        '
        'btnSelectExportType
        '
        Me.btnSelectExportType.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSelectExportType.BackColor = System.Drawing.Color.Transparent
        Me.btnSelectExportType.BindingForm = Nothing
        Me.btnSelectExportType.Font = New System.Drawing.Font("Arial", 12.0!)
        Me.btnSelectExportType.Location = New System.Drawing.Point(736, 18)
        Me.btnSelectExportType.Name = "btnSelectExportType"
        Me.btnSelectExportType.Padding = New System.Windows.Forms.Padding(6, 0, 10, 3)
        Me.btnSelectExportType.Selectable = True
        Me.btnSelectExportType.Size = New System.Drawing.Size(30, 38)
        Me.btnSelectExportType.TabIndex = 82
        Me.btnSelectExportType.tbAdriftIconWhenHover = False
        Me.btnSelectExportType.tbAutoSize = False
        Me.btnSelectExportType.tbAutoSizeEx = False
        Me.btnSelectExportType.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue_right_corner
        Me.btnSelectExportType.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSelectExportType.tbBadgeNumber = 0
        Me.btnSelectExportType.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSelectExportType.tbEndEllipsis = False
        Me.btnSelectExportType.tbIconHoldPlace = True
        Me.btnSelectExportType.tbIconImage = Global.iTong.My.Resources.Resources.btn_ddl_selecttype
        Me.btnSelectExportType.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnSelectExportType.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSelectExportType.tbIconMore = False
        Me.btnSelectExportType.tbIconMouseDown = Nothing
        Me.btnSelectExportType.tbIconMouseHover = Nothing
        Me.btnSelectExportType.tbIconMouseLeave = Nothing
        Me.btnSelectExportType.tbIconPlaceText = 3
        Me.btnSelectExportType.tbIconReadOnly = Nothing
        Me.btnSelectExportType.tbImageMouseDown = Nothing
        Me.btnSelectExportType.tbImageMouseHover = Nothing
        Me.btnSelectExportType.tbImageMouseLeave = Nothing
        Me.btnSelectExportType.tbProgressValue = 50
        Me.btnSelectExportType.tbReadOnly = False
        Me.btnSelectExportType.tbReadOnlyText = False
        Me.btnSelectExportType.tbShadow = False
        Me.btnSelectExportType.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSelectExportType.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSelectExportType.tbShowDot = False
        Me.btnSelectExportType.tbShowMoreIconImg = Nothing
        Me.btnSelectExportType.tbShowNew = False
        Me.btnSelectExportType.tbShowProgress = False
        Me.btnSelectExportType.tbShowTip = True
        Me.btnSelectExportType.tbShowToolTipOnButton = False
        Me.btnSelectExportType.tbSplit = "13,11,13,11"
        Me.btnSelectExportType.tbText = ""
        Me.btnSelectExportType.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectExportType.tbTextColor = System.Drawing.Color.White
        Me.btnSelectExportType.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSelectExportType.tbTextColorDown = System.Drawing.Color.White
        Me.btnSelectExportType.tbTextColorHover = System.Drawing.Color.White
        Me.btnSelectExportType.tbTextMouseDownPlace = 0
        Me.btnSelectExportType.tbToolTip = ""
        Me.btnSelectExportType.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSelectExportType.tbToolTipFont = New System.Drawing.Font("Arial", 12.0!)
        Me.btnSelectExportType.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectExportType.VisibleEx = True
        '
        'btnExport
        '
        Me.btnExport.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("Arial", 12.0!)
        Me.btnExport.Location = New System.Drawing.Point(606, 18)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(130, 38)
        Me.btnExport.TabIndex = 82
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = False
        Me.btnExport.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue_left_corner
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = False
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Nothing
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 3
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = Nothing
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "导出到电脑"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.White
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.White
        Me.btnExport.tbTextColorDown = System.Drawing.Color.White
        Me.btnExport.tbTextColorHover = System.Drawing.Color.White
        Me.btnExport.tbTextMouseDownPlace = 0
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("Arial", 12.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'cbxSelectAll
        '
        Me.cbxSelectAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.cbxSelectAll.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxSelectAll.AutoSize = True
        Me.cbxSelectAll.BackColor = System.Drawing.Color.Transparent
        Me.cbxSelectAll.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.cbxSelectAll.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cbxSelectAll.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cbxSelectAll.Location = New System.Drawing.Point(9, 9)
        Me.cbxSelectAll.Name = "cbxSelectAll"
        Me.cbxSelectAll.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxSelectAll.Size = New System.Drawing.Size(99, 23)
        Me.cbxSelectAll.TabIndex = 86
        Me.cbxSelectAll.tbAdriftIconWhenHover = False
        Me.cbxSelectAll.tbAutoSize = True
        Me.cbxSelectAll.tbAutoSizeEx = False
        Me.cbxSelectAll.tbIconChecked = CType(resources.GetObject("cbxSelectAll.tbIconChecked"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbIconCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbIconCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbIconHoldPlace = True
        Me.cbxSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxSelectAll.tbIconIndeterminate = CType(resources.GetObject("cbxSelectAll.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconIndeterminateMouseDown = Nothing
        Me.cbxSelectAll.tbIconIndeterminateMouseHover = Nothing
        Me.cbxSelectAll.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxSelectAll.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbIconPlaceText = 1
        Me.cbxSelectAll.tbIconUnChecked = CType(resources.GetObject("cbxSelectAll.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconUnCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbIconUnCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbImageBackground = Nothing
        Me.cbxSelectAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxSelectAll.tbImageCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbImageCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbImageCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbReadOnly = False
        Me.cbxSelectAll.tbShadow = False
        Me.cbxSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxSelectAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxSelectAll.tbSplit = "3,3,3,3"
        Me.cbxSelectAll.tbToolTip = ""
        Me.cbxSelectAll.Text = "全选"
        Me.cbxSelectAll.UseVisualStyleBackColor = False
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(185, 50)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(415, 18)
        Me.lblState.TabIndex = 30
        Me.lblState.tbAdriftWhenHover = False
        Me.lblState.tbAutoEllipsis = False
        Me.lblState.tbAutoSize = False
        Me.lblState.tbHideImage = False
        Me.lblState.tbIconImage = Nothing
        Me.lblState.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblState.tbIconPlaceText = 5
        Me.lblState.tbShadow = False
        Me.lblState.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblState.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblState.tbShowScrolling = False
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblClickSession
        '
        Me.lblClickSession.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblClickSession.BackColor = System.Drawing.Color.White
        Me.lblClickSession.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblClickSession.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblClickSession.Location = New System.Drawing.Point(410, 286)
        Me.lblClickSession.Name = "lblClickSession"
        Me.lblClickSession.Size = New System.Drawing.Size(260, 45)
        Me.lblClickSession.TabIndex = 14
        Me.lblClickSession.Text = "查看消息，请选择左侧联系人！"
        Me.lblClickSession.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblClickSession.Visible = False
        '
        'picNoSearchContent
        '
        Me.picNoSearchContent.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picNoSearchContent.BackColor = System.Drawing.Color.White
        Me.picNoSearchContent.Image = Global.iTong.My.Resources.Resources.icon_nodata
        Me.picNoSearchContent.Location = New System.Drawing.Point(496, 188)
        Me.picNoSearchContent.Margin = New System.Windows.Forms.Padding(0)
        Me.picNoSearchContent.Name = "picNoSearchContent"
        Me.picNoSearchContent.Size = New System.Drawing.Size(85, 92)
        Me.picNoSearchContent.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picNoSearchContent.TabIndex = 14
        Me.picNoSearchContent.TabStop = False
        Me.picNoSearchContent.Visible = False
        '
        'wbsChat
        '
        Me.wbsChat.AllowWebBrowserDrop = False
        Me.wbsChat.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.wbsChat.Client = ""
        Me.wbsChat.DeviceList = Nothing
        Me.wbsChat.IsConnected = False
        Me.wbsChat.IsWebBrowserContextMenuEnabled = False
        Me.wbsChat.Jailbreaked = False
        Me.wbsChat.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.wbsChat.LoadingFont = Nothing
        Me.wbsChat.LoadingGif = CType(resources.GetObject("wbsChat.LoadingGif"), System.Drawing.Image)
        Me.wbsChat.Location = New System.Drawing.Point(273, 0)
        Me.wbsChat.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbsChat.Name = "wbsChat"
        Me.wbsChat.ScriptErrorsSuppressed = True
        Me.wbsChat.ShowLoadingWait = False
        Me.wbsChat.ShowNavigateErrorPage = True
        Me.wbsChat.ShowProgress = True
        Me.wbsChat.Size = New System.Drawing.Size(512, 542)
        Me.wbsChat.SN = ""
        Me.wbsChat.TabIndex = 11
        Me.wbsChat.UserInfo = ""
        Me.wbsChat.UserInfoEncode = ""
        '
        'pnlSession
        '
        Me.pnlSession.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.pnlSession.BackColor = System.Drawing.Color.White
        Me.pnlSession.Location = New System.Drawing.Point(0, 0)
        Me.pnlSession.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlSession.Name = "pnlSession"
        Me.pnlSession.Size = New System.Drawing.Size(272, 542)
        Me.pnlSession.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlSession.TabIndex = 10
        Me.pnlSession.tbBackgroundImage = Nothing
        Me.pnlSession.tbShowWatermark = False
        Me.pnlSession.tbSplit = "0,0,0,0"
        Me.pnlSession.tbWatermark = Nothing
        Me.pnlSession.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlSession.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlTop
        '
        Me.pnlTop.BackColor = System.Drawing.Color.White
        Me.pnlTop.Controls.Add(Me.btnExportSetting)
        Me.pnlTop.Controls.Add(Me.lblRegister)
        Me.pnlTop.Controls.Add(Me.btnSelect)
        Me.pnlTop.Controls.Add(Me.btnBack)
        Me.pnlTop.Controls.Add(Me.txtSearch)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTop.Location = New System.Drawing.Point(704, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(785, 39)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 14
        Me.pnlTop.tbBackgroundImage = Nothing
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "0,0,0,0"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnExportSetting
        '
        Me.btnExportSetting.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnExportSetting.BackColor = System.Drawing.Color.Transparent
        Me.btnExportSetting.BindingForm = Nothing
        Me.btnExportSetting.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.btnExportSetting.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnExportSetting.Location = New System.Drawing.Point(516, 7)
        Me.btnExportSetting.Margin = New System.Windows.Forms.Padding(0)
        Me.btnExportSetting.Name = "btnExportSetting"
        Me.btnExportSetting.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExportSetting.Selectable = True
        Me.btnExportSetting.Size = New System.Drawing.Size(92, 25)
        Me.btnExportSetting.TabIndex = 85
        Me.btnExportSetting.tbAdriftIconWhenHover = False
        Me.btnExportSetting.tbAutoSize = True
        Me.btnExportSetting.tbAutoSizeEx = True
        Me.btnExportSetting.tbBackgroundImage = Nothing
        Me.btnExportSetting.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnExportSetting.tbBadgeNumber = 0
        Me.btnExportSetting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExportSetting.tbEndEllipsis = False
        Me.btnExportSetting.tbIconHoldPlace = True
        Me.btnExportSetting.tbIconImage = Global.iTong.My.Resources.Resources.btn_export_4
        Me.btnExportSetting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExportSetting.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExportSetting.tbIconMore = False
        Me.btnExportSetting.tbIconMouseDown = Nothing
        Me.btnExportSetting.tbIconMouseHover = Nothing
        Me.btnExportSetting.tbIconMouseLeave = Nothing
        Me.btnExportSetting.tbIconPlaceText = 2
        Me.btnExportSetting.tbIconReadOnly = Nothing
        Me.btnExportSetting.tbImageMouseDown = Nothing
        Me.btnExportSetting.tbImageMouseHover = Nothing
        Me.btnExportSetting.tbImageMouseLeave = Nothing
        Me.btnExportSetting.tbProgressValue = 50
        Me.btnExportSetting.tbReadOnly = False
        Me.btnExportSetting.tbReadOnlyText = False
        Me.btnExportSetting.tbShadow = False
        Me.btnExportSetting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExportSetting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExportSetting.tbShowDot = False
        Me.btnExportSetting.tbShowMoreIconImg = CType(resources.GetObject("btnExportSetting.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExportSetting.tbShowNew = False
        Me.btnExportSetting.tbShowProgress = False
        Me.btnExportSetting.tbShowTip = True
        Me.btnExportSetting.tbShowToolTipOnButton = False
        Me.btnExportSetting.tbSplit = "0,0,0,0"
        Me.btnExportSetting.tbText = "导出设置"
        Me.btnExportSetting.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportSetting.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnExportSetting.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnExportSetting.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(4, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(234, Byte), Integer))
        Me.btnExportSetting.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(173, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.btnExportSetting.tbTextMouseDownPlace = 2
        Me.btnExportSetting.tbToolTip = ""
        Me.btnExportSetting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExportSetting.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnExportSetting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportSetting.VisibleEx = True
        '
        'lblRegister
        '
        Me.lblRegister.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblRegister.BackColor = System.Drawing.Color.Transparent
        Me.lblRegister.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblRegister.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.lblRegister.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblRegister.Location = New System.Drawing.Point(303, 10)
        Me.lblRegister.Name = "lblRegister"
        Me.lblRegister.Size = New System.Drawing.Size(210, 19)
        Me.lblRegister.TabIndex = 95
        Me.lblRegister.Text = "试用部分内容用*号代替，请点击右上角"
        Me.lblRegister.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnSelect
        '
        Me.btnSelect.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnSelect.BackColor = System.Drawing.Color.Transparent
        Me.btnSelect.BindingForm = Nothing
        Me.btnSelect.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.btnSelect.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnSelect.Location = New System.Drawing.Point(93, 4)
        Me.btnSelect.Name = "btnSelect"
        Me.btnSelect.Padding = New System.Windows.Forms.Padding(15, 3, 15, 3)
        Me.btnSelect.Selectable = True
        Me.btnSelect.Size = New System.Drawing.Size(163, 31)
        Me.btnSelect.TabIndex = 83
        Me.btnSelect.tbAdriftIconWhenHover = False
        Me.btnSelect.tbAutoSize = False
        Me.btnSelect.tbAutoSizeEx = False
        Me.btnSelect.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnSelect.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSelect.tbBadgeNumber = 0
        Me.btnSelect.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSelect.tbEndEllipsis = False
        Me.btnSelect.tbIconHoldPlace = True
        Me.btnSelect.tbIconImage = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnSelect.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnSelect.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSelect.tbIconMore = False
        Me.btnSelect.tbIconMouseDown = Nothing
        Me.btnSelect.tbIconMouseHover = Nothing
        Me.btnSelect.tbIconMouseLeave = Nothing
        Me.btnSelect.tbIconPlaceText = 5
        Me.btnSelect.tbIconReadOnly = Nothing
        Me.btnSelect.tbImageMouseDown = Nothing
        Me.btnSelect.tbImageMouseHover = Nothing
        Me.btnSelect.tbImageMouseLeave = Nothing
        Me.btnSelect.tbProgressValue = 50
        Me.btnSelect.tbReadOnly = False
        Me.btnSelect.tbReadOnlyText = False
        Me.btnSelect.tbShadow = False
        Me.btnSelect.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSelect.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSelect.tbShowDot = False
        Me.btnSelect.tbShowMoreIconImg = Nothing
        Me.btnSelect.tbShowNew = False
        Me.btnSelect.tbShowProgress = False
        Me.btnSelect.tbShowTip = True
        Me.btnSelect.tbShowToolTipOnButton = False
        Me.btnSelect.tbSplit = "13,11,13,11"
        Me.btnSelect.tbText = "显示所有"
        Me.btnSelect.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelect.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSelect.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSelect.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSelect.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSelect.tbTextMouseDownPlace = 0
        Me.btnSelect.tbToolTip = ""
        Me.btnSelect.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSelect.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnSelect.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelect.VisibleEx = True
        '
        'btnBack
        '
        Me.btnBack.BackColor = System.Drawing.Color.Transparent
        Me.btnBack.BindingForm = Nothing
        Me.btnBack.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBack.Location = New System.Drawing.Point(10, 14)
        Me.btnBack.Name = "btnBack"
        Me.btnBack.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBack.Selectable = True
        Me.btnBack.Size = New System.Drawing.Size(16, 14)
        Me.btnBack.TabIndex = 79
        Me.btnBack.tbAdriftIconWhenHover = False
        Me.btnBack.tbAutoSize = False
        Me.btnBack.tbAutoSizeEx = True
        Me.btnBack.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_retreat
        Me.btnBack.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBack.tbBadgeNumber = 0
        Me.btnBack.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBack.tbEndEllipsis = False
        Me.btnBack.tbIconHoldPlace = True
        Me.btnBack.tbIconImage = Nothing
        Me.btnBack.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBack.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnBack.tbIconMore = False
        Me.btnBack.tbIconMouseDown = Nothing
        Me.btnBack.tbIconMouseHover = Nothing
        Me.btnBack.tbIconMouseLeave = Nothing
        Me.btnBack.tbIconPlaceText = 2
        Me.btnBack.tbIconReadOnly = Nothing
        Me.btnBack.tbImageMouseDown = Nothing
        Me.btnBack.tbImageMouseHover = Nothing
        Me.btnBack.tbImageMouseLeave = Nothing
        Me.btnBack.tbProgressValue = 50
        Me.btnBack.tbReadOnly = False
        Me.btnBack.tbReadOnlyText = False
        Me.btnBack.tbShadow = False
        Me.btnBack.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBack.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBack.tbShowDot = False
        Me.btnBack.tbShowMoreIconImg = CType(resources.GetObject("btnBack.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBack.tbShowNew = False
        Me.btnBack.tbShowProgress = False
        Me.btnBack.tbShowTip = True
        Me.btnBack.tbShowToolTipOnButton = False
        Me.btnBack.tbSplit = "12,1,1,1"
        Me.btnBack.tbText = ""
        Me.btnBack.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnBack.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnBack.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBack.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBack.tbTextMouseDownPlace = 2
        Me.btnBack.tbToolTip = ""
        Me.btnBack.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBack.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnBack.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.VisibleEx = True
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.txtSearch.ForeColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.txtSearch.Location = New System.Drawing.Point(607, 8)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "Search"
        Me.txtSearch.ShowClear = True
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(170, 23)
        Me.txtSearch.TabIndex = 76
        Me.txtSearch.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.5R
        '
        'pblLoading
        '
        Me.pblLoading.BackColor = System.Drawing.Color.White
        Me.pblLoading.Controls.Add(Me.lblLoading)
        Me.pblLoading.Controls.Add(Me.picLoading)
        Me.pblLoading.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pblLoading.Location = New System.Drawing.Point(343, 40)
        Me.pblLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pblLoading.Name = "pblLoading"
        Me.pblLoading.Size = New System.Drawing.Size(361, 626)
        Me.pblLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pblLoading.TabIndex = 13
        Me.pblLoading.tbBackgroundImage = Nothing
        Me.pblLoading.tbShowWatermark = False
        Me.pblLoading.tbSplit = "0,0,0,0"
        Me.pblLoading.tbWatermark = Nothing
        Me.pblLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pblLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(156, 291)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(260, 45)
        Me.lblLoading.TabIndex = 13
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picLoading
        '
        Me.picLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading.BackColor = System.Drawing.Color.White
        Me.picLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.picLoading.Location = New System.Drawing.Point(129, 301)
        Me.picLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(24, 24)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picLoading.TabIndex = 12
        Me.picLoading.TabStop = False
        '
        'pnlError
        '
        Me.pnlError.BackColor = System.Drawing.Color.White
        Me.pnlError.Controls.Add(Me.lblReadError)
        Me.pnlError.Controls.Add(Me.lblError)
        Me.pnlError.Controls.Add(Me.picError)
        Me.pnlError.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlError.Location = New System.Drawing.Point(0, 40)
        Me.pnlError.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlError.Name = "pnlError"
        Me.pnlError.Size = New System.Drawing.Size(343, 626)
        Me.pnlError.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlError.TabIndex = 16
        Me.pnlError.tbBackgroundImage = Nothing
        Me.pnlError.tbShowWatermark = False
        Me.pnlError.tbSplit = "0,0,0,0"
        Me.pnlError.tbWatermark = Nothing
        Me.pnlError.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlError.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblReadError
        '
        Me.lblReadError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblReadError.BackColor = System.Drawing.Color.Transparent
        Me.lblReadError.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblReadError.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.lblReadError.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblReadError.Location = New System.Drawing.Point(-107, 329)
        Me.lblReadError.Name = "lblReadError"
        Me.lblReadError.Size = New System.Drawing.Size(556, 16)
        Me.lblReadError.TabIndex = 95
        Me.lblReadError.Text = "读取失败,请重试或尝试其他恢复模式"
        Me.lblReadError.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblError
        '
        Me.lblError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblError.Font = New System.Drawing.Font("Arial", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblError.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblError.Location = New System.Drawing.Point(41, 368)
        Me.lblError.Name = "lblError"
        Me.lblError.Size = New System.Drawing.Size(260, 45)
        Me.lblError.TabIndex = 15
        Me.lblError.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picError
        '
        Me.picError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picError.BackColor = System.Drawing.Color.Transparent
        Me.picError.Image = Global.iTong.My.Resources.Resources.icon_readerror
        Me.picError.Location = New System.Drawing.Point(131, 207)
        Me.picError.Margin = New System.Windows.Forms.Padding(0)
        Me.picError.Name = "picError"
        Me.picError.Size = New System.Drawing.Size(81, 102)
        Me.picError.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picError.TabIndex = 14
        Me.picError.TabStop = False
        '
        'TbPanel1
        '
        Me.TbPanel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(233, Byte), Integer), CType(CType(233, Byte), Integer))
        Me.tblayoutMain.SetColumnSpan(Me.TbPanel1, 3)
        Me.TbPanel1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TbPanel1.Location = New System.Drawing.Point(0, 39)
        Me.TbPanel1.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel1.Name = "TbPanel1"
        Me.TbPanel1.Size = New System.Drawing.Size(1489, 1)
        Me.TbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel1.TabIndex = 17
        Me.TbPanel1.tbBackgroundImage = Nothing
        Me.TbPanel1.tbShowWatermark = False
        Me.TbPanel1.tbSplit = "0,0,0,0"
        Me.TbPanel1.tbWatermark = Nothing
        Me.TbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlTopLoading
        '
        Me.tblayoutMain.SetColumnSpan(Me.pnlTopLoading, 2)
        Me.pnlTopLoading.Controls.Add(Me.btnBackLoading)
        Me.pnlTopLoading.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTopLoading.Location = New System.Drawing.Point(0, 0)
        Me.pnlTopLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTopLoading.Name = "pnlTopLoading"
        Me.pnlTopLoading.Size = New System.Drawing.Size(704, 39)
        Me.pnlTopLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTopLoading.TabIndex = 18
        Me.pnlTopLoading.tbBackgroundImage = Nothing
        Me.pnlTopLoading.tbShowWatermark = False
        Me.pnlTopLoading.tbSplit = "0,0,0,0"
        Me.pnlTopLoading.tbWatermark = Nothing
        Me.pnlTopLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTopLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnBackLoading
        '
        Me.btnBackLoading.BackColor = System.Drawing.Color.Transparent
        Me.btnBackLoading.BindingForm = Nothing
        Me.btnBackLoading.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBackLoading.Location = New System.Drawing.Point(10, 14)
        Me.btnBackLoading.Name = "btnBackLoading"
        Me.btnBackLoading.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBackLoading.Selectable = True
        Me.btnBackLoading.Size = New System.Drawing.Size(16, 14)
        Me.btnBackLoading.TabIndex = 80
        Me.btnBackLoading.tbAdriftIconWhenHover = False
        Me.btnBackLoading.tbAutoSize = False
        Me.btnBackLoading.tbAutoSizeEx = True
        Me.btnBackLoading.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_retreat
        Me.btnBackLoading.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBackLoading.tbBadgeNumber = 0
        Me.btnBackLoading.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackLoading.tbEndEllipsis = False
        Me.btnBackLoading.tbIconHoldPlace = True
        Me.btnBackLoading.tbIconImage = Nothing
        Me.btnBackLoading.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackLoading.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnBackLoading.tbIconMore = False
        Me.btnBackLoading.tbIconMouseDown = Nothing
        Me.btnBackLoading.tbIconMouseHover = Nothing
        Me.btnBackLoading.tbIconMouseLeave = Nothing
        Me.btnBackLoading.tbIconPlaceText = 2
        Me.btnBackLoading.tbIconReadOnly = Nothing
        Me.btnBackLoading.tbImageMouseDown = Nothing
        Me.btnBackLoading.tbImageMouseHover = Nothing
        Me.btnBackLoading.tbImageMouseLeave = Nothing
        Me.btnBackLoading.tbProgressValue = 50
        Me.btnBackLoading.tbReadOnly = False
        Me.btnBackLoading.tbReadOnlyText = False
        Me.btnBackLoading.tbShadow = False
        Me.btnBackLoading.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackLoading.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackLoading.tbShowDot = False
        Me.btnBackLoading.tbShowMoreIconImg = CType(resources.GetObject("btnBackLoading.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBackLoading.tbShowNew = False
        Me.btnBackLoading.tbShowProgress = False
        Me.btnBackLoading.tbShowTip = True
        Me.btnBackLoading.tbShowToolTipOnButton = False
        Me.btnBackLoading.tbSplit = "12,1,1,1"
        Me.btnBackLoading.tbText = ""
        Me.btnBackLoading.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackLoading.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnBackLoading.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnBackLoading.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackLoading.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackLoading.tbTextMouseDownPlace = 2
        Me.btnBackLoading.tbToolTip = ""
        Me.btnBackLoading.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackLoading.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnBackLoading.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackLoading.VisibleEx = True
        '
        'cmsExportNews
        '
        Me.cmsExportNews.AccessibleDescription = "166x92"
        Me.cmsExportNews.DropShadowEnabled = False
        Me.cmsExportNews.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsExportNews.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExcelEx, Me.tsmiTxtEx, Me.tsmiHtml})
        Me.cmsExportNews.Name = "munOperate"
        Me.cmsExportNews.Size = New System.Drawing.Size(166, 70)
        Me.cmsExportNews.tbBackColor = System.Drawing.Color.White
        Me.cmsExportNews.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExportNews.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExportNews.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiExcelEx
        '
        Me.tsmiExcelEx.Name = "tsmiExcelEx"
        Me.tsmiExcelEx.Size = New System.Drawing.Size(165, 22)
        Me.tsmiExcelEx.Text = "导出为Excel文件"
        '
        'tsmiTxtEx
        '
        Me.tsmiTxtEx.Name = "tsmiTxtEx"
        Me.tsmiTxtEx.Size = New System.Drawing.Size(165, 22)
        Me.tsmiTxtEx.Text = "导出为Txt文件"
        '
        'tsmiHtml
        '
        Me.tsmiHtml.Name = "tsmiHtml"
        Me.tsmiHtml.Size = New System.Drawing.Size(165, 22)
        Me.tsmiHtml.Text = "导出为Html文件"
        '
        'cmsShowSession
        '
        Me.cmsShowSession.AccessibleDescription = "161x92"
        Me.cmsShowSession.DropShadowEnabled = False
        Me.cmsShowSession.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsShowSession.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiShowAll, Me.tsmiShowDelete, Me.tsmiShowNotDelete})
        Me.cmsShowSession.Name = "munOperate"
        Me.cmsShowSession.Size = New System.Drawing.Size(161, 70)
        Me.cmsShowSession.tbBackColor = System.Drawing.Color.White
        Me.cmsShowSession.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsShowSession.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsShowSession.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiShowAll
        '
        Me.tsmiShowAll.Name = "tsmiShowAll"
        Me.tsmiShowAll.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowAll.Text = "显示所有"
        '
        'tsmiShowDelete
        '
        Me.tsmiShowDelete.Name = "tsmiShowDelete"
        Me.tsmiShowDelete.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowDelete.Text = "只显示删除文件"
        '
        'tsmiShowNotDelete
        '
        Me.tsmiShowNotDelete.Name = "tsmiShowNotDelete"
        Me.tsmiShowNotDelete.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowNotDelete.Text = "显示已存在文件"
        '
        'cmsExport
        '
        Me.cmsExport.AccessibleDescription = "153x120"
        Me.cmsExport.DropShadowEnabled = False
        Me.cmsExport.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsExport.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiPicEx, Me.tsmiAudioEx, Me.tsmiVideoEx, Me.ToolStripSeparator1, Me.tsmiAllEx})
        Me.cmsExport.Name = "munOperate"
        Me.cmsExport.Size = New System.Drawing.Size(137, 98)
        Me.cmsExport.tbBackColor = System.Drawing.Color.White
        Me.cmsExport.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExport.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExport.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiPicEx
        '
        Me.tsmiPicEx.Name = "tsmiPicEx"
        Me.tsmiPicEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiPicEx.Text = "导出图片"
        '
        'tsmiAudioEx
        '
        Me.tsmiAudioEx.Name = "tsmiAudioEx"
        Me.tsmiAudioEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiAudioEx.Text = "导出语音"
        '
        'tsmiVideoEx
        '
        Me.tsmiVideoEx.Name = "tsmiVideoEx"
        Me.tsmiVideoEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiVideoEx.Text = "导出小视频"
        '
        'ToolStripSeparator1
        '
        Me.ToolStripSeparator1.Name = "ToolStripSeparator1"
        Me.ToolStripSeparator1.Size = New System.Drawing.Size(133, 6)
        '
        'tsmiAllEx
        '
        Me.tsmiAllEx.Name = "tsmiAllEx"
        Me.tsmiAllEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiAllEx.Text = "导出全部"
        '
        'frmWhatsApp
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.White
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(1492, 698)
        Me.Controls.Add(Me.tblayoutMain)
        Me.Name = "frmWhatsApp"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,55"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "WhatsApp"
        Me.Controls.SetChildIndex(Me.tblayoutMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.tblayoutMain.ResumeLayout(False)
        Me.pnlContainer.ResumeLayout(False)
        Me.TbPanel2.ResumeLayout(False)
        Me.TbPanel2.PerformLayout()
        CType(Me.picNoSearchContent, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTop.ResumeLayout(False)
        Me.pblLoading.ResumeLayout(False)
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlError.ResumeLayout(False)
        CType(Me.picError, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTopLoading.ResumeLayout(False)
        Me.cmsExportNews.ResumeLayout(False)
        Me.cmsShowSession.ResumeLayout(False)
        Me.cmsExport.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents tblayoutMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlContainer As iTong.Components.tbPanel
    Friend WithEvents wbsChat As iTong.Components.tbWebBrowserEx
    Friend WithEvents pnlSession As iTong.Components.tbPanel
    Friend WithEvents pnlTop As iTong.Components.tbPanel
    Friend WithEvents txtSearch As iTong.Components.tbSearch
    Private WithEvents pblLoading As iTong.Components.tbPanel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblState As iTong.Components.tbLabel
    Friend WithEvents lblClickSession As System.Windows.Forms.Label
    Friend WithEvents cmsExportNews As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiExcelEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTxtEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiHtml As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlError As iTong.Components.tbPanel
    Friend WithEvents lblError As System.Windows.Forms.Label
    Friend WithEvents picError As System.Windows.Forms.PictureBox
    Friend WithEvents btnExport As iTong.Components.tbButton
    Friend WithEvents cbxSelectAll As iTong.Components.tbCheckBox
    Friend WithEvents btnBack As iTong.Components.tbButton
    Friend WithEvents TbPanel1 As iTong.Components.tbPanel
    Friend WithEvents pnlTopLoading As iTong.Components.tbPanel
    Friend WithEvents btnBackLoading As iTong.Components.tbButton
    Friend WithEvents TbPanel2 As iTong.Components.tbPanel
    Friend WithEvents btnSelect As iTong.Components.tbButton
    Friend WithEvents btnExportSetting As iTong.Components.tbButton
    Friend WithEvents cmsShowSession As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiShowAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShowDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShowNotDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnGetNewBackup As System.Windows.Forms.LinkLabel
    Friend WithEvents lblBackupInfo As System.Windows.Forms.Label
    Friend WithEvents lblRegister As iTong.tbBattery
    Friend WithEvents cmsExport As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiPicEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAudioEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiVideoEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAllEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblReadError As iTong.tbBattery
    Friend WithEvents picNoSearchContent As System.Windows.Forms.PictureBox
    Friend WithEvents btnSelectExportType As iTong.Components.tbButton
    Friend WithEvents ToolStripSeparator1 As System.Windows.Forms.ToolStripSeparator
End Class
