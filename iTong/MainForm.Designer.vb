﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MainForm
    'Inherits PNGControlBase
    Inherits tbBaseGuiForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MainForm))
        Me.btn_minimize = New iTong.Components.tbButton()
        Me.btn_normal = New iTong.Components.tbButton()
        Me.btn_close = New iTong.Components.tbButton()
        Me.btn_Setting = New iTong.Components.tbButton()
        Me.pnlContainer = New System.Windows.Forms.Panel()
        Me.pnlState = New iTong.Components.tbPanel()
        Me.downloadBar = New iTong.tbDownloadBar()
        Me.lblExpireTime = New System.Windows.Forms.Label()
        Me.lblState = New System.Windows.Forms.Label()
        Me.btnDownload = New iTong.Components.tbButton()
        Me.picMode = New System.Windows.Forms.PictureBox()
        Me.menuMain = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmSetting = New System.Windows.Forms.ToolStripMenuItem()
        Me.tssSkin = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmSkin = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItem2 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmUpdate = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSelectLang = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSelectSkin = New System.Windows.Forms.ToolStripMenuItem()
        Me.tss2 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiGetLogs = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSplitLogin = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiLogin = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmTongbuPersonalCenterEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmTongbuAccountCenterEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator2 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmChangeAccountEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmLogoutEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator3 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiCoexistPay = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator4 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiFeedback = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAbout = New System.Windows.Forms.ToolStripMenuItem()
        Me.btnFeedBack = New iTong.Components.tbButton()
        Me.btnCheckUpdate = New iTong.Components.tbButton()
        Me.lblVersion = New iTong.Components.tbButton()
        Me.picWeibo = New iTong.Components.tbPictureBox()
        Me.tmrRun = New System.Windows.Forms.Timer(Me.components)
        Me.btnLogin = New iTong.Components.tbButton()
        Me.menuLogout = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmTongbuAccountCenter = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripMenuItem1 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmChangeAccount = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmLogout = New System.Windows.Forms.ToolStripMenuItem()
        Me.btnTuiTutorial = New iTong.Components.tbButton()
        Me.btnSkin = New iTong.Components.tbButton()
        Me.menuSkin = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.ToolStripSeparator1 = New System.Windows.Forms.ToolStripSeparator()
        Me.picLogo = New iTong.Components.tbPictureBox()
        Me.btnBuy = New iTong.Components.tbButton()
        Me.pnlState.SuspendLayout()
        CType(Me.picMode, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.menuMain.SuspendLayout()
        CType(Me.picWeibo, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.menuLogout.SuspendLayout()
        Me.menuSkin.SuspendLayout()
        CType(Me.picLogo, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_minimize
        '
        Me.btn_minimize.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_minimize.BackColor = System.Drawing.Color.Transparent
        Me.btn_minimize.BindingForm = Nothing
        Me.btn_minimize.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_minimize.Location = New System.Drawing.Point(893, 1)
        Me.btn_minimize.Name = "btn_minimize"
        Me.btn_minimize.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_minimize.Selectable = True
        Me.btn_minimize.Size = New System.Drawing.Size(24, 24)
        Me.btn_minimize.TabIndex = 2
        Me.btn_minimize.tbAdriftIconWhenHover = False
        Me.btn_minimize.tbAutoSize = False
        Me.btn_minimize.tbAutoSizeEx = False
        Me.btn_minimize.tbBackgroundImage = Nothing
        Me.btn_minimize.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btn_minimize.tbBadgeNumber = 0
        Me.btn_minimize.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_minimize.tbEndEllipsis = False
        Me.btn_minimize.tbIconHoldPlace = True
        Me.btn_minimize.tbIconImage = Nothing
        Me.btn_minimize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_minimize.tbIconMore = False
        Me.btn_minimize.tbIconMouseDown = Nothing
        Me.btn_minimize.tbIconMouseHover = Nothing
        Me.btn_minimize.tbIconMouseLeave = Nothing
        Me.btn_minimize.tbIconPlaceText = 2
        Me.btn_minimize.tbIconReadOnly = Nothing
        Me.btn_minimize.tbImageMouseDown = Nothing
        Me.btn_minimize.tbImageMouseHover = Nothing
        Me.btn_minimize.tbImageMouseLeave = Nothing
        Me.btn_minimize.tbProgressValue = 50
        Me.btn_minimize.tbReadOnly = False
        Me.btn_minimize.tbReadOnlyText = False
        Me.btn_minimize.tbShadow = False
        Me.btn_minimize.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_minimize.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_minimize.tbShowDot = False
        Me.btn_minimize.tbShowMoreIconImg = CType(resources.GetObject("btn_minimize.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_minimize.tbShowNew = False
        Me.btn_minimize.tbShowProgress = False
        Me.btn_minimize.tbShowTip = True
        Me.btn_minimize.tbShowToolTipOnButton = False
        Me.btn_minimize.tbSplit = "3,3,3,3"
        Me.btn_minimize.tbText = ""
        Me.btn_minimize.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.tbTextColor = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorDown = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorHover = System.Drawing.Color.White
        Me.btn_minimize.tbTextMouseDownPlace = 0
        Me.btn_minimize.tbToolTip = ""
        Me.btn_minimize.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_minimize.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_minimize.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.VisibleEx = True
        '
        'btn_normal
        '
        Me.btn_normal.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_normal.BackColor = System.Drawing.Color.Transparent
        Me.btn_normal.BindingForm = Nothing
        Me.btn_normal.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_normal.Location = New System.Drawing.Point(923, 1)
        Me.btn_normal.Name = "btn_normal"
        Me.btn_normal.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_normal.Selectable = True
        Me.btn_normal.Size = New System.Drawing.Size(24, 24)
        Me.btn_normal.TabIndex = 3
        Me.btn_normal.tbAdriftIconWhenHover = False
        Me.btn_normal.tbAutoSize = False
        Me.btn_normal.tbAutoSizeEx = False
        Me.btn_normal.tbBackgroundImage = Nothing
        Me.btn_normal.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btn_normal.tbBadgeNumber = 0
        Me.btn_normal.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_normal.tbEndEllipsis = False
        Me.btn_normal.tbIconHoldPlace = True
        Me.btn_normal.tbIconImage = Nothing
        Me.btn_normal.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_normal.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_normal.tbIconMore = False
        Me.btn_normal.tbIconMouseDown = Nothing
        Me.btn_normal.tbIconMouseHover = Nothing
        Me.btn_normal.tbIconMouseLeave = Nothing
        Me.btn_normal.tbIconPlaceText = 2
        Me.btn_normal.tbIconReadOnly = Nothing
        Me.btn_normal.tbImageMouseDown = Nothing
        Me.btn_normal.tbImageMouseHover = Nothing
        Me.btn_normal.tbImageMouseLeave = Nothing
        Me.btn_normal.tbProgressValue = 50
        Me.btn_normal.tbReadOnly = False
        Me.btn_normal.tbReadOnlyText = False
        Me.btn_normal.tbShadow = False
        Me.btn_normal.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_normal.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_normal.tbShowDot = False
        Me.btn_normal.tbShowMoreIconImg = CType(resources.GetObject("btn_normal.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_normal.tbShowNew = False
        Me.btn_normal.tbShowProgress = False
        Me.btn_normal.tbShowTip = True
        Me.btn_normal.tbShowToolTipOnButton = False
        Me.btn_normal.tbSplit = "3,3,3,3"
        Me.btn_normal.tbText = ""
        Me.btn_normal.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_normal.tbTextColor = System.Drawing.Color.White
        Me.btn_normal.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_normal.tbTextColorDown = System.Drawing.Color.White
        Me.btn_normal.tbTextColorHover = System.Drawing.Color.White
        Me.btn_normal.tbTextMouseDownPlace = 0
        Me.btn_normal.tbToolTip = ""
        Me.btn_normal.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_normal.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_normal.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_normal.VisibleEx = True
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(953, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 4
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Nothing
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btn_Setting
        '
        Me.btn_Setting.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Setting.BackColor = System.Drawing.Color.Transparent
        Me.btn_Setting.BindingForm = Nothing
        Me.btn_Setting.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btn_Setting.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.Location = New System.Drawing.Point(881, 0)
        Me.btn_Setting.Name = "btn_Setting"
        Me.btn_Setting.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Setting.Selectable = True
        Me.btn_Setting.Size = New System.Drawing.Size(24, 24)
        Me.btn_Setting.TabIndex = 1
        Me.btn_Setting.tbAdriftIconWhenHover = False
        Me.btn_Setting.tbAutoSize = True
        Me.btn_Setting.tbAutoSizeEx = True
        Me.btn_Setting.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_setting
        Me.btn_Setting.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_Setting.tbBadgeNumber = 0
        Me.btn_Setting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Setting.tbEndEllipsis = False
        Me.btn_Setting.tbIconHoldPlace = True
        Me.btn_Setting.tbIconImage = Nothing
        Me.btn_Setting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_Setting.tbIconMore = False
        Me.btn_Setting.tbIconMouseDown = Nothing
        Me.btn_Setting.tbIconMouseHover = Nothing
        Me.btn_Setting.tbIconMouseLeave = Nothing
        Me.btn_Setting.tbIconPlaceText = 2
        Me.btn_Setting.tbIconReadOnly = Nothing
        Me.btn_Setting.tbImageMouseDown = Nothing
        Me.btn_Setting.tbImageMouseHover = Nothing
        Me.btn_Setting.tbImageMouseLeave = Nothing
        Me.btn_Setting.tbProgressValue = 50
        Me.btn_Setting.tbReadOnly = False
        Me.btn_Setting.tbReadOnlyText = False
        Me.btn_Setting.tbShadow = False
        Me.btn_Setting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Setting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Setting.tbShowDot = False
        Me.btn_Setting.tbShowMoreIconImg = CType(resources.GetObject("btn_Setting.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_Setting.tbShowNew = False
        Me.btn_Setting.tbShowProgress = False
        Me.btn_Setting.tbShowTip = True
        Me.btn_Setting.tbShowToolTipOnButton = False
        Me.btn_Setting.tbSplit = "3,3,3,3"
        Me.btn_Setting.tbText = ""
        Me.btn_Setting.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btn_Setting.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.btn_Setting.tbTextColorDisable = System.Drawing.Color.Gray
        Me.btn_Setting.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextMouseDownPlace = 0
        Me.btn_Setting.tbToolTip = ""
        Me.btn_Setting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Setting.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.VisibleEx = True
        '
        'pnlContainer
        '
        Me.pnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainer.Location = New System.Drawing.Point(1, 93)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(976, 614)
        Me.pnlContainer.TabIndex = 5
        '
        'pnlState
        '
        Me.pnlState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlState.BackColor = System.Drawing.Color.Transparent
        Me.pnlState.Controls.Add(Me.downloadBar)
        Me.pnlState.Controls.Add(Me.lblExpireTime)
        Me.pnlState.Controls.Add(Me.lblState)
        Me.pnlState.Controls.Add(Me.btnDownload)
        Me.pnlState.Controls.Add(Me.picMode)
        Me.pnlState.Location = New System.Drawing.Point(5, 707)
        Me.pnlState.Name = "pnlState"
        Me.pnlState.Size = New System.Drawing.Size(968, 26)
        Me.pnlState.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlState.TabIndex = 6
        Me.pnlState.tbBackgroundImage = Nothing
        Me.pnlState.tbShowWatermark = False
        Me.pnlState.tbSplit = "0,0,0,0"
        Me.pnlState.tbWatermark = Nothing
        Me.pnlState.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlState.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'downloadBar
        '
        Me.downloadBar.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.downloadBar.BackColor = System.Drawing.Color.Transparent
        Me.downloadBar.Cursor = System.Windows.Forms.Cursors.Hand
        Me.downloadBar.Font = New System.Drawing.Font("宋体", 8.0!)
        Me.downloadBar.ForeColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(88, Byte), Integer))
        Me.downloadBar.Location = New System.Drawing.Point(586, 3)
        Me.downloadBar.Name = "downloadBar"
        Me.downloadBar.Size = New System.Drawing.Size(250, 20)
        Me.downloadBar.TabIndex = 3
        Me.downloadBar.tbBackgroundImage = Global.iTong.My.Resources.Resources.app_downloads_background
        Me.downloadBar.tbEmptyText = "您还没有下载任务"
        Me.downloadBar.tbFailureText = "失败"
        Me.downloadBar.tbPauseText = "暂停"
        Me.downloadBar.tbProgressImage = Global.iTong.My.Resources.Resources.app_downloads_value
        Me.downloadBar.tbRetryDown = "正在重试"
        Me.downloadBar.tbSplit = "5,5,5,5"
        Me.downloadBar.tbWaitingText = "等待"
        Me.downloadBar.Text = "TbDownloadBar1"
        '
        'lblExpireTime
        '
        Me.lblExpireTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblExpireTime.Location = New System.Drawing.Point(22, 3)
        Me.lblExpireTime.Name = "lblExpireTime"
        Me.lblExpireTime.Size = New System.Drawing.Size(244, 21)
        Me.lblExpireTime.TabIndex = 1
        Me.lblExpireTime.Text = "Trial version will expire on 2013/08/05"
        Me.lblExpireTime.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(196, 3)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(406, 21)
        Me.lblState.TabIndex = 1
        Me.lblState.Text = "提示信息"
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnDownload
        '
        Me.btnDownload.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDownload.BackColor = System.Drawing.Color.Transparent
        Me.btnDownload.BindingForm = Nothing
        Me.btnDownload.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDownload.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.btnDownload.Location = New System.Drawing.Point(838, 3)
        Me.btnDownload.Name = "btnDownload"
        Me.btnDownload.Padding = New System.Windows.Forms.Padding(5, 0, 5, 2)
        Me.btnDownload.Selectable = True
        Me.btnDownload.Size = New System.Drawing.Size(121, 21)
        Me.btnDownload.TabIndex = 2
        Me.btnDownload.tbAdriftIconWhenHover = False
        Me.btnDownload.tbAutoSize = True
        Me.btnDownload.tbAutoSizeEx = True
        Me.btnDownload.tbBackgroundImage = Nothing
        Me.btnDownload.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDownload.tbBadgeNumber = 0
        Me.btnDownload.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownload.tbEndEllipsis = False
        Me.btnDownload.tbIconHoldPlace = True
        Me.btnDownload.tbIconImage = Global.iTong.My.Resources.Resources.download_stop
        Me.btnDownload.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDownload.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDownload.tbIconMore = False
        Me.btnDownload.tbIconMouseDown = Nothing
        Me.btnDownload.tbIconMouseHover = Nothing
        Me.btnDownload.tbIconMouseLeave = Nothing
        Me.btnDownload.tbIconPlaceText = 2
        Me.btnDownload.tbIconReadOnly = Nothing
        Me.btnDownload.tbImageMouseDown = Nothing
        Me.btnDownload.tbImageMouseHover = Nothing
        Me.btnDownload.tbImageMouseLeave = Nothing
        Me.btnDownload.tbProgressValue = 50
        Me.btnDownload.tbReadOnly = False
        Me.btnDownload.tbReadOnlyText = False
        Me.btnDownload.tbShadow = False
        Me.btnDownload.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDownload.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDownload.tbShowDot = False
        Me.btnDownload.tbShowMoreIconImg = CType(resources.GetObject("btnDownload.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDownload.tbShowNew = False
        Me.btnDownload.tbShowProgress = False
        Me.btnDownload.tbShowTip = True
        Me.btnDownload.tbShowToolTipOnButton = False
        Me.btnDownload.tbSplit = "3,3,3,3"
        Me.btnDownload.tbText = "Doloading(1023)"
        Me.btnDownload.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDownload.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.btnDownload.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.btnDownload.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.btnDownload.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.btnDownload.tbTextMouseDownPlace = 0
        Me.btnDownload.tbToolTip = ""
        Me.btnDownload.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownload.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownload.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDownload.VisibleEx = True
        '
        'picMode
        '
        Me.picMode.Image = Global.iTong.My.Resources.Resources.icon_time
        Me.picMode.Location = New System.Drawing.Point(-2, 0)
        Me.picMode.Margin = New System.Windows.Forms.Padding(0)
        Me.picMode.Name = "picMode"
        Me.picMode.Size = New System.Drawing.Size(26, 26)
        Me.picMode.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picMode.TabIndex = 0
        Me.picMode.TabStop = False
        '
        'menuMain
        '
        Me.menuMain.AccessibleDescription = "153x282"
        Me.menuMain.DropShadowEnabled = False
        Me.menuMain.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.menuMain.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmSetting, Me.tssSkin, Me.tsmSkin, Me.ToolStripMenuItem2, Me.tsmUpdate, Me.tsmiSelectLang, Me.tsmiSelectSkin, Me.tss2, Me.tsmiGetLogs, Me.tsmiSplitLogin, Me.tsmiLogin, Me.ToolStripSeparator3, Me.tsmiCoexistPay, Me.ToolStripSeparator4, Me.tsmiFeedback, Me.tsmiAbout})
        Me.menuMain.Name = "menuMain"
        Me.menuMain.Size = New System.Drawing.Size(143, 260)
        Me.menuMain.Tag = ""
        Me.menuMain.tbBackColor = System.Drawing.Color.White
        Me.menuMain.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuMain.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuMain.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmSetting
        '
        Me.tsmSetting.Name = "tsmSetting"
        Me.tsmSetting.Size = New System.Drawing.Size(142, 22)
        Me.tsmSetting.Text = "参数设置(&O)"
        '
        'tssSkin
        '
        Me.tssSkin.Name = "tssSkin"
        Me.tssSkin.Size = New System.Drawing.Size(139, 6)
        '
        'tsmSkin
        '
        Me.tsmSkin.Name = "tsmSkin"
        Me.tsmSkin.Size = New System.Drawing.Size(142, 22)
        Me.tsmSkin.Text = "皮肤"
        '
        'ToolStripMenuItem2
        '
        Me.ToolStripMenuItem2.Name = "ToolStripMenuItem2"
        Me.ToolStripMenuItem2.Size = New System.Drawing.Size(139, 6)
        '
        'tsmUpdate
        '
        Me.tsmUpdate.Name = "tsmUpdate"
        Me.tsmUpdate.Size = New System.Drawing.Size(142, 22)
        Me.tsmUpdate.Text = "检查更新(&K)"
        '
        'tsmiSelectLang
        '
        Me.tsmiSelectLang.Name = "tsmiSelectLang"
        Me.tsmiSelectLang.Size = New System.Drawing.Size(142, 22)
        Me.tsmiSelectLang.Text = "选择语言(&L)"
        '
        'tsmiSelectSkin
        '
        Me.tsmiSelectSkin.Name = "tsmiSelectSkin"
        Me.tsmiSelectSkin.Size = New System.Drawing.Size(142, 22)
        Me.tsmiSelectSkin.Text = "皮肤"
        Me.tsmiSelectSkin.Visible = False
        '
        'tss2
        '
        Me.tss2.Name = "tss2"
        Me.tss2.Size = New System.Drawing.Size(139, 6)
        '
        'tsmiGetLogs
        '
        Me.tsmiGetLogs.Name = "tsmiGetLogs"
        Me.tsmiGetLogs.Size = New System.Drawing.Size(142, 22)
        Me.tsmiGetLogs.Text = "错误日志"
        '
        'tsmiSplitLogin
        '
        Me.tsmiSplitLogin.Name = "tsmiSplitLogin"
        Me.tsmiSplitLogin.Size = New System.Drawing.Size(139, 6)
        '
        'tsmiLogin
        '
        Me.tsmiLogin.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmTongbuPersonalCenterEx, Me.tsmTongbuAccountCenterEx, Me.ToolStripSeparator2, Me.tsmChangeAccountEx, Me.tsmLogoutEx})
        Me.tsmiLogin.Name = "tsmiLogin"
        Me.tsmiLogin.Size = New System.Drawing.Size(142, 22)
        Me.tsmiLogin.Text = "登陆"
        '
        'tsmTongbuPersonalCenterEx
        '
        Me.tsmTongbuPersonalCenterEx.Name = "tsmTongbuPersonalCenterEx"
        Me.tsmTongbuPersonalCenterEx.Size = New System.Drawing.Size(148, 22)
        Me.tsmTongbuPersonalCenterEx.Text = "个人中心"
        Me.tsmTongbuPersonalCenterEx.Visible = False
        '
        'tsmTongbuAccountCenterEx
        '
        Me.tsmTongbuAccountCenterEx.Name = "tsmTongbuAccountCenterEx"
        Me.tsmTongbuAccountCenterEx.Size = New System.Drawing.Size(148, 22)
        Me.tsmTongbuAccountCenterEx.Text = "同步账号中心"
        Me.tsmTongbuAccountCenterEx.Visible = False
        '
        'ToolStripSeparator2
        '
        Me.ToolStripSeparator2.Name = "ToolStripSeparator2"
        Me.ToolStripSeparator2.Size = New System.Drawing.Size(145, 6)
        Me.ToolStripSeparator2.Visible = False
        '
        'tsmChangeAccountEx
        '
        Me.tsmChangeAccountEx.Name = "tsmChangeAccountEx"
        Me.tsmChangeAccountEx.Size = New System.Drawing.Size(148, 22)
        Me.tsmChangeAccountEx.Text = "切换账号"
        Me.tsmChangeAccountEx.Visible = False
        '
        'tsmLogoutEx
        '
        Me.tsmLogoutEx.Name = "tsmLogoutEx"
        Me.tsmLogoutEx.Size = New System.Drawing.Size(148, 22)
        Me.tsmLogoutEx.Text = "注销"
        Me.tsmLogoutEx.Visible = False
        '
        'ToolStripSeparator3
        '
        Me.ToolStripSeparator3.Name = "ToolStripSeparator3"
        Me.ToolStripSeparator3.Size = New System.Drawing.Size(139, 6)
        '
        'tsmiCoexistPay
        '
        Me.tsmiCoexistPay.Name = "tsmiCoexistPay"
        Me.tsmiCoexistPay.Size = New System.Drawing.Size(142, 22)
        Me.tsmiCoexistPay.Text = "共存版付费"
        '
        'ToolStripSeparator4
        '
        Me.ToolStripSeparator4.Name = "ToolStripSeparator4"
        Me.ToolStripSeparator4.Size = New System.Drawing.Size(139, 6)
        '
        'tsmiFeedback
        '
        Me.tsmiFeedback.Name = "tsmiFeedback"
        Me.tsmiFeedback.Size = New System.Drawing.Size(142, 22)
        Me.tsmiFeedback.Text = "Feedback"
        '
        'tsmiAbout
        '
        Me.tsmiAbout.Name = "tsmiAbout"
        Me.tsmiAbout.Size = New System.Drawing.Size(142, 22)
        Me.tsmiAbout.Text = "关于(&A)"
        '
        'btnFeedBack
        '
        Me.btnFeedBack.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnFeedBack.BackColor = System.Drawing.Color.Transparent
        Me.btnFeedBack.BindingForm = Nothing
        Me.btnFeedBack.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnFeedBack.Font = New System.Drawing.Font("宋体", 8.0!)
        Me.btnFeedBack.Location = New System.Drawing.Point(856, 0)
        Me.btnFeedBack.Name = "btnFeedBack"
        Me.btnFeedBack.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFeedBack.Selectable = True
        Me.btnFeedBack.Size = New System.Drawing.Size(24, 24)
        Me.btnFeedBack.TabIndex = 19
        Me.btnFeedBack.tbAdriftIconWhenHover = False
        Me.btnFeedBack.tbAutoSize = True
        Me.btnFeedBack.tbAutoSizeEx = True
        Me.btnFeedBack.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_feedback_3
        Me.btnFeedBack.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnFeedBack.tbBadgeNumber = 0
        Me.btnFeedBack.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFeedBack.tbEndEllipsis = False
        Me.btnFeedBack.tbIconHoldPlace = True
        Me.btnFeedBack.tbIconImage = Nothing
        Me.btnFeedBack.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFeedBack.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btnFeedBack.tbIconMore = False
        Me.btnFeedBack.tbIconMouseDown = Nothing
        Me.btnFeedBack.tbIconMouseHover = Nothing
        Me.btnFeedBack.tbIconMouseLeave = Nothing
        Me.btnFeedBack.tbIconPlaceText = 2
        Me.btnFeedBack.tbIconReadOnly = Nothing
        Me.btnFeedBack.tbImageMouseDown = Nothing
        Me.btnFeedBack.tbImageMouseHover = Nothing
        Me.btnFeedBack.tbImageMouseLeave = Nothing
        Me.btnFeedBack.tbProgressValue = 50
        Me.btnFeedBack.tbReadOnly = False
        Me.btnFeedBack.tbReadOnlyText = False
        Me.btnFeedBack.tbShadow = False
        Me.btnFeedBack.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFeedBack.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFeedBack.tbShowDot = False
        Me.btnFeedBack.tbShowMoreIconImg = CType(resources.GetObject("btnFeedBack.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFeedBack.tbShowNew = False
        Me.btnFeedBack.tbShowProgress = False
        Me.btnFeedBack.tbShowTip = True
        Me.btnFeedBack.tbShowToolTipOnButton = False
        Me.btnFeedBack.tbSplit = "13,11,13,11"
        Me.btnFeedBack.tbText = ""
        Me.btnFeedBack.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFeedBack.tbTextColor = System.Drawing.Color.WhiteSmoke
        Me.btnFeedBack.tbTextColorDisable = System.Drawing.Color.WhiteSmoke
        Me.btnFeedBack.tbTextColorDown = System.Drawing.Color.WhiteSmoke
        Me.btnFeedBack.tbTextColorHover = System.Drawing.Color.White
        Me.btnFeedBack.tbTextMouseDownPlace = 0
        Me.btnFeedBack.tbToolTip = ""
        Me.btnFeedBack.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFeedBack.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFeedBack.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFeedBack.VisibleEx = True
        '
        'btnCheckUpdate
        '
        Me.btnCheckUpdate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnCheckUpdate.BackColor = System.Drawing.Color.Transparent
        Me.btnCheckUpdate.BindingForm = Nothing
        Me.btnCheckUpdate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnCheckUpdate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCheckUpdate.Location = New System.Drawing.Point(104, 710)
        Me.btnCheckUpdate.Margin = New System.Windows.Forms.Padding(0)
        Me.btnCheckUpdate.Name = "btnCheckUpdate"
        Me.btnCheckUpdate.Padding = New System.Windows.Forms.Padding(0, 2, 5, 2)
        Me.btnCheckUpdate.Selectable = True
        Me.btnCheckUpdate.Size = New System.Drawing.Size(58, 22)
        Me.btnCheckUpdate.TabIndex = 20
        Me.btnCheckUpdate.tbAdriftIconWhenHover = False
        Me.btnCheckUpdate.tbAutoSize = False
        Me.btnCheckUpdate.tbAutoSizeEx = True
        Me.btnCheckUpdate.tbBackgroundImage = Nothing
        Me.btnCheckUpdate.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnCheckUpdate.tbBadgeNumber = 0
        Me.btnCheckUpdate.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCheckUpdate.tbEndEllipsis = False
        Me.btnCheckUpdate.tbIconHoldPlace = True
        Me.btnCheckUpdate.tbIconImage = Nothing
        Me.btnCheckUpdate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCheckUpdate.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCheckUpdate.tbIconMore = False
        Me.btnCheckUpdate.tbIconMouseDown = Nothing
        Me.btnCheckUpdate.tbIconMouseHover = Nothing
        Me.btnCheckUpdate.tbIconMouseLeave = Nothing
        Me.btnCheckUpdate.tbIconPlaceText = 2
        Me.btnCheckUpdate.tbIconReadOnly = Nothing
        Me.btnCheckUpdate.tbImageMouseDown = Nothing
        Me.btnCheckUpdate.tbImageMouseHover = Nothing
        Me.btnCheckUpdate.tbImageMouseLeave = Nothing
        Me.btnCheckUpdate.tbProgressValue = 50
        Me.btnCheckUpdate.tbReadOnly = False
        Me.btnCheckUpdate.tbReadOnlyText = False
        Me.btnCheckUpdate.tbShadow = False
        Me.btnCheckUpdate.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCheckUpdate.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCheckUpdate.tbShowDot = False
        Me.btnCheckUpdate.tbShowMoreIconImg = CType(resources.GetObject("btnCheckUpdate.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCheckUpdate.tbShowNew = False
        Me.btnCheckUpdate.tbShowProgress = False
        Me.btnCheckUpdate.tbShowTip = True
        Me.btnCheckUpdate.tbShowToolTipOnButton = False
        Me.btnCheckUpdate.tbSplit = "3,3,3,3"
        Me.btnCheckUpdate.tbText = "检查更新"
        Me.btnCheckUpdate.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCheckUpdate.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCheckUpdate.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCheckUpdate.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCheckUpdate.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCheckUpdate.tbTextMouseDownPlace = 0
        Me.btnCheckUpdate.tbToolTip = ""
        Me.btnCheckUpdate.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCheckUpdate.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCheckUpdate.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCheckUpdate.VisibleEx = True
        '
        'lblVersion
        '
        Me.lblVersion.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblVersion.BackColor = System.Drawing.Color.Transparent
        Me.lblVersion.BindingForm = Nothing
        Me.lblVersion.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblVersion.Location = New System.Drawing.Point(4, 710)
        Me.lblVersion.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.lblVersion.Name = "lblVersion"
        Me.lblVersion.Padding = New System.Windows.Forms.Padding(5, 2, 0, 2)
        Me.lblVersion.Selectable = True
        Me.lblVersion.Size = New System.Drawing.Size(82, 22)
        Me.lblVersion.TabIndex = 21
        Me.lblVersion.tbAdriftIconWhenHover = False
        Me.lblVersion.tbAutoSize = True
        Me.lblVersion.tbAutoSizeEx = True
        Me.lblVersion.tbBackgroundImage = Nothing
        Me.lblVersion.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.lblVersion.tbBadgeNumber = 0
        Me.lblVersion.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lblVersion.tbEndEllipsis = False
        Me.lblVersion.tbIconHoldPlace = True
        Me.lblVersion.tbIconImage = Nothing
        Me.lblVersion.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblVersion.tbIconImageState = iTong.Components.ImageState.OneState
        Me.lblVersion.tbIconMore = False
        Me.lblVersion.tbIconMouseDown = Nothing
        Me.lblVersion.tbIconMouseHover = Nothing
        Me.lblVersion.tbIconMouseLeave = Nothing
        Me.lblVersion.tbIconPlaceText = 2
        Me.lblVersion.tbIconReadOnly = Nothing
        Me.lblVersion.tbImageMouseDown = Nothing
        Me.lblVersion.tbImageMouseHover = Nothing
        Me.lblVersion.tbImageMouseLeave = Nothing
        Me.lblVersion.tbProgressValue = 50
        Me.lblVersion.tbReadOnly = False
        Me.lblVersion.tbReadOnlyText = False
        Me.lblVersion.tbShadow = False
        Me.lblVersion.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblVersion.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblVersion.tbShowDot = False
        Me.lblVersion.tbShowMoreIconImg = CType(resources.GetObject("lblVersion.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lblVersion.tbShowNew = False
        Me.lblVersion.tbShowProgress = False
        Me.lblVersion.tbShowTip = True
        Me.lblVersion.tbShowToolTipOnButton = False
        Me.lblVersion.tbSplit = "3,3,3,3"
        Me.lblVersion.tbText = "同步助手 3.0"
        Me.lblVersion.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblVersion.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblVersion.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblVersion.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblVersion.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblVersion.tbTextMouseDownPlace = 0
        Me.lblVersion.tbToolTip = ""
        Me.lblVersion.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lblVersion.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lblVersion.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblVersion.VisibleEx = True
        '
        'picWeibo
        '
        Me.picWeibo.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.picWeibo.BackColor = System.Drawing.Color.Transparent
        Me.picWeibo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.picWeibo.Image = Nothing
        Me.picWeibo.Location = New System.Drawing.Point(804, 39)
        Me.picWeibo.Name = "picWeibo"
        Me.picWeibo.Size = New System.Drawing.Size(58, 68)
        Me.picWeibo.TabIndex = 22
        Me.picWeibo.TabStop = False
        Me.picWeibo.tbAutoSize = False
        Me.picWeibo.tbBackgroundImage = Nothing
        Me.picWeibo.tbSplit = "0,0,0,0"
        Me.picWeibo.Visible = False
        '
        'tmrRun
        '
        Me.tmrRun.Interval = 3000
        '
        'btnLogin
        '
        Me.btnLogin.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnLogin.BackColor = System.Drawing.Color.Transparent
        Me.btnLogin.BindingForm = Nothing
        Me.btnLogin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnLogin.Location = New System.Drawing.Point(864, 36)
        Me.btnLogin.Name = "btnLogin"
        Me.btnLogin.Padding = New System.Windows.Forms.Padding(0, 10, 0, 0)
        Me.btnLogin.Selectable = True
        Me.btnLogin.Size = New System.Drawing.Size(98, 78)
        Me.btnLogin.TabIndex = 23
        Me.btnLogin.tbAdriftIconWhenHover = False
        Me.btnLogin.tbAutoSize = False
        Me.btnLogin.tbAutoSizeEx = False
        Me.btnLogin.tbBackgroundImage = Nothing
        Me.btnLogin.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnLogin.tbBadgeNumber = 0
        Me.btnLogin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnLogin.tbEndEllipsis = False
        Me.btnLogin.tbIconHoldPlace = True
        Me.btnLogin.tbIconImage = Nothing
        Me.btnLogin.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnLogin.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnLogin.tbIconMore = False
        Me.btnLogin.tbIconMouseDown = Nothing
        Me.btnLogin.tbIconMouseHover = Nothing
        Me.btnLogin.tbIconMouseLeave = Nothing
        Me.btnLogin.tbIconPlaceText = 2
        Me.btnLogin.tbIconReadOnly = Nothing
        Me.btnLogin.tbImageMouseDown = Nothing
        Me.btnLogin.tbImageMouseHover = Nothing
        Me.btnLogin.tbImageMouseLeave = Nothing
        Me.btnLogin.tbProgressValue = 50
        Me.btnLogin.tbReadOnly = False
        Me.btnLogin.tbReadOnlyText = False
        Me.btnLogin.tbShadow = False
        Me.btnLogin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnLogin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnLogin.tbShowDot = False
        Me.btnLogin.tbShowMoreIconImg = CType(resources.GetObject("btnLogin.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnLogin.tbShowNew = False
        Me.btnLogin.tbShowProgress = False
        Me.btnLogin.tbShowTip = True
        Me.btnLogin.tbShowToolTipOnButton = False
        Me.btnLogin.tbSplit = "3,3,3,3"
        Me.btnLogin.tbText = "登录"
        Me.btnLogin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.tbTextColor = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDisable = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDown = System.Drawing.Color.White
        Me.btnLogin.tbTextColorHover = System.Drawing.Color.White
        Me.btnLogin.tbTextMouseDownPlace = 0
        Me.btnLogin.tbToolTip = ""
        Me.btnLogin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnLogin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnLogin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.Visible = False
        Me.btnLogin.VisibleEx = True
        '
        'menuLogout
        '
        Me.menuLogout.AccessibleDescription = "149x76"
        Me.menuLogout.DropShadowEnabled = False
        Me.menuLogout.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.menuLogout.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmTongbuAccountCenter, Me.ToolStripMenuItem1, Me.tsmChangeAccount, Me.tsmLogout})
        Me.menuLogout.Name = "menuMain"
        Me.menuLogout.Size = New System.Drawing.Size(149, 76)
        Me.menuLogout.Tag = ""
        Me.menuLogout.tbBackColor = System.Drawing.Color.White
        Me.menuLogout.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuLogout.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuLogout.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmTongbuAccountCenter
        '
        Me.tsmTongbuAccountCenter.Name = "tsmTongbuAccountCenter"
        Me.tsmTongbuAccountCenter.Size = New System.Drawing.Size(148, 22)
        Me.tsmTongbuAccountCenter.Text = "同步账号中心"
        '
        'ToolStripMenuItem1
        '
        Me.ToolStripMenuItem1.Name = "ToolStripMenuItem1"
        Me.ToolStripMenuItem1.Size = New System.Drawing.Size(145, 6)
        '
        'tsmChangeAccount
        '
        Me.tsmChangeAccount.Name = "tsmChangeAccount"
        Me.tsmChangeAccount.Size = New System.Drawing.Size(148, 22)
        Me.tsmChangeAccount.Text = "切换账号"
        '
        'tsmLogout
        '
        Me.tsmLogout.Name = "tsmLogout"
        Me.tsmLogout.Size = New System.Drawing.Size(148, 22)
        Me.tsmLogout.Text = "注销"
        '
        'btnTuiTutorial
        '
        Me.btnTuiTutorial.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnTuiTutorial.BackColor = System.Drawing.Color.Transparent
        Me.btnTuiTutorial.BindingForm = Nothing
        Me.btnTuiTutorial.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnTuiTutorial.Location = New System.Drawing.Point(697, 2)
        Me.btnTuiTutorial.Margin = New System.Windows.Forms.Padding(0)
        Me.btnTuiTutorial.Name = "btnTuiTutorial"
        Me.btnTuiTutorial.Padding = New System.Windows.Forms.Padding(4, 0, 0, 0)
        Me.btnTuiTutorial.Selectable = True
        Me.btnTuiTutorial.Size = New System.Drawing.Size(95, 21)
        Me.btnTuiTutorial.TabIndex = 24
        Me.btnTuiTutorial.tbAdriftIconWhenHover = False
        Me.btnTuiTutorial.tbAutoSize = True
        Me.btnTuiTutorial.tbAutoSizeEx = True
        Me.btnTuiTutorial.tbBackgroundImage = Nothing
        Me.btnTuiTutorial.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnTuiTutorial.tbBadgeNumber = 0
        Me.btnTuiTutorial.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTuiTutorial.tbEndEllipsis = False
        Me.btnTuiTutorial.tbIconHoldPlace = True
        Me.btnTuiTutorial.tbIconImage = Global.iTong.My.Resources.Resources.icon_warning2
        Me.btnTuiTutorial.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnTuiTutorial.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnTuiTutorial.tbIconMore = False
        Me.btnTuiTutorial.tbIconMouseDown = Nothing
        Me.btnTuiTutorial.tbIconMouseHover = Nothing
        Me.btnTuiTutorial.tbIconMouseLeave = Nothing
        Me.btnTuiTutorial.tbIconPlaceText = 2
        Me.btnTuiTutorial.tbIconReadOnly = Nothing
        Me.btnTuiTutorial.tbImageMouseDown = Nothing
        Me.btnTuiTutorial.tbImageMouseHover = Global.iTong.My.Resources.Resources.btn_1_transparent
        Me.btnTuiTutorial.tbImageMouseLeave = Nothing
        Me.btnTuiTutorial.tbProgressValue = 50
        Me.btnTuiTutorial.tbReadOnly = False
        Me.btnTuiTutorial.tbReadOnlyText = False
        Me.btnTuiTutorial.tbShadow = False
        Me.btnTuiTutorial.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTuiTutorial.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTuiTutorial.tbShowDot = False
        Me.btnTuiTutorial.tbShowMoreIconImg = CType(resources.GetObject("btnTuiTutorial.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnTuiTutorial.tbShowNew = False
        Me.btnTuiTutorial.tbShowProgress = False
        Me.btnTuiTutorial.tbShowTip = True
        Me.btnTuiTutorial.tbShowToolTipOnButton = False
        Me.btnTuiTutorial.tbSplit = "4,4,4,4"
        Me.btnTuiTutorial.tbText = "iOS9必备技能"
        Me.btnTuiTutorial.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTuiTutorial.tbTextColor = System.Drawing.Color.White
        Me.btnTuiTutorial.tbTextColorDisable = System.Drawing.Color.White
        Me.btnTuiTutorial.tbTextColorDown = System.Drawing.Color.White
        Me.btnTuiTutorial.tbTextColorHover = System.Drawing.Color.White
        Me.btnTuiTutorial.tbTextMouseDownPlace = 0
        Me.btnTuiTutorial.tbToolTip = ""
        Me.btnTuiTutorial.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnTuiTutorial.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTuiTutorial.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTuiTutorial.VisibleEx = True
        '
        'btnSkin
        '
        Me.btnSkin.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSkin.BackColor = System.Drawing.Color.Transparent
        Me.btnSkin.BindingForm = Nothing
        Me.btnSkin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnSkin.Font = New System.Drawing.Font("宋体", 8.0!)
        Me.btnSkin.Location = New System.Drawing.Point(828, 2)
        Me.btnSkin.Name = "btnSkin"
        Me.btnSkin.Padding = New System.Windows.Forms.Padding(1, 2, 5, 2)
        Me.btnSkin.Selectable = True
        Me.btnSkin.Size = New System.Drawing.Size(23, 21)
        Me.btnSkin.TabIndex = 25
        Me.btnSkin.tbAdriftIconWhenHover = False
        Me.btnSkin.tbAutoSize = True
        Me.btnSkin.tbAutoSizeEx = True
        Me.btnSkin.tbBackgroundImage = Nothing
        Me.btnSkin.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnSkin.tbBadgeNumber = 0
        Me.btnSkin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSkin.tbEndEllipsis = False
        Me.btnSkin.tbIconHoldPlace = True
        Me.btnSkin.tbIconImage = Global.iTong.My.Resources.Resources.btn_skin_3
        Me.btnSkin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSkin.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btnSkin.tbIconMore = False
        Me.btnSkin.tbIconMouseDown = Nothing
        Me.btnSkin.tbIconMouseHover = Nothing
        Me.btnSkin.tbIconMouseLeave = Nothing
        Me.btnSkin.tbIconPlaceText = 2
        Me.btnSkin.tbIconReadOnly = Nothing
        Me.btnSkin.tbImageMouseDown = Nothing
        Me.btnSkin.tbImageMouseHover = Nothing
        Me.btnSkin.tbImageMouseLeave = Nothing
        Me.btnSkin.tbProgressValue = 50
        Me.btnSkin.tbReadOnly = False
        Me.btnSkin.tbReadOnlyText = False
        Me.btnSkin.tbShadow = False
        Me.btnSkin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSkin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSkin.tbShowDot = False
        Me.btnSkin.tbShowMoreIconImg = CType(resources.GetObject("btnSkin.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSkin.tbShowNew = False
        Me.btnSkin.tbShowProgress = False
        Me.btnSkin.tbShowTip = True
        Me.btnSkin.tbShowToolTipOnButton = False
        Me.btnSkin.tbSplit = "13,11,13,11"
        Me.btnSkin.tbText = ""
        Me.btnSkin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSkin.tbTextColor = System.Drawing.Color.WhiteSmoke
        Me.btnSkin.tbTextColorDisable = System.Drawing.Color.WhiteSmoke
        Me.btnSkin.tbTextColorDown = System.Drawing.Color.WhiteSmoke
        Me.btnSkin.tbTextColorHover = System.Drawing.Color.White
        Me.btnSkin.tbTextMouseDownPlace = 0
        Me.btnSkin.tbToolTip = ""
        Me.btnSkin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSkin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSkin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSkin.VisibleEx = True
        '
        'menuSkin
        '
        Me.menuSkin.AccessibleDescription = "153x32"
        Me.menuSkin.DropShadowEnabled = False
        Me.menuSkin.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.menuSkin.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.ToolStripSeparator1})
        Me.menuSkin.Name = "menuMain"
        Me.menuSkin.Size = New System.Drawing.Size(61, 10)
        Me.menuSkin.Tag = ""
        Me.menuSkin.tbBackColor = System.Drawing.Color.White
        Me.menuSkin.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuSkin.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuSkin.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'ToolStripSeparator1
        '
        Me.ToolStripSeparator1.Name = "ToolStripSeparator1"
        Me.ToolStripSeparator1.Size = New System.Drawing.Size(57, 6)
        '
        'picLogo
        '
        Me.picLogo.BackColor = System.Drawing.Color.Transparent
        Me.picLogo.Cursor = System.Windows.Forms.Cursors.Default
        Me.picLogo.Image = Nothing
        Me.picLogo.Location = New System.Drawing.Point(17, 51)
        Me.picLogo.Name = "picLogo"
        Me.picLogo.Size = New System.Drawing.Size(158, 50)
        Me.picLogo.TabIndex = 26
        Me.picLogo.TabStop = False
        Me.picLogo.tbAutoSize = False
        Me.picLogo.tbBackgroundImage = Nothing
        Me.picLogo.tbSplit = "0,0,0,0"
        '
        'btnBuy
        '
        Me.btnBuy.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBuy.BackColor = System.Drawing.Color.Transparent
        Me.btnBuy.BindingForm = Nothing
        Me.btnBuy.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnBuy.Location = New System.Drawing.Point(596, 0)
        Me.btnBuy.Margin = New System.Windows.Forms.Padding(0)
        Me.btnBuy.Name = "btnBuy"
        Me.btnBuy.Padding = New System.Windows.Forms.Padding(0, 3, 1, 0)
        Me.btnBuy.Selectable = True
        Me.btnBuy.Size = New System.Drawing.Size(54, 23)
        Me.btnBuy.TabIndex = 27
        Me.btnBuy.tbAdriftIconWhenHover = False
        Me.btnBuy.tbAutoSize = True
        Me.btnBuy.tbAutoSizeEx = True
        Me.btnBuy.tbBackgroundImage = Nothing
        Me.btnBuy.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnBuy.tbBadgeNumber = 0
        Me.btnBuy.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBuy.tbEndEllipsis = False
        Me.btnBuy.tbIconHoldPlace = True
        Me.btnBuy.tbIconImage = Global.iTong.My.Resources.Resources.btn_buy_3
        Me.btnBuy.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBuy.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btnBuy.tbIconMore = False
        Me.btnBuy.tbIconMouseDown = Nothing
        Me.btnBuy.tbIconMouseHover = Nothing
        Me.btnBuy.tbIconMouseLeave = Nothing
        Me.btnBuy.tbIconPlaceText = 0
        Me.btnBuy.tbIconReadOnly = Nothing
        Me.btnBuy.tbImageMouseDown = Nothing
        Me.btnBuy.tbImageMouseHover = Global.iTong.My.Resources.Resources.btn_1_transparent
        Me.btnBuy.tbImageMouseLeave = Nothing
        Me.btnBuy.tbProgressValue = 50
        Me.btnBuy.tbReadOnly = False
        Me.btnBuy.tbReadOnlyText = False
        Me.btnBuy.tbShadow = False
        Me.btnBuy.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBuy.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBuy.tbShowDot = False
        Me.btnBuy.tbShowMoreIconImg = CType(resources.GetObject("btnBuy.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBuy.tbShowNew = False
        Me.btnBuy.tbShowProgress = False
        Me.btnBuy.tbShowTip = True
        Me.btnBuy.tbShowToolTipOnButton = False
        Me.btnBuy.tbSplit = "4,4,4,4"
        Me.btnBuy.tbText = "续费"
        Me.btnBuy.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBuy.tbTextColor = System.Drawing.Color.White
        Me.btnBuy.tbTextColorDisable = System.Drawing.Color.White
        Me.btnBuy.tbTextColorDown = System.Drawing.Color.White
        Me.btnBuy.tbTextColorHover = System.Drawing.Color.White
        Me.btnBuy.tbTextMouseDownPlace = 0
        Me.btnBuy.tbToolTip = ""
        Me.btnBuy.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBuy.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBuy.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBuy.VisibleEx = True
        '
        'MainForm
        '
        Me.BackColor = System.Drawing.SystemColors.Control
        Me.ClientSize = New System.Drawing.Size(978, 734)
        Me.Controls.Add(Me.btnBuy)
        Me.Controls.Add(Me.picLogo)
        Me.Controls.Add(Me.btnSkin)
        Me.Controls.Add(Me.btnTuiTutorial)
        Me.Controls.Add(Me.btnLogin)
        Me.Controls.Add(Me.picWeibo)
        Me.Controls.Add(Me.lblVersion)
        Me.Controls.Add(Me.btnCheckUpdate)
        Me.Controls.Add(Me.btnFeedBack)
        Me.Controls.Add(Me.pnlState)
        Me.Controls.Add(Me.pnlContainer)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.btn_normal)
        Me.Controls.Add(Me.btn_Setting)
        Me.Controls.Add(Me.btn_minimize)
        Me.MinimumSize = New System.Drawing.Size(800, 600)
        Me.Name = "MainForm"
        Me.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.pnlState.ResumeLayout(False)
        CType(Me.picMode, System.ComponentModel.ISupportInitialize).EndInit()
        Me.menuMain.ResumeLayout(False)
        CType(Me.picWeibo, System.ComponentModel.ISupportInitialize).EndInit()
        Me.menuLogout.ResumeLayout(False)
        Me.menuSkin.ResumeLayout(False)
        CType(Me.picLogo, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_minimize As tbButton
    Friend WithEvents btn_normal As tbButton
    Friend WithEvents btn_close As tbButton
    Friend WithEvents btn_Setting As tbButton
    Friend WithEvents pnlContainer As System.Windows.Forms.Panel
    Friend WithEvents pnlState As tbPanel
    Friend WithEvents btnDownload As tbButton
    Friend WithEvents lblState As System.Windows.Forms.Label
    Friend WithEvents lblExpireTime As System.Windows.Forms.Label
    Friend WithEvents menuMain As tbContextMenuStrip
    Friend WithEvents tsmUpdate As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripMenuItem2 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmSetting As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAbout As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiSelectLang As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tss2 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiFeedback As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnFeedBack As tbButton
    Friend WithEvents downloadBar As tbDownloadBar
    Friend WithEvents picMode As System.Windows.Forms.PictureBox
    Friend WithEvents btnCheckUpdate As tbButton
    Friend WithEvents lblVersion As tbButton
    Friend WithEvents picWeibo As tbPictureBox
    Friend WithEvents tmrRun As System.Windows.Forms.Timer
    Friend WithEvents btnLogin As tbButton
    Friend WithEvents menuLogout As tbContextMenuStrip
    Friend WithEvents tsmChangeAccount As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmLogout As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmTongbuAccountCenter As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripMenuItem1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents btnTuiTutorial As iTong.Components.tbButton
    Friend WithEvents tsmiSelectSkin As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnSkin As iTong.Components.tbButton
    Friend WithEvents menuSkin As iTong.Components.tbContextMenuStrip
    Friend WithEvents ToolStripSeparator1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiGetLogs As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents picLogo As iTong.Components.tbPictureBox
    Friend WithEvents ToolStripSeparator3 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiCoexistPay As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripSeparator4 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tssSkin As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmSkin As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnBuy As iTong.Components.tbButton
    Friend WithEvents tsmiSplitLogin As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiLogin As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmTongbuPersonalCenterEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmTongbuAccountCenterEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmChangeAccountEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmLogoutEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripSeparator2 As System.Windows.Forms.ToolStripSeparator

End Class
