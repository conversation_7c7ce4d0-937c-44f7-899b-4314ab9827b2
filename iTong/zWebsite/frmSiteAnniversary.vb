﻿Public Class frmSiteAnniversary

    Public Sub New(ByVal application As IApplication, ByVal device As IDevice, ByVal strUrl As String)
        MyBase.New(application, device)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        If Me.mDeivce IsNot Nothing Then
            Me.Name = Me.mDeivce.Identifier & "frmSiteAnniversary"
        Else
            Me.Name = "frmSiteAnniversary"
        End If

        Me.mUrl = "http://v3.tongbu.com/share/anniversary.html#!"
        If Not String.IsNullOrEmpty(strUrl) Then
            Me.mUrl = strUrl
        End If
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.Size = New Size(837, 604)
        Me.btn_normal.Visible = False
        Me.CanResize = False
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = "活动"
    End Sub

    Protected Overrides Sub OnLoginOut(ByVal sender As Object, ByVal e As EventArgs)
        Try
            Dim loginObj As PluginLogin = sender
            Me.wbsSite.UserInfo = loginObj.UserInfo
            Me.wbsSite.UserInfoEncode = loginObj.UserInfoEncode
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSiteAnniversary_OnLoginOut")
        End Try
      
        Me.RefreshUrl()
    End Sub

    '教程显示
    Public Shared Sub ShowForm(ByVal app As IApplication, ByVal strUrl As String)
        Dim frm As frmSiteAnniversary = Nothing
        For Each Item As Form In My.Application.OpenForms()
            If TypeOf Item Is frmSiteAnniversary Then
                frm = Item
            End If

            If app Is Nothing AndAlso TypeOf Item Is MainForm Then
                app = Item
            End If
            If frm IsNot Nothing AndAlso app IsNot Nothing Then
                Exit For
            End If
        Next

        Dim isCreateNewForm As Boolean = True
        If frm IsNot Nothing Then
            If frm.IsDisposed Then
                '如果窗体被释放了， 直接销毁重建
                frm = Nothing
            Else
                frm.Activate()
                frm.WindowState = FormWindowState.Normal
                frm.BringToFront()
                isCreateNewForm = False
            End If
        End If

        '创建新的窗体
        If isCreateNewForm Then
            frm = New frmSiteAnniversary(app, Nothing, strUrl)
            Utility.ShowFormInMiddleLocation(app, frm)
            frm.Show(app)
        End If
    End Sub

    Protected Overrides Sub OnSizeChanged(ByVal e As System.EventArgs)
        MyBase.OnSizeChanged(e)
        Me.btn_minimize.Location = New Point(Me.btn_close.Left - Me.btn_minimize.Width - 2, Me.btn_minimize.Top)
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)
        Me.btn_minimize.Location = New Point(Me.btn_close.Left - Me.btn_minimize.Width - 2, Me.btn_minimize.Top)
    End Sub

End Class