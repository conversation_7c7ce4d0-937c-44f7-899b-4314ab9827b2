﻿'移到CoreModuleCS
Imports System.Threading
Imports System.Text.RegularExpressions
Imports System.Text
'Imports WebKit

Public Class WebSiteHelper
    Inherits WebSiteHelperBase
    Private mInstallHelper As iPhoneInstallHelper = Nothing
    Private mThreadTopicDownload As Thread
    Private mtdGetAllInstalled As Thread

    Public Event ShowTongbuLogin As EventHandler(Of EventArgs)
    Public Event ShowWeibo As EventHandler(Of ShowWeiboArgs)
    Public Event GetDeviceForWeb As EventHandler(Of GetDeviceForWebArgs)

    Public Sub New()
        Me.mDownManage = tbPlugins.DownloadManager
    End Sub

    Public Shared Function Instance() As WebSiteHelper
        Try
            SyncLock mLocker
                If mWebSiteHelperBase Is Nothing Then
                    mWebSiteHelperBase = New WebSiteHelper()
                End If
            End SyncLock
        Catch ex As Exception
        End Try

        Return mWebSiteHelperBase
    End Function

    Public Overrides Sub DownloadAppByIDEx(ByVal application As Components.IApplication, ByVal device As Device.iPhoneDevice, ByVal strID As String, ByVal strIdentifier As String, ByVal strName As String, Optional source As DownloadCenterSource = DownloadCenterSource.None)
        MyBase.DownloadAppByIDEx(application, device, strID, strIdentifier, strName)
        '最后一个参数5008是用户下载软件统计用的。助手推荐下载地址用的是5008的标签。
        Dim obj As CoreSignatureArgs = CoreUpdateHelper.Instance.GetSignatureOverdueData(strID, strIdentifier, "5008")
        If String.IsNullOrEmpty(obj.SidURL) Then
            Return
        End If

        Dim blnAbroad As Boolean = False
        If obj.Area = "1" Then
            blnAbroad = True
        End If
        WebSiteHelper.Instance().Start2AnalyseDownloadUrlByDownloadCenter(application, obj.SidURL, strName, "", strID, _
                                                                        "", False, "", device, False, "", "", blnAbroad, False, True, Nothing, source)

        'WebSiteHelper.Instance().Start2AnalyseDownloadUrlByDownloadCenter(application, strDownloadUrl, strAppName, strLoadId, strItemId, strVersion, _
        '                                         blnNeedToBackInfo, strIconUrl, device, blnJailbreak, strBTTypeText, "", blnAbroad)
    End Sub

    Public Overrides Sub DownloadFromStore(ByVal info As PackageInfo, ByVal strTongbuUrl As String)
        MyBase.DownloadFromStore(info, strTongbuUrl)
        CoreUpdateHelper.Instance().DownloadAppleSoft(info, False, ResourceClass.Software, strTongbuUrl)
    End Sub

    Public Overrides Function ShowLoginForm(ByVal dev As iPhoneDevice) As String
        Return CoreUpdateHelper.Instance().ShowLoginFormEx(dev, LoginMode.DownShareApp)
    End Function

    Public Overrides Function CheckLogin(ByVal strAccount As String) As Boolean

        Return CoreUpdateHelper.Instance().CheckLogin(strAccount)
    End Function

    Public Sub AutoDownloadRing(ByVal strPara As String)
        Try
            Dim strUrlDecode As String = System.Web.HttpUtility.UrlDecode(strPara).TrimEnd("/")
            Me.DownloadRingtone(strUrlDecode)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WebSiteHelper_AutoDownloadRing")
        End Try
    End Sub

    Public Sub AutoDownloadWallpaper(ByVal strPara As String)
        Try
            Dim strUrlDecode As String = System.Web.HttpUtility.UrlDecode(strPara).TrimEnd("/")
            If strUrlDecode.StartsWith("tongbu://wallpaper/download?", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadWallPaper(strUrlDecode)

            ElseIf strUrlDecode.StartsWith("tongbu://android/wallpaper/download?", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadWallPaper(strUrlDecode, "Android")

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WebSiteHelper_AutoDownloadWallpaper")
        End Try
    End Sub

    Public Overrides Sub WebBrowserNavigating(ByVal application As IApplication, ByVal device As IDevice, ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs, Optional ByVal type As String = "")
        Try
            If TypeOf device Is iPhoneDevice Then
                Me.mInstallHelper = iPhoneInstallHelper.GetInstance(device)
            End If

            Dim url As String = e.Url.ToString()
            'Common.Log(String.Format("WebSiteHelper.WebBrowserNavigating: {0}", url))

            If url.Contains("appname=同步推") Then
                Try
                    If device IsNot Nothing Then
                        frmSummary.ShowFormTuiInstall(False, True, device, application)
                        e.Cancel = True
                        Return
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "WebBrowserNavigating_同步推")
                End Try
            End If

            If url.EndsWith(".ipa", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadIPA(device, url)
                e.Cancel = True

            ElseIf Not String.IsNullOrEmpty(e.Url.Query) AndAlso e.Url.Query.IndexOf("actiontype=") >= 0 AndAlso Not url.StartsWith("http://pc/tongbu/apk/download/", StringComparison.OrdinalIgnoreCase) Then
                Dim source As DownloadCenterSource = DownloadCenterSource.None
                If type = "popup" Then
                    source = DownloadCenterSource.Popup
                End If
                If TypeOf device Is iPhoneDevice Then
                    Me.DownloadApp(application, device, e.Url.OriginalString, source)
                Else
                    Me.DownloadApp(application, Nothing, e.Url.OriginalString, source)
                End If
                e.Cancel = True
            ElseIf url.StartsWith("tongbu://app/download", StringComparison.OrdinalIgnoreCase) Then
                'tongbu://app/download/itms?
                Dim strUrl As String = url.ToLower
                Dim info As New PackageInfo()
                info.ItemId = Utility.GetParamValueFromQuery("appleid", strUrl)
                info.Name = Utility.GetParamValueFromQuery("name", strUrl)
                info.Version = Utility.GetParamValueFromQuery("version", strUrl)
                If device IsNot Nothing Then
                    info.DeviceId = device.DeviceID
                End If

                CoreUpdateHelper.Instance().DownloadAppleSoft(info, False)

                e.Cancel = True
            ElseIf url.StartsWith("tongbu://deb/download", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadDeb(device, sender, url)
                e.Cancel = True
            ElseIf url.StartsWith("tongbu://restartsprindboard/", StringComparison.OrdinalIgnoreCase) Then
                If device IsNot Nothing AndAlso device.IsConnected AndAlso TypeOf device Is iPhoneDevice Then
                    CType(device, iPhoneDevice).Restart()
                    'device.Restart() 'modify
                End If
                e.Cancel = True
            ElseIf url.StartsWith("tongbu://app/topicdownload", StringComparison.OrdinalIgnoreCase) Then
                'url = "tongbu://app/topicdownload?appleids=563443400,631177,469341118,524359189,522704697,512456155,488156323,506234146,431213733,431688122,504575083,512772066,480189064,479952446"
                Me.DownloadTopicApp(application, device, url)
                e.Cancel = True

            ElseIf url.StartsWith("tongbu://wallpaper/download?", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadWallPaper(url)
                e.Cancel = True

            ElseIf url.StartsWith("tongbu://android/wallpaper/download?", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadWallPaper(url, "Android")
                e.Cancel = True

            ElseIf url.StartsWith("tongbu://ring/download?", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadRingtone(url)
                e.Cancel = True

            ElseIf url.StartsWith("tongbu://music/download/", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadMusic(url, e.Url.OriginalString)
                e.Cancel = True

                'ElseIf url.StartsWith("tongbu://music/search?name=", StringComparison.OrdinalIgnoreCase) Then
                '    Dim strName As String = Utility.GetParamValueFromQuery("name", url)
                '    Me.txtSearch.Text = strName
                '    Me.StartSearch()
                '    e.Cancel = True

                '视频下载暂时屏蔽掉
                'ElseIf url.StartsWith("tongbu://video/download", StringComparison.OrdinalIgnoreCase) Then
                '    Me.DownloadVideo(url)
                '    e.Cancel = True

            ElseIf url.IndexOf("itms:", StringComparison.OrdinalIgnoreCase) >= 0 Then
                '在 iTunes 打开页面(购买正版)，验证iTunes是不是安装，如果没有安装提示用户
                If Common.CheckiTunesInstall() Then
                    Common.OpenExplorer(url)
                Else
                    Try
                        'Dim webBorwser As tbWebBrowser = CType(sender, tbWebBrowser)
                        CType(sender, tbWebBrowser).Document.InvokeScript("tip", New Object() {"noitunes"})
                    Catch ex As Exception
                        Debug.Print(ex.ToString)
                    End Try
                End If
                e.Cancel = True

            ElseIf url.StartsWith("tongbu://weibo/sina/send?msg=", StringComparison.OrdinalIgnoreCase) Then
                e.Cancel = True
                'DoSinaWeiBo(url)

            ElseIf url.StartsWith("ie://", StringComparison.OrdinalIgnoreCase) Then
                '在IE中直接显示 "ie://url" pc端会替换ie://为http://
                Common.OpenExplorer(url.Replace("ie://", "http://"))
                e.Cancel = True

            ElseIf url.ToLower().Contains("itong://refresh") Then
                e.Cancel = True
                Dim wb As tbWebBrowser = CType(sender, tbWebBrowser)
                wb.Navigate(wb.Name)

            ElseIf url.ToLower().Contains("tongbu://jp/?") Then
                e.Cancel = True

                Dim intSrc As Integer = Val(Utility.GetParamValueFromQuery("src", e.Url.ToString(), "&", False, True))
                Dim jumpUrl As String = Utility.GetParamValueFromQuery("url", e.Url.ToString(), "&", False, True)
                Dim strNode As String = "AppShare"

                jumpUrl = System.Web.HttpUtility.UrlDecode(jumpUrl)
                If intSrc = 1 Then
                    strNode = "App"
                ElseIf intSrc = 2 Then
                    strNode = "AppAndroid"
                End If

                Dim strDeviceID As String = ""
                If device IsNot Nothing Then
                    strDeviceID = device.DeviceID
                End If

                If Common.VerIs30 Then
                    application.GotoSite(ActionFuncType.App, strNode, strDeviceID, jumpUrl)
                    '3.0没有用到这个代码
                    'Else
                    '    If type = "app" Then
                    '        Me.SetfrmAppSelectNode(strNode, device)
                    '    Else
                    '        application.GotoSite(strNode, jumpUrl)
                    '    End If
                End If

            ElseIf url.StartsWith("http://pc/tongbu/apk/download", StringComparison.InvariantCultureIgnoreCase) AndAlso Common.VerIs30() AndAlso (device Is Nothing OrElse TypeOf device Is AndroidDevice) Then
                Dim adbDevice As AndroidDevice = Nothing
                Dim args As New GetDeviceForWebArgs
                RaiseEvent GetDeviceForWeb(Me, args)
                If device Is Nothing AndAlso application IsNot Nothing AndAlso args.Device IsNot Nothing Then
                    If TypeOf args.Device Is AndroidDevice Then
                        adbDevice = CType(args.Device, AndroidDevice)
                    End If
                ElseIf TypeOf device Is AndroidDevice Then
                    adbDevice = device
                End If

                CoreUpdateHelper.Instance(adbDevice)

                If TypeOf device Is AndroidDevice Then
                    Me.DownloadAPKEx(application, device, e.Url.OriginalString)
                Else
                    Me.DownloadAPKEx(application, Nothing, e.Url.OriginalString)
                End If
                e.Cancel = True

            ElseIf url.StartsWith("tongbu://weixin/download?", StringComparison.OrdinalIgnoreCase) Then
                Me.DownloadWeixinEmoticon(device, sender, url)

            ElseIf url.EndsWith(".ipsw", StringComparison.OrdinalIgnoreCase) Then
                '接管固件下载
                Me.DownloadIPSW(url)
                e.Cancel = True

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "WebSiteHelper.WebBrowserNavigating")
        End Try
    End Sub

    Public Sub WebBrowserNewWindow3(ByVal sender As tbWebBrowserEx, ByRef ppDisp As Object, ByRef Cancel As Boolean, ByVal dwFlags As UInteger, ByVal bstrUrlContext As String, ByVal bstrUrl As String, Optional ByVal webObject As WebSiteObject = Nothing)

        If bstrUrl.EndsWith(".ipsw", StringComparison.OrdinalIgnoreCase) Then
            '接管固件下载
            Me.DownloadIPSW(bstrUrl)
            Cancel = True
        ElseIf webObject IsNot Nothing AndAlso webObject.Name.ToLower() = "weblive" Then

            If ShowNewWindow(webObject) Then
                Common.OpenExplorer(bstrUrl)
                Cancel = True
                Return
            End If

            '直播截取跳转链接在webbrowser中显示
            If bstrUrl Is Nothing OrElse String.IsNullOrEmpty(bstrUrl) Then
                Return
            End If

            If bstrUrl.Contains("toPay") Then
                Common.OpenExplorer(bstrUrl)
                Cancel = True
                Return
            End If

            If Not bstrUrl.ToLower.Contains("javascript") Then
                Cancel = True
                sender.Navigate(bstrUrl)
                sender.ScriptErrorsSuppressed = True
            End If

        ElseIf Not String.IsNullOrEmpty(bstrUrl) Then
            Common.OpenExplorer(bstrUrl.Replace("ie://", "http://"))
            Cancel = True

        End If
    End Sub


    Public Function ShowNewWindow(ByVal webObject As WebSiteObject) As Boolean
        Dim blnShow As Boolean = True
        Try
            If webObject IsNot Nothing Then
                blnShow = webObject.ShowNewWindow
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowNewWindow")
        End Try
        Return blnShow
    End Function
    Public Sub WebBrowserNavigated(ByVal device As iPhoneDevice, ByVal sender As Object, ByVal e As System.Windows.Forms.WebBrowserNavigatedEventArgs)
        Try
            Dim objPara() As Object = New Object() {device, sender}
            'Dim thr As New Thread(New ParameterizedThreadStart(AddressOf SetBrowserStatus))
            'With thr
            '    .IsBackground = True
            '    .Start(objPara)
            'End With
            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf SetBrowserStatus), objPara)
        Catch ex As Exception
        End Try
    End Sub

    Private Sub SetBrowserStatus(ByVal objPara As Object)
        Try

            If objPara Is Nothing OrElse objPara(1) Is Nothing Then
                Return
            End If

            Dim device As iPhoneDevice = CType(objPara(0), iPhoneDevice)
            Dim browser As tbWebBrowserEx = CType(objPara(1), tbWebBrowserEx)

            '' 使用WebKit
            'Dim browser As WebKitBrowserEx = CType(objPara(1), WebKitBrowserEx)

            If device IsNot Nothing AndAlso device.IsConnected Then
                browser.IsConnected = True

                If device.Jailbreaked OrElse device.InstallCydia Then
                    browser.Jailbreaked = True
                    Me.SetWebStatus(browser, 0, WebPluginStatus.DeviceConnected)

                Else
                    browser.Jailbreaked = False
                    Me.SetWebStatus(browser, 0, WebPluginStatus.Jail)
                End If

            Else
                browser.IsConnected = False
                browser.Jailbreaked = False
                Me.SetWebStatus(browser, 0, WebPluginStatus.DeviceDisconnect)
            End If

            Me.SetWallpaperType(device, browser)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WebSiteHelper.SetBrowserStatus")
        End Try
    End Sub

    Public Sub SetWallpaperType(ByVal device As iPhoneDevice, ByVal browser As tbWebBrowserEx)
        '壁纸站接入相应设备后选中类型。
        If device IsNot Nothing AndAlso _
           browser IsNot Nothing AndAlso _
           browser.Tag IsNot Nothing AndAlso _
           TypeOf browser.Tag Is String AndAlso _
           browser.Tag.ToString().ToLower = "webwallpaper" Then

            Me.SetWebAppMarketStatus(browser, "switchApi", frmSummary.FormatProductForJailbreak(device.ProductType), "")
        End If
    End Sub

    Public Sub WebBrowserNewMessage(ByVal device As iPhoneDevice, ByVal sender As tbWebBrowser, ByVal message As String, Optional ByVal tabPage As tbTabPanel = Nothing)
        Me.OperateWebNewMessage(device, sender, message, tabPage)
    End Sub

    '#Region "==== WebKitBrowser ===="
    '    Public Sub WebKitBrowserNavigated(ByVal device As iPhoneDevice, ByVal sender As Object, ByVal e As WebBrowserNavigatedEventArgs)
    '        Dim objPara() As Object = New Object() {device, sender}

    '        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf SetBrowserStatus), objPara)
    '    End Sub

    '    Public Sub WebKitBrowserNavigating(ByVal application As IApplication, ByVal device As IDevice, ByVal sender As System.Object, ByVal e As WebBrowserNavigatingEventArgs, Optional ByVal type As String = "")
    '        Try
    '            If TypeOf device Is iPhoneDevice Then
    '                Me.mInstallHelper = iPhoneInstallHelper.GetInstance(device)
    '            End If

    '            Dim urlStr As String = e.Url.ToString()

    '            e.Cancel = True

    '            If urlStr.EndsWith(".ipa", StringComparison.InvariantCultureIgnoreCase) Then

    '                Me.DownloadIPA(CType(device, iPhoneDevice), urlStr)

    '            ElseIf Not String.IsNullOrEmpty(e.Url.Query) AndAlso _
    '                   e.Url.Query.IndexOf("actiontype=") >= 0 AndAlso _
    '                   Not urlStr.StartsWith("http://pc/tongbu/apk/download/", StringComparison.InvariantCultureIgnoreCase) Then

    '                Me.DownloadApp(application, CType(device, iPhoneDevice), e.Url.OriginalString)

    '            ElseIf urlStr.StartsWith("tongbu://app/download", StringComparison.OrdinalIgnoreCase) Then

    '                Dim strUrl As String = urlStr.ToLower()
    '                Dim info As New PackageInfo()

    '                info.ItemId = Utility.GetParamValueFromQuery("appleid", strUrl)
    '                info.Name = Utility.GetParamValueFromQuery("name", strUrl)
    '                info.Version = Utility.GetParamValueFromQuery("version", strUrl)

    '                CoreUpdateHelper.Instance().DownloadAppleSoft(info)
    '            ElseIf urlStr.StartsWith("tongbu://deb/download", StringComparison.OrdinalIgnoreCase) Then

    '                Me.DownloadDeb(CType(device, iPhoneDevice), sender, urlStr)

    '            ElseIf urlStr.StartsWith("tongbu://restartsprindboard/", StringComparison.OrdinalIgnoreCase) Then

    '                If device IsNot Nothing AndAlso device.IsConnected AndAlso TypeOf device Is iPhoneDevice Then
    '                    CType(device, iPhoneDevice).Restart()
    '                End If

    '            ElseIf urlStr.StartsWith("tongbu://app/topicdownload", StringComparison.OrdinalIgnoreCase) Then

    '                Me.DownloadTopicApp(application, CType(device, iPhoneDevice), urlStr)

    '            ElseIf urlStr.StartsWith("tongbu://wallpaper/download?", StringComparison.OrdinalIgnoreCase) Then

    '                Me.DownloadWallPaper(urlStr)

    '            ElseIf urlStr.StartsWith("tongbu://ring/download?", StringComparison.OrdinalIgnoreCase) Then

    '                Me.DownloadRingtone(urlStr)

    '            ElseIf urlStr.StartsWith("tongbu://music/download/", StringComparison.OrdinalIgnoreCase) Then

    '                Me.DownloadMusic(urlStr, e.Url.OriginalString)

    '            ElseIf urlStr.ToLower().IndexOf("itms:") >= 0 Then

    '                If Common.CheckiTunesInstall() Then
    '                    Common.OpenExplorer(urlStr)
    '                Else
    '                    Try
    '                        CType(sender, WebKitBrowserEx).Document.InvokeScriptMethod("tip", New Object() {"noitunes"})
    '                    Catch ex As Exception
    '                        Debug.Write(ex)
    '                    End Try
    '                End If

    '            ElseIf urlStr.StartsWith("ie://", StringComparison.OrdinalIgnoreCase) Then

    '                Common.OpenExplorer(urlStr.Replace("ie://", "http://"))

    '            ElseIf urlStr.ToLower().Contains("itong://refresh") Then
    '                Dim wb As WebKitBrowserEx = CType(sender, WebKitBrowserEx)
    '                wb.Navigate(wb.Name)

    '            ElseIf urlStr.ToLower().Contains("tongbu://jp/?") Then

    '                Dim intSrc As Integer = 0
    '                Int32.TryParse(Utility.GetParamValueFromQuery("src", urlStr, "&", False, True), intSrc)

    '                Dim jumpUrl As String = Utility.GetParamValueFromQuery("url", urlStr, "&", False, True)
    '                Dim strNode As String = "AppShare"

    '                jumpUrl = System.Web.HttpUtility.UrlDecode(jumpUrl)
    '                If intSrc = 1 Then
    '                    strNode = "App"
    '                ElseIf intSrc = 2 Then
    '                    strNode = "AppAndroid"
    '                End If

    '                If Common.VerIs30 Then
    '                    Dim strDeviceID As String = ""
    '                    If device IsNot Nothing Then
    '                        strDeviceID = device.DeviceID
    '                    End If

    '                    application.GotoSite(ActionFuncType.App, strNode, strDeviceID, jumpUrl)
    '                Else
    '                    If type.Equals("app", StringComparison.InvariantCultureIgnoreCase) Then
    '                        Me.SetfrmAppSelectNode(strNode, CType(device, iPhoneDevice))
    '                    Else
    '                        application.GotoSite(strNode, jumpUrl)
    '                    End If
    '                End If

    '            ElseIf urlStr.StartsWith("http://pc/tongbu/apk/download") AndAlso Common.VerIs30() AndAlso _
    '                  (device IsNot Nothing AndAlso TypeOf device Is AndroidDevice) Then

    '                Me.DownloadAPKEx(application, CType(device, AndroidDevice), e.Url.OriginalString)

    '            ElseIf urlStr.StartsWith("tongbu://weibo/sina/send?msg=", StringComparison.OrdinalIgnoreCase) Then
    '                e.Cancel = True
    '            Else
    '                e.Cancel = False
    '            End If

    '        Catch ex As Exception
    '            e.Cancel = False
    '            Debug.Write(ex)
    '        End Try
    '    End Sub


    '    Private Sub SetWebKitBrowserStatus(ByVal state As Object)
    '        Try
    '            Dim device As iPhoneDevice = CType(state(0), iPhoneDevice)
    '            Dim browser As WebKitBrowserEx = CType(state(1), WebKitBrowserEx)

    '            If browser Is Nothing Then
    '                Return
    '            End If



    '        Catch ex As Exception

    '        End Try
    '    End Sub
    '#End Region

#Region "--- web收集信息方法 ---"

    Private Sub OperateWebNewMessage(ByVal device As iPhoneDevice, ByVal webBrowser As tbWebBrowser, ByVal message As String, Optional ByVal tabPage As tbTabPanel = Nothing)
        Common.LogTest("接收web的数据")
        Common.LogTest(message)

        If String.IsNullOrEmpty(message) Then
            Return
        End If

        Try
            Dim objJson As JsonObject = JsonParser.ParseString(message)

            If objJson Is Nothing Then
                Return
            End If

            Dim strAction As String = String.Empty

            If objJson.ContainsKey("action") Then
                strAction = CType(objJson("action"), JsonString).Value
            End If

            Dim strBridgeCallBack As String = ""                    '给web回传信息时候用的函数名
            If objJson.ContainsKey("bridgeCallBack") Then
                strBridgeCallBack = CType(objJson("bridgeCallBack"), JsonString).Value
            End If

            Select Case strAction
                Case "PostWebOperateData"
                    ActionCollectHelper.SaveActionData(message)
                    'Common.LogTest("保存web行为数据")

                Case "GetAllInstalledApps"
                    Me.SetWebAppStatus_AllInstalled(device, webBrowser)

                Case "NavBarStatus"
                    If tabPage Is Nothing Then
                        Return
                    End If
                    Dim strFrom As String = ""
                    Dim strType As String = ""
                    If objJson.ContainsKey("from") Then
                        strFrom = CType(objJson("from"), JsonString).Value
                    End If

                    If objJson.ContainsKey("type") Then
                        strType = CType(objJson("type"), JsonString).Value
                    End If
                    Me.SetTabPageStatus(tabPage, strFrom, strType)
                Case "Login"
                    RaiseEvent ShowTongbuLogin(Me, Nothing)
                    'frmLogin.ShowLogin(Nothing, False)

                Case "SendWeiBo"
                    'ThreadPool.QueueUserWorkItem(AddressOf SendWebBoThread, objJson)
                    Me.SendWebBo(objJson)
                    Me.SetWebAppMarketStatus(webBrowser, "bridgeCallBack", strBridgeCallBack, "")

                Case "encryptRequest"
                    Me.WebEncryptRequest(message, strBridgeCallBack, webBrowser)

                Case "clientInfo"
                    Me.GetClientInfo(device, strBridgeCallBack, webBrowser)

            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OperateWebNewMessage")
        End Try
    End Sub

    Private Sub SendWebBo(ByVal objPara As Object)
        Try
            Dim objJson As JsonObject = objPara
            Dim strContact As String = ""                   '存放微博发送的内容和Q空间的URL
            Dim type As ClientType = ClientType.Sina
            Dim img As Image = Nothing

            If objJson.ContainsKey("content") AndAlso Not TypeOf objJson("content") Is JsonNull Then
                strContact = CType(objJson("content"), JsonString).Value
            End If

            If objJson.ContainsKey("type") AndAlso Not TypeOf objJson("type") Is JsonNull Then
                Dim strType As String = ""
                strType = CType(objJson("type"), JsonString).Value
                If strType.ToLower = "qzone" Then
                    Common.OpenExplorer(strContact)
                    Return
                End If
            End If

            If objJson.ContainsKey("imgUrl") AndAlso Not TypeOf objJson("imgUrl") Is JsonNull Then
                Dim strImgUrl As String = ""
                strImgUrl = CType(objJson("imgUrl"), JsonString).Value
                img = Me.GetWeiboImage(strImgUrl)
            End If

            Dim args As New ShowWeiboArgs
            args.Type = type
            args.Img = img
            args.Contact = strContact
            RaiseEvent ShowWeibo(Me, args)
            'WeiboHelper.SendMessage(type, Nothing, strContact, img, "", Nothing, False)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WebSiteHelper_ShowWebBo")
        End Try
    End Sub

    Private Function GetWeiboImage(ByVal strPicUrl As String) As Image
        Dim img As Image = Nothing
        Dim strImgPath As String = Path.Combine(Folder.TempFolder, "Weibo")
        If strPicUrl.Length > 0 Then
            Folder.CheckFolder(strImgPath)
            strImgPath = Path.Combine(strImgPath, Path.GetFileName(strPicUrl))
            If Not File.Exists(strImgPath) Then
                ThreadPool.QueueUserWorkItem(AddressOf DownLoadImage, New String() {strPicUrl, strImgPath})
                Dim intCount As Integer = 0
                While True
                    If File.Exists(strImgPath) OrElse intCount > 5 Then
                        Exit While
                    End If
                    Utility.WaitSeconds(1)
                    intCount += 1
                End While
                'Common.DownloadImage(strPicUrl, 20000, strImgPath)
            End If

            If File.Exists(strImgPath) Then
                img = Utility.GetImageFormFile(strImgPath)
            End If
        End If
        Return img
    End Function

    Private Sub DownLoadImage(ByVal objPara As Object)
        Try
            If objPara Is Nothing OrElse Not TypeOf objPara Is String() Then
                Return
            End If
            Dim strPara() As String = objPara
            Common.DownloadImage(strPara(0), 20000, strPara(1))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WebSiteHelper_DownLoadImage")
        End Try
    End Sub

    Private Sub GetClientInfo(ByVal device As iPhoneDevice, ByVal strBridgeCallBack As String, ByVal webBrowser As tbWebBrowser)
        Try
            Dim objPara() As Object = New Object() {device, strBridgeCallBack, webBrowser}
            Dim thr As New Thread(New ParameterizedThreadStart(AddressOf GetClientInfoThread))
            With thr
                .IsBackground = True
                .Start(objPara)
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "WebSiteHelper_GetClientInfo")
        End Try
    End Sub

    '取得基本信息
    Private Sub GetClientInfoThread(ByVal objPara As Object)
        Try
            Dim device As iPhoneDevice = objPara(0)
            Dim strBridgeCallBack As String = objPara(1)
            Dim webBrowser As tbWebBrowser = objPara(2)
            Dim strTuiSku As String = ""
            Dim strTuiVersion As String = ""
            Dim jOnject As New JsonObject
            Dim intD As Integer = 1
            Dim strProductType As String = ""
            Dim strProductVersion As String = ""
            Dim strSN As String = ""
            Dim blnJailbreaked As Boolean = False

            If device IsNot Nothing Then
                If device.ProductType.ToLower.Contains("ipad") Then
                    intD = 2
                End If
                Dim tuiInfo As FileSharingPackageInfo = device.GetTuiInfo()
                If tuiInfo IsNot Nothing Then
                    strTuiSku = tuiInfo.Identifier
                    strTuiVersion = tuiInfo.Version
                End If
                strProductType = device.ProductType
                strProductVersion = device.ProductVersion
                strSN = device.SerialNumber
                blnJailbreaked = device.Jailbreaked
            End If

            jOnject.Add("ProductType", strProductType)
            jOnject.Add("FirmwareVersion", strProductVersion)
            jOnject.Add("ZhushouVersion", My.Application.Info.Version.ToString(4))
            jOnject.Add("TuiSku", strTuiSku)
            jOnject.Add("TuiVersion", strTuiVersion)
            jOnject.Add("Sn", strSN)
            jOnject.Add("Mac", Common.GetMacAddress)
            jOnject.Add("D", intD)
            'jOnject.Add("sq", True)
            jOnject.Add("IsJailbreak", blnJailbreaked)
            'jOnject.Add("Ia", "")
            jOnject.Add("UserBindiTunesAccount", IniSetting.GetDefaultUserAccount())
            jOnject.Add("SqVersion", DeviceIni.GetAuthVersion)

            Dim jWriter As New JsonWriter
            jOnject.Write(jWriter)

            Me.SetWebAppMarketStatus(webBrowser, "bridgeCallBack", strBridgeCallBack, jWriter.ToString)
        Catch ex As Exception
            Common.LogException(ex.ToString, "WebSiteHelper_GetClientInfoThread")
        End Try
    End Sub

    '闪退赔付PC给web开的请求服务器的接口
    Private Sub WebEncryptRequest(ByVal strEncryptRequest As String, ByVal strBridgeCallBack As String, ByVal webBrowser As tbWebBrowser)
        Try
            Dim objPara() As Object = New Object() {strEncryptRequest, strBridgeCallBack, webBrowser}
            Dim thr As New Thread(New ParameterizedThreadStart(AddressOf WebEncryptRequestThread))
            With thr
                .IsBackground = True
                .Start(objPara)
            End With

            'Dim intCount As Integer = 0
            'While True
            '    If thr.ThreadState = ThreadState.Stopped Then
            '        Exit While
            '    End If
            '    Utility.WaitSeconds(0.5)
            '    intCount += 1
            '    If intCount > 40 Then
            '        Exit While
            '    End If
            'End While
        Catch ex As Exception
            Common.LogException(ex.ToString, "WebSiteHelper_WebEncryptRequest")
        End Try
    End Sub

    Private Sub WebEncryptRequestThread(ByVal obj As Object)
        If obj Is Nothing Then
            Return
        End If
        Try
            Dim strValue As String = obj(0)
            Dim strBridgeCallBack = obj(1)
            Dim webBrowser As tbWebBrowser = obj(2)

            'Common.Log("WebEncryptRequestThread: RequestURL:" & strValue)
            'Common.Log("WebEncryptRequestThread: Start:" & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"))

            Dim strReturn As String = HtmlHelper.WebEncryptRequest(strValue)

            'Common.Log("WebEncryptRequestThread: End:" & DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"))
            'Common.Log("WebEncryptRequestThread: Result:" & strReturn)

            Me.SetWebAppMarketStatus(webBrowser, "bridgeCallBack", strBridgeCallBack, strReturn)
        Catch ex As Exception
            Common.LogException(ex.ToString, "WebSiteHelper_WebEncryptRequestThread")
        End Try
    End Sub

    '设置tabPage状态
    Private Sub SetTabPageStatus(ByVal tabPage As tbTabPanel, ByVal strFrom As String, ByVal strType As String)
        Select Case strFrom
            Case "home", "game", "applice", "topic", "chart"

                Dim page As tbTabPanelPage = Me.GetPageByPageName(TabPage, strFrom)

                If page IsNot Nothing AndAlso strType = "focus" AndAlso TabPage.SelectedPage IsNot page Then
                    TabPage.SelectedPage = page
                    'ElseIf page IsNot Nothing AndAlso strType = "unfocus" AndAlso tabPage.SelectedPage IsNot Nothing AndAlso tabPage.SelectedPage Is page Then
                    '    tabPage.SelectedPage = Nothing
                End If

            Case "goForward"
                If strType = "focus" Then
                    TabPage.tbBarWebButtonsNextEnable = True
                Else
                    TabPage.tbBarWebButtonsNextEnable = False
                End If
            Case "goBack"
                If strType = "focus" Then
                    TabPage.tbBarWebButtonsPreviousEnable = True
                Else
                    TabPage.tbBarWebButtonsPreviousEnable = False
                End If
            Case "iphone"
                TabPage.tbBarDeviceButtonStatus = tbBarDeviceButtonStatus.IPhone
            Case "ipad"
                TabPage.tbBarDeviceButtonStatus = tbBarDeviceButtonStatus.IPad
            Case "android"
                TabPage.tbBarDeviceButtonStatus = tbBarDeviceButtonStatus.Android

        End Select
    End Sub

    Private Function GetPageByPageName(ByVal tabPage As tbTabPanel, ByVal strFrom As String) As tbTabPanelPage
        Dim page As tbTabPanelPage = Nothing

        Try
            '' Modified by Utmost20141021
            For Each Item As tbTabPanelPage In tabPage.Pages
                'If Item.PageName.ToLower = strFrom.ToLower Then
                '    page = Item
                '    Exit For
                'End If
                If Item.PageName.Equals(strFrom, StringComparison.OrdinalIgnoreCase) Then
                    page = Item
                    Exit For
                End If

            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetPageByPageName of WebSiteHelper")
        End Try

        Return page
    End Function

    '获取所有软件
    Private Sub SetWebAppStatus_AllInstalled(ByVal device As iPhoneDevice, ByVal webBrowser As tbWebBrowser)
        Try
            If Me.mtdGetAllInstalled IsNot Nothing AndAlso Me.mtdGetAllInstalled.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Dim objPara() As Object = New Object() {device, webBrowser}
            Me.mtdGetAllInstalled = New Thread(New ParameterizedThreadStart(AddressOf SetWebAppStatus_AllInstalledInThread))
            With Me.mtdGetAllInstalled
                .IsBackground = True
                .Start(objPara)
            End With
        Catch
        End Try
    End Sub

    Private Sub SetWebAppStatus_AllInstalledInThread(ByVal objPara As Object)
        Try
            Dim device As iPhoneDevice = objPara(0)
            Dim webBrowser As tbWebBrowser = objPara(1)
            If device Is Nothing Then
                Return
            End If

            Dim dicApps As Dictionary(Of String, FileSharingPackageInfo) = device.InstalledApplications(ApplicationType.User)
            Dim lstApps As New List(Of PackageInfo)

            For Each Item As FileSharingPackageInfo In dicApps.Values
                lstApps.Add(Item)
            Next

            Dim strJson As String = Me.GetJsonForWebAppMarketStatus(lstApps)

            If webBrowser Is Nothing Then
                Me.SetWebAppMarketStatus(webBrowser, WebPluginStatus.AllInstalled, strJson)
            Else
                Me.SetWebAppMarketStatus(webBrowser, "InitBtnStatus", strJson, "")
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
    End Sub

    Private Function GetJsonForWebAppMarketStatus(ByVal lstPackages As List(Of PackageInfo)) As String
        Dim strJson As String = String.Empty

        If lstPackages Is Nothing OrElse lstPackages.Count = 0 Then
            Return strJson
        End If

        Dim dic As New JsonObject()
        Dim arrValues As New JsonArray

        For Each Item As PackageInfo In lstPackages
            Dim dicValue As New JsonObject()

            dicValue.Add("identifier", Item.Identifier)
            dicValue.Add("version", Item.Version)

            arrValues.Add(dicValue)
        Next

        dic.Add("list", arrValues)

        Dim writer As New JsonWriter()
        dic.Write(writer)
        strJson = writer.ToString()

        Return strJson
    End Function

#End Region

#Region "--- 软件下载 ---"

    Public Sub DownloadIPA(ByVal device As iPhoneDevice, ByVal url As String, _
                                                Optional ByVal strName As String = "", _
                                                Optional ByVal strVersion As String = "", _
                                                Optional ByVal strItemId As String = "", _
                                                Optional ByVal strIconUrl As String = "", _
                                                Optional ByVal strLoadID As String = "", _
                                                Optional ByVal isNeedToBackInfo As Boolean = False, _
                                                Optional ByVal isJailbreak As Boolean = False, _
                                                Optional ByVal strBTTypeText As String = "", _
                                                Optional ByVal strIdentify As String = "")       '下载BT专区的软件，才有这个字段
        Try
            Dim info As New MultiThreadDownloadItemInfo
            With info
                .IsSingleThread = False

                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .Source = TaskSource.Tongbu
                .LoadId = strLoadID
                .Url = url
                .ItemId = strItemId
                .IconUrl = strIconUrl
                .Version = strVersion
                If isNeedToBackInfo Then
                    .UrlId = strLoadID
                End If

                If strName.Length > 0 Then
                    If strVersion.Length > 0 Then
                        strName &= String.Format("_v{0}", strVersion)
                    End If

                    '在BT包的名字添加BT类型，主要是用户可以根据名字来区分。

                    '另外本地应用展示时，也要显示BT类型。

                    '例如：暗夜之光_v1.0.3_@(通关版).ipa
                    If strBTTypeText.Length > 0 Then
                        strName &= String.Format("_@({0})", strBTTypeText)
                    End If

                    If Not strName.EndsWith(".ipa", StringComparison.OrdinalIgnoreCase) Then
                        strName &= ".ipa"
                    End If
                End If
                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If .Url.ToLower().Contains("tbtui") Then
                    .IsUpdate = True
                End If

                If device IsNot Nothing AndAlso device.IsConnected Then
                    .Identifier = device.Identifier
                    .DeviceId = device.Identifier
                End If
                .Jailbreak = isJailbreak
                If Not String.IsNullOrEmpty(strIdentify) Then
                    .Identifier = strIdentify
                End If
            End With

            Me.mDownManage.NewTask(info)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadIPA")
        End Try
    End Sub

    Public Sub DownloadAPKEx(ByVal application As IApplication, ByVal device As AndroidDevice, ByVal strUrl As String)
        Try
            'strUrl = "http://pc/tongbu/app/download?actiontype=2&loadid=706903&url=http%3A%2F%2Fd.tongbu.com%2F%3Fsid%3DDy9fBav56bkn4%2FUHlncT7uK4ln31NzLf&appname=托尔：神域战士&ver=1.0.0&icon=http://im1.tongbu.com/appicon/app/873/73/735102873_E6BF2A8D_75.jpg&appleid=735102873&jailbreak=1&bttype=无限道具版"
            Dim strDownloadUrl As String = Utility.GetParamValueFromQuery("url", strUrl, "&", True)
            Dim strName As String = Utility.GetParamValueFromQuery("appname", strUrl, "&", True)
            Dim strVersion As String = Utility.GetParamValueFromQuery("ver", strUrl, "&", True)
            Dim strItemId As String = Utility.GetParamValueFromQuery("appleid", strUrl, "&", True)
            Dim strIconUrl As String = Utility.GetParamValueFromQuery("icon", strUrl, "&", True)
            Dim strJailbreak As String = Utility.GetParamValueFromQuery("jailbreak", strUrl, "&", True)
            Dim strBTTypeText As String = Utility.GetParamValueFromQuery("bttype", strUrl, "&", True)                '下载BT专区的软件，才有这个字段（表示软件的类型，例如无限道具版）
            Dim strAppID As String = Utility.GetParamValueFromQuery("packagename", strUrl, "&", True)
            Dim strAPKID As String = Utility.GetParamValueFromQuery("apkid", strUrl, "&", True)
            Dim appInfo As UpdatePackageInfo = New UpdatePackageInfo()

            Dim strLoadId As String = String.Empty
            Dim blnNeedToBackInfo As Boolean = False


            Dim arrKey() As Byte = Encoding.UTF8.GetBytes("&35%383@")
            Dim arrMac() As Byte = Encoding.UTF8.GetBytes(Common.GetMacAddress)
            Dim arrDes() As Byte = Common.EncryptDES(arrMac, arrKey, arrKey)
            Dim arrMD5() As Byte = Common.GetMd5Array(arrMac)
            Dim strSID As String = String.Empty
            Dim strK As String = String.Empty
            '在IE8下载会乱码，所在添加了urlencode
            If strName.Length > 0 Then
                strName = System.Web.HttpUtility.UrlDecode(strName)
            End If
            Me.DownloadAPK(application, strAppID, strIconUrl, strVersion, strName)

            'appInfo.Identifier = strAppID


            'strDownloadUrl = AndroidApp.GetUpdateAPKDownloadURLEx(appInfo, "z")

            'Dim info As New MultiThreadDownloadItemInfo
            'With info
            '    .IsSingleThread = False

            '    .SaveFolder = IniSetting.GetDownloadSoftFolderApk()
            '    .Type = ResourceType.APK
            '    .Class = ResourceClass.Software
            '    .Source = TaskSource.Tongbu
            '    .Url = strDownloadUrl
            '    .ItemId = strAppID
            '    .IconUrl = strIconUrl
            '    .Version = strVersion

            '    If strName.Length > 0 Then
            '        If strVersion.Length > 0 Then
            '            strName &= String.Format("_v{0}", strVersion)
            '        End If

            '        If strName.ToLower().EndsWith(".apk") = False Then
            '            strName &= ".apk"
            '        End If
            '    End If
            '    If strName.Length = 0 Then
            '        strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
            '    End If
            '    info.Name = System.Web.HttpUtility.UrlDecode(strName)
            'End With

            'Me.mDownManage.NewTask(info)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadApp")
        End Try
    End Sub

    Public Sub DownloadAPK(ByVal application As IApplication, ByVal strAppId As String, ByVal strIconUrl As String, ByVal strVer As String, ByVal strName As String)
        Dim strDownloadUrl As String = ""
        Dim appInfo As UpdatePackageInfo = New UpdatePackageInfo()
        appInfo.Identifier = strAppId

        strDownloadUrl = AndroidApp.GetUpdateAPKDownloadURLEx(appInfo, "z")

        If strDownloadUrl.Length = 0 Then

            '如果失败，则打log。输出请求的url。

            Common.LogException("此链接已失效DownloadAPK：" & vbCrLf & strDownloadUrl)

            Dim strMessage As String = application.Language.GetString("Site.Message.LinkDisable")
            If strName.Trim.Length > 0 Then
                strMessage = strName & vbCrLf & strMessage
            End If

            '此链接已失效，请选择其它下载地址。

            tbMessageBox.Show(application, strMessage, _
                            application.Language.GetString("Common.Info"), _
                            MessageBoxButtons.OK, _
                            MessageBoxIcon.Information)

            Return
        End If

        Dim info As New MultiThreadDownloadItemInfo
        With info
            .IsSingleThread = False

            .SaveFolder = IniSetting.GetDownloadSoftFolderApk()
            .Type = ResourceType.APK
            .Class = ResourceClass.Software
            .Source = TaskSource.Tongbu
            .Url = strDownloadUrl
            .ItemId = strAppId
            .IconUrl = strIconUrl
            .Version = strVer

            If strName.Length > 0 Then
                If strVer.Length > 0 Then
                    strName &= String.Format("_v{0}", strVer)
                End If

                If Not strName.EndsWith(".apk", StringComparison.OrdinalIgnoreCase) Then
                    strName &= ".apk"
                End If
            End If
            If strName.Length = 0 Then
                strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
            End If
            info.Name = System.Web.HttpUtility.UrlDecode(strName)
        End With

        Me.mDownManage.NewTask(info)

    End Sub

    Private Sub DownloadDeb(ByVal device As iPhoneDevice, ByVal webBrowser As WebBrowser, ByVal url As String)
        Dim strUrl As String = url.ToLower
        Dim info As New MultiThreadDownloadItemInfo
        Try
            '判断是否安装afc2
            If device.Jailbreaked = False AndAlso device.InstallCydia Then
                If tbMessageBox.Show(Me, LanguageInterface.Instance().GetString("WebSite.Message.InstallAFC2"), LanguageInterface.Instance().GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                    Common.OpenExplorer("http://news.tongbu.com/71765.html")
                End If
                Return
            End If

            Dim strName As String = Utility.GetParamValueFromQuery("name", strUrl)
            Dim strVersion As String = Utility.GetParamValueFromQuery("version", strUrl)
            Dim strUrlPara As String = Utility.GetParamValueFromQuery("url", strUrl)
            Dim strSid As String = Utility.GetParamValueFromQuery("sid", strUrlPara)
            Dim strJs As String = Common.EncryptDES(Me.CreateJsonString(strSid), "ac68!3#1", "ac68!3#1").Replace("+", "_@_")
            Dim strGetDownLoadPathUrl As String = WebUrl.PageJailbreakPlugins '"http://tbzsapi.tongbu.com/tbzsdata/tbzsjbPlugins.aspx"

            'If Me.mDevice IsNot Nothing AndAlso Not Me.mDevice.Jailbreaked Then
            '    Me.SetWebStatus(0, WebPluginStatus.Jail)
            '    Return
            'End If

            If Not Me.CheckDeviceMatch(device, strUrl) Then
                Me.SetWebStatus(webBrowser, strSid, WebPluginStatus.NoMatch)
                Return
            End If

            Dim arrKey() As Byte = Encoding.UTF8.GetBytes("&35%383@")
            Dim arrMac() As Byte = Encoding.UTF8.GetBytes(Common.GetMacAddress)
            Dim arrDes() As Byte = Common.EncryptDES(arrMac, arrKey, arrKey)
            Dim arrMD5() As Byte = Common.GetMd5Array(arrMac)

            strGetDownLoadPathUrl = strGetDownLoadPathUrl & String.Format("?v={0}", strJs) & String.Format("&src={0}", Common.ToHexString(arrDes)) & String.Format("&des={0}", Common.ToHexString(arrMD5))
            '如果是在同步推页面，获取LoadId
            Dim strContentURL As String = Utility.GetContentStringFromUrl(strGetDownLoadPathUrl, System.Text.Encoding.UTF8)
            Dim urlFinished As String = Common.DecryptDES(Me.GetJsonValue(strContentURL), "ac68!3#1", "ac68!3#1")
            If Not urlFinished.StartsWith("http://") Then
                urlFinished = "http://" & urlFinished
            End If
            With info
                .IsSingleThread = False
                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.DEB
                .Class = ResourceClass.Plugins
                .Source = TaskSource.Tongbu
                .LoadId = ""
                .Url = urlFinished
                .ItemId = strSid
                .IconUrl = ""
                .TaskID = strSid
                Dim strExt As String = Path.GetExtension(urlFinished) '取得后缀 有可能是deb .zip
                If strName.Length > 0 Then
                    If strVersion.Length > 0 Then
                        strName &= String.Format("_v{0}", strVersion)
                    End If
                    If strName.ToLower().EndsWith(strExt) = False Then
                        strName &= strExt
                    End If
                End If

                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(.Url)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If device IsNot Nothing AndAlso device.IsConnected Then
                    .Identifier = device.Identifier
                End If
            End With
            If Me.CheckDebFileExist(webBrowser, info) = False Then
                Me.mDownManage.NewTask(info)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Function CheckDeviceMatch(ByVal device As iPhoneDevice, ByVal strUrl As String) As Boolean
        Dim strMixVersion As String = Utility.GetParamValueFromQuery("minfirmware", strUrl).PadRight(3, "0")
        Dim strMaxVersion As String = Utility.GetParamValueFromQuery("maxfirmware", strUrl).PadRight(3, "0")
        Dim strDeviceType As String = Utility.GetParamValueFromQuery("deviceid", strUrl)
        Dim strDeviceVersion As String = device.ProductVersion.Replace(".", "").PadRight(3, "0")

        If device IsNot Nothing Then
            If strDeviceType <> "0" Then
                'If strDeviceType = "1" AndAlso device.ProductType.ToLower.StartsWith("ipad") Then
                '    Return False
                'End If

                'If strDeviceType = "2" AndAlso Not device.ProductType.ToLower.StartsWith("ipad") Then
                '    Return False
                'End If

                If strDeviceType = "1" AndAlso device.ProductType.StartsWith("ipad", StringComparison.OrdinalIgnoreCase) Then
                    Return False
                End If

                If strDeviceType = "2" AndAlso Not device.ProductType.StartsWith("ipad", StringComparison.OrdinalIgnoreCase) Then
                    Return False
                End If
            End If

            If Common.CompareVer(strDeviceVersion, strMixVersion.Replace(".", "")) < 0 OrElse Common.CompareVer(strMaxVersion.Replace(".", ""), strDeviceVersion) < 0 Then
                Return False
            End If
        End If
        Return True
    End Function

    '拼装json 向服务器请求下载地址
    Private Function CreateJsonString(ByVal strSid As String) As String
        Dim strReturn As String = ""
        Dim dicChat As JsonObject
        'Dim arrChats As New JsonArray
        dicChat = New JsonObject()
        dicChat.Add("mode", "1")
        dicChat.Add("sid", strSid)

        Dim writer As New JsonWriter()
        dicChat.Write(writer)
        strReturn = writer.ToString()
        Return strReturn
    End Function

    '取得服务器返回的json 的 下载地址，下载地址是加密的。

    Private Function GetJsonValue(ByVal strJs As String) As String
        Dim strReturn As String = ""
        Dim strCode As String = ""
        Dim jsObjects As JsonObject = iTong.CoreFoundation.JsonParser.ParseString(strJs)

        Try
            If jsObjects IsNot Nothing AndAlso jsObjects.Keys.Count > 0 Then
                If jsObjects.ContainsKey("code") Then
                    strCode = CType(jsObjects("code"), JsonString).Value
                End If

                If strCode = "1" AndAlso jsObjects.ContainsKey("url") Then
                    strReturn = CType(jsObjects("url"), JsonString).Value
                End If
            End If
        Catch ex As Exception
        End Try
        Return strReturn
    End Function

    Private Function CheckDebFileExist(ByVal webBrowser As WebBrowser, ByVal info As MultiThreadDownloadItemInfo) As Boolean
        Dim blnReturn As Boolean = False
        If File.Exists(info.DownloadPath) Then
            blnReturn = True
            If Me.mInstallHelper IsNot Nothing Then
                Dim pkginfo As New PackageInfo()
                pkginfo.PackagePath = info.DownloadPath
                pkginfo.StoreId = info.TaskID
                Me.mInstallHelper.InstallDeb(pkginfo)
                Me.SetWebStatus(webBrowser, info.TaskID, WebPluginStatus.Installing)
            Else
                Me.SetWebStatus(webBrowser, info.TaskID, WebPluginStatus.Downloaded)
            End If
        End If
        Return blnReturn
    End Function

    Public Sub SetWebStatus(ByVal webBrowser As Object, ByVal strId As String, ByVal status As WebPluginStatus)
        Dim strStatus As String = "none"

        Select Case status
            Case WebPluginStatus.Downloading
                strStatus = "Downloading"
            Case WebPluginStatus.Downloaded
                strStatus = "Downloaded"
            Case WebPluginStatus.DownloadFailure
                strStatus = "DownloadFailure"
            Case WebPluginStatus.Installed
                strStatus = "Installed"
            Case WebPluginStatus.InstallFailure
                strStatus = "InstallFailure"
            Case WebPluginStatus.Installing
                strStatus = "Installing"
            Case WebPluginStatus.NoMatch
                strStatus = "NoMatch"
            Case WebPluginStatus.Jail
                strStatus = "Jail"
            Case WebPluginStatus.DeviceConnected
                strStatus = "DeviceConnected"
            Case WebPluginStatus.DeviceDisconnect
                strStatus = "DeviceDisconnect"
        End Select

        Me.ShowChatOnWebPage(webBrowser, strId, strStatus)
    End Sub

    '设置deb的安装下载状态。
    Private Delegate Sub ShowChatOnWebPageHandler(ByVal objBrowser As Object, ByVal strID As String, ByVal strStatus As String)
    Public Sub ShowChatOnWebPage(ByVal objBrowser As Object, ByVal strID As String, ByVal strStatus As String)
        Try
            Dim webBrowser As WebBrowser = CType(objBrowser, WebBrowser)

            '' 使用WebKit
            'Dim webBrowser As WebKitBrowserEx = CType(objBrowser, WebKitBrowserEx)

            If webBrowser.InvokeRequired Then
                webBrowser.BeginInvoke(New ShowChatOnWebPageHandler(AddressOf ShowChatOnWebPage), webBrowser, strID, strStatus)
            Else
                If webBrowser.Document IsNot Nothing Then
                    webBrowser.Document.InvokeScript("TB_API", New Object() {"installControl_v1", strID, strStatus})

                    '' 使用WebKit
                    'webBrowser.Document.InvokeScriptMethod("TB_API", New Object() {"installControl_v1", strID, strStatus})

                    'Application.DoEvents()
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "PluginJsError")
        End Try
    End Sub

    Private Delegate Sub WebBrowserHandler(ByVal objBrowser As Object, ByVal blnClose As Boolean)
    Public Sub SetTipCloseForApp(ByVal objBrowser As Object, ByVal blnClose As Boolean)
        Dim webBrowser As WebBrowser = CType(objBrowser, WebBrowser)

        '' 使用WebKit
        'Dim webBrowser As WebKitBrowserEx = CType(objBrowser, WebKitBrowserEx)

        If webBrowser.InvokeRequired Then
            webBrowser.BeginInvoke(New WebBrowserHandler(AddressOf SetTipCloseForApp), webBrowser, blnClose)
        Else
            If webBrowser.Document IsNot Nothing Then

                Dim strValue As String = "openTip"
                If blnClose Then
                    strValue = "closeTip"
                End If

                webBrowser.Document.InvokeScript(strValue, New Object() {})

                '' 使用WebKit
                'webBrowser.Document.InvokeScriptMethod("TB_API", New Object() {"installControl_v1", strID, strStatus})

                'Application.DoEvents()
            End If
        End If
    End Sub

#End Region

#Region "--- 专题应用下载 ---"

    Private Sub DownloadTopicApp(ByVal application As IApplication, ByVal device As iPhoneDevice, ByVal url As String)
        'Try
        '    If Me.mThreadTopicDownload IsNot Nothing AndAlso Me.mThreadTopicDownload.ThreadState <> ThreadState.Stopped Then
        '        Me.mThreadTopicDownload.Abort()
        '    End If
        'Catch
        'End Try
        'Dim objPara() As Object = New Object() {application, device, url}
        'Me.mThreadTopicDownload = New Thread(New ParameterizedThreadStart(AddressOf DownloadTopicAppInThread))
        'With Me.mThreadTopicDownload
        '    .IsBackground = True
        '    .Start(objPara)
        'End With
        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf DownloadTopicAppInThread), New Object() {application, device, url})
    End Sub

    Private Sub DownloadTopicAppInThread(ByVal objUrl As Object)
        Try
            Dim application As IApplication = CType(objUrl(0), IApplication)
            Dim device As iPhoneDevice = CType(objUrl(2), iPhoneDevice)
            Dim strUrl As String = CType(objUrl(3), String)

            Dim strAppleIds As String = Utility.GetParamValueFromQuery("appleids", strUrl)

            If String.IsNullOrEmpty(strAppleIds) Then
                Return
            End If

            Dim arrId As String() = strAppleIds.Split(",")
            Dim strContent As String = String.Empty
            Dim strUrlForId As String = WebUrl.PageIpaDownInfo '"http://v2.tongbu.com/app/api/apidown?appleid={0}"

            For Each Item As String In arrId
                If String.IsNullOrEmpty(Item) Then
                    Exit For
                End If

                strContent = Utility.GetContentStringFromUrl(String.Format(strUrlForId, Item), System.Text.Encoding.UTF8)

                If Not String.IsNullOrEmpty(Item) Then
                    Me.DownloadApp(application, device, strContent)
                End If

            Next

        Catch
        End Try
    End Sub

#End Region

#Region "--- Wallpaper ---"

    Private Sub DownloadWallPaper(ByVal strUrl As String, Optional ByVal strAndroid As String = "")
        If String.IsNullOrEmpty(strUrl) Then
            Return
        End If

        Dim itemInfo As New MultiThreadDownloadItemInfo()

        itemInfo.Url = Utility.GetParamValueFromQuery("url", strUrl)
        itemInfo.Name = Me.CreateWallpaperName(itemInfo.Url)

        Dim strDeviceType As String = "" 'Me.GetDeviceType(Utility.GetParamValueFromQuery("type", strUrl))

        If strAndroid.Equals("Android", StringComparison.OrdinalIgnoreCase) Then
            strDeviceType = strAndroid
        Else
            strDeviceType = Me.GetDeviceType(Utility.GetParamValueFromQuery("type", strUrl))
        End If

        Dim strFolder As String = IniSetting.GetDownloadWallpaperFolder & strDeviceType & "\"
        Folder.CheckFolder(strFolder)

        itemInfo.SaveFolder = strFolder

        Dim itemExtension As String = Path.GetExtension(itemInfo.Name)
        If itemExtension.Equals(".PNG", StringComparison.OrdinalIgnoreCase) Then
            itemInfo.Type = ResourceType.PNG
        Else
            itemInfo.Type = ResourceType.JPG
        End If

        itemInfo.Category = strDeviceType
        itemInfo.Class = ResourceClass.Wallpaper
        If strUrl.Contains("tag=") Then
            itemInfo.Description = Utility.GetParamValueFromQuery("tag", strUrl)
        End If

        Me.mDownManage.NewTask(itemInfo)
    End Sub

    Private Function GetDeviceType(ByVal intType As Integer) As String
        Dim strDeviceType As String = ""

        '4表示iPhone4/S  5表示iPhone5 2表示iPad 3表示NewPad
        If intType = 2 OrElse intType = 3 Then
            strDeviceType = "iPad"
        Else
            strDeviceType = "iPhone"
        End If

        Return strDeviceType
    End Function

    Private Function CreateWallpaperName(ByVal strUrl As String) As String
        Dim strName As String = String.Empty
        Dim strExt As String = Path.GetExtension(strUrl) & ""

        If strExt.Length = 0 OrElse Not strExt.Equals(".PNG", StringComparison.OrdinalIgnoreCase) Then  ' strExt.ToUpper() <> ".PNG"
            strExt = ".JPG"
        End If

        ' strName = Convert.ToString(Now.Ticks, 16) & strExt
        strName = Now.ToString("yyyyMMdd_HHmmss") & strExt.ToUpper()

        Return strName
    End Function

#End Region

#Region "--- Ringtone ---"

    Private Sub DownloadRingtone(ByVal strURL As String)

        Dim dictMusicList As New Dictionary(Of String, String)
        Dim strUrlHeader As String = "tongbu://ring/download?"

        Dim index As Integer = strURL.IndexOf(strUrlHeader)
        Dim strMusic() As String = strURL.Substring(index + strUrlHeader.Length).Split(New String() {"@@"}, StringSplitOptions.RemoveEmptyEntries)
        Dim strIdentify As String = ""       '当前音乐要导入到哪个设备的标识

        For Each strMusicItem As String In strMusic
            Dim strTemp() As String = strMusicItem.Split(New Char() {"&"}, StringSplitOptions.RemoveEmptyEntries)
            Dim urlText As String = ""
            Dim fName As String = ""

            urlText = strTemp(0).Replace("url=", "")
            fName = Utility.GetParamValueFromQuery("filename", strTemp(1))
            If fName.Length > 0 Then
                fName = System.Web.HttpUtility.UrlDecode(fName)
            End If
            If strTemp.Length >= 3 Then
                strIdentify = Utility.GetParamValueFromQuery("Identify", strTemp(2))
            End If

            dictMusicList.Add(urlText, fName)
        Next

        For Each Item As String In dictMusicList.Keys
            Dim strFileName As String = dictMusicList(Item) & ".mp3"

            Dim fMp3 As MultiThreadDownloadItemInfo = MP3Download.Download(Item, strFileName)
            If fMp3 IsNot Nothing Then
                fMp3.Category = "RingTone"
                fMp3.Class = ResourceClass.Ringtone
                fMp3.SaveFolder = IniSetting.GetDownloadRingtoneFolder 'Folder.RingtoneFolder
                fMp3.Identifier = strIdentify
                If Not String.IsNullOrEmpty(strIdentify) Then
                    '铃声如果有接入设备就不提示重新下载任务存在的message直接导致到设备里面
                    fMp3.IsNeedTryDown = True
                End If

                Me.mDownManage.NewTask(fMp3)
            End If
        Next

    End Sub

#End Region

#Region "--- Music ---"

    Private Sub DownloadMusic(ByVal url As String, ByVal originalUrl As String)
        Dim strFrom As String = Utility.GetParamValueFromQuery("from", url)
        'Dim strTitle As String = Utility.GetParamValueFromQuery("name", url)
        Dim strTitle As String = Me.GetParamValueFromQueryForMusic("name", url, False, False)
        Dim strArtist As String = Utility.GetParamValueFromQuery("singername", url)
        Dim strAlbum As String = Utility.GetParamValueFromQuery("albumname", url)
        Dim strUrl As String = Utility.GetParamValueFromQuery("source", originalUrl, "&", False, True)

        Me.mDownManage.NewTask(MP3Download.Download(strUrl, strTitle, strFrom))
    End Sub

    Private Function GetParamValueFromQueryForMusic(ByVal key As String, _
                                                 ByVal url As String, _
                                                 ByVal isDecode As Boolean, _
                                                 ByVal isToEnd As Boolean) As String
        Dim strValue As String = ""
        Dim strSeparator As String = "&"

        If key.Length > 0 Then
            Dim indexStart As Integer = -1

            While True
                indexStart = url.IndexOf(key & "=", indexStart + 1)

                Dim strPrevious As String = ""
                If indexStart > 0 Then
                    strPrevious = url.Substring(indexStart - 1, 1)
                End If

                If String.Compare(strPrevious, "?", True) = 0 OrElse _
                   String.Compare(strPrevious, strSeparator, True) = 0 Then
                    Exit While
                End If
            End While

            Dim indexEnd As Integer = -1

            If indexStart > -1 Then
                '如果有多个参数的，参数以&分隔
                indexEnd = url.IndexOf(strSeparator, indexStart)

                If indexEnd = -1 OrElse isToEnd Then
                    '如果只有一个参数则读到url的长度
                    indexEnd = url.Length
                End If

                If indexEnd > -1 AndAlso indexEnd > indexStart Then
                    strValue = url.Substring(indexStart + key.Length + 1, indexEnd - indexStart - key.Length - 1)
                    If isDecode = True Then
                        strValue = System.Web.HttpUtility.UrlDecode(strValue)
                    End If
                End If
            End If
        End If

        Return strValue
    End Function

#End Region

    'Private Sub SetfrmAppSelectNode(ByVal strName As String, ByVal device As iPhoneDevice)
    '    Try
    '        For Each frm As Form In Application.OpenForms
    '            If TypeOf frm Is frmApp AndAlso CType(frm, frmApp).mDevice Is device AndAlso frm.Name = "frmApp" Then
    '                Dim app As frmApp = CType(frm, frmApp)
    '                Dim node As tbTreeNode = app.tvwApp.Nodes(strName)
    '                If node Is Nothing Then
    '                    node = app.tvwApp.Nodes("web" & strName)
    '                End If
    '                If node IsNot Nothing Then
    '                    app.tvwApp.SelectedNode = node
    '                End If
    '                Exit For
    '            End If
    '        Next
    '    Catch ex As Exception
    '    End Try
    'End Sub

End Class

Public Class DownloadWebKitHtml

    Private localPath As String = String.Empty
    Private url As String = String.Empty
    Private htmlFile As String = String.Empty
    Private folderName As String = String.Empty

    Public Delegate Sub DownloadHtmlFinshedHandle(ByVal isFinishLoad As Boolean)
    Public Event DownloadFinish As DownloadHtmlFinshedHandle

    Public Sub New(ByVal url As String, ByVal localPath As String, ByVal htmlFile As String, ByVal folderName As String)
        Me.url = url
        Me.htmlFile = htmlFile
        Me.folderName = folderName
        Me.localPath = localPath
    End Sub

    Public Sub BeginLoad()
        Dim thread As Thread = New Thread(AddressOf DownloadPage)
        thread.IsBackground = True
        thread.Start()
    End Sub

    Private Sub DownloadPage()
        Try
            If Not Common.NetworkIsAvailable() Then
                'If Not DownloadFinish Is Nothing Then
                RaiseEvent DownloadFinish(False)
                'End If
                'return false;
            End If
        Catch ex As Exception
        End Try

        Dim response As HttpWebResponse = Nothing
        Try
            Dim strTemp As String = String.Empty
            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
            request.Timeout = 3000
            request.UserAgent = "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 2.0.40607; .NET CLR 1.1.4322)"
            response = CType(request.GetResponse(), HttpWebResponse)
            Dim encode As System.Text.Encoding

            Using stream As Stream = response.GetResponseStream()
                If Not String.IsNullOrEmpty(response.CharacterSet) Then  'response.CharacterSet <> String.Empty
                    encode = Encoding.GetEncoding(response.CharacterSet.ToLower())
                Else
                    encode = Encoding.Default
                End If

                If Not encode Is Encoding.UTF8 Then
                    encode = Encoding.Default
                End If

                Dim sr As StreamReader = New StreamReader(stream, encode)
                strTemp = sr.ReadToEnd()
                sr.Close()
            End Using

            If Not String.IsNullOrEmpty(strTemp) Then  'strTemp <> String.Empty
                strTemp = Me.loadJsCssImages(strTemp)
                If Not Directory.Exists(localPath) Then
                    Directory.CreateDirectory(localPath)
                End If

                Using streamWrite As StreamWriter = New StreamWriter(localPath & htmlFile, False, encode)
                    streamWrite.Write(strTemp)
                    streamWrite.Flush()
                    'If Not DownloadFinish Is Nothing Then
                    RaiseEvent DownloadFinish(True)
                    'End If
                    Return
                End Using

            Else
                'If Not DownloadFinish Is Nothing Then
                RaiseEvent DownloadFinish(False)
                'End If
                Return
            End If

        Catch
            'If Not DownloadFinish Is Nothing Then
            RaiseEvent DownloadFinish(False)
            'End If
            Return
        Finally
            If Not response Is Nothing Then
                response.Close()
            End If
        End Try
    End Sub

#Region "下载Js Css Images"

    Private Function loadJsCssImages(ByVal content As String) As String
        Dim dirInfo As DirectoryInfo = New DirectoryInfo(localPath + folderName + "\")
        If Not dirInfo.Exists Then
            dirInfo.Create()
        End If

        Dim regex As System.Text.RegularExpressions.Regex = New Regex("[a-zA-z|"")+://[^\s)]*", RegexOptions.IgnoreCase Or RegexOptions.Multiline)
        Dim matchs As RegularExpressions.MatchCollection = regex.Matches(content)
        Dim m As RegularExpressions.Match
        For Each m In matchs
            Dim srcOrginString As String = m.ToString()                                     '可能头尾带引号的string
            Dim srcString As String = srcOrginString.Trim(New Char() {" ", """"}).Trim()           'url地址 As )
            If srcString.EndsWith(".js", StringComparison.InvariantCultureIgnoreCase) OrElse _
                srcString.EndsWith(".css", StringComparison.InvariantCultureIgnoreCase) OrElse _
                srcString.EndsWith(".jpg", StringComparison.InvariantCultureIgnoreCase) OrElse _
                srcString.EndsWith(".png", StringComparison.InvariantCultureIgnoreCase) OrElse _
                srcString.EndsWith(".gif", StringComparison.InvariantCultureIgnoreCase) Then

                Try
                    Dim orginName As String = srcString.Substring(srcString.LastIndexOf("/") + 1)
                    Dim fileName As String = String.Empty
                    'Dim loadHeper As DownloadHelper = New DownloadHelper()

                    If Utility.DownLoadFile(srcString, localPath & folderName & "\" & orginName) Then
                        content = content.Replace(srcString, folderName + "/" + orginName)
                    End If
                Catch ex As Exception
                End Try

            End If
        Next
        Return content

    End Function

#End Region

End Class

Public Class ShowWeiboArgs
    Inherits EventArgs
    Private mType As ClientType = ClientType.Tecent
    Private mImg As Image = Nothing
    Private mContact As String = String.Empty
    Public Property Type() As ClientType
        Get
            Return Me.mType
        End Get
        Set(value As ClientType)
            Me.mType = value
        End Set
    End Property

    Public Property Img() As Image
        Get
            Return Me.mImg
        End Get
        Set(value As Image)
            Me.mImg = value
        End Set
    End Property

    Public Property Contact() As String
        Get
            Return Me.mContact
        End Get
        Set(value As String)
            Me.mContact = value
        End Set
    End Property
End Class

Public Class GetDeviceForWebArgs
    Inherits EventArgs
    Private mDevice As IDevice = Nothing
    Public Property Device() As IDevice
        Get
            Return Me.mDevice
        End Get
        Set(value As IDevice)
            Me.mDevice = value
        End Set
    End Property
End Class


