﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSite
    Inherits frmDeviceBase

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSite))
        Me.tvwDevice = New iTong.Components.tbTreeView()
        Me.pnlContainer = New System.Windows.Forms.Panel()
        Me.pnlGuide = New iTong.Components.tbPanel()
        Me.PictureBoxMedian = New System.Windows.Forms.PictureBox()
        Me.lblJailbreakBackup = New System.Windows.Forms.Label()
        Me.lblFlashBackup = New System.Windows.Forms.Label()
        Me.lblJailbreakDepict = New System.Windows.Forms.Label()
        Me.lblFlashDepict = New System.Windows.Forms.Label()
        Me.btnInJailbreak = New iTong.Components.tbButton()
        Me.btnInFlash = New iTong.Components.tbButton()
        Me.PictureBoxJailbreak = New System.Windows.Forms.PictureBox()
        Me.PictureBoxFlash = New System.Windows.Forms.PictureBox()
        Me.pnlFirmware = New iTong.Components.tbPanel()
        Me.pnlFlash = New iTong.Components.tbPanel()
        Me.tpnlJailbreakPlugin = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlState = New iTong.Components.tbPanel()
        Me.pbarShowProgress = New iTong.Components.tbControlBar()
        Me.btnAbrot = New iTong.Components.tbButton()
        Me.lblState = New System.Windows.Forms.Label()
        Me.pnlContainer.SuspendLayout()
        Me.pnlGuide.SuspendLayout()
        CType(Me.PictureBoxMedian, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBoxJailbreak, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBoxFlash, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tpnlJailbreakPlugin.SuspendLayout()
        Me.pnlState.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'tvwDevice
        '
        Me.tvwDevice.AutoScroll = True
        Me.tvwDevice.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tvwDevice.Dock = System.Windows.Forms.DockStyle.Left
        Me.tvwDevice.ImageList = Nothing
        Me.tvwDevice.Indent = 13
        Me.tvwDevice.ItemHeight = 28
        Me.tvwDevice.Location = New System.Drawing.Point(0, 0)
        Me.tvwDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.tvwDevice.Name = "tvwDevice"
        Me.tvwDevice.Padding = New System.Windows.Forms.Padding(0, 0, 0, 10)
        Me.tvwDevice.PathSeparator = "\"
        Me.tvwDevice.SelectedNode = Nothing
        Me.tvwDevice.Size = New System.Drawing.Size(195, 600)
        Me.tvwDevice.TabIndex = 0
        Me.tvwDevice.TabStop = True
        Me.tvwDevice.tbBackgroundImage = Nothing
        Me.tvwDevice.tbNodeCountSelectedColor = System.Drawing.Color.White
        Me.tvwDevice.tbNodeDiff = 20
        Me.tvwDevice.tbNodeFont = New System.Drawing.Font("宋体", 9.0!)
        Me.tvwDevice.tbNodeForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.tvwDevice.tbNodeLabelFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold)
        Me.tvwDevice.tbNodeLabelForeColor = System.Drawing.Color.Black
        Me.tvwDevice.tbNodePadding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.tvwDevice.tbNodeShadowColor = System.Drawing.Color.Transparent
        Me.tvwDevice.tbSplit = "1,27,3,3"
        Me.tvwDevice.WrapContents = False
        '
        'pnlContainer
        '
        Me.pnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainer.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlContainer.Controls.Add(Me.pnlGuide)
        Me.pnlContainer.Controls.Add(Me.pnlFirmware)
        Me.pnlContainer.Controls.Add(Me.pnlFlash)
        Me.pnlContainer.Controls.Add(Me.tpnlJailbreakPlugin)
        Me.pnlContainer.Location = New System.Drawing.Point(196, 0)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(603, 600)
        Me.pnlContainer.TabIndex = 1
        '
        'pnlGuide
        '
        Me.pnlGuide.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlGuide.Controls.Add(Me.PictureBoxMedian)
        Me.pnlGuide.Controls.Add(Me.lblJailbreakBackup)
        Me.pnlGuide.Controls.Add(Me.lblFlashBackup)
        Me.pnlGuide.Controls.Add(Me.lblJailbreakDepict)
        Me.pnlGuide.Controls.Add(Me.lblFlashDepict)
        Me.pnlGuide.Controls.Add(Me.btnInJailbreak)
        Me.pnlGuide.Controls.Add(Me.btnInFlash)
        Me.pnlGuide.Controls.Add(Me.PictureBoxJailbreak)
        Me.pnlGuide.Controls.Add(Me.PictureBoxFlash)
        Me.pnlGuide.Location = New System.Drawing.Point(97, 292)
        Me.pnlGuide.Name = "pnlGuide"
        Me.pnlGuide.Size = New System.Drawing.Size(328, 166)
        Me.pnlGuide.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlGuide.TabIndex = 20
        Me.pnlGuide.tbBackgroundImage = Nothing
        Me.pnlGuide.tbShowWatermark = False
        Me.pnlGuide.tbSplit = "0,0,0,0"
        Me.pnlGuide.tbWatermark = Nothing
        Me.pnlGuide.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlGuide.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'PictureBoxMedian
        '
        Me.PictureBoxMedian.BackColor = System.Drawing.Color.Transparent
        Me.PictureBoxMedian.Location = New System.Drawing.Point(149, 18)
        Me.PictureBoxMedian.Name = "PictureBoxMedian"
        Me.PictureBoxMedian.Size = New System.Drawing.Size(20, 50)
        Me.PictureBoxMedian.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBoxMedian.TabIndex = 28
        Me.PictureBoxMedian.TabStop = False
        '
        'lblJailbreakBackup
        '
        Me.lblJailbreakBackup.BackColor = System.Drawing.Color.Transparent
        Me.lblJailbreakBackup.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblJailbreakBackup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblJailbreakBackup.ForeColor = System.Drawing.Color.FromArgb(CType(CType(218, Byte), Integer), CType(CType(50, Byte), Integer), CType(CType(58, Byte), Integer))
        Me.lblJailbreakBackup.Location = New System.Drawing.Point(249, 78)
        Me.lblJailbreakBackup.Name = "lblJailbreakBackup"
        Me.lblJailbreakBackup.Size = New System.Drawing.Size(41, 12)
        Me.lblJailbreakBackup.TabIndex = 27
        Me.lblJailbreakBackup.Text = "Label1"
        '
        'lblFlashBackup
        '
        Me.lblFlashBackup.BackColor = System.Drawing.Color.Transparent
        Me.lblFlashBackup.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblFlashBackup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFlashBackup.ForeColor = System.Drawing.Color.FromArgb(CType(CType(218, Byte), Integer), CType(CType(50, Byte), Integer), CType(CType(58, Byte), Integer))
        Me.lblFlashBackup.Location = New System.Drawing.Point(26, 88)
        Me.lblFlashBackup.Name = "lblFlashBackup"
        Me.lblFlashBackup.Size = New System.Drawing.Size(41, 12)
        Me.lblFlashBackup.TabIndex = 27
        Me.lblFlashBackup.Text = "Label1"
        '
        'lblJailbreakDepict
        '
        Me.lblJailbreakDepict.BackColor = System.Drawing.Color.Transparent
        Me.lblJailbreakDepict.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.lblJailbreakDepict.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblJailbreakDepict.Location = New System.Drawing.Point(249, 56)
        Me.lblJailbreakDepict.Name = "lblJailbreakDepict"
        Me.lblJailbreakDepict.Size = New System.Drawing.Size(41, 12)
        Me.lblJailbreakDepict.TabIndex = 27
        Me.lblJailbreakDepict.Text = "Label1"
        '
        'lblFlashDepict
        '
        Me.lblFlashDepict.BackColor = System.Drawing.Color.Transparent
        Me.lblFlashDepict.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.lblFlashDepict.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFlashDepict.Location = New System.Drawing.Point(26, 66)
        Me.lblFlashDepict.Name = "lblFlashDepict"
        Me.lblFlashDepict.Size = New System.Drawing.Size(41, 12)
        Me.lblFlashDepict.TabIndex = 27
        Me.lblFlashDepict.Text = "Label1"
        '
        'btnInJailbreak
        '
        Me.btnInJailbreak.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnInJailbreak.BackColor = System.Drawing.Color.Transparent
        Me.btnInJailbreak.BindingForm = Nothing
        Me.btnInJailbreak.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnInJailbreak.Location = New System.Drawing.Point(189, 123)
        Me.btnInJailbreak.Name = "btnInJailbreak"
        Me.btnInJailbreak.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnInJailbreak.Selectable = True
        Me.btnInJailbreak.Size = New System.Drawing.Size(135, 40)
        Me.btnInJailbreak.TabIndex = 26
        Me.btnInJailbreak.tbAdriftIconWhenHover = False
        Me.btnInJailbreak.tbAutoSize = False
        Me.btnInJailbreak.tbAutoSizeEx = False
        Me.btnInJailbreak.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnInJailbreak.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInJailbreak.tbBadgeNumber = 0
        Me.btnInJailbreak.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInJailbreak.tbEndEllipsis = False
        Me.btnInJailbreak.tbIconHoldPlace = True
        Me.btnInJailbreak.tbIconImage = Nothing
        Me.btnInJailbreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInJailbreak.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInJailbreak.tbIconMore = False
        Me.btnInJailbreak.tbIconMouseDown = Nothing
        Me.btnInJailbreak.tbIconMouseHover = Nothing
        Me.btnInJailbreak.tbIconMouseLeave = Nothing
        Me.btnInJailbreak.tbIconPlaceText = 2
        Me.btnInJailbreak.tbIconReadOnly = Nothing
        Me.btnInJailbreak.tbImageMouseDown = Nothing
        Me.btnInJailbreak.tbImageMouseHover = Nothing
        Me.btnInJailbreak.tbImageMouseLeave = Nothing
        Me.btnInJailbreak.tbProgressValue = 50
        Me.btnInJailbreak.tbReadOnly = False
        Me.btnInJailbreak.tbReadOnlyText = False
        Me.btnInJailbreak.tbShadow = False
        Me.btnInJailbreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInJailbreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInJailbreak.tbShowDot = False
        Me.btnInJailbreak.tbShowMoreIconImg = CType(resources.GetObject("btnInJailbreak.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInJailbreak.tbShowNew = False
        Me.btnInJailbreak.tbShowProgress = False
        Me.btnInJailbreak.tbShowTip = True
        Me.btnInJailbreak.tbShowToolTipOnButton = False
        Me.btnInJailbreak.tbSplit = "13,11,13,11"
        Me.btnInJailbreak.tbText = "进入越狱"
        Me.btnInJailbreak.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInJailbreak.tbTextColor = System.Drawing.Color.White
        Me.btnInJailbreak.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInJailbreak.tbTextColorDown = System.Drawing.Color.White
        Me.btnInJailbreak.tbTextColorHover = System.Drawing.Color.White
        Me.btnInJailbreak.tbTextMouseDownPlace = 0
        Me.btnInJailbreak.tbToolTip = ""
        Me.btnInJailbreak.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInJailbreak.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInJailbreak.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInJailbreak.VisibleEx = True
        '
        'btnInFlash
        '
        Me.btnInFlash.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnInFlash.BackColor = System.Drawing.Color.Transparent
        Me.btnInFlash.BindingForm = Nothing
        Me.btnInFlash.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnInFlash.Location = New System.Drawing.Point(2, 123)
        Me.btnInFlash.Name = "btnInFlash"
        Me.btnInFlash.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnInFlash.Selectable = True
        Me.btnInFlash.Size = New System.Drawing.Size(135, 40)
        Me.btnInFlash.TabIndex = 25
        Me.btnInFlash.tbAdriftIconWhenHover = False
        Me.btnInFlash.tbAutoSize = False
        Me.btnInFlash.tbAutoSizeEx = False
        Me.btnInFlash.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnInFlash.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInFlash.tbBadgeNumber = 0
        Me.btnInFlash.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInFlash.tbEndEllipsis = False
        Me.btnInFlash.tbIconHoldPlace = True
        Me.btnInFlash.tbIconImage = Nothing
        Me.btnInFlash.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInFlash.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInFlash.tbIconMore = False
        Me.btnInFlash.tbIconMouseDown = Nothing
        Me.btnInFlash.tbIconMouseHover = Nothing
        Me.btnInFlash.tbIconMouseLeave = Nothing
        Me.btnInFlash.tbIconPlaceText = 2
        Me.btnInFlash.tbIconReadOnly = Nothing
        Me.btnInFlash.tbImageMouseDown = Nothing
        Me.btnInFlash.tbImageMouseHover = Nothing
        Me.btnInFlash.tbImageMouseLeave = Nothing
        Me.btnInFlash.tbProgressValue = 50
        Me.btnInFlash.tbReadOnly = False
        Me.btnInFlash.tbReadOnlyText = False
        Me.btnInFlash.tbShadow = False
        Me.btnInFlash.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInFlash.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInFlash.tbShowDot = False
        Me.btnInFlash.tbShowMoreIconImg = CType(resources.GetObject("btnInFlash.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInFlash.tbShowNew = False
        Me.btnInFlash.tbShowProgress = False
        Me.btnInFlash.tbShowTip = True
        Me.btnInFlash.tbShowToolTipOnButton = False
        Me.btnInFlash.tbSplit = "13,11,13,11"
        Me.btnInFlash.tbText = "进入刷机"
        Me.btnInFlash.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInFlash.tbTextColor = System.Drawing.Color.White
        Me.btnInFlash.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInFlash.tbTextColorDown = System.Drawing.Color.White
        Me.btnInFlash.tbTextColorHover = System.Drawing.Color.White
        Me.btnInFlash.tbTextMouseDownPlace = 0
        Me.btnInFlash.tbToolTip = ""
        Me.btnInFlash.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInFlash.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInFlash.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInFlash.VisibleEx = True
        '
        'PictureBoxJailbreak
        '
        Me.PictureBoxJailbreak.BackColor = System.Drawing.Color.Transparent
        Me.PictureBoxJailbreak.Location = New System.Drawing.Point(225, 3)
        Me.PictureBoxJailbreak.Name = "PictureBoxJailbreak"
        Me.PictureBoxJailbreak.Size = New System.Drawing.Size(100, 50)
        Me.PictureBoxJailbreak.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBoxJailbreak.TabIndex = 0
        Me.PictureBoxJailbreak.TabStop = False
        '
        'PictureBoxFlash
        '
        Me.PictureBoxFlash.BackColor = System.Drawing.Color.Transparent
        Me.PictureBoxFlash.Location = New System.Drawing.Point(12, 3)
        Me.PictureBoxFlash.Name = "PictureBoxFlash"
        Me.PictureBoxFlash.Size = New System.Drawing.Size(100, 50)
        Me.PictureBoxFlash.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBoxFlash.TabIndex = 0
        Me.PictureBoxFlash.TabStop = False
        '
        'pnlFirmware
        '
        Me.pnlFirmware.Location = New System.Drawing.Point(326, 130)
        Me.pnlFirmware.Name = "pnlFirmware"
        Me.pnlFirmware.Size = New System.Drawing.Size(200, 100)
        Me.pnlFirmware.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFirmware.TabIndex = 18
        Me.pnlFirmware.tbBackgroundImage = Nothing
        Me.pnlFirmware.tbShowWatermark = False
        Me.pnlFirmware.tbSplit = "0,0,0,0"
        Me.pnlFirmware.tbWatermark = Nothing
        Me.pnlFirmware.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFirmware.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlFlash
        '
        Me.pnlFlash.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFlash.Location = New System.Drawing.Point(326, 24)
        Me.pnlFlash.Name = "pnlFlash"
        Me.pnlFlash.Size = New System.Drawing.Size(200, 100)
        Me.pnlFlash.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFlash.TabIndex = 18
        Me.pnlFlash.tbBackgroundImage = Nothing
        Me.pnlFlash.tbShowWatermark = False
        Me.pnlFlash.tbSplit = "0,0,0,0"
        Me.pnlFlash.tbWatermark = Nothing
        Me.pnlFlash.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFlash.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'tpnlJailbreakPlugin
        '
        Me.tpnlJailbreakPlugin.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tpnlJailbreakPlugin.ColumnCount = 1
        Me.tpnlJailbreakPlugin.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlJailbreakPlugin.Controls.Add(Me.pnlState, 0, 1)
        Me.tpnlJailbreakPlugin.Location = New System.Drawing.Point(12, 13)
        Me.tpnlJailbreakPlugin.Name = "tpnlJailbreakPlugin"
        Me.tpnlJailbreakPlugin.RowCount = 2
        Me.tpnlJailbreakPlugin.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlJailbreakPlugin.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.tpnlJailbreakPlugin.Size = New System.Drawing.Size(259, 56)
        Me.tpnlJailbreakPlugin.TabIndex = 17
        Me.tpnlJailbreakPlugin.Visible = False
        '
        'pnlState
        '
        Me.pnlState.BackColor = System.Drawing.Color.FromArgb(CType(CType(205, Byte), Integer), CType(CType(215, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.pnlState.Controls.Add(Me.pbarShowProgress)
        Me.pnlState.Controls.Add(Me.btnAbrot)
        Me.pnlState.Controls.Add(Me.lblState)
        Me.pnlState.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlState.Location = New System.Drawing.Point(0, 26)
        Me.pnlState.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlState.Name = "pnlState"
        Me.pnlState.Size = New System.Drawing.Size(259, 30)
        Me.pnlState.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlState.TabIndex = 10
        Me.pnlState.tbBackgroundImage = Nothing
        Me.pnlState.tbShowWatermark = False
        Me.pnlState.tbSplit = "0,0,0,0"
        Me.pnlState.tbWatermark = Nothing
        Me.pnlState.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlState.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pbarShowProgress
        '
        Me.pbarShowProgress.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pbarShowProgress.BackColor = System.Drawing.Color.Transparent
        Me.pbarShowProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.pbarShowProgress.Location = New System.Drawing.Point(114, 10)
        Me.pbarShowProgress.Name = "pbarShowProgress"
        Me.pbarShowProgress.Size = New System.Drawing.Size(110, 10)
        Me.pbarShowProgress.TabIndex = 27
        Me.pbarShowProgress.tbBackgroundImage = CType(resources.GetObject("pbarShowProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbCanDragValue = False
        Me.pbarShowProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pbarShowProgress.tbDotImage = CType(resources.GetObject("pbarShowProgress.tbDotImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pbarShowProgress.tbDotMouseDown = Nothing
        Me.pbarShowProgress.tbDotMouseHover = Nothing
        Me.pbarShowProgress.tbDotMouseLeave = Nothing
        Me.pbarShowProgress.tbDownloadImage = Global.iTong.My.Resources.Resources.app_progress_background
        Me.pbarShowProgress.tbDownMax = 100
        Me.pbarShowProgress.tbDownMin = 0
        Me.pbarShowProgress.tbDownValue = 0
        Me.pbarShowProgress.tbIsWaiting = False
        Me.pbarShowProgress.tbPlayImage = Global.iTong.My.Resources.Resources.app_progress_value
        Me.pbarShowProgress.tbPlayMax = 100
        Me.pbarShowProgress.tbPlayMin = 0
        Me.pbarShowProgress.tbPlayValue = 0
        Me.pbarShowProgress.tbShowDot = False
        Me.pbarShowProgress.tbShowText = True
        Me.pbarShowProgress.tbSplit = "4,0,4,0"
        Me.pbarShowProgress.Text = "TbControlBar1"
        '
        'btnAbrot
        '
        Me.btnAbrot.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnAbrot.BackColor = System.Drawing.Color.Transparent
        Me.btnAbrot.BindingForm = Nothing
        Me.btnAbrot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAbrot.Location = New System.Drawing.Point(237, 8)
        Me.btnAbrot.Name = "btnAbrot"
        Me.btnAbrot.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnAbrot.Selectable = True
        Me.btnAbrot.Size = New System.Drawing.Size(15, 15)
        Me.btnAbrot.TabIndex = 26
        Me.btnAbrot.tbAdriftIconWhenHover = False
        Me.btnAbrot.tbAutoSize = False
        Me.btnAbrot.tbAutoSizeEx = True
        Me.btnAbrot.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_cancel
        Me.btnAbrot.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnAbrot.tbBadgeNumber = 0
        Me.btnAbrot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAbrot.tbEndEllipsis = False
        Me.btnAbrot.tbIconHoldPlace = True
        Me.btnAbrot.tbIconImage = Nothing
        Me.btnAbrot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAbrot.tbIconMore = False
        Me.btnAbrot.tbIconMouseDown = Nothing
        Me.btnAbrot.tbIconMouseHover = Nothing
        Me.btnAbrot.tbIconMouseLeave = Nothing
        Me.btnAbrot.tbIconPlaceText = 2
        Me.btnAbrot.tbIconReadOnly = Nothing
        Me.btnAbrot.tbImageMouseDown = Nothing
        Me.btnAbrot.tbImageMouseHover = Nothing
        Me.btnAbrot.tbImageMouseLeave = Nothing
        Me.btnAbrot.tbProgressValue = 50
        Me.btnAbrot.tbReadOnly = False
        Me.btnAbrot.tbReadOnlyText = False
        Me.btnAbrot.tbShadow = False
        Me.btnAbrot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAbrot.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAbrot.tbShowDot = False
        Me.btnAbrot.tbShowMoreIconImg = CType(resources.GetObject("btnAbrot.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAbrot.tbShowNew = False
        Me.btnAbrot.tbShowProgress = False
        Me.btnAbrot.tbShowTip = True
        Me.btnAbrot.tbShowToolTipOnButton = False
        Me.btnAbrot.tbSplit = "0,0,0,0"
        Me.btnAbrot.tbText = ""
        Me.btnAbrot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbTextColor = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDisable = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDown = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorHover = System.Drawing.Color.White
        Me.btnAbrot.tbTextMouseDownPlace = 0
        Me.btnAbrot.tbToolTip = ""
        Me.btnAbrot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAbrot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAbrot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.VisibleEx = True
        '
        'lblState
        '
        Me.lblState.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(13, 5)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(95, 21)
        Me.lblState.TabIndex = 0
        Me.lblState.Text = "状态"
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmSite
        '
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.tvwDevice)
        Me.Controls.Add(Me.pnlContainer)
        Me.Name = "frmSite"
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Controls.SetChildIndex(Me.pnlContainer, 0)
        Me.Controls.SetChildIndex(Me.tvwDevice, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlContainer.ResumeLayout(False)
        Me.pnlGuide.ResumeLayout(False)
        Me.pnlGuide.PerformLayout()
        CType(Me.PictureBoxMedian, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBoxJailbreak, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBoxFlash, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tpnlJailbreakPlugin.ResumeLayout(False)
        Me.pnlState.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Public WithEvents tvwDevice As tbTreeView
    Friend WithEvents pnlContainer As System.Windows.Forms.Panel


    Friend WithEvents tpnlJailbreakPlugin As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlState As tbPanel
    Friend WithEvents lblState As System.Windows.Forms.Label
    Friend WithEvents pbarShowProgress As tbControlBar
    Friend WithEvents btnAbrot As tbButton
    Friend WithEvents pnlFirmware As iTong.Components.tbPanel
    Friend WithEvents pnlFlash As iTong.Components.tbPanel
    Friend WithEvents pnlGuide As iTong.Components.tbPanel
    Friend WithEvents PictureBoxMedian As System.Windows.Forms.PictureBox
    Friend WithEvents lblJailbreakBackup As System.Windows.Forms.Label
    Friend WithEvents lblFlashBackup As System.Windows.Forms.Label
    Friend WithEvents lblJailbreakDepict As System.Windows.Forms.Label
    Friend WithEvents lblFlashDepict As System.Windows.Forms.Label
    Friend WithEvents btnInJailbreak As iTong.Components.tbButton
    Friend WithEvents btnInFlash As iTong.Components.tbButton
    Friend WithEvents PictureBoxJailbreak As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBoxFlash As System.Windows.Forms.PictureBox

End Class
