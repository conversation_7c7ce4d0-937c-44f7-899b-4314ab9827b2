﻿Imports System.Xml
Imports System.Text
Imports System.IO
Imports System.Security.Cryptography
Imports System.Threading

'' 引入WebKit相关命名空间
'Imports WebKit
'Imports WebKit.DOM

Public Class frmSite

#Region "--- 全局变量 ---"
    Public Shared mstrJailbreakSelected As String = ""          '记录用户最后一次选中的越狱站点
    Private mDictSite As New Dictionary(Of String, tbWebBrowserEx)(StringComparer.InvariantCultureIgnoreCase)
    Private mDictPanel As New Dictionary(Of String, tbTabPanel)(StringComparer.InvariantCultureIgnoreCase)
    Private mDownManage As MultiThreadDownload

    Private mJailBreak As Boolean = False
    Private mShowDataGridView As Boolean = False
    Private mSearchReturn As DataTable
    Private mJumpUrl As String = String.Empty
    Private mErrorDocumentText As String = String.Empty

    Private WithEvents dgvMusic As New tbDataGridView
    Private mNodeSite As tbTreeNode

    Private mNodeFlash As tbTreeNode
    Private mNodeFirmware As tbTreeNode

    Private mSiteFolder As String = ""          '存放服务器下载下来的网页缓存，如plist文件，树节点的图标，如果存在相同名称的就不再下载了。

    Private mThreadLoadIcon As Thread
    Private mThreadTopicDownload As Thread
    'Private mtdGetAllInstalled As Thread        '获取所有已安装的app

    Private mLoaded As Boolean = False
    Private mLoadConn As Boolean = False

    Private mSiteType As WebSiteType = WebSiteType.Normal
    Private mInstallHelper As iPhoneInstallHelper = Nothing
    Private mUninstallHelper As iPhoneUninstallHelper = Nothing

    'Public Shared Loaded As Boolean = False
    Private mMusicTab As tbTabPanel = Nothing

    '' 当前主界面单击的按钮
    Public _clickName As String = "Welcome"

    Private mtdGetAllInstalled As Thread = Nothing
    Private mPluginLogin As PluginLogin         '同步账号登陆接口  

    Private mIsRecoverMode As Boolean = False
    Private mIsDFUMode As Boolean = False

    Private mCurrentGuideSize As Size = New Size(0, 0)

    Private Shared mStrCurrentSelectTreeNode As String = ""

    '' Added by Utmost20140728
    '' 使用WebKit
    'Private _dictWebKitSites As New Dictionary(Of String, WebKitBrowserEx)(StringComparer.InvariantCultureIgnoreCase)

#If IS_ITONG Then
    Private mFrmFlash As frmFlash = Nothing
    Private mFrmFirmware As frmFirmware = Nothing
#End If

#End Region

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As IDevice)
        Me.New(application, device, WebSiteType.Normal)

        Me.mIsRecoverMode = (TypeOf device Is iPhoneRecoveryDevice)
        Me.mIsDFUMode = (TypeOf device Is iPhoneDFUDevice)

        If Me.mIsRecoverMode Then
            Me.mDeviceRecovery = device
        ElseIf Me.mIsDFUMode Then
            Me.mDeviceDFU = device
        Else
            Me.mDevice = device
        End If

    End Sub

    Public Sub New(ByVal application As IApplication, ByVal device As IDevice, ByVal siteType As WebSiteType)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application

        'If TypeOf device Is iPhoneDevice Then
        '    Me.mDevice = device
        'End If

        'Added by Utmost20140616
        Me.Tag = DeviceType.Unknown
        If TypeOf device Is iPhoneDevice Then
            Me.mDevice = device
            Me.Tag = DeviceType.iOS
        ElseIf TypeOf device Is iPhoneRecoveryDevice Then
            Me.mDeviceRecovery = device
            Me.Tag = DeviceType.iOS
        ElseIf TypeOf device Is iPhoneDFUDevice Then
            Me.mDeviceDFU = device
            Me.Tag = DeviceType.iOS
        ElseIf TypeOf device Is AndroidDevice Then
            Me.mAndroid = device
            Me.Tag = DeviceType.Android
        End If

        Me.CanbeMove = False
        Me.Language = Me.mApplication.Language

        Me.BackColor = System.Drawing.Color.FromArgb(188, 197, 210)
        Me.FunctionMappingKey = FunctionKey.Site

        Me.InitDownLoadAndInstall()

        'SiteXmlHelper.Init()

        Me.mSiteType = siteType
        If Me.mSiteType = WebSiteType.App Then
            Me.tvwDevice.Visible = False
            Me.pnlContainer.Location = New Point(0, 0)
            Me.pnlContainer.Size = Me.Size
        End If
        Me.mSiteFolder = Path.Combine(Folder.CacheFolder, "WebSite")
        Folder.CheckFolder(Me.mSiteFolder)
    End Sub

    Private Sub InitDownLoadAndInstall()
        Me.mDownManage = MultiThreadDownload.Instance()
        RemoveHandler Me.mDownManage.TaskAdd, AddressOf mDownManager_TaskAdd
        AddHandler Me.mDownManage.TaskAdd, AddressOf mDownManager_TaskAdd

        RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf mDownManager_Downloading
        AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf mDownManager_Downloading

        RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf mDownManage_DownloadItemCompleted
        AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf mDownManage_DownloadItemCompleted

        If Me.mDevice IsNot Nothing Then
            Me.InitInstaller()
            Me.InitiPhoneUninstallHelper()
        End If

        '请求下载个人签名包要等待生成包有的时候时间会比较长，在页面上调协等待。
        RemoveHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
        AddHandler WebSiteHelperBase.InstanceBase.DownloadEventHandler, AddressOf OnDownloadEventHandler
        RemoveHandler WebSiteHelper.Instance.DownloadEventHandler, AddressOf OnDownloadEventHandler
        AddHandler WebSiteHelper.Instance.DownloadEventHandler, AddressOf OnDownloadEventHandler
    End Sub


    Private Sub InitInstaller()
        Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)

        RemoveHandler Me.mInstallHelper.InstallDebEventHandler, AddressOf mInstallHelper_OnInstallDeb
        AddHandler Me.mInstallHelper.InstallDebEventHandler, AddressOf mInstallHelper_OnInstallDeb

        RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
        AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
    End Sub

    Private Sub InitiPhoneUninstallHelper()
        Me.mUninstallHelper = iPhoneUninstallHelper.GetInstance(Me.mDevice)

        RemoveHandler Me.mUninstallHelper.UninstallEventHandler, AddressOf mUninstallHelper_OnUninstall
        AddHandler Me.mUninstallHelper.UninstallEventHandler, AddressOf mUninstallHelper_OnUninstall
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        'Me.InitDataGridView()

        Me.lblJailbreakBackup.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.lblFlashBackup.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.lblJailbreakDepict.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.lblFlashDepict.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.btnInJailbreak.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.btnInFlash.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)

        If Common.VerIs30 Then
            Me.InitControlsV3()
        Else
            Me.InitControlsV2()
        End If
    End Sub

    Protected Sub InitControlsV2()
        If Me.mSiteType = WebSiteType.App Then
            Me.InitWebSiteApp()

        ElseIf Me.mSiteType = WebSiteType.Jailbreak Then
            Me.InitWebSiteJailbreak()
        Else
            '1.从本地取得页面节点的设置
            Me.InitWebSiteFromXML()
            '重新下载最新的xml文件
            'Me.mThreadLoadIcon = New Thread(AddressOf DownloadNodeIconAndXML)
            'With Me.mThreadLoadIcon
            '    .IsBackground = True
            '    .Start()
            'End With

            'ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf DownloadNodeIconAndXMLPool))
        End If
    End Sub

    Protected Sub InitControlsV3()
        '初始化登陆接口
        Me.mPluginLogin = PluginLogin.Instance()
        RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        AddHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        RemoveHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent
        AddHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent

        If Me.mSiteType = WebSiteType.Jailbreak Then
            Me.InitWebSiteJailbreak()
        Else
            Me.tvwDevice.Visible = False
            Me.pnlContainer.Location = New Point(0, 0)
            Me.pnlContainer.Size = Me.Size

            '1.从本地取得页面节点的设置
            Me.ReInitNode()
            '重新下载最新的xml文件
            'Me.mThreadLoadIcon = New Thread(AddressOf DownloadNodeIconAndXML)
            'With Me.mThreadLoadIcon
            '    .IsBackground = True
            '    .Start()
            'End With

            'ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf DownloadNodeIconAndXMLPool))

        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        'Me.lblAppTip.Text = Me.Language.GetString("Site.Label.FreeInstallApp")      '"未越狱也能免费安装app!"

        'If IniSetting.GetWebSiteAppShareShowTip() AndAlso Me.mSiteType = WebSiteType.Normal Then
        '    Me.pnlAppTip.Size = New Point(Me.lblAppTip.Width + 10, Me.lblAppTip.Height + 25)
        '    Me.pnlAppTip.Visible = True
        '    Me.pnlAppTip.BringToFront()
        '    Me.pnlAppTip.Location = New Point(Me.tvwDevice.Width - Me.pnlAppTip.Width - 3, Me.pnlAppTip.Top)

        'Else
        '    Me.pnlAppTip.Visible = False
        '    Me.pnlAppTip.SendToBack()
        'End If

    End Sub

    Public Overrides Sub CheckInit()
        MyBase.CheckInit()

#If IS_ITONG Then
        Me.mFrmFlash = Common.CheckFormExist(GetType(frmFlash))
        If Me.mFrmFlash IsNot Nothing Then
            Dim iDevice As Object = Me.mDevice
            If Me.mDeviceRecovery IsNot Nothing Then
                iDevice = Me.mDeviceRecovery
            ElseIf Me.mDeviceDFU IsNot Nothing Then
                iDevice = Me.mDeviceDFU
            End If
            Me.mFrmFlash.OnConnect(iDevice)
        End If

        Me.mFrmFirmware = Common.CheckFormExist(GetType(frmFirmware))
        If Me.mFrmFirmware IsNot Nothing Then
            Me.mFrmFirmware.OnConnect(Me.mDevice)
        End If
#End If

        If Me.mDevice Is Nothing Then
            Return
        End If

        Me.mJailBreak = Me.mDevice.Jailbreaked
        If Me.mDevice.Jailbreaked <> mJailBreak Then

            If Me.tvwDevice.SelectedNode IsNot Nothing AndAlso _
               Me.tvwDevice.SelectedNode.Name.Equals("App", StringComparison.InvariantCultureIgnoreCase) Then

                If Me.mDictSite.ContainsKey("App") Then
                    Me.mDictSite("App").Navigate(Me.mDictSite("App").Name)
                End If

                '' 使用WebKit
                'If Me._dictWebKitSites.ContainsKey("App") Then
                '    Me._dictWebKitSites("App").Navigate(Me._dictWebKitSites("App").Name)
                'End If

            End If
        End If

        '在 Cookie 里面写入是否越狱，以最后接入的那个设备为准，给WEB后端调用
        CookieApi.SetCookie("bo", IIf(Me.mJailBreak, "1", "0"), Now.AddDays(14))
        '写入设备信息到ciikie
        CookieApi.SetCookie("deviceid", Me.GetDeviceID(), Now.AddDays(14))
        CookieApi.SetCookie("DeviceIdentifier", Me.mDevice.Identifier, Now.AddDays(14))

        '程序初始化的时候将是否有设备连接过写到cookie 　和　ini 
        CookieApi.SetCookie("iosDeviceConnected", "1", Now.AddDays(14))
        IniSetting.SetDeviceConnectedOneTime(True)

        '设置浏览器自定义属性
        For Each wb As tbWebBrowserEx In Me.mDictSite.Values
            wb.Jailbreaked = Me.GetJailbreaked()
            If Me.mDevice IsNot Nothing Then
                wb.SN = Me.mDevice.SerialNumber
            End If
        Next
        Me.SetWebAppStatus_AllInstalled(Nothing)

        '  Me.LoadDevice()
        '' 使用WebKit
        'For Each wbkt As WebKitBrowserEx In Me._dictWebKitSites.Values
        '    wbkt.Jailbreaked = Me.GetJailbreaked()
        '    If Me.mDevice IsNot Nothing Then
        '        wbkt.SN = Me.mDevice.SerialNumber
        '    End If
        'Next
        'Me.SetWebAppStatus_AllInstalled(Nothing)
        '''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

        Try
            Me.InitInstaller()
            Me.InitiPhoneUninstallHelper()

            Me.SetBrowserStatus()

            If Me.mDictSite.ContainsKey("App") AndAlso Not Me.mDevice.Jailbreaked Then
                WebSiteHelper.Instance.SetTipCloseForApp(Me.mDictSite("App"), False)
            End If

            '' 使用WebKit
            'If Me._dictWebKitSites.ContainsKey("App") AndAlso Not Me.mDevice.Jailbreaked Then
            '    Me.SetTipCloseForApp(Me._dictWebKitSites("App"))
            'End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnConnect")
        End Try
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.OnDisconnectToFlash(device)

        Me.SetBrowserStatus()
        Me.SetWebAppStatus_DeviceDisconnect()
        If Me.mDictSite.ContainsKey("App") Then
            WebSiteHelper.Instance.SetTipCloseForApp(Me.mDictSite("App"), True)
        End If

        Me.Stop2SetWebAppStatus_AllInstalled()
    End Sub

    Public Overrides Sub OnDFUDisconnect(device As Device.iPhoneDFUDevice)
        MyBase.OnDFUDisconnect(device)
        Me.OnDisconnectToFlash(device)
    End Sub

    Public Overrides Sub OnRecoverDisconnect(device As Device.iPhoneRecoveryDevice)
        MyBase.OnRecoverDisconnect(device)
        Me.OnDisconnectToFlash(device)
    End Sub

    'Private Sub frmSite_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown

    '    If _clickName.Equals("App", StringComparison.OrdinalIgnoreCase) Then
    '        Me.SetDefaultNodeSelected()
    '    End If

    'End Sub

    Private Delegate Sub SetDefaultNodeSelectedHandler()
    Private Sub SetDefaultNodeSelected()
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetDefaultNodeSelectedHandler(AddressOf SetDefaultNodeSelected))
            Else
                If Me.tvwDevice.SelectedNode Is Nothing Then
                    Dim node As tbTreeNode = Me.tvwDevice.Nodes("App")

                    If (Me.mDevice Is Nothing OrElse _
                       (Not Me.mDevice.InstallCydia AndAlso Not Me.mDevice.Jailbreaked)) AndAlso _
                        Me.tvwDevice.Nodes("AppShare") IsNot Nothing Then

                        node = Me.tvwDevice.Nodes("AppShare")

                    End If

                    If node IsNot Nothing Then
                        Me.tvwDevice.SelectedNode = node
                    End If

                    Me.mLoaded = True
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub InitWebSiteApp()
        Me.mNodeSite = Me.tvwDevice.Nodes.Add("Site", "站点", True)
        Dim strAppVer As String = My.Application.Info.Version.ToString(4)
        Dim strLang As String = Me.Language.CurrentLanguage.LangName

        Dim strDeviceID As String = Me.GetDeviceID()
        Dim pType As String = Me.GetDeviceProductType()
        Dim appId As String = Me.GetDeviceIDForApp()

        'http://tm.tongbu.com/music/v2/search/index.html?tbversion=*******
        '"http://v2.tongbu.com/app?ver={0}&lang={1}&deviceid={2}&devicetype={3}&form=app"
        Dim strUrlApp As String = String.Format(SiteXmlHelper.GetSiteUrlByName("App") & "&form=app", strAppVer, strLang, appId, pType)

        If Me.mDevice IsNot Nothing AndAlso Not Me.GetJailbreaked() Then
            strUrlApp = strUrlApp & "&istip=1"
            'IniSetting.SetWebSiteShowTip()
        End If

        '"http://v2.tongbu.com/appshare?ver={0}&lang={1}&deviceid={2}&devicetype={3}&form=app"
        Dim strUrlAppShare As String = String.Format(SiteXmlHelper.GetSiteUrlByName("AppShare") & "&form=app", strAppVer, strLang, appId, pType)
        Dim strConfig As String = Folder.AppFolder & "\Site.ini"
        Dim strValue As String = String.Empty

        strValue = IniClass.GetIniSectionKey("Url", "App", strConfig)
        If Not String.IsNullOrEmpty(strValue) Then
            strUrlApp = strValue
        End If

        Dim node As tbTreeNode = Nothing
        With Me.mNodeSite
            node = .ChildNodes.Add("App", "越狱商店")                          '"同步软件"
            node.NodeIcon = My.Resources.tvw_tongbuapp_2
            node.NodeIconState = ImageState.TwoState

            node = .ChildNodes.Add("AppShare", "正版商店")                          '"同步软件"
            node.NodeIcon = My.Resources.tvw_tongbuapp_2
            node.NodeIconState = ImageState.TwoState
        End With

        Me.mDictSite.Add("App", Me.CreateWebSite(strUrlApp, False))
        Me.mDictSite.Add("AppShare", Me.CreateWebSite(strUrlAppShare, False))

        '' 使用WebKit
        'Me._dictWebKitSites.Add("App", Me.CreateWebKitSite(strUrlApp, False))
        'Me._dictWebKitSites.Add("AppShare", Me.CreateWebKitSite(strUrlApp, False))

    End Sub

    Private Sub InitWebSiteJailbreak()

        Me.mNodeSite = Me.tvwDevice.Nodes.Add("Jailbreak", "越狱", True)

        Dim ver As Version = My.Application.Info.Version
        Dim strPara As String = ""

        If Me.mDevice IsNot Nothing Then
            strPara = String.Format("?device={0}&firmware={1}&version={2}", Me.mDevice.ProductType, Me.mDevice.ProductVersion, String.Format("{0}.{1}", ver.Major, ver.Minor))
        End If

        Dim strUrlGuide As String = "http://yueyu.tongbu.com/api/pc" & strPara
        Dim strUrlFirmware As String = "http://yueyu.tongbu.com/api/mac_gd" & strPara
        Dim strUrlPlugin As String = "http://v2.tongbu.com/cydia" & strPara
        Dim strUrlTBSources As String = "http://v3.tongbu.com/share/baike/course/article/76291"

        Dim node As tbTreeNode = Nothing
        With Me.mNodeSite
            node = .ChildNodes.Add("Guide", "越狱教程")                          '"越狱教程"
            node.NodeIcon = My.Resources.tvw_tutorials_2
            node.NodeIconState = ImageState.TwoState

            'If Not Common.IsTestMode Then
            '    node = .ChildNodes.Add("Firmware", "固件下载")                       '"固件下载"
            '    node.NodeIcon = My.Resources.tvw_firmware_2
            '    node.NodeIconState = ImageState.TwoState
            'End If

            node = .ChildNodes.Add("Plugin", "越狱插件")                          '"越狱插件"
            node.NodeIcon = My.Resources.tvw_plug_2
            node.NodeIconState = ImageState.TwoState

            node = .ChildNodes.Add("Sources", "同步推源 ")                          '"同步推源 "
            node.NodeIcon = My.Resources.tvw_tongbuapp_2
            node.NodeIconState = ImageState.TwoState
        End With


        Me.LoadFlashFirmware()

        Me.mDictSite.Add("Guide", Me.CreateWebSite(strUrlGuide, False))
        If Not Common.IsTestMode Then
            Me.mDictSite.Add("Firmware", Me.CreateWebSite(strUrlFirmware, False))
        End If
        Me.mDictSite.Add("Plugin", Me.CreateWebSite(strUrlPlugin, False))
        Me.mDictSite.Add("Sources", Me.CreateWebSite(strUrlTBSources, True))

        Dim wb As tbWebBrowserEx = Me.mDictSite("Plugin")
        AddHandler wb.Navigated, AddressOf WebBrowser_Navigated

        '' 使用WebKit
        'Me._dictWebKitSites.Add("Guide", CreateWebKitSite(strUrlGuide, False))
        'Me._dictWebKitSites.Add("Firmware", CreateWebKitSite(strUrlFirmware, False))
        'Me._dictWebKitSites.Add("Plugin", CreateWebKitSite(strUrlPlugin, False))

        'Dim wb As WebKitBrowserEx = Me._dictWebKitSites("Plugin")
        'AddHandler wb.Navigated, AddressOf WebKitBrowser_Navigated

#If IS_ITONG Then
        '如果不是刷机中 且 没有选中任何节点  出引导页
        If FlashHelper.GetInstance(Me.mDevice).IsDoFlash Then
            Me.tvwDevice.SelectedNode = Me.tvwDevice.Nodes("OnekeyFlash")
        ElseIf mStrCurrentSelectTreeNode.Length > 0 Then
            Me.tvwDevice.SelectedNode = Me.tvwDevice.Nodes(mStrCurrentSelectTreeNode)
        Else
            Me.LoadpnlGuide()
        End If
#Else
        If Not String.IsNullOrEmpty(frmSite.mstrJailbreakSelected) AndAlso Me.tvwDevice.Nodes(frmSite.mstrJailbreakSelected) IsNot Nothing Then
            Me.tvwDevice.SelectedNode = Me.tvwDevice.Nodes(frmSite.mstrJailbreakSelected)
        Else
            Me.tvwDevice.SelectedNode = Me.mNodeSite.ChildNodes(0)
        End If
#End If

        RemoveHandler Me.mApplication.GoToSiteEvent, AddressOf OnGoToSiteEvent
        AddHandler Me.mApplication.GoToSiteEvent, AddressOf OnGoToSiteEvent

        Me.LoadDevice()

    End Sub

    Private Sub InitWebSiteFromXML()
        Try
            Dim strPath As String = Path.Combine(Me.mSiteFolder, SiteXmlHelper.SiteFileName)

            If Not File.Exists(strPath) Then
                Return
            End If

            Dim doc As New System.Xml.XmlDocument()
            doc.Load(strPath)

            If doc IsNot Nothing AndAlso doc.ChildNodes.Count > 0 Then

                '取得site节点
                Dim site As Xml.XmlElement = doc.ChildNodes(0)

                For Each itemGroup As Xml.XmlElement In site.ChildNodes

                    '取得group
                    Dim nodeGroup As tbTreeNode = Me.tvwDevice.Nodes.Add(itemGroup.Name, itemGroup.Attributes("Text").Value, True)

                    If Common.VerIs30 AndAlso nodeGroup.Name.Equals("musicgroup", StringComparison.InvariantCultureIgnoreCase) Then

                        '把音乐放到一个tap页面里面。
                        Dim node As tbTreeNode = Nothing
                        Dim objSite As WebSiteObject = Nothing

                        For Each itemNode As Xml.XmlElement In itemGroup.ChildNodes

                            objSite = New WebSiteObject(itemNode)
                            If objSite.Name.Equals("webringtone", StringComparison.InvariantCultureIgnoreCase) Then
                                Me.UpdateWebPage(itemNode, nodeGroup)
                            Else
                                Me.UpdateTabGroupPage(nodeGroup, node, Me.mMusicTab, objSite)
                            End If

                        Next
                    Else
                        For Each itemNode As Xml.XmlElement In itemGroup.ChildNodes
                            Me.UpdateWebPage(itemNode, nodeGroup)
                        Next
                    End If
                Next
                '如果用户选中站点就默认先中一个节点。（只有2.0）
                If Not Common.VerIs30 Then
                    Me.SetDefaultNodeSelected()
                End If
            End If

            Dim thr As New Thread(AddressOf GetNodeIcon)
            With thr
                .IsBackground = True
                .Start()
            End With

        Catch ex As XmlException
            Debug.Write(ex)
        Catch ex As FileNotFoundException
            Debug.Write(ex)
        Catch ex As Exception
        End Try
    End Sub

    Private Sub UpdateTabGroupPage(ByVal nodeGroup As tbTreeNode, ByRef node As tbTreeNode, ByRef tab As tbTabPanel, ByVal objSite As WebSiteObject)
        'objSite = New WebSiteObject(itemNode)
        Dim url As String = Me.SetUrlPara(objSite.Url)

        Me.RemovePage(tab, objSite)
        If node Is Nothing OrElse tab Is Nothing OrElse _
          objSite.Name.Equals(node.Tag.name) Then  'node.Tag.name = objSite.Name

            Dim strText As String = "同步音乐"
            If Not String.IsNullOrEmpty(objSite.TabGroup) Then
                strText = objSite.TabGroup
            End If

            node = nodeGroup.ChildNodes(objSite.Name)
            If node Is Nothing Then
                node = nodeGroup.ChildNodes.Add(objSite.Name, strText)
            End If

            node.NodeIcon = Me.GetNodeIcon(objSite.IconName)
            node.NodeIconState = ImageState.TwoState
            node.Tag = objSite  '把网页信息存在tag里面。

            objSite.ShowTab = True
            If objSite.ShowTab Then
                tab = Me.CreateTabPage(objSite.Name)
                Me.pnlContainer.Controls.Add(tab)

                Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(url, True, tab)
                tab.Pages.Add(New tbTabPanelPage(url, objSite.Text, webbrow))

                If Me.mDictSite.ContainsKey(objSite.Name) Then
                    Me.mDictSite.Remove(objSite.Name)
                End If

                If Me.mDictPanel.ContainsKey(objSite.Name) Then
                    Me.mDictPanel.Remove(objSite.Name)
                End If
                Me.mDictSite.Add(objSite.Name, webbrow)


                ''' 使用WebKit
                'Dim browser As WebKitBrowserEx = Me.CreateWebKitSite(url, True, tab)
                'tab.Pages.Add(New tbTabPanelPage(url, objSite.Text, browser))

                'If Me._dictWebKitSites.ContainsKey(objSite.Name) Then
                '    Me._dictWebKitSites.Remove(objSite.Name)
                'End If
                'If Me._dictWebKitSites.ContainsKey(objSite.Name) Then
                '    Me._dictWebKitSites.Remove(objSite.Name)
                'End If
                'Me._dictWebKitSites.Add(objSite.Name, browser)
                '''''''''''''''''''''''''''''''''''''''''''''''''

                Me.mDictPanel.Add(objSite.Name, tab)

                AddHandler tab.SelectIndexChanged, AddressOf tab_SelectIndexChanged
                AddHandler tab.WebHome, AddressOf tab_WebHome
                AddHandler tab.WebNext, AddressOf tab_WebNext
                AddHandler tab.WebPrevious, AddressOf tab_WebPrevious
                AddHandler tab.WebRefresh, AddressOf tab_WebRefresh
            End If
            objSite.Text = strText
        Else

            Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(url, True, tab)
            tab.Pages.Add(New tbTabPanelPage(url, objSite.Text, webbrow))

            ''' 使用WebKit
            'Dim browser As WebKitBrowserEx = Me.CreateWebKitSite(url, True, tab)
            'tab.Pages.Add(New tbTabPanelPage(url, objSite.Text, browser))

        End If
    End Sub

    Private Sub RemovePage(ByVal pnl As tbTabPanel, ByVal objSite As WebSiteObject)
        Dim ctrl As tbTabPanelPage = Nothing
        Try
            If pnl Is Nothing Then
                Return
            End If

            Dim url As String = Me.SetUrlPara(objSite.Url)
            For Each Item As tbTabPanelPage In pnl.Pages
                If Item.Name.Equals(url, StringComparison.InvariantCultureIgnoreCase) Then
                    ctrl = Item
                    Exit For
                End If
            Next

            If ctrl IsNot Nothing Then
                pnl.Pages.Remove(ctrl)
            End If

            Me.mDictSite.Remove(objSite.Name)

            '' 使用WebKit
            'Me._dictWebKitSites.Remove(objSite.Name)

            Me.mDictPanel.Remove(objSite.Name)

        Catch ex As Exception
        End Try
    End Sub

    Private Sub UpdateWebPage(ByVal itemNode As Xml.XmlElement, ByVal nodeGroup As tbTreeNode)
        Dim objSite As New WebSiteObject(itemNode)

        Dim node As tbTreeNode = Nothing

        node = nodeGroup.ChildNodes.Add(objSite.Name, objSite.Text)
        node.NodeIcon = Me.GetNodeIcon(objSite.IconName)
        node.NodeIconState = ImageState.TwoState
        node.Tag = objSite  '把网页信息存在tag里面。

        Dim url As String = Me.SetUrlPara(objSite.Url)
        If Me.mDevice IsNot Nothing AndAlso Not Me.GetJailbreaked() AndAlso _
           node.Name.Equals("App", StringComparison.InvariantCultureIgnoreCase) Then
            url = url & "&istip=1"
        End If

        If objSite.ShowTab Then
            Dim tab As tbTabPanel = Me.CreateTabPage(objSite.Name)
            Me.pnlContainer.Controls.Add(tab)

            Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(url, True, tab)
            tab.Pages.Add(New tbTabPanelPage(url, objSite.Text, webbrow))

            If Not Me.mDictSite.ContainsKey(objSite.Name) Then
                Me.mDictSite.Add(objSite.Name, webbrow)
            End If


            '' 使用WebKit
            'Dim browser As WebKitBrowserEx = Me.CreateWebKitSite(url, True, tab)
            'tab.Pages.Add(New tbTabPanelPage(url, objSite.Text, browser))

            'If Not Me._dictWebKitSites.ContainsKey(objSite.Name) Then
            '    Me._dictWebKitSites.Add(objSite.Name, browser)
            'End If

            Me.mDictPanel.Add(objSite.Name, tab)
        Else
            If Not Me.mDictSite.ContainsKey(objSite.Name) Then
                Me.mDictSite.Add(objSite.Name, Me.CreateWebSite(url, objSite.ShowTab))
            End If

            '' 使用WebKit
            'If Not Me._dictWebKitSites.ContainsKey(objSite.Name) Then
            '    Me._dictWebKitSites.Add(objSite.Name, Me.CreateWebKitSite(url, objSite.ShowTab))
            'End If
        End If
    End Sub

    Private Sub OnGoToSiteEvent(ByVal sender As tbWebBrowser, ByVal type As ActionFuncType, ByVal strNode As String, ByVal strDeviceID As String, ByVal strUrl As String)

        If type = ActionFuncType.Jailbreak Then
            If strDeviceID.Length > 0 AndAlso Me.mDevice.DeviceID <> strDeviceID Then
                Return
            End If
            frmSite.mstrJailbreakSelected = strNode
            Me.tvwDevice.SelectedNode = Me.tvwDevice.Nodes(frmSite.mstrJailbreakSelected)
        End If

    End Sub

#End Region

#Region "--- TabPage WebBrowser事件 ---"

    Private Sub tab_SelectIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        Dim pnl As tbTabPanel = CType(sender, tbTabPanel)

        If pnl.SelectedPage.PageFrom IsNot Nothing Then
            If TypeOf pnl.SelectedPage.PageFrom Is tbWebBrowserEx AndAlso pnl.SelectedPage.PageFrom.Tag Is Nothing Then
                CType(pnl.SelectedPage.PageFrom, tbWebBrowserEx).Navigate(pnl.SelectedPage.PageFrom.Name)
                pnl.SelectedPage.PageFrom.Tag = "Selected"
            End If

            '' 使用WebKit
            'If TypeOf pnl.SelectedPage.PageFrom Is WebKitBrowserEx AndAlso pnl.SelectedPage.PageFrom.Tag Is Nothing Then
            '    CType(pnl.SelectedPage.PageFrom, WebKitBrowserEx).Navigate(pnl.SelectedPage.PageFrom.Name)
            '    pnl.SelectedPage.PageFrom.Tag = "Selected"
            'End If

            Me.SetNavigatingContralStatus(pnl.SelectedPage.PageFrom)

            ''软件页面不同tab点击引导web跳转
            If pnl.SelectedPage IsNot Nothing AndAlso Not String.IsNullOrEmpty(pnl.SelectedPage.PageName) Then

                '' WebBrowser 及 使用WebKit
                WebSiteHelper.Instance().SetWebAppMarketStatus(pnl.SelectedPage.PageFrom, "navBar", pnl.SelectedPage.PageName, "")
            End If

        End If
    End Sub

    Private Sub tab_DeviceButtonStatusChanged(ByVal sender As Object, ByVal e As DeviceButtonStatusChangedEventArgs)
        Dim pnl As tbTabPanel = CType(sender, tbTabPanel)

        If pnl.SelectedPage IsNot Nothing AndAlso Not String.IsNullOrEmpty(pnl.SelectedPage.PageName) Then

            ''  WebBrowser 及 使用WebKit
            WebSiteHelper.Instance().SetWebAppMarketStatus(pnl.SelectedPage.PageFrom, "navBar", e.DeviceStatus.ToString.ToLower, "")
        End If
    End Sub

    Private Sub tab_WebHome(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim pnl As tbTabPanel = CType(sender, tbTabPanel)

        If pnl.SelectedPage.PageFrom IsNot Nothing Then

            If TypeOf pnl.SelectedPage.PageFrom Is tbWebBrowserEx Then
                CType(pnl.SelectedPage.PageFrom, tbWebBrowserEx).Navigate(pnl.SelectedPage.PageFrom.Name)
            End If

            '' 使用WebKit
            'If TypeOf pnl.SelectedPage.PageFrom Is WebKitBrowserEx Then
            '    CType(pnl.SelectedPage.PageFrom, WebKitBrowserEx).Navigate(pnl.SelectedPage.PageFrom.Name)
            'End If

            pnl.SelectedPage.PageFrom.Tag = "Selected"
        End If
    End Sub

    Private Sub tab_WebNext(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim pnl As tbTabPanel = CType(sender, tbTabPanel)

        If pnl.SelectedPage.PageFrom IsNot Nothing Then

            If TypeOf pnl.SelectedPage.PageFrom Is tbWebBrowserEx Then
                CType(pnl.SelectedPage.PageFrom, tbWebBrowserEx).GoForward()
            End If

            '' 使用WebKit
            'If TypeOf pnl.SelectedPage.PageFrom Is WebKitBrowserEx Then
            '    CType(pnl.SelectedPage.PageFrom, WebKitBrowserEx).GoForward()
            'End If

        End If
    End Sub

    Private Sub tab_WebPrevious(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim pnl As tbTabPanel = CType(sender, tbTabPanel)

        If pnl.SelectedPage.PageFrom IsNot Nothing Then

            If TypeOf pnl.SelectedPage.PageFrom Is tbWebBrowserEx Then
                CType(pnl.SelectedPage.PageFrom, tbWebBrowserEx).GoBack()
            End If

            '' 使用WebKit
            'If TypeOf pnl.SelectedPage.PageFrom Is WebKitBrowserEx Then
            '    CType(pnl.SelectedPage.PageFrom, WebKitBrowserEx).GoBack()
            'End If
        End If
    End Sub

    Private Sub tab_WebRefresh(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim pnl As tbTabPanel = sender
        If pnl.SelectedPage.PageFrom IsNot Nothing Then

            If TypeOf pnl.SelectedPage.PageFrom Is tbWebBrowserEx Then
                Dim webPage As tbWebBrowserEx = CType(pnl.SelectedPage.PageFrom, tbWebBrowserEx)
                webPage.Navigate(webPage.Url.OriginalString)
            End If

            '' 使用WebKit
            'If TypeOf pnl.SelectedPage.PageFrom Is WebKitBrowserEx Then
            '    Dim webPage As WebKitBrowserEx = CType(pnl.SelectedPage.PageFrom, WebKitBrowserEx)
            '    webPage.Navigate(webPage.Url.OriginalString)
            'End If

        End If
    End Sub

    Private Sub SetNavigatingContralStatus(ByVal ctrl As Control)  ' ByVal webBorwser As tbWebBrowserEx
        Try

            If ctrl IsNot Nothing Then
                Dim browser As tbWebBrowserEx = CType(ctrl, tbWebBrowserEx)

                '' 使用WebKit
                'Dim browser As WebKitBrowserEx = CType(ctrl, WebKitBrowserEx)

                If Me.mMusicTab IsNot Nothing AndAlso _
                   ctrl Is Me.mMusicTab.SelectedPage.PageFrom Then

                    Me.mMusicTab.tbBarWebButtonsNextEnable = browser.CanGoForward
                    Me.mMusicTab.tbBarWebButtonsPreviousEnable = browser.CanGoBack

                End If

                Dim tagName As String = String.Empty
                If browser.Tag IsNot Nothing Then
                    tagName = browser.Tag.ToString()
                End If

                Dim tab As tbTabPanel = Me.GetTabPage(tagName)

                If tab IsNot Nothing AndAlso Me.CheckToDoBrowserEvent(tagName) Then

                    tab.tbBarWebButtonsNextEnable = browser.CanGoForward
                    tab.tbBarWebButtonsPreviousEnable = browser.CanGoBack

                End If
            End If

        Catch ex As Exception
            Debug.Write(ex)
        End Try
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub WebBrowser_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs)
        Try
            Dim urlStr As String = e.Url.ToString()

            If urlStr.StartsWith("tongbu://music/search?name=", StringComparison.OrdinalIgnoreCase) Then

                'Me.txtSearch.Text = Utility.GetParamValueFromQuery("name", urlStr)
                'Me.StartSearch()
                'e.Cancel = True
            Else
                Dim strApp As String = "app"
                If Me.tvwDevice.Nodes.Count > 1 Then
                    strApp = ""
                End If

                If (mDevice IsNot Nothing AndAlso mAndroid IsNot Nothing) OrElse _
                   (mAndroid Is Nothing AndAlso mDevice Is Nothing) Then
                    WebSiteHelper.Instance().WebBrowserNavigating(mApplication, Nothing, sender, e, strApp)

                ElseIf mDevice IsNot Nothing Then
                    WebSiteHelper.Instance().WebBrowserNavigating(mApplication, mDevice, sender, e, strApp)

                ElseIf mAndroid IsNot Nothing Then
                    WebSiteHelper.Instance().WebBrowserNavigating(mApplication, mAndroid, sender, e, strApp)
                End If

            End If
            'Return

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSite.WebBrowser_Navigating")
        End Try
    End Sub

    Private Sub WebBrowser_Navigated(ByVal sender As Object, ByVal e As System.Windows.Forms.WebBrowserNavigatedEventArgs)
        WebSiteHelper.Instance.WebBrowserNavigated(Me.mDevice, sender, e)
        Me.SetNavigatingContralStatus(sender)
    End Sub

    Private Sub SetBrowserStatus()
        Try
            Dim browser As tbWebBrowserEx = Nothing

            If Me.mDictSite.ContainsKey("Plugin") Then
                browser = Me.mDictSite("Plugin")
            End If

            If Me.mDictSite.ContainsKey("App") Then
                browser = Me.mDictSite("App")
            End If

            If browser Is Nothing Then
                Return
            End If

            '' 使用WebKit
            'Dim browser As WebKitBrowserEx = Nothing
            'If Me._dictWebKitSites.ContainsKey("Plugin") Then
            '    browser = Me._dictWebKitSites("Plugin")
            'End If

            'If Me._dictWebKitSites.ContainsKey("App") Then
            '    browser = Me._dictWebKitSites("App")
            'End If
            'If browser Is Nothing Then
            '    Return
            'End If
            ''''''''''''''''''''''''''''''''''''''''''''''''''

            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                browser.IsConnected = True

                If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
                    browser.Jailbreaked = True
                    Me.SetWebStatus(0, WebPluginStatus.DeviceConnected)

                Else
                    browser.Jailbreaked = False
                    Me.SetWebStatus(0, WebPluginStatus.Jail)
                End If

            Else
                browser.IsConnected = False
                browser.Jailbreaked = False

                Me.SetWebStatus(0, WebPluginStatus.DeviceDisconnect)
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Sub webDownloadPage_NavigateError(ByVal sender As Object, ByVal e As WebBrowserNavigateErrorEventArgs)
        WebSiteHelper.Instance.WebBrowserNavigateError(Me.mApplication, sender, e)
    End Sub

    Private Sub webDownloadPage_NewWindow3(ByVal sender As tbWebBrowserEx, ByRef ppDisp As Object, ByRef Cancel As Boolean, ByVal dwFlags As UInteger, ByVal bstrUrlContext As String, ByVal bstrUrl As String)
        Dim webObject As WebSiteObject = mfrmSite.tvwDevice.SelectedNode.Tag
        If webObject IsNot Nothing AndAlso TypeOf webObject Is WebSiteObject Then
            WebSiteHelper.Instance.WebBrowserNewWindow3(sender, ppDisp, Cancel, dwFlags, bstrUrlContext, bstrUrl, webObject)
        Else
            WebSiteHelper.Instance.WebBrowserNewWindow3(sender, ppDisp, Cancel, dwFlags, bstrUrlContext, bstrUrl, Nothing)
        End If

    End Sub

    Private Sub tabPanel_WebHome(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            Dim browser As Control = Nothing
            Me.GetBrowserFromTab(sender, browser)

            If browser IsNot Nothing Then
                Dim tagName As String = browser.Tag.ToString()
                If Me.CheckToDoBrowserEvent(tagName) Then

                    CType(browser, tbWebBrowserEx).Navigate(browser.Name)

                    '' 使用WebKit
                    'CType(browser, WebKitBrowserEx).Navigate(browser.Name)
                Else
                    WebSiteHelper.Instance().SetWebAppMarketStatus(browser, "navBar", "home", "")
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub tabPanel_WebNext(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            Dim browser As Control = Nothing

            Me.GetBrowserFromTab(sender, browser)

            If browser IsNot Nothing Then

                Dim tagName As String = browser.Tag.ToString()

                If Me.CheckToDoBrowserEvent(tagName) Then
                    CType(browser, tbWebBrowserEx).GoForward()

                    '' 使用WebKit
                    'CType(browser, WebKitBrowserEx).GoForward()
                Else
                    WebSiteHelper.Instance().SetWebAppMarketStatus(browser, "navBar", "goForward", "")
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub tabPanel_WebPrevious(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            Dim browser As Control = Nothing
            Me.GetBrowserFromTab(sender, browser)

            If browser IsNot Nothing Then
                Dim tagName As String = browser.Tag.ToString()

                If Me.CheckToDoBrowserEvent(tagName) Then

                    CType(browser, tbWebBrowserEx).GoBack()

                    '' 使用WebKit
                    'CType(browser, WebKitBrowserEx).GoBack()
                Else
                    WebSiteHelper.Instance().SetWebAppMarketStatus(browser, "navBar", "goBack", "")
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub tabPanel_WebRefresh(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            Dim browser As Control = Nothing

            Me.GetBrowserFromTab(sender, browser)
            If browser IsNot Nothing Then

                Dim tagName As String = browser.Tag.ToString()

                If Me.CheckToDoBrowserEvent(tagName) Then
                    Dim wb As tbWebBrowserEx = CType(browser, tbWebBrowserEx)

                    '' 使用WebKit
                    'Dim wb As WebKitBrowserEx = CType(browser, WebKitBrowserEx)
                    wb.Navigate(wb.Url.OriginalString)
                Else
                    WebSiteHelper.Instance().SetWebAppMarketStatus(browser, "navBar", "reload", "")
                End If
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Function CheckToDoBrowserEvent(ByVal tagName As String) As Boolean  ' ByVal browser As tbWebBrowserEx
        Dim blnReturn As Boolean = True

        Try
            Dim node As tbTreeNode = Me.tvwDevice.Nodes(tagName)

            If node IsNot Nothing AndAlso node.Tag IsNot Nothing AndAlso _
               TypeOf node.Tag Is WebSiteObject Then

                Dim objsite As WebSiteObject = CType(node.Tag, WebSiteObject)
                If objsite.Tabs IsNot Nothing AndAlso objsite.Tabs.Count > 0 Then
                    blnReturn = False
                End If

            End If

        Catch ex As Exception
        End Try

        Return blnReturn
    End Function

    Private Sub GetBrowserFromTab(ByVal tab As tbTabPanel, ByRef browser As Control)

        If tab.Tag IsNot Nothing AndAlso TypeOf tab.Tag Is String Then
            browser = Me.mDictSite(tab.Tag)
        End If
        If browser Is Nothing Then
            browser = Me.mDictSite(tab.Name.Replace("Tab", ""))
        End If

        '' 使用WebKit
        'If tab.Tag IsNot Nothing AndAlso TypeOf tab.Tag Is String Then
        '    browser = Me._dictWebKitSites(tab.Tag)
        'End If
        'If browser Is Nothing Then
        '    browser = Me._dictWebKitSites(tab.Name.Replace("Tab", ""))
        'End If
    End Sub

    Private Sub tvwDevice_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As tbTreeView_SelectedIndexChanged_EventArgs) Handles tvwDevice.SelectedIndexChanged
        If Not e.Node.Checked Then
            Return
        End If
        Dim strName As String = Me.tvwDevice.SelectedNode.Name

        If strName = CONST_DEVICESTATE Then
            e.Cancel = True
            Return
        End If

        If strName.ToLower() = "weblive" Then
            ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.MovieLive)
        End If

        mStrCurrentSelectTreeNode = strName
        '刷机  固件下载
        Me.FlashFirmwareTreeSelectedChanged(strName)

        If Not Me.mDictSite.ContainsKey(strName) Then
            Return
        End If

        '' 使用WebKit
        'If Not Me._dictWebKitSites.ContainsKey(strName) Then
        '    Return
        'End If

        Dim wb As tbWebBrowser = Me.mDictSite(strName)

        '' 使用WebKit
        'Dim wb As WebKitBrowserEx = Me._dictWebKitSites(strName)

        'If Common.VerIs30 AndAlso Me.mShowDataGridView AndAlso _
        '   strName.Equals("WebMusicSearch", StringComparison.InvariantCultureIgnoreCase) Then
        '    Me.pnlSearch.BringToFront()
        'ElseIf strName = "MusicSearch" AndAlso Me.mShowDataGridView Then
        '    Me.pnlSearch.BringToFront()
        'Else
        Dim ctrl As tbTabPanel = Me.GetTabPage(strName)

        If ctrl IsNot Nothing Then
            ctrl.BringToFront()
        Else
            wb.BringToFront()
        End If

        If wb.Tag Is Nothing Then
            wb.Tag = strName

            If String.IsNullOrEmpty(Me.mJumpUrl) Then
                Dim strUrl As String = wb.Name
                If e.Node.Tag IsNot Nothing AndAlso TypeOf e.Node.Tag Is WebSiteObject Then
                    strUrl = Me.SetUrlPara(CType(e.Node.Tag, WebSiteObject).Url)

                    If wb.Name.EndsWith("&istip=1", StringComparison.InvariantCultureIgnoreCase) Then
                        strUrl = strUrl & "&istip=1"
                    End If
                End If

                If Me.mDevice IsNot Nothing AndAlso Not Me.GetJailbreaked() AndAlso _
                    strName.Equals("plugin", StringComparison.InvariantCultureIgnoreCase) AndAlso _
                    IniSetting.GetWebSiteJailbreakPluginShowTip Then

                    strUrl &= "&istip=1"
                    IniSetting.SetWebSiteJailbreakPluginShowTip()

                End If

                wb.Navigate(strUrl)
            Else
                Me.mJumpUrl = String.Empty
            End If
        Else
            Me.mJumpUrl = String.Empty
        End If
        'End If

        '隐藏正版商店节点tip，
        'If strName.Equals("appshare", StringComparison.InvariantCultureIgnoreCase) Then

        '    IniSetting.SetWebSiteAppShareShowTip()

        '    'If Me.mLoaded AndAlso Me.pnlAppTip.Visible Then
        '    '    Me.pnlAppTip.Visible = False
        '    'End If

        'End If

        'If strName.Equals("App", StringComparison.InvariantCultureIgnoreCase) OrElse _
        '   strName.Equals("AppShare", StringComparison.InvariantCultureIgnoreCase) Then
        '    If Me.mDictSite.ContainsKey(strName) Then
        '        Me.CheckLoginInfo(Me.mDictSite(strName))
        '    End If
        'End If

        '' 使用WebKit
        'If strName.Equals("App", StringComparison.InvariantCultureIgnoreCase) OrElse _
        '   strName.Equals("AppShare", StringComparison.InvariantCultureIgnoreCase) Then

        '    If Me._dictWebKitSites.ContainsKey(strName) Then
        '        Me.CheckLoginInfo(Me._dictWebKitSites(strName))
        '    End If

        'End If
        If Me.mSiteType = WebSiteType.Jailbreak AndAlso Me.tvwDevice.SelectedNode IsNot Nothing Then
            frmSite.mstrJailbreakSelected = Me.tvwDevice.SelectedNode.Name
        End If
    End Sub

    'Private Sub lblAppTip_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Me.pnlAppTip.Visible = False
    '    IniSetting.SetWebSiteAppShareShowTip()
    'End Sub

    Private Sub WebBrowser_NewMessage(ByVal sender As tbWebBrowser, ByVal message As String)
        Dim tabPage As tbTabPanel = Nothing
        Try

            If sender.Tag IsNot Nothing AndAlso TypeOf sender.Tag Is String Then
                Dim strTag As String = sender.Tag.ToString()

                If Me.mDictPanel.ContainsKey(strTag) Then
                    tabPage = Me.mDictPanel(strTag)
                End If

            End If
        Catch ex As Exception
        End Try
        WebSiteHelper.Instance.WebBrowserNewMessage(Me.mDevice, sender, message, tabPage)
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Try
            RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
            RemoveHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 动态加载页面 ---"

    'Private Sub CreateNodePlist()
    '    Try
    '        Dim dictPlist As New List(Of Object)

    '        Dim dict As New Dictionary(Of Object, Object)
    '        dict.Add("name", "App")
    '        dict.Add("text", "越狱商店")
    '        dict.Add("url", "http://v2.tongbu.com/app/?ver=#ver#&lang=#lang#&deviceid=#deviceidforapp#&devicetype=#devicetype#")
    '        dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/AppJail.png")
    '        dict.Add("showtab", "False")
    '        dict.Add("iconname", "AppJail.png")
    '        dictPlist.Add(dict)

    '        dict = New Dictionary(Of Object, Object)
    '        dict.Add("name", "AppShare")
    '        dict.Add("text", "不越狱装正版")
    '        dict.Add("url", "http://v2.tongbu.com/appshare?ver=#ver#&lang=#lang#&deviceid=#deviceidforapp#&devicetype=#devicetype#")
    '        dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/App.png")
    '        dict.Add("showtab", "False")
    '        dict.Add("iconname", "App.png")
    '        dictPlist.Add(dict)

    '        'dict = New Dictionary(Of Object, Object)
    '        'dict.Add("name", "App")
    '        'dict.Add("text", "同步软件")
    '        'dict.Add("url", "http://v2.tongbu.com/app?ver=#ver#&lang=#lang#&deviceid=#deviceidforapp#&devicetype=#devicetype#")
    '        'dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/App.png")
    '        'dict.Add("showtab", "False")
    '        'dict.Add("iconname", "App.png")
    '        'dictPlist.Add(dict)

    '        dict = New Dictionary(Of Object, Object)
    '        dict.Add("name", "Ringtone")
    '        dict.Add("text", "同步铃声")
    '        dict.Add("url", "http://tm.tongbu.com/ring/v2?ver=#ver#&lang=#lang#")
    '        dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/Ringtone.png")
    '        dict.Add("showtab", "False")
    '        dict.Add("iconname", "Ringtone.png")
    '        dictPlist.Add(dict)

    '        dict = New Dictionary(Of Object, Object)
    '        dict.Add("name", "Wallpaper")
    '        dict.Add("text", "同步壁纸")
    '        dict.Add("url", "http://v2.tongbu.com/wallpaper?ver=#ver#&lang=#lang#&deviceid=#deviceid#")
    '        dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/Wallpaper.png")
    '        dict.Add("showtab", "False")
    '        dict.Add("iconname", "Wallpaper.png")
    '        dictPlist.Add(dict)

    '        dict = New Dictionary(Of Object, Object)
    '        dict.Add("name", "MusicSearch")
    '        dict.Add("text", "音乐搜索")
    '        dict.Add("url", "http://tm.tongbu.com/music/v2/search/index.html?ver=#ver#&lang=#lang#")
    '        dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/MusicSearch.png")
    '        dict.Add("showtab", "False")
    '        dict.Add("iconname", "MusicSearch.png")
    '        dictPlist.Add(dict)

    '        'dict = New Dictionary(Of Object, Object)
    '        'dict.Add("name", "MusicJuJing")
    '        'dict.Add("text", "巨鲸音乐")
    '        'dict.Add("url", "http://hz.top100.cn/tongbu/?sid=1023")
    '        'dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/MusicJuJing.png")
    '        'dict.Add("showtab", "True")
    '        'dict.Add("iconname", "MusicJuJing.png")
    '        'dictPlist.Add(dict)

    '        dict = New Dictionary(Of Object, Object)
    '        dict.Add("name", "CoolShowWW")
    '        dict.Add("text", "酷音铃声")
    '        dict.Add("url", "http://tongbuapp.kuyinxiu.com")
    '        dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/KuYinXiu.png")
    '        dict.Add("showtab", "True")
    '        dict.Add("iconname", "KuYinXiu.png")
    '        dictPlist.Add(dict)

    '        dict = New Dictionary(Of Object, Object)
    '        dict.Add("name", "Duomi")
    '        dict.Add("text", "多米音乐")
    '        dict.Add("url", "http://player.pc.duomi.com/player/index.shtml?m=tb&amp;os=iOS")
    '        dict.Add("iconurl", "http://t.tongbu.com/images/tbzs20/Duomi.png")
    '        dict.Add("showtab", "True")
    '        dict.Add("iconname", "Duomi.png")
    '        dictPlist.Add(dict)

    '        Dim dictlang As New Dictionary(Of Object, Object)
    '        dictlang.Add("zh-CN", dictPlist)

    '        iTong.Device.CoreFoundation.WritePlist(dictlang, Path.Combine(Me.mSiteFolder, "site.plist"))
    '        Debug.Print(iTong.Device.CoreFoundation.CreatePlistString(dictlang))
    '    Catch ex As Exception
    '        Debug.Print(ex.ToString())
    '    End Try
    'End Sub

    'Private Sub CreateNodeXML()
    '    Dim doc As New System.Xml.XmlDocument()
    '    Dim site As Xml.XmlElement = doc.CreateElement("Site")

    '    Dim webGroup As System.Xml.XmlElement = frmSite.CreateGroupForXML(doc, "AppGroup", "游戏/应用")
    '    webGroup.AppendChild(frmSite.CreateNodeForXML(doc, "App", "越狱商店", "http://v2.tongbu.com/app/?ver=#ver#&lang=#lang#&deviceid=#deviceidforapp#&devicetype=#devicetype#", "http://t.tongbu.com/images/tbzs20/AppJail.png", "False", "AppJail.png"))
    '    webGroup.AppendChild(frmSite.CreateNodeForXML(doc, "AppShare", "正版商店", "http://v2.tongbu.com/appshare?ver=#ver#&lang=#lang#&deviceid=#deviceidforapp#&devicetype=#devicetype#", "http://t.tongbu.com/images/tbzs20/App.png", "False", "App.png"))
    '    site.AppendChild(webGroup)

    '    webGroup = frmSite.CreateGroupForXML(doc, "MusicGroup", "音乐/铃声")
    '    webGroup.AppendChild(frmSite.CreateNodeForXML(doc, "Ringtone", "同步铃声", "http://tm.tongbu.com/ring/v2?ver=#ver#&lang=#lang#", "http://t.tongbu.com/images/tbzs20/Ringtone.png", "False", "Ringtone.png"))
    '    webGroup.AppendChild(frmSite.CreateNodeForXML(doc, "MusicSearch", "音乐搜索", "http://tm.tongbu.com/music/v2/search/index.html?ver=#ver#&lang=#lang#", "http://t.tongbu.com/images/tbzs20/MusicSearch.png", "False", "MusicSearch.png"))
    '    webGroup.AppendChild(frmSite.CreateNodeForXML(doc, "CoolShowRing", "酷音铃声", "http://tongbuapp.kuyinxiu.com", "http://t.tongbu.com/images/tbzs20/KuYinXiu.png", "True", "KuYinXiu.png"))
    '    webGroup.AppendChild(frmSite.CreateNodeForXML(doc, "Duomi", "多米音乐", "http://player.pc.duomi.com/player/index.shtml?m=tb&amp;os=iOS", "http://t.tongbu.com/images/tbzs20/Duomi.png", "True", "Duomi.png"))
    '    site.AppendChild(webGroup)

    '    webGroup = frmSite.CreateGroupForXML(doc, "WallpaperGroup", "壁纸")
    '    webGroup.AppendChild(frmSite.CreateNodeForXML(doc, "Wallpaper", "同步壁纸", "http://v2.tongbu.com/wallpaper?ver=#ver#&lang=#lang#&deviceid=#deviceid#", "http://t.tongbu.com/images/tbzs20/Wallpaper.png", "False", "Wallpaper.png"))
    '    site.AppendChild(webGroup)

    '    doc.AppendChild(site)
    '    doc.Save(System.IO.Path.Combine("C:\Documents and Settings\zhoudaohui\桌面\Lang", "site.xml"))
    'End Sub

    'Public Shared Function CreateGroupForXML(ByVal doc As System.Xml.XmlDocument, ByVal strName As String, ByVal strText As String)
    '    Dim webGroup As System.Xml.XmlElement = doc.CreateElement(strName)
    '    webGroup.SetAttribute("Text", strText)
    '    Return webGroup
    'End Function

    'Public Shared Function CreateNodeForXML(ByVal doc As System.Xml.XmlDocument, ByVal strName As String, ByVal strText As String, ByVal strURL As String, ByVal strIconUrl As String, ByVal strShowTab As String, ByVal strIconName As String)
    '    Dim webnode As System.Xml.XmlElement = doc.CreateElement("node")
    '    Dim nodeName As System.Xml.XmlElement = doc.CreateElement("name")
    '    Dim nodeNameValue As System.Xml.XmlText = doc.CreateTextNode(strName)
    '    nodeName.AppendChild(nodeNameValue)
    '    webnode.AppendChild(nodeName)

    '    Dim nodeText As System.Xml.XmlElement = doc.CreateElement("text")
    '    Dim nodeTextValue As System.Xml.XmlText = doc.CreateTextNode(strText)
    '    nodeText.AppendChild(nodeTextValue)
    '    webnode.AppendChild(nodeText)

    '    Dim nodeURL As System.Xml.XmlElement = doc.CreateElement("url")
    '    Dim nodeURLValue As System.Xml.XmlText = doc.CreateTextNode(strURL)
    '    nodeURL.AppendChild(nodeURLValue)
    '    webnode.AppendChild(nodeURL)

    '    Dim nodeIconURL As System.Xml.XmlElement = doc.CreateElement("iconurl")
    '    Dim nodeIconValue As System.Xml.XmlText = doc.CreateTextNode(strIconUrl)
    '    nodeIconURL.AppendChild(nodeIconValue)
    '    webnode.AppendChild(nodeIconURL)

    '    Dim nodeShowTab As System.Xml.XmlElement = doc.CreateElement("showtab")
    '    Dim nodeShowTabValue As System.Xml.XmlText = doc.CreateTextNode(strShowTab)
    '    nodeShowTab.AppendChild(nodeShowTabValue)
    '    webnode.AppendChild(nodeShowTab)

    '    Dim nodeIconName As System.Xml.XmlElement = doc.CreateElement("iconname")
    '    Dim nodeIconNameValue As System.Xml.XmlText = doc.CreateTextNode(strIconName)
    '    nodeIconName.AppendChild(nodeIconNameValue)
    '    webnode.AppendChild(nodeIconName)

    '    Return webnode
    'End Function

    'Private Sub DownloadNodeIconAndPlist()
    '    For Each Item As tbTreeNode In Me.mNodeSite.ChildNodes()
    '        If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is WebSiteObject Then
    '            Dim strImagePath As String = Path.Combine(Me.mSiteFolder, CType(Item.Tag, WebSiteObject).IconName)
    '            If Not File.Exists(strImagePath) Then
    '                Dim img As Image = Common.DownloadImage(CType(Item.Tag, WebSiteObject).IconUrl)
    '                If img IsNot Nothing Then
    '                    img.Save(strImagePath, System.Drawing.Imaging.ImageFormat.Png)
    '                    Me.SetNodeIcon(Item, img)
    '                End If
    '            End If
    '        End If
    '    Next

    '    '下载最新的plist文件    ''"http://t.tongbu.com/tbzs20/site.aspx"
    '    Common.HttpDownload(WebUrl.ResourceSitePlist, Path.Combine(Me.mSiteFolder, "site.plist"), 10000)
    '    Me.ReInitNode()
    'End Sub

    'Private Sub DownloadNodeIconAndXMLPool(ByVal state As Object)
    '    DownloadNodeIconAndXML()
    'End Sub

    'Private Sub DownloadNodeIconAndXML()
    '    Try
    '        '下载最新的plist文件    ''"http://t.tongbu.com/tbzs20/sitexml.aspx"         
    '        Common.HttpDownload(SiteXmlHelper.SiteUrl, SiteXmlHelper.SiteFilePath, 10000)

    '        Me.ReInitNode()

    '        '更新节点图标
    '        For Each ItemGroup As tbTreeNode In Me.tvwDevice.Nodes()

    '            For Each Item As tbTreeNode In ItemGroup.ChildNodes
    '                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is WebSiteObject Then
    '                    Dim objsite As WebSiteObject = CType(Item.Tag, WebSiteObject)

    '                    Dim strImagePath As String = Path.Combine(Me.mSiteFolder, objsite.IconName)

    '                    If Not File.Exists(strImagePath) Then
    '                        Dim img As Image = Common.DownloadImage(objsite.IconUrl)

    '                        If img IsNot Nothing Then
    '                            img.Save(strImagePath, System.Drawing.Imaging.ImageFormat.Png)
    '                            Me.SetNodeIcon(Item, img)
    '                        End If

    '                    End If
    '                End If
    '            Next

    '        Next

    '        If Not Common.VerIs30 Then
    '            Me.SetDefaultNodeSelected()
    '        Else
    '            Me.ReInitNodeV3()
    '        End If
    '    Catch ex As Exception
    '    End Try
    'End Sub

    'Private Delegate Sub ReInitNodeV3Handler()
    'Private Sub ReInitNodeV3()
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New ReInitNodeV3Handler(AddressOf ReInitNodeV3))
    '    Else
    '        For Each Item As tbTreeNode In mlstNode
    '            frmSite.CreateSiteNode(Item, Item.Tag)
    '        Next
    '    End If
    'End Sub

    'Private Sub DownloadNodeIconAndXMLV3()
    '    Try
    '        '下载最新的plist文件    ''"http://t.tongbu.com/tbzs20/sitexml.aspx"
    '        Common.HttpDownload(WebUrl.ResourceSitePlist, Path.Combine(Me.mSiteFolder, "site.xml"), 10000)
    '        'frmSite.Loaded = True
    '        Me.ReInitNodeV3()
    '    Catch ex As Exception
    '    End Try
    'End Sub

    'plist下载完后再重新验证一下节点，加载最新的节点。
    Private Delegate Sub ReInitNodeHandler()
    'Private Sub ReInitNode()
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New ReInitNodeHandler(AddressOf ReInitNode))
    '    Else
    '        Dim lstInsertNodes As New List(Of tbTreeNode)
    '        Dim lstDeleteNodes As New List(Of tbTreeNode)
    '        Dim lstTotalNodes As New List(Of tbTreeNode)
    '        Dim strNodePlist As String = Path.Combine(Me.mSiteFolder, "Site.plist")
    '        Try
    '            If File.Exists(strNodePlist) Then
    '                '找出要插入的节点
    '                For Each Item As WebSiteObject In GetWebSiteObjectFromPlist()
    '                    If Me.mNodeSite.ChildNodes(Item.Name) Is Nothing Then
    '                        Dim node As New tbTreeNode(Item.Name, Item.Text)
    '                        node.NodeIcon = Me.GetNodeIcon(Item.IconName)
    '                        node.NodeIconState = ImageState.TwoState
    '                        node.Tag = Item  '把网页信息存在tag里面。

    '                        Dim url As String = Me.SetUrlPara(Item.Url)
    '                        If Item.ShowTab Then
    '                            Dim tab As tbTabPanel = Me.CreateTabPage(Item.Name)
    '                            Me.pnlContainer.Controls.Add(tab)
    '                            Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(url, True, tab)
    '                            tab.Pages.Add(New tbTabPanelPage(url, Item.Text, webbrow))
    '                            Me.mDictSite.Add(Item.Name, webbrow)
    '                            Me.mDictPanel.Add(Item.Name, tab)
    '                        Else
    '                            Me.mDictSite.Add(Item.Name, Me.CreateWebSite(url, Item.ShowTab))
    '                        End If
    '                        lstInsertNodes.Add(node)
    '                        lstTotalNodes.Add(node)
    '                    Else
    '                        lstTotalNodes.Add(Me.mNodeSite.ChildNodes(Item.Name))
    '                    End If
    '                Next
    '                '找出要删除的节点
    '                For Each Item As tbTreeNode In Me.mNodeSite.ChildNodes
    '                    If Not Me.CheckDeleteNode(Item.Name, lstTotalNodes) Then
    '                        lstDeleteNodes.Add(Item)
    '                    End If
    '                Next

    '                '删除节点
    '                For Each Item As tbTreeNode In lstDeleteNodes
    '                    Me.mNodeSite.ChildNodes.Remove(Item)
    '                Next

    '                '如果有添加节点就重新删除后再添加
    '                If lstInsertNodes.Count > 0 Then
    '                    For Each Item As tbTreeNode In lstInsertNodes
    '                        Me.mNodeSite.ChildNodes.Add(Item)
    '                    Next
    '                End If

    '                '如果没有选中的节点就选中第一个
    '                If Me.tvwDevice.SelectedNode Is Nothing AndAlso Me.mNodeSite.ChildNodes.Count > 0 Then
    '                    Me.tvwDevice.SelectedNode = Me.mNodeSite.ChildNodes(0)
    '                End If

    '                lstInsertNodes.Clear()
    '                lstDeleteNodes.Clear()
    '                lstTotalNodes.Clear()
    '                lstInsertNodes = Nothing
    '                lstDeleteNodes = Nothing
    '                lstTotalNodes = Nothing
    '            End If
    '        Catch ex As Exception
    '            Common.Log("ReInitNodeError" & ex.ToString())
    '        End Try
    '    End If
    'End Sub
    Private Sub ReInitNode()
        If Me.InvokeRequired Then
            Me.Invoke(New ReInitNodeHandler(AddressOf ReInitNode))
        Else
            If Not File.Exists(SiteXmlHelper.SiteFilePath) Then
                Return
            End If

            Try
                '网络不通的时候可以正常加载出来
                Dim doc As System.Xml.XmlDocument = Common.XmlLoader(SiteXmlHelper.SiteFilePath)

                'Dim doc As New System.Xml.XmlDocument()
                'doc.Load(SiteXmlHelper.SiteFilePath)

                If doc IsNot Nothing AndAlso doc.ChildNodes.Count > 0 Then
                    '取得site节点
                    Dim site As Xml.XmlElement = doc.ChildNodes(0)

                    Me.ReInitGroup(site)
                    Me.ReInitSite(site)
                End If
                Me.SetDeviceListToBrowser()
            Catch ex As Exception
                Common.LogException(ex.ToString, "ReInitNode")
            End Try
        End If
    End Sub

    'Private Sub ReInitNodeV3()
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New ReInitNodeHandler(AddressOf ReInitNodeV3))
    '    Else
    '        Try
    '            Dim doc As New System.Xml.XmlDocument()
    '            Dim strPath As String = Path.Combine(Me.mSiteFolder, "site.xml")
    '            doc.Load(strPath)
    '            If doc IsNot Nothing AndAlso doc.ChildNodes.Count > 0 Then
    '                '取得site节点
    '                Dim site As Xml.XmlElement = doc.ChildNodes(0)
    '                Me.ReInitSiteV3(site)
    '            End If
    '        Catch ex As Exception
    '        End Try
    '    End If
    'End Sub

    Private Sub ReInitGroup(ByVal site As Xml.XmlElement)
        Dim listNodeTotal As New List(Of String)

        '添加不存在的Group，修改文字改变的Group
        Dim indexGroup As Integer = 0
        For Each itemGroup As Xml.XmlElement In site.ChildNodes
            Dim groupName As String = itemGroup.Name

            listNodeTotal.Add(groupName)

            If Me.tvwDevice.Nodes(groupName) IsNot Nothing Then
                If Not Me.tvwDevice.Nodes(groupName).Text.Equals(itemGroup.Attributes("Text").Value) Then
                    Me.tvwDevice.Nodes(groupName).Text = itemGroup.Attributes("Text").Value
                End If
            Else
                Me.tvwDevice.Nodes.Add(groupName, itemGroup.Attributes("Text").Value, True)
            End If

            '对node排序
            Dim nodeGroup As tbTreeNode = Me.tvwDevice.Nodes(groupName)
            If nodeGroup IsNot Nothing AndAlso Not nodeGroup Is Me.tvwDevice.Nodes(indexGroup) Then
                Me.tvwDevice.Nodes.Remove(nodeGroup)
                Me.tvwDevice.Nodes.Add(indexGroup, nodeGroup)
            End If
            indexGroup += 1
        Next

        Dim lstDeleteNode As New List(Of String)
        '去掉删除掉的Group
        For Each Item As tbTreeNode In Me.tvwDevice.Nodes
            If Not listNodeTotal.Contains(Item.Name) Then
                lstDeleteNode.Add(Item.Name)
            End If
        Next

        For Each Item As String In lstDeleteNode
            Me.tvwDevice.Nodes.Remove(Me.tvwDevice.Nodes(Item))
        Next

        listNodeTotal.Clear()
        listNodeTotal = Nothing
        lstDeleteNode.Clear()
        lstDeleteNode = Nothing
    End Sub

    Private Function GetNodeByObj(ByVal nodeGroup As tbTreeNode, ByVal objSite As WebSiteObject) As tbTreeNode
        Dim node As tbTreeNode = Nothing
        For Each Item As tbTreeNode In nodeGroup.ChildNodes
            If Item.Tag IsNot Nothing AndAlso _
               TypeOf Item.Tag Is WebSiteObject AndAlso _
               CType(Item.Tag, WebSiteObject).TabGroup = objSite.TabGroup Then
                node = Item
                Exit For
            End If
        Next
        Return node
    End Function

    Private Sub ReInitSite(ByVal site As Xml.XmlElement)
        ''' Added by Utmost20140616
        Dim a As String = ""
        Dim dt As Date = Now
        For Each itemGroup As Xml.XmlElement In site.ChildNodes

            Dim nodeGroup As tbTreeNode = Me.tvwDevice.Nodes(itemGroup.Name)

            If nodeGroup IsNot Nothing Then
                Dim node As tbTreeNode = Nothing

                For Each itemNode As Xml.XmlElement In itemGroup.ChildNodes
                    Dim objSite As New WebSiteObject(itemNode)
                    Me.SetPandaURL(objSite)
                    Me.AddWebSiteObject2TreeView(nodeGroup, objSite, node)
                Next

                ''3.0站点添加广告节点，从服务器上取得要显示的广告节点的名称和URL, 先去掉这个功能
                'Me.AddAdvertisingNode(nodeGroup)
            End If
        Next
        Debug.Print("CreateWebFormAA " & Now.Subtract(dt).TotalMilliseconds)
        Dim thr As New Thread(AddressOf GetNodeIcon)
        With thr
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub SetPandaURL(ByVal objSite As WebSiteObject)
        If Not Utility.IsPanda Then
            Return
        End If

        '熊猫助手去掉同步标识给web的参数
        '另外一个参数是浏览器属性Clinet=panda
        If objSite.Name = "AppGame" OrElse objSite.Name = "App" OrElse objSite.Name = "WebMusicSearch" Then
            objSite.Url = objSite.Url & "&client=panda"
        End If
    End Sub

    'Private Sub AddAdvertisingNode(ByVal nodeGroup As tbTreeNode)
    '    '3.0站点添加广告节点，从服务器上取得要显示的广告节点的名称和URL
    '    If Common.VerIs30 AndAlso String.Equals(nodeGroup.Name, "AppGroup", StringComparison.InvariantCultureIgnoreCase) Then
    '        Dim type As PopMsgDeviceType = PopMsgDeviceType.All
    '        If Me.mDevice IsNot Nothing Then
    '            type = PopMsgDeviceType.iOS
    '        ElseIf Me.mAndroid IsNot Nothing Then
    '            type = PopMsgDeviceType.Android
    '        End If

    '        Dim strAdvertising As String = PopMsgHelper.Instance.GetPopMsgAdvertising(type)

    '        If String.IsNullOrEmpty(strAdvertising) Then
    '            Return
    '        End If
    '        Dim strArr() As String = strAdvertising.Replace("@@", Chr(1)).Split(Chr(1))
    '        If strArr.Length <> 2 Then
    '            Return
    '        End If

    '        Dim objSite As New WebSiteObject()
    '        objSite.WebType = WebSiteType.App
    '        objSite.Url = strArr(1)
    '        objSite.ShowTab = False
    '        objSite.Name = "AppAdvertising"
    '        objSite.Text = strArr(0)
    '        Dim node As tbTreeNode = Nothing
    '        Me.AddSiteNode(nodeGroup, objSite, node)
    '    End If

    'End Sub

    ''' Added by Utmost20140616
    Private Sub AddWebSiteObject2TreeView(ByVal nodeGroup As tbTreeNode, _
                                          ByVal objSite As WebSiteObject, _
                                          ByRef nodeMusic As tbTreeNode)
        Dim node As tbTreeNode = Nothing

        If Not String.IsNullOrEmpty(objSite.TabGroup) Then
            '取得节点
            node = Me.GetNodeByObj(nodeGroup, objSite)
            Dim tab As tbTabPanel = Nothing
            If node IsNot Nothing AndAlso Me.mDictPanel.ContainsKey(node.Tag.Name) Then
                tab = Me.mDictPanel(node.Tag.Name)
            End If

            Me.UpdateTabGroupPage(nodeGroup, node, tab, objSite)
            'ElseIf Common.VerIs30 AndAlso _
            '       String.Equals(nodeGroup.Name, "musicgroup", StringComparison.InvariantCultureIgnoreCase) AndAlso _
            '       Not String.Equals(objSite.Name, "webringtone", StringComparison.InvariantCultureIgnoreCase) Then

            '    Me.UpdateTabGroupPage(nodeGroup, nodeMusic, Me.mMusicTab, objSite)

        Else
            Me.AddSiteNode(nodeGroup, objSite, node)
        End If
    End Sub

    Private Sub GetNodeIcon()
        Try
            For Each Item As tbTreeNode In Me.tvwDevice.Nodes
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is WebSiteObject Then
                    If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is WebSiteObject Then
                        Me.DownloadNodeIcon(Item)
                    End If
                Else
                    For Each Item1 As tbTreeNode In Item.ChildNodes
                        If Item1.Tag IsNot Nothing AndAlso TypeOf Item1.Tag Is WebSiteObject Then
                            Me.DownloadNodeIcon(Item1)
                        End If
                    Next
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSite_GetNodeIcon")
        End Try
    End Sub

    Private Sub DownloadNodeIcon(ByRef node As tbTreeNode)
        Try
            Dim obj As WebSiteObject = node.Tag
            Dim strImgPath As String = Path.Combine(Me.mSiteFolder, obj.IconName)
            If Not File.Exists(strImgPath) AndAlso Not String.IsNullOrEmpty(obj.IconUrl) Then
                Common.DownloadImage(obj.IconUrl, 2000, strImgPath)

            End If
            If File.Exists(strImgPath) Then
                Me.SetNodeIcon(node, Me.GetNodeIcon(obj.IconName))
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSite_DownloadNodeIcon")
        End Try
    End Sub

    Private Sub AddSiteNode(ByVal nodeGroup As tbTreeNode, ByVal objSite As WebSiteObject, ByRef node As tbTreeNode)
        '只添加站点的逻辑－－－－－－－－－－－－－－－－－－－－－－－－－－
        If nodeGroup.ChildNodes(objSite.Name) Is Nothing Then
            node = nodeGroup.ChildNodes.Add(objSite.Name, objSite.Text)
            node.NodeIcon = Me.GetNodeIcon(objSite.IconName)
            node.NodeIconState = ImageState.TwoState
            node.Tag = objSite  '把网页信息存在tag里面。

            Dim urlStr As String = Me.SetUrlPara(objSite.Url)

            If objSite.ShowTab Then
                Dim tab As tbTabPanel = Me.CreateTabPage(objSite.Name)
                Me.pnlContainer.Controls.Add(tab)

                Dim browser As tbWebBrowserEx = Me.CreateWebSite(urlStr, True, tab)
                'Dim browser As WebKitBrowserEx = Me.CreateWebKitSite(urlStr, True, tab)

                If objSite.Tabs IsNot Nothing AndAlso objSite.Tabs.Count > 0 Then
                    browser.ScrollBarsEnabled = False

                    For Each Item As KeyValuePair(Of String, String) In objSite.Tabs
                        Dim pages As New tbTabPanelPage(urlStr, Item.Value, browser)
                        pages.PageName = Item.Key
                        tab.Pages.Add(pages)
                    Next
                    'android的站点可以不显示 iPhone/ipad
                    If objSite.Name.Contains("Android") Then
                        tab.tbBarShowDeviceButtons = False
                    Else
                        tab.tbBarShowDeviceButtons = True
                    End If

                    tab.tbBarShowWebHomeButton = False

                    AddHandler tab.SelectIndexChanged, AddressOf tab_SelectIndexChanged
                    AddHandler tab.DeviceButtonStatusChanged, AddressOf tab_DeviceButtonStatusChanged
                Else
                    tab.Pages.Add(New tbTabPanelPage(urlStr, objSite.Text, browser))
                End If

                If Not Me.mDictSite.ContainsKey(objSite.Name) Then
                    Me.mDictSite.Add(objSite.Name, browser)
                    Me.mDictPanel.Add(objSite.Name, tab)
                End If

                '' 使用WebKit
                'If Not Me._dictWebKitSites.ContainsKey(objSite.Name) Then
                '    Me._dictWebKitSites.Add(objSite.Name, browser)
                '    Me.mDictPanel.Add(objSite.Name, tab)
                'End If

            Else
                If Not Me.mDictSite.ContainsKey(objSite.Name) Then
                    Me.mDictSite.Add(objSite.Name, Me.CreateWebSite(urlStr, objSite.ShowTab))
                End If

                '' 使用WebKit
                'If Not Me._dictWebKitSites.ContainsKey(objSite.Name) Then
                '    Me._dictWebKitSites.Add(objSite.Name, Me.CreateWebKitSite(urlStr, objSite.ShowTab))
                'End If
            End If
        Else
            If nodeGroup.ChildNodes(objSite.Name).Text <> objSite.Text Then
                nodeGroup.ChildNodes(objSite.Name).Text = objSite.Text
            End If
            node = nodeGroup.ChildNodes(objSite.Name)
            node.Tag = objSite
        End If
        '只添加站点的逻辑－－－－－－－－－－－－－－－－－－－－－－－－－－
    End Sub

    'Private Sub ReInitSiteV3(ByVal site As Xml.XmlElement)
    '    Dim listNodeTotal As New List(Of tbTreeNode)
    '    For Each itemGroup As Xml.XmlElement In site.ChildNodes
    '        For Each itemNode As Xml.XmlElement In itemGroup.ChildNodes
    '            Dim objSite As New WebSiteObject(itemNode)
    '            If Me.mSiteType <> WebSiteType.Normal AndAlso Me.mSiteType = objSite.WebType Then
    '                Dim node As tbTreeNode = Me.tvwDevice.Nodes(objSite.Name)
    '                If node Is Nothing Then
    '                    node = Me.tvwDevice.Nodes.Add(objSite.Name, objSite.Text)
    '                    node.NodeIcon = Me.GetNodeIcon(objSite.IconName)
    '                    node.NodeIconState = ImageState.TwoState
    '                    node.Tag = objSite  '把网页信息存在tag里面。

    '                    Dim url As String = Me.SetUrlPara(objSite.Url)
    '                    If objSite.ShowTab Then
    '                        Dim tab As tbTabPanel = Me.CreateTabPage(objSite.Name)
    '                        Me.pnlContainer.Controls.Add(tab)
    '                        Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(url, True, tab)
    '                        tab.Pages.Add(New tbTabPanelPage(url, objSite.Text, webbrow))

    '                        If Not Me.mDictSite.ContainsKey(objSite.Name) Then
    '                            Me.mDictSite.Add(objSite.Name, webbrow)
    '                            Me.mDictPanel.Add(objSite.Name, tab)
    '                        End If
    '                    Else
    '                        If Not Me.mDictSite.ContainsKey(objSite.Name) Then
    '                            Me.mDictSite.Add(objSite.Name, Me.CreateWebSite(url, objSite.ShowTab))
    '                        End If
    '                    End If
    '                End If
    '                listNodeTotal.Add(node)
    '            End If
    '        Next
    '    Next

    '    ' 排序
    '    Dim index As Integer = 0
    '    For Each Item As tbTreeNode In listNodeTotal
    '        Me.tvwDevice.Nodes.Remove(Item)
    '        Me.tvwDevice.Nodes.Add(index, Item)
    '        index += 1
    '    Next

    '    '删除不存在的节点
    '    Dim lstDeleteNode As New List(Of tbTreeNode)
    '    For Each Item As tbTreeNode In Me.tvwDevice.Nodes
    '        If listNodeTotal.Contains(Item) Then
    '            Continue For
    '        Else
    '            lstDeleteNode.Add(Item)
    '        End If
    '    Next

    '    For Each Item As tbTreeNode In lstDeleteNode
    '        Me.tvwDevice.Nodes.Remove(Item)
    '        Item = Nothing
    '    Next

    '    listNodeTotal.Clear()
    '    listNodeTotal = Nothing
    '    lstDeleteNode.Clear()
    '    lstDeleteNode = Nothing

    '    Me.tvwDevice.SelectedNode = Me.tvwDevice.Nodes(0)
    'End Sub

    'Private Function CheckDeleteNode(ByVal strNodeName As String, ByVal listNodes As List(Of tbTreeNode)) As Boolean
    '    Dim nodeExist As Boolean = False
    '    For Each Item As tbTreeNode In listNodes
    '        If Item.Name.Equals(strNodeName, StringComparison.InvariantCultureIgnoreCase) Then
    '            nodeExist = True
    '        End If
    '    Next
    '    Return nodeExist
    'End Function

    Private Delegate Sub SetNodeIconhandler(ByVal node As tbTreeNode, ByVal img As Image)
    Private Sub SetNodeIcon(ByVal node As tbTreeNode, ByVal img As Image)
        If Me.InvokeRequired Then
            Me.Invoke(New SetNodeIconhandler(AddressOf SetNodeIcon), node, img)
        Else
            node.NodeIcon = img
            Application.DoEvents()
        End If
    End Sub

    ''从本地设置里面取得网站节点
    'Private Function InitWebSiteFromSetting() As Boolean
    '    Dim blnReturn As Boolean = False
    '    Dim strNodePlist As String = Path.Combine(Me.mSiteFolder, "Site.plist")

    '    Try
    '        If File.Exists(strNodePlist) Then
    '            With Me.mNodeSite
    '                For Each Item As WebSiteObject In GetWebSiteObjectFromPlist()

    '                    blnReturn = True
    '                    Dim node As tbTreeNode = Nothing
    '                    node = .ChildNodes.Add(Item.Name, Item.Text)
    '                    node.NodeIcon = Me.GetNodeIcon(Item.IconName)
    '                    node.NodeIconState = ImageState.TwoState
    '                    node.Tag = Item  '把网页信息存在tag里面。

    '                    Dim url As String = Me.SetUrlPara(Item.Url)
    '                    If Item.ShowTab Then
    '                        Dim tab As tbTabPanel = Me.CreateTabPage(Item.Name)
    '                        Me.pnlContainer.Controls.Add(tab)
    '                        Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(url, True, tab)
    '                        tab.Pages.Add(New tbTabPanelPage(url, Item.Text, webbrow))
    '                        Me.mDictSite.Add(Item.Name, webbrow)
    '                        Me.mDictPanel.Add(Item.Name, tab)
    '                    Else
    '                        Me.mDictSite.Add(Item.Name, Me.CreateWebSite(url, Item.ShowTab))
    '                    End If
    '                Next
    '            End With

    '            Me.AddKuYinTest()
    '        End If
    '    Catch ex As Exception
    '        blnReturn = False
    '        Common.Log("InitNodeError" & ex.ToString)
    '    End Try

    '    Return blnReturn
    'End Function

    'Private Sub AddKuYinTest()
    '    With Me.mNodeSite
    '        If IniSetting.GetShowKuYin Then
    '            Dim lstSiteObj As New List(Of WebSiteObject)
    '            Dim wwObj As New WebSiteObject("CoolShowWW", "个性手机铃声", "http://tongbuapp.kuyinxiu.com", "http://t.tongbu.com/images/tbzs20/KuYinXiu.png", True, "KuYinXiu.png", WebSiteType.Music)
    '            'Dim wwObj As New WebSiteObject("CoolShowWW", "酷音秀秀外网", "http://*************:9004/tongbuapp", "http://t.tongbu.com/images/tbzs20/MusicJuJing.png", True, "MusicJuJing.png")
    '            'Dim kfObj As New WebSiteObject("CoolShowKF", "酷音秀秀开发", "http://localhost:45636/", "http://t.tongbu.com/images/tbzs20/MusicJuJing.png", True, "MusicJuJing.png")
    '            'Dim kcsObj As New WebSiteObject("CoolShowCS", "酷音秀秀测试", "http://*************:9000/tongbuapp", "http://t.tongbu.com/images/tbzs20/MusicJuJing.png", True, "MusicJuJing.png")
    '            lstSiteObj.Add(wwObj)
    '            'lstSiteObj.Add(kfObj)
    '            'lstSiteObj.Add(kcsObj)
    '            For Each Item As WebSiteObject In lstSiteObj
    '                Dim node As tbTreeNode = Nothing
    '                node = .ChildNodes.Add(Item.Name, Item.Text)
    '                node.NodeIcon = Me.GetNodeIcon(Item.IconName)
    '                node.NodeIconState = ImageState.TwoState
    '                node.Tag = Item  '把网页信息存在tag里面。

    '                Dim url As String = Me.SetUrlPara(Item.Url)
    '                If Item.ShowTab Then
    '                    Dim tab As tbTabPanel = Me.CreateTabPage(Item.Name)
    '                    Me.pnlContainer.Controls.Add(tab)
    '                    Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(url, True, tab)
    '                    tab.Pages.Add(New tbTabPanelPage(url, Item.Text, webbrow))
    '                    Me.mDictSite.Add(Item.Name, webbrow)
    '                    Me.mDictPanel.Add(Item.Name, tab)
    '                Else
    '                    Me.mDictSite.Add(Item.Name, Me.CreateWebSite(url, Item.ShowTab))
    '                End If
    '            Next
    '        End If
    '    End With
    'End Sub

    'Public Shared Function GetWebSiteObjectFromPlist() As List(Of WebSiteObject)
    '    Dim strLang As String = "zh-CN"
    '    If Not Common.RunInChina Then
    '        strLang = LanguageInterface.Instance.CurrentLanguage.LangName
    '    End If
    '    Dim strPlistPath As String = Path.Combine(Folder.CacheFolder, "WebSite\Site.plist")
    '    '测试用的plist
    '    Dim strConfig As String = Path.Combine(Folder.AppFolder, "TestSite.plist")
    '    If File.Exists(strConfig) Then
    '        strPlistPath = strConfig
    '    End If

    '    Dim lstReturn As New List(Of WebSiteObject)
    '    Dim obj As Object = iTong.Device.CoreFoundation.ReadPlist_managed(strPlistPath)
    '    If TypeOf obj Is Dictionary(Of Object, Object) Then
    '        Dim dictLang As Dictionary(Of Object, Object) = obj
    '        For Each Item As Dictionary(Of Object, Object) In dictLang.Item(strLang)
    '            Dim objSite As New WebSiteObject
    '            For Each key As Object In Item.Keys
    '                Select Case key
    '                    Case "name"
    '                        objSite.Name = Item(key)

    '                    Case "text"
    '                        objSite.Text = Item(key)

    '                    Case "url"
    '                        objSite.Url = Item(key)

    '                    Case "iconurl"
    '                        objSite.IconUrl = Item(key)

    '                    Case "showtab"
    '                        objSite.ShowTab = Item(key)

    '                    Case "iconname"
    '                        objSite.IconName = Item(key)

    '                End Select
    '            Next
    '            lstReturn.Add(objSite)
    '        Next
    '    End If

    '    Return lstReturn
    'End Function  

    Public Function GetNodeIcon(ByVal strIconName As String) As Image

        Dim imgReturn As Image = My.Resources.tvw_tongbuapp_2

        Try
            Dim strImgPath As String = Path.Combine(Me.mSiteFolder, strIconName)
            If File.Exists(strImgPath) Then
                'imgReturn = Utility.GetImageFormFile(strImgPath)

                ''' Added by Utmost20150121
                '' 使用下面代码测试避免内存泄漏
                Dim bmp As New Bitmap(strImgPath)
                If bmp IsNot Nothing Then
                    imgReturn = CType(bmp.Clone(), Image)

                    bmp.Dispose()
                    bmp = Nothing
                End If
            End If

        Catch ex As Exception
            Debug.Write(ex)
        End Try

        Return imgReturn
    End Function

    Private Function SetUrlPara(ByVal strUrl As String) As String
        Dim strAppVer As String = My.Application.Info.Version.ToString(4)
        Dim strLang As String = Me.Language.CurrentLanguage.LangName
        Dim strDeviceId As String = Me.GetDeviceID()
        Dim strDeviceIdForApp As String = Me.GetDeviceIDForApp()
        Dim strType As String = Me.GetDeviceProductType()

        Dim strReturn As String = strUrl.Replace("#ver#", strAppVer).Replace("#lang#", strLang).Replace("#deviceid#", strDeviceId).Replace("#deviceidforapp#", strDeviceIdForApp).Replace("#devicetype#", strType).Replace("#jailbreak#", Me.GetJail())
        Return strReturn
    End Function

    '取得当前设备是不是越狱 
    'None    =0
    'Jail      =1
    'Jailbreak  =2   
    'Root     =3
    'Unroot   =4 
    Private Function GetJail() As String
        Dim strJail As String = "0"
        Try
            If Me.mAndroid IsNot Nothing AndAlso Me.mAndroid.Summary IsNot Nothing Then
                If Me.mAndroid.Summary.root Then
                    strJail = "3"
                Else
                    strJail = "4"
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "GetJail1")
        End Try

        Try
            If Me.mDevice IsNot Nothing Then
                If Me.mDevice.Jailbreaked Then
                    strJail = "2"
                Else
                    strJail = "1"
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "GetJail2")
        End Try
        Return strJail
    End Function

    Public Function GetNodeNameFromXML(ByVal strNodeName As String) As String
        Dim strText As String = ""
        Dim strXmlNodeName As String = ""
        Try
            Dim doc As New System.Xml.XmlDocument()
            doc.Load(SiteXmlHelper.SiteFilePath)

            If doc IsNot Nothing AndAlso doc.ChildNodes.Count > 0 Then
                '取得site节点
                Dim site As Xml.XmlElement = doc.ChildNodes(0)

                For Each itemGroup As Xml.XmlElement In site.ChildNodes
                    For Each itemNode As Xml.XmlElement In itemGroup.ChildNodes
                        Dim objSite As New WebSiteObject

                        For Each itemValue As Xml.XmlElement In itemNode.ChildNodes
                            If itemValue.Name.Equals("name", StringComparison.InvariantCultureIgnoreCase) Then
                                strXmlNodeName = itemValue.InnerText
                            ElseIf itemValue.Name.Equals("text", StringComparison.InvariantCultureIgnoreCase) Then
                                strText = itemValue.InnerText
                            End If
                        Next
                        If strXmlNodeName.Equals(strNodeName, StringComparison.InvariantCultureIgnoreCase) Then
                            Return strText
                        End If
                    Next
                Next
            End If
        Catch ex As Exception
        End Try

        If strXmlNodeName <> strNodeName Then
            strText = ""
        End If
        Return strText
    End Function

#End Region

#Region "--- App ---"

    Private Sub DownloadDeb(ByVal url As String)
        Dim strUrl As String = url.ToLower
        Dim info As New MultiThreadDownloadItemInfo
        Try
            Dim strName As String = Utility.GetParamValueFromQuery("name", strUrl)
            Dim strVersion As String = Utility.GetParamValueFromQuery("version", strUrl)
            Dim strUrlPara As String = Utility.GetParamValueFromQuery("url", strUrl)
            Dim strSid As String = Utility.GetParamValueFromQuery("sid", strUrlPara)
            Dim strJs As String = Common.EncryptDES(Me.CreateJsonString(strSid), "ac68!3#1", "ac68!3#1").Replace("+", "_@_")
            Dim strGetDownLoadPathUrl As String = WebUrl.PageJailbreakPlugins '"http://tbzsapi.tongbu.com/tbzsdata/tbzsjbPlugins.aspx"

            'If Me.mDevice IsNot Nothing AndAlso Not Me.mDevice.Jailbreaked Then
            '    Me.SetWebStatus(0, WebPluginStatus.Jail)
            '    Return
            'End If

            If Not Me.CheckDeviceMatch(strUrl) Then
                Me.SetWebStatus(strSid, WebPluginStatus.NoMatch)
                Return
            End If

            Dim arrKey() As Byte = Encoding.UTF8.GetBytes("&35%383@")
            Dim arrMac() As Byte = Encoding.UTF8.GetBytes(Common.GetMacAddress)
            Dim arrDes() As Byte = Common.EncryptDES(arrMac, arrKey, arrKey)
            Dim arrMD5() As Byte = Common.GetMd5Array(arrMac)

            strGetDownLoadPathUrl = strGetDownLoadPathUrl & String.Format("?v={0}", strJs) & String.Format("&src={0}", Common.ToHexString(arrDes)) & String.Format("&des={0}", Common.ToHexString(arrMD5))
            '如果是在同步推页面，获取LoadId
            Dim strContentURL As String = Utility.GetContentStringFromUrl(strGetDownLoadPathUrl, System.Text.Encoding.UTF8)
            Dim urlFinished As String = Common.DecryptDES(Me.GetJsonValue(strContentURL), "ac68!3#1", "ac68!3#1")
            If Not urlFinished.StartsWith("http://", StringComparison.InvariantCultureIgnoreCase) Then
                urlFinished = "http://" & urlFinished
            End If
            With info
                .IsSingleThread = False
                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.DEB
                .Class = ResourceClass.Plugins
                .Source = TaskSource.Tongbu
                .LoadId = ""
                .Url = urlFinished
                .ItemId = strSid
                .IconUrl = ""
                .TaskID = strSid
                Dim strExt As String = Path.GetExtension(urlFinished) '取得后缀 有可能是deb .zip
                If strName.Length > 0 Then
                    If strVersion.Length > 0 Then
                        strName &= String.Format("_v{0}", strVersion)
                    End If
                    If Not strName.EndsWith(strExt, StringComparison.InvariantCultureIgnoreCase) Then
                        strName &= strExt
                    End If
                End If

                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(.Url)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                    .Identifier = Me.mDevice.Identifier
                End If
            End With

            If Not Me.CheckDebFileExist(info) Then
                Me.mDownManage.NewTask(info)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Function CheckDeviceMatch(ByVal strUrl As String) As Boolean
        Dim strMixVersion As String = Utility.GetParamValueFromQuery("minfirmware", strUrl).PadRight(3, "0")
        Dim strMaxVersion As String = Utility.GetParamValueFromQuery("maxfirmware", strUrl).PadRight(3, "0")
        Dim strDeviceType As String = Utility.GetParamValueFromQuery("deviceid", strUrl)
        Dim strDeviceVersion As String = Me.mDevice.ProductVersion.Replace(".", "").PadRight(3, "0")

        If Me.mDevice IsNot Nothing Then
            If Not strDeviceType.Equals("0") Then

                Dim isIpad As Boolean = Me.mDevice.ProductType.StartsWith("ipad", StringComparison.InvariantCultureIgnoreCase)
                If (strDeviceType.Equals("1") AndAlso isIpad) OrElse _
                   (strDeviceType.Equals("2") AndAlso Not isIpad) Then
                    Return False
                End If

            End If

            If Common.CompareVer(strDeviceVersion, strMixVersion.Replace(".", "")) < 0 OrElse Common.CompareVer(strMaxVersion.Replace(".", ""), strDeviceVersion) < 0 Then
                Return False
            End If
        End If

        Return True
    End Function

    Private Function CheckDebFileExist(ByVal info As MultiThreadDownloadItemInfo) As Boolean
        Dim blnReturn As Boolean = False
        If File.Exists(info.DownloadPath) Then
            blnReturn = True
            If Me.mInstallHelper IsNot Nothing Then
                Dim pkginfo As New PackageInfo()
                pkginfo.PackagePath = info.DownloadPath
                pkginfo.StoreId = info.TaskID

                Me.mInstallHelper.InstallDeb(pkginfo)
                Me.SetWebStatus(info.TaskID, WebPluginStatus.Installing)
            Else
                Me.SetWebStatus(info.TaskID, WebPluginStatus.Downloaded)
            End If
        End If

        Return blnReturn
    End Function

    '拼装json 向服务器请求下载地址
    Private Function CreateJsonString(ByVal strSid As String) As String
        Dim dicChat As JsonObject
        Dim arrChats As New JsonArray

        dicChat = New JsonObject()
        dicChat.Add("mode", "1")
        dicChat.Add("sid", strSid)

        Dim writer As New JsonWriter()
        dicChat.Write(writer)

        Return writer.ToString()
    End Function

    '取得服务器返回的json 的 下载地址，下载地址是加密的。
    Private Function GetJsonValue(ByVal strJs As String) As String
        Dim strReturn As String = ""
        Dim strCode As String = ""
        Dim jsObjects As JsonObject = iTong.CoreFoundation.JsonParser.ParseString(strJs)

        Try
            If jsObjects IsNot Nothing AndAlso jsObjects.Keys().Count > 0 Then
                If jsObjects.ContainsKey("code") Then
                    strCode = CType(jsObjects("code"), JsonString).Value()
                End If

                If strCode.Equals("1") AndAlso jsObjects.ContainsKey("url") Then
                    strReturn = CType(jsObjects("url"), JsonString).Value()
                End If
            End If
        Catch ex As Exception
        End Try

        Return strReturn
    End Function

#End Region

#Region "--- Wallpaper ---"

    Private Function GetDeviceID() As String
        Dim deviceID As String = "5"   '没有接设备，默认返回5（即iPhone5）

        If Me.mDevice IsNot Nothing Then
            Dim strType As String = Me.mDevice.ProductType
            If strType.StartsWith("ipad", StringComparison.InvariantCultureIgnoreCase) AndAlso Val(strType.Substring(4)) <= 2 Then  'TypeConverter.Str2Int(strType.Substring(4)) <= 2
                deviceID = "2"

            ElseIf strType.StartsWith("ipad", StringComparison.InvariantCultureIgnoreCase) AndAlso Val(strType.Substring(4)) >= 3 Then
                deviceID = "3"

            ElseIf strType.StartsWith("ipod", StringComparison.InvariantCultureIgnoreCase) AndAlso Val(strType.Substring(4)) >= 5 Then
                deviceID = "5"

            ElseIf strType.StartsWith("iphone", StringComparison.InvariantCultureIgnoreCase) AndAlso Val(strType.Substring(6)) >= 5 Then
                deviceID = "5"

            ElseIf strType.StartsWith("iphone", StringComparison.InvariantCultureIgnoreCase) AndAlso Val(strType.Substring(6)) = 4 Then
                deviceID = "4"

            ElseIf strType.StartsWith("iphone", StringComparison.InvariantCultureIgnoreCase) AndAlso Val(strType.Substring(6)) < 4 Then
                deviceID = "1"

            ElseIf strType.StartsWith("ipod", StringComparison.InvariantCultureIgnoreCase) AndAlso Val(strType.Substring(4)) <= 4 Then
                deviceID = "1"

            End If
        End If

        Return deviceID
    End Function

    Private Function GetDeviceProductType() As String
        Dim strType As String = "iphone5,1"   '没有接设备，默认返回iphone5,1（即iPhone5）
        If Me.mDevice IsNot Nothing Then
            strType = Me.mDevice.ProductType.ToLower()
        End If

        Return strType
    End Function

    Private Function GetDeviceIDForApp() As String
        Dim deviceID As String = "1"
        If Me.mDevice IsNot Nothing Then
            Dim strType As String = Me.mDevice.ProductType '.ToLower()
            If strType.StartsWith("ipad", StringComparison.InvariantCultureIgnoreCase) Then
                deviceID = "2"
            Else
                deviceID = "1"
            End If
        End If

        Return deviceID
    End Function

#End Region

#Region "--- Music ---"

    '因为Utility.GetParamValueFromQuery函数没有兼容name与singername，单独写一个方法
    '以后定key时，要避免这种情况。
    '此方法只兼容url的参数格式（？或&分割）
    'Private Function GetParamValueFromQueryForMusic(ByVal key As String, _
    '                                              ByVal url As String, _
    '                                              ByVal isDecode As Boolean, _
    '                                              ByVal isToEnd As Boolean) As String
    '    Dim strValue As String = ""
    '    Dim strSeparator As String = "&"

    '    If key.Length > 0 Then
    '        Dim indexStart As Integer = -1

    '        While True
    '            indexStart = url.IndexOf(key & "=", indexStart + 1)

    '            Dim strPrevious As String = ""
    '            If indexStart > 0 Then
    '                strPrevious = url.Substring(indexStart - 1, 1)
    '            End If

    '            If String.Compare(strPrevious, "?", True) = 0 OrElse _
    '               String.Compare(strPrevious, strSeparator, True) = 0 Then
    '                Exit While
    '            End If
    '        End While

    '        Dim indexEnd As Integer = -1

    '        If indexStart > -1 Then
    '            '如果有多个参数的，参数以&分隔
    '            indexEnd = url.IndexOf(strSeparator, indexStart)

    '            If indexEnd = -1 OrElse isToEnd Then
    '                '如果只有一个参数则读到url的长度
    '                indexEnd = url.Length
    '            End If

    '            If indexEnd > -1 AndAlso indexEnd > indexStart Then
    '                strValue = url.Substring(indexStart + key.Length + 1, indexEnd - indexStart - key.Length - 1)
    '                If isDecode = True Then
    '                    strValue = System.Web.HttpUtility.UrlDecode(strValue)
    '                End If
    '            End If
    '        End If
    '    End If

    '    Return strValue
    'End Function

    'Private Sub InitDataGridView()

    '    With dgvMusic
    '        .SuspendLayout()
    '        .ScrollBars = System.Windows.Forms.ScrollBars.Vertical
    '        .Columns.Clear()
    '        .RowTemplate.Height = 50
    '        .Dock = DockStyle.Fill
    '        .tbShowNoData = False
    '        .tbNoDataText = "搜索结果为空。"

    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colChk", "", 20, False))
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Title", Me.Language.GetString("Common.Label.Name"), 180))                  '"名称"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Artist", Me.Language.GetString("Music.Label.Artist"), 100))               '"表演者"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Album", Me.Language.GetString("Common.Album"), 120))                          '"专辑"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Size", Me.Language.GetString("Common.Columns.Size"), 60, True, False, DataGridViewContentAlignment.MiddleRight))           '"大小"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "g_Duration", Me.Language.GetString("App.Button.TimeSort"), 60, True, False, DataGridViewContentAlignment.MiddleRight))     '"时间"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Source", Me.Language.GetString("Site.Label.Source"), 80, True, False, DataGridViewContentAlignment.MiddleCenter))                                    '"来源"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewButtonColumn), "Listen", Me.Language.GetString("Site.Label.Listen"), 80, False, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False)) '"试听"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewButtonColumn), "Download", Me.Language.GetString("Site.Label.Download"), 80, True, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False)) '"下载"
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "g_MusicID", "", 25, False))
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "b_DownUrl", "", 25, False))
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "SourceType", "", 25, False))
    '        .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Other", "", 100.0!, True, True))

    '        Me.pnlDgv.Controls.Add(dgvMusic)
    '        .ContextMenuStrip = Me.munSearch
    '        .ResumeLayout()
    '    End With

    '    Me.pnlSearch.Dock = DockStyle.Fill
    'End Sub

    'Private Sub StartSearch()

    '    If Not bgwSearch.IsBusy Then
    '        Me.lblResInfo.Text = Me.Language.GetString("Site.Label.SearchingMusic")                 '"正在搜索音乐，请稍候 ..."
    '        Me.dgvMusic.tbShowNoData = False
    '        Me.txtSearch.ReadOnly = True
    '        Me.dgvMusic.Rows.Clear()

    '        If Me.mSearchReturn IsNot Nothing AndAlso Me.mSearchReturn.Rows.Count > 0 Then
    '            Me.mSearchReturn.Rows.Clear()
    '        End If

    '        bgwSearch.RunWorkerAsync()
    '    End If

    '    Me.mShowDataGridView = True
    '    Me.pnlSearch.BringToFront()

    'End Sub

    'Private Sub btnReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Me.mShowDataGridView = False

    '    If Common.VerIs30 Then
    '        Me.GetTabPage("WebMusicSearch").BringToFront()
    '    Else
    '        Me.mDictSite("MusicSearch").BringToFront()

    '    End If

    '    Try
    '        If Me.bgwSearch.IsBusy Then
    '            Me.bgwSearch.CancelAsync()
    '        End If
    '        If Me.mSearchReturn IsNot Nothing AndAlso Me.mSearchReturn.Rows.Count > 0 Then
    '            Me.mSearchReturn.Rows.Clear()
    '        End If
    '    Catch ex As Exception
    '    End Try
    'End Sub

    'Private Sub txtSearch_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)
    '    If e.KeyCode = Keys.Enter AndAlso Me.txtSearch.Enabled Then
    '        Me.StartSearch()
    '    End If
    'End Sub

    'Private Sub ChangeSearchCount()
    '    If Me.dgvMusic.Rows.Count = 0 Then
    '        Me.lblResInfo.Text = ""
    '    Else
    '        Me.lblResInfo.Text = String.Format(Me.Language.GetString("Site.Label.MusicCount"), Me.dgvMusic.Rows.Count.ToString)  '"{0} 首歌曲"
    '    End If
    'End Sub

    'Private Sub PostSearchKey(ByVal strKey As Object)
    '    Try
    '        Dim key As String = System.Web.HttpUtility.UrlEncode(strKey)
    '        Dim mac As String = Common.GetMacAddress()
    '        'Dim pluginLogin As IPluginLogin = Me.mApplication.GetService(GetType(Tongbu.Components.IPluginLogin))
    '        'Dim uid As String = IIf(pluginLogin.Uid.Length = 0, "0", pluginLogin.Uid)
    '        Dim strSearchUrl As String = WebUrl.ChinaMusicSearchKey       '"http://tm.tongbu.com/music/api/music.aspx?mode=musicFindLogAdd&tag={0}&mid={1}&uid={2}"
    '        Dim urlPost As String = String.Format(strSearchUrl, key, mac, String.Empty) & "&guid=" & Guid.NewGuid().ToString("N")

    '        Dim webRequest As System.Net.HttpWebRequest = Nothing
    '        Dim webResponse As System.Net.HttpWebResponse = Nothing
    '        Dim requestStream As System.IO.Stream = Nothing
    '        Dim responseStream As System.IO.Stream = Nothing

    '        'Debug.Print(urlArgs.MainUrl & "?" & postString)
    '        webRequest = System.Net.WebRequest.Create(urlPost)

    '        webRequest.KeepAlive = False
    '        'webRequest.ContentType = "multipart/form-data; boundary=" & boundary
    '        'webRequest.ContentType = "application/x-www-form-urlencoded"
    '        webRequest.Timeout = 5000
    '        webRequest.Method = "GET"
    '        webResponse = webRequest.GetResponse()

    '    Catch ex As Exception
    '        Common.Log("PostSearchKey" & vbTab & ex.ToString())
    '    End Try
    'End Sub

    'Private Sub bgwSearch_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs)
    '    Try
    '        'Dim thd As New Threading.Thread(New Threading.ParameterizedThreadStart(AddressOf PostSearchKey))
    '        'thd.IsBackground = True
    '        'thd.Start(Me.txtSearch.Text.Trim())
    '        Dim searchText As String = Me.txtSearch.Text.Trim()
    '        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf PostSearchKey), searchText)
    '        mSearchReturn = MP3Search.SearchMusic(searchText)
    '    Catch ex As Exception
    '        Common.Log("bgwSearch_DoWork" & vbTab & ex.ToString())
    '    End Try
    'End Sub

    'Private Sub bgwSearch_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs)
    '    Me.txtSearch.ReadOnly = False

    '    Dim doc As New XmlDocument
    '    Try

    '        Me.dgvMusic.SuspendLayout()
    '        For Each row As DataRow In Me.mSearchReturn.Select("", "OrderID ASC")
    '            Dim strSourceType As String = Common.GetValue(Of String)(row("Source"), "")
    '            Dim strSource As String = ""
    '            If strSourceType = "google" Then
    '                strSource = Me.Language.GetString("Site.Label.GoogleMusic")             '"谷歌音乐"
    '            ElseIf strSourceType = "baidu" Then
    '                strSource = Me.Language.GetString("Site.Label.BaiduMusic")              '"百度音乐"
    '            ElseIf strSourceType = "soso" Then
    '                strSource = Me.Language.GetString("Site.Label.SoSoMusic")               '"搜搜音乐"

    '            End If

    '            Dim intSize As Integer = Common.GetValue(Of Integer)(row("b_Size"), 0)
    '            Dim strSize As String = IIf(intSize > 0, Utility.FormatFileSize(intSize), "")
    '            Dim intDuration As Integer = Common.GetValue(Of Integer)(row("g_Duration"), 0)
    '            Dim strDuration As String = IIf(intDuration > 0, Utility.FormatDuration(intDuration), "")

    '            Dim index As Integer = Me.dgvMusic.Rows.Add(False, _
    '                                                         Common.GetValue(Of String)(row("Title"), ""), _
    '                                                         Common.GetValue(Of String)(row("Artist"), ""), _
    '                                                         Common.GetValue(Of String)(row("Album"), ""), _
    '                                                         strSize, _
    '                                                         strDuration, _
    '                                                         strSource, _
    '                                                         Me.Language.GetString("Site.Label.Listen"), _
    '                                                         Me.Language.GetString("Site.Label.Download"), _
    '                                                         Common.GetValue(Of String)(row("g_MusicID"), ""), _
    '                                                         Common.GetValue(Of String)(row("b_DownUrl"), ""), _
    '                                                         strSourceType, _
    '                                                         "")
    '            ''"试听"
    '            '"下载"
    '            Dim cell As tbDataGridViewButtonCell = Me.dgvMusic.Rows(index).Cells("Download")
    '            cell.tbImageMouseHover = GuiHelper.GetMouseStateImage(My.Resources.btn_4_white, 2, 4)

    '        Next

    '        Me.dgvMusic.ResumeLayout()

    '        If Me.dgvMusic.Rows.Count = 0 Then
    '            'Me.lblNoneResult.Visible = True
    '            Me.dgvMusic.tbShowNoData = True
    '            Me.BringToFront()
    '        Else
    '            'Me.lblNoneResult.Visible = False
    '            Me.dgvMusic.tbShowNoData = False
    '        End If

    '        Me.ChangeSearchCount()

    '    Catch ex As Exception

    '    End Try

    'End Sub

    'Private Sub DownloadMP3(ByVal strSource As String, ByVal rowIndex As Integer, ByVal strName As String, ByVal cell As tbDataGridViewButtonCell)
    '    Dim strUrl As String = String.Empty

    '    If strSource.Equals("google", StringComparison.InvariantCultureIgnoreCase) Then
    '        strUrl = Me.dgvMusic.Item("g_MusicID", rowIndex).Value
    '    ElseIf strSource.Equals("baidu", StringComparison.InvariantCultureIgnoreCase) Then
    '        strUrl = Me.dgvMusic.Item("b_DownUrl", rowIndex).Value
    '    ElseIf strSource.Equals("soso", StringComparison.InvariantCultureIgnoreCase) Then
    '        strUrl = Me.dgvMusic.Item("b_DownUrl", rowIndex).Value
    '    End If

    '    'Dim thd As New System.Threading.Thread(AddressOf DownloadMP3Thread)
    '    'thd.IsBackground = True
    '    'thd.SetApartmentState(ApartmentState.STA)

    '    'thd.Start(New Object() {strSource, strUrl, strName, cell})
    '    ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf DownloadMP3Thread), New Object() {strSource, strUrl, strName, cell})
    'End Sub

    'Private Sub DownloadMP3Thread(ByVal state As Object)
    '    Try
    '        Dim strSource As String = state(0).ToString()
    '        Dim strUrl As String = state(1).ToString()
    '        Dim strName As String = state(2).ToString()
    '        Dim cell As tbDataGridViewButtonCell = CType(state(3), tbDataGridViewButtonCell)

    '        Dim task As MultiThreadDownloadItem = Nothing

    '        If strSource.Equals("google", StringComparison.InvariantCultureIgnoreCase) Then
    '            task = Me.mDownManage.NewTask(MP3Download.DownloadFromGoogle(strUrl, strName))
    '        ElseIf strSource.Equals("baidu", StringComparison.InvariantCultureIgnoreCase) Then
    '            task = Me.mDownManage.NewTask(MP3Download.DownloadFromBaidu(strUrl, strName))
    '        ElseIf strSource.Equals("soso", StringComparison.InvariantCultureIgnoreCase) Then
    '            task = Me.mDownManage.NewTask(MP3Download.DownloadFromSOSO(strUrl, strName))
    '        End If

    '        If task IsNot Nothing Then
    '            '下载的时候把文件路径保存到tag里标识，下载完成后修改状态。
    '            cell.Tag = task.ItemInfo.FilePath
    '        Else
    '            Me.SetCellValue(cell, "无法下载")
    '        End If
    '    Catch ex As Exception

    '    End Try
    'End Sub

    'Private Delegate Sub SetCellValueHandler(ByVal cell As tbDataGridViewButtonCell, ByVal strValue As String)
    'Private Sub SetCellValue(ByVal cell As tbDataGridViewButtonCell, ByVal strValue As String)
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New SetCellValueHandler(AddressOf SetCellValue), cell, strValue)
    '    Else
    '        cell.Value = strValue
    '    End If
    'End Sub

    'Private Sub dgvMusic_CellClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles dgvMusic.CellClick
    '    If e.RowIndex = -1 Then
    '        Return
    '    End If

    '    Dim colName As String = Me.dgvMusic.Columns(e.ColumnIndex).Name
    '    Select Case colName
    '        Case "Download"
    '            Dim cell As tbDataGridViewButtonCell = Me.dgvMusic.Rows(e.RowIndex).Cells("Download")
    '            If Not String.Equals(cell.Value, Me.Language.GetString("Site.Label.Download")) Then              '"下载"
    '                Return
    '            End If

    '            Dim strName As String = Common.ReplaceWinIllegalName((Me.dgvMusic.Item("Title", e.RowIndex).Value & "").ToString().Trim() & ".mp3")
    '            Dim strSource As String = Me.dgvMusic.Item("SourceType", e.RowIndex).Value
    '            Dim task As MultiThreadDownloadItem = Nothing


    '            Me.DownloadMP3(strSource, e.RowIndex, strName, cell)
    '            cell.Tag = String.Empty
    '            cell.Value = Me.Language.GetString("App.Cell.Downloading")              '"正在下载"
    '            cell.tbImageMouseHover = cell.tbImageMouseLeave
    '            cell.Enabled = False


    '        Case "Listen"
    '            Dim strUrl As String = ""
    '            Dim strSource As String = Me.dgvMusic.Item("SourceType", e.RowIndex).Value

    '            Dim info As New MusicInfo
    '            info.Title = Me.dgvMusic.Item("Title", e.RowIndex).Value
    '            info.Artist = Me.dgvMusic.Item("Artist", e.RowIndex).Value
    '            info.Album = Me.dgvMusic.Item("Album", e.RowIndex).Value

    '            If strSource.Equals("google", StringComparison.InvariantCultureIgnoreCase) Then
    '                Dim musicID As String = Me.dgvMusic.Item("g_MusicID", e.RowIndex).Value
    '                info.GoogleMusicID = musicID
    '                'strUrl = MP3Search.GetListeningUrlFromGoogle(musicID)
    '                'google视听接口挂了，换成下载的地址
    '                strUrl = MP3Download.ShowGoogleCheckCodeForm(musicID) & ""
    '            ElseIf strSource.Equals("baidu", StringComparison.InvariantCultureIgnoreCase) Then
    '                strUrl = MP3Search.GetMP3UrlFromBaidu(Me.dgvMusic.Item("b_DownUrl", e.RowIndex).Value)
    '            ElseIf strSource.Equals("soso", StringComparison.InvariantCultureIgnoreCase) Then
    '                strUrl = Me.dgvMusic.Item("b_DownUrl", e.RowIndex).Value
    '            End If

    '            '获取歌曲试听地址
    '            If strUrl.Length <= 0 Then
    '                tbMessageBox.Show(IIf(Me.Parent Is Nothing, Me, Me.mApplication), Me.Language.GetString("Site.Message.MusicCannotListen"), _
    '                                Me.Language.GetString("Common.Info"), _
    '                                MessageBoxButtons.OK, _
    '                                MessageBoxIcon.Information)
    '                '对不起，此歌曲无法进行试听，请点击其他的歌曲进行试听。
    '                '"信息"
    '                Return
    '            End If
    '            ' Me.PlayWebMusic(strUrl, info)
    '    End Select
    'End Sub

    Private Sub OnDownloadEventHandler(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        If sender IsNot Nothing AndAlso TypeOf sender Is WebSiteHelperBase.AnalyseDownloadInfo Then
            Dim info As WebSiteHelperBase.AnalyseDownloadInfo = sender
            '右下角出tip提示用户正在取得下载地址，等待几秒
            If info.Status = TaskState.GetDownUrl AndAlso info.WaitSecond > 0 Then
                'CType(Me.mApplication, MainForm).SetToolTip(info.Name, String.Format(Me.Language.GetString("Download.Cell.WaitSecond"), info.WaitSecond)) '"正在获取下载地址请稍等 {0} 秒"
                CType(Me.mApplication, MainForm).SetToolTip(info.Name, Me.Language.GetString("Download.Cell.WaitSecond")) '正在抓紧时间打包中，请稍候 ...
            End If

            Dim webBrowser As tbWebBrowserEx = Nothing
            If Me.mDictSite.ContainsKey("AppShare") Then
                webBrowser = Me.mDictSite("AppShare")
            Else
                Return
            End If

            If info.Status = TaskState.GetDownUrl Then
                WebSiteHelper.Instance.SetWebAppMarketStatus(webBrowser, "downStatus", info.ItemId, "downing")
            ElseIf info.Status = TaskState.GetDownUrlFailure Then
                WebSiteHelper.Instance.SetWebAppMarketStatus(webBrowser, "downStatus", info.ItemId, "downerr")
            ElseIf info.Status = TaskState.GetDownUrlSucceed Then
                WebSiteHelper.Instance.SetWebAppMarketStatus(webBrowser, "downStatus", info.ItemId, "default")
            End If
        End If
    End Sub

    Private Sub mDownManager_TaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.ItemInfo.Type = ResourceType.DEB Then
            Me.SetWebStatus(item.ItemInfo.ItemId, WebPluginStatus.Downloading)
        End If
    End Sub

    Private Sub mDownManager_Downloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Dim info As MultiThreadDownloadItem = CType(sender, MultiThreadDownloadItem)
        If info IsNot Nothing AndAlso info.ItemInfo.Type = ResourceType.DEB Then
            Me.SetWebStatus(info.ItemInfo.ItemId, WebPluginStatus.Downloading)
        End If
    End Sub

    Public Sub mDownManage_DownloadItemCompleted(ByVal sender As Object, ByVal e As iTong.CoreFoundation.MultiThreadDownloadCompletedEventArgs)
        Dim info As MultiThreadDownloadItem = CType(sender, MultiThreadDownloadItem)
        If info IsNot Nothing Then
            For Each Item As tbDataGridViewRow In Me.dgvMusic.Rows
                If Item IsNot Nothing AndAlso _
                   Item.Cells("Download").Tag IsNot Nothing AndAlso _
                   Item.Cells("Download").Value = Me.Language.GetString("App.Cell.Downloading") AndAlso _
                   TypeOf Item.Cells("Download").Tag Is String AndAlso _
                   Item.Cells("Download").Tag = info.ItemInfo.FilePath Then  '"正在下载"

                    Item.Cells("Download").Value = Me.Language.GetString("Download.Cell.DoenloadCompleted")             '"下载完成"
                    Item.Cells("Download").Tag = Nothing
                End If
            Next
            If info.ItemInfo.Type = ResourceType.DEB AndAlso (Me.mDevice Is Nothing OrElse Not Me.mDevice.IsConnected) Then
                Me.SetWebStatus(info.ItemInfo.ItemId, WebPluginStatus.Downloaded)
            End If
        End If
    End Sub

    'Private Sub munSearchArtist_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Dim strSinger As String = ""
    '    For Each dgvRow As DataGridViewRow In Me.dgvMusic.SelectedRows
    '        strSinger = dgvRow.Cells("Artist").Value & ""
    '        Exit For
    '    Next
    '    If strSinger.Length <> 0 Then
    '        Me.txtSearch.Text = strSinger
    '        Me.StartSearch()
    '    End If
    'End Sub

    'Private Sub munSearchAlbum_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Dim strAlbum As String = ""
    '    For Each dgvRow As DataGridViewRow In Me.dgvMusic.SelectedRows
    '        strAlbum = dgvRow.Cells("Album").Value & ""
    '        Exit For
    '    Next
    '    If strAlbum.Length <> 0 Then
    '        Me.txtSearch.Text = strAlbum
    '        Me.StartSearch()
    '    End If
    'End Sub

    Private Sub mInstallHelper_OnInstallDeb(ByVal sender As Object, ByVal args As InstallArgs)
        Select Case args.InstallState
            Case InstallState.Installing
                Me.SetWebStatus(args.PackageInfo.StoreId, WebPluginStatus.Installing)
            Case InstallState.Succeed
                Me.SetWebStatus(args.PackageInfo.StoreId, WebPluginStatus.Installed)
            Case InstallState.UnSucceed
                Me.SetWebStatus(args.PackageInfo.StoreId, WebPluginStatus.InstallFailure)
        End Select
    End Sub

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            If args.InstallState = InstallState.Succeed Then

                'Common.LogTest("安装回调给网页：" & args.PackageInfo.Identifier & vbTab & args.PackageInfo.Version)

                If args.PackageInfo IsNot Nothing Then
                    Me.SetWebAppStatus_Installed(args.PackageInfo)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "mInstallHelper_OnInstall")
        End Try
    End Sub

    Private Sub mUninstallHelper_OnUninstall(ByVal sender As Object, ByVal args As UninstallArgs)
        Try
            If args.UninstallState = UninstallState.UnInstallSucceed Then

                'Common.LogTest("卸载回调给网页：" & args.PackageInfo.Identifier & vbTab & args.PackageInfo.Version)

                If args.PackageInfo IsNot Nothing Then
                    Me.SetWebAppStatus_Uninstall(args.PackageInfo)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "mUninstallHelper_OnUninstall")
        End Try
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function CreateTabPage(ByVal name As String, Optional ByVal intBarHeight As Integer = 31) As tbTabPanel
        Dim tab As New tbTabPanel
        With tab
            .BackColor = System.Drawing.Color.White
            .Dock = System.Windows.Forms.DockStyle.Fill
            .Location = New System.Drawing.Point(0, 0)
            .Margin = New System.Windows.Forms.Padding(0)
            .Name = String.Format("{0}Tab", name) '"pnlMain"
            .SelectedIndex = -1
            .SelectedPage = Nothing
            .Size = New System.Drawing.Size(752, 641)
            .TabIndex = 0
            .TabStop = True
            .tbBackgroundImage = Nothing
            .tbBackgroundImageSplit = "0,0,0,0"
            .tbBarHeight = intBarHeight
            .tbBarOffset = New System.Drawing.Point(0, 0)
            .tbBarShadowColor = System.Drawing.Color.Black
            .tbBarShadowOffset = New System.Drawing.Point(0, -1)
            .tbBarShowWebButtons = True
            .tbBarWebButtonsHomeEnable = True
            .tbBarWebButtonsNextEnable = False
            .tbBarWebButtonsPreviousEnable = False
            .tbBarWebButtonsReflashEnable = True
            .tbBarWebButtonsRefreshStatus = tbTabPanelBar.BarRefreshButtonStatus.Refresh
            .tbPageBackgroundImageSplit = "2,3,2,3"
            .tbPageHeight = 21
            .tbPageNextButtonEnable = False
            .tbPageOnCenter = True
            .tbPagePrevButtonEnable = False
            .tbPageSpace = 2
            .tbBarBackgroundImage = My.Resources.pnl_bg_navigation
            .Tag = name

            If Common.VerIs30 Then
                .tbPageDisplayMode = tbBarPageDisplayMode.Line
                '.tbBarShowDeviceButtons = True
            End If
        End With

        AddHandler tab.WebHome, AddressOf tabPanel_WebHome
        AddHandler tab.WebNext, AddressOf tabPanel_WebNext
        AddHandler tab.WebPrevious, AddressOf tabPanel_WebPrevious
        AddHandler tab.WebRefresh, AddressOf tabPanel_WebRefresh

        Return tab
    End Function

    Private Function GetTabPage(ByVal name As String) As tbTabPanel
        Dim tab As tbTabPanel = Nothing
        If Me.mDictPanel.ContainsKey(name) Then
            tab = Me.mDictPanel(name)
        End If

        Return tab
    End Function

    'Private Sub InitWebSiteDefault()
    '    Dim strAppVer As String = My.Application.Info.Version.ToString(4)
    '    Dim strLang As String = Me.Language.CurrentLanguage.LangName

    '    Dim strDeviceID As String = Me.GetDeviceID()
    '    Dim pType As String = "iphone3,1"
    '    If Me.mDevice IsNot Nothing Then
    '        pType = Me.mDevice.ProductType.ToLower()
    '    End If

    '    'http://tm.tongbu.com/music/v2/search/index.html?tbversion=*******
    '    '"http://v2.tongbu.com/app?ver={0}&lang={1}&deviceid={2}&devicetype={3}"
    '    Dim strUrlApp As String = String.Format(WebUrl.FormSiteAppUrl, strAppVer, strLang, Me.GetDeviceIDForApp(), pType)
    '    '"http://tm.tongbu.com/music/v2/search/index.html?ver={0}&lang={1}"
    '    Dim strUrlMusicSearch As String = String.Format(WebUrl.FormSiteMusicSearchUrl, strAppVer, strLang) '"http://iclover.com/" '
    '    '"http://v2.tongbu.com/wallpaper?ver={0}&lang={1}&deviceid={2}"
    '    Dim strUrlWallpaper As String = String.Format(WebUrl.FormSiteWallpaperUrl, strAppVer, strLang, strDeviceID)
    '    '"http://tm.tongbu.com/ring/v2?ver={0}&lang={1}"
    '    Dim strUrlRingtone As String = String.Format(WebUrl.FormSiteRingtoneUrl, strAppVer, strLang)
    '    'Dim strUrlMusicJuJing As String = "http://hz.top100.cn/tongbu/?sid=1023"

    '    Dim strConfig As String = Folder.AppFolder & "\Site.ini"
    '    Dim strValue As String = String.Empty

    '    strValue = IniClass.GetIniSectionKey("Url", "App", strConfig)
    '    If Not String.IsNullOrEmpty(strValue) Then
    '        strUrlApp = strValue
    '    End If

    '    strValue = IniClass.GetIniSectionKey("Url", "MusicSearch", strConfig)
    '    If Not String.IsNullOrEmpty(strValue) Then
    '        strUrlMusicSearch = strValue
    '    End If

    '    strValue = IniClass.GetIniSectionKey("Url", "Ringtone", strConfig)
    '    If Not String.IsNullOrEmpty(strValue) Then
    '        strUrlRingtone = strValue
    '    End If

    '    strValue = IniClass.GetIniSectionKey("Url", "Wallpaper", strConfig)
    '    If Not String.IsNullOrEmpty(strValue) Then
    '        strUrlWallpaper = strValue
    '    End If

    '    Dim node As tbTreeNode = Nothing
    '    With Me.mNodeSite
    '        node = .ChildNodes.Add("App", Me.Language.GetString("Site.Label.SiteApp"))                          '"同步软件"
    '        node.NodeIcon = My.Resources.tvw_tongbuapp_2
    '        node.NodeIconState = ImageState.TwoState

    '        node = .ChildNodes.Add("Ringtone", Me.Language.GetString("Site.Label.SiteRingtone"))                '"同步铃声"
    '        node.NodeIcon = My.Resources.tvw_ringtone_2
    '        node.NodeIconState = ImageState.TwoState

    '        node = .ChildNodes.Add("Wallpaper", Me.Language.GetString("Photo.Message.iTongWallpaper"))          '"同步壁纸"
    '        node.NodeIcon = My.Resources.tvw_wallpaper_2
    '        node.NodeIconState = ImageState.TwoState

    '        node = .ChildNodes.Add("MusicSearch", Me.Language.GetString("Site.Label.MusicSearch"))              '"音乐搜索"
    '        node.NodeIcon = My.Resources.tvw_musicsearch_2
    '        node.NodeIconState = ImageState.TwoState

    '        'node = .ChildNodes.Add("MusicJuJing", Me.Language.GetString("Site.Label.MusicJuJing"))              '"巨鲸音乐"
    '        'node.NodeIcon = My.Resources.tvw_jvjing_2
    '        'node.NodeIconState = ImageState.TwoState
    '    End With

    '    'Dim tab As tbTabPanel = Me.CreateTabPage("MusicJuJing")
    '    'Me.pnlContainer.Controls.Add(tab)
    '    'Dim webbrow As tbWebBrowserEx = Me.CreateWebSite(strUrlMusicJuJing, True, tab)
    '    'tab.Pages.Add(New tbTabPanelPage(strUrlMusicJuJing, Me.Language.GetString("Site.Label.MusicJuJing"), webbrow))
    '    'Me.mDictPanel.Add("MusicJuJing", tab)

    '    Me.mDictSite.Add("App", Me.CreateWebSite(strUrlApp, False))

    '    Me.mDictSite.Add("MusicSearch", Me.CreateWebSite(strUrlMusicSearch, False))
    '    'Me.mDictSite.Add("MusicJuJing", webbrow)
    '    Me.mDictSite.Add("Ringtone", Me.CreateWebSite(strUrlRingtone, False))
    '    Me.mDictSite.Add("Wallpaper", Me.CreateWebSite(strUrlWallpaper, False))
    'End Sub

    Private Function CreateWebSite(ByVal strUrl As String, Optional ByVal showScrollBars As Boolean = True, Optional ByVal pnl As Control = Nothing) As tbWebBrowserEx
        Dim wb As New tbWebBrowserEx

        '添加到界面
        If pnl Is Nothing Then
            pnl = Me.pnlContainer
            pnl.Controls.Add(wb)
        End If

        '设置事件
        AddHandler wb.Navigating, AddressOf WebBrowser_Navigating
        AddHandler wb.NavigateError, AddressOf webDownloadPage_NavigateError
        AddHandler wb.NewWindow3, AddressOf webDownloadPage_NewWindow3
        AddHandler wb.Navigated, AddressOf WebBrowser_Navigated
        AddHandler wb.NewMessage, AddressOf WebBrowser_NewMessage

        '设置属性
        wb.IsWebBrowserContextMenuEnabled = True
        wb.ScriptErrorsSuppressed = True
        Try
            wb.ScrollBarsEnabled = showScrollBars
        Catch ex As Exception

        End Try

        'wb.WebBrowserShortcutsEnabled = False
        wb.ShowNavigateErrorPage = False
        wb.ShowLoadingWait = True
        wb.Name = strUrl
        wb.Size = New Size(pnl.Width + 4, pnl.Height + 4)
        wb.Location = New Point(-2, -2)
        wb.Dock = DockStyle.None
        wb.Anchor = AnchorStyles.Left Or AnchorStyles.Top Or AnchorStyles.Right Or AnchorStyles.Bottom

        If Me.mPluginLogin IsNot Nothing AndAlso Me.mPluginLogin.UserInfo IsNot Nothing Then
            wb.UserInfo = Me.mPluginLogin.UserInfo
            wb.UserInfoEncode = Me.mPluginLogin.UserInfoEncode
        End If

        If Not Folder.LangType = LanguageType.en_US Then
            wb.LoadingGif = GuiResource.gif_loading_24
        End If

        '设置浏览器自定义属性
        'wb.MAC = Common.GetMacAddress()
        'wb.ZhuShouVersion = My.Application.Info.Version.ToString().Replace(".", "")
        wb.Jailbreaked = Me.GetJailbreaked()
        If Me.mDevice IsNot Nothing Then
            wb.SN = Me.iPhone.SerialNumber
        End If
        If Utility.IsPanda Then
            wb.Client = "panda"
        End If
        Return wb
    End Function

    '获取设备的越狱状态
    Private Function GetJailbreaked() As Boolean
        Dim isJailBreak As Boolean = False

        If Me.iPhone Is Nothing AndAlso IniSetting.GetJailbreaked() Then
            isJailBreak = True

        ElseIf Me.iPhone IsNot Nothing AndAlso (Me.iPhone.Jailbreaked OrElse Me.iPhone.InstallCydia) Then
            isJailBreak = True
        End If

        Return isJailBreak
    End Function

    'Private Shared Function GetEncryptFileStrean(ByVal strFilePath As String) As MemoryStream
    '    Dim buffer() As Byte
    '    Dim stream As MemoryStream = Nothing
    '    Try
    '        Using fs As New FileStream(strFilePath, FileMode.Open)
    '            Dim bytesFile(fs.Length - 1) As Byte
    '            fs.Read(bytesFile, 0, fs.Length)
    '            buffer = iTong.CoreFoundation.Common.DecryptDES(bytesFile)

    '        End Using
    '        If buffer IsNot Nothing AndAlso buffer.Length > 0 Then
    '            stream = New MemoryStream(buffer)
    '        End If
    '    Catch ex As Exception

    '    End Try
    '    Return stream
    'End Function

    'Private Delegate Sub ShowStatusBarHandler(ByVal blnShow As Boolean)
    'Private Sub ShowStatusBar(ByVal blnShow As Boolean)
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New ShowStatusBarHandler(AddressOf ShowStatusBar), blnShow)
    '    Else
    '        If blnShow Then
    '            Me.tpnlJailbreakPlugin.RowStyles(1).Height = 30
    '        Else
    '            Me.tpnlJailbreakPlugin.RowStyles(1).Height = 0
    '        End If
    '    End If
    'End Sub

    'Private Delegate Sub SetStatusHandler(ByVal strMessage As String, ByVal intValue As Integer)
    'Private Sub SetStatus(ByVal strMessage As String, ByVal intValue As Integer)
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New SetStatusHandler(AddressOf SetStatus), strMessage, intValue)
    '    Else
    '        Me.lblState.Text = strMessage
    '        Me.pbarShowProgress.tbPlayValue = intValue
    '    End If
    'End Sub

#End Region

#Region "--- 对外方法 ---"

    Private mstrCurrentDeviceId As String = ""
    '选中树节点的项
    Public Sub SelectSite(ByVal strSiteName As String, Optional ByVal strUrl As String = "", Optional ByVal strTab As String = "")
        Try
            Me.mJumpUrl = strUrl

            Dim node As tbTreeNode = Me.tvwDevice.Nodes(strSiteName)
            Dim ctrl As tbTabPanel = Me.GetTabPage(strSiteName)
            If node IsNot Nothing Then
                If String.IsNullOrEmpty(strUrl) Then
                    Me.tvwDevice.SelectedNode = node
                Else
                    If Me.tvwDevice.SelectedNode IsNot Nothing AndAlso Me.tvwDevice.SelectedNode.Name = node.Name Then
                        Me.mJumpUrl = String.Empty
                    End If

                    If Me.tvwDevice.SelectedNode Is Nothing OrElse Not Me.tvwDevice.SelectedNode Is node Then
                        Me.tvwDevice.SelectedNode = node
                    End If

                    Dim wb As tbWebBrowser = Me.mDictSite(strSiteName)

                    '' 使用WebKit
                    'Dim wb As WebKitBrowserEx = Me._dictWebKitSites(strSiteName)
                    'If ctrl IsNot Nothing AndAlso strTab.Length > 0 Then
                    '    Dim page As tbTabPanelPage = ctrl.Pages(strTab)
                    '    If page IsNot Nothing Then
                    '        ctrl.SelectedPage = page
                    '    End If
                    'End If

                    If strUrl.StartsWith("http", StringComparison.InvariantCultureIgnoreCase) Then
                        wb.Navigate(strUrl)
                        If strTab.Length > 0 Then
                            Utility.WaitSeconds(3)
                            wb.Navigate(strUrl)
                        End If
                    Else
                        If wb.AccessibleDescription Is Nothing Then
                            wb.Navigate(Me.GetUrlByNodeName(strSiteName))
                            wb.AccessibleDescription = "Loaded"
                            Utility.WaitSeconds(3)
                        End If
                        'frmSite.Instance(Me.mApplication, Me.mDevice).SetWebAppMarketStatus(wb, "appInfo", "openDetail", strUrl)
                        WebSiteHelper.Instance().SetWebAppMarketStatus(wb, "appInfo", "openDetail", strUrl)
                    End If
                End If
            End If


            If ctrl IsNot Nothing Then
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected AndAlso _
                   Not Me.mDevice.Identifier.Equals(Me.mstrCurrentDeviceId, StringComparison.InvariantCultureIgnoreCase) Then

                    Me.mstrCurrentDeviceId = Me.mDevice.Identifier
                    If Me.mDevice.ProductType.ToLower().Contains("ipad") Then
                        ctrl.tbBarDeviceButtonStatus = tbBarDeviceButtonStatus.IPad
                    Else
                        ctrl.tbBarDeviceButtonStatus = tbBarDeviceButtonStatus.IPhone
                    End If

                End If
            End If

            '壁纸站根据设备选中相应的类型
            If Me.mDictSite.ContainsKey(strSiteName) Then
                Dim wbwalper As tbWebBrowser = Me.mDictSite(strSiteName)
                WebSiteHelper.Instance().SetWallpaperType(Me.mDevice, wbwalper)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub DeviceChangeed(ByVal device As iPhoneDevice)
        Me.mDevice = device
        Me.mAndroid = Nothing
        Me.SetDeviceListToBrowser()
    End Sub

    Public Sub OnAndroidConnected(ByVal device As AndroidDevice)
        Me.mDevice = Nothing
        Me.mAndroid = device
    End Sub

    Public Sub DisConnected()
        Me.mDevice = Nothing

        If Me.mAndroid IsNot Nothing Then
            Me.mAndroid.Dispose()
        End If

        Me.mAndroid = Nothing
        Me.SetDeviceListToBrowser()
    End Sub

    '设置浏览器属性：设备列表
    Private Sub SetDeviceListToBrowser()
        Try
            Dim jPara As New JsonObject
            Dim jList As New JsonArray
            Dim mainFrm As MainForm = Me.mApplication

            If mainFrm IsNot Nothing AndAlso mainFrm.UsbDevices IsNot Nothing Then
                For Each Item As UsbDevice In mainFrm.UsbDevices.Values
                    If Item IsNot Nothing AndAlso Item.IsConnectedUsb AndAlso Item.MobileDevice IsNot Nothing Then
                        Dim strType As String = "iOS"
                        If Item.MobileDevice.DeviceType <> DeviceType.iOS Then
                            strType = "Android"
                            Continue For
                        End If
                        Dim jObj As New JsonObject()
                        jObj.Add("Type", strType)
                        jObj.Add("Identify", Item.MobileDevice.DeviceID)
                        jObj.Add("Name", Item.MobileDevice.DeviceName)
                        jList.Add(jObj)
                    End If
                Next
            End If

            Dim strPara As String = ""
            If jList.Count > 0 Then
                jPara.Add("DeviceList", jList)
                Dim jw As New JsonWriter
                jPara.Write(jw)
                strPara = jw.ToString()
            End If

            If Me.mDictSite IsNot Nothing Then
                For Each Item As tbWebBrowserEx In Me.mDictSite.Values
                    If String.IsNullOrEmpty(strPara) Then
                        Item.DeviceList = Nothing
                    Else
                        Item.DeviceList = strPara
                    End If

                    WebSiteHelper.Instance().SetWebAppMarketStatus(Item, "changeStatus", "", "")
                Next
            End If

            Me.SetWebAppStatus_AllInstalled(Nothing)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSite_SetDeviceListToBrowser")
        End Try
    End Sub

#End Region

#Region "--- 与web页面交互 ---"

    Private Delegate Sub SetWebStatusHandler(ByVal strId As String, ByVal status As WebPluginStatus)
    Private Sub SetWebStatus(ByVal strId As String, ByVal status As WebPluginStatus)
        If Me.mDictSite.ContainsKey("Plugin") Then
            WebSiteHelper.Instance.SetWebStatus(Me.mDictSite("Plugin"), strId, status)
        End If

        If Me.mDictSite.ContainsKey("App") Then
            WebSiteHelper.Instance.SetWebStatus(Me.mDictSite("App"), strId, status)
        End If

        '' 使用WebKit
        'If Me._dictWebKitSites.ContainsKey("Plugin") Then
        '    ShowChatOnWebPage(Me._dictWebKitSites("Plugin"), strId, strStatus)
        'End If

        'If Me._dictWebKitSites.ContainsKey("App") Then
        '    ShowChatOnWebPage(Me._dictWebKitSites("App"), strId, strStatus)
        'End If
    End Sub

#Region "--- app市场按钮状态 ---"

    '获取所有软件
    Private Sub SetWebAppStatus_AllInstalled(ByVal webBrowser As tbWebBrowser)
        Try
            If Me.mtdGetAllInstalled IsNot Nothing AndAlso Me.mtdGetAllInstalled.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mtdGetAllInstalled = New Thread(AddressOf SetWebAppStatus_AllInstalledInThread)
            With Me.mtdGetAllInstalled
                .IsBackground = True
                .Start(webBrowser)
            End With
        Catch
        End Try
        'ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf SetWebAppStatus_AllInstalledInThread), webBrowser)
    End Sub

    Private Sub Stop2SetWebAppStatus_AllInstalled()
        If Me.mtdGetAllInstalled IsNot Nothing AndAlso Me.mtdGetAllInstalled.ThreadState <> ThreadState.Stopped Then
            Me.mtdGetAllInstalled.Abort()
        End If
    End Sub

    Private Sub SetWebAppStatus_AllInstalledInThread(ByVal objWebBrowser As Object)
        Try
            If Me.mDevice Is Nothing Then
                Return
            End If

            Dim dicApps As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User)
            Dim lstApps As New List(Of PackageInfo)

            For Each Item As FileSharingPackageInfo In dicApps.Values
                lstApps.Add(Item)
            Next

            Dim strJson As String = Me.GetJsonForWebAppMarketStatus(lstApps)

            If objWebBrowser Is Nothing Then
                Me.SetWebAppMarketStatus(WebPluginStatus.AllInstalled, strJson)
            Else
                Dim webBrowser As tbWebBrowser = CType(objWebBrowser, tbWebBrowser)
                WebSiteHelper.Instance().SetWebAppMarketStatus(webBrowser, "InitBtnStatus", strJson, "")
            End If


        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
    End Sub

    '设备断开
    Private Sub SetWebAppStatus_DeviceDisconnect()
        Me.SetWebAppMarketStatus(WebPluginStatus.DeviceDisconnect, "")
    End Sub

    '安装软件
    Private Sub SetWebAppStatus_Installed(ByVal package As PackageInfo)
        Dim strJson As String = Me.GetJsonForWebAppMarketStatus(package)

        Me.SetWebAppMarketStatus(WebPluginStatus.Installed, strJson)
    End Sub

    '卸载软件
    Public Sub SetWebAppStatus_Uninstall(ByVal package As PackageInfo)
        Dim strJson As String = Me.GetJsonForWebAppMarketStatus(package)
        Me.SetWebAppMarketStatus(WebPluginStatus.Uninstall, strJson)
    End Sub

    '调用js
    Private Sub SetWebAppMarketStatus(ByVal status As WebPluginStatus, ByVal data As String)
        Dim functionName As String = String.Empty
        Dim type As String = String.Empty

        Select Case status
            'Case WebPluginStatus.DeviceConnected
            '    functionName = "InitBtnStatus"
            '    type = ""

            Case WebPluginStatus.AllInstalled
                functionName = "InitBtnStatus"
                type = ""

            Case WebPluginStatus.DeviceDisconnect
                functionName = "InitBtnStatus"
                type = ""

            Case WebPluginStatus.Installed
                functionName = "RefreshBtnStatus"
                type = "Installed"

            Case WebPluginStatus.Uninstall
                functionName = "RefreshBtnStatus"
                type = "Uninstalled"

        End Select

        If Me.mDictSite.ContainsKey("App") Then
            WebSiteHelper.Instance().SetWebAppMarketStatus(Me.mDictSite("App"), functionName, data, type)
        End If

        If Me.mDictSite.ContainsKey("AppShare") Then
            WebSiteHelper.Instance().SetWebAppMarketStatus(Me.mDictSite("AppShare"), functionName, data, type)
        End If
    End Sub

    Private Function GetJsonForWebAppMarketStatus(ByVal package As PackageInfo) As String
        If package Is Nothing Then
            Return String.Empty
        End If

        Dim lstPackages As New List(Of PackageInfo)
        lstPackages.Add(package)

        Return Me.GetJsonForWebAppMarketStatus(lstPackages)
    End Function

    Private Function GetJsonForWebAppMarketStatus(ByVal lstPackages As List(Of PackageInfo)) As String
        Dim strJson As String = String.Empty

        If lstPackages Is Nothing OrElse lstPackages.Count = 0 Then
            Return strJson
        End If

        Dim dic As New JsonObject()
        Dim arrValues As New JsonArray

        For Each Item As PackageInfo In lstPackages
            Dim dicValue As New JsonObject()

            dicValue.Add("identifier", Item.Identifier)
            dicValue.Add("version", Item.Version)

            arrValues.Add(dicValue)
        Next

        dic.Add("list", arrValues)

        Dim writer As New JsonWriter()
        dic.Write(writer)
        strJson = writer.ToString()

        Return strJson
    End Function

#End Region

#End Region

#Region "--- V3站点交互逻辑 ---"

    Private Shared mLocker As New Object
    Private Shared mfrmSite As frmSite
    Private Shared mlstNode As New List(Of tbTreeNode)
    Public Shared Function Instance(ByVal app As IApplication, ByVal device As IDevice) As frmSite
        Try
            SyncLock mLocker
                If frmSite.mfrmSite Is Nothing Then
                    frmSite.mfrmSite = New frmSite(app, device, WebSiteType.Normal)
                Else
                    If device IsNot Nothing AndAlso TypeOf device Is iPhoneDevice Then
                        frmSite.mfrmSite.DeviceChangeed(device)
                    ElseIf device IsNot Nothing AndAlso TypeOf device Is AndroidDevice Then
                        frmSite.mfrmSite.OnAndroidConnected(device)
                    Else
                        frmSite.mfrmSite.DisConnected()
                    End If
                End If
            End SyncLock
        Catch ex As Exception
        End Try
        Return frmSite.mfrmSite
    End Function

    Public Function GetWebSiteObj(ByVal type As WebSiteType) As List(Of WebSiteObject)
        Dim lstWebObjs As New List(Of WebSiteObject)
        '网络不通的时候可以正常加载出来
        Try
            Dim doc As System.Xml.XmlDocument = Common.XmlLoader(SiteXmlHelper.SiteFilePath)
            Dim strTabGroup As String = ""
            If doc IsNot Nothing AndAlso doc.ChildNodes.Count > 0 Then
                Dim site As Xml.XmlElement = doc.ChildNodes(0)
                For Each itemGroup As Xml.XmlElement In site.ChildNodes
                    For Each itemNode As Xml.XmlElement In itemGroup.ChildNodes
                        Dim objSite As New WebSiteObject(itemNode)
                        If strTabGroup.Length > 0 AndAlso strTabGroup = objSite.TabGroup Then
                            Continue For
                        End If
                        If objSite.WebType = type Then
                            lstWebObjs.Add(objSite)
                        End If
                        strTabGroup = objSite.TabGroup

                        If strTabGroup.Length > 0 Then
                            objSite.Text = strTabGroup
                        End If
                    Next
                Next
            End If
            doc = Nothing
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSite3_GetWebSiteObj")
        End Try
        Return lstWebObjs
    End Function

    Public Shared Sub CreateSiteNode(ByVal node As tbTreeNode, ByVal type As WebSiteType)
        Try
            If frmSite.mfrmSite Is Nothing OrElse node Is Nothing Then
                Return
            End If
            '把节点取出来当重新加载过站点的时候重新刷新站点node
            If Not mlstNode.Contains(node) Then
                mlstNode.Add(node)
                node.Tag = type
            End If
            Dim lstObj As List(Of WebSiteObject) = frmSite.mfrmSite.GetWebSiteObj(type)

            If lstObj Is Nothing OrElse lstObj.Count <= 0 Then
                Return
            End If

            Dim lstAddNodes As New List(Of tbTreeNode)
            'Dim lstDeleteNodes As New List(Of tbTreeNode)

            For Each item As WebSiteObject In lstObj

                Dim childNode As tbTreeNode = node.ChildNodes(item.Name)

                If childNode Is Nothing Then
                    childNode = node.ChildNodes.Add(item.Name, item.Text)
                    childNode.NodeIconState = ImageState.TwoState

                End If

                childNode.Tag = item
                childNode.NodeIcon = frmSite.mfrmSite.GetNodeIcon(item.IconName)
                lstAddNodes.Add(childNode)

            Next

            'For Each Item As tbTreeNode In node.ChildNodes
            '    If lstAddNodes.Contains(Item) Then
            '        Continue For
            '    End If
            '    lstDeleteNodes.Add(Item)
            'Next

            'For Each Item As tbTreeNode In lstDeleteNodes
            '    node.ChildNodes.Remove(Item)
            '    Item = Nothing
            'Next
            'Dim index As Integer = 0
            'For Each Item As tbTreeNode In lstAddNodes
            '    Dim blnVisable As Boolean = Item.Visible

            '    Item.Visible = blnVisable
            '    index += 1
            'Next

            lstAddNodes.Clear()
            lstAddNodes = Nothing
            'lstDeleteNodes.Clear()
            'lstDeleteNodes = Nothing
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSite.CreateSiteNode")
        End Try
    End Sub

    Public Shared Function SelectNode(ByVal node As tbTreeNode, ByVal usb As UsbDevice) As Boolean

        If Not Common.VerIs30 OrElse frmSite.mfrmSite Is Nothing OrElse node Is Nothing Then
            Return False
        End If
        If Not node.Visible Then
            Return False
        End If

        If node.Tag IsNot Nothing AndAlso TypeOf node.Tag Is WebSiteObject Then
            If usb Is Nothing Then
                frmSite.mfrmSite.Tag = DeviceType.Unknown
            Else
                frmSite.mfrmSite.Tag = usb.DeviceType
            End If
            frmSite.mfrmSite.Show()
            frmSite.mfrmSite.SelectSite(CType(node.Tag, WebSiteObject).Name)
            frmSite.mfrmSite.Visible = True
            frmSite.mfrmSite.BringToFront()
        Else
            frmSite.mfrmSite.Visible = False
            frmSite.mfrmSite.SendToBack()

            Return False
        End If

        Return True
    End Function

    Public Function GetUrlByNodeName(ByVal strNode As String) As String
        Dim obj As WebSiteObject = Nothing

        Dim node As tbTreeNode = Me.tvwDevice.Nodes(strNode)
        If node IsNot Nothing AndAlso node.Tag IsNot Nothing AndAlso TypeOf node.Tag Is WebSiteObject Then
            obj = node.Tag
        End If

        If obj Is Nothing Then
            Return ""
        End If

        Return obj.Url
    End Function

#End Region

#Region "==== WebKitBrowser ===="

    'Private Function CreateWebKitSite(ByVal strUrl As String, Optional ByVal showScrollBars As Boolean = True, Optional ByVal pnl As Control = Nothing) As WebKitBrowserEx
    '    Dim wb As New WebKitBrowserEx()

    '    wb.Url = New Uri(strUrl)
    '    wb.IsWebBrowserContextMenuEnabled = True
    '    wb.ShowNavigateErrorPage = False
    '    wb.ShowLoadingWait = True
    '    wb.Name = strUrl
    '    wb.LoadingText = LanguageInterface.Instance().GetString("File.Label.Loading")

    '    wb.Size = New Size(Me.pnlContainer.Width + 4, Me.pnlContainer.Height + 4)
    '    wb.Location = New Point(-2, -2)
    '    wb.Dock = DockStyle.None
    '    wb.Anchor = AnchorStyles.Left Or AnchorStyles.Top Or AnchorStyles.Right Or AnchorStyles.Bottom

    '    If Not Folder.LangType = LanguageType.en_US Then
    '        wb.LoadingGif = My.Resources.gif_loading_24
    '    End If

    '    '设置浏览器自定义属性
    '    wb.MAC = Common.GetMacAddress()
    '    wb.ZhuShouVersion = My.Application.Info.Version.ToString().Replace(".", "")
    '    wb.Jailbreaked = Me.GetJailbreaked()

    '    If Me.mDevice IsNot Nothing Then
    '        wb.SN = Me.iPhone.SerialNumber
    '    End If


    '    AddHandler wb.Navigating, AddressOf WebKitBrowser_Navigating
    '    AddHandler wb.Navigated, AddressOf WebKitBrowser_Navigated
    '    AddHandler wb.NavigateError, AddressOf WebKitBrowser_NavigateError

    '    If pnl Is Nothing Then
    '        pnl = Me.pnlContainer
    '        pnl.Controls.Add(wb)
    '    End If

    '    Return wb
    'End Function

    'Private Sub WebKitBrowser_Navigated(ByVal sender As Object, ByVal e As WebBrowserNavigatedEventArgs)

    '    If Me.mAndroid Is Nothing Then
    '        WebSiteHelper.Instance.WebKitBrowserNavigated(Me.mDevice, sender, e)
    '        Me.SetNavigatingContralStatus(sender)
    '    End If

    'End Sub

    'Private Sub WebKitBrowser_NavigateError(ByVal sender As Object, ByVal e As WebKitBrowserErrorEventArgs)
    '    WebSiteHelper.Instance.WebBrowserNavigateError(Me.mApplication, sender, Nothing)
    'End Sub

    'Private Sub WebKitBrowser_Navigating(ByVal sender As Object, ByVal e As WebBrowserNavigatingEventArgs)
    '    Try
    '        Dim urlStr As String = e.Url.ToString()
    '        If urlStr.Equals("about:blank", StringComparison.InvariantCultureIgnoreCase) Then
    '            Return
    '        End If

    '        If urlStr.StartsWith("tongbu://music/search?name=", StringComparison.OrdinalIgnoreCase) Then
    '            Me.txtSearch.Text = Utility.GetParamValueFromQuery("name", urlStr)
    '            Me.StartSearch()
    '            e.Cancel = True
    '        Else
    '            Dim strApp As String = "app"
    '            If Me.tvwDevice.Nodes.Count > 1 Then
    '                strApp = ""
    '            End If

    '            If (mDevice IsNot Nothing AndAlso mAndroid IsNot Nothing) OrElse _
    '                mDevice Is Nothing AndAlso mAndroid Is Nothing Then

    '                WebSiteHelper.Instance().WebKitBrowserNavigating(mApplication, Nothing, sender, e, strApp)

    '            ElseIf mDevice IsNot Nothing Then
    '                WebSiteHelper.Instance().WebKitBrowserNavigating(mApplication, mDevice, sender, e, strApp)

    '            ElseIf mAndroid IsNot Nothing Then
    '                WebSiteHelper.Instance().WebKitBrowserNavigating(mApplication, mAndroid, sender, e, strApp)

    '            End If

    '        End If
    '    Catch ex As Exception

    '    End Try
    'End Sub

#End Region

#Region "--- 同步账号登陆逻辑 ---"

    Private Delegate Sub PluginLogin_LoginEventHandler(ByVal sender As Object, ByVal e As EventArgs)
    Private Sub PluginLogin_LoginEvent(ByVal sender As Object, ByVal e As EventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New PluginLogin_LoginEventHandler(AddressOf PluginLogin_LoginEvent), sender, e)
        Else
            Try
                If sender Is Nothing OrElse Me.IsDisposed Then
                    Return
                End If
                Dim loginObj As PluginLogin = sender
                'Me.wbsSite.UserInfo = loginObj.UserInfo
                If Me.mDictSite IsNot Nothing AndAlso Me.mDictSite.Count > 0 Then
                    For Each Item As tbWebBrowser In mDictSite.Values
                        Try
                            If Item Is Nothing Then
                                Continue For
                            End If
                            Item.UserInfo = loginObj.UserInfo
                            Item.UserInfoEncode = loginObj.UserInfoEncode
                        Catch ex As Exception
                            Common.LogException(ex.ToString, "frmSite_PluginLogin_LoginEvent1")
                        End Try
                    Next
                End If

                If Me.mDictPanel IsNot Nothing AndAlso Me.mDictPanel.Count > 0 Then
                    For Each Item As tbTabPanel In Me.mDictPanel.Values
                        Try
                            If Item Is Nothing Then
                                Continue For
                            End If

                            For Each page As tbTabPanelPage In Item.Pages
                                If page Is Nothing OrElse Not TypeOf page Is tbTabPanelPage OrElse CType(page, tbTabPanelPage).PageFrom Is Nothing Then
                                    Continue For
                                End If
                                Dim wb As tbWebBrowser = CType(page, tbTabPanelPage).PageFrom
                                wb.UserInfo = loginObj.UserInfo
                                wb.UserInfoEncode = loginObj.UserInfoEncode
                            Next
                        Catch ex As Exception
                            Common.LogException(ex.ToString, "frmSite_PluginLogin_LoginEvent2")
                        End Try
                    Next
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmSite_PluginLogin_LoginEvent3")
            End Try
        End If
    End Sub

    Private Sub PluginLogin_LogoutEvent(ByVal sender As Object, ByVal e As EventArgs)
        If sender Is Nothing Then
            Return
        End If
        Dim loginObj As PluginLogin = sender
        'Me.wbsSite.UserInfo = loginObj.UserInfo
        If Me.mDictSite IsNot Nothing AndAlso Me.mDictSite.Count > 0 Then
            For Each Item As tbWebBrowser In mDictSite.Values
                Try
                    If Item Is Nothing Then
                        Continue For
                    End If
                    Item.UserInfo = loginObj.UserInfo
                    Item.UserInfoEncode = loginObj.UserInfoEncode
                Catch ex As Exception
                    Common.LogException(ex.ToString, "frmSite_PluginLogout_LoginEvent1")
                End Try
            Next
        End If

        If Me.mDictPanel IsNot Nothing AndAlso Me.mDictPanel.Count > 0 Then
            For Each Item As tbTabPanel In Me.mDictPanel.Values
                Try
                    If Item Is Nothing Then
                        Continue For
                    End If

                    For Each page As tbTabPanelPage In Item.Pages
                        If page Is Nothing OrElse Not TypeOf page Is tbTabPanelPage OrElse CType(page, tbTabPanelPage).PageFrom Is Nothing Then
                            Continue For
                        End If
                        Dim wb As tbWebBrowser = CType(page, tbTabPanelPage).PageFrom
                        wb.UserInfo = loginObj.UserInfo
                        wb.UserInfoEncode = loginObj.UserInfoEncode
                    Next
                Catch ex As Exception
                    Common.LogException(ex.ToString, "frmSite_PluginLogout_LoginEvent2")
                End Try
            Next
        End If
    End Sub

#End Region

#Region "--- 刷机 固件下载 ---"

    Private Sub LoadFlashFirmware()
        Dim fnode As tbTreeNode = Nothing
#If IS_ITONG Then
        Me.mNodeFlash = Me.tvwDevice.Nodes.Add("Flash", "刷机", True) '刷机
        With Me.mNodeFlash
            fnode = .ChildNodes.Add("OnekeyFlash", "一键刷机")
            fnode.NodeIcon = My.Resources.tvw_tongbuapp_2
            fnode.NodeIconState = ImageState.TwoState

            Dim iDevice As Object = Me.mDevice
            If Me.mDeviceRecovery IsNot Nothing Then
                iDevice = Me.mDeviceRecovery
            ElseIf Me.mDeviceDFU IsNot Nothing Then
                iDevice = Me.mDeviceDFU
            End If

            Me.mFrmFlash = Common.CheckFormExist(GetType(frmFlash))
            If Me.mFrmFlash Is Nothing OrElse Me.mFrmFlash.IsDisposed Then
                Me.mFrmFlash = New frmFlash(Me.mApplication, iDevice)
                Utility.AddForm2Panel(Me.mFrmFlash, Me.pnlFlash, True)
            Else
                Me.DeviceConnect()
            End If

            fnode = .ChildNodes.Add("DowmloadFirmware", "固件下载")
            fnode.NodeIcon = My.Resources.tvw_firmware_2
            fnode.NodeIconState = ImageState.TwoState

            Me.mFrmFirmware = Common.CheckFormExist(GetType(frmFirmware))
            If Me.mFrmFirmware Is Nothing OrElse Me.mFrmFirmware.IsDisposed Then
                Me.mFrmFirmware = New frmFirmware(Me.mApplication, iDevice)
            Else
                Me.mFrmFirmware.OnConnect(iDevice)
            End If

            Utility.AddForm2Panel(Me.mFrmFirmware, Me.pnlFirmware, True)

        End With

#End If
    End Sub

    Private Sub FlashFirmwareTreeSelectedChanged(ByVal strName As String)

        If strName = "DowmloadFirmware" Then
            Me.pnlFirmware.BringToFront()
            Me.pnlFirmware.Dock = DockStyle.Fill
            frmSite.mstrJailbreakSelected = Me.tvwDevice.SelectedNode.Name
        End If

        If strName = "OnekeyFlash" Then
            Me.pnlFlash.BringToFront()
            Me.pnlFlash.Dock = DockStyle.Fill
            frmSite.mstrJailbreakSelected = Me.tvwDevice.SelectedNode.Name


            '#If IS_ITONG Then
            '            If Me.mFrmFlash IsNot Nothing Then
            '                Me.mFrmFlash.OnConnect(Me.mDevice)
            '            End If
            '#End If
        End If

    End Sub

    Private Sub DeviceConnect()
#If IS_ITONG Then
        Try
            If Me.mDevice IsNot Nothing Then
                'If Me.mFrmFlash.mDevice IsNot Nothing Then
                Me.mFrmFlash.mDevice = Me.mDevice
                Me.mFrmFlash.mDeviceRecovery = Nothing
                Me.mFrmFlash.mDeviceDFU = Nothing
                Me.mFrmFlash.OnConnect(Me.mDevice)
                'End If
            ElseIf Me.mDeviceRecovery IsNot Nothing Then
                'If Me.mFrmFlash.mDeviceRecovery Is Nothing Then
                Me.mFrmFlash.mDeviceRecovery = Me.mDeviceRecovery
                Me.mFrmFlash.mDevice = Nothing
                Me.mFrmFlash.mDeviceDFU = Nothing
                Me.mFrmFlash.OnConnect(Me.mDeviceRecovery)
                'End If
            ElseIf Me.mDeviceDFU IsNot Nothing Then
                'If Me.mFrmFlash.mDeviceDFU Is Nothing Then
                Me.mFrmFlash.mDeviceDFU = Me.mDeviceDFU
                Me.mFrmFlash.mDevice = Nothing
                Me.mFrmFlash.mDeviceRecovery = Nothing
                Me.mFrmFlash.OnConnect(Me.mDeviceDFU)
                'End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeviceConnect")
        End Try
#End If
    End Sub

    Protected Overrides Sub OnVisibleChanged(e As System.EventArgs)
        MyBase.OnVisibleChanged(e)
        If Me.Visible Then
            Me.FlashDeviceChanged()
        End If
    End Sub

    Private Sub OnDisconnectToFlash(ByVal device As IDevice)

        Dim iDevice As IDevice = Me.mDevice
        If Me.mDeviceRecovery IsNot Nothing Then
            iDevice = Me.mDeviceRecovery
        ElseIf Me.mDeviceDFU IsNot Nothing Then
            iDevice = Me.mDeviceDFU
        End If

        If iDevice IsNot Nothing AndAlso iDevice Is device Then
#If IS_ITONG Then
            Me.mFrmFlash = Common.CheckFormExist(GetType(frmFlash))
            If Me.mFrmFlash IsNot Nothing Then
                Me.mFrmFlash.OnDisconnect(device)
            End If

            Me.mFrmFirmware = Common.CheckFormExist(GetType(frmFirmware))
            If Me.mFrmFirmware IsNot Nothing Then
                Me.mFrmFirmware.OnDisconnect(device)
            End If
            Me.FlashDeviceChanged()
#End If
        End If

        Me.RemoveDeviceNodes()

    End Sub

    Private Sub FlashDeviceChanged()
#If IS_ITONG Then
        Me.mFrmFlash = Common.CheckFormExist(GetType(frmFlash))
        If Me.mFrmFlash IsNot Nothing Then
            Me.DeviceConnect()
            Utility.AddForm2Panel(Me.mFrmFlash, Me.pnlFlash, True)
        End If

        Me.mFrmFirmware = Common.CheckFormExist(GetType(frmFirmware))
        If Me.mFrmFirmware IsNot Nothing Then
            Me.mFrmFirmware.OnConnect(Me.mDevice)
            Utility.AddForm2Panel(Me.mFrmFirmware, Me.pnlFirmware, True)
        End If

        If Me.tvwDevice.SelectedNode Is Nothing Then
            LoadpnlGuide()
        End If
#End If
    End Sub

#End Region

#Region "--- 引导页面 ---"
    Private Sub LoadpnlGuide()
        Me.pnlGuide.BringToFront()
        Me.pnlGuide.Dock = DockStyle.Fill

        Me.PictureBoxMedian.Image = My.Resources.bg_guide_median
        Me.PictureBoxMedian.Size = New Size(1, 216)

        Me.PictureBoxFlash.Image = My.Resources.bg_Flash_Guide
        Me.PictureBoxFlash.Size = New Size(138, 138)
        Me.lblFlashDepict.Text = "针对苹果操作系统（iOS系统）固件进行处理，简单的说就是改变或者更新手机的操作系统，刷机可以有效的解决白苹果，黑屏，无限Recovery等问题。"
        Me.lblFlashDepict.Size = New Size(316, 102)
        Me.lblFlashBackup.Text = String.Format("注意事项:{0}1、刷机不可逆，成功后无法返回到已关闭验证的固件版本{0}2、越狱设备会还原为非越狱设备{0}3、刷机前建议您先备份数据{0}4、使用本工具请自行承担风险！", vbCrLf)
        Me.lblFlashBackup.Size = New Size(316, 108)

        Me.PictureBoxJailbreak.Image = My.Resources.bg_Jailbreak_Guide
        Me.PictureBoxJailbreak.Size = New Size(138, 138)
        Me.lblJailbreakDepict.Text = "充分开放苹果操作系统（iOS系统）的用户操作权限，越狱后会取得系统最高的操作权限，可以更充分的利用系统，安装破解软件、插件等，并能任意读写系统任意区域的文件。"
        Me.lblJailbreakDepict.Size = New Size(316, 102)
        Me.lblJailbreakBackup.Text = String.Format("注意事项:{0}1、越狱后极小概率会导致系统不稳定（可通过刷机解决）{0}2、有可能增加设备耗电量", vbCrLf)
        Me.lblJailbreakBackup.Size = New Size(316, 108)

        Me.ChangeLocation()
    End Sub

    Private Sub ChangeLocation()
        Dim iDepictGapMedianX As Double = 74
        Dim iPictureGapDepictY As Double = 44
        Dim iDepictGapBackupY As Double = 34
        Dim iBackupGapBtn As Double = 76
        Dim iDepictY As Double = (Me.pnlGuide.Height - Me.lblFlashDepict.Height - Me.lblFlashBackup.Height) / 2

        Me.PictureBoxMedian.Location = New Point((Me.pnlGuide.Width - Me.PictureBoxMedian.Width) / 2, (Me.pnlGuide.Height - Me.PictureBoxMedian.Height) / 2)

        'Me.lblFlashDepict.Location = New Point(Me.PictureBoxMedian.Location.X - iDepictGapMedianX - Me.lblFlashDepict.Width, iDepictY)
        'Me.lblJailbreakDepict.Location = New Point(Me.PictureBoxMedian.Location.X + iDepictGapMedianX, iDepictY)

        Me.lblFlashDepict.Location = New Point(Me.PictureBoxMedian.Location.X + iDepictGapMedianX, iDepictY)
        Me.lblJailbreakDepict.Location = New Point(Me.PictureBoxMedian.Location.X - iDepictGapMedianX - Me.lblFlashDepict.Width, iDepictY)

        Me.lblFlashBackup.Location = New Point(Me.lblFlashDepict.Location.X, iDepictY + Me.lblFlashDepict.Height)
        Me.lblJailbreakBackup.Location = New Point(Me.lblJailbreakDepict.Location.X, iDepictY + Me.lblJailbreakDepict.Height)

        Me.btnInFlash.Location = New Point(Me.lblFlashBackup.Location.X + Me.lblFlashBackup.Width / 2 - Me.btnInFlash.Width / 2, Me.lblFlashBackup.Location.Y + iBackupGapBtn)
        Me.btnInJailbreak.Location = New Point(Me.lblJailbreakBackup.Location.X + Me.lblJailbreakBackup.Width / 2 - Me.btnInFlash.Width / 2, Me.lblJailbreakBackup.Location.Y + iBackupGapBtn)

        Me.PictureBoxFlash.Location = New Point(Me.lblFlashDepict.Location.X + Me.lblFlashDepict.Width / 2 - Me.PictureBoxFlash.Width / 2, Me.lblFlashDepict.Location.Y - iPictureGapDepictY - Me.PictureBoxFlash.Height)
        Me.PictureBoxJailbreak.Location = New Point(Me.lblJailbreakDepict.Location.X + Me.lblJailbreakDepict.Width / 2 - Me.PictureBoxJailbreak.Width / 2, Me.lblJailbreakDepict.Location.Y - iPictureGapDepictY - Me.PictureBoxJailbreak.Height)

        Me.btnInFlash.Location = New Point(Me.lblFlashBackup.Location.X + (Me.lblFlashBackup.Width - Me.btnInFlash.Width) / 2, Me.lblFlashBackup.Location.Y + Me.lblFlashBackup.Height + iBackupGapBtn)
        Me.btnInJailbreak.Location = New Point(Me.lblJailbreakBackup.Location.X + (Me.lblJailbreakBackup.Width - Me.btnInJailbreak.Width) / 2, Me.lblJailbreakBackup.Location.Y + Me.lblJailbreakBackup.Height + iBackupGapBtn)

        Me.mCurrentGuideSize = Me.pnlGuide.Size
    End Sub
    Private Sub btnInFlash_Click(sender As Object, e As EventArgs) Handles btnInFlash.Click
        Me.tvwDevice.SelectedNode = Me.tvwDevice.Nodes("OnekeyFlash")
    End Sub

    Private Sub btnInJailbreak_Click(sender As Object, e As EventArgs) Handles btnInJailbreak.Click
        Me.tvwDevice.SelectedNode = Me.tvwDevice.Nodes("Guide")
    End Sub

    Private Sub pnlGuide_SizeChanged(sender As Object, e As EventArgs) Handles pnlGuide.SizeChanged
        If Me.mCurrentGuideSize <> Me.pnlGuide.Size Then
            Me.ChangeLocation()
        End If
    End Sub

#End Region




    Private mDeviceNode As tbTreeNode = Nothing
    Private mDeviceStateNode As tbTreeNode = Nothing
    Private Const CONST_DEVICE As String = "device"
    Private Const CONST_DEVICESTATE As String = "devicestate"

    Private Sub LoadDevice()
        Dim strNoDevice As String = Me.Language.GetString("App.TreeNode.NotConnectDevice") '"设备(未连接)"

        Dim strDeviceState As String = ""
        Dim strDeviceName As String = ""
        If Me.mDevice IsNot Nothing Then
            strDeviceState = "正常模式"
            strDeviceName = Me.mDevice.DeviceName
        ElseIf Me.mDeviceDFU IsNot Nothing Then
            strDeviceState = "DFU模式"
            strDeviceName = Me.mDeviceDFU.DeviceName
        ElseIf Me.mDeviceRecovery IsNot Nothing Then
            strDeviceState = "恢复模式"
            strDeviceName = Me.mDeviceRecovery.DeviceName
        End If

        Dim strDevice As String = IIf(strDeviceName.Length = 0, strNoDevice, String.Format("{0} - {1}", Me.Language.GetString("App.TreeNode.Device"), strDeviceName))
        If Me.mDeviceNode Is Nothing Then
            Me.mDeviceNode = New tbTreeNode(CONST_DEVICE, strDevice, Color.Black, True) 'Device
            Me.tvwDevice.Nodes.Add(Me.mDeviceNode)
        Else
            Me.mDeviceNode.Text = strDevice
        End If

        If strDevice <> strNoDevice Then
            If mDeviceStateNode Is Nothing Then
                With Me.mDeviceNode
                    mDeviceStateNode = .ChildNodes.Add(CONST_DEVICESTATE, strDeviceState)
                    mDeviceStateNode.NodeIcon = My.Resources.app_product_all
                    mDeviceStateNode.NodeIconState = ImageState.OneState
                End With
            Else
                Me.mDeviceStateNode.Text = strDeviceState
            End If
        End If
    End Sub

    Private Sub RemoveDeviceNodes()
        Me.tvwDevice.SuspendLayout()

        Dim node As tbTreeNode = Me.tvwDevice.Nodes(CONST_DEVICE)

        If node IsNot Nothing Then
            node.Text = Me.Language.GetString("App.TreeNode.NotConnectDevice") '"设备(未连接)"

            For Each childNode As tbTreeNode In node.ChildNodes
                Me.tvwDevice.Nodes.Remove(childNode)
            Next
        End If

        Me.tvwDevice.ResumeLayout()
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.tvwDevice.tbNodeSelectedBackground = My.Resources.tvw_node_bg_select
    End Sub

End Class
