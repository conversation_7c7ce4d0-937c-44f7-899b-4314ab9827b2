﻿Imports System.Collections.Specialized
Imports System.Drawing
Imports System.Drawing.Drawing2D
Imports System.ComponentModel.Design
Imports System.Threading

Public Class MainForm
    Implements IApplication

    Public mCurrentDevice As UsbDevice = Nothing

    '' Added by Utmost20140718
    '' 使用StringComparer.InvariantCultureIgnoreCase可以避免关键字使用ToLower或ToUpper函数转换
    Private mDictUsbDevice As New Dictionary(Of String, UsbDevice)(StringComparer.InvariantCultureIgnoreCase)
    Private mDictDeviceButton As New Dictionary(Of String, tbCheckButton)(StringComparer.InvariantCultureIgnoreCase)
    Private mDictAppleDevice As New Dictionary(Of String, IUSB)(StringComparer.InvariantCultureIgnoreCase)
    Private mListFormCheckInit As New List(Of String)

    Private mFrmAndroidFeedback As frmAndroidFeedback

    Private mToolButtonHeight As Integer = 90
    Private mToolButtonLeft As Integer = 196
    Private mDeviceButtonHeight As Integer = 25
    Private mDeviceButtonLeft As Integer = 8
    Private mDeviceButtonTop As Integer = 6
    Private mDeviceButtonDiff As Integer = 8
    Private mDeviceButtonWidthMax As Integer = 200
    Private mDeviceButtonWidthMin As Integer = 60

    Private mContainerLeft As Integer = 1
    Private mLstDeviceButtons As List(Of tbCheckButton) = New List(Of tbCheckButton)()
    Private DEVICE_BUTTON_MAX_TEXT_LENGTH As Integer = 20

    Private mBgColorDeviceButton As Color = Color.FromArgb(&H56, &H9D, &HC7)
    Private mBgColorToolButton As Color = Color.FromArgb(&H46, &H98, &HC5)

    Private mUsbDeviceMgr As AndroidDeviceMonitor = Nothing
    Private Shared mLstKilledADBs As List(Of String) = New List(Of String)()
    Private mDaemonPkgInfo As PackageInfo = Nothing

    Public _CurrentAccessibleDescription As String = "Welcome"    '' 默认创建Welcome窗体
    Private mblnUserClick As Boolean = True

    Private _currentItem As tbRadioButton = Nothing

    Private btnGetLog As New iTong.Components.tbButton

    Public Shared mIsShowiTunesUpdate As Boolean = True

    '登陆用到的参数
    Private mPluginLogin As PluginLogin
    Private mThrDownIcon As Thread

    '主窗体头部皮肤
    Private mtbBackground As New TextureBrush(My.Resources.new_bottom_bg)
    Private mtbTopBackground As New TextureBrush(My.Resources.new_top_bg)
    Private mtbWaterMark As New TextureBrush(My.Resources.new_top_watermark)
    Private mtbLine As New TextureBrush(My.Resources.new_top_line)
    Private mShowFiveYearAnniversary As Boolean = False
    Private mShowFiveYearBar As Boolean = False
    'Private _dictConnectedForms As New Dictionary(Of String, Dictionary(Of String, Form))

    Public Shared Event OnPush As EventHandler(Of AirEventArgs)

    Friend WithEvents _tmrMonitor As System.Timers.Timer = New System.Timers.Timer()
    '' 开始监控设备连接状况
    Private Sub Monitor_Tick(ByVal sender As System.Object, ByVal e As System.Timers.ElapsedEventArgs) Handles _tmrMonitor.Elapsed

        If mUsbDeviceMgr IsNot Nothing AndAlso _
           mCurrentDevice IsNot Nothing AndAlso _
           mCurrentDevice.MobileDevice Is Nothing AndAlso _
           mCurrentDevice.DeviceType = DeviceType.Android Then

            Try
                Dim deviceId As String = mCurrentDevice.DeviceID

                If mUsbDeviceMgr.DictUsbDevices.ContainsKey(deviceId) Then
                    mCurrentDevice = mUsbDeviceMgr.DictUsbDevices(deviceId)
                    'Me.LogCurrentDevice("Monitor_Tick")
                    mDictUsbDevice(deviceId) = mCurrentDevice
                End If

                If mDictDeviceButton.ContainsKey(deviceId) Then
                    mDictDeviceButton(deviceId).Tag = mCurrentDevice
                End If
            Catch ex As Exception

            End Try

        End If
    End Sub

    '验证mainform是不是加载完成了。
    Private mFormLoaded As Boolean = False
    Public ReadOnly Property FormLoaded() As Boolean
        Get
            Return Me.mFormLoaded
        End Get
    End Property

    Public ReadOnly Property UsbDevices() As Dictionary(Of String, UsbDevice)
        Get
            Return Me.mDictUsbDevice
        End Get
    End Property

    '当前选中的设备
    Public ReadOnly Property CurrentDevice() As UsbDevice
        Get
            Return Me.mCurrentDevice
        End Get
    End Property

    Public Sub SetDeviceButtonTag(ByVal deviceId As String, ByVal usbDev As UsbDevice)
        'Dim devId As String = deviceId.ToLower()

        If Me.mDictDeviceButton.ContainsKey(deviceId) Then
            Me.mDictDeviceButton(deviceId).Tag = usbDev
        End If
    End Sub

    '' Added by Utmost200701
    '' 更新顶部设备名称
    Public Sub UpdateTopDeviceButtonText(ByVal deviceId As String, ByVal deviceName As String)

        If Me.mDictDeviceButton.ContainsKey(deviceId) Then
            Me.mDictDeviceButton(deviceId).Text = Utility.GetShortString(deviceName, DEVICE_BUTTON_MAX_TEXT_LENGTH, 0)

            If deviceName.Length > DEVICE_BUTTON_MAX_TEXT_LENGTH Then
                Me.mDictDeviceButton(deviceId).tbToolTip = deviceName
            Else
                Me.mDictDeviceButton(deviceId).tbToolTip = ""
            End If
            Me.UpdateDeviceButtonLocation()
        End If

    End Sub

#Region "--- iOS设备连接事件Ex ---"

    Private Sub StartListen()
        Dim dt As Date = Now

        '要求安装完整版的iTunes.
        If Not Common.CheckiTunesInstall() Then
            Common.LogException("Check iTunes installed state", "StartListen of MainForm")
            Return
        End If
        '验证iTunes12.6插件是不是匹配
        frmWelcome.CheckiTunes()

        Debug.Print("Common.CheckiTunesInstall: " & Now.Subtract(dt).TotalMilliseconds)
        dt = Now

        ' Common.LogException("Initial Apple Device", "StartListen of MainForm")
        Dim deviceMgr As MobileDeviceManager = MobileDeviceManager.Instance()
        If Common.VerIs30() Then
            '注册设备连接处理事情
            AddHandler deviceMgr.ConnectionChanged, AddressOf OnDeviceConnectionChanged
            AddHandler deviceMgr.RecoveryConnectionChanged, AddressOf OnDeviceRecoveryConnectionChanged
            AddHandler deviceMgr.DFUConnectionChanged, AddressOf OnDeviceDFUConnectionChanged
            AddHandler deviceMgr.ConnectFailed, AddressOf OnDeviceConnectFailed
        Else
            '2.x版本在ProcForm已经注册了设备连接事件
            'Dim deviceMgr As MobileDeviceManager = MobileDeviceManager.Instance()
            'AddHandler deviceMgr.DetectDevice, AddressOf DetectDeviceEvent

            'AddHandler deviceMgr.ConnectionChanged, AddressOf OnConnectionChanged
            'AddHandler deviceMgr.RecoveryConnectionChanged, AddressOf OnRecoveryConnectionChanged
            'AddHandler deviceMgr.ConnectFailed, AddressOf OnConnectFailed
        End If


        deviceMgr.StartListen()
        Debug.Print("deviceMgr.StartListen: " & Now.Subtract(dt).TotalMilliseconds)
        dt = Now

        CoreUpdateHelper.Instance()

        Debug.Print(" CoreUpdateHelper.Instance: " & Now.Subtract(dt).TotalMilliseconds)
        dt = Now
    End Sub

    Public Sub OnDeviceConnectionChangedInvoke(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        Try
            Dim strDeviceID As String = args.Device.DeviceID '.ToLower()

            If args.Action = DeviceAction.kConnected Then
                Me.mDictAppleDevice(strDeviceID) = args.Device

                'args.Device.DeviceEnterRecovery()

                If Me.mDictUsbDevice.ContainsKey(strDeviceID) Then
                    'Common.Log(String.Format("connect to exsited ios: {0}", strDeviceID))
                    Dim devUsb As UsbDevice = Me.mDictUsbDevice(strDeviceID)
                    devUsb.MobileDevice = args.Device

                    '更新名称
                    If Me.mDictDeviceButton.ContainsKey(strDeviceID) Then
                        'Common.Log(String.Format("update ios device button: {0}", strDeviceID))
                        Me.mDictDeviceButton(strDeviceID).Text = Utility.GetShortString(args.Device.DeviceName, DEVICE_BUTTON_MAX_TEXT_LENGTH, 0)
                        Me.UpdateDeviceButtonLocation()

                        If args.Device.DeviceName.Length > DEVICE_BUTTON_MAX_TEXT_LENGTH Then
                            Me.mDictDeviceButton(strDeviceID).tbToolTip = args.Device.DeviceName
                        End If

                        Me.mDictDeviceButton(strDeviceID).Tag = devUsb
                        'Else
                        '    Common.Log(String.Format("fail to update ios device button: {0}", strDeviceID))
                    End If

                    '更新工具栏状态
                    If Me.mCurrentDevice IsNot Nothing AndAlso Me.mCurrentDevice.DeviceID.Equals(strDeviceID) Then
                        'Common.Log(String.Format("set item state enable: {0}", strDeviceID))
                        Me.SetItemsState(False)
                        'ElseIf Me.mCurrentDevice Is Nothing Then
                        '    Common.Log(String.Format("not set item state enable/ mCurrentDevice is nothing: {0}", strDeviceID))
                        'Else
                        '    Common.Log(String.Format("not set item state enable/ not current device: {0}/{1}", Me.mCurrentDevice.DeviceID, strDeviceID))
                    End If

                    Dim listForm As New List(Of frmDeviceBase)
                    Dim listExist As New List(Of frmDeviceBase)

                    For Each frm As Form In Application.OpenForms
                        '' 不执行监控窗体
                        If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                            Continue For
                        End If

                        If TypeOf frm Is frmDeviceBase AndAlso (Not TypeOf frm Is frmAndroidBase) Then
                            If frm.Name.StartsWith(strDeviceID, StringComparison.OrdinalIgnoreCase) Then
                                ' CType(frm, frmDeviceBase).OnConnect(args.Device)
                                listExist.Add(frm)

                            Else
                                If CType(frm, frmDeviceBase).iPhone Is Nothing AndAlso CType(frm, frmDeviceBase).mAndroid Is Nothing Then
                                    'frm.Name &= strDeviceID & frm.Name
                                    listForm.Add(frm)
                                End If
                            End If
                        End If
                    Next

                    For Each frm As frmDeviceBase In listExist
                        Try
                            'Common.Log(String.Format("send on connect to existing form: {0}", frm.Name))
                            frm.OnUsbConnect(devUsb)
                            frm.OnConnect(args.Device)
                        Catch ex As Exception
                        End Try
                    Next

                    For Each frm As frmDeviceBase In listForm
                        Try
                            'Common.Log(String.Format("send on connect to empty form: {0}", frm.Name))
                            frm.mUsbDev = devUsb
                            frm.OnConnect(args.Device)
                        Catch ex As Exception
                        End Try
                    Next
                Else

                    '' usb端口没有找到iOS设备
                    Dim usb As New UsbDevice(strDeviceID, "vid_05ac", args.Device, args.Device.DeviceType)
                    usb.ConnectMode = ConnectMode.USB_Install_Driver
                    usb.DeviceType = DeviceType.iOS

                    Me.OnUsbChangedInvoke(Nothing, New UsbDeviceEventArgs(usb, ConnectionType.Connected))
                End If

            ElseIf args.Action = DeviceAction.kDisConnected Then
                If Me.mDictAppleDevice.ContainsKey(strDeviceID) Then
                    Me.mDictAppleDevice.Remove(strDeviceID)
                End If

                If Me.mDictUsbDevice.ContainsKey(strDeviceID) Then
                    Dim usb As UsbDevice = Me.mDictUsbDevice(strDeviceID)
                    usb.DeviceType = DeviceType.iOS

                    Me.OnUsbChangedInvoke(Nothing, New UsbDeviceEventArgs(usb, ConnectionType.Disconnected))
                End If

            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub OnDeviceConnectionChanged(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        'Debug.Print("OnDeviceConnectionChanged:" & vbTab & args.Device.DeviceID)
        'Common.Log("OnDeviceConnectionChanged:" & vbTab & args.Device.DeviceID)
        Me.BeginInvoke(New EventHandler(Of EventArgs)(AddressOf OnBeginInvoke), sender, args)
    End Sub

    Public Sub OnDeviceRecoveryConnectionChangedBeginInvoke(ByVal sender As Object, ByVal args As RecoveryConnectionChangedEventArgs)
        Try
            Common.Log(String.Format("OnDeviceRecoveryConnectionChangedBeginInvoke action: {0}", args.Action.ToString()))
            Dim deviceId As String = args.Device.DeviceID '.ToLower()

            If args.Action = DeviceAction.kConnected Then
                Me.mDictAppleDevice(deviceId) = args.Device
                If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                    Common.Log(String.Format("OnDeviceRecoveryConnectionChangedBeginInvoke usb has connected: {0}", args.Device.DeviceID))
                    For Each frm As Form In Application.OpenForms
                        '' 不执行监控窗体
                        If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                            Continue For
                        End If

                        If TypeOf frm Is frmDeviceBase AndAlso (Not TypeOf frm Is frmAndroidBase) Then
                            CType(frm, frmDeviceBase).OnRecoverConnect(args.Device)
                        End If
                    Next
                Else
                    Common.Log(String.Format("OnDeviceRecoveryConnectionChangedBeginInvoke usb has not connected: {0}", args.Device.DeviceID))

                    Dim usb As New UsbDevice(deviceId, "vid_05ac", args.Device, args.Device.DeviceType)
                    Dim argUsb As New UsbDeviceEventArgs(usb, ConnectionType.Connected)

                    Me.OnUsbChangedInvoke(Nothing, argUsb)
                End If

            ElseIf args.Action = DeviceAction.kDisConnected Then
                If Me.mDictAppleDevice.ContainsKey(deviceId) Then
                    Me.mDictAppleDevice.Remove(deviceId)
                End If

                If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                    Dim usb As UsbDevice = Me.mDictUsbDevice(deviceId)
                    Dim argUsb As New UsbDeviceEventArgs(usb, ConnectionType.Disconnected)

                    Me.OnUsbChangedInvoke(Nothing, argUsb)
                End If

            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub OnDeviceRecoveryConnectionChanged(ByVal sender As Object, ByVal args As RecoveryConnectionChangedEventArgs)
        Debug.Print("OnDeviceRecoveryConnectionChanged:" & vbTab & args.Device.DeviceID)
        Me.BeginInvoke(New EventHandler(Of EventArgs)(AddressOf OnBeginInvoke), sender, args)
    End Sub

    Public Sub OnDeviceDFUConnectionChangedInvoke(ByVal sender As Object, ByVal args As DFUConnectionChangedEventArgs)
        Try
            Dim deviceId As String = args.Device.DeviceID

            If args.Action = DeviceAction.kConnected Then
                Me.mDictAppleDevice(deviceId) = args.Device
                If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                    Common.Log(String.Format("OnDeviceDFUConnectionChangedInvoke usb has connected: {0}", args.Device.DeviceID))
                    For Each frm As Form In Application.OpenForms
                        '' 不执行监控窗体
                        If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                            Continue For
                        End If

                        If TypeOf frm Is frmDeviceBase AndAlso (Not TypeOf frm Is frmAndroidBase) Then
                            CType(frm, frmDeviceBase).OnDFUConnect(args.Device)
                        End If
                    Next

                Else
                    Common.Log(String.Format("OnDeviceDFUConnectionChangedInvoke usb has not connected: {0}", args.Device.DeviceID))

                    Dim usb As New UsbDevice(deviceId, "vid_05ac", args.Device, args.Device.DeviceType)
                    Dim argUsb As New UsbDeviceEventArgs(usb, ConnectionType.Connected)

                    Me.OnUsbChangedInvoke(Nothing, argUsb)
                End If

            ElseIf args.Action = DeviceAction.kDisConnected Then
                If Me.mDictAppleDevice.ContainsKey(deviceId) Then
                    Me.mDictAppleDevice.Remove(deviceId)
                End If

                If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                    Dim usb As UsbDevice = Me.mDictUsbDevice(deviceId)
                    Dim argUsb As New UsbDeviceEventArgs(usb, ConnectionType.Disconnected)

                    Me.OnUsbChangedInvoke(Nothing, argUsb)
                End If

            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub OnDeviceDFUConnectionChanged(ByVal sender As Object, ByVal args As DFUConnectionChangedEventArgs)
        Debug.Print("OnDeviceDFUConnectionChanged:" & vbTab & args.Device.DeviceID)
        Me.BeginInvoke(New EventHandler(Of EventArgs)(AddressOf OnBeginInvoke), sender, args)
    End Sub

    Private mblnShowTrust As Boolean = False

    Public Sub OnDeviceConnectFailed(ByVal sender As Object, ByVal args As ConnectFailedEventArgs)
        Try

            Dim device As iPhoneDevice = sender
            Common.LogException("ConnectFailed: " & device.DeviceName & vbTab & device.ProductVersion & vbTab & CInt(args.ErrorCode).ToString())
            'Dim img As Image = Nothing
            Dim strMsg As String = String.Empty
            Select Case args.ErrorCode
                Case kAMDError.kAMDPasswordProtectedError
                    '"未能连接到设备 {0}，您必须在设备上输入密码，然后再尝试连接。"
                    strMsg = String.Format(Me.Language.GetString("Main.Message.ShouldTypePwd"), device.DeviceName)
                    If Val(device.ProductVersion.Substring(0, 1)) >= 7 Then
                        strMsg = String.Format(Me.Language.GetString("Main.Message.TrustPC"), device.DeviceName)
                        'img = My.Resources.pic_trust
                    End If
                Case kAMDError.kAMDTrustComputerError
                    '"未能连接到设备 {0}，您必须在设备上信任此电脑，然后再尝试连接。"
                    strMsg = String.Format(Me.Language.GetString("Main.Message.TrustPC"), device.DeviceName)
                    'img = My.Resources.pic_trust
                Case Else
                    strMsg = Me.Language.GetString("Main.Message.DeviceIsLocked")
                    'img = My.Resources.pic_trust
            End Select

            If Me.mblnShowTrust Then
                '当message在显示的时候直接重试，关闭message后如果设备上没有点信任还可以提示用户。
                args.Retry = True
                Utility.WaitSeconds(3)
                Return
            End If

            Me.mblnShowTrust = True
            '"您的设备已被密码锁定，iTong无法连接。您必须输入密码，然后才能继续使用。"
            If tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), _
                                 MessageBoxButtons.RetryCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, _
                                 My.Resources.pic_trust) = Windows.Forms.DialogResult.Retry Then
                args.Retry = True
            End If
            Me.mblnShowTrust = False
        Catch ex As Exception
        End Try
        'End If
    End Sub

    Public Sub OnAndroidConnectionChangedBeginInvoke(ByVal sender As Object, ByVal args As AndroidConnectEventArgs)
        Try
            If mDictUsbDevice Is Nothing OrElse mDictUsbDevice.Count = 0 Then Exit Sub
            If mDictDeviceButton Is Nothing OrElse mDictDeviceButton.Count = 0 Then Exit Sub

            Dim deviceID As String = args.Device.DeviceID '.ToLower()
            If Not Me.mDictUsbDevice.ContainsKey(deviceID) Then

                'Common.Log(String.Format("not contain in mDictUsbDevice: {0}", deviceID))
                'For Each strDevID As String In mDictUsbDevice.Keys
                '    Common.Log(strDevID)
                'Next

                Return
            End If

            If Me.mUsbDeviceMgr.DictBrokenDevices.Count > 0 Then
                If Me.mUsbDeviceMgr.DictBrokenDevices.ContainsKey(deviceID) Then Exit Sub
            End If

            If args.IsConnected Then
                '更新名称
                If Me.mDictDeviceButton.ContainsKey(deviceID) Then  ''AndAlso Not String.IsNullOrEmpty(args.Device.DeviceName)

                    Me.mDictDeviceButton(deviceID).Text = Utility.GetShortString(args.Device.DeviceName, DEVICE_BUTTON_MAX_TEXT_LENGTH, 0)

                    'If args.Device.DeviceName.Length > DEVICE_BUTTON_MAX_TEXT_LENGTH Then
                    Me.mDictDeviceButton(deviceID).tbToolTip = args.Device.DeviceName
                    'End If

                    Me.UpdateDeviceButtonLocation()
                End If

                ''' Noted by Utmost20140403
                ''' 此值已在frmAndroidSummary窗体中已改变
                'Me.mDictUsbDevice(deviceID).MobileDevice = args.Device

                '更新工具栏状态

                If Me.mCurrentDevice IsNot Nothing AndAlso _
                   String.Equals(Me.mCurrentDevice.DeviceID, deviceID, StringComparison.InvariantCultureIgnoreCase) Then

                    Me.mCurrentDevice = Me.mDictDeviceButton(deviceID).Tag
                    Me.LogCurrentDevice("OnAndroidConnectionChangedBeginInvoke")
                    'Common.Log(String.Format("connect to current android get tag: {0}", Me.mCurrentDevice.InfoString))
                    Me.mCurrentDevice.MobileDevice = args.Device
                    Me.SetItemsState(False)
                    'Common.Log(String.Format("connect to current android: {0}", Me.mCurrentDevice.InfoString))
                End If

                If mFrmAndroidFeedback IsNot Nothing AndAlso Not mFrmAndroidFeedback.IsDisposed Then
                    If String.Equals(mFrmAndroidFeedback.Name, deviceID, StringComparison.InvariantCultureIgnoreCase) OrElse _
                       mFrmAndroidFeedback.Name.Equals("mFrmAndroidFeedback") Then
                        mFrmAndroidFeedback.Close()
                    End If
                End If

                Dim listForm As New List(Of frmDeviceBase)
                For Each frm As Form In Application.OpenForms

                    '' 不执行监控窗体
                    If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                        Continue For
                    End If

#If IS_ITONG Then
                    If (TypeOf frm Is frmAndroidBase _
                        OrElse TypeOf frm Is frmMore OrElse TypeOf frm Is frmCommonApp) _
                        AndAlso CType(frm, frmDeviceBase).mDevice Is Nothing Then
#Else
                    If (TypeOf frm Is frmAndroidBase _
                            OrElse TypeOf frm Is frmCommonApp) _
                            AndAlso CType(frm, frmDeviceBase).mDevice Is Nothing Then
#End If


                        If Not TypeOf frm Is frmAndroidWelcome AndAlso _
                        (frm.Name.StartsWith(deviceID, StringComparison.OrdinalIgnoreCase)) AndAlso _
                        (frm.Parent Is Me.pnlContainer OrElse frm.Parent Is Nothing) Then
                            '由于OnConnect事件只触发一次，所以向除Welcome之外的所有属于该设备的窗体分发，不判断窗体是否已有mAndroid by luyanjun
                            listForm.Add(frm)
                        End If

                    End If

                    If TypeOf frm Is frmDownloaded Then
                        listForm.Add(frm)
                    End If
                Next

                For Each frm As frmDeviceBase In listForm
                    Try
                        'frm.mUsbDev = sender
                        'frm.OnUsbConnect(sender)

                        '' Noted by Utmost20140403
                        'frm.OnConnect(args.Device)  
                        frm.mUsbDev = Me.mDictDeviceButton(deviceID).Tag
                        frm.OnConnect(args.Device)
                    Catch ex As Exception
                    End Try
                Next

            Else
                If Me.mDictUsbDevice.ContainsKey(deviceID) Then
                    Dim usb As UsbDevice = Me.mDictUsbDevice(deviceID)
                    Dim argUsb As New UsbDeviceEventArgs(usb, ConnectionType.Disconnected)

                    Me.OnUsbChangedInvoke(Nothing, argUsb)
                End If

                If Me.mDictDeviceButton.ContainsKey(deviceID) Then
                    Me.mDictDeviceButton(deviceID).Text = ""
                End If
            End If
            'Common.Log(args.Device.ToString())
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnAndroidConnectionChangedBeginInvoke")
        End Try
    End Sub

    Public Sub OnAndroidConnectionChanged(ByVal sender As Object, ByVal args As AndroidConnectEventArgs)
        'Debug.Print("OnAndroidConnectionChanged:" & vbTab & args.Device.DeviceID & vbCrLf)
        Me.BeginInvoke(New EventHandler(Of AndroidConnectEventArgs)(AddressOf OnAndroidConnectionChangedBeginInvoke), sender, args)
    End Sub

#End Region

    Private mLockerInvoke As New Object()
    Private Sub OnBeginInvoke(ByVal sender As Object, ByVal e As EventArgs)
        SyncLock mLockerInvoke
            Try
                If TypeOf e Is UsbDeviceEventArgs Then
                    Me.OnUsbChangedInvoke(sender, CType(e, UsbDeviceEventArgs))

                ElseIf TypeOf e Is ConnectionChangedEventArgs Then
                    Me.OnDeviceConnectionChangedInvoke(sender, CType(e, ConnectionChangedEventArgs))

                ElseIf TypeOf e Is RecoveryConnectionChangedEventArgs Then
                    Me.OnDeviceRecoveryConnectionChangedBeginInvoke(sender, CType(e, RecoveryConnectionChangedEventArgs))

                ElseIf TypeOf e Is DFUConnectionChangedEventArgs Then
                    Me.OnDeviceDFUConnectionChangedInvoke(sender, CType(e, DFUConnectionChangedEventArgs))

                ElseIf TypeOf e Is AndroidStateEventArgs Then
                    Me.OnAndroidStateChangedInvoke(sender, CType(e, AndroidStateEventArgs))

                End If
                Me.SetWebboVisable()
            Catch ex As Exception
                Debug.Write(ex)
            End Try

        End SyncLock
    End Sub

    Private Sub OnAndroidDeviceConnected(ByVal sender As Object, ByVal e As AndroidDeviceEventArgs)

        Try
            If e.State = AndroidDeviceState.Online AndAlso e.Device IsNot Nothing AndAlso e.Device.Summary IsNot Nothing Then
                Dim listForms As New List(Of Form)
                For Each frm As Form In Application.OpenForms
                    listForms.Add(frm)
                Next

                For Each frm As Form In listForms
                    If TypeOf frm Is frmAndroidWelcome Then
                        CType(frm, frmAndroidWelcome).OnChangeState(WelcomState.DeamonConnected)
                        Exit For
                    End If
                Next

                listForms.Clear()
            ElseIf e.State <> AndroidDeviceState.Online Then
                Dim listForms As New List(Of Form)
                For Each frm As Form In Application.OpenForms
                    listForms.Add(frm)
                Next

                For Each frm As Form In listForms
                    If TypeOf frm Is frmAndroidBase Then
                        Dim dev As AndroidUsbEventArgs = CType(e, AndroidUsbEventArgs)
                        CType(frm, frmAndroidBase).OnAndroidDeviceConnect(dev.UsbDev, dev.State)
                        Exit For
                    End If
                Next
                listForms.Clear()
            End If
        Catch ex As Exception
            Debug.Write(ex)
        End Try

    End Sub


    Public Sub OnUsbChanged(ByVal sender As Object, ByVal e As UsbDeviceEventArgs)
        'If e.Device.DeviceType = DeviceType.iOS AndAlso e.ConnectionType = ConnectionType.Connected Then
        '    '如果且已经安装了iTunes，MobileDeviceManager未监听到iOS设备接入事件，则暂不响应设备USB新增事情
        '    If Common.CheckiTunesInstall() AndAlso Not Me.mDictAppleDevice.ContainsKey(e.Device.DeviceID) Then
        '        e.Cancel = True
        '        Return
        '    End If
        'End If
        'Common.Log("OnUsbChanged of Mainform:" & vbTab & e.Device.DeviceID & vbTab & e.Device.DeviceType.ToString())

        Try
            '' Modified by Utmost20141117
            '' 先执行类型判断，如果不是iOS则不用执行文件判断
            If e.Device IsNot Nothing AndAlso _
               e.Device.DeviceType = DeviceType.iOS AndAlso _
               File.Exists(iTunesHelper.iTunesDllPath) Then
                Common.Log("OnUsbChanged of Mainform iOS:" & vbTab & e.Device.DeviceID & vbTab & e.Device.DeviceType.ToString())
                Return
            End If

            Me.BeginInvoke(New EventHandler(Of EventArgs)(AddressOf OnBeginInvoke), sender, e)
        Catch opex As InvalidOperationException
            Debug.Write(opex)
        Catch ex As Exception
            Debug.Write(ex)
        End Try
    End Sub

    Public Sub OnUsbChangedInvoke(ByVal sender As Object, ByVal e As UsbDeviceEventArgs)
        Try
            '' Added by Utmost20140408
            '' 避免多次访问设备属性
            Dim currentDev As UsbDevice = e.Device
            Dim deviceId As String = String.Empty

            If Me.mDictDeviceButton Is Nothing OrElse _
               Me.mDictUsbDevice Is Nothing Then
                Exit Sub
            End If

            If currentDev Is Nothing Then

                Dim nCount As Integer = mDictUsbDevice.Count
                If nCount = 0 Then
                    Me.mUsbDeviceMgr.DictBrokenDevices.Clear()
                    Return
                End If


                If e.Device Is Nothing AndAlso _
                   e.ConnectionType = ConnectionType.Disconnected Then


                    Try

                        Dim aryDevices(nCount - 1) As UsbDevice
                        mDictUsbDevice.Values.CopyTo(aryDevices, 0)

                        For Each dev As UsbDevice In aryDevices

                            deviceId = dev.DeviceID

                            Me.mUsbDeviceMgr.DictBrokenDevices.Remove(deviceId)
                            Me.RemoveDevice(dev)
                            mDictUsbDevice.Remove(deviceId)

                        Next

                    Catch ex As Exception
                        Debug.Write(ex)
                    End Try

                End If

                Return
            End If

            deviceId = currentDev.DeviceID
            If Me.mUsbDeviceMgr.DictBrokenDevices.ContainsKey(deviceId) Then Exit Sub

            If e.ConnectionType = ConnectionType.OnlyConnetedAndroid Then
                '' 添加显示usb设备连接，侦测到当前的设备，但还没有侦测到设备状态（未安装/已安装驱动、调试模式等）
                ' Added by Utmost20140520
                'USBDeviceUploadHelper.UploadDeviceInfoThread(currentDev, AndroidDeviceFailStatus.UNKNOW)

                Exit Sub
            End If

            ''******************************************************

            If currentDev.ConnectMode = ConnectMode.USBConnectedADBDisconnected AndAlso _
               currentDev.DeviceID.Equals(currentDev.HardwareId) AndAlso _
               currentDev.DeviceID.Equals("None") AndAlso _
               e.ConnectionType = ConnectionType.USBDisconnected Then

                ''XP系统下，拔掉Sony设备时，偶尔发生无法清除界面，使用此方法触发——Added by Utmost200416
                Me.mDictUsbDevice.Clear()
                Me.mDictDeviceButton.Clear()
                Me.RemoveDevice(currentDev)

                Return
            End If

            If e.ConnectionType = ConnectionType.Unknown AndAlso currentDev.DeviceType = DeviceType.Android Then
                Dim cnnMode As ConnectMode = currentDev.ConnectMode
                If cnnMode = ConnectMode.UNKNOWN OrElse cnnMode = ConnectMode.USB OrElse cnnMode = ConnectMode.USB_None_Driver Then
                    '''''此处添加上传usb设备信息代码，避免重复收集-----Added by Utmost20140508
                    ''' TODO
                    'Common.Log(String.Format("OnUsbChangedInvoke usb connected: {0}", deviceId))

                    Me.AddDevice(currentDev)

                ElseIf cnnMode = ConnectMode.DisconnectedDevice Then
                    Me.RemoveDevice(currentDev)
                End If
                'Application.DoEvents()
            End If


            Select Case e.ConnectionType

                Case ConnectionType.USBConnected, ConnectionType.ADBConnected, ConnectionType.Connected, ConnectionType.USBDebugConnected
                    If Me.mDictUsbDevice.ContainsKey(deviceId) Then

                        '' 原来已连接上USB设备，然后改变模式重新连接ADB设备
                        If currentDev.DeviceType = DeviceType.Android Then
                            'Common.Log(String.Format("OnUsbChangedInvoke android adb connected: {0}", deviceId))

                            If Not (Me.mDictUsbDevice.ContainsKey(deviceId) AndAlso Me.mDictDeviceButton.ContainsKey(deviceId)) Then
                                Me.AddDevice(currentDev)
                            End If

                            '' 已经添加了设备
                            If Me.mDictUsbDevice(deviceId).MobileDevice IsNot Nothing AndAlso currentDev.MobileDevice Is Nothing Then
                                currentDev = Me.mDictUsbDevice(deviceId)
                            End If

                            OnUsbStateChangedInvoke(sender, New UsbDeviceEventArgs(currentDev, e.ConnectionType))
                        End If

                        Return
                    End If

                    If currentDev.DeviceType = DeviceType.iOS Then
                        'Common.Log(String.Format("OnUsbChangedInvoke ios connected: {0}", deviceId))


                        Dim dictForms As Dictionary(Of String, frmDeviceBase) = New Dictionary(Of String, frmDeviceBase)(StringComparer.InvariantCultureIgnoreCase)
                        For Each frm As Form In Application.OpenForms

                            '' 不执行监控窗体
                            If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                                Continue For
                            End If

                            If TypeOf frm Is frmAndroidBase OrElse TypeOf frm Is frmSite OrElse (Not TypeOf frm Is frmDeviceBase) Then
                                Continue For
                            End If

                            Dim frmApple As frmDeviceBase = frm
                            If frmApple.Name.StartsWith(deviceId, StringComparison.OrdinalIgnoreCase) Then

                            ElseIf frmApple.mUsbDev Is Nothing AndAlso frmApple.mDevice Is Nothing AndAlso frmApple.mDeviceRecovery Is Nothing AndAlso frmApple.mAndroid Is Nothing Then
                                Dim strKey As String = String.Format("{0}_{1}", deviceId, frmApple.Name)

                                If dictForms.ContainsKey(strKey) Then
                                    Continue For
                                End If

                                'Debug.Print(strKey)

                                frmApple.Name = strKey
                                frmApple.mUsbDev = currentDev
                                'frmApple.OnConnect(currentDev.MobileDevice)

                                dictForms(strKey) = frm
                            End If
                        Next

                        Me.AddDevice(currentDev)
                    Else
                        'Common.Log(String.Format("OnUsbChangedInvoke android connected: {0}", deviceId))

                        '新增安卓设备
                        Me.AddDevice(currentDev)

                        'Application.DoEvents()
                    End If

                Case ConnectionType.USBDisconnected, ConnectionType.ADBDisconnected, ConnectionType.Disconnected
                    If Not Me.mDictUsbDevice.ContainsKey(deviceId) Then
                        Return
                    End If

                    If currentDev.ConnectMode = ConnectMode.USB AndAlso _
                        (mCurrentDevice IsNot Nothing AndAlso _
                         mCurrentDevice.DeviceID.Equals(currentDev.DeviceID) AndAlso _
                         mCurrentDevice.DeviceType = DeviceType.Android) Then

                        Me.DeviceItem_Click(Me.mDictDeviceButton(currentDev.DeviceID), System.EventArgs.Empty)

                    Else
                        If currentDev.DeviceType = DeviceType.Android AndAlso _
                           currentDev.DeviceStatus = DeviceStatus.Disabled AndAlso _
                           currentDev.ConnectMode = ConnectMode.DisabledDevice Then
                            '' 安装了驱动，但设备处于警告状态——Added by Utmost20150416
                            For Each frm As Form In Application.OpenForms
                                If frm.Name.Contains("frmAndroidWelcome") Then
                                    CType(frm, frmAndroidWelcome).ShowNoneConnectedForm(True, QuestionConnecting.Showing3Questions)
                                    Exit For
                                End If
                            Next
                            Return
                        End If
                        '' Added by Utmost20140625
                        '' 关闭未信任的iOS设备 
                        If Me.mblnShowTrust Then
                            tbMessageBox.CloseMe()

                            Me.mblnShowTrust = False
                        End If

                        'If Not Me.mDictUsbDevice.ContainsKey(deviceId) Then
                        '    Return
                        'End If

                        Me.RemoveDevice(currentDev)
                    End If

                Case ConnectionType.NotTrustediOSDevice
                    ' 处理 iOS设备未信任
                    ' Added by Utmost20140414
                    'If Not Me.mDictUsbDevice.ContainsKey(deviceId) Then
                    '    Me.AddDevice(currentDev)
                    'End If

                    'tbMessageBox.Show("请插拔设备，然后信任之")
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnUsbChangedBeginInvoke")
        End Try
    End Sub

    Public Sub OnUsbStateChanged(ByVal sender As Object, ByVal e As UsbDeviceEventArgs)

        If e.Device Is Nothing Then Return

        If Me.mDictDeviceButton Is Nothing OrElse _
           Me.mDictUsbDevice Is Nothing Then
            Exit Sub
        End If

        '' Modified by Utmost20141117
        '' 先执行类型判断，如果不是iOS则不用执行文件判断
        If e.Device.DeviceType = DeviceType.iOS AndAlso File.Exists(iTunesHelper.iTunesDllPath) Then
            Common.Log("OnUsbStateChanged of Mainform iOS:" & vbTab & e.Device.DeviceID & vbTab & e.Device.DeviceType.ToString())
            Return
        End If

        ''' 此事件只是针对安卓设备
        Dim devId As String = e.Device.DeviceID
        If Me.mCurrentDevice Is Nothing Then
            Me.mCurrentDevice = e.Device
            Me.LogCurrentDevice("OnUsbStateChanged")
            Me.mDictUsbDevice(devId) = e.Device

            Me.BeginInvoke(New EventHandler(Of EventArgs)(AddressOf OnBeginInvoke), sender, e)
        Else
            ''' 判断改变设备是否在字典中且当前设备是否为安卓设备，如不是这则设为安卓设备
            Try
                If Not mDictUsbDevice.ContainsKey(devId) Then
                    mDictUsbDevice.Add(devId, e.Device)
                    If mCurrentDevice.DeviceType = DeviceType.iOS Then
                        mCurrentDevice = e.Device
                    End If
                End If

            Catch ex As Exception
            End Try

            Me.BeginInvoke(New EventHandler(Of UsbDeviceEventArgs)(AddressOf OnUsbStateChangedInvoke), sender, e)
        End If

    End Sub

    Private Function RepeatedDetectUsbDevice(ByVal sender As Object, ByRef currentDev As UsbDevice) As Boolean
        If currentDev.DeviceType = DeviceType.Android Then

            ''' Added by Utmost20141210
            Try
                Dim devMonitor As AndroidDeviceMonitor = Nothing
                If sender IsNot Nothing AndAlso TypeOf sender Is AndroidDeviceMonitor Then
                    devMonitor = CType(sender, AndroidDeviceMonitor)
                End If

                If devMonitor IsNot Nothing Then

                    If devMonitor.UsbPluginDevices.Count = 0 Then
                        Return False
                    End If

                    If currentDev.ConnectMode <> ConnectMode.USB_Install_Driver Then
                        Try
                            For Each devItem As UsbDevice In devMonitor.UsbPluginDevices
                                If devItem.DeviceType = DeviceType.Android Then
                                    Dim curHid As String = currentDev.HardwareId
                                    Dim itmHid As String = devItem.HardwareId

                                    If currentDev.DeviceID.Equals(devItem.DeviceID, StringComparison.InvariantCultureIgnoreCase) AndAlso _
                                       Not curHid.Equals(itmHid, StringComparison.InvariantCultureIgnoreCase) Then

                                        If itmHid.Length > curHid.Length Then
                                            currentDev = devItem
                                            Exit For
                                        End If
                                    End If
                                End If
                            Next
                        Catch ex As Exception
                            Debug.Write(ex)
                        End Try
                    End If

                End If
            Catch
            End Try
            ''''''''''''''''''''''''''''''''''''''''''
        End If

        Return True
    End Function


    Public Sub OnUsbStateChangedInvoke(ByVal sender As Object, ByVal e As UsbDeviceEventArgs)
        Try
            '' Added by Utmost20140408
            '' 避免多次访问设备属性
            Dim currentDev As UsbDevice = e.Device

            If currentDev Is Nothing OrElse mDictUsbDevice Is Nothing Then
                Exit Sub
            End If

            '如果接多个设备,打开助手当前设备不进行切换，不然会混乱。
            If Me.mCurrentDevice Is Nothing OrElse _
               (Me.mCurrentDevice IsNot Nothing AndAlso currentDev.DeviceID.Equals(Me.mCurrentDevice.DeviceID)) Then

                Me.mCurrentDevice = currentDev
                Me.LogCurrentDevice("OnUsbStateChangedInvoke")

            End If

            If Me.mCurrentDevice.DeviceType <> currentDev.DeviceType Then
                Exit Sub
            End If

            Dim deviceId As String = currentDev.DeviceID

            'If Not RepeatedDetectUsbDevice(sender, currentDev) Then
            '    Exit Sub
            'End If

            'Application.DoEvents()

            Select Case e.ConnectionType
                Case ConnectionType.USBConnected, ConnectionType.ADBConnected, ConnectionType.Connected, ConnectionType.USBDebugConnected
                    If currentDev.DeviceType = DeviceType.Android Then

                        Dim arrEmptyDevFrms As New List(Of frmDeviceBase)
                        Dim arrExistingDevFrms As New List(Of frmDeviceBase)
                        Dim listForms As New List(Of frmDeviceBase)

                        For Each frm As Form In Application.OpenForms
                            '' 不执行监控窗体
                            If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                                Continue For
                            End If

#If IS_ITONG Then
                            If (TypeOf frm Is frmAndroidBase _
                                 OrElse TypeOf frm Is frmMore OrElse TypeOf frm Is frmCommonApp) Then
#Else
                            If (TypeOf frm Is frmAndroidBase OrElse TypeOf frm Is frmCommonApp) Then
#End If

                                If frm.Name.Contains(deviceId) Then
                                    arrExistingDevFrms.Add(frm)
                                Else
                                    arrEmptyDevFrms.Add(frm)
                                End If

                            End If
                        Next

                        If arrExistingDevFrms.Count > 0 Then
                            For Each frm As Form In arrExistingDevFrms
                                listForms.Add(frm)
                            Next
                        Else
                            For Each frm As Form In arrEmptyDevFrms
                                listForms.Add(frm)
                            Next
                        End If

                        Dim originDev As UsbDevice = Nothing
                        If mDictUsbDevice.ContainsKey(deviceId) Then
                            originDev = mDictUsbDevice(deviceId)
                        End If

                        ''' Added by Utmost20140321
                        ' 保存接入设备状态发生改变的值
                        If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                            Me.mDictUsbDevice(deviceId) = currentDev
                        Else
                            Me.mDictUsbDevice.Add(deviceId, currentDev)
                        End If

                        If Me.mDictDeviceButton.ContainsKey(deviceId) Then
                            Me.mDictDeviceButton(deviceId).Tag = currentDev
                        End If
                        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

                        Dim cnntFlag As Boolean = True
                        If originDev IsNot Nothing Then
                            If originDev.ConnectMode = ConnectMode.USB_None_Debug OrElse currentDev.ConnectMode = ConnectMode.USB_None_Debug Then

                                '' usb调试窗体没有，则继续显示
                                Me.SetItemsState(True)
                                DispatchNoDebugMessage(listForms, originDev, currentDev)

                                cnntFlag = False
                            Else
                                Dim bCnnct As Boolean = (originDev.ConnectMode = ConnectMode.USB_None_Driver AndAlso _
                                                         currentDev.ConnectMode = ConnectMode.USB_None_Driver AndAlso _
                                                         originDev.HardwareId.Equals(currentDev.HardwareId, StringComparison.InvariantCultureIgnoreCase))
                                If Not bCnnct Then
                                    If originDev.DeviceID.Equals(currentDev.DeviceID) AndAlso _
                                       currentDev.ConnectMode = ConnectMode.USB_Install_Driver AndAlso _
                                       originDev.ConnectMode = ConnectMode.USB_None_Driver Then

                                        For Each frm As frmDeviceBase In listForms
                                            If TypeOf frm Is frmAndroidWelcome Then
                                                CType(frm, frmAndroidWelcome).StopDriverDownload(originDev)
                                                Exit For
                                            End If
                                        Next
                                    End If
                                End If
                            End If
                        End If

                        If cnntFlag Then

                            'Dim dictForms As New Dictionary(Of String, Form)

                            Try
                                'If _dictConnectedForms.ContainsKey(deviceId) Then
                                '    dictForms = _dictConnectedForms(deviceId)
                                'End If

                                For Each frm As frmDeviceBase In listForms

                                    'If Not dictForms.ContainsKey(frm.Name) Then
                                    'If currentDev.MobileDevice IsNot Nothing Then
                                    '    dictForms.Add(frm.Name, frm)
                                    'End If

                                    frm.OnUsbStateConnect(currentDev)
                                    'End If

                                Next
                                '_dictConnectedForms(deviceId) = dictForms

                                Me.SetItemsState(False)
                            Catch ex As Exception

                            End Try


                        End If

                        ''' 显示当前首先设备
                        'If Me.mCurrentDevice IsNot Nothing Then
                        '    deviceId = Me.mCurrentDevice.DeviceID '.ToLower()
                        '    DeviceItem_Click(Me.mDictDeviceButton(deviceId), EventArgs.Empty)
                        'End If

                    ElseIf currentDev.DeviceType = DeviceType.iOS Then
                        '' 处理未信任iOS设备到已信任状态改变
                        '' Added by Utmost20140414
                        If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                            Me.mDictUsbDevice(deviceId) = currentDev
                            If Me.mDictDeviceButton.ContainsKey(deviceId) Then
                                Me.mDictDeviceButton(deviceId).Tag = currentDev
                            End If
                        Else
                            Me.AddDevice(currentDev)
                        End If

                    Else
                        Common.LogException(deviceId & vbTab & currentDev.DeviceType.ToString() & vbTab & currentDev.ConnectMode.ToString())
                    End If

                Case ConnectionType.USBDisconnected, ConnectionType.ADBDisconnected, ConnectionType.Disconnected

                    If currentDev.DeviceType = DeviceType.Android Then

                        If currentDev.ConnectMode = ConnectMode.USB Then
                            If (mCurrentDevice IsNot Nothing AndAlso _
                                mCurrentDevice.DeviceID.Equals(currentDev.DeviceID) AndAlso _
                                mCurrentDevice.DeviceType = DeviceType.Android) Then

                                Me.DeviceItem_Click(Me.mDictDeviceButton(currentDev.DeviceID), System.EventArgs.Empty)

                                'Added by Utmost20140627
                                'TODO 添加 adb无法连接界面
                                '同一台设备的时候，定时监控触发的事件到此

                                'Dim reConnectedHelper As frmAndroidNoConnectedHelperSimple = New frmAndroidNoConnectedHelperSimple(False)
                                'Utility.AddForm2Panel(reConnectedHelper, Me.pnlContainer, False)
                                'reConnectedHelper.TitleText = "设备连接异常，重新拔插USB线"
                                'reConnectedHelper.SubTitleText = "" ''重新拔插USB线，然后再次连接电脑和设备。"
                                'reConnectedHelper.HelpImg = My.Resources.welcome_reconnect")
                                'reConnectedHelper.Visible = True
                                'reConnectedHelper.BringToFront()

                            Else
                                ' 不同设备时候
                                If Not Me.mDictUsbDevice.ContainsKey(deviceId) Then
                                    Me.AddDevice(currentDev)
                                End If
                            End If
                        ElseIf currentDev.ConnectMode = ConnectMode.DisabledDevice AndAlso currentDev.DeviceStatus = DeviceStatus.Disabled Then
                            '' 安装了驱动，但设备处于警告状态 —— Added by Utmost20150416
                            For Each frm As Form In Application.OpenForms
                                If TypeOf frm Is frmAndroidWelcome Then
                                    CType(frm, frmAndroidWelcome).ShowNoneConnectedForm(True, QuestionConnecting.Showing3Questions)
                                    Exit For
                                End If
                            Next
                        End If
                    Else
                        Me.RemoveDevice(currentDev)
                    End If
                Case ConnectionType.USBConnectingADBDisconnect, ConnectionType.NotDaemonOfAndroid, ConnectionType.NotUnauthorizedOfAndroid, ConnectionType.OnlyConnetedAndroid

                    If currentDev.DeviceType = DeviceType.Android Then
                        For Each frm As Form In Application.OpenForms
                            If TypeOf frm Is frmAndroidWelcome AndAlso frm.Visible Then
                                Dim frmWel As frmAndroidWelcome = CType(frm, frmAndroidWelcome)
                                frmWel.m_connectType = e.ConnectionType
                                frmWel.OnUsbStateConnect(currentDev)
                                Exit For
                            End If
                        Next
                    End If


            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnUsbChangedBeginInvoke")
        End Try
    End Sub


    Private Sub DispatchNoDebugMessage(ByVal list As List(Of frmDeviceBase), ByVal originDev As UsbDevice, ByVal currentDev As UsbDevice)

        Dim androidWelcomeFrm As frmAndroidWelcome = Nothing
        For Each frm As frmDeviceBase In list
            If TypeOf frm Is frmAndroidWelcome Then
                androidWelcomeFrm = frm
            Else
                frm.OnDisconnect(originDev.MobileDevice)
                frm.OnUsbConnect(currentDev)
            End If
        Next
        '''frmAndroidWelcome最后触发，防止调试模式教程窗口阻塞其他窗体响应事件 by luyanjun
        If androidWelcomeFrm IsNot Nothing Then
            androidWelcomeFrm.OnDisconnect(originDev.MobileDevice)
            androidWelcomeFrm.OnUsbConnect(currentDev)
        End If
    End Sub

    Public Sub OnAndroidStateChanged(ByVal sender As Object, ByVal e As AndroidStateEventArgs)
        Me.BeginInvoke(New EventHandler(Of EventArgs)(AddressOf OnBeginInvoke), sender, e)
    End Sub

    Public Sub OnAndroidStateChangedInvoke(ByVal sender As Object, ByVal e As AndroidStateEventArgs)
        Try
            Dim listForms As New List(Of frmAndroidBase)
            For Each frm As Form In Application.OpenForms

                If TypeOf frm Is frmAndroidBase Then
                    If frm.Name.StartsWith(e.Device.DeviceID, StringComparison.InvariantCultureIgnoreCase) Then
                        listForms.Add(frm)
                    End If
                End If

            Next

            Application.DoEvents()

            For Each frm As frmAndroidBase In listForms
                frm.OnAndroidStateChanged(e)
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnAndroidStateChangedInvoke")
        End Try
    End Sub

    Private Function CreateToolItemEx(ByVal strName As String, _
                           ByVal strText As String, _
                           ByVal fntText As Font, _
                           ByVal icon As Image, _
                           ByVal arrType() As Type, _
                           ByVal defaultSize As Size, _
                           Optional ByVal iconReadOnly As Image = Nothing, _
                           Optional ByVal menuClick As EventHandler = Nothing, _
                           Optional ByVal args As Object = Nothing) As tbRadioButton

        Dim item As New tbRadioButton()
        With item
            .Name = strName
            .Text = strText
            .tbIconImage = icon
            .Size = New Size(defaultSize.Width, defaultSize.Height + 1)
            .tbIconImageState = ImageState.OneState
            .tbIconPlaceText = 1
            .tbIconImageAlign = ContentAlignment.MiddleCenter

            .tbImageCheckedMouseLeave = My.Resources.new_tool_select
            .tbImageCheckedMouseDown = My.Resources.new_tool_select
            .tbImageCheckedMouseHover = My.Resources.new_tool_select
            .tbImageUnCheckedMouseHover = My.Resources.new_tool_hover

            .ForeColor = Color.FromArgb(255, 255, 254)
            .ForeColor_Checked = Color.White
            .tbIconReadOnly = iconReadOnly
            .tbIconReadOnlyLight = Nothing 'My.Resources.new_top_light
            .BackColor = Color.Transparent
            .tbIconImageAlign = ContentAlignment.TopCenter

            Dim strTypes As String = String.Empty
            For Each t As Type In arrType
                If t Is Nothing Then
                    strTypes &= "AndroidNothing&"
                Else
                    strTypes &= t.FullName & "&"
                End If
            Next
            strTypes = strTypes.Trim("&"c)
            .AccessibleDescription = strTypes

            If args IsNot Nothing Then
                .Tag = args
            End If

            If iconReadOnly IsNot Nothing Then
                .tbReadOnly = True
            End If

            'If menuClick IsNot Nothing Then
            '    AddHandler .MoreClick, menuClick
            '    .tbIconMore = True
            '    .tbAutoSizeEx = True
            '    .Padding = New Padding(3, 15, 3, 5)
            'Else
            .tbIconMore = False
            .tbAutoSizeEx = True
            .Padding = New Padding(0, 15, 0, 5)
            'End If
            .MinimumSize = defaultSize

            .Font = fntText   ''  Common.CreateFont("Arial", 9, FontStyle.Regular)
            .TextAlign = ContentAlignment.MiddleCenter
        End With

        AddHandler item.CheckedChanged, AddressOf ToolItem_ClickEx

        Me.Controls.Add(item)
        Me.mListItems.Add(item)
        'Me._dictItems.Add(item.Name, item)

        Return item
    End Function

    Private Function GetDeviceName(ByVal dev As UsbDevice)
        Dim strName As String = "iOS"
        If dev Is Nothing Then
            Return strName
        End If
        If dev.MobileDevice IsNot Nothing Then
            strName = dev.MobileDevice.DeviceName
        ElseIf Not String.IsNullOrEmpty(dev.DeviceName) Then
            strName = dev.DeviceName
        End If

        If String.IsNullOrEmpty(strName) Then
            strName = dev.DeviceType.ToString()
        End If
        Return strName
    End Function

    Private Function CreateDeviceButton(ByVal dev As UsbDevice, ByVal fntText As Font) As tbCheckButton
        Dim item As New tbCheckButton()
        Dim strName As String = dev.DeviceName

        With item
            .Size = New Size(150, Me.mDeviceButtonHeight)
            .Padding = New Padding(8, 0, 8, 3)

            .Font = fntText 'Common.CreateFont("Arial", 8, FontStyle.Regular)
            .Name = dev.DeviceID

            .MinimumSize = New Size(Me.mDeviceButtonWidthMin, Me.mDeviceButtonHeight)
            .MaximumSize = New Size(Me.mDeviceButtonWidthMax, Me.mDeviceButtonHeight)

            'If dev.MobileDevice IsNot Nothing Then
            '    strName = dev.MobileDevice.DeviceName
            'ElseIf Not String.IsNullOrEmpty(dev.DeviceName) Then
            '    strName = dev.DeviceName
            'End If

            'If String.IsNullOrEmpty(strName) Then
            '    strName = dev.DeviceType.ToString()
            'End If
            strName = Me.GetDeviceName(dev)

            .Text = Utility.GetShortString(strName, DEVICE_BUTTON_MAX_TEXT_LENGTH, 0)
            If strName.Length > DEVICE_BUTTON_MAX_TEXT_LENGTH Then
                .tbToolTip = strName
            End If


            If dev.DeviceType = DeviceType.Android Then
                .tbIconImage = My.Resources.new_device_airdroid_gray
                .tbIconMouseHover = My.Resources.new_device_airdroid
            Else
                .tbIconImage = My.Resources.new_device_ios_gray
                .tbIconMouseHover = My.Resources.new_device_ios
            End If

            .tbIconImageState = ImageState.OneState
            .tbIconPlaceText = 1
            .tbIconImageAlign = ContentAlignment.MiddleLeft

            .ForeColor = Color.White
            .BackColor = Color.Transparent
            .tbIconImageAlign = ContentAlignment.TopCenter
            .Cursor = Cursors.Hand

            .Tag = dev

            Dim blnFlash As Boolean = False
            '如果当前正在刷机，并且用户选择了越狱节点，则不跳转到首页， 否则设备连接的时候都触发跳转到首页，以便可以触发summaryform的连接行为              

#If IS_ITONG Then
            If dev.DeviceType = DeviceType.iOS AndAlso Me._CurrentAccessibleDescription.Equals("Jailbreak") AndAlso dev.MobileDevice IsNot Nothing AndAlso FlashHelper.GetInstance(dev.MobileDevice).IsDoFlash Then
                blnFlash = True
            End If

            '视频影片节点，链接设备时不跳转到首页
            If Me._CurrentAccessibleDescription.Equals("Video") AndAlso ServerIniSetting.GetShowLiveVideo() Then
                blnFlash = True
            End If

#End If

            Try
                If blnFlash Then
                    .AccessibleDescription = _CurrentAccessibleDescription
                Else
                    .AccessibleDescription = "Welcome"
                    _CurrentAccessibleDescription = "Welcome"
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "CreateDeviceButton")
            End Try
            .tbIconMore = False

            .tbTextAlign = ContentAlignment.MiddleLeft
            .tbIconImageAlign = ContentAlignment.MiddleLeft
            .tbAutoSizeEx = True
        End With

        AddHandler item.Click, AddressOf DeviceItem_Click
        AddHandler item.CloseClick, AddressOf DeviceItem_CloseClick
        'AddHandler item.MouseEnter, AddressOf DeviceItem_MouseEnter
        'AddHandler item.MouseLeave, AddressOf DeviceItem_MouseLeave

        Return item
    End Function

    Private Sub SetClick(ByVal device As IDevice, ByVal strName As String, ByVal type As DeviceType)
        Try
            If Me.mblnUserClick = False Then
                Return
            End If

            If type = DeviceType.Android Then
                If strName.Contains("Welcome") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.AndroidSummary)
                ElseIf strName.Contains("App") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.AndroidApp)
                ElseIf strName.Contains("Music") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.AndroidMsuic)
                ElseIf strName.Contains("Photo") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.AndroidPhoto)
                ElseIf strName.Contains("Video") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.AndroidMovie)
                ElseIf strName.Contains("File") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.AndroidFile)
                ElseIf strName.Contains("More") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.AndroidMore)
                End If
            Else
                If strName.Contains("Welcome") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.Summary)
                ElseIf strName.Contains("App") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.App)
                ElseIf strName.Contains("Music") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.Music)
                ElseIf strName.Contains("Photo") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.Photo)
                ElseIf strName.Contains("Video") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.Movie)
                ElseIf strName.Contains("File") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.File)
                ElseIf strName.Contains("EBook") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.EBook)
                ElseIf strName.Contains("Jailbreak") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.Jailbreak)
                ElseIf strName.Contains("More") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.More)
                ElseIf strName.Contains("Site") Then
                    ActionCollectHelper.ClickTopAccition(device, ModelKey.Site)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "MainForm_SetClick")
        End Try
    End Sub

    Private _adbSummary As New Dictionary(Of String, AndroidDeviceSummary)(StringComparer.InvariantCultureIgnoreCase)
    Public WriteOnly Property DeviceSummary(ByVal adbDev As AndroidDevice) As AndroidDeviceSummary
        Set(ByVal value As AndroidDeviceSummary)
            Dim devId As String = adbDev.DeviceID
            _adbSummary(devId) = value
        End Set
    End Property

    '有发现在用户的环境里面可能在线程中触发了click事件，所以需要invoke回到
    Private Sub ToolItem_ClickEx(ByVal sender As Object, ByVal e As EventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(AddressOf ToolItem_ClickExInVoke), sender, e)
        Else
            Me.ToolItem_ClickExInVoke(sender, e)
        End If
    End Sub

    Private Sub ToolItem_ClickExInVoke(ByVal sender As Object, ByVal e As EventArgs)
        Dim dt As Date = Now

        Try
            Dim item As tbRadioButton = sender

            If item Is Nothing Then
                'item = Me.Controls("Welcome")
                item = Me.Controls(_CurrentAccessibleDescription)
            End If


            '' Added by Utmost20140604
            '' 加载图片、音乐过程中拔掉设备后，item可能为Nothing
            If item Is Nothing Then
                Return
            End If

            _currentItem = item
            If mCurrentDevice IsNot Nothing Then
                _currentItem.tbTag = mCurrentDevice.DeviceType
            Else
                _currentItem.tbTag = Nothing
            End If

            If e IsNot Nothing AndAlso (Not item.Checked OrElse item.tbReadOnly) Then
                item.Checked = False   '防止按钮失效 by luyanjun
                Return
            End If

            '记录当前按下的按钮。
            If Me.mClickItem Is Nothing Then
                Me.mClickItem = item

                _CurrentAccessibleDescription = item.Name
            End If

            If Not Me.mClickItem.Name.Equals(item.Name) Then
                item.Checked = False
                Return
            End If
            'dt = Now
            Me.CreateWebForm()

            'Debug.Print("CreateWebForm " & Now.Subtract(dt).TotalMilliseconds)
            'dt = Now

            Dim frmItem As Form = Nothing
            Dim strTypeName As String = String.Empty
            Dim strCtlName As String = String.Empty

            Dim arrFormName() As String = item.AccessibleDescription.Split("&"c)
            Dim deviceId As String = String.Empty
            Dim devType As DeviceType = DeviceType.Unknown

            If Me.mCurrentDevice Is Nothing OrElse Not Me.mCurrentDevice.IsConnectedUsb Then

                strTypeName = arrFormName(0)
                strCtlName = String.Format("{0}_{1}", strTypeName, item.Name)

            Else

                deviceId = Me.mCurrentDevice.DeviceID
                devType = Me.mCurrentDevice.DeviceType

                If devType = DeviceType.iOS Then
                    strTypeName = arrFormName(1)
                ElseIf devType = DeviceType.Android Then
                    strTypeName = arrFormName(2)
                End If

                strCtlName = String.Format("{0}_{1}_{2}", deviceId, strTypeName, item.Name)
            End If

            Dim frmType As Type = System.Type.GetType(strTypeName, True, False)
            If frmType Is Nothing Then
                Return
            End If

            '2015-08-12 by chenbihai------------- 装机大师  激活教程   数据统计
#If IS_ITONG_ZJ Then
            If frmType Is GetType(frmLogin) Then
                '自动登录装机大师后台
                Dim strUrl As String = String.Format("http://zj.tongbu.com?from=client&usertoken={0}", PluginLogin.Instance().Token)
                Common.OpenExplorer(strUrl)
                Return
            End If

            If frmType Is GetType(frmActivationHelp) Then
                Dim frm As New frmActivationHelp(frmActivationHelp.View.CourseActive)
                frm.Show(Me)
                Return
            End If
#End If
            '--------------

            Dim strKey As String = strCtlName ''String.Format("{0}_{1}_{2}", strTypeName, item.Name)

            'For Each ctl As Control In Me.pnlContainer.Controls
            '    Debug.Print(ctl.Name)
            'Next

            For Each ctl As Control In Me.pnlContainer.Controls
                If Not (TypeOf ctl Is Form) Then
                    Continue For
                End If

                If mCurrentDevice IsNot Nothing AndAlso ctl.Name = strCtlName Then
                    frmItem = ctl
                    frmItem.Name = strCtlName
                    Exit For

                ElseIf mCurrentDevice Is Nothing AndAlso ctl.Name.Equals(strKey) Then
                    frmItem = ctl
                    frmItem.Name = strCtlName
                    Exit For

                End If
            Next
            'Common.LogException(Now.Subtract(dt).TotalMilliseconds, "Timespan1:")
DO_NEXT:
            If frmItem Is Nothing Then
                Try
                    Dim args As New List(Of Object)
                    args.Add(Me)

                    If Me.mCurrentDevice IsNot Nothing Then

                        If mCurrentDevice.DeviceType = DeviceType.Android AndAlso _
                           mCurrentDevice.MobileDevice IsNot Nothing AndAlso _
                           _adbSummary IsNot Nothing Then

                            Dim devId As String = mCurrentDevice.DeviceID
                            If _adbSummary.ContainsKey(devId) Then
                                CType(mCurrentDevice.MobileDevice, AndroidDevice).Summary = _adbSummary(devId)
                            End If
                            CType(mCurrentDevice.MobileDevice, AndroidDevice).DeviceHardwareId = mCurrentDevice.HardwareId
                        End If

                        '如果当前实例化为frmWelcome，否需要添加设备参数到参数列表
                        If frmDeviceBase.CheckResponse(Me.mCurrentDevice) OrElse frmType Is GetType(frmWelcome) OrElse frmType Is GetType(frmSite) Then
                            args.Add(Me.mCurrentDevice.MobileDevice)
                        Else
                            args.Add(Nothing)
                        End If

                    Else
                        args.Add(Nothing)
                    End If

                    If item.Name = "Welcome" Then
                        args.Add(Me.mCurrentDevice)
                    End If

                    If item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is Object() Then
                        For Each arg As Object In item.Tag
                            args.Add(arg)
                        Next
                    End If

                    frmItem = Activator.CreateInstance(frmType, args.ToArray())
                    'Common.LogException(Now.Subtract(dt).TotalMilliseconds, "Timespan2:")
                Catch ex As Exception
                    Common.LogException(ex.ToString())

                    Try
                        frmItem = Activator.CreateInstance(frmType)
                    Catch ex1 As Exception
                        Common.LogException(ex1.ToString())
                    End Try
                End Try

                If frmItem Is Nothing Then
                    '' 有时候拔插安卓设备的时候发生frmItem为Nothing——Added by Utmost20150513
                    Exit Try
                End If


                frmItem.Name = strCtlName
                frmItem.AccessibleDescription = strTypeName       '' 关联当前显示的窗体

                If Me.mCurrentDevice IsNot Nothing Then
                    frmItem.Tag = Me.mCurrentDevice.DeviceType
                Else
                    frmItem.Tag = DeviceType.iOS
                End If
                'Common.LogException(Now.Subtract(dt).TotalMilliseconds, "Timespan3:")
                'dt = Now
                'Common.LogException(Now.Subtract(dt).TotalMilliseconds, "Timespan4:")
                CType(frmItem, tbBaseGuiForm).tbGuiBackground = Nothing
                Utility.AddForm2Panel(frmItem, Me.pnlContainer, True)
                'Common.LogException(Now.Subtract(dt).TotalMilliseconds, "Timespan5:")
                Debug.Print("clickover1 " & Now.Subtract(dt).TotalMilliseconds)
                '更新USB状态数据
                If TypeOf frmItem Is frmDeviceBase AndAlso Me.mCurrentDevice IsNot Nothing Then
                    Me.mListFormCheckInit.Add(frmItem.Name)
                    CType(frmItem, frmDeviceBase).OnUsbConnect(Me.mCurrentDevice)
                End If
            ElseIf frmItem.Name.Equals(strCtlName) Then

                If Me.mCurrentDevice IsNot Nothing AndAlso Not Me.mListFormCheckInit.Contains(frmItem.Name) Then '
                    Me.mListFormCheckInit.Add(frmItem.Name)

                    frmItem.Tag = Me.mCurrentDevice.DeviceType
                    frmItem.AccessibleDescription = strTypeName   '' 关联当前显示的窗体

                    If TypeOf frmItem Is frmDeviceBase Then
                        CType(frmItem, frmDeviceBase).OnUsbConnect(Me.mCurrentDevice)

                        If Me.mCurrentDevice.MobileDevice IsNot Nothing Then
                            CType(frmItem, frmDeviceBase).CheckInit()
                        End If
                    End If
                End If
            End If


            'If sender IsNot Nothing AndAlso mCurrentDevice Is Nothing Then  '防止被阻塞的frmWelcome线程重新执行后关闭设备未连接界面 by luyanjun
            '    GoTo DO_EXIT
            'End If

            'Debug.Print(Now.ToString() & vbTab & item.Text)

            frmItem.Visible = True
            frmItem.BringToFront()
            'Common.LogException(Now.Subtract(dt).TotalMilliseconds, "Timespan6:")
            Debug.Print("clickover " & Now.Subtract(dt).TotalMilliseconds)
            dt = Now

            '设置其它窗体不可用，点击事件不会交叉
            For Each ctl As Control In Me.pnlContainer.Controls
                If TypeOf ctl Is Form AndAlso ctl IsNot frmItem Then
                    If TypeOf ctl Is frmDeviceBase Then
                        CType(ctl, frmDeviceBase).Visible = False
                    Else
                        ctl.Visible = False
                    End If
                End If
            Next

            If TypeOf frmItem Is frmDeviceBase Then
                CType(frmItem, frmDeviceBase).OnReSelectSiteNode(Me.mCurrentDevice)
            End If

            If Me.mCurrentDevice IsNot Nothing AndAlso mDictUsbDevice.Count > 0 Then
                If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                    Dim btn As tbCheckButton = Me.mDictDeviceButton(deviceId)
                    btn.AccessibleDescription = item.Name
                End If
            End If

            Me.mSelectKey = CType(frmItem, tbBaseGuiForm).FunctionMappingKey

            Dim strText As String = String.Empty
            If Me.mDictFuncInfoMsg.ContainsKey(Me.mSelectKey) Then
                Dim dictText As StringDictionary = Me.mDictFuncInfoMsg(Me.mSelectKey)
                If dictText.ContainsKey(strCtlName) Then
                    strText = dictText(strCtlName)
                ElseIf dictText.ContainsKey(Me.mSelectKey.ToString()) Then
                    strText = dictText(Me.mSelectKey.ToString())
                End If
            End If

            Me.UpdateLabelText(strText)
            'Debug.Print(frmItem.Visible.ToString() & vbTab & frmItem.Location.ToString() & vbTab & frmItem.Size.ToString())

            If item.Checked Then
                Select Case item.Name
                    Case "More"
                        IniSetting.SetMoreShowNew()
                        item.tbShowNew = False

                    Case "Site"
                        IniSetting.SetWebSiteShowNew()
                        item.tbShowNew = False

                    Case "Video"
                        IniSetting.SetShowVideoNew()
                        item.tbShowNew = False

                    Case "Photo"
                        IniSetting.SetShowPhotoNew()
                        item.tbShowNew = False

                End Select
            End If

            Dim dev As IDevice = Nothing
            If Me.mCurrentDevice IsNot Nothing Then
                dev = Me.mCurrentDevice.MobileDevice
            End If

            If e IsNot Nothing Then
                Me.SetClick(dev, item.Name, devType)
            End If

            '' Added by Utmost20150414
            If Me.mCurrentDevice IsNot Nothing AndAlso Me.mCurrentDevice.DeviceType = DeviceType.Android Then
                For Each frm As Form In Application.OpenForms
                    If frm.Name.Contains("frmAndroidWelcome") Then
                        Dim frmWel As frmAndroidWelcome = CType(frm, frmAndroidWelcome)
                        If item.Name.Equals("Welcome") Then
                            '' 开始读电量
                            frmWel.RunBattery(True)
                        Else
                            '' 停止读电量
                            frmWel.RunBattery(False)
                        End If

                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ToolItem_ClickEx")
        Finally
            If e IsNot Nothing AndAlso Me.mClickItem IsNot Nothing Then
                Me.mClickItem.Checked = True
            End If
            Me.mClickItem = Nothing
        End Try
    End Sub

    Private mfrmSite As frmSite = Nothing
    Private Sub CreateWebForm()
        Dim strName As String = Me.mClickItem.Name
        If strName.Equals("App", StringComparison.OrdinalIgnoreCase) OrElse _
           strName.Equals("Music", StringComparison.OrdinalIgnoreCase) OrElse _
           strName.Equals("Photo", StringComparison.OrdinalIgnoreCase) OrElse _
           strName.Equals("EBook", StringComparison.OrdinalIgnoreCase) OrElse _
           (strName.Equals("Video", StringComparison.OrdinalIgnoreCase) AndAlso _
            ServerIniSetting.GetShowLiveVideo()) Then

            If Me.mCurrentDevice IsNot Nothing Then
                Me.mfrmSite = frmSite.Instance(Me, Me.mCurrentDevice.MobileDevice)
                Me.mfrmSite.Tag = Me.mCurrentDevice.DeviceType
            Else
                Me.mfrmSite = frmSite.Instance(Me, Me.mDevice)
            End If

            Me.mfrmSite._clickName = strName

            If Not Me.pnlContainer.Contains(Me.mfrmSite) Then
                Utility.AddForm2Panel(Me.mfrmSite, Me.pnlContainer, False, False)
                Me.mfrmSite.Dock = DockStyle.None
                Me.mfrmSite.Size = New Size(Me.pnlContainer.Width - 196, Me.pnlContainer.Height)
                Me.mfrmSite.Location = New Point(196, 0)
                Me.mfrmSite.Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
            End If
        End If
    End Sub

    '''切换到设备对应界面 by luyanjun
    Public Sub SelectUSBDev(ByVal usbDev As UsbDevice)
        If usbDev IsNot Nothing Then

            For Each btnDev As KeyValuePair(Of String, tbCheckButton) In mDictDeviceButton

                If String.Equals(btnDev.Key, usbDev.DeviceID, StringComparison.InvariantCultureIgnoreCase) Then  ' btnDev.Key = usbDev.DeviceID
                    Me.DeviceItem_Click(btnDev.Value, New EventArgs())

                    Exit Sub
                End If

            Next

        End If

    End Sub

    Private Sub DeviceItem_CloseClick(ByVal sender As Object, ByVal e As EventArgs)
        Try

            Dim deviceButton As tbCheckButton = sender
            Dim selectedDev As UsbDevice = deviceButton.Tag

            If selectedDev IsNot Nothing Then
                If tbMessageBox.Show(Me, _
                                            Me.Language.GetString("Welcome.Message.Disconnect"), _
                                            Me.Language.GetString("Common.Info"), _
                                            MessageBoxButtons.YesNo, _
                                            MessageBoxIcon.Question, _
                                            MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.No Then
                    Return
                End If

                If selectedDev.DeviceType = DeviceType.Android AndAlso selectedDev.MobileDevice IsNot Nothing Then
                    CType(selectedDev.MobileDevice, AndroidDevice).ShowDaemonForm(False)
                End If

                Dim deviceId As String = selectedDev.DeviceID

                Me.mUsbDeviceMgr.DictBrokenDevices(deviceId) = selectedDev

                Me.RemoveDevice(selectedDev)
                If Me.mUsbDeviceMgr.DictBrokenDevices.ContainsKey(deviceId) AndAlso selectedDev.DeviceType = DeviceType.iOS Then
                    Me.mUsbDeviceMgr.DictBrokenDevices.Remove(deviceId)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
    End Sub

    Private Sub DeviceItem_Click(ByVal sender As Object, ByVal e As EventArgs)
        Me.mblnUserClick = False

        Try
            Dim btn As tbCheckButton = sender
            Dim rdb As tbRadioButton = Me.Controls("Welcome")

            If btn Is Nothing Then
                Me.mCurrentDevice = Nothing
                Me.LogCurrentDevice("DeviceItem_Click1")

                If frmDownloadCenter.Instance IsNot Nothing Then
                    frmDownloadCenter.Instance.CurrentDeviceType = DeviceType.iOS
                End If

                rdb.Checked = True
                Me.ToolItem_ClickEx(rdb, e)

            Else
                Me.mCurrentDevice = btn.Tag
                Me.LogCurrentDevice("DeviceItem_Click2")
                If frmDownloadCenter.Instance IsNot Nothing Then
                    frmDownloadCenter.Instance.CurrentDeviceType = Me.mCurrentDevice.DeviceType
                End If

                'Common.Log("mDictDeviceButton counts: " & Me.mDictDeviceButton.Values.Count)
                'Common.Log("Current Button: " & btn.Name)

                '调整设备按钮样式
                For Each item As tbCheckButton In Me.mDictDeviceButton.Values
                    'Common.Log("DeviceButton : " & item.Name)

                    If item.Tag IsNot Nothing Then

                        Dim usbType As DeviceType = CType(item.Tag, UsbDevice).DeviceType
                        If item Is btn Then
                            item.tbIconImage = IIf(usbType = DeviceType.iOS, My.Resources.new_device_ios, My.Resources.new_device_airdroid)
                            item.Checked = True
                        Else
                            item.tbIconImage = IIf(usbType = DeviceType.iOS, My.Resources.new_device_ios_gray, My.Resources.new_device_airdroid_gray)
                            item.Checked = False
                        End If

                    End If

                Next


                If String.IsNullOrEmpty(btn.AccessibleDescription) Then
                    For Each ctl As Control In Me.Controls
                        If TypeOf ctl Is tbRadioButton AndAlso CType(ctl, tbRadioButton).Checked Then
                            rdb = ctl
                            Exit For
                        End If
                    Next
                    If rdb.Checked Then
                        Me.ToolItem_ClickEx(rdb, e)
                    Else
                        rdb.tbReadOnly = False
                        rdb.Checked = True
                    End If

                Else
                    Dim bIsWelcome As Boolean = False

                    For Each ctl As Control In Me.Controls
                        If TypeOf ctl Is tbRadioButton AndAlso ctl.Name.Equals(btn.AccessibleDescription) Then
                            rdb = ctl
                            If rdb.Checked Then
                                Me.ToolItem_ClickEx(rdb, e)
                            Else
                                rdb.tbReadOnly = False
                                rdb.Checked = True
                            End If

                            bIsWelcome = btn.AccessibleDescription.Equals("Welcome")
                            Exit For
                        End If

                    Next

                    '' 多台安卓设备连接时候，有时候某台设备一直连接中...——Added by Utmost20160725
                    If bIsWelcome AndAlso mCurrentDevice IsNot Nothing AndAlso mCurrentDevice.DeviceType = DeviceType.Android Then
                        Dim deviceId As String = mCurrentDevice.DeviceID
                        AndroidDeviceMonitor.Instance.DeviceChangedDicts(deviceId) = False
                    End If

                End If


                For Each ctl As Control In Me.Controls
                    If TypeOf ctl Is tbRadioButton AndAlso ctl.Name.Equals("More") Then
                        rdb = ctl
                        If mCurrentDevice IsNot Nothing AndAlso mCurrentDevice.DeviceType = DeviceType.iOS AndAlso (IniSetting.GetShowFindWeChatDataNew() OrElse IniSetting.GetShowWeChatCoexist) Then
                            rdb.tbShowNew = True
                        Else
                            rdb.tbShowNew = False
                        End If
                    End If
                Next

            End If

            '更新工具栏按钮状态
            For Each ctl As tbRadioButton In Me.mListItems
                Select Case ctl.Name
                    Case "Jailbreak", "EBook"
                        If mCurrentDevice IsNot Nothing AndAlso mCurrentDevice.DeviceType = DeviceType.iOS Then
                            ctl.Visible = True
                        Else
                            ctl.Visible = False
                        End If
                End Select
            Next
            Me.UpdateToolButtonLocation()

            If Me.mDictUpdateNumber.Count > 0 AndAlso Me.mCurrentDevice IsNot Nothing Then
                '' 改变主窗体上应用按钮的数字值
                '' Added by Utmost20140417
                Dim num As Integer = 0
                Dim strDeviceId As String = Me.mCurrentDevice.DeviceID '.ToLower()
                Dim dtp As DeviceType = Me.mCurrentDevice.DeviceType

                If Me.mDictUpdateNumber.ContainsKey(strDeviceId) Then
                    num = Me.mDictUpdateNumber(strDeviceId)
                Else
                    num = 0
                End If

                Me.SetItemNumber(FunctionKey.App, num, strDeviceId, dtp)
            End If

            Application.DoEvents()

            '更新顶部功能按钮可用性
            Dim bIsReadOnly As Boolean = btn Is Nothing

            If Me.mCurrentDevice Is Nothing OrElse Me.mCurrentDevice.MobileDevice Is Nothing OrElse Not frmDeviceBase.CheckResponse(Me.mCurrentDevice) Then
                bIsReadOnly = True

            ElseIf Me.mCurrentDevice.DeviceType = DeviceType.Android Then
                If CType(Me.mCurrentDevice.MobileDevice, AndroidDevice).Summary IsNot Nothing Then
                    bIsReadOnly = False
                End If

            End If


            Me.SetItemsState(bIsReadOnly)

            Dim list As New List(Of frmDeviceBase)
            For Each frm As Form In Application.OpenForms
                If frm.Parent Is Nothing AndAlso TypeOf frm Is frmDeviceBase Then
                    list.Add(frm)
                End If
            Next

            For Each frm As frmDeviceBase In list
                If frm.mUsbDev Is Me.mCurrentDevice Then
                    If Me.mCurrentDevice.MobileDevice IsNot Nothing Then
                        frm.OnConnect(Me.mCurrentDevice.MobileDevice)
                    End If
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeviceItem_Click")
        End Try

        Me.mblnUserClick = True
    End Sub

    Private Sub AddDevice(ByVal dev As UsbDevice)
        'ios设备如果长度为24的不处理
        If dev Is Nothing OrElse (dev.DeviceID.Length = 24 AndAlso dev.DeviceType = DeviceType.iOS AndAlso Common.CheckiTunesInstall()) Then
            Exit Sub
        End If

        Dim deviceId As String = dev.DeviceID '.ToLower()

        If Me.mDictUsbDevice.ContainsKey(deviceId) AndAlso Me.mDictDeviceButton.ContainsKey(deviceId) Then
            '' 已经添加了设备
            Return
        End If


        Try
            For Each devItem As UsbDevice In Me.mDictUsbDevice.Values
                If String.Equals(dev.HardwareId, devItem.HardwareId, StringComparison.InvariantCultureIgnoreCase) AndAlso _
                   devItem.DeviceType = dev.DeviceType AndAlso dev.DeviceType = DeviceType.iOS AndAlso _
                   String.Equals(dev.DeviceID, devItem.DeviceID, StringComparison.InvariantCultureIgnoreCase) Then
                    '' ' Added by Utmost20140618
                    '' 相同类型设备，避免usb接口没有枚举到iOS设备，但iTunes找到该设备
                    Return
                End If
            Next
        Catch ex As Exception
            Debug.Write(ex)
        End Try


        Debug.WriteLine("AddDevice of MainForm ====>" + dev.InfoString)

        Dim fntText As Font = Common.CreateFont("Arial", 8, FontStyle.Regular)
        Dim item As tbCheckButton = Me.CreateDeviceButton(dev, fntText)
        Dim ctrlIndex As Integer = -1
        Dim ctrl As Control = Nothing

        For Each ctrl In Me.Controls
            If TypeOf (ctrl) Is tbCheckButton Then
                Dim chkCtrl As tbCheckButton = CType(ctrl, tbCheckButton)

                If String.Compare(ctrl.Name, item.Name, True) = 0 Then
                    ctrlIndex = Me.Controls.IndexOf(ctrl)
                    Exit For
                End If
            End If
        Next

        If ctrlIndex <> -1 Then
            Me.Controls.RemoveAt(ctrlIndex)
        End If

        item.Checked = True

        ''当接入第一台设备后，触发设备连接事件
        Me.mCurrentDevice = dev
        Me.LogCurrentDevice("AddDevice")
        '保存接入设备
        Me.mDictUsbDevice(deviceId) = dev

        '保存接入设备按钮
        If Me.mDictDeviceButton.ContainsKey(deviceId) Then
            Common.Log(String.Format("add device has button: {0}", deviceId))
            Me.RemoveDeviceBtn(dev)
        End If

        Me.mDictDeviceButton(deviceId) = item
        Me.mLstDeviceButtons.Add(item)
        Me.Controls.Add(item)
        Me.tbShowTitleOnForm = False

        '' Noted by Utmost20150302
        '' 直到连接全部完成后再
        '调整菜单可用状态
        'If dev.MobileDevice IsNot Nothing Then
        '    If TypeOf dev.MobileDevice Is iPhoneDevice Then
        '        Me.SetItemsState(False)
        '    End If
        'End If

        '更新设备按钮位置
        Me.UpdateDeviceButtonLocation()
        Application.DoEvents()
        Me.DeviceItem_Click(item, EventArgs.Empty)
    End Sub

    ' 移除连接设备
    Private Sub RemoveDevice(ByVal dev As UsbDevice)
        Dim devType As DeviceType = DeviceType.Unknown

        Try
            If dev IsNot Nothing Then

                If mCurrentDevice IsNot Nothing AndAlso mCurrentDevice.DeviceType = DeviceType.Android AndAlso _
                   dev.DeviceID.Equals("None") AndAlso dev.ConnectMode = ConnectMode.USBConnectedADBDisconnected Then
                    Me.mCurrentDevice = Nothing
                    Me.LogCurrentDevice("RemoveDevice1")
                    Exit Sub
                End If

                If mCurrentDevice IsNot Nothing AndAlso mCurrentDevice.DeviceID.Equals(dev.DeviceID) Then  'Not dev.IsConnectedUsb
                    Me.mCurrentDevice = Nothing
                    Me.LogCurrentDevice("RemoveDevice2")
                End If

                If dev.DeviceType = DeviceType.Android Then
                    If mFrmAndroidFeedback IsNot Nothing AndAlso Not mFrmAndroidFeedback.IsDisposed Then
                        If String.Equals(mFrmAndroidFeedback.Name, dev.DeviceID, StringComparison.InvariantCultureIgnoreCase) OrElse _
                           mFrmAndroidFeedback.Name.Equals("mFrmAndroidFeedback", StringComparison.OrdinalIgnoreCase) Then
                            mFrmAndroidFeedback.Close()
                        End If
                    End If
                End If

                RemoveDeviceBtn(dev)

                '当选中文件的是视频或文件，用户在拔掉设备的时候默认选到首页
                If _CurrentAccessibleDescription = "File" OrElse _CurrentAccessibleDescription = "EBook" OrElse (_CurrentAccessibleDescription = "Video" AndAlso ServerIniSetting.GetShowLiveVideo() = False) Then
                    _CurrentAccessibleDescription = "Welcome"
                End If

                '如果点击移除设备，会出现蓝屏的问题。（移除当前设备）
                '所以当移除的是当前设备的时候把 Me.mCurrentDevice 设置nothing
                If Me.mCurrentDevice IsNot Nothing AndAlso Me.mCurrentDevice Is dev Then
                    Me.mCurrentDevice = Nothing
                    Me.LogCurrentDevice("RemoveDevice3")
                End If


                DispatchDisconnect(dev)
                SwitchToAvailabeDevBtn(dev)

                'DispatchDisconnectMsg(dev, False)

                Me.ClearItemNumber(dev.DeviceID)
                '_dictConnectedForms.Remove(dev.DeviceID)

                If Me.mLstDeviceButtons.Count = 0 Then
                    Me.mCurrentDevice = Nothing
                    Me.LogCurrentDevice("RemoveDevice4")

                    For Each frm1 As Form In Application.OpenForms

                        If frm1.Name.Contains("tbImageViewForm") Then
                            frm1.Close()
                            Exit For
                        End If
                    Next


                    For Each frm As Form In Application.OpenForms

                        If _CurrentAccessibleDescription.Equals("Welcome") AndAlso frm.Name.Contains("frmCommonWel") Then
                            frm.Visible = True
                            frm.BringToFront()

                            Exit For
                        End If
                    Next
                End If

                UpdateToolButtonLocation()

                Application.DoEvents()
            End If

        Catch ex As Exception
            Debug.Write(ex)
        End Try


        'Me.KillAdbWeixin()
    End Sub

    'Private Sub KillAdbWeixin()
    '    Try
    '        ''' 断开安卓微信备份还原
    '        Dim strWeixin As String = Path.Combine(Folder.AppFolder, "zAndroidWeixin\iAdbWeixin.exe")
    '        ProcessHelper.KillProcess(strWeixin)

    '    Catch ex As Exception
    '    End Try

    'End Sub

    Private Sub DispatchDisconnect(ByVal dev As UsbDevice)
        If dev Is Nothing Then
            Exit Sub
        End If

        Dim deviceId As String = dev.DeviceID
        Dim listForms As New List(Of Form)
        Dim listName As New List(Of String)

        Try
            '弹出窗体也要兼容断开连接事件
            For Each frm As Form In Application.OpenForms
                If Not String.IsNullOrEmpty(frm.Name) Then
                    listName.Add(frm.Name)
                End If

                If frm.Name.StartsWith(deviceId, StringComparison.OrdinalIgnoreCase) Then
                    frm.AccessibleDescription = String.Empty
                    listForms.Add(frm)

                ElseIf TypeOf frm Is frmAndroidUSBDebugBrowser OrElse TypeOf frm Is frmAndroidUSBDebugSimple Then
                    listForms.Add(frm)

                End If
            Next

            For Each frm As Form In listForms
                If Me.mListFormCheckInit.Contains(frm.Name) Then
                    Me.mListFormCheckInit.Remove(frm.Name)
                End If

                If frm.IsDisposed Then
                    Continue For
                End If

                Try
                    If TypeOf frm Is frmDeviceBase Then
                        CType(frm, frmDeviceBase).TerminateThread()
                        CType(frm, frmDeviceBase).OnUsbDisconnect(dev)

                        If frm.Name.StartsWith(deviceId) Then
                            '还原窗体名称，去掉DeviceID_
                            'b241dce7cb2c048c5f989ad64731f5262b24c1b_iTong.frmWelcome_Welcome()
                            frm.Name = frm.Name.Substring(deviceId.Length + 1)
                        End If
                    End If

                    If listName.Contains(frm.Name) Then
                        '关闭同类型冗余窗口

#If IS_ITONG Then
                        '刷机窗体不需要在设备断开的时候释放
                        If TypeOf frm Is frmFlash OrElse TypeOf frm Is frmFirmware OrElse TypeOf frm Is frmSite Then
                            Continue For
                        End If
                        frm.Close()
#End If
                    Else
                        Dim blnExist As Boolean = False
                        For Each strName As String In listName
                            '多台设备移除的时候， 窗体会被释放掉，最后一台设备移除的时候，不做释放
                            If strName.EndsWith(frm.Name) AndAlso Not strName.StartsWith(deviceId) Then
                                blnExist = True
                                Exit For
                            End If
                        Next
                        If blnExist Then
#If IS_ITONG Then
                            '刷机窗体不需要在设备断开的时候释放
                            If TypeOf frm Is frmFlash OrElse TypeOf frm Is frmFirmware OrElse TypeOf frm Is frmSite Then
                                Continue For
                            End If
                            frm.Close()
#End If
                        Else
                            listName.Add(frm.Name)
                        End If
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "MainForm.DispatchDisconnect")
                End Try
            Next

        Catch ex As Exception
            Debug.Write(ex)
        End Try

        Me.SetWebboVisable()
    End Sub

    Private Sub DispatchDisconnectMsg(ByVal dev As UsbDevice, ByVal blnIsOffLine As Boolean)

        If dev Is Nothing OrElse String.IsNullOrEmpty(dev.DeviceID) Then
            'Common.LogException("remove empty device")
            Return
        End If

        Dim deviceId As String = dev.DeviceID '.ToLower()

        Try
            '如果是智能设备，则需要触发该设备的断开事件
            Dim dictForm As New Dictionary(Of String, Form)(StringComparer.InvariantCultureIgnoreCase)
            Dim dictOpen As New Dictionary(Of String, Form)(StringComparer.InvariantCultureIgnoreCase)

            Try
DO_CTL:
                For Each ctl As Control In Me.pnlContainer.Controls
                    'Common.Log(String.Format("Dispatch disconnect container: {0}", ctl.Name))

                    If TypeOf ctl Is Form AndAlso ctl.Name.StartsWith(deviceId, StringComparison.InvariantCultureIgnoreCase) Then
                        '安卓设备由online转到offline时，向除AndroidWelcome外的所有窗体触发断开连接事件 by luyanjun
                        If (blnIsOffLine AndAlso TypeOf ctl Is frmAndroidWelcome) Then
                            Continue For
                        End If

                        '重命名窗体名称
                        'Common.Log(String.Format("Dispatch disconnect container to: {0}", ctl.Name))

                        ctl.AccessibleDescription = String.Empty
                        dictForm(ctl.Name) = ctl
                    End If
                Next
            Catch ex As Exception
                GoTo DO_CTL
            End Try

            Dim listForms As New List(Of Form)

            Try
DO_OPEN:
                listForms.Clear()
                For Each frm As Form In Application.OpenForms
                    listForms.Add(frm)
                Next

                For Each frm As Form In listForms
                    Debug.Print(frm.Name)
                    'Common.Log(String.Format("Dispatch disconnect OpenForms: {0}", frm.Name))

                    '' 不执行监控窗体
                    If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                        Continue For
                    End If

                    If frm.Name.StartsWith(deviceId, StringComparison.InvariantCultureIgnoreCase) Then
                        If (blnIsOffLine AndAlso TypeOf frm Is frmAndroidWelcome) Then
                            Continue For
                        End If

                        'Common.Log(String.Format("Dispatch disconnect OpenForms to: {0}", frm.Name))

                        frm.AccessibleDescription = String.Empty
                        dictOpen(frm.Name) = frm
                    End If
                    If TypeOf frm Is frmDownloaded Then
                        dictOpen(frm.Name) = frm
                    End If

                Next
            Catch ex As Exception
                GoTo DO_OPEN
            End Try

            Try
                '触发该设备打开窗体的断开事件
                'If dev.MobileDevice IsNot Nothing Then  '可能在还未连接时拔插设备，所以不需要判断MobileDevice是否为空 by luyanjun
                Dim mobileDev As IDevice = dev.MobileDevice

                For Each frm As Form In dictForm.Values
                    If TypeOf frm Is frmAndroidBase Then
                        CType(frm, frmAndroidBase).OnDisconnect(mobileDev)
                        CType(frm, frmAndroidBase).OnUsbDisconnect(dev)
                    ElseIf TypeOf frm Is frmDeviceBase Then
                        CType(frm, frmDeviceBase).OnDisconnect(mobileDev)
                        CType(frm, frmDeviceBase).OnUsbDisconnect(dev)
                    End If
                Next

                For Each frm As Form In dictOpen.Values
                    '重命名窗体名称
                    frm.Name = frm.GetType().FullName

                    If frm.Parent IsNot Nothing AndAlso Not TypeOf frm Is frmDownloaded Then
                        Continue For
                    End If

                    If TypeOf frm Is frmAndroidBase Then
                        CType(frm, frmAndroidBase).OnDisconnect(mobileDev)
                        CType(frm, frmAndroidBase).OnUsbDisconnect(dev)
                    ElseIf TypeOf frm Is frmDeviceBase Then
                        CType(frm, frmDeviceBase).OnDisconnect(mobileDev)
                        CType(frm, frmDeviceBase).OnUsbDisconnect(dev)
                    End If
                Next
                'End If
            Catch ex As Exception
                Common.LogException(ex.ToString())
            End Try

            dictForm.Clear()
            dictOpen.Clear()

            For Each frm As Form In listForms
                '' Added by Utmost20141202
                If TypeOf frm Is frmAndroidUSBDebugBrowser OrElse TypeOf frm Is frmAndroidUSBDebugSimple Then
                    Application.OpenForms(frm.Name).Close()
                End If

            Next

            listForms.Clear()

            If blnIsOffLine Then

                If mCurrentDevice IsNot Nothing AndAlso _
                   String.Equals(deviceId, mCurrentDevice.DeviceID, StringComparison.InvariantCultureIgnoreCase) Then

                    SetItemsState(True)

                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RemvoeDevice_3")
        End Try

        Me.SetWebboVisable()
    End Sub

    '''' 移除当前拔掉设备的按钮
    Private Sub RemoveDeviceBtn(ByVal dev As UsbDevice)

        Try

            If mDictDeviceButton.Count = 0 OrElse mDictUsbDevice.Count = 0 Then Exit Sub

            Dim deviceId As String = dev.DeviceID

            '移除设备按钮
            'Common.Log(String.Format("remove device button: {0}", deviceId))
            If Me.mDictDeviceButton.ContainsKey(deviceId) Then
                '移除按钮
                Dim btn As tbCheckButton = Me.mDictDeviceButton(deviceId)

                Me.Controls.Remove(btn)
                Me.mDictDeviceButton.Remove(deviceId)
                Me.mLstDeviceButtons.Remove(btn)

                '调整菜单可用状态
                Me.UpdateDeviceButtonLocation()
                If Me.Controls.Contains(btn) Then
                    Common.Log("remove device button fail")
                End If
            Else
                Common.Log(String.Format("remove device button not found: {0}", deviceId))
            End If

            If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                Me.mDictUsbDevice.Remove(deviceId)
            End If

            If Me.mDictUpdateNumber.ContainsKey(deviceId) Then
                ClearItemNumber(deviceId)
            End If


            For Each funcKey As FunctionKey In Me.mDictFuncInfoMsg.Keys

                If funcKey = FunctionKey.App Then
                    Dim infoValues As New StringDictionary

                    If Me.mDictFuncInfoMsg.TryGetValue(funcKey, infoValues) AndAlso infoValues.Count > 0 Then
                        If infoValues.ContainsKey(deviceId) Then
                            infoValues.Remove(deviceId)
                        End If
                    End If

                End If

            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "RemoveDeviceBtn of MainForm")
        End Try
    End Sub

    Private Sub SwitchToAvailabeDevBtn(ByVal dev As UsbDevice)

        Me.mblnUserClick = False

        If dev Is Nothing Then Exit Sub
        If String.IsNullOrEmpty(dev.DeviceID) Then Exit Sub

        Dim deviceId As String = dev.DeviceID

        mUsbDeviceMgr.Dispose(deviceId)   '' 有时候拔掉CoolPad,Nexus4等设备无法断开

        Debug.Print("SwitchToAvailabeDevBtn of MainForm DeviceID={0}, DeviceType={1}" + vbCrLf, dev.DeviceID, dev.DeviceType)


        Try
            ''' Modified by Utmost20140512
            '移除设备
            If Me.mDictUsbDevice.ContainsKey(deviceId) Then
                Me.mDictUsbDevice.Remove(deviceId)
            End If

            ''' 移除保存的应用数量
            If Me.mDictUpdateNumber.ContainsKey(deviceId) Then
                Me.mDictUpdateNumber.Remove(deviceId)
            End If

            If Me.mDictDeviceButton.Count = 0 Then '防止没有按钮时不显示标题
                '''触发空设备默认选中首页事件
                'Common.Log(String.Format("SwitchToAvailabeDevBtn no device"))
                Me.ToolItem_ClickEx(Nothing, EventArgs.Empty)
                Me.tbShowTitleOnForm = True
                '调整菜单可用状态
                Me.SetItemsState(True)
                Application.DoEvents()

                Me.SetFormText()
                If frmDownloadCenter.Instance IsNot Nothing Then
                    frmDownloadCenter.Instance.CurrentDeviceType = DeviceType.iOS
                End If
                frmSite.Instance(Me, Nothing).DisConnected()

            Else
                Me.tbShowTitleOnForm = False
                Try
                    For Each item As UsbDevice In Me.mDictUsbDevice.Values
                        Dim itemId As String = item.DeviceID '.ToLower()
                        If Me.mDictDeviceButton.ContainsKey(itemId) Then
                            '触发其他设备选中事件

                            Me.DeviceItem_Click(Me.mDictDeviceButton(itemId), System.EventArgs.Empty)

                            If item.MobileDevice Is Nothing Then
                                Me.SetItemsState(True)
                            End If
                            Application.DoEvents()
                            Exit For
                        Else
                            'Common.Log(String.Format("SwitchToAvailabeDevBtn device has not button: {0}", itemId))
                        End If
                    Next
                Catch ex As Exception
                    Debug.Write(ex)
                End Try
            End If
        Catch ex As Exception
            Debug.Write(ex)
            Common.LogException(ex.ToString(), "SwitchToAvailabeDevBtn")
        End Try

        ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
        'Try
        '    If Me.mCurrentDevice IsNot Nothing AndAlso _
        '       String.Equals(dev.DeviceID, Me.mCurrentDevice.DeviceID, StringComparison.InvariantCultureIgnoreCase) Then

        '        mCurrentDevice = dev

        '        If Me.mDictUsbDevice.Count = 0 Then
        '            '触发空设备默认选中首页事件
        '            Me.ToolItem_ClickEx(Nothing, EventArgs.Empty)

        '            '调整菜单可用状态
        '            Me.SetItemsState(True)
        '            Me.SetItemNumber(FunctionKey.App, 0, Me.mCurrentDevice.DeviceType)

        '            Me.tbShowTitleOnForm = True

        '            '如果拔掉当然选中的设备，默认选中其他设备
        '            Me.mCurrentDevice = Nothing
        '        Else
        '            '如果拔掉当然选中的设备，默认选中其他设备
        '            Me.mCurrentDevice = Nothing

        '            For Each item As UsbDevice In Me.mDictUsbDevice.Values
        '                Dim itemId As String = item.DeviceID

        '                If Me.mDictDeviceButton.ContainsKey(itemId) Then
        '                    '触发其他设备选中事件
        '                    Me.DeviceItem_Click(Me.mDictDeviceButton(itemId), System.EventArgs.Empty)

        '                    '调整菜单可用状态
        '                    If item.MobileDevice Is Nothing Then
        '                        Me.SetItemsState(True)
        '                    End If
        '                    Exit For
        '                End If
        '            Next
        '        End If

        '    End If

        'Catch ex As Exception
        '    Common.LogException(ex.ToString(), "RemvoeDevice_2")
        'End Try
        Me.mblnUserClick = True
    End Sub

    '取得设备button的平均宽度
    Private Function GetDeviceButtonWidth() As Integer
        Dim intWideth As Integer = Me.mDeviceButtonWidthMax
        Try
            If Me.mLstDeviceButtons.Count > 3 Then
                intWideth = Me.btnTuiTutorial.Left / Me.mLstDeviceButtons.Count
            End If
            intWideth = intWideth - Me.mDeviceButtonDiff

            If intWideth < Me.mDeviceButtonWidthMin Then
                intWideth = Me.mDeviceButtonWidthMin
            ElseIf intWideth > Me.mDeviceButtonWidthMax Then
                intWideth = Me.mDeviceButtonWidthMax
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "MainForm_GetDeviceButtonWidth")
        End Try
        Return intWideth
    End Function

    Private Function GetDeviceTextByWidth(ByVal strName As String, ByVal intWidth As Integer)
        Dim strReturn As String = strName
        Dim intCount As Integer = (intWidth - 45) / 8
        If intCount > 0 Then
            strReturn = Utility.GetShortString(strName, intCount, 0)
        Else
            strReturn = "..."
        End If
        Return strReturn
    End Function

    '设置button的宽度和位置
    Private Sub UpdateDeviceButtonLocation()
        Try
            Me.Invalidate()

            Dim intX As Integer = Me.mDeviceButtonLeft
            Dim intY As Integer = Me.mDeviceButtonTop
            Dim intWidth As Integer = Me.GetDeviceButtonWidth

            For Each btn As tbCheckButton In Me.mLstDeviceButtons
                btn.Location = New Point(intX, intY)

                '助手多设备的时候自动调整button宽度，显示在顶端
                If intWidth < btn.Width Then
                    btn.tbAutoSize = False
                    btn.tbAutoSizeEx = False
                    btn.Width = intWidth
                    Dim strName As String = Me.GetDeviceName(btn.Tag)
                    btn.Text = Me.GetDeviceTextByWidth(strName, btn.Width)
                    btn.tbToolTip = strName
                End If

                Dim right As Integer = btn.Right

                '' Added by Utmost20140612
                '' 根据Text长度自动调整按钮宽度
                'Dim textSize As Size = TextRenderer.MeasureText(btn.Text, btn.Font)
                If right < btn.Width Then
                    right = btn.Width
                End If
                intX = right + Me.mDeviceButtonDiff

                btn.Invalidate()
            Next
        Catch ex As Exception

        End Try
        Me.Invalidate()

    End Sub

    Private Sub InitUsbMgr()
        If Common.VerIs30() Then

            Me.mUsbDeviceMgr = AndroidDeviceMonitor.Instance()

            AddHandler mUsbDeviceMgr.UsbChanged, AddressOf OnUsbChanged
            AddHandler mUsbDeviceMgr.UsbStateChanged, AddressOf OnUsbStateChanged
            AddHandler mUsbDeviceMgr.DoDriverDownloadInstall, AddressOf OnDoDriverDownloadInstall      '' 下载、安装驱动
            AddHandler mUsbDeviceMgr.DeviceConnected, AddressOf OnAndroidDeviceConnected

            'Me.mUsbDeviceMgr.MakeAndroidEnvoinment(True)  ' 初始化Android运行环境
            'Me.mUsbDeviceMgr.StartListen()

            _tmrMonitor.Interval = 3000
            _tmrMonitor.Start()
        End If

    End Sub

    Private Sub UpdateContainer()
        Me.pnlContainer.Location = New Point(Me.mContainerLeft, Me.mDeviceButtonTop + Me.mDeviceButtonHeight + Me.mToolButtonHeight)
        Me.pnlContainer.Size = New Size(Me.Width - Me.mContainerLeft * 2, Me.Height - Me.pnlContainer.Top - 26)

        If Me.mfrmSite IsNot Nothing Then
            Me.mfrmSite.Size = New Size(Me.pnlContainer.Width - 196, Me.pnlContainer.Height)
            Me.mfrmSite.Location = New Point(196, 0)
            Me.mfrmSite.Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
        End If
    End Sub

    Private Sub InitFormEx()

        '=== 根据屏幕尺寸调整窗体大小 =============================
        'Me.Height = 734
        'Me.Width = 996

        'Dim intWidth As Integer = 978
        'Dim intHeight As Integer = 734

        '小尺寸
        Dim intSmallW As Integer = 978
        Dim intSmallH As Integer = 734

        '大尺寸
        Me.Width = 1126
        Me.Height = 826

        Dim intWidth As Integer = 1126
        Dim intHeight As Integer = 826

        Dim blnChanged As Boolean = False
        If Screen.PrimaryScreen.WorkingArea.Width < Me.Width Then
            intWidth = Screen.PrimaryScreen.WorkingArea.Width
            blnChanged = True
        End If
        If Screen.PrimaryScreen.WorkingArea.Height < Me.Height Then
            intHeight = Screen.PrimaryScreen.WorkingArea.Height
            blnChanged = True
        End If

        If blnChanged Then
            If intWidth > intSmallW Then
                intWidth = intSmallW
            End If
            If intHeight > intSmallH Then
                intHeight = intSmallH
            End If
            Me.Size = New Size(intWidth, intHeight)
        End If
        '==========================================================

        Me.btn_close.tbBackgroundImage = My.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_normal.tbBackgroundImage = My.Resources.btn_max
        Me.btn_normal.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_minimize.tbBackgroundImage = My.Resources.btn_min
        Me.btn_minimize.tbBackgroundImageState = ImageState.ThreeState
        Me.picLogo.Image = My.Resources.logo

        Dim btn As tbRadioButton = Nothing

        Me.btnDownload.tbTextColor = Color.FromArgb(86, 86, 86)
        Me.btnDownload.tbTextColorDisable = Color.FromArgb(86, 86, 86)
        Me.btnDownload.tbTextColorDown = Color.FromArgb(86, 86, 86)
        Me.btnDownload.tbTextColorHover = Color.FromArgb(86, 86, 86)

        'Me.BackColor = Color.FromArgb(&H56, &H9D, &HC7)

        Me.UpdateContainer()

        Dim szButtonHeight As New Size(80, mToolButtonHeight)
        Dim fntText As Font = Common.CreateFont("Arial", 9, FontStyle.Regular)

        '"Welcome"
        Me.CreateToolItemEx("Welcome", Me.Language.GetString("Main.Button.Welcome"), fntText, My.Resources.new_top_home, _
                            New Type() {GetType(frmCommonWel), GetType(frmWelcome), GetType(frmAndroidWelcome)}, _
                            szButtonHeight, Nothing, New EventHandler(AddressOf OnMoreClick))
        '"App"
        Me.CreateToolItemEx(MainForm.CONSTAPP, Me.Language.GetString("Main.Button.AppNew"), fntText, My.Resources.new_top_app, _
                            New Type() {GetType(frmCommonApp), GetType(frmCommonApp), GetType(frmCommonApp)}, _
                            szButtonHeight)


#If IS_ITONG Then
        'Music
        Me.CreateToolItemEx("Music", Me.Language.GetString("Main.Button.MusicNew"), fntText, My.Resources.new_top_music, _
                            New Type() {GetType(frmMusic), GetType(frmMusic), GetType(frmAndroidMusic)}, _
                            szButtonHeight)

        'Photo
        If Not Folder.LangType = LanguageType.en_US Then 'AndAlso IniSetting.GetPhotoShow()
            btn = Me.CreateToolItemEx("Photo", Me.Language.GetString("Main.Button.Photo"), fntText, My.Resources.new_top_photo, New Type() {GetType(frmPhoto), GetType(frmPhoto), GetType(frmAndroidPhoto)}, szButtonHeight)
            If IniSetting.GetShowPhotoNew() Then
                btn.tbShowNew = True
            End If
        End If

        'Video
        If ServerIniSetting.GetShowLiveVideo() Then
            btn = Me.CreateToolItemEx("Video", Me.Language.GetString("Main.Button.Video"), fntText, My.Resources.new_top_video, New Type() {GetType(frmVideo), GetType(frmVideo), GetType(frmAndroidVideo)}, szButtonHeight)
            If IniSetting.GetIsShowVideoNew() Then
                btn.tbShowNew = True
            End If
        Else
            Me.CreateToolItemEx("Video", Me.Language.GetString("Main.Button.Video"), fntText, My.Resources.new_top_video, New Type() {GetType(frmVideo), GetType(frmVideo), GetType(frmAndroidVideo)}, szButtonHeight, My.Resources.new_top_video_light)
        End If
#End If

        'File
        Me.CreateToolItemEx("File", Me.Language.GetString("Main.Button.FileSystem"), fntText, My.Resources.new_top_file, _
                            New Type() {GetType(frmFile), GetType(frmFile), GetType(frmAndroidFile)}, _
                            szButtonHeight, My.Resources.new_top_file_light, _
                            Nothing, New Object() {FileFormType.FileSystem})


        '#If IS_ITONG Then
        '        '电子书
        '        Me.CreateToolItemEx("EBook", Me.Language.GetString("Media.Type.EBook"), fntText, My.Resources.new_top_ebook, _
        '                          New Type() {GetType(frmEBook), GetType(frmEBook), Nothing}, _
        '                          szButtonHeight, My.Resources.new_top_ebook_light)
        '#End If

        'If Folder.LangType = LanguageType.zh_CN AndAlso Not Utility.IsPanda Then
        '    Me.CreateToolItemEx("Jailbreak", Me.Language.GetString("Main.Button.FlashJailbreak"), fntText, My.Resources.new_top_jailbreak, _
        '                        New Type() {GetType(frmSite), GetType(frmSite), Nothing}, _
        '                        szButtonHeight, Nothing, Nothing, New Object() {WebSiteType.Jailbreak})   '"越狱"
        'End If

#If IS_ITONG_ZJ Then
        '2015-08-12 by chenbihai  ----
        Me.CreateToolItemEx("Activation", "教程", fntText, My.Resources.new_top_activation, _
                            New Type() {GetType(frmActivationHelp), GetType(frmActivationHelp), GetType(frmActivationHelp)}, _
                            szButtonHeight)

        Me.CreateToolItemEx("Data", "统计", fntText, My.Resources.new_top_data, _
                            New Type() {GetType(frmLogin), GetType(frmLogin), GetType(frmLogin)}, _
                            szButtonHeight)
        '----------------------------
#End If




#If IS_ITONG Then
        'More
        btn = Me.CreateToolItemEx("More", Me.Language.GetString("Main.Button.More"), fntText, My.Resources.new_top_more, _
                                  New Type() {GetType(frmMore), GetType(frmMore), GetType(frmMore)}, szButtonHeight)
        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected AndAlso IniSetting.GetShowFindWeChatDataNew() Then
            btn.tbShowNew = True
        End If
#End If

        Me.UpdateToolButtonLocation()

        Me.SetFormText()

        ProcForm.Instance().SetNotifyIconText(Me.Text)

        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
            Me.SetItemsState(False)
        End If

        ProcForm.Instance().InitLang(Me.tsmiSelectLang)


        Me.mListItems(0).Checked = True
        'Me._dictItems("Welcome").Checked = True
        'AddHandler USBDeviceUploadHelper.OnUploadDeviceInfo, AddressOf OnAndroidConnectFail
        Application.DoEvents()

        If Utility.IsPanda Then
            Me.btnFeedBack.Visible = False
            Me.tsmiAbout.Visible = False
            Me.tss2.Visible = False
        End If

#If IS_ITONG_ZJ Then
        Me.tsmiAbout.Visible = False
        Me.tsmiFeedback.Visible = False
        Me.tss2.Visible = False

        CreateLogButton()
#End If
        Me.InitLogin()
    End Sub

    Private Sub InitLogin()
        '登陆事件
#If IS_ITONG_ZJ Then
        Me.mPluginLogin = WealthLoginHelper.Instance()
#Else
        Me.mPluginLogin = PluginLogin.Instance()
#End If
        RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        AddHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        RemoveHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent
        AddHandler mPluginLogin.LogoutEvent, AddressOf PluginLogin_LogoutEvent

        Dim isVisible As Boolean = (Folder.LangType = LanguageType.zh_CN AndAlso Not Utility.IsPanda AndAlso Not Folder.AppType = RunType.Tongbu_Abroad)
        Me.btnLogin.Visible = False
        Me.tsmiLogin.Visible = isVisible
        Me.tsmiSplitLogin.Visible = isVisible

        '检查自动登录
        'Me.mPluginLogin.CheckAutoLogin()
    End Sub

    Public mDevice As iPhoneDevice = Nothing
    Public Shared CONSTAPP As String = "App"

    Private mDeviceRecovery As iPhoneRecoveryDevice = Nothing
    Private _ServiceContainer As ServiceContainer
    Private _WaitForCloseForm As Boolean = False
    Private mSelectKey As FunctionKey = FunctionKey.None
    Private mDictFuncInfoMsg As New Dictionary(Of FunctionKey, StringDictionary)  'Dictionary(Of String, String)
    Private mListItems As New List(Of tbRadioButton)
    'Private _dictItems As New Dictionary(Of String, tbRadioButton)

    Private mClickItem As tbRadioButton = Nothing

    Private mDownManager As MultiThreadDownload
    Private mToolTip As ToolTip
    Private mblnNewVersion As Boolean = False

    Private mIAndroid As Integer = 0 '0 没有设备  1 ios设备  2 Android设备

    Public Sub New(ByVal device As Object)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.InitDrawPara()
        Me.Language = LanguageInterface.Instance()
        'MusicDB.Instance().Get_DBInfo(DBInfoKey.key_Version)
        If device IsNot Nothing Then
            If TypeOf device Is iPhoneDevice Then
                Me.mDevice = device
            Else
                Me.mDeviceRecovery = device
            End If
        End If

        If device Is Nothing Then
            Me.mIAndroid = 0
        Else
            If (TypeOf device Is AndroidDevice) Then
                Me.mIAndroid = 2
            Else
                Me.mIAndroid = 1
            End If
        End If


        Me.InitDownloadManager()
        Me.InitWebSiteHelper()

        If Common.VerIs30() Then
            Me.mShowFiveYearAnniversary = ServerIniSetting.GetShowFiveYearAnniversary
            Me.mShowFiveYearBar = ServerIniSetting.GetShowFiveYearBar
            Me.InitFormEx()
            Me.SetLabelText(FunctionKey.Welcome, "")
        End If

        Me.InitExpireTime()

        Me.btnLogin.tbIconImage = My.Resources.icon_login

    End Sub

    Private Sub InitExpireTime()
        Me.picMode.Visible = False
        Me.lblExpireTime.Visible = False
        'If Not Folder.LangType = LanguageType.en_US Then
        '    Me.picMode.Visible = False
        '    Me.lblExpireTime.Visible = False
        'Else
        '    '"Trial version will expire on {0}"
        '    Me.lblExpireTime.Text = String.Format(Me.Language.GetString("Welcome.Message.ExpireTime"), "2013/09/01")
        'End If
    End Sub

    Private Function CreateToolItem(ByVal strName As String, _
                               ByVal strText As String, _
                               ByVal icon As Image, _
                               ByVal frmType As Type, _
                               ByVal defaultSize As Size, _
                               Optional ByVal iconReadOnly As Image = Nothing, _
                               Optional ByVal menuClick As EventHandler = Nothing, _
                               Optional ByVal args As Object = Nothing) As tbRadioButton

        Dim item As New tbRadioButton()
        With item
            .Name = strName
            .AccessibleDescription = frmType.FullName 'AccessibleName
            .Text = strText
            .tbIconImage = icon
            .Size = defaultSize
            .tbIconImageState = ImageState.OneState
            .tbIconPlaceText = 1
            .tbIconImageAlign = ContentAlignment.MiddleLeft

#If IS_ITONG Then
            .tbImageCheckedMouseLeave = My.Resources.top_bg_select
            .tbImageCheckedMouseDown = My.Resources.top_bg_select
            .tbImageCheckedMouseHover = My.Resources.top_bg_select
            .tbImageUnCheckedMouseHover = My.Resources.top_bg_select_h
#End If

            .ForeColor = Color.White
            .ForeColor_Checked = Color.White
            .tbIconReadOnly = iconReadOnly
            .BackColor = Color.Transparent
            .tbIconImageAlign = ContentAlignment.TopCenter

            If args IsNot Nothing Then
                .Tag = args
            End If

            If iconReadOnly IsNot Nothing Then
                .tbReadOnly = True
            End If

            If menuClick IsNot Nothing Then
                AddHandler .MoreClick, menuClick
                .tbIconMore = True
                .tbAutoSizeEx = True
                .Padding = New Padding(3, 5, 3, 5)
            Else
                .tbIconMore = False
                .tbAutoSizeEx = True
                .Padding = New Padding(3, 5, 3, 5)
            End If
            .MinimumSize = defaultSize

            .Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
            .TextAlign = ContentAlignment.MiddleCenter

        End With

        AddHandler item.CheckedChanged, AddressOf ToolItem_Click
        AddHandler item.MouseEnter, AddressOf ToolItem_MouseEnter
        AddHandler item.MouseLeave, AddressOf ToolItem_MouseLeave

        Me.mListItems.Add(item)

        Me.Controls.Add(item)
        'Me._dictItems.Add(strName, item)

        Return item
    End Function

    Private Sub ToolItem_MouseEnter(ByVal sender As Object, ByVal e As EventArgs)
        If CType(sender, tbRadioButton).tbReadOnly Then
            frmTipInfo.ShowTip(Me.Language.GetString("Main.Message.ConnectDevice"), sender, HorizontalAlignment.Center, 5) '"请接入iOS设备"
        End If
    End Sub

    Private Sub ToolItem_MouseLeave(ByVal sender As Object, ByVal e As EventArgs)
        If CType(sender, tbRadioButton).tbReadOnly Then
            frmTipInfo.HideTip(sender)
        End If
    End Sub

    Private Sub ToolItem_Click(ByVal sender As Object, ByVal e As EventArgs)
        Try
            Dim item As tbRadioButton = sender

            If Not item.Checked OrElse item.tbReadOnly Then
                Return
            End If

            '记录当前按下的按钮。
            If Me.mClickItem Is Nothing Then
                Me.mClickItem = item
            End If

            If Me.mClickItem.Name <> item.Name Then
                item.Checked = False
                Return
            End If

            Dim frmType As Type = System.Type.GetType(item.AccessibleDescription, True, False)
            If frmType Is Nothing Then
                Return
            End If

            Dim frmItem As Form = Nothing
            For Each ctl As Control In Me.pnlContainer.Controls
                If TypeOf ctl Is Form AndAlso ctl.GetType().FullName = item.AccessibleDescription AndAlso item.Tag Is ctl.Tag Then
                    frmItem = ctl
                    Exit For
                End If
            Next

            If frmItem Is Nothing Then
                Try
                    Dim args As New List(Of Object)
                    args.Add(Me)
                    args.Add(Me.mDevice)

                    If item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is Object() Then
                        For Each arg As Object In item.Tag
                            args.Add(arg)
                        Next
                    End If
                    frmItem = Activator.CreateInstance(frmType, args.ToArray())
                Catch ex As Exception
                    Common.LogException(ex.ToString())

                    Try
                        frmItem = Activator.CreateInstance(frmType)
                    Catch ex1 As Exception
                        Common.LogException(ex1.ToString())
                    End Try
                End Try

                CType(frmItem, tbBaseGuiForm).tbGuiBackground = Nothing
                frmItem.Tag = item.Tag
                Utility.AddForm2Panel(frmItem, Me.pnlContainer, True)
            End If
            frmItem.Visible = True
            frmItem.BringToFront()


            '设置其它窗体不可用，点击事件不会交叉
            'For Each ctl As Control In Me.pnlContainer.Controls
            '    If TypeOf ctl Is Form AndAlso ctl.GetType().FullName <> item.AccessibleDescription AndAlso Not TypeOf ctl Is frmMusic Then
            '        ctl.Visible = False
            '    End If
            'Next
            '设置其它窗体不可用，点击事件不会交叉
            For Each ctl As Control In Me.pnlContainer.Controls
                If TypeOf ctl Is Form AndAlso ctl IsNot frmItem Then
                    ctl.Visible = False
                End If
            Next

            Me.mSelectKey = CType(frmItem, tbBaseGuiForm).FunctionMappingKey

            Dim strText As String = String.Empty
            If Me.mDictFuncInfoMsg.ContainsKey(Me.mSelectKey) Then
                Dim dictText As StringDictionary = Me.mDictFuncInfoMsg(Me.mSelectKey)
                If dictText.ContainsKey(Me.mSelectKey.ToString()) Then
                    strText = dictText(Me.mSelectKey.ToString())
                End If
            End If
            Me.UpdateLabelText(strText)
            'Debug.Print(frmItem.Visible.ToString() & vbTab & frmItem.Location.ToString() & vbTab & frmItem.Size.ToString())

            If item.Checked Then
                Select Case item.Name
                    Case "More"
                        IniSetting.SetMoreShowNew()
                        item.tbShowNew = False
                    Case "Site"
                        IniSetting.SetWebSiteShowNew()
                        item.tbShowNew = False
                    Case "Video"
                        IniSetting.SetShowVideoNew()
                        item.tbShowNew = False

                    Case "Photo"
                        IniSetting.SetShowPhotoNew()
                        item.tbShowNew = False

                End Select
            End If

            Me.mClickItem.Checked = True
            Me.mClickItem = Nothing
            Me.SetClick(Me.mDevice, item.Name, DeviceType.iOS)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ToolItem_Click")
        End Try
    End Sub

    Private Sub UpdateToolButtonLocation()
        Dim intLeft As Integer = 8
        Dim intTop As Integer = 24

        If Common.VerIs30() Then
            intLeft = Me.mToolButtonLeft
            intTop = Me.mDeviceButtonLeft + Me.mDeviceButtonHeight - 1
        End If

        For Each item As tbRadioButton In Me.mListItems
            If Me.mCurrentDevice Is Nothing Then
                item.Visible = True
            End If

            If Me.mCurrentDevice Is Nothing OrElse item.Visible Then
                item.Location = New Point(intLeft, intTop)
                item.Height = 43
                intLeft += item.Width + 3
            End If
        Next
    End Sub

    Private Sub SetFormText()
        Dim strVer As String = String.Empty
        If My.Application.Info.Version.MinorRevision = 0 Then
            strVer = My.Application.Info.Version.ToString(3)
        Else
            strVer = My.Application.Info.Version.ToString(4)
        End If

        Dim strAgentTitle As String = String.Empty
        If AgentHelper.IsAgent Then
            strAgentTitle = AgentHelper.GetAgentTitle()
        End If

#If IS_ITONG_ZJ Then
        me.SetFormTitleByZJ(strVer, strAgentTitle)
#Else
        Me.SetFormTitleByZhushou(strVer, strAgentTitle)
#End If
    End Sub

    Private Sub SetFormTitleByZhushou(ByVal strVer As String, ByVal strAgentTitle As String)
        If Me.mDevice IsNot Nothing AndAlso Not String.IsNullOrEmpty(Me.mDevice.DeviceName) Then
            '"(试用到期：{0})"
            Me.Text = String.Format("{0}{3} {1} - {2} ", Me.Language.GetString("Main.Text.Name"), strVer, Me.mDevice.DeviceName, strAgentTitle) '& String.Format(Me.Language.GetString("Main.Message.TimeLimit"), RegistHelper.ExpireDateString)
        ElseIf Common.VerIs30() Then
            Me.Text = String.Format("{0}", Me.Language.GetString("Main.Text.Name")) '& String.Format(Me.Language.GetString("Main.Message.TimeLimit"), RegistHelper.ExpireDateString)
        Else
            Me.Text = String.Format("{0} {1}", Me.Language.GetString("Main.Text.Name"), strVer) '& String.Format(Me.Language.GetString("Main.Message.TimeLimit"), RegistHelper.ExpireDateString)
        End If

        If Not Common.VerIs30 Then
            Me.lblVersion.Visible = False
            Me.btnCheckUpdate.Visible = False
        End If

        Dim strBit As String = IIf(Common.IsX64, "64bit", "32bit")
        Me.lblVersion.Text = String.Format("{2}:{1} v{0}_{3}", strVer, strAgentTitle, Me.Language.GetString("App.Column.CurrentVersion"), strBit)

        Me.btnCheckUpdate.Location = New Point(Me.lblVersion.Right, Me.btnCheckUpdate.Top)
        Me.btnCheckUpdate.Text = String.Format("({0})", Me.Language.GetString("Main.Menu.CheckUpdate"))        '"检查更新"
    End Sub

    Private Sub SetFormTitleByZJ(ByVal strVer As String, ByVal strAgentTitle As String)

        If String.IsNullOrEmpty(strAgentTitle) Then
            strAgentTitle = "同步助手（商家版）"
        End If

        If Me.mDevice IsNot Nothing AndAlso Not String.IsNullOrEmpty(Me.mDevice.DeviceName) Then
            Me.Text = strAgentTitle & String.Format(" - {0} ", Me.mDevice.DeviceName)
        Else
            Me.Text = strAgentTitle
        End If

        Dim strBit As String = IIf(Common.IsX64, "64bit", "32bit")
        Me.lblVersion.Text = String.Format("{1}: v{0}_{2}", strVer, strAgentTitle, strBit)

        Me.btnCheckUpdate.Location = New Point(Me.lblVersion.Right, Me.btnCheckUpdate.Top)
        Me.btnCheckUpdate.Text = String.Format("({0})", Me.Language.GetString("Main.Menu.CheckUpdate"))        '"检查更新"
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.tsmSetting.Text = Me.Language.GetString("Common.Button.Setting") & "(&O)"                '参数设置(&O)
        Me.tsmUpdate.Text = Me.Language.GetString("Main.Menu.CheckUpdate") & "(&K)"             '"检查更新(&K)"
        Me.tsmiGetLogs.Text = Me.Language.GetString("Main.Menu.CreateError")        '"错误日志 "
        Me.tsmiSelectLang.Text = Me.Language.GetString("Main.Menu.SelectLang") & "(&L)"         '"选择语言(&L)"
        'Me.tsmiSelectSkin.Text = "选择皮肤" & "(&S)"         '"选择皮肤(&L)"
        Me.tsmiAbout.Text = Me.Language.GetString("Main.Menu.About") & "(&A)"                    '"关于(&A)"

        Me.btn_minimize.tbToolTip = Me.Language.GetString("Setting.RadioButton.MiniToTray")
        Me.btn_normal.tbToolTip = Me.Language.GetString("Common.Button.Maximize")               '"最大化"
        Me.btn_close.tbToolTip = Me.Language.GetString("Common.Button.Close")
        Me.btn_Setting.tbToolTip = Me.Language.GetString("Common.Button.Setting")
        Me.btnFeedBack.tbToolTip = Me.Language.GetString("Main.Button.Feedback")                '"Feedback"
        Me.tsmiFeedback.Text = Me.Language.GetString("Main.Button.Feedback")                    '"Feedback"
        ' Me.downloadBar.tbEmptyText = Me.Language.GetString("Main.Message.NoDownlaodTask")           '"当前没有下载任务"
        Me.downloadBar.tbFailureText = Me.Language.GetString("Download.Label.Failed")
        Me.downloadBar.tbPauseText = Me.Language.GetString("Download.Button.Pause")
        Me.downloadBar.tbWaitingText = Me.Language.GetString("Download.Button.Pause")           '"暂停"
        Me.downloadBar.tbRetryDown = Me.Language.GetString("Download.Label.Retry")              '"正在重试"
        Me.tsmChangeAccount.Text = Me.Language.GetString("Login.Label.ChangeAccount")           '"切换账号"
        Me.tsmLogout.Text = Me.Language.GetString("Login.Label.LogOff")                         '"注销"
        Me.btnLogin.Text = Me.Language.GetString("Common.Login")                                '"登录"
        Me.tsmTongbuAccountCenter.Text = Me.Language.GetString("Common.TongbuAccountCenter")    '"同步账号中心"

        Me.tsmiLogin.Text = Me.Language.GetString("Common.Login")                                 '"登录"
        Me.tsmTongbuPersonalCenterEx.Text = Me.Language.GetString("Login.Label.PersonalCenter")   '"个人中心"
        Me.tsmTongbuAccountCenterEx.Text = Me.Language.GetString("Common.TongbuAccountCenter")    '"同步账号中心"
        Me.tsmChangeAccountEx.Text = Me.Language.GetString("Login.Label.ChangeAccount")           '"切换账号"
        Me.tsmLogoutEx.Text = Me.Language.GetString("Login.Label.LogOff")                         '"注销"

        Me.SetButtonDownloadLocation()

        Me.btnTuiTutorial.Text = Me.Language.GetString("App.InstallTutorial.Label.EssentialSkilliOS9")  '"iOS9必备技能"
        Me.btnTuiTutorial.tbToolTip = Me.Language.GetString("Main.Message.TipForiOS9")                  '"解决iOS9系统打开软件提示[未受信任企业级开发者]问题，使应用取得信任。"

        Me.tsmiCoexistPay.Text = Me.Language.GetString("WeChat.Button.PayWeChatCoexist") '"共存版付费"

        Me.tsmSkin.Text = Me.Language.GetString("Skin.Label.Title")                         '皮肤


        If Folder.AppType = RunType.Tongbu_Abroad Then
#If IS_ITONG Then
            Me.btnSkin.Visible = False
            'AddHandler ChargeHelper.Instance().RegistrCompleted, AddressOf ChargeHelper_RegistrCompleted
            Me.mChargeHelper = ChargeHelper.Instance(ChargeProjectType.Assistant_Abroad)
            AddHandler Me.mChargeHelper.QueryCallback, AddressOf OnQueryCallback
            AddHandler Me.mChargeHelper.ActiveCallback, AddressOf OnActiveCallback
            Me.SetBuyButtonStatus()
            Me.mChargeHelper.Query()
#End If
        Else
            Me.tsmSkin.Visible = False
            Me.tssSkin.Visible = False
            Me.btnBuy.Visible = False
        End If
    End Sub

    Private mChargeHelper As ChargeHelper

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        If ServerIniSetting.ShowCoexistPay() Then
            Me.ToolStripSeparator4.Visible = True
            Me.tsmiCoexistPay.Visible = True
        Else
            Me.ToolStripSeparator4.Visible = False
            Me.tsmiCoexistPay.Visible = False
        End If

        '显示应用详细信息的事件放到界面层（iPhoneDeviceHelper）
        AddHandler iPhoneDeviceHelper.ShowAppDetailEvent, AddressOf OnShowAppDetail
    End Sub

    Private Sub OnMoreClick(ByVal sender As Object, ByVal e As EventArgs)
        If MobileDeviceManager.Instance().ConnectedDevices.Count = 0 Then
            Return
        End If

        '如果设备状态都是没连接的就不显示－－－－－－－－－－
        Dim isConnection As Boolean = False
        For Each Item As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
            If Item IsNot Nothing AndAlso Item.IsConnected Then
                isConnection = True
                Exit For
            End If
        Next
        If Not isConnection Then
            Return
        End If
        '－－－－－－－－－－－－－－－－－－－－－－－－－－

        Dim rdb As tbRadioButton = sender
        Dim posMore As New Point(rdb.tbMoreRect.Left + rdb.tbMoreRect.Width \ 2, rdb.tbMoreRect.Bottom)
        Dim posMoreToScreen As Point = rdb.PointToScreen(posMore)

#If IS_ITONG Then
        Dim frmList As New frmDeviceList(Me, sender)
        frmList.StartPosition = FormStartPosition.Manual
        frmList.Location = New Point(posMoreToScreen.X - 103, posMoreToScreen.Y + frmList.PaddingEx.Top - 5)
        frmList.Show()
#End If

    End Sub

    Dim mExistApp As Boolean = False
    Protected Overrides Sub OnFormClosing(ByVal e As System.Windows.Forms.FormClosingEventArgs)
        Dim listForms As New List(Of Form)
        For Each frm As Form In Application.OpenForms
            listForms.Add(frm)
        Next

        '关闭前先判断要不要关闭窗体
        For Each frm As Form In listForms
            If TypeOf frm Is frmDeviceBase Then
                Dim cnloseStatus As CloseStatus = CType(frm, frmDeviceBase).CheckCanClose
                If cnloseStatus = CloseStatus.UnClose Then
                    '如果不关闭窗体就取消操作
                    e.Cancel = True
                    CType(frm, frmDeviceBase).IsCanClose = CloseStatus.None
                    Return
                End If
            End If
        Next


        If ProcForm.Instance().CheckMainFormIsOnlyOne() Then
            ProcForm.Instance().CheckMainFormClosing(Me, e)

            ''--------------------------助手关闭窗体的时候发送用户行为数据收集-----------------------------
            ''如果处在发送的阶段用户再次点击打开用手，重新打开窗体不关闭
            ''如果发送后再点打开助手就关闭窗体
            'ProcForm.Instance().StartToSendOperateData(True)
            'ProcForm.Instance().ProcFormClosing = False
            'Common.Log(String.Format("SendOperateData5:Start {0}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")))
            ''停下0.1秒，让线程启动。
            'Utility.WaitSeconds(0.1)
            'While True
            '    If Not ProcForm.Instance().CheckSendingOperateData Then
            '        Exit While
            '    End If

            '    If ProcForm.Instance().ReActive Then
            '        ProcForm.Instance().ReActive = False
            '        e.Cancel = True
            '        Return
            '    End If
            'End While
            'Common.Log(String.Format("SendOperateData5:End {0}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")))
            ''-------------------------------------------------------------------------------------------------

            'If Not e.Cancel Then
            '    'TODO:判断当前设备有没有正在操作
            '    'If MessageBox.Show("当前设备正常同步操作，您确定要要关闭程序吗？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
            '    '    e.Cancel = True
            '    'End If
            'End If

            If Not e.Cancel Then
                mExistApp = True
                ProcForm.Instance().ProcFormClosing = True
            End If

        Else
            'TODO:判断当前设备有没有正在操作
            'If MessageBox.Show("当前设备正常同步操作，您确定要要关闭当前窗体吗？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
            '    e.Cancel = True
            'End If
        End If

        MyBase.OnFormClosing(e)
    End Sub

    ''' 释放所有局部变量——Added by Utmost20150504
    Private Sub ReleaseVaribles()
        If mCurrentDevice IsNot Nothing Then
            If mCurrentDevice.MobileDevice IsNot Nothing AndAlso TypeOf mCurrentDevice.MobileDevice Is AndroidDevice Then
                CType(mCurrentDevice.MobileDevice, AndroidDevice).Dispose()
            End If
            mCurrentDevice.Dispose()
        End If

        If mDictUsbDevice IsNot Nothing Then
            mDictUsbDevice.Clear()
            mDictUsbDevice = Nothing
        End If

        If mDictDeviceButton IsNot Nothing Then
            mDictDeviceButton.Clear()
            mDictDeviceButton = Nothing
        End If

        If mDictAppleDevice IsNot Nothing Then
            mDictAppleDevice.Clear()
            mDictAppleDevice = Nothing
        End If

        If mFrmAndroidFeedback IsNot Nothing Then
            mFrmAndroidFeedback.Dispose()
            mFrmAndroidFeedback = Nothing
        End If

        If mLstDeviceButtons IsNot Nothing Then
            mLstDeviceButtons.Clear()
            mLstDeviceButtons = Nothing
        End If

        If mLstKilledADBs IsNot Nothing Then
            mLstKilledADBs.Clear()
            mLstKilledADBs = Nothing
        End If


        _tmrMonitor.Stop()

        '_dictConnectedForms.Clear()
        '_dictConnectedForms = Nothing

    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)

        RemoveHandler Me.mDownManager.TaskStart, AddressOf OnTaskStart
        RemoveHandler Me.mDownManager.DownloadItemCompleted, AddressOf OnDownloaded
        RemoveHandler Me.mDownManager.TaskDelete, AddressOf OnTaskDelete
        RemoveHandler Me.mDownManager.TaskAdd, AddressOf OnTaskAdd
        RemoveHandler CoreUpdateHelper.Instance().CheckDownloadEventHandler, AddressOf OnCheckDownload
        RemoveHandler CoreUpdateHelper.Instance().ShowAppleIdLogin, AddressOf OnShowAppleIdLogin
        RemoveHandler CoreUpdateHelper.Instance().ShowAppDownloaded, AddressOf OnShowAppDownloaded

        Me.DisposeWebSiteHelper()

        If mExistApp Then

            If Common.VerIs30() Then
                Try
                    For Each usbDev As UsbDevice In mDictUsbDevice.Values
                        If usbDev.DeviceType = DeviceType.Android Then
                            If usbDev.MobileDevice IsNot Nothing Then
                                CType(usbDev.MobileDevice, AndroidDevice).ShowDaemonForm(False)
                            End If
                        End If
                    Next
                Catch ex As Exception
                    Debug.Write(ex)
                End Try
            End If

            Me.ReleaseVaribles()

            '' Added by Utmost20140508
            If mUsbDeviceMgr IsNot Nothing AndAlso Common.VerIs30() Then

                mUsbDeviceMgr.BrokenAllUsbDevice()
                mUsbDeviceMgr.KillAdbServer()
                mUsbDeviceMgr.StopListen()

                mUsbDeviceMgr = Nothing
            End If

            ProcForm.Instance().ExitApp()
        End If

        ''''' Added by Utmost20140122  ''''''''''''''''''''''
        Try
            For Each childProc As Process In Process.GetProcesses()
                Dim strProcName As String = childProc.ProcessName.ToLower()
                If strProcName.CompareTo("feedback") = 0 Then
                    '' kill feedback进程
                    childProc.Kill()
                    Utility.WaitSeconds(0.1)
                ElseIf strProcName.CompareTo("helper") = 0 Then
                    '' Added by Utmost20140126
                    '' 没有点升级，直接退出程序，避免重启程序后升级程序还存在。

                    '这两行代码去掉，如果helper还在运行助手会发指令去重新让升级窗体打开。
                    'childProc.Kill()
                    'Utility.WaitSeconds(0.1)
                End If
            Next
        Catch ex As Exception

        End Try

        'Me.KillAdbWeixin()

        ''''''''''''''''''''''''''''''''''''''''''''''

        Try
            Me.Hide()
            Dim lstForm As New List(Of Form)
            'For Each ctl As Control In Me.pnlContainer.Controls
            '    If TypeOf ctl Is Form Then
            '        lstForm.Add(ctl)
            '    End If
            'Next

            For Each frm As Form In Application.OpenForms
                If TypeOf frm Is MainForm OrElse TypeOf frm Is ProcForm Then
                    Continue For
                End If

                lstForm.Add(frm)
            Next

            For Each Item As Form In lstForm
                Item.Close()
            Next
            lstForm.Clear()
            lstForm = Nothing


            ''ReplaceDaemon()
        Catch ex As Exception
        End Try

    End Sub


    Private Sub MainForm_Disposed(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Disposed

        If Common.VerIs30 Then
            Try
                Dim p As Process = Process.GetCurrentProcess()

                For Each t As ProcessThread In p.Threads
                    t.Dispose()
                Next
            Catch ex As Exception
                Debug.Write(ex)
            End Try


            ' Added by Utmost20140701
            ' 此事件可能可以结束并释放主窗体并终止任务栏的该进程
            End
        End If

    End Sub

    Private Sub MainForm_Resize(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Resize

        If Me.btn_normal IsNot Nothing Then
            'Application.DoEvents()

            If Me.WindowState = FormWindowState.Maximized Then
                If Me.Language IsNot Nothing Then
                    Me.btn_normal.tbToolTip = Me.Language.GetString("Common.Button.Restore") '"还原"
                End If

                Me.btn_normal.tbBackgroundImage = My.Resources.btn_restore
                Me.btn_normal.tbBackgroundImageState = ImageState.ThreeState

            ElseIf Me.WindowState = FormWindowState.Normal Then
                If Me.Language IsNot Nothing Then
                    Me.btn_normal.tbToolTip = Me.Language.GetString("Common.Button.Maximize") '"最大化"
                End If
                Me.btn_normal.tbBackgroundImage = My.Resources.btn_max
                Me.btn_normal.tbBackgroundImageState = ImageState.ThreeState
            End If
        End If

    End Sub

    Private Sub InitDownloadManager()
        Me.mDownManager = MultiThreadDownload.Instance()

        AddHandler Me.mDownManager.TaskStart, AddressOf OnTaskStart
        AddHandler Me.mDownManager.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler Me.mDownManager.TaskDelete, AddressOf OnTaskDelete
        AddHandler Me.mDownManager.TaskAdd, AddressOf OnTaskAdd

        AddHandler CoreUpdateHelper.Instance().CheckDownloadEventHandler, AddressOf OnCheckDownload
        AddHandler CoreUpdateHelper.Instance().ShowAppleIdLogin, AddressOf OnShowAppleIdLogin
        AddHandler CoreUpdateHelper.Instance().ShowAppDownloaded, AddressOf OnShowAppDownloaded

        Me.SetDownloadCenterInfo()
    End Sub

    '''暂时使用原来的ShowDialog方式
    'Private Sub OnShowTipForm(ByVal app As PackageInfo)
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New CoreUpdateHelper.ShowDownloadedTipFrmHandle(AddressOf OnShowTipForm), app)
    '    Else
    '        ShowDownloadedTipFrm(app)
    '    End If
    'End Sub
    Private Sub ShowDownloadedTipFrm(ByVal app As PackageInfo)
        Dim frmAppTip As frmAppDownloaded = Common.CheckFormExist(frmAppDownloaded.GetType())
        Dim frmActiveFrm As Windows.Forms.Form = Windows.Forms.Form.ActiveForm
        If frmAppTip IsNot Nothing Then
            frmAppTip.AddPackage(app)
        Else
            frmAppTip = New frmAppDownloaded
            frmAppTip.AddPackage(app)
            frmAppTip.Show()
        End If
        If frmActiveFrm IsNot Nothing Then
            frmActiveFrm.Activate()
        End If
    End Sub

#Region "--- 连接 iPhone 委托事件 ---"

    Private Sub SetItemsState(ByVal blnReadonly As Boolean)
        For Each item As tbRadioButton In Me.mListItems 'Me._dictItems.Values '
            If item.tbIconReadOnly IsNot Nothing Then
                item.tbReadOnly = blnReadonly
            End If
        Next
    End Sub

    Private mLocker As New Object
    Private Delegate Sub iPhone_ConnectionChangedDelegate(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)

    Public Sub OnConnecting(ByVal sender As Object, ByVal args As EventArgs)
        '等待窗口句柄创建成功，避免 BeginInvoke 失败
        While Me.IsHandleCreated = False
            Application.DoEvents()
        End While

        BeginInvoke(New EventHandler(Of EventArgs)(AddressOf iPhone_Connecting), sender, args)
    End Sub

    Private Sub iPhone_Connecting(ByVal sender As Object, ByVal args As EventArgs)
        Try
            SyncLock mLocker
                If Me.mDevice Is Nothing AndAlso Me.mDeviceRecovery Is Nothing Then
                    Dim listCtl As New List(Of Control)
                    For Each ctl As Control In Me.pnlContainer.Controls
                        listCtl.Add(ctl)
                    Next
                    For Each ctl As Control In listCtl
                        If TypeOf ctl Is frmDeviceBase Then
                            CType(ctl, frmDeviceBase).OnConnecting()
                        End If
                    Next
                    listCtl.Clear()
                End If
            End SyncLock
        Catch ex As Exception
            Debug.Print(ex.ToString)
            Common.Log(ex.ToString)
        End Try
    End Sub

    Public Sub OnConnectionChanged(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        '等待窗口句柄创建成功，避免 BeginInvoke 失败
        While Me.IsHandleCreated = False
            Application.DoEvents()
        End While
        ' args.Device.DeviceEnterRecovery()

        BeginInvoke(New iPhone_ConnectionChangedDelegate(AddressOf iPhone_ConnectionChanged), sender, args)
    End Sub

    Private Sub iPhone_ConnectionChanged(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        Try
            If args.Device.ConnectMode = ConnectMode.WIFI Then
                Return
            End If

            SyncLock mLocker
                If Me.mDevice Is Nothing AndAlso Me.mDeviceRecovery Is Nothing Then
                    If args.Action = DeviceAction.kConnected Then
                        If Not args.Device.IsConnected Then
                            Dim listForm As New List(Of Form)
                            For Each frm As Form In Application.OpenForms

                                '' 不执行监控窗体
                                If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                                    Continue For
                                End If

                                listForm.Add(frm)
                            Next

                            For Each frm As Form In listForm
                                If TypeOf frm Is frmWelcome Then
                                    If CType(frm, frmWelcome).mDevice Is Nothing Then
                                        CType(frm, frmWelcome).OnDisconnect(args.Device)
                                    End If
                                End If
                            Next
                            listForm.Clear()

                        Else
                            Me.mDevice = args.Device

                            Me.SetItemsState(False)

                            '"{0} was connected."
                            ProcForm.Instance().ShowBalloonTip(String.Format(Me.Language.GetString("Main.Message.DeviceConnected"), Me.mDevice.DeviceName))

                            Dim listCtl As New List(Of Control)
                            For Each ctl As Control In Me.pnlContainer.Controls
                                listCtl.Add(ctl)
                            Next
                            For Each ctl As Control In listCtl
                                'Debug.Print(ctl.Name & vbTab & ctl.GetType().FullName)
                                If TypeOf ctl Is frmDeviceBase Then
                                    If Me.mDevice IsNot Nothing AndAlso (CType(ctl, frmDeviceBase).CheckResponse(args) OrElse ctl Is GetType(frmSite)) Then
                                        CType(ctl, frmDeviceBase).OnConnect(args.Device)
                                    End If
                                End If
                            Next
                            listCtl.Clear()

                            Dim listForm As New List(Of Form)
                            For Each frm As Form In Application.OpenForms
                                '' 不执行监控窗体
                                If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                                    Continue For
                                End If

#If IS_ITONG Then
                                If TypeOf frm Is frmDeviceBase OrElse TypeOf frm Is RingMakerForm Then
                                    listForm.Add(frm)
                                End If
#End If

                            Next
                            For Each frm As Form In listForm
                                If TypeOf frm Is frmDeviceBase Then
                                    If Me.mDevice IsNot Nothing AndAlso CType(frm, frmDeviceBase).CheckResponse(args) Then
                                        CType(frm, frmDeviceBase).OnConnect(args.Device)
                                    End If
                                End If

#If IS_ITONG Then
                                '铃声制作窗体连接事件
                                If TypeOf frm Is RingMakerForm Then
                                    If Me.mDevice IsNot Nothing AndAlso CType(frm, RingMakerForm).CheckResponse(args) Then
                                        CType(frm, RingMakerForm).OnConnect(args.Device)
                                    End If
                                End If
#End If

                            Next
                            listForm.Clear()
                        End If
                    End If

                ElseIf Me.mDevice IsNot Nothing Then
                    If Not Me.mDevice.IsConnected OrElse args.Device.SerialNumber = Me.mDevice.SerialNumber AndAlso args.Action = DeviceAction.kDisConnected Then
                        Me.SetLabelText(FunctionKey.Welcome, "")
                        Me.mDevice = Nothing
                        Me.SetItemsState(True)
                        Me.SetFormText()
                        Me.CheckGotoWelcome()

                        If Not ProcForm.Instance().CheckMainFormIsOnlyOne() Then
                            Me.Close()
                        Else
                            Dim listCtl As New List(Of Control)
                            For Each ctl As Control In Me.pnlContainer.Controls
                                listCtl.Add(ctl)
                            Next
                            For Each ctl As Control In listCtl
                                'Debug.Print(ctl.Name & vbTab & ctl.GetType().FullName)
                                If TypeOf ctl Is frmDeviceBase Then
                                    If CType(ctl, frmDeviceBase).CheckResponse(args) Then
                                        CType(ctl, frmDeviceBase).OnDisconnect(args.Device)
                                    End If
                                End If
                            Next
                            listCtl.Clear()

                            Dim listForm As New List(Of Form)
                            For Each frm As Form In Application.OpenForms
                                '' 不执行监控窗体
                                If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                                    Continue For
                                End If

                                listForm.Add(frm)
                            Next

                            For Each frm As Form In listForm
                                If TypeOf frm Is frmDeviceBase Then
                                    If CType(frm, frmDeviceBase).CheckResponse(args) Then
                                        'If Not ProcForm.Instance().CheckMainFormIsOnlyOne() Then
                                        '    Me.Hide()
                                        'End If

                                        CType(frm, frmDeviceBase).OnDisconnect(args.Device)
                                    End If
                                End If

#If IS_ITONG Then
                                '铃声制作窗体连接事件
                                If TypeOf frm Is RingMakerForm Then
                                    If CType(frm, RingMakerForm).CheckResponse(args) Then
                                        CType(frm, RingMakerForm).OnDisconnect(args.Device)
                                    End If
                                End If
#End If

                            Next
                            listForm.Clear()
                            If MobileDeviceManager.Instance().ConnectedDevices.Count > 0 Then
                                Dim tmpDevice As iPhoneDevice = MobileDeviceManager.Instance().ConnectedDevices(0)
                                Dim tmpArgs As New ConnectionChangedEventArgs(tmpDevice, 0, DeviceAction.kConnected)

                                Me.OnConnectionChanged(Nothing, tmpArgs)
                            End If
                        End If
                    End If
                End If
            End SyncLock
        Catch ex As Exception
            Debug.Print(ex.ToString)
            Common.Log(ex.ToString)
        End Try

        Me.SetFormText()
    End Sub

    '设备拔掉如果在文件，电子书等不可用的节点的时候自动转到welcome
    Private Sub CheckGotoWelcome()
        For Each item As tbRadioButton In Me.mListItems 'Me._dictItems.Values '
            If item.tbIconReadOnly IsNot Nothing AndAlso item.Checked Then
                Me.GotoItem(ActionFuncType.Welcome)
            End If
        Next
    End Sub

    Private Delegate Sub iPhone_RecoveryConnectionChangedDelegate(ByVal sender As Object, ByVal args As RecoveryConnectionChangedEventArgs)

    Public Sub OnRecoveryConnectionChanged(ByVal sender As Object, ByVal args As RecoveryConnectionChangedEventArgs)

        '等待窗口句柄创建成功，避免 BeginInvoke 失败
        While Me.IsHandleCreated = False
            Application.DoEvents()
        End While

        ' Debug.Print(args.DeviceSerialNumber)

        BeginInvoke(New iPhone_RecoveryConnectionChangedDelegate(AddressOf iPhone_RecoveryConnectionChanged), sender, args)

    End Sub

    Private Sub iPhone_RecoveryConnectionChanged(ByVal sender As Object, ByVal args As RecoveryConnectionChangedEventArgs)
        Try
            SyncLock mLocker
                If Me.mDevice Is Nothing AndAlso Me.mDeviceRecovery Is Nothing Then
                    If args.Action = DeviceAction.kConnected Then
                        Me.mDeviceRecovery = args.Device
                        '"{0} was connected."
                        ProcForm.Instance().ShowBalloonTip(String.Format(Me.Language.GetString("Main.Message.DeviceConnected"), Me.mDeviceRecovery.SerialNumber))

                        For Each ctl As Control In Me.pnlContainer.Controls
                            If TypeOf ctl Is frmDeviceBase Then
                                If CType(ctl, frmDeviceBase).CheckResponseRecovery(args) Then
                                    CType(ctl, frmDeviceBase).OnRecoverConnect(args.Device)
                                End If
                            End If
                        Next

                        For Each frm As Form In Application.OpenForms
                            '' 不执行监控窗体
                            If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                                Continue For
                            End If

                            If TypeOf frm Is frmDeviceBase Then
                                If CType(frm, frmDeviceBase).CheckResponseRecovery(args) Then
                                    CType(frm, frmDeviceBase).OnRecoverConnect(args.Device)
                                End If
                            End If
                        Next
                    End If

                ElseIf Me.mDeviceRecovery IsNot Nothing Then
                    If args.Device.SerialNumber = Me.mDeviceRecovery.SerialNumber AndAlso args.Action = DeviceAction.kDisConnected Then
                        Me.mDeviceRecovery = Nothing

                        If Not ProcForm.Instance().CheckMainFormIsOnlyOne() Then
                            Me.Close()
                        Else
                            For Each ctl As Control In Me.pnlContainer.Controls
                                If TypeOf ctl Is frmDeviceBase Then
                                    If CType(ctl, frmDeviceBase).CheckResponseRecovery(args) Then
                                        CType(ctl, frmDeviceBase).OnRecoverDisconnect(args.Device)
                                    End If
                                End If
                            Next

                            For Each frm As Form In Application.OpenForms

                                '' 不执行监控窗体
                                If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                                    Continue For
                                End If

                                If TypeOf frm Is frmDeviceBase Then
                                    If CType(frm, frmDeviceBase).CheckResponseRecovery(args) Then
                                        CType(frm, frmDeviceBase).OnRecoverDisconnect(args.Device)
                                    End If
                                End If

                            Next
                        End If
                    End If
                End If
            End SyncLock
        Catch ex As Exception
            Debug.Print(ex.ToString)
            Common.Log(ex.ToString)
        End Try

    End Sub

#End Region

#Region "--- 下载事件 ---"

    Private Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If Not Me.IsHandleCreated Then
            Return
        End If

        Me.BeginInvoke(New EventHandler(Of MultiThreadDownloadItem)(AddressOf OnTaskAddInvoke), sender, item)
    End Sub

    Private Sub OnTaskAddInvoke(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
#If IS_ITONG_ZJ Then
        '如果是插件或者强装的软件下载，则不显示进度
        If StrongInstallHelper.Instance().CheckIsStrongInstall(item.ItemInfo.FilePath) Then
            Return
        End If
#End If
        If (item.ItemInfo.Class = ResourceClass.Music OrElse item.ItemInfo.Class = ResourceClass.Ringtone OrElse _
           item.ItemInfo.Class = ResourceClass.Software OrElse item.ItemInfo.Class = ResourceClass.Video) AndAlso item.ItemInfo.ShowTip Then

            Me.SetToolTip(item.ItemInfo.Name, Me.Language.GetString("Download.Message.InsertToDownloadList").Replace("{0} ", ""))
            Me.SetDownloadCenterInfo()
        End If
    End Sub

    Private Sub OnTaskStart(ByVal sender As Object, ByVal e As MultiThreadDownloadItem)
        If Not Me.IsHandleCreated Then
            Return
        End If

        Me.BeginInvoke(New ThreadStart(AddressOf SetDownloadCenterInfo))
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        If Not Me.IsHandleCreated Then
            Return
        End If

        Try
            Me.BeginInvoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloadedInvoke), sender, e)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnDownloaded")
        End Try
    End Sub

    Private Sub OnDownloadedInvoke(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Dim item As MultiThreadDownloadItem = sender

        '安装插件。
        If item.ItemInfo.Class = ResourceClass.Plugins OrElse item.ItemInfo.Type = ResourceType.DEB Then
            Me.InstallAppSyncThread()

            '' Modified by Utmost20141119
            '' 下载完成分析Android守护apk写入Settings.ini文件
            If Common.VerIs30() AndAlso _
               String.Equals(Path.GetFileName(item.DownloadInfo.FilePath), AndroidDeviceDaemonMgr.DAEMON_FILENAME, StringComparison.InvariantCultureIgnoreCase) Then

                ThreadPool.QueueUserWorkItem(AddressOf GetDaemonInfoThread, item.DownloadInfo.FilePath)
            End If
        End If

        If item.ItemInfo.Class = ResourceClass.Music OrElse item.ItemInfo.Class = ResourceClass.Ringtone OrElse _
           item.ItemInfo.Class = ResourceClass.Software OrElse item.ItemInfo.Class = ResourceClass.Wallpaper OrElse _
           item.ItemInfo.Class = ResourceClass.Video Then


#If IS_ITONG_ZJ Then
            '如果是插件或者强装的软件下载，则不显示进度
            If StrongInstallHelper.Instance().CheckIsStrongInstall(item.ItemInfo.FilePath) Then
                Return
            End If
#End If
            If item.ItemInfo.Status = TaskState.Completed AndAlso item.ItemInfo.Class <> ResourceClass.Software Then
                Me.SetToolTip(Path.GetFileName(e.FilePath), Me.Language.GetString("Download.Cell.DoenloadCompleted"))
            End If
        End If
        Me.SetDownloadCenterInfo()

#If IS_ITONG Then
        '铃声下载完成后导入到设备（按先定的设备导入）
        If item.ItemInfo.Category.ToLower = "ringtone" AndAlso item.ItemInfo.Status = TaskState.Completed Then
            Dim lstRingtone As New List(Of String)
            lstRingtone.Add(item.ItemInfo.FilePath)

            Dim usbDevice As UsbDevice = Me.GetDevice(item.ItemInfo.Identifier)
            If usbDevice Is Nothing Then
                Return
            End If
            Dim helper As New iPhoneImportHelper(usbDevice, Me, Me)
            helper.StartImport(ImportType.Ringtone, lstRingtone, Me)
        End If
#End If
    End Sub

    Public Function GetDevice(ByVal strIdentify As String) As UsbDevice
        Dim devReturn As UsbDevice = Nothing
        Try
            If UsbDevices IsNot Nothing Then
                For Each dev As UsbDevice In Me.UsbDevices.Values
                    If dev IsNot Nothing AndAlso dev.MobileDevice IsNot Nothing AndAlso dev.MobileDevice.DeviceID.Equals(strIdentify, StringComparison.InvariantCultureIgnoreCase) Then
                        devReturn = dev
                        Exit For
                    End If
                Next
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "MainForm_GetDevice")
        End Try

        Return devReturn
    End Function

    Private Sub GetDaemonInfoThread(ByVal objInfo As Object)
        Try
            If objInfo Is Nothing Then
                Exit Try
            End If

            Dim strDaemon As String = objInfo.ToString()
            If File.Exists(strDaemon) Then
                Dim packDaemon As FileSharingPackageInfo = ApkReader.GetPackageInfo(strDaemon)
                If packDaemon IsNot Nothing Then
                    Try

                        For Each dev As UsbDevice In mDictUsbDevice.Values
                            If dev.DeviceType = DeviceType.Android Then
                                Exit For
                            End If
                        Next

                    Catch ex As Exception
                        Debug.Write(ex)
                    End Try

                    If File.Exists(packDaemon.PackagePath) Then
                        File.Copy(packDaemon.PackagePath, AndroidDeviceDaemonMgr.DAEMONPATH, True)
                    End If

                    IniSetting.SetLocalAndroidTuiVerCode(packDaemon.VersionNumber)
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDaemonInfoThread")
        End Try
    End Sub

    Private Sub OnTaskDelete(sender As Object, ByVal item As MultiThreadDownloadItem)
        If Not Me.IsHandleCreated Then
            Return
        End If

        Me.BeginInvoke(New ThreadStart(AddressOf SetDownloadCenterInfo))
    End Sub

    Private Sub OnCheckDownload(ByVal sender As Object, ByVal args As CheckDownloadArgs)
        If Not Me.IsHandleCreated Then
            Return
        End If

    End Sub

    Private Sub SetDownloadCenterInfo()
        Dim iDownloading As Integer = Me.mDownManager.DownloadingList.Count + Me.mDownManager.WaitingList.Count
        If iDownloading > 0 Then
            Me.btnDownload.tbIconImage = My.Resources.download_downloading
        Else
            Me.btnDownload.tbIconImage = My.Resources.download_stop
        End If

        Dim iCount As Integer = Me.mDownManager.GetCount()
        If iCount > 0 Then
            Me.btnDownload.tbText = Me.Language.GetString("Download.FormTitle") & String.Format("({0})", iCount)
        Else
            Me.btnDownload.tbText = Me.Language.GetString("Download.FormTitle")

            Dim sizeSaved As Long = MultiThreadDownloadDB.GetInstance.GetTotalResourceSize()
            If sizeSaved > 0 Then
                Me.downloadBar.tbEmptyText = String.Format(Me.Language.GetString("Download.Label.FlowSaved"), Utility.FormatFileSize(sizeSaved))
            Else
                Me.downloadBar.tbEmptyText = Me.Language.GetString("Main.Message.NoDownlaodTask")
            End If
        End If

        '如果窗体最小化后下载数量发生变化打开窗体会看不到下载中心的button.
        'If Me.Width > 400 Then
        '    Me.btnDownload.Location = New Point(Me.Width - Me.btnDownload.Width - 13, 3)
        'End If
        'Me.downloadBar.Location = New Point(Me.btnDownload.Left - Me.downloadBar.Width - 5, Me.downloadBar.Top)
    End Sub

    Private Sub SetButtonDownloadLocation()
        Dim tbText As String = Me.btnDownload.tbText
        Me.btnDownload.tbText = Me.Language.GetString("Main.Button.DownloadCenter") & String.Format("({0})", 10)
        Me.btnDownload.Location = New Point(Me.Width - Me.btnDownload.Width - 15, 4)
        Me.btnDownload.tbText = tbText

        Me.downloadBar.Location = New Point(Me.btnDownload.Left - Me.downloadBar.Width - 5, Me.downloadBar.Top)
    End Sub

    'Public Sub SetToolTip(ByVal strTaskName As String, ByVal strInfo As String)
    '    If Form.ActiveForm IsNot Nothing AndAlso TypeOf Form.ActiveForm Is frmTip Then
    '        Form.ActiveForm.Hide()
    '    End If

    '    Dim frmActive As Form = Form.ActiveForm
    '    If frmActive Is Me Then
    '        frmTip.ShowTip(strTaskName, strInfo, Me)
    '    ElseIf frmActive IsNot Nothing AndAlso TypeOf frmActive Is tbImageViewForm Then
    '        Return
    '    ElseIf frmActive IsNot Nothing AndAlso frmActive Is frmDownloadCenter.Instance Then
    '        frmTip.ShowTip(strTaskName, strInfo, Nothing)
    '        frmActive.Activate()
    '    ElseIf ProcForm.Instance().CheckMainFormIsOnlyOne() Then
    '        frmTip.ShowTip(strTaskName, strInfo, Nothing)
    '    End If
    'End Sub


    Private Delegate Sub SetToolTipHandler(ByVal strTaskName As String, ByVal strInfo As String)
    Public Sub SetToolTip(ByVal strTaskName As String, ByVal strInfo As String)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New SetToolTipHandler(AddressOf SetToolTip), strTaskName, strInfo)
        Else
            If Form.ActiveForm IsNot Nothing AndAlso TypeOf Form.ActiveForm Is frmTip Then
                Form.ActiveForm.Hide()
            End If

            Dim frmActive As Form = Form.ActiveForm
            If frmActive Is Me OrElse Me.Visible Then
                frmTip.ShowTip(strTaskName, strInfo, Me)
            ElseIf frmActive IsNot Nothing AndAlso TypeOf frmActive Is tbImageViewForm Then
                Return
            ElseIf frmActive IsNot Nothing AndAlso frmActive Is frmDownloadCenter.Instance Then
                frmTip.ShowTip(strTaskName, strInfo, Nothing)
                frmActive.Activate()
            ElseIf ProcForm.Instance().CheckMainFormIsOnlyOne() Then
                frmTip.ShowTip(strTaskName, strInfo, Nothing)
            End If
        End If
    End Sub


    Private Sub OnShowAppleIdLogin(ByVal sender As Object, ByVal args As ShowAppleIdLoginArgs)
        Dim frmLogin As frmiTunesLoginV3
        If args.Device IsNot Nothing AndAlso args.Info Is Nothing Then
            frmLogin = New frmiTunesLoginV3(args.Device, args.Mode)
            args.Id = Me.ShowLoginFormInvoke(frmLogin)
        ElseIf args.Device Is Nothing AndAlso args.Info IsNot Nothing Then
            frmLogin = New frmiTunesLoginV3(args.Info, args.Mode)
            args.Id = Me.ShowLoginFormInvoke(frmLogin)
        Else
            If frmiTunesLoginV3.CurrentUserId.Length > 0 AndAlso frmiTunesLoginV3.LoginSucceed Then
                args.Id = frmiTunesLoginV3.CurrentUserId
            End If
        End If
    End Sub

    Private Function ShowLoginFormInvoke(ByVal frmLogin As frmiTunesLoginV3) As String
        Dim strId As String = ""
        Try
            Dim frmOwner As Form = Form.ActiveForm
            If frmOwner Is Nothing OrElse TypeOf frmOwner Is ProcForm Then
                frmOwner = ProcForm.Instance().GetMainForm()
            End If
            If frmOwner IsNot Nothing Then
                frmLogin.Owner = frmOwner
                '当主窗体不可见或者没在可视频的位置时显示在桌面中间
                Try
                    If Not frmOwner.Visible OrElse frmOwner.Location.X < -1000 Then
                        frmLogin.StartPosition = FormStartPosition.CenterScreen
                    Else
                        frmLogin.StartPosition = FormStartPosition.CenterParent
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "ShowLoginFormLocation")
                End Try
            End If

            strId = ShowLoginFormInvokeInternel(frmLogin, frmOwner)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowLoginFormInvoke")
        End Try
        Return strId
    End Function

    Private Delegate Function ShowLoginFormInvokeInternelHandler(ByVal frmLogin As frmiTunesLoginV3, ByVal frmOwner As Form) As String
    Private Function ShowLoginFormInvokeInternel(ByVal frmLogin As frmiTunesLoginV3, ByVal frmOwner As Form) As String
        Dim strId As String = ""
        Try
            If frmLogin.InvokeRequired Then
                strId = frmLogin.Invoke(New ShowLoginFormInvokeInternelHandler(AddressOf ShowLoginFormInvokeInternel), frmLogin, frmOwner)
            ElseIf frmOwner IsNot Nothing AndAlso frmOwner.InvokeRequired Then
                strId = frmOwner.Invoke(New ShowLoginFormInvokeInternelHandler(AddressOf ShowLoginFormInvokeInternel), frmLogin, frmOwner)
            Else
                frmLogin.ShowDialog(frmOwner)
                strId = frmLogin.CurrentLoginAccount
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowLoginFormInvoke")
        End Try
        Return strId
    End Function


    Private Sub OnShowAppDownloaded(ByVal sender As Object, ByVal args As ShowAppDownloadedArgs)
        Me.ShowAppDownloaded(args.Info)
    End Sub

    Private Delegate Sub ShowAppDownloadedHandler(ByVal pInfo As PackageInfo)
    Private Sub ShowAppDownloaded(ByVal pInfo As PackageInfo)
        Dim frmMain As Form = ProcForm.Instance().GetMainForm()
        If frmMain Is Nothing Then
            Return
        End If

        If frmMain.InvokeRequired Then
            frmMain.Invoke(New ShowAppDownloadedHandler(AddressOf ShowAppDownloaded), pInfo)
        Else
            Dim form As frmAppDownloaded = Common.CheckFormExist(frmAppDownloaded.GetType())
            If form Is Nothing Then
                form = New frmAppDownloaded()
                form.AddPackage(pInfo)
                form.Show()
            Else
                form.AddPackage(pInfo)
            End If
        End If
    End Sub

#End Region

#Region "--- IApplication接口 ---"

    Public Event MainFormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Implements IApplication.MainFormClosing
    Public Event GoToSiteEvent(ByVal sender As tbWebBrowser, ByVal type As ActionFuncType, ByVal strNode As String, ByVal strDeviceID As String, ByVal strUrl As String) Implements IApplication.GoToSiteEvent

#Region "--- Service 实现 ---"

    Public Sub AddService(ByVal serviceType As System.Type, ByVal serviceInstance As Object) Implements System.ComponentModel.Design.IServiceContainer.AddService
        Me._ServiceContainer.AddService(serviceType, serviceInstance)
    End Sub

    Public Sub AddService(ByVal serviceType As System.Type, ByVal serviceInstance As Object, ByVal promote As Boolean) Implements System.ComponentModel.Design.IServiceContainer.AddService
        Me._ServiceContainer.AddService(serviceType, serviceInstance, promote)
    End Sub

    Public Sub AddService(ByVal serviceType As System.Type, ByVal callback As System.ComponentModel.Design.ServiceCreatorCallback) Implements System.ComponentModel.Design.IServiceContainer.AddService
        Me._ServiceContainer.AddService(serviceType, callback)
    End Sub

    Public Sub AddService(ByVal serviceType As System.Type, ByVal callback As System.ComponentModel.Design.ServiceCreatorCallback, ByVal promote As Boolean) Implements System.ComponentModel.Design.IServiceContainer.AddService
        Me._ServiceContainer.AddService(serviceType, callback, promote)
    End Sub

    Public Sub RemoveService(ByVal serviceType As System.Type) Implements System.ComponentModel.Design.IServiceContainer.RemoveService
        Me._ServiceContainer.RemoveService(serviceType)
    End Sub

    Public Sub RemoveService(ByVal serviceType As System.Type, ByVal promote As Boolean) Implements System.ComponentModel.Design.IServiceContainer.RemoveService
        Me._ServiceContainer.RemoveService(serviceType, promote)
    End Sub

    Public Shadows Function GetService(ByVal serviceType As System.Type) As Object Implements System.IServiceProvider.GetService
        Return Me._ServiceContainer.GetService(serviceType)
    End Function

#End Region

    Public ReadOnly Property LanguageInstance() As LanguageInterface Implements IApplication.Language
        Get
            Return Me.Language
        End Get
    End Property

    Private mAction As ActionFuncType = ActionFuncType.None
    Public Property Action() As ActionFuncType Implements IApplication.Action
        Get
            Return Me.mAction
        End Get
        Set(ByVal value As ActionFuncType)
            Me.mAction = value
        End Set
    End Property

    Public Sub SetLabelText(ByVal func As FunctionKey, ByVal strText As String) Implements IApplication.SetLabelText
        If mSelectKey = FunctionKey.None OrElse mSelectKey = func Then
            Me.UpdateLabelText(strText)
        End If
        If func <> FunctionKey.None Then
            Dim dictText As New StringDictionary
            If Me.mDictFuncInfoMsg.ContainsKey(func) Then
                dictText = Me.mDictFuncInfoMsg(func)
            Else
                Me.mDictFuncInfoMsg(func) = dictText
            End If

            If Me.mCurrentDevice Is Nothing Then
                dictText(func.ToString()) = strText
            Else
                dictText(Me.mCurrentDevice.DeviceID) = strText
            End If
        End If
    End Sub

    Private Sub ClearItemNumber(ByVal deviceId As String)
        If Me.mDictUpdateNumber.ContainsKey(deviceId) Then
            Me.mDictUpdateNumber.Remove(deviceId)
        End If

        For Each item As tbRadioButton In Me.mListItems ' Me._dictItems.Values '
            If item.Name.Equals(FunctionKey.App.ToString()) Then
                item.tbBadgeNumber = 0
                item.Invalidate()
                Exit For
            End If
        Next
    End Sub

    Private mDictUpdateNumber As New Dictionary(Of String, Integer)(StringComparer.InvariantCultureIgnoreCase)
    Public Sub SetItemNumber(ByVal func As FunctionKey, ByVal number As Integer, _
                             ByVal deviceId As String, _
                             Optional ByVal devType As DeviceType = DeviceType.iOS) Implements IApplication.SetItemNumber

        If Not String.IsNullOrEmpty(deviceId) Then
            Me.mDictUpdateNumber(deviceId) = number
        End If

        If Me.mCurrentDevice Is Nothing OrElse Me.mCurrentDevice.DeviceID <> deviceId Then
            Return
        End If

        'Dim strDeviceId As String = deviceId

        'If String.IsNullOrEmpty(strDeviceId) AndAlso Me.mCurrentDevice IsNot Nothing Then
        '    strDeviceId = Me.mCurrentDevice.DeviceID '.ToLower()
        'End If

        Dim strName As String = func.ToString()
        For Each item As tbRadioButton In Me.mListItems 'Me._dictItems.Values '
            If item.Name.Equals(strName) Then
                item.tbBadgeNumber = number
                item.Invalidate()
                Exit For
            End If
        Next

    End Sub

    Public Sub GotoWelcome(ByVal frmTag As Object) Implements IApplication.GotoWelcome
        For Each ctl As tbRadioButton In Me.mListItems 'Me._dictItems.Values '
            '找到选中ToolItem
            If Not ctl.Checked Then
                Continue For
            End If

            Try
                '如果当前选中ToolItem与执行跳转一致，则跳转到首页
                'Dim frmType As Type = System.Type.GetType(ctl.Name, True, False)
                If Common.VerIs30() Then
                    If ctl.AccessibleDescription.Contains(frmTag.GetType().FullName) Then
                        Me.mListItems(0).Checked = True
                        'Me._dictItems("Welcome").Checked = True
                        Exit For
                    End If
                Else
                    If frmTag.GetType().FullName = ctl.AccessibleDescription Then
                        Me.mListItems(0).Checked = True
                        'Me._dictItems("Welcome").Checked = True
                        Exit For
                    End If
                End If
            Catch ex As Exception
            End Try
        Next
    End Sub

    Private _IsDownloadingWallpaper As Boolean = False
    Public Property DownloadingWallPaper() As Boolean
        Get
            Return _IsDownloadingWallpaper
        End Get
        Set(ByVal value As Boolean)
            _IsDownloadingWallpaper = value
        End Set
    End Property


    Public Sub GotoItem(ByVal actType As ActionFuncType) Implements IApplication.GotoItem
        Me.mAction = actType
        Dim strName As String = actType.ToString()
        For Each item As tbRadioButton In Me.mListItems ' Me._dictItems.Values '
            If item.Name = strName Then
                item.Checked = True
                Exit For
            End If
        Next
    End Sub

    Public Sub GotoItemLocal(ByVal actType As ActionFuncType) Implements IApplication.GotoItemLocal

        Me.mAction = actType
        Dim itemTemp As tbRadioButton = Nothing
        Dim strName As String = actType.ToString()
        For Each item As tbRadioButton In Me.mListItems 'Me._dictItems.Values '
            If item.Name = strName Or (strName = ActionFuncType.Ringtone.ToString AndAlso item.Name = ActionFuncType.Music.ToString) Then
                itemTemp = item
                item.Checked = True
                Exit For
            End If
        Next

        Dim frmItem As frmDeviceBase = Nothing
        If itemTemp IsNot Nothing Then
            For Each ctl As Control In Me.pnlContainer.Controls
                If TypeOf ctl Is Form AndAlso ctl.GetType().FullName = itemTemp.Name AndAlso itemTemp.Tag Is ctl.Tag Then
                    Try
                        frmItem = ctl
                    Catch ex As Exception
                    End Try
                    Exit For
                End If
            Next
        End If

        If frmItem IsNot Nothing Then
            frmItem.GoToLocalNode(actType)
        End If

        If Me.WindowState = FormWindowState.Minimized Then
            '当窗体最小化时，先SHOW再设置WindowState，这样会窗体显示出来
            Me.Show()
            Me.WindowState = FormWindowState.Normal
            Me.Activate()
        End If
    End Sub

    Public Sub GotoSite(ByVal strSiteName As String, Optional ByVal strUrl As String = "") Implements IApplication.GotoSite
        Dim btnSite As tbRadioButton = Nothing
        Dim strName As String = ActionFuncType.Site.ToString()

        Try
            For Each item As tbRadioButton In Me.mListItems
                If item.Name = strName Then
                    btnSite = item
                    Exit For
                End If
            Next

            If btnSite Is Nothing AndAlso strSiteName.StartsWith("App", StringComparison.OrdinalIgnoreCase) Then
                For Each item As tbRadioButton In Me.mListItems
                    If item.Name = "App" Then
                        btnSite = item
                        Exit For
                    End If
                Next
            End If

            If btnSite Is Nothing Then
                Return
            End If

            btnSite.Checked = True
            '有的时候跳转到web会卡住。
            Application.DoEvents()


            Dim frmSrcSite As frmSite = Nothing
            Dim frmAppSite As frmSite = Nothing

            For Each frm As Form In Application.OpenForms
                If TypeOf frm Is frmSite AndAlso CType(frm, frmSite).mDevice Is Me.mDevice Then
                    If frm.Name = "web" Then
                        frmAppSite = frm

                    ElseIf frm.Name = "frmSite" Then
                        frmSrcSite = frm

                    End If
                End If
            Next

            If frmSrcSite IsNot Nothing Then
                frmSrcSite.SelectSite(strSiteName, strUrl)

            ElseIf frmAppSite IsNot Nothing Then
                frmAppSite.SelectSite(strSiteName, strUrl)

                If frmAppSite.Parent IsNot Nothing AndAlso frmAppSite.Parent.Parent IsNot Nothing AndAlso TypeOf frmAppSite.Parent.Parent Is frmApp Then
                    Dim frmApp As frmApp = frmAppSite.Parent.Parent
                    frmApp.tvwApp.SelectedNode = frmApp.tvwApp.Nodes("webappshare")
                End If

            End If

        Catch ex As Exception
        End Try

        '点击跳转的时候产页面显示到最前面
        If Me.WindowState = FormWindowState.Minimized Then
            Me.WindowState = FormWindowState.Normal
        End If
        Me.Activate()
    End Sub

    Public Sub SelectDevice(ByVal strDeviceID As String)
        For Each item As tbCheckButton In mLstDeviceButtons
            If item.Name = strDeviceID Then
                item.Checked = True
                Me.DeviceItem_Click(Me.mDictDeviceButton(strDeviceID), System.EventArgs.Empty)
            End If
        Next
    End Sub

    Public Sub GotoSite(ByVal type As ActionFuncType, ByVal strSiteName As String, ByVal strDeviceID As String, Optional ByVal strUrl As String = "") Implements IApplication.GotoSite
        For Each item As tbRadioButton In Me.mListItems 'Me._dictItems.Values '
            If item.Name = type.ToString Then
                item.Checked = True
                '有的时候跳转到web会卡住。
                Application.DoEvents()
                Exit For
            End If
        Next
        RaiseEvent GoToSiteEvent(Nothing, type, strSiteName, strDeviceID, strUrl)
        Me.Activate()
    End Sub

    Public Function GetClickedItem() As tbRadioButton Implements IApplication.GetClickedItem
        Dim itemResult As tbRadioButton = Nothing

        For Each item As tbRadioButton In Me.mListItems 'Me._dictItems.Values '
            If item.Checked Then
                itemResult = item
                Exit For
            End If
        Next

        Return itemResult
    End Function

    Private Delegate Sub UpdateLabelTextHandler(ByVal strText As String)
    Private Sub UpdateLabelText(ByVal strText As String)
        If Me.lblState.InvokeRequired Then
            Me.lblState.Invoke(New UpdateLabelTextHandler(AddressOf UpdateLabelText), strText)
        Else
            Me.lblState.Text = strText
        End If
    End Sub

#End Region

#Region "--- 窗体绘图、移动 ---"

    Private Sub InitDrawPara()
        Me.Icon = My.Resources.iTong
        Me.FilletRadius = 5
        Me.CanbeMove = True
        'Me.PaddingEx = New Padding(6)

        Me.tbGuiBackground = Nothing 'My.Resources.frm_bg_sub
        Me.tbSplit = "10,33,10,33"

        If Not Common.VerIs30 Then
            Me.BackColor = Color.FromArgb(20, 111, 184)
        End If

        Me.CanResize = True
        'Me.PNGForm.CanbeMove = False
        'Me.PNGForm.CanResize = False
    End Sub

    Private Sub SetPngFormBackground()
        Dim diffOut As Integer = 6
        'Dim bmp As New Bitmap(Me.Width + diffOut * 2, Me.Height + diffOut * 2)

        'Dim g As Graphics = Graphics.FromImage(bmp)
        'Dim tmpFont As Font = Common.CreateFont("Arial", 80, FontStyle.Regular)

        'g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic
        'g.Clear(Color.Transparent)


        ''绘制外框阴影
        'Dim rect As New Rectangle(0, 0, bmp.Width, bmp.Height)
        'GuiHelper.DrawImage(g, rect, New tbSplitStructure(11), My.Resources.frm_bg_shadow"))
        'GuiHelper.DrawText(g, "iTong PC Suite", tmpFont, rect, Me.BackColor, Color.Transparent, ContentAlignment.MiddleCenter)

        'tmpFont.Dispose()
        'g.Dispose()

        ''Me.PNGForm.SetPngBackground(bmp, 255)

        'bmp.Dispose()

        '' Modified by Utmost20150202
        Try
            Using bmp As New Bitmap(Me.Width + diffOut * 2, Me.Height + diffOut * 2)

                Dim rect As New Rectangle(0, 0, bmp.Width, bmp.Height)

                Using g As Graphics = Graphics.FromImage(bmp)

                    g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic
                    g.Clear(Color.Transparent)

                    GuiHelper.DrawImage(g, rect, New tbSplitStructure(11), My.Resources.frm_bg_shadow)

                    Using tmpFont As Font = Common.CreateFont("Arial", 80, FontStyle.Regular)

                        GuiHelper.DrawText(g, "iTong PC Suite", tmpFont, rect, Me.BackColor, Color.Transparent, ContentAlignment.MiddleCenter)

                    End Using

                End Using

            End Using

        Catch ex As Exception

        End Try
    End Sub

    Private Function RoundRect(ByVal r As RectangleF, ByVal r1 As Single, ByVal r2 As Single, ByVal r3 As Single, ByVal r4 As Single) As GraphicsPath
        Dim x As Single = r.X, y As Single = r.Y, w As Single = r.Width, h As Single = r.Height
        Dim rr As New GraphicsPath()
        rr.AddBezier(x, y + r1, x, y, x + r1, y, x + r1, y)
        rr.AddLine(x + r1, y, x + w - r2, y)
        rr.AddBezier(x + w - r2, y, x + w, y, x + w, y + r2, x + w, y + r2)
        rr.AddLine(x + w, y + r2, x + w, y + h - r3)
        rr.AddBezier(x + w, y + h - r3, x + w, y + h, x + w - r3, y + h, x + w - r3, y + h)
        rr.AddLine(x + w - r3, y + h, x + r4, y + h)
        rr.AddBezier(x + r4, y + h, x, y + h, x, y + h - r4, x, y + h - r4)
        rr.AddLine(x, y + h - r4, x, y + r1)
        Return rr
    End Function

    Private Sub PaintBackground(ByVal g As System.Drawing.Graphics)

        Dim cBorderOut As Color = Color.FromArgb(&H5E, &H8F, &HB6) ' Color.FromArgb(&H3E, &HA6, &HE7) ' Color.FromArgb(53, 124, 184)
        Dim cBorderIn As Color = Color.FromArgb(&H6E, &HBC, &HED)
        Dim cTop1 As Color = Color.FromArgb(&H3E, &HA6, &HE7)
        Dim cBg As Color = Color.FromArgb(233, 237, 243)

        Dim diff As Integer = 5
        Dim radius As Integer = diff - 1
        'Dim imgTop As Image = My.Resources.nav_top_bg

        'Rect的宽度和高度要减去1像素，WINDOWS绘图会默认向右向下偏一个像素
        Dim rectOut As New Rectangle(0, 0, Me.Width - 1, Me.Height - 1)
        Dim rectIn As New Rectangle(rectOut.Left + 1, rectOut.Top + 1, rectOut.Width - 1, 83)
        'Dim rectTop As New Rectangle(rectIn.Left, rectIn.Top + 1, rectIn.Width - 2, imgTop.Height)

        Dim gBorderOut As GraphicsPath = Me.RoundRect(rectOut, radius, radius, radius, radius)
        'Dim gBorderIn As GraphicsPath = Me.RoundRect(rectIn, radius, radius, radius, radius)
        'Dim gTop As GraphicsPath = Me.RoundRect(rectTop, radius, radius, 0, 0)

        g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic
        'g.Clear(cBorderOut)

        'g.DrawRectangle(GuiHelper.GetCachePen(cTop1), rectTop)
        'g.FillRectangle(GuiHelper.GetCacheBrush(cTop1), rectTop)

        If Common.VerIs30() Then
            Dim rectTop As New Rectangle(rectOut.Left, rectOut.Top - 1, rectOut.Width, Me.mDeviceButtonHeight + Me.mDeviceButtonTop)
            Dim rectTool As New Rectangle(rectTop.Left, rectTop.Bottom, rectTop.Width, Me.mToolButtonHeight)

            'g.FillRectangle(GuiHelper.GetCacheBrush(Me.mBgColorDeviceButton), rectTop)
            'g.DrawRectangle(GuiHelper.GetCachePen(Me.mBgColorDeviceButton), rectTop)

            'g.FillRectangle(GuiHelper.GetCacheBrush(Me.mBgColorToolButton), rectTool)
            'g.DrawRectangle(GuiHelper.GetCachePen(Me.mBgColorToolButton), rectTool)

            'If Me.mDictUsbDevice.Count > 0 Then
            '    g.DrawLine(GuiHelper.GetCachePen(Color.FromArgb(&H40, &H91, &HBD)), _
            '               New Point(rectTool.Left, rectTool.Top), New Point(rectTool.Right, rectTool.Top))
            'End If

            g.FillRectangle(mtbBackground, 0, My.Resources.new_top_bg.Height, Me.Width, Me.Height)
            g.FillRectangle(mtbTopBackground, -1, -1, Me.Width + 1, My.Resources.new_top_bg.Height)
            g.FillRectangle(mtbWaterMark, 0, 0, Me.Width, My.Resources.new_top_watermark.Height)

            '去掉显示5周年的logo
            'If Me.mShowFiveYearBar Then
            '    g.DrawImage(My.Resources.new_top_fiveyear, New Rectangle(Me.Width - 115 - My.Resources.new_top_fiveyear.Width, 30, My.Resources.new_top_fiveyear.Width, My.Resources.new_top_fiveyear.Height), New Rectangle(0, 0, My.Resources.new_top_fiveyear.Width, My.Resources.new_top_fiveyear.Height), GraphicsUnit.Pixel)
            'End If


            If Me.mDictUsbDevice.Count > 0 Then
                'g.DrawLine(GuiHelper.GetCachePen(Color.FromArgb(&H40, &H91, &HBD)), _
                '           New Point(rectTool.Left, rectTool.Top), New Point(rectTool.Right, rectTool.Top))
                g.FillRectangle(mtbLine, rectTool.Left, rectTool.Top - 2, Me.Width, My.Resources.new_top_line.Height)
            End If
        Else
            GuiHelper.DrawImage(g, New Rectangle(rectOut.Left, rectOut.Top + 1, rectOut.Width, 92), _
                                New tbSplitStructure(3), My.Resources.nav_top_bg)
        End If

        'g.DrawPath(GuiHelper.GetCachePen(cBorderIn), gBorderIn)

        ''填充底部
        'g.FillPath(GuiHelper.GetCacheBrush(cTop1), gTop)
        'g.DrawPath(GuiHelper.GetCachePen(cTop1), gTop)

        'Dim img As Image = My.Resources.pnl_bg_state
        Dim rectState As New Rectangle(1, Me.Height - 1 - My.Resources.pnl_bg_state.Height, Me.Width - 2, My.Resources.pnl_bg_state.Height)
        GuiHelper.DrawImage(g, rectState, New tbSplitStructure(2, 10, 2, 10), My.Resources.pnl_bg_state)


        If Not Common.VerIs30 Then
            '绘制蓝色边界线()
            g.DrawPath(GuiHelper.GetCachePen(cBorderOut), gBorderOut)
        End If

        gBorderOut.Dispose()
        'gTop.Dispose()
    End Sub

    Protected Overrides Sub OnPaintBackground(ByVal e As System.Windows.Forms.PaintEventArgs)
        MyBase.OnPaintBackground(e)
        Me.PaintBackground(e.Graphics)
    End Sub

    'Private Sub PaintBackground(ByVal g As System.Drawing.Graphics)
    '    Dim cBorder As Color = Color.FromArgb(20, 111, 184)
    '    Dim cTop1 As Color = Color.FromArgb(59, 158, 224)
    '    Dim cTop2 As Color = Color.FromArgb(38, 133, 204)
    '    Dim cBottom As Color = Color.FromArgb(233, 237, 243)

    '    Dim diffOut As Integer = 0
    '    Dim diff As Integer = 5
    '    Dim radius As Integer = diff - diffOut - 1
    '    Dim topHeight As Integer = 85

    '    Dim rect As New Rectangle(diffOut, diffOut, Me.Width - diffOut * 2 - 1, Me.Height - diffOut * 2 - 1)
    '    Dim rectTop As New Rectangle(rect.Left, rect.Top, rect.Width, radius)
    '    Dim rectTopLinear As New Rectangle(rectTop.Left, rectTop.Bottom, rectTop.Width, topHeight - radius)
    '    Dim rectBottom As New Rectangle(rectTopLinear.Left, rectTopLinear.Bottom, rectTopLinear.Width, rect.Bottom - rectTopLinear.Bottom)

    '    Dim brushTopLinear As New LinearGradientBrush(rectTopLinear, cTop1, cTop2, LinearGradientMode.Vertical)


    '    Dim gBorder As GraphicsPath = Me.RoundRect(rect, radius, radius, radius, radius)
    '    Dim gTop As GraphicsPath = Me.RoundRect(rectTop, radius, radius, 0, 0)
    '    Dim gBottom As GraphicsPath = Me.RoundRect(rectBottom, 0, 0, radius, radius)

    '    g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic
    '    g.Clear(cBorder)

    '    '填充底部
    '    g.FillPath(GuiHelper.GetCacheBrush(cTop1), gTop)
    '    g.DrawPath(GuiHelper.GetCachePen(cTop1), gTop)
    '    g.FillRectangle(brushTopLinear, rectTopLinear)

    '    '覆盖渐变矩型TOP边框线为cTOP2颜色的问题
    '    g.DrawLine(GuiHelper.GetCachePen(cTop1), New Point(rectTop.Left, rectTop.Bottom), New Point(rectTop.Right, rectTop.Bottom))

    '    '填充底部
    '    g.FillPath(GuiHelper.GetCacheBrush(cBottom), gBottom)

    '    Dim img As Image = My.Resources.pnl_bg_state
    '    Dim rectState As New Rectangle(1, Me.Height - 1 - img.Height, Me.Width - 2, img.Height)
    '    GuiHelper.DrawImage(g, rectState, New tbSplitStructure(2, 0, 2, 0), img)
    '    'g.DrawImage(My.Resources.pnl_bg_state, rectState)

    '    '绘制蓝色边界线
    '    g.DrawPath(GuiHelper.GetCachePen(cBorder), gBorder)

    '    brushTopLinear.Dispose()
    '    gBorder.Dispose()
    '    gTop.Dispose()
    '    gBottom.Dispose()

    'End Sub

    'Protected Overrides Sub OnSizeChanged(ByVal e As System.EventArgs)
    '    MyBase.OnSizeChanged(e)

    '    If Me.Visible AndAlso Me.WindowState = FormWindowState.Normal Then
    '        Me.SetPngFormBackground()
    '    End If
    'End Sub    

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btn_Setting_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btn_Setting.Click
        If Folder.LangType = LanguageType.th_TH AndAlso Folder.AppType = RunType.Tongbu Then
            Me.tss2.Visible = False
            Me.tsmiAbout.Visible = False
            Me.tsmiFeedback.Visible = False
        End If

        Try
            If ServerIniSetting.ShowCoexistPay() Then
                If Me.mCurrentDevice IsNot Nothing AndAlso mCurrentDevice.DeviceType = DeviceType.Android Then
                    Me.ToolStripSeparator4.Visible = False
                    Me.tsmiCoexistPay.Visible = False
                Else
                    Me.ToolStripSeparator4.Visible = True
                    Me.tsmiCoexistPay.Visible = True
                End If
            End If
        Catch
        End Try

        Me.menuMain.Show(Me.btn_Setting, New Point(0, Me.btn_Setting.Height))
    End Sub

    Private Sub btnDownload_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDownload.Click, downloadBar.Click
        Me.ShowDownloadCenter()
    End Sub

    Public Sub ShowDownloadCenter()
        If mCurrentDevice IsNot Nothing Then
            frmDownloadCenter.ShowForm(Me, mCurrentDevice.DeviceType)
        Else
            frmDownloadCenter.ShowForm(Me)
        End If
    End Sub

    Private Sub tsmSetting_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmSetting.Click
        If Common.VerIs30 Then
            Dim frmSetting As frmSettingV3 = New frmSettingV3(FunctionKey.None)
            frmSetting.Show(Me)
        Else
#If IS_ITONG Then
            Dim frmSetting As frmSetting = New frmSetting(FunctionKey.None)
            frmSetting.Show(Me)
#End If
        End If
    End Sub

    Private Sub tsmUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmUpdate.Click
        If CheckUpdate(Me, Me.Language) Then
            Return
        End If
        ProcForm.Instance().StartToCheckUpdate(True)
    End Sub

    'Private Sub tsmFeedback_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiFeedback.Click

    'End Sub

    Private Sub tsmAbout_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiAbout.Click
        'SinaWeibo.SendMessage("测试" & Now.ToString(), Me.mDevice.GetScreenshotImage(), "iPhone", 89.2, 75.5)
        'SinaWeibo.SendMessage("测试" & Now.ToString(), Common.ImageFromFile("C:\Documents and Settings\locustgxf\桌面\iTunesArtwork.jpg"), "", 89.2, 75.5)
        'Return
        'Common.OpenExplorer("http://www.tongbu.com")
        Dim frm As New frmAbout()
        frm.Show(Me)
        'frm.ShowDialog()
    End Sub

    Private Sub btnFeedBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFeedBack.Click, tsmiFeedback.Click
        If Folder.LangType = LanguageType.en_US Then
            Common.OpenUrl(WebUrl.PageFAQ)
            Return
        End If

        If Common.VerIs30() Then
            FeedbackAllUsbDevice()
        Else
            Utility.ShowFeedback()
        End If
    End Sub

    Private Sub FeedbackAllUsbDevice()

        Utility.ShowFeedback("", False)

        'Dim sbNoDriverDevices As StringBuilder = New StringBuilder()
        'Dim arrNoDriverDevices As List(Of UsbDevice) = New List(Of UsbDevice)
        'arrNoDriverDevices.AddRange(AndroidDeviceMonitor.Instance().DictHardwareUsbDevices.Values)
        'If arrNoDriverDevices.Count > 0 Then
        '    sbNoDriverDevices.AppendLine("感谢您反馈问题，希望您能填写以下信息")
        '    sbNoDriverDevices.AppendLine()
        '    sbNoDriverDevices.AppendLine("遇到的问题：")
        '    sbNoDriverDevices.AppendLine()
        '    sbNoDriverDevices.AppendLine("手机的品牌、型号：")
        '    sbNoDriverDevices.AppendLine()
        '    sbNoDriverDevices.AppendLine("********以下是同步助手的调试信息，请不要修改********")
        '    For Each dev As UsbDevice In arrNoDriverDevices
        '        sbNoDriverDevices.AppendLine(dev.InfoString())
        '    Next
        '    Utility.ShowFeedback(sbNoDriverDevices.ToString(), False)
        'Else
        '    Utility.ShowFeedback()
        'End If
    End Sub

    Private Sub btnFeedBack_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFeedBack.SizeChanged, btnTuiTutorial.SizeChanged, btnBuy.SizeChanged
        Me.btnFeedBack.Location = New Point(Me.btn_Setting.Left - Me.btnFeedBack.Width - 1, Me.btnFeedBack.Top)
        Me.btnSkin.Location = New Point(Me.btnFeedBack.Left - Me.btnSkin.Width - 1, Me.btnSkin.Top)

        Dim intLeft As Integer = Me.btnSkin.Left
        'Me.btnSkin.Visible = False

#If IS_ITONG_ZJ Then
        Me.btnSkin.Visible=false
        intLeft=Me.btnFeedBack.Left
        Me.btnTuiTutorial.Location = New Point(intLeft - Me.btnTuiTutorial.Width - 1 - 24, Me.btnTuiTutorial.Top)
#Else
        If Me.btnSkin.Visible = False Then
            intLeft = Me.btnFeedBack.Left
        End If
        If Me.btnBuy.Visible = True Then
            Me.btnBuy.Location = New Point(intLeft - Me.btnBuy.Width - 1, Me.btnBuy.Top)
            intLeft = Me.btnBuy.Left
        End If
        Me.btnTuiTutorial.Location = New Point(intLeft - Me.btnTuiTutorial.Width - 1, Me.btnTuiTutorial.Top)
#End If

    End Sub

    Private Sub btnDownload_Resize(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnDownload.Resize
        Me.btnDownload.Left = Me.pnlState.Width - 5 - btnDownload.Width
        Me.downloadBar.Left = Me.btnDownload.Left - downloadBar.Width
    End Sub

#End Region

#Region "--- Weibo分享 ---"

    Private Sub picWeibo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles picWeibo.Click
        'Dim a As String = ServerIniSetting.SetWeiboContent()
        Me.SendToWeibo()
    End Sub

    Private Sub SendToWeibo()
        WeiboHelper.SendMessage(ClientType.Sina, Me, Me.GetContent, Me.GetWeiboImage, "", Nothing, False)
    End Sub

    Private Function GetWeiboImage() As Image
        Dim img As Image = My.Resources.weibo_v3
        Dim strPicUrl As String = ServerIniSetting.GetWeiboPicURL()
        Dim strImgPath As String = Path.Combine(Folder.TempFolder, "Weibo")
        If strPicUrl.Length > 0 Then
            Folder.CheckFolder(strImgPath)
            strImgPath = Path.Combine(strImgPath, Path.GetFileName(strPicUrl))
            If Not File.Exists(strImgPath) Then
                Common.DownloadImage(strPicUrl, 20000, strImgPath)
            End If

            If File.Exists(strImgPath) Then
                img = Utility.GetImageFormFile(strImgPath)
            End If
        End If
        Return img
    End Function

    Private Function GetContent()
        Dim strContent As String = ServerIniSetting.GetWeiboContent()
        Dim strVer As String = String.Empty
        If My.Application.Info.Version.MinorRevision = 0 Then
            strVer = My.Application.Info.Version.ToString(3)
        Else
            strVer = My.Application.Info.Version.ToString(4)
        End If
        'strContent = iTong.CoreModule.ServerIniSetting.SetWeiboContent()
        If strContent.Length <= 0 Then
            '不用翻译只针对国内用户
            strContent = "#同步助手支持苹果安卓双平台#同步助手{0}震撼上线，左拥苹果右抱安卓~现在升级还送iPad Air2，别问我为什么，有钱就是任性！    下载同步助手即可参与活动：http://www.tongbu.com/zhushou/"
        End If
        strContent = String.Format(strContent, strVer)
        Return strContent
    End Function

    '设置web界面是否显示
    Private Delegate Sub SetWebboVisableHandler()
    Private Sub SetWebboVisable()
        If Me.InvokeRequired Then
            Me.Invoke(New SetWebboVisableHandler(AddressOf SetWebboVisable))
        Else
            If Not Common.VerIs30 OrElse Not ServerIniSetting.GetWeiboShow Then
                Return
            End If
            If MobileDeviceManager.Instance.ConnectedDevices.Count > 0 Then
                Me.picWeibo.Visible = True
            Else
                Me.picWeibo.Visible = False
            End If
            If Utility.IsPanda Then
                Me.picWeibo.Visible = False
            End If
        End If
    End Sub

#End Region

    Public Shared Function ItemShow(ByVal frmType As Type, ByVal device As IDevice, ByVal app As IApplication, Optional ByVal paras() As Object = Nothing) As Form
        Dim frmItem As Form = Nothing

        Try
            If frmType Is Nothing Then
                GoTo Do_Exit
            End If

            For Each frm As Form In Application.OpenForms

                '' 不执行监控窗体
                If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                    Continue For
                End If

                If TypeOf frm Is Form AndAlso frm.GetType().FullName.Equals(frmType.FullName) AndAlso frm.Tag Is paras Then
                    If device IsNot Nothing And TypeOf frm Is frmDeviceBase AndAlso CType(frm, frmDeviceBase).iPhone IsNot device Then
                        Continue For
                    End If

                    frmItem = frm
                    Exit For
                End If
            Next

            If frmItem Is Nothing Then
                Try
                    Dim args As New List(Of Object)
                    args.Add(app)
                    args.Add(device)

                    If paras IsNot Nothing AndAlso TypeOf paras Is Object() Then
                        For Each arg As Object In paras
                            args.Add(arg)
                        Next
                    End If

                    frmItem = Activator.CreateInstance(frmType, args.ToArray())
                Catch ex As Exception
                    frmItem = Activator.CreateInstance(frmType)
                End Try

                Dim frmBaseGui As tbBaseGuiForm = frmItem

                With frmBaseGui
                    .FilletRadius = 5
                    .Tag = paras
                    .StartPosition = FormStartPosition.Manual

                    Dim frmMain As Form = app
                    If .tbAutoSetFormSize Then
                        If .Height + 20 >= frmMain.Height Then
                            .Location = New Point(frmMain.Left + (frmMain.Width - frmItem.Width) / 2, frmMain.Top + 20)
                        ElseIf .Height + 65 >= frmMain.Height Then
                            .Location = New Point(frmMain.Left + (frmMain.Width - frmItem.Width) / 2, frmMain.Bottom - frmItem.Height)
                        Else
                            .Location = New Point(frmMain.Left + (frmMain.Width - frmItem.Width) / 2, frmMain.Top + (frmMain.Height - frmItem.Height) / 2)
                        End If
                    Else
                        .Size = New Size(frmMain.Width - 80, frmMain.Height - 85)
                        .Location = New Point(frmMain.Left + 40, frmMain.Top + 75)
                    End If

                End With
            End If

            If frmItem.WindowState = FormWindowState.Minimized Then
                frmItem.WindowState = FormWindowState.Normal
            End If

            If Not frmItem.Visible Then
                frmItem.Show()
            End If
            frmItem.Activate()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ItemShow")
        End Try

Do_Exit:
        Return frmItem
    End Function


    Public Shared Function ItemShowEx(ByVal frmType As Type, _
                                                                      ByVal strCtlName As String, _
                                                                      ByVal mobileDevice As IDevice, _
                                                                      ByVal app As IApplication, _
                                                                      Optional ByVal paras() As Object = Nothing, _
                                                                      Optional ByVal onlyCreateForm As Boolean = False, _
                                                                      Optional ByVal isShowDialog As Boolean = False, _
                                                                      Optional ByVal abType As AloneBackupType = AloneBackupType.None) As Form
        Dim frmItem As Form = Nothing
        Dim strTypeName As String = frmType.FullName

        Try
            If frmType Is Nothing Then
                GoTo Do_Exit
            End If

            For Each frm As Form In Application.OpenForms
                '' 不执行监控窗体
                If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                    Continue For
                End If

                Debug.Print(frm.Name)

                If frm.Name.Equals(strCtlName) OrElse frm.Name.Equals(strTypeName) Then
                    frmItem = frm
                    Exit For
                End If
            Next

            If frmItem Is Nothing Then
                Try
                    Dim args As New List(Of Object)
                    args.Add(app)

                    args.Add(mobileDevice)
                    If paras IsNot Nothing AndAlso TypeOf paras Is Object() Then
                        For Each arg As Object In paras
                            args.Add(arg)
                        Next
                    End If


                    If strTypeName.Equals("iTong.AppUnion.RingMakerFormAndroid") Then
                        ''' 安卓铃声制作——Added by Utmost20160317
                        Dim usbDev As UsbDevice = CType(app, MainForm).mCurrentDevice
                        If usbDev IsNot Nothing Then
                            args.Add(usbDev.HardwareId)
                        End If
                    End If

                    If strTypeName.Contains("frmWeixinEmoticonApp") Then
                        args.Add(False)
                    End If

                    If strTypeName.Contains("frmWeixinMain") Then
                        args.Add(abType)
                        IniSetting.SetShowWeChatCoexist()
                    End If

                    If strTypeName.Contains("frmIconManage") Then
                        args.Add(False)
                    End If

                    frmItem = Activator.CreateInstance(frmType, args.ToArray())
                Catch ex As Exception
                    frmItem = Activator.CreateInstance(frmType)
                End Try
                frmItem.Name = strCtlName

                Dim frmBaseGui As tbBaseGuiForm = frmItem
                With frmBaseGui
                    .FilletRadius = 5
                    .Tag = paras
                    .StartPosition = FormStartPosition.Manual

                    Dim frmMain As Form = app

                    Dim wid As Integer = (frmMain.Width - frmItem.Width) \ 2
                    If .tbAutoSetFormSize Then
                        If .Height + 20 >= frmMain.Height Then
                            .Location = New Point(frmMain.Left + wid, frmMain.Top + 20)
                        ElseIf .Height + 65 >= frmMain.Height Then
                            .Location = New Point(frmMain.Left + wid, frmMain.Bottom - frmItem.Height)
                        Else
                            .Location = New Point(frmMain.Left + wid, frmMain.Top + (frmMain.Height - frmItem.Height) \ 2)
                        End If
                    Else
                        .Size = New Size(frmMain.Width - 80, frmMain.Height - 85)
                        .Location = New Point(frmMain.Left + 40, frmMain.Top + 75)
                    End If

                End With

            ElseIf frmItem.Name = strTypeName Then
                frmItem.Name = strCtlName
            End If

            If frmItem.WindowState = FormWindowState.Minimized Then
                frmItem.WindowState = FormWindowState.Normal
            End If

            If Not onlyCreateForm Then
                If Not frmItem.Visible Then
                    If isShowDialog Then
                        frmItem.ShowDialog()
                    Else
                        frmItem.Show()
                    End If
                End If
                frmItem.Activate()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ItemShow")
        End Try

Do_Exit:
        Return frmItem
    End Function

    Private mHasListen As Boolean = False
    Private Sub tmrRun_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrRun.Tick
        Me.tmrRun.Stop()

        If mHasListen Then
            Return
        End If
        mHasListen = True

        Dim thd As New Thread(AddressOf Start2Listen)
        thd.IsBackground = True
        thd.Start()
    End Sub

    Public Shared Function CheckUpdate(ByVal frm As Form, ByVal lang As LanguageInterface) As Boolean
        Dim blnReturn As Boolean = False
        Try
            Dim kry As ModelKey = ModelKey.iTong32UnSupport
            '检测是否需要升级到32位助手
            '"检测到您安装的是32位的iTunes，\r\n无法正常使用同步助手（64位），\r\n请点击""确定""升级到正确的同步助手版本。"
            If Common.UpdateAppToX86() Then
                tbMessageBox.Show(frm, String.Format(lang.GetString("Main.Message.UpdateToNewVersion"), "32", "64"), _
                                 lang.GetString("Common.Info"), _
                                 MessageBoxButtons.OK, _
                                 MessageBoxIcon.Information, _
                                 MessageBoxDefaultButton.Button1)

                Dim strAppName As String = lang.GetString("Common.Tongbu")
                HelperExeManager.CheckUpdateApp2FitiTunes(True, strAppName, Application.ExecutablePath, HelperExeManager.CheckUpdateAction.CheckUpdateForX86)
                Common.LogException("需要升级到32位的助手")

                blnReturn = True
                kry = ModelKey.iTong64UnSupport
            End If

            '检测是否需要升级到64位助手
            If Common.UpdateAppToX64() Then
                tbMessageBox.Show(frm, String.Format(lang.GetString("Main.Message.UpdateToNewVersion"), "64", "32"), _
                                 lang.GetString("Common.Info"), _
                                 MessageBoxButtons.OK, _
                                 MessageBoxIcon.Information, _
                                 MessageBoxDefaultButton.Button1)

                Dim strAppName As String = lang.GetString("Common.Tongbu")
                HelperExeManager.CheckUpdateApp2FitiTunes(True, strAppName, Application.ExecutablePath, HelperExeManager.CheckUpdateAction.CheckUpdateForX64)
                Common.LogException("需要升级到64位的助手")

                blnReturn = True
                kry = ModelKey.iTong32UnSupport
            End If

            If blnReturn Then
                '收集助手是32还是64位
                ActionCollectHelper.ShowiTongAccition(Nothing, kry)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmMain_CheckUpdate")
        End Try
        Return blnReturn
    End Function

    Private Sub Start2Listen()
        Try
            Dim dt As Date = Now
            If CheckUpdate(Me, Me.Language) Then
                Return
            End If

            '自动登录、下载Sitexml、Vendor.ini、更新安卓守护、
            ServerIniSetting.DownloadSvrFiles()

            Debug.Print("CheckUpdate: " & Now.Subtract(dt).TotalMilliseconds)
            dt = Now

            If Common.VerIs30() Then
                If Utility.IsPanda() OrElse _
                    Folder.AppType = RunType.ZJHelper OrElse _
                    Folder.AppType = RunType.TongbuLite OrElse _
                    Not IniSetting.GetDervicePermission Then

                    If Not Common.CheckiTunesInstall() Then
                        Me.ShowiTunesInstall()
                        Return
                    End If
                Else
                    Me.mUsbDeviceMgr.StartListen()
                    Debug.Print("mUsbDeviceMgr.StartListen: " & Now.Subtract(dt).TotalMilliseconds)
                    dt = Now
                End If
            End If

            Me.StartListen()
            Debug.Print("Me.StartListen: " & Now.Subtract(dt).TotalMilliseconds)
            dt = Now

        Catch ex As Exception
            Common.LogException(ex.ToString(), "Start2Listen")
        End Try
    End Sub

    Private Sub ShowiTunesInstall()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ThreadStart(AddressOf ShowiTunesInstall))
        Else
            '' usb端口没有找到iOS设备
            Dim usb As New UsbDevice(Guid.NewGuid().ToString(), "vid_05ac", Nothing, DeviceType.iOS)
            usb.ConnectMode = ConnectMode.USB_Install_Driver
            usb.DeviceType = DeviceType.iOS

            Me.OnUsbChangedInvoke(Nothing, New UsbDeviceEventArgs(usb, ConnectionType.Connected))
        End If
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

        Try

            'Me.SetPngFormBackground()
            ProcForm.Instance().SetVirtualMemory()

            ProcForm.Instance().CloseSplash()
            'NotifyIcon  在mainForm显示的时候才提示。
            ProcForm.Instance.SetNotifyIconVisable(True)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnShown of MainForm")
        End Try


        If Common.VerIs30() Then
            Me.InitUsbMgr()
        End If

        '收集助手是32还是64位
        Dim kry As ModelKey = ModelKey.iTong32
        If Common.IsX64 Then
            kry = ModelKey.iTong64
        End If
        ActionCollectHelper.ShowiTongAccition(Nothing, kry)
        ActionCollectHelper.GetChannel()                    '收集助手渠道数据

        Try
            If ServerIniSetting.ShowPandaControl() Then
                Dim isShow As Boolean = IniSetting.GetShowPandaControl()
                If isShow Then
#If NET40 Then
                    Dim frm As frmPandaControlTip = New frmPandaControlTip()
                    frm.StartPosition = FormStartPosition.CenterParent
                    frm.ShowDialog(Me)
#End If
                End If
            End If
        Catch ex As Exception
            Common.Log(ex.ToString(), "PandaControlTip")
        End Try

        Me.tmrRun.Interval = 200
        Me.tmrRun.Start()
        Me.mFormLoaded = True

#If IS_ITONG Then
        Me.InitSkin()
#End If
    End Sub

    Private Sub InstallAppSyncThread()
        If Me.mDevice IsNot Nothing Then

            'Dim thr As New Threading.Thread(AddressOf InstallAppSync)
            'With thr
            '    .IsBackground = True
            '    .Name = "InstallAppSyncThread"
            '    .Start()
            'End With

            ThreadPool.QueueUserWorkItem(AddressOf InstallAppSync, Nothing)
        End If
    End Sub

    Private Sub InstallAppSync(ByVal objPara As Object)

        If Me.mDevice IsNot Nothing Then
            frmSummary.Install(Me.mDevice)
        End If

    End Sub

    ''' <summary>
    ''' android设备下载、安装驱动事件
    ''' </summary>
    Private Sub OnDoDriverDownloadInstall(ByVal sender As Object, ByVal e As UsbDriverEventArgs)

        'Dim arrAndroidWelcomeFrms As List(Of frmAndroidWelcome) = New List(Of frmAndroidWelcome)()
        Dim dictAndroidForms As Dictionary(Of String, frmAndroidWelcome) = New Dictionary(Of String, frmAndroidWelcome)(StringComparer.InvariantCultureIgnoreCase)
        Dim lstForms As New List(Of Form)

        Try
            For Each frm As Form In Application.OpenForms
                '' 不执行监控窗体
                If frm.Name.Equals("DaemonForm") OrElse frm.Name.Equals("ProcForm") Then
                    Continue For
                End If

                lstForms.Add(frm)
            Next
        Catch ex As Exception

        End Try


        Try
            For Each frm As Form In lstForms

                If TypeOf frm Is frmDeviceBase AndAlso (TypeOf frm Is frmAndroidWelcome) Then
                    Dim deviceId As String = e.Device.DeviceID

                    If frm.Name.StartsWith(deviceId, StringComparison.OrdinalIgnoreCase) OrElse _
                       (e.DriverMode = UsbDriverMode.ExeMode AndAlso _
                        (e.Status = ExceptionStatus.StartedStatus OrElse _
                         e.Status = ExceptionStatus.InstalledStatus OrElse _
                         e.Status = ExceptionStatus.CompletedStatus)) Then

                        ' 驱动安装完成，但没有单击完成按钮时候，对于华为设备Windows系统会自动删除原先设备。
                        ' Noted by Utmost20140410
                        'arrAndroidWelcomeFrms.Add(frm)

                        If Not dictAndroidForms.ContainsKey(deviceId) Then
                            dictAndroidForms.Add(deviceId, frm)
                        End If

                        Exit For
                    End If
                End If
            Next
        Catch ex As Exception

        End Try

        Application.DoEvents()

        For Each frm As frmAndroidWelcome In dictAndroidForms.Values
            frm.OnDoDriverDownloadInstall(e)
        Next

    End Sub

    Private Sub OnAndroidConnectFail(ByVal sender As System.Object, ByVal e As UsbDeviceEventArgs)

        ''' Modified by Utmost20140905
        Dim usbDev As UsbDevice = e.Device
        If usbDev Is Nothing Then
            Exit Sub
        End If

        If usbDev IsNot Nothing AndAlso usbDev.DeviceType = DeviceType.Android AndAlso usbDev.MobileDevice IsNot Nothing Then
            Dim adbDev As AndroidDevice = CType(usbDev.MobileDevice, AndroidDevice)

            If adbDev.IsConnected Then
                Exit Sub
            End If
        End If

        If (IniSetting.GetLastShowAndroidFeedback() - DateTime.Now).Days = 0 Then
            Return
        End If
        IniSetting.SetLastShowAndroidFeedback()
        'If mFrmAndroidFeedback Is Nothing OrElse mFrmAndroidFeedback.IsDisposed Then
        '    mFrmAndroidFeedback = New frmAndroidFeedback(Me)
        'End If
        'mFrmAndroidFeedback.Name = IIf(e.Device Is Nothing, "mFrmAndroidFeedback", e.Device.DeviceID)
        'mFrmAndroidFeedback.Show()
    End Sub

    Private mLastState As FormWindowState = FormWindowState.Normal
    Private Sub pnl_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pnlContainer.Resize
        'Common.Log(ShowStack(New StackTrace().GetFrames()))
        'Common.Log(pnlContainer.Height.ToString())
        If mLastState = FormWindowState.Minimized AndAlso Common.VerIs30() Then
            Me.UpdateContainer()
        End If
        Me.mLastState = Me.WindowState
    End Sub

    Public Shared Sub SetButtonStatus(ByVal ctrl As tbButton)
        Try
            If Common.VerIs30 AndAlso ctrl IsNot Nothing Then
                ctrl.tbTextColorHover = Color.FromArgb(88, 171, 216)
                ctrl.tbTextColorDown = Color.FromArgb(23, 120, 205)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnCheckUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCheckUpdate.Click
        If Me.mblnNewVersion = True Then
            Return
        End If
        ProcForm.Instance().StartToCheckUpdate(True)
    End Sub

    Private Delegate Sub SetIsNewVersionHandler()
    Public Sub SetIsNewVersion()
        If Me.InvokeRequired Then
            Me.Invoke(New SetIsNewVersionHandler(AddressOf SetIsNewVersion))
        Else
            Me.btnCheckUpdate.Text = String.Format("({0})", Me.Language.GetString("Main.Message.AlreadyNewVersion"))       '"已经是最新版本"
            Me.mblnNewVersion = True
        End If
    End Sub

#Region "--- 登录事件 ---"

    Private Sub tsmiLogin_Click(sender As Object, e As EventArgs) Handles tsmiLogin.Click
        If Not Me.mPluginLogin.CheckLogin Then
            frmLogin.ShowLogin(Me)
            Return
        End If
    End Sub

    Private Sub btnLogin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLogin.Click

        If Not Me.mPluginLogin.CheckLogin Then
            frmLogin.ShowLogin(Me)
            Return
        End If

#If IS_ITONG Then
        If ServerIniSetting.GetShowPersonalCenter Then
            frmRepairPayment.ShowPersonalCenter(Me, Me, Nothing)
            Return
        End If
#End If

        '已经登录账号，再点击就显示注销菜单
        Me.menuLogout.Show(Me.btnLogin, New Point(Me.btnLogin.Width - 10, 10))
    End Sub

    Private Sub btnLogin_MouseClick(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles btnLogin.MouseClick
        If Me.mPluginLogin.IsLogin AndAlso e.Button = Windows.Forms.MouseButtons.Right Then
            Me.menuLogout.Show(Me.btnLogin, New Point(Me.btnLogin.Width - 10, 10))
        End If
    End Sub

    Private Sub tsmTongbuAccountCenter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmTongbuAccountCenter.Click, tsmTongbuAccountCenterEx.Click
        '"https://id.tongbu.com/login"
        Common.OpenExplorer(WebUrl.PageLogin)
    End Sub

    Private Sub PluginLogin_LoginEvent(ByVal sender As Object, ByVal e As EventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(AddressOf PluginLogin_LoginEvent), sender, e)
        Else
            Try
                If sender Is Nothing OrElse Me.IsDisposed Then
                    Return
                End If
                Dim loginObj As PluginLogin = sender
                If loginObj.IsLogin Then

                    Me.btnLogin.Text = Common.StringFormat(loginObj.Name, 15, True, False)
                    If Me.btnLogin.Text.Trim <> loginObj.Name Then
                        Me.btnLogin.tbToolTip = loginObj.Name
                    End If
                    Me.DownloadIcon()
                    Dim uinfo As New UserInfo()
                    uinfo.Account = loginObj.Account
                    uinfo.UId = loginObj.Uid
                    uinfo.Token = loginObj.Token
                    IniSetting.SetUserInfo(uinfo)
                End If
                For Each item As ToolStripItem In Me.tsmiLogin.DropDownItems
                    item.Visible = True
                Next
                Me.tsmiLogin.Text = Me.Language.GetString("Common.Login") & String.Format("({0})", loginObj.Name)            '"登录"
            Catch ex As Exception
                Common.LogException(ex.ToString(), "MainForm_PluginLogin_LoginEvent")
            End Try
        End If
    End Sub

    Private Sub PluginLogin_LogoutEvent(ByVal sender As Object, ByVal e As EventArgs)
        Me.btnLogin.tbToolTip = ""
        Me.btnLogin.Text = Me.Language.GetString("Common.Login")            '"登录"
        Me.SetUsrtIcon(Nothing, Nothing)
        IniSetting.SetUserInfo(Nothing)
        For Each item As ToolStripItem In Me.tsmiLogin.DropDownItems
            item.Visible = False
        Next
        Me.tsmiLogin.Text = Me.Language.GetString("Common.Login")            '"登录"
    End Sub

    Private Sub DownloadIcon()
        Try
            If Me.mThrDownIcon IsNot Nothing AndAlso Me.mThrDownIcon.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mThrDownIcon = New Thread(AddressOf DownloadIconThread)
            With mThrDownIcon
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "PluginLogin_Login")
        End Try
    End Sub

    Private Sub DownloadIconThread()
        Try
            If Not String.IsNullOrEmpty(Me.mPluginLogin.Icon) Then
                Dim strPathLogin As String = Path.Combine(Folder.CacheFolder, "Login")
                Dim strPath As String = strPathLogin
                Dim strPathNormal As String = strPathLogin
                Dim strPathLight As String = strPathLogin

                Folder.CheckFolder(strPath)
                strPath = Path.Combine(strPath, String.Format("{0}_{1}_1.jpg", Utility.ReplaceWinIllegalName(Me.mPluginLogin.Account), Me.mPluginLogin.IconTime, Skin.Instance.GetSkinSetting))
                strPathNormal = Path.Combine(strPathNormal, String.Format("{0}_{1}_{2}normal_1.jpg", Utility.ReplaceWinIllegalName(Me.mPluginLogin.Account), Me.mPluginLogin.IconTime, Skin.Instance.GetSkinSetting))
                strPathLight = Path.Combine(strPathLight, String.Format("{0}_{1}_{2}light_1.jpg", Utility.ReplaceWinIllegalName(Me.mPluginLogin.Account), Me.mPluginLogin.IconTime, Skin.Instance.GetSkinSetting))

                Dim img As Image = Nothing
                Dim imgLight As Image = Nothing
                Try
                    If Not File.Exists(strPath) OrElse Not File.Exists(strPathNormal) OrElse Not File.Exists(strPathLight) Then
                        Me.CreateIcon(strPath, strPathNormal, False)
                        Me.CreateIcon(strPath, strPathLight, True)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString, "MainForm_DownloadIconThread_Save")
                End Try

                If File.Exists(strPathNormal) Then
                    img = Utility.GetImageFormFile(strPathNormal)
                End If
                If File.Exists(strPathLight) Then
                    imgLight = Utility.GetImageFormFile(strPathLight)
                End If

                Me.SetUsrtIcon(img, imgLight)
            Else
                Me.SetUsrtIcon(Nothing, Nothing)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "MainForm_DownloadIconThread")
        End Try
    End Sub

    Private Sub CreateIcon(ByVal strPath As String, ByVal strPathDes As String, ByVal isLight As Boolean)
        Dim img As Image = Nothing
        If Not File.Exists(strPath) Then
            Common.DownloadImage(Me.mPluginLogin.Icon, 2000, strPath)
        End If
        img = Utility.GetImageFormFile(strPath)

        img = Utility.GetThumbnail(img, My.Resources.icon_login_empty.Size)
        If isLight Then
            img = Utility.GetThumbnailEllipse(img, My.Resources.icon_login_empty_light.Clone)
        Else
            img = Utility.GetThumbnailEllipse(img, My.Resources.icon_login_empty.Clone)
        End If

        img.Save(strPathDes, Imaging.ImageFormat.Png)
    End Sub

    Private Delegate Sub SetUsrtIconHandler(ByVal img As Image, ByVal imgLight As Image)
    Private Sub SetUsrtIcon(ByVal img As Image, ByVal imgLight As Image)
        If Me.InvokeRequired Then
            Me.Invoke(New SetUsrtIconHandler(AddressOf SetUsrtIcon), img, imgLight)
        Else
            If img IsNot Nothing Then
                Me.btnLogin.Padding = New Padding(0, 4, 0, 0)
                Me.btnLogin.tbIconImage = img
                Me.btnLogin.tbIconMouseHover = imgLight
                Me.btnLogin.tbIconMouseDown = imgLight
                'Me.btnLogin.tbIconMore = True
            Else
                Me.btnLogin.Padding = New Padding(0, 10, 0, 0)
                Me.btnLogin.tbIconImage = My.Resources.icon_login.Clone
                Me.btnLogin.tbIconMouseHover = Nothing
                Me.btnLogin.tbIconMouseDown = Nothing
                'Me.btnLogin.tbIconMore = False
            End If
        End If
    End Sub

    Private Sub tsmChangeAccount_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmChangeAccount.Click, tsmChangeAccountEx.Click
        Me.mPluginLogin.Logout()
        frmLogin.ShowLogin(Me, True)
    End Sub

    Private Sub tsmLogout_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmLogout.Click, tsmLogoutEx.Click
        Me.mPluginLogin.Logout()
    End Sub

    Private Sub tsmTongbuPersonalCenterEx_Click(sender As Object, e As EventArgs) Handles tsmTongbuPersonalCenterEx.Click
        frmRepairPayment.ShowPersonalCenter(Me, Me, Nothing)
    End Sub

#End Region

    Private Sub btnGetLog_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If tbMessageBox.Show("您确定要生成错误日志吗？", "信息", MessageBoxButtons.OKCancel) = Windows.Forms.DialogResult.OK Then
            MainForm.CreateErrorZip()
        End If
    End Sub

    '创建获取日志按钮
    Private Sub CreateLogButton()

#If IS_ITONG_ZJ Then
        btnGetLog.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        btnGetLog.BackColor = System.Drawing.Color.Transparent
        btnGetLog.BindingForm = Nothing
        btnGetLog.Cursor = System.Windows.Forms.Cursors.Hand
        btnGetLog.Font = New System.Drawing.Font("宋体", 8.0!)
        btnGetLog.Name = "btnGetLog"
        btnGetLog.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        btnGetLog.Selectable = True
        btnGetLog.Size = New System.Drawing.Size(24, 24)
        btnGetLog.TabIndex = 24
        btnGetLog.tbAdriftIconWhenHover = False
        btnGetLog.tbAutoSize = True
        btnGetLog.tbAutoSizeEx = True
        btnGetLog.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        btnGetLog.tbBadgeNumber = 0
        btnGetLog.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        btnGetLog.tbEndEllipsis = False
        btnGetLog.tbIconHoldPlace = True
        btnGetLog.tbIconImage = Nothing
        btnGetLog.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        btnGetLog.tbIconImageState = iTong.Components.ImageState.ThreeState
        btnGetLog.tbIconMore = False
        btnGetLog.tbIconMouseDown = Nothing
        btnGetLog.tbIconMouseHover = Nothing
        btnGetLog.tbIconMouseLeave = Nothing
        btnGetLog.tbIconPlaceText = 2
        btnGetLog.tbIconReadOnly = Nothing
        btnGetLog.tbImageMouseDown = Nothing
        btnGetLog.tbImageMouseHover = Nothing
        btnGetLog.tbImageMouseLeave = Nothing
        btnGetLog.tbReadOnly = False
        btnGetLog.tbReadOnlyText = False
        btnGetLog.tbShadow = False
        btnGetLog.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        btnGetLog.tbShadowOffset = New System.Drawing.Point(1, 1)
        btnGetLog.tbShowNew = False
        btnGetLog.tbShowToolTipOnButton = False
        btnGetLog.tbSplit = "13,11,13,11"
        btnGetLog.tbText = ""
        btnGetLog.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        btnGetLog.tbTextColor = System.Drawing.Color.WhiteSmoke
        btnGetLog.tbTextColorDisable = System.Drawing.Color.WhiteSmoke
        btnGetLog.tbTextColorDown = System.Drawing.Color.WhiteSmoke
        btnGetLog.tbTextColorHover = System.Drawing.Color.White
        btnGetLog.tbTextMouseDownPlace = 0
        btnGetLog.tbToolTip = "生成日志"
        btnGetLog.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        btnGetLog.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        btnGetLog.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        btnGetLog.Visible = False
        btnGetLog.VisibleEx = True
        btnGetLog.Visible = True
        btnGetLog.tbBackgroundImage = My.Resources.btn_openlog
        btnGetLog.Location = New Point(Me.btn_Setting.Left - Me.btnFeedBack.Width - 1 - btnGetLog.Width - 1, Me.btnGetLog.Top)
        Me.Controls.Add(btnGetLog)
removeHandler btnGetLog.Click, AddressOf btnGetLog_Click
        AddHandler btnGetLog.Click, AddressOf btnGetLog_Click
#End If

    End Sub

#Region "---  错误信息处理  ---"

    Public Shared Sub CreateErrorZip()
        Try

            Dim tmpFile As String = Path.Combine(Folder.TempFolder, "ErrorZip\" & Guid.NewGuid().ToString("N").Substring(0, 10))
            Dim tmpFileException As String = Path.Combine(tmpFile, "Exception")
            Dim tmpFileLog As String = Path.Combine(tmpFile, "Logs")
            Dim tmpFileSend As String = Path.Combine(tmpFile, "Send")
            Dim tmpFileInstall As String = Path.Combine(tmpFile, "Install")
            Try
                Directory.Delete(tmpFile, True)
            Catch ex As Exception
            End Try

            Folder.CheckFolder(tmpFileException)
            Folder.CheckFolder(tmpFileLog)
            Folder.CheckFolder(tmpFileSend)
            Folder.CheckFolder(tmpFileInstall)

            Dim strSourcePath As String = Folder.DocumentFolder
            MainForm.GetErrorFile(Path.Combine(strSourcePath, "Exception"), tmpFileException, Nothing)
            MainForm.GetErrorFile(Path.Combine(strSourcePath, "Logs"), tmpFileLog, Nothing)

            Dim SendFolder As String = Path.Combine(strSourcePath, "Data\Send")
            MainForm.GetSendLog(SendFolder, tmpFileSend)

            Dim InstallFolder As String = Path.Combine(strSourcePath, "Data\Install")
            MainForm.GetInstallDeviceLog(InstallFolder, tmpFileInstall)

            For Each device As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
                Dim tmpFileDeviceLog As String = Path.Combine(tmpFile, String.Format("DeviceLog{0}", Utility.ReplaceWinIllegalName(device.SerialNumber)))
                Folder.CheckFolder(tmpFileDeviceLog)
                MainForm.GetErrorFile("/Documents", tmpFileDeviceLog, device)
            Next

            '压缩
            Dim strZipFilePath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), String.Format("LOG{0}-{1}.zip", DateTime.Now.ToString("yyyyMMddHHmmss"), PluginLogin.Instance().Name))
            Utility.PackFiles(strZipFilePath, tmpFile)
            Common.OpenExplorer(strZipFilePath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateErrorZip")
        End Try

    End Sub

    Private Shared Sub GetErrorFile(ByVal sourcePath As String, ByVal desPath As String, ByVal device As iPhoneDevice)
        '这个方法要取三个地方的数据
        '1.我的文档下面Exception,Log,
        '2.设备上苹果游戏商店里面的log
        '只取今天之前七天的数据
        Dim strFileName As String = String.Format("{0}{1}.txt", Path.GetFileNameWithoutExtension(sourcePath).TrimEnd("s"), "{0}")
        Dim fileList As New List(Of String)

        If device IsNot Nothing Then
            strFileName = "LOG{0}.log"
        End If

        For index As Integer = 0 To 6
            fileList.Add(String.Format(strFileName, (DateTime.Now.AddDays(-index)).ToString("yyyyMMdd")))
        Next
        Try
            '取log Exception
            If device Is Nothing Then
                If Not Directory.Exists(sourcePath) Then
                    Return
                End If
                For Each Item As String In Directory.GetFiles(sourcePath)
                    If fileList.Contains(Path.GetFileName(Item)) Then
                        Try
                            File.Copy(Item, Path.Combine(desPath, Path.GetFileName(Item)))
                        Catch ex As Exception
                        End Try
                    End If
                Next
            Else
                '设备上log
                Dim handler As IntPtr = IntPtr.Zero ' device.GetAfcByFileSharing("com.tongbu.AGSPlan")
#If IS_ITONG_ZJ Then
                handler=AppleStoreHelper.GetAfcByFileSharingEx(device)
#End If
                If Not device.Exists(handler, sourcePath) Then
                    Return
                End If
                For Each Item As String In device.GetFiles(handler, sourcePath)
                    Debug.Print(Item)
                    If fileList.Contains(Item) Then
                        Try
                            device.DownFromPhone(handler, sourcePath & "/" & Item, Path.Combine(desPath, Item))
                        Catch ex As Exception
                        End Try
                    End If
                Next
            End If
        Catch ex As Exception
        End Try

    End Sub

    Private Shared Sub GetSendLog(ByVal sourcePath As String, ByVal desPath As String)
        Try

            If Not Directory.Exists(sourcePath) Then
                Return
            End If

            Dim fileList As New List(Of String)
            For index As Integer = 0 To 6
                fileList.Add(String.Format("{0}.txt", (DateTime.Now.AddDays(-index)).ToString("yyyyMMdd")))
            Next
            For Each Item As String In Directory.GetFiles(sourcePath)
                If fileList.Contains(Path.GetFileName(Item)) Then
                    Try
                        File.Copy(Item, Path.Combine(desPath, Path.GetFileName(Item)))
                    Catch ex As Exception
                    End Try
                End If
            Next

            fileList.Clear()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSendLog")
        End Try

    End Sub

    Private Shared Sub GetInstallDeviceLog(ByVal sourcePath As String, ByVal desPath As String)
        If Not Directory.Exists(sourcePath) Then
            Return
        End If

        Dim fileList As New List(Of String)
        Dim strTempFolder As String = ""

        For index As Integer = 0 To 6
            fileList.Add(DateTime.Now.AddDays(-index).ToString("yyyyMMdd"))
        Next
        For Each Item As String In Directory.GetDirectories(sourcePath)
            If fileList.Contains(Path.GetFileName(Item)) Then
                Try
                    strTempFolder = Path.Combine(desPath, Path.GetFileName(Item))
                    If Not Directory.Exists(strTempFolder) Then
                        Directory.CreateDirectory(strTempFolder)
                    End If
                    For Each subfile As String In Directory.GetFiles(Item)
                        File.Copy(subfile, Path.Combine(strTempFolder, Path.GetFileName(subfile)))
                    Next

                Catch ex As Exception
                End Try
            End If
        Next

        fileList.Clear()
    End Sub


#End Region

    Private Sub btnTuiTutorial_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTuiTutorial.Click
        frmTuiInstallTutorial.ShowForm(Me)
    End Sub

#Region "--- 切换皮肤---"

    'Private mblnSkinInited As Boolean = False
    Private mThrLoadSkin As Thread
    Public Sub ReloadSkin()
        Try
            If Me.mThrLoadSkin IsNot Nothing AndAlso Me.mThrLoadSkin.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadSkin.Abort()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "PluginLogin_Abort")
        End Try

        Try
            Me.mThrLoadSkin = New Thread(AddressOf ReloadSkinThread)
            With mThrLoadSkin
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "PluginLogin_Login")
        End Try
    End Sub

    Private Sub ReloadSkinThread()
        Try
            ''如果是第一次加载就等2秒
            'If Not Me.CheckCurrentSkinZiped() Then
            '    Utility.WaitSeconds(3)
            'End If

            Me.UnZipSkin()
            Skin.Instance.GetSkin(True)

            Dim lstFroms As New List(Of tbBaseGuiForm)
            lstFroms.Add(Me)
            '加载窗体的时候可能会发生队列改变的错误
            Try
                For Each frmOpen As Form In Application.OpenForms
                    If TypeOf frmOpen Is tbBaseGuiForm Then
                        lstFroms.Add(frmOpen)
                    End If
                Next
            Catch ex As Exception
                Common.LogException(ex.ToString, "ReloadSkinThread_GetForms")
            End Try

            For Each Item As tbBaseGuiForm In lstFroms
                Me.DoOverridSkin(Item)
            Next

            '发指令到其它窗体更换皮肤
            My.Application.SendArgsToProcess("Helper", "ReloadSkin")
            My.Application.SendArgsToProcess("Feedback", "ReloadSkin")
            My.Application.SendArgsToProcess("PkgInstaller", "ReloadSkin")
            My.Application.SendArgsToProcess("iTunesUninstaller", "ReloadSkin")
        Catch ex As Exception
            Common.LogException(ex.ToString, "ReloadSkinThread")
        End Try
    End Sub

    Public Sub UnZipSkin()
        Try
            Dim skinFiles() As String = Directory.GetFiles(Skin.Instance.SkinFolder, "*.zip", SearchOption.TopDirectoryOnly)
            If skinFiles Is Nothing OrElse skinFiles.Length <= 0 Then
                Return
            End If

            For Each Item As String In skinFiles
                Try
                    Dim strFileName As String = Path.GetFileNameWithoutExtension(Item)
                    Dim strUnzipPath As String = Path.Combine(Skin.Instance.SkinFolder, strFileName & "_Files")
                    If Not Directory.Exists(strUnzipPath) Then
                        Folder.CheckFolder(strUnzipPath)
                        Utility.unzip(Item, strUnzipPath)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString, "UnZipSkin_1")
                End Try
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "UnZipSkin_2")
        End Try
    End Sub

    Private Delegate Sub DoOverridSkinHandler(ByVal frm As tbBaseGuiForm)
    Private Sub DoOverridSkin(ByVal frm As tbBaseGuiForm)
        If Me.InvokeRequired Then
            Me.Invoke(New DoOverridSkinHandler(AddressOf DoOverridSkin), frm)
        Else
            frm.OverridSkin()
        End If
    End Sub

    Public Sub InitSkin()
        Try
            If Not Directory.Exists(Skin.Instance.SkinFolder) Then
                Return
            End If

            '在皮肤上显示一点红点
            If IniSetting.GetShowSkinNew() Then
                Me.btnSkin.tbShowDot = True
            End If

            Me.ReloadSkin()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitSkin")
        End Try
    End Sub

    '如果用户第一次打开有皮肤的界面就进行解压并重新加载默认的界面元素。
    Private Function CheckCurrentSkinZiped() As Boolean
        Dim blnReturn As Boolean = False
        If Skin.Instance.GetSkinSetting = "Default" Then
            blnReturn = True

        ElseIf Directory.Exists(Skin.Instance.SkinFolder) Then
            Dim strSkinFolders() As String = Directory.GetDirectories(Skin.Instance.SkinFolder, "*.*", SearchOption.TopDirectoryOnly)
            If strSkinFolders IsNot Nothing AndAlso strSkinFolders.Length > 0 Then
                For Each Item As String In strSkinFolders
                    If Item.Contains(Skin.Instance.GetSkinSetting) Then
                        blnReturn = True
                        Exit For
                    End If
                Next
            End If
        End If
        Return blnReturn
    End Function

    'Private Sub SkinMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs)
    '    Try
    '        Dim item As ToolStripMenuItem = sender
    '        If item Is Nothing OrElse item.Tag Is Nothing OrElse Not TypeOf item.Tag Is String Then
    '            Return
    '        End If

    '        For Each itemMenu As ToolStripMenuItem In Me.menuSkin.Items
    '            If item Is itemMenu Then
    '                itemMenu.Checked = True
    '            Else
    '                itemMenu.Checked = False
    '            End If
    '        Next
    '        Skin.Instance.SetSkinSetting(item.Tag)
    '        Me.ReloadSkin()
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString, "SkinMenuItem_Click")
    '    End Try
    'End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.SuspendLayout()
        Try
            Me.btn_close.tbBackgroundImage = My.Resources.btn_close
            Me.btn_normal.tbBackgroundImage = My.Resources.btn_max
            Me.btn_minimize.tbBackgroundImage = My.Resources.btn_min
            Me.btnFeedBack.tbBackgroundImage = My.Resources.btn_feedback_3
            Me.btn_Setting.tbBackgroundImage = My.Resources.btn_setting
            Me.btnSkin.tbIconImage = My.Resources.btn_skin_3
        Catch ex As Exception
            Common.LogException(ex.ToString, "MainForm_OverridSkin")
        End Try

        Try
            For Each Item As tbRadioButton In Me.mListItems
                Item.tbImageCheckedMouseLeave = My.Resources.new_tool_select
                Item.tbImageCheckedMouseDown = My.Resources.new_tool_select
                Item.tbImageCheckedMouseHover = My.Resources.new_tool_select
                Item.tbImageUnCheckedMouseHover = My.Resources.new_tool_hover
                'Item.tbIconReadOnlyLight = My.Resources.new_top_light
                Item.Refresh()
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "MainForm_OverridSkin1")
        End Try

        Try
            Me.mtbBackground = New TextureBrush(My.Resources.new_bottom_bg)
            Me.mtbTopBackground = New TextureBrush(My.Resources.new_top_bg)
            Me.mtbWaterMark = New TextureBrush(My.Resources.new_top_watermark)
            Me.mtbLine = New TextureBrush(My.Resources.new_top_line)
        Catch ex As Exception
            Common.LogException(ex.ToString, "MainForm_OverridSkin2")
        End Try
        Me.DownloadIcon()
        Me.ResumeLayout()
        Me.Invalidate()
    End Sub

    Private Sub btnSkin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSkin.Click, tsmSkin.Click
        IniSetting.SetShowSkinNew()
        Me.btnSkin.tbShowDot = False
        'Me.InitSkin()
        'Me.menuSkin.Show(Me.btnSkin, New Point(0, Me.btnSkin.Height))
#If IS_ITONG Then
        frmSettingSkin.ShowForm(Me)
#End If
    End Sub

#End Region

    Private Sub picFiveYear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.ShowSiteAnniversary("")
    End Sub

    Public Sub ShowSiteAnniversary(ByVal strUrl As String)
#If IS_ITONG Then
        frmSiteAnniversary.ShowForm(Me, strUrl)
#End If
    End Sub

    Private Sub lblVersion_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblVersion.SizeChanged
        Me.btnCheckUpdate.Location = New Point(Me.lblVersion.Right, Me.btnCheckUpdate.Top)
    End Sub


    Private Sub LogCurrentDevice(ByVal strMark As String)
        '测试用的暂时去掉。
        'Try
        '    If Me.mCurrentDevice Is Nothing Then
        '        Common.LogException("LogCurrentDevice--Nothing", strMark)
        '    Else
        '        Common.LogException(String.Format("LogCurrentDevice--DeviceName:{0}  DeviceType{1}", Me.mCurrentDevice.DeviceName, Me.mCurrentDevice.DeviceType), strMark)
        '    End If
        'Catch ex As Exception
        '    Common.LogException(ex.ToString(), strMark)
        'End Try
    End Sub

    Private Sub tsmiGetLogs_Click(sender As Object, e As EventArgs) Handles tsmiGetLogs.Click
        ProcForm.CreateError()
    End Sub

    Private Sub tsmiCoexistPay_Click(sender As Object, e As EventArgs) Handles tsmiCoexistPay.Click

        '判断是否直接一台设备            
        If AndroidDeviceMonitor.Instance().DictUsbDevices.Values IsNot Nothing AndAlso AndroidDeviceMonitor.Instance().DictUsbDevices.Values.Count > 1 Then
            tbMessageBox.Show(Me.Language.GetString("WeChat.Message.CoexistPay"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            '"请将你要安装分身版的手机连接到同步助手（不能同时连接多台）"
            Return
        End If

        If Me.CurrentDevice IsNot Nothing AndAlso Me.CurrentDevice.MobileDevice IsNot Nothing AndAlso Me.CurrentDevice.MobileDevice.DeviceType = DeviceType.iOS Then
#If IS_ITONG Then
            Dim frm As frmPayCoexist = New frmPayCoexist(Me, Me.CurrentDevice.MobileDevice)
            frm.ShowDialog(Me)
#End If
        Else
            tbMessageBox.Show(Me.Language.GetString("WeChat.Message.CoexistPay"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        End If

    End Sub


    Public Shared Sub OpenWeChatSpare(ByVal frmType As Type, ByVal Appli As IApplication, ByVal idevice As iPhoneDevice)
        Try
            Dim strName As String = frmType.FullName & AloneBackupType.WeChatSpare.ToString()
            If idevice IsNot Nothing Then
                strName = idevice.Identifier & frmType.FullName & AloneBackupType.WeChatSpare.ToString()
            End If
            ItemShowEx(frmType, strName, idevice, Appli, Nothing, False, True, AloneBackupType.WeChatSpare)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OpenWeChatSpare")
        End Try
    End Sub

#Region "--- 海外收费版注册事件 ---"

#If IS_ITONG Then

    Private mblnActive As Boolean = False

    Private Sub btnBuy_Click(sender As Object, e As EventArgs) Handles btnBuy.Click
        Me.mblnActive = True
        Me.mChargeHelper.ShowChargeForm(Me)
        Me.mblnActive = False
    End Sub

    Private Delegate Sub SetBuyButtonStatusHandler()
    Private Sub SetBuyButtonStatus()
        If Me.InvokeRequired Then
            Me.Invoke(New SetBuyButtonStatusHandler(AddressOf SetBuyButtonStatus))
        Else
            Try
                If Me.mChargeHelper.CheckIsVIP() Then
                    Me.btnBuy.BackColor = Color.Transparent
                    Me.btnBuy.tbIconImage = My.Resources.btn_buy_3
                    Me.btnBuy.Text = Me.Language.GetString("MainVip.Message.Renewal")   '"续期"
                    Me.btnBuy.Padding = New Padding(0, 3, 1, 0)
                Else
                    Me.btnBuy.BackColor = Color.FromArgb(255, 255, 78, 56)
                    Me.btnBuy.tbIconImage = Nothing
                    Me.btnBuy.Text = Me.Language.GetString("MainVip.Message.UpgradeAtOnce")    '"马上升级"
                    Me.btnBuy.Padding = New Padding(1, 3, 1, 0)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "MainForm_SetBuyButtonStatus")
            End Try
        End If
    End Sub

    Private Sub OnQueryCallback(sender As Object, e As ChargeResultArgs)
        Me.SetBuyButtonStatus()
    End Sub

    Private Sub OnActiveCallback(sender As Object, e As ChargeResultArgs)
        Me.SetBuyButtonStatus()
        If Me.mblnActive = True AndAlso Not e.Code = ActiveCode.HasAuthed Then
            tbMessageBox.Show(Me, e.Msg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, e.MsgBoxIcon)
        End If
    End Sub

#End If

#End Region

#Region "--- 删除界面的备份数据 ---"
    Public Shared Sub DeleteEncryptedBackup(ByVal strPath As String)
        Try
            Dim thd As New Thread(AddressOf DoDeleteEncryptedBackup)
            thd.IsBackground = True
            thd.Start(strPath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteEncryptedBackup")
        End Try
    End Sub

    Private Shared Sub DoDeleteEncryptedBackup(ByVal obj As Object)
        Try
            Dim strOld As String = obj.ToString()
            If Directory.Exists(strOld) Then
                Dim strNew As String = strOld

                Dim intIndex As Integer = 0
                While True
                    If Directory.Exists(strNew) Then
                        intIndex += 1
                        strNew = String.Format("{0}({1})", strOld, intIndex)
                    Else
                        Exit While
                    End If
                End While

                '先重命名
                Microsoft.VisualBasic.FileIO.FileSystem.RenameDirectory(strOld, Path.GetFileName(strNew))
                Common.LogException(String.Format("{0}  {1}", strOld, strNew), "DoDeleteEncryptedBackup_先重命名")

                '加入删除记录
                IniClass.SetIniSectionKey("MobileSyncBackup_Delete", strNew.ToLower(), strNew.ToLower(), Folder.MobileSyncBackupIniFile)

                '执行删除                
                Common.LogException(String.Format("路径：{0}", strNew), "DoDeleteEncryptedBackup_开始删除")
                Directory.Delete(strNew, True)
                Common.LogException(String.Format("路径：{0}", strNew), "DoDeleteEncryptedBackup_完成删除")

                IniClass.DeleteSectionKey("MobileSyncBackup_Delete", strNew.ToLower(), Folder.MobileSyncBackupIniFile)
                Common.LogException(String.Format("路径：{0}", strNew), "DoDeleteEncryptedBackup_删除结束")
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoDeleteEncryptedBackup")
        End Try
    End Sub

#End Region

#Region "--- WebSiteHelper相关事件 ---"

    Private Sub InitWebSiteHelper()
        AddHandler WebSiteHelper.Instance().GetDeviceForWeb, AddressOf OnGetDeviceForWeb
        AddHandler WebSiteHelper.Instance().ShowWeibo, AddressOf OnShowWeibo
        AddHandler WebSiteHelper.Instance().ShowTongbuLogin, AddressOf OnShowTongbuLogin
    End Sub

    Private Sub DisposeWebSiteHelper()
        RemoveHandler WebSiteHelper.Instance().GetDeviceForWeb, AddressOf OnGetDeviceForWeb
        RemoveHandler WebSiteHelper.Instance().ShowWeibo, AddressOf OnShowWeibo
        RemoveHandler WebSiteHelper.Instance().ShowTongbuLogin, AddressOf OnShowTongbuLogin
    End Sub

    Private Sub OnGetDeviceForWeb(ByVal sender As Object, ByVal e As GetDeviceForWebArgs)
        If Me.mCurrentDevice IsNot Nothing AndAlso Me.mCurrentDevice.MobileDevice IsNot Nothing Then
            e.Device = Me.mCurrentDevice.MobileDevice
        End If
    End Sub

    Private Sub OnShowWeibo(ByVal sender As Object, ByVal e As ShowWeiboArgs)
        WeiboHelper.SendMessage(e.Type, Nothing, e.Contact, e.Img, "", Nothing, False)
    End Sub

    Private Sub OnShowTongbuLogin(ByVal sender As Object, ByVal e As EventArgs)
        frmLogin.ShowLogin(Nothing, False)
    End Sub

#End Region

    Private Sub OnShowAppDetail(ByVal sender As Object, ByVal e As ShowAppDetailArgs)
        Try
            If e IsNot Nothing Then
                Dim appDetailForm As New frmAppDetail(e.URL, e.ItemId, e.ShowUpdateInfoAtFirst)
                appDetailForm.Text = Language.GetString("App.Text.AppDetail")            '"详细信息
                appDetailForm.StartPosition = FormStartPosition.CenterParent
                appDetailForm.Show(e.Owner)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "MainForm_OnShowAppDetail")
        End Try
    End Sub


End Class
