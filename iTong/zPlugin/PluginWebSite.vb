﻿Public Class PluginWebSite
    Implements IPluginWebSite

    Public Event ShareApp As EventHandler
    Public Event NavigateInWeb As NavigateInWebEventHandler
    Public Delegate Sub NavigateInWebEventHandler(ByVal url As String)

    Public ReadOnly Property Name() As String Implements Components.IPlugin.Name
        Get
            Return "WebSite"
        End Get
    End Property

    Public Sub ShareSoft2TonbugTui(ByVal package As CoreFoundation.PackageInfo) Implements Components.IPluginWebSite.ShareSoft2TonbugTui
        RaiseEvent ShareApp(package, EventArgs.Empty)
    End Sub

    Public Sub NavigateInTonbutui(ByVal url As String) Implements Components.IPluginWebSite.NavigateInTonbutui
        RaiseEvent NavigateInWeb(url)
    End Sub

End Class
