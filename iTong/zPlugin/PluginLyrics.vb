﻿Public Class PluginLyrics
    Implements IPluginLyrics


    Public Event LrcClear(ByVal sender As Object, ByVal e As System.EventArgs) Implements Components.IPluginLyrics.LrcClear
    Public Event LrcClick(ByVal sender As Object, ByVal e As ClickEventArgs) Implements Components.IPluginLyrics.LrcClick
    Public Event LrcSetting(ByVal sender As Object, ByVal e As EventArgs) Implements Components.IPluginLyrics.LrcSetting


    Public ReadOnly Property Name() As String Implements Components.IPlugin.Name
        Get
            Return "Lyrics Plugin"
        End Get
    End Property

    Public ReadOnly Property ShowLrc() As Boolean Implements Components.IPluginLyrics.ShowLrc
        Get
            Return LoadLrcSetting()
        End Get
    End Property

    Private Function LoadLrcSetting() As Boolean
        Return IniSetting.GetShowLyrics()
    End Function

    Private Sub SetLrcSetting(ByVal showLrc As Boolean)
        IniSetting.SetShowLyrics(showLrc)
    End Sub

    Public Sub UpdateLrcSetting() Implements Components.IPluginLyrics.UpdateLrcSetting
        RaiseEvent LrcSetting(Me, New EventArgs())
    End Sub

    Public Sub OpenLrc(ByVal e As ClickEventArgs) Implements Components.IPluginLyrics.OpenLrc
        Me.SetLrcSetting(True)
        RaiseEvent LrcClick(Me, e)
    End Sub

    Public Sub CloseLrc(ByVal e As ClickEventArgs) Implements Components.IPluginLyrics.CloseLrc
        Me.SetLrcSetting(False)
        RaiseEvent LrcClick(Me, e)
    End Sub

    Public Sub ClearLrc() Implements Components.IPluginLyrics.ClearLrc
        RaiseEvent LrcClear(Me, New EventArgs())
    End Sub

End Class
