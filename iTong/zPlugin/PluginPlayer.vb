﻿Imports System.Xml
Imports System.Text
Imports System.IO
Imports System.Security.Cryptography

Public Class PluginPlayer
    Inherits WMPPlayer
    Implements IPluginPlayer

    Private _currentMp3Info As MusicInfo

    '' Added by Utmost on 2011.07.13
    Private _currentMp3InfoList As List(Of MusicInfo)

    Public Sub New()
        Me._currentMp3InfoList = New List(Of MusicInfo)
    End Sub

    '' Added by Utmost on 2011.07.14
    Protected Overrides Sub Finalize()
        MyBase.Finalize()

        Me._currentMp3InfoList.Clear()
        Me._currentMp3InfoList = Nothing
    End Sub

    Public ReadOnly Property Name() As String Implements IPlugin.Name
        Get
            Return "MediaPlayer"
        End Get
    End Property

    Public Property CurrentMp3Info() As MusicInfo Implements IPluginPlayer.CurrentMp3Info
        Get
            Return _currentMp3Info
        End Get
        Set(ByVal value As MusicInfo)
            _currentMp3Info = value
        End Set
    End Property

    '' Added by Utmost on 2011.07.13
    Public Property CurrentMp3InfoList() As List(Of MusicInfo) Implements IPluginPlayer.CurrentMp3InfoList
        Get
            If Me._currentMp3InfoList Is Nothing Then
                Me._currentMp3InfoList = New List(Of MusicInfo)
            End If

            Return Me._currentMp3InfoList
        End Get
        Set(ByVal value As List(Of MusicInfo))
            If value IsNot Nothing Then
                Me._currentMp3InfoList = value
            End If
        End Set
    End Property

    Public Event GotoAllMusicEvevt(ByVal strPath As String) Implements IPluginPlayer.GotoAllMusicEvevt

    Public Sub GoToAllMusicByPath(ByVal strPath As String) Implements IPluginPlayer.GoToAllMusicByPath
        RaiseEvent GotoAllMusicEvevt(strPath)
    End Sub

    Public Function GetListeningUrlFromGoogle(ByVal musicID As String) As String Implements IPluginPlayer.GetListeningUrlFromGoogle
        Return MP3Download.ShowGoogleCheckCodeForm(musicID)
    End Function

End Class
