﻿'Public Class PluginDownloadManager
'    Inherits MultiThreadDownload
'    Implements IPluginDownloadManager

'    Sub New()
'        MultiThreadDownload.Instance()
'    End Sub

'    Public ReadOnly Property Name() As String Implements Components.IPlugin.Name
'        Get
'            Return "MultiThreadDownload"
'        End Get
'    End Property

'    Public Function GoogleMusicDownload(ByVal strCode As String, ByVal strName As String) As MultiThreadDownloadItemInfo Implements Components.IPluginDownloadManager.GoogleMusicDownload
'        Return MP3Download.DownloadFromGoogle(strCode, strName)
'    End Function

'    Public Sub VideoPluginDownload()
'        Dim lstItem As List(Of MultiThreadDownloadItem) = Me.GetItems(ResourceClass.Plugins, ResourceType.pVideo, TaskState.All)

'        For Each Item As MultiThreadDownloadItem In lstItem
'            If Item.ItemInfo.Url = WebUrl.VedioPluginUrl Then
'                Me.Start(Item)
'                Return
'            End If
'        Next

'        Dim info As New MultiThreadDownloadItemInfo
'        info.Url = WebUrl.VedioPluginUrl        '"http://t.tongbu.com/CommenLoad/ffmpeg.zip"
'        info.Type = ResourceType.pVideo
'        info.Class = ResourceClass.Plugins
'        info.Name = "Video Plugin"
'        info.SavePath = Folder.Plugins
'        Me.NewTask(info)

'    End Sub

'    Public Sub ScreenshotPluginDownload(ByVal strVersion As String) Implements IPluginDownloadManager.ScreenshotPluginDownload
'        Dim lstItem As List(Of MultiThreadDownloadItem) = Me.GetItems(ResourceClass.Plugins, ResourceType.pScreenShot, TaskState.All)
'        Dim strUrl As String = ScreenshotHelper.GetInstance().GetScreenshotPluginDownloadUrl(strVersion)

'        For Each Item As MultiThreadDownloadItem In lstItem
'            If Item.ItemInfo.Url = strUrl Then

'                Me.Start(Item)
'                Return
'            End If
'        Next

'        Dim info As New MultiThreadDownloadItemInfo
'        If String.IsNullOrEmpty(strUrl.Trim) = False Then
'            info.Url = strUrl
'            info.Type = ResourceType.pScreenShot
'            info.Class = ResourceClass.Plugins
'            info.Name = "Screenshot Plugin"
'            info.SavePath = Folder.Plugins
'            Me.NewTask(info)
'        End If

'    End Sub

'End Class
