﻿Public Class frmPopMsg

    Private Const WS_EX_NOACTIVATE As Integer = &H8000000
    Private mDevice As iPhoneDevice
    Private mDownManage As MultiThreadDownload
    Private mUrl As String = String.Empty
    Private mApplication As IApplication
    Private mErrorDocumentText As String = String.Empty

    Protected Overrides ReadOnly Property CreateParams() As CreateParams
        Get
            Dim cp As CreateParams = MyBase.CreateParams
            cp.ExStyle = cp.ExStyle Or WS_EX_NOACTIVATE
            Return cp
        End Get
    End Property

    ''' 用于弹出安卓设备检测微信过程窗体——Added by Utmost20150528
    Public Sub New(ByVal app As IApplication, ByVal strTitle As String)
        InitializeComponent()

        Me.mApplication = app
        Me.Language = app.Language
        Me.TopMost = True
        Me.FilletRadius = 3
        Me.Icon = My.Resources.iTong
        Me.tbShowTitleOnForm = True
        Me.ShowInTaskbar = True
        Me.tbTitleLocation = New Point(Me.tbTitleLocation.X, Me.tbTitleLocation.Y + 5)

        Me.CanbeMove = True
        Me.CanResize = False

        Me.Text = strTitle
        Me.StartPosition = FormStartPosition.CenterScreen

        Me.pnlMain.SendToBack()
        Me.webMsg.SendToBack()
        Me.pbLoading.Visible = True
        Me.pbLoading.BringToFront()

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice, ByVal strText As String, ByVal strUrl As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = app
        Me.Language = app.Language
        Me.Text = strText
        Me.mUrl = strUrl

        Me.TopMost = True
        Me.FilletRadius = 3
        Me.Icon = My.Resources.iTong
        Me.tbShowTitleOnForm = True
        Me.ShowInTaskbar = True
        Me.tbTitleLocation = New Point(Me.tbTitleLocation.X, Me.tbTitleLocation.Y + 5)

        Me.CanbeMove = True
        Me.CanResize = False

        Me.mDevice = device
        Me.mDownManage = MultiThreadDownload.Instance()

        If String.IsNullOrEmpty(Me.Text) Then
            Me.Text = Me.Language.GetString("Main.Text.Name")
        End If

        Me.SetFormLocation(False)

        With Me.webMsg
            .IsWebBrowserContextMenuEnabled = False
            .ScriptErrorsSuppressed = True
            .ScrollBarsEnabled = False
            .WebBrowserShortcutsEnabled = True
            .ShowNavigateErrorPage = False
            .ShowLoadingWait = True
            If Not Folder.LangType = LanguageType.en_US Then
                .LoadingGif = GuiResource.gif_loading_24
            Else
                .LoadingGif = My.Resources.pnl_loading_small
            End If

            .LoadingFont = Common.CreateFont("Arial", 10.0F, FontStyle.Regular)
        End With

        If Not Folder.LangType = LanguageType.en_US Then
            Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        End If

        Me.webMsg.Navigate(Me.mUrl)

        '弹窗图片尺寸：320*187
    End Sub

    Private Sub SetFormLocation(ByVal blnShow As Boolean)
        Me.StartPosition = FormStartPosition.Manual
        If blnShow Then
            Dim rect As Rectangle = Screen.PrimaryScreen.WorkingArea
            Me.Location = New Point(rect.Right - Me.Width, rect.Bottom - Me.Height)
        Else
            Me.Location = New Point(-2000, -2000)
        End If
    End Sub

    Private Sub webMsg_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs) Handles webMsg.Navigating
        Dim strUrl As String = e.Url.ToString()

        If strUrl.Contains("itong://refresh") Then
            e.Cancel = True
            Me.webMsg.Navigate(Me.mUrl)

        ElseIf strUrl.Contains("tongbu://new") Then
            e.Cancel = True
            Dim openUrl As String = Utility.GetParamValueFromQuery("url", strUrl, "&", False, True)
            openUrl = System.Web.HttpUtility.UrlDecode(openUrl)
            Common.OpenExplorer(openUrl)
            Me.Close()

        ElseIf strUrl.Contains("tongbu://jp/?") Then
            e.Cancel = True
            Dim jumpUrl As String = Utility.GetParamValueFromQuery("url", strUrl, "&", False, True)
            jumpUrl = System.Web.HttpUtility.UrlDecode(jumpUrl)
            Common.Log(jumpUrl)
            Me.mApplication.GotoSite("App", jumpUrl)

        ElseIf String.IsNullOrEmpty(e.Url.Query) = False AndAlso e.Url.Query.IndexOf("actiontype=") >= 0 Then
            Me.DownloadApp(e.Url.OriginalString)
            e.Cancel = True

        End If
    End Sub

    Private Sub tbWebBrowser_NavigateError(ByVal sender As Object, ByVal e As WebBrowserNavigateErrorEventArgs) Handles webMsg.NavigateError
        Dim wb As tbWebBrowser = CType(sender, tbWebBrowser)

        Dim strImageFile As String = Folder.CacheFolder & "welcome_gif_error.gif"
        If Not System.IO.File.Exists(strImageFile) Then
            My.Resources.welcome_gif_error.Save(strImageFile)
        End If

        If Not String.IsNullOrEmpty(e.Url) AndAlso _
         (wb.Url Is Nothing OrElse _
          wb.Url.ToString().StartsWith("about:blank", StringComparison.OrdinalIgnoreCase) OrElse _
          e.Url.ToString.Contains(New Uri(wb.Name).Host)) Then

            wb.Url = New Uri(e.Url)

            If String.IsNullOrEmpty(mErrorDocumentText) Then
                mErrorDocumentText = My.Resources.welcome_htm_error.Replace("error_icon.gif", strImageFile)

                mErrorDocumentText = mErrorDocumentText.Replace("{0}", Me.Language.GetString("Welcome.Label.ConnFailed")).Replace("{0}", Me.Language.GetString("Welcome.Label.ConnFailed"))     '"Connection failed."
                '"Please try one of the following:{0}1. Check if your network is {0}working properly.{0}2.{1}Refresh{2} this page."
                Dim strPara0 As String = String.Format("<br/><div class=""{0}"">", Me.Language.CurrentLanguage.LangName)
                Dim strPara1 As String = String.Format("</div><div class=""{0}"">", Me.Language.CurrentLanguage.LangName)
                Dim strPara2 As String = "<a href=""iTong://Refresh"">"
                Dim strPara3 As String = "</a>"

                mErrorDocumentText = mErrorDocumentText.Replace("{1}", String.Format(Me.Language.GetString("Welcome.Message.HowToRefreshPage"), strPara0, strPara1, strPara2, strPara3))
            End If

            wb.DocumentText = mErrorDocumentText
        End If
        Me.pbLoading.Visible = False
        Me.pbLoading.SendToBack()
        Me.Close()
    End Sub

    Private Sub webRecommend_NewWindow3(ByVal sender As tbWebBrowserEx, ByRef ppDisp As System.Object, ByRef Cancel As System.Boolean, ByVal dwFlags As System.UInt32, ByVal bstrUrlContext As System.String, ByVal bstrUrl As System.String) Handles webMsg.NewWindow3
        Cancel = True
        Common.OpenExplorer(bstrUrl)
        Me.Close()
    End Sub

#Region "--- 下载ipa逻辑 ---"

    Private Sub DownloadApp(ByVal strUrl As String)
        Try
            Dim strDownloadUrl As String = Utility.GetParamValueFromQuery("url", strUrl, "&", True)
            Dim strAppName As String = Utility.GetParamValueFromQuery("appname", strUrl, "&", True)
            Dim strVersion As String = Utility.GetParamValueFromQuery("ver", strUrl, "&", True)
            Dim strItemId As String = Utility.GetParamValueFromQuery("appleid", strUrl, "&", True)
            Dim strIconUrl As String = Utility.GetParamValueFromQuery("icon", strUrl, "&", True)
            Dim strLoadId As String = String.Empty
            Dim blnNeedToBackInfo As Boolean = False

            '如果是在同步推页面，获取LoadId
            Dim strContent As String = Utility.GetContentStringFromUrl(strDownloadUrl, System.Text.Encoding.UTF8)
            If strContent.Length = 0 Then

                '如果失败，则打log。输出请求的url。
                Common.LogException("此链接已失效DownloadApp：" & vbCrLf & strDownloadUrl)

                '此链接已失效，请选择其它下载地址。
                tbMessageBox.Show(Me.Language.GetString("Site.Message.LinkDisable"), _
                                Me.Language.GetString("Common.Info"), _
                                MessageBoxButtons.OK, _
                                MessageBoxIcon.Information)

                Return
            End If

            '在1.35版本的时候，web要求每个下载链接都进行解密
            strContent = Common.DecryptDES(strContent, "ac68!3#1", "ac68!3#1")

            strDownloadUrl = Utility.GetParamValueFromQuery("url", strContent, "@@")    '下载地址
            strLoadId = Utility.GetParamValueFromQuery("loadid", strContent, "@@")    'UrlId
            Dim strBackInfo As String = Utility.GetParamValueFromQuery("isbackinfo", strContent, "@@")   '是否回传信息给服务器
            If strBackInfo.ToLower = "0" Then
                blnNeedToBackInfo = True
            End If

            Me.DownloadIPA(strDownloadUrl, strAppName, strVersion, strItemId, strIconUrl, strLoadId, blnNeedToBackInfo)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadApp")
        End Try
    End Sub

    Private Sub DownloadIPA(ByVal url As String, _
                                                  Optional ByVal strName As String = "", _
                                                  Optional ByVal strVersion As String = "", _
                                                  Optional ByVal strItemId As String = "", _
                                                  Optional ByVal strIconUrl As String = "", _
                                                  Optional ByVal strLoadID As String = "", _
                                                  Optional ByVal isNeedToBackInfo As Boolean = False)
        Try
            Dim info As New MultiThreadDownloadItemInfo
            With info
                .IsSingleThread = False

                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .Source = TaskSource.Tongbu
                .LoadId = strLoadID
                .Url = url
                .ItemId = strItemId
                .IconUrl = strIconUrl

                If isNeedToBackInfo Then
                    .UrlId = strLoadID
                End If

                If strName.Length > 0 Then
                    If strVersion.Length > 0 Then
                        strName &= String.Format("_v{0}", strVersion)
                    End If
                    If strName.ToLower().EndsWith(".ipa") = False Then
                        strName &= ".ipa"
                    End If
                End If
                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If .Url.ToLower().Contains("tbtui") Then
                    .IsUpdate = True
                End If
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                    .Identifier = Me.mDevice.Identifier
                End If
            End With

            Me.mDownManage.NewTask(info)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadIPA")
        End Try
    End Sub

#End Region

    Private Sub webMsg_Navigated(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatedEventArgs) Handles webMsg.Navigated
        Me.pbLoading.Visible = False
        Me.pbLoading.SendToBack()
        Me.SetFormLocation(True)
    End Sub

    Private Sub btn_close_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btn_close.Click
        Me.Close()
    End Sub
End Class
