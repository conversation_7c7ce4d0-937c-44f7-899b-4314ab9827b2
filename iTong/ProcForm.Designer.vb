﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ProcForm
    Inherits Form
    'Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Me.mNotifyIcon = New System.Windows.Forms.NotifyIcon(Me.components)
        Me.mMenu = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpen = New System.Windows.Forms.ToolStripMenuItem()
        Me.tss1 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiSetting = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiCheckUpdate = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSelectLang = New System.Windows.Forms.ToolStripMenuItem()
        Me.tss4 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiCreateError = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiCreateComputerInfo = New System.Windows.Forms.ToolStripMenuItem()
        Me.tss2 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiExist = New System.Windows.Forms.ToolStripMenuItem()
        Me.bgwCheckUpdate = New System.ComponentModel.BackgroundWorker()
        Me.bgwSendData = New System.ComponentModel.BackgroundWorker()
        Me.tmrTimer = New System.Windows.Forms.Timer(Me.components)
        Me.bgwAnalyseAppSite = New System.ComponentModel.BackgroundWorker()
        Me.tmrUpgrade = New System.Windows.Forms.Timer(Me.components)
        Me.bgwAnalysisApp = New System.ComponentModel.BackgroundWorker()
        Me.bgwSendOperateData = New System.ComponentModel.BackgroundWorker()
        Me.mMenu.SuspendLayout()
        Me.SuspendLayout()
        '
        'mNotifyIcon
        '
        Me.mNotifyIcon.ContextMenuStrip = Me.mMenu
        Me.mNotifyIcon.Text = "NotifyIcon1"
        '
        'mMenu
        '
        Me.mMenu.AccessibleDescription = "153x198"
        Me.mMenu.DropShadowEnabled = False
        Me.mMenu.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.mMenu.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpen, Me.tss1, Me.tsmiSetting, Me.tsmiCheckUpdate, Me.tsmiSelectLang, Me.tss4, Me.tsmiCreateError, Me.tsmiCreateComputerInfo, Me.tss2, Me.tsmiExist})
        Me.mMenu.Name = "mMenu"
        Me.mMenu.Size = New System.Drawing.Size(150, 176)
        Me.mMenu.tbBackColor = System.Drawing.Color.White
        Me.mMenu.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.mMenu.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.mMenu.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpen
        '
        Me.tsmiOpen.Name = "tsmiOpen"
        Me.tsmiOpen.Size = New System.Drawing.Size(149, 22)
        Me.tsmiOpen.Text = "打开同步助手"
        '
        'tss1
        '
        Me.tss1.Name = "tss1"
        Me.tss1.Size = New System.Drawing.Size(146, 6)
        '
        'tsmiSetting
        '
        Me.tsmiSetting.Name = "tsmiSetting"
        Me.tsmiSetting.Size = New System.Drawing.Size(149, 22)
        Me.tsmiSetting.Text = "参数设置(&O)"
        '
        'tsmiCheckUpdate
        '
        Me.tsmiCheckUpdate.Name = "tsmiCheckUpdate"
        Me.tsmiCheckUpdate.Size = New System.Drawing.Size(149, 22)
        Me.tsmiCheckUpdate.Text = "检查更新(&K)..."
        '
        'tsmiSelectLang
        '
        Me.tsmiSelectLang.Name = "tsmiSelectLang"
        Me.tsmiSelectLang.Size = New System.Drawing.Size(149, 22)
        Me.tsmiSelectLang.Text = "选择语言(&L)"
        '
        'tss4
        '
        Me.tss4.Name = "tss4"
        Me.tss4.Size = New System.Drawing.Size(146, 6)
        '
        'tsmiCreateError
        '
        Me.tsmiCreateError.Name = "tsmiCreateError"
        Me.tsmiCreateError.Size = New System.Drawing.Size(149, 22)
        Me.tsmiCreateError.Text = "错误日志(&E)"
        '
        'tsmiCreateComputerInfo
        '
        Me.tsmiCreateComputerInfo.Name = "tsmiCreateComputerInfo"
        Me.tsmiCreateComputerInfo.Size = New System.Drawing.Size(149, 22)
        Me.tsmiCreateComputerInfo.Text = "电脑信息"
        Me.tsmiCreateComputerInfo.Visible = False
        '
        'tss2
        '
        Me.tss2.Name = "tss2"
        Me.tss2.Size = New System.Drawing.Size(146, 6)
        '
        'tsmiExist
        '
        Me.tsmiExist.Name = "tsmiExist"
        Me.tsmiExist.Size = New System.Drawing.Size(149, 22)
        Me.tsmiExist.Text = "退出(&X)"
        '
        'bgwCheckUpdate
        '
        Me.bgwCheckUpdate.WorkerSupportsCancellation = True
        '
        'bgwSendData
        '
        Me.bgwSendData.WorkerSupportsCancellation = True
        '
        'tmrTimer
        '
        Me.tmrTimer.Interval = 1000
        '
        'bgwAnalyseAppSite
        '
        Me.bgwAnalyseAppSite.WorkerSupportsCancellation = True
        '
        'tmrUpgrade
        '
        Me.tmrUpgrade.Interval = 500
        '
        'bgwAnalysisApp
        '
        Me.bgwAnalysisApp.WorkerSupportsCancellation = True
        '
        'bgwSendOperateData
        '
        Me.bgwSendOperateData.WorkerSupportsCancellation = True
        '
        'ProcForm
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(128, 23)
        Me.ControlBox = False
        Me.Location = New System.Drawing.Point(-2000, -2000)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "ProcForm"
        Me.ShowIcon = False
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.Manual
        Me.mMenu.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents mNotifyIcon As System.Windows.Forms.NotifyIcon
    Friend WithEvents mMenu As tbContextMenuStrip
    Friend WithEvents tsmiOpen As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiCheckUpdate As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tss1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiSetting As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tss4 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiSelectLang As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tss2 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiExist As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents bgwCheckUpdate As System.ComponentModel.BackgroundWorker
    Friend WithEvents bgwSendData As System.ComponentModel.BackgroundWorker
    Private WithEvents tmrTimer As System.Windows.Forms.Timer
    Friend WithEvents bgwAnalyseAppSite As System.ComponentModel.BackgroundWorker
    Friend WithEvents tmrUpgrade As System.Windows.Forms.Timer
    Friend WithEvents bgwAnalysisApp As System.ComponentModel.BackgroundWorker
    Friend WithEvents bgwSendOperateData As System.ComponentModel.BackgroundWorker
    Friend WithEvents tsmiCreateError As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiCreateComputerInfo As System.Windows.Forms.ToolStripMenuItem
End Class
