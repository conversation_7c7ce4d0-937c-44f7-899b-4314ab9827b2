﻿Imports SharePodLib.Parsers.iTunesDB
Imports iTong.AppUnion
Imports System.Threading

Public Class frmMore

    Private mAssemblyName As String = String.Empty
    Private mRepairButton As tbButton
    Private mDictBackground As New Dictionary(Of String, System.ComponentModel.BackgroundWorker)
    Private mlstForm As New List(Of Form)               '记录在more中打开的窗体，当more关闭的时候再一起关闭.
    'Private mbtniTunesOpen As tbButton = Nothing
    Private mThrOpeniTunes As Thread


    Private miTunesUninstaller As String = "iTunesUninstaller" '2015-08-05 by chenbihai 

    Private mdictWeixinProcess As New Dictionary(Of String, String)

    Private _frmWechat As Form 'frmAndroidWeixin = Nothing
    Private m_dictWechats As New Dictionary(Of String, AndroidDevice)(StringComparer.InvariantCultureIgnoreCase)

    Private mbtnKingRoot As tbButton = Nothing
    Private mKingRootHelper As KingRootHelper

#Region "--- 初始化 ---"

    Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal dev As IDevice)
        MyBase.New(app, dev)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FunctionMappingKey = FunctionKey.More
        Me.mKingRootHelper = KingRootHelper.Instance()
        AddHandler Me.mKingRootHelper.InstallKingRootEvent, AddressOf OnInstallKingRoot
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        If Common.VerIs30() Then
            Me.InitControlsEx()
            Return
        End If

        If File.Exists(Folder.AppFolder & "AppUnion.dll") Then
            mAssemblyName = ",AppUnion"
        Else
            mAssemblyName = ",PkgInstaller"
        End If

        Dim group As tbGroupPanel = Nothing

        Dim blnConnected As Boolean = Me.mDevice IsNot Nothing
        Dim btn As tbButton = Nothing

        '媒体====================================================================================================================================
        group = Me.CreateGroup(Me.Language.GetString("Main.Group.Media"))                                   '"Media"

        'group.Controls.Add(Me.CreateItem(Me.Language.GetString("Media.Type.Ringtone"), My.Resources.more_ring, IIf(Common.RunInChina, Nothing, My.Resources.more_ring_light), GetType(frmRingtone), New Object() {MediaType.Ringtone}))                                   '"Ringtone"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.iTunesU"), My.Resources.more_itunesu, My.Resources.more_itunesu_light, GetType(frmMediaOthers), New Object() {MediaType.iTunesU}))                                    '"iTunes U"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.Podcasts"), My.Resources.more_podcast, My.Resources.more_podcast_light, GetType(frmMediaOthers), New Object() {MediaType.Podcast}))                                   '"Podcasts"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Media.Type.MusicVideo"), My.Resources.more_musicvideo, My.Resources.more_musicvideo_light, GetType(frmMediaOthers), New Object() {MediaType.MusicVideo}))                               '"MusicVideo"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.TVShows"), My.Resources.more_tvshow, My.Resources.more_tvshow_light, GetType(frmMediaOthers), New Object() {MediaType.TVShow}))                                     '"TV Shows"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Media.Type.Audiobook"), My.Resources.more_audiobook, My.Resources.more_audiobook_light, GetType(frmMediaOthers), New Object() {MediaType.AudioBook}))                                 '"AudioBook"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Media.Type.VoiceMemo"), My.Resources.more_voicememo, My.Resources.more_voicememo_light, GetType(frmMediaOthers), New Object() {MediaType.VoiceMemo}))                                 '"VoiceMemo"

        '资料====================================================================================================================================
        group = Me.CreateGroup(Me.Language.GetString("Main.Group.Data"))                                    '"资料"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.Document"), My.Resources.more_document, My.Resources.more_document_light, GetType(frmFile), New Object() {FileFormType.Document}))                                     '"Document"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.UDisk"), My.Resources.more_udisk, My.Resources.more_udisk_light, GetType(frmFile), New Object() {FileFormType.UDisk}))                                              '"UDisk"
        'group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.FileSystem"), My.Resources.more_file, My.Resources.more_file_light, GetType(frmFile), New Object() {FileFormType.FileSystem}))                                     '"FileSystem"

        'If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 400 Then
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.IconManager"), My.Resources.more_iconmanager, My.Resources.more_iconmanager_light, GetType(frmIconManage), Nothing))                                     '"Icon"
        'End If

        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.Contact"), My.Resources.more_contacts, My.Resources.more_contacts_light, GetType(frmContact), Nothing))            '"Contact"

        '"Note"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.Note"), My.Resources.more_note, My.Resources.more_note_light, GetType(frmNote), New Object() {Nothing}))                      '"Note"



        '备份还原
        btn = Me.CreateItem(Me.Language.GetString("Backup.Text.Title"), My.Resources.more_restore, Nothing, GetType(frmBackupList))
        If Me.Language.CurrentLanguage.LangName <> "zh-CN" Then
            btn.tbAutoSizeEx = True
        End If
        'If IniSetting.GetBackupShowNew Then
        '    btn.tbShowNew = True
        'End If
        group.Controls.Add(btn)

        '短信
        btn = Me.CreateItem(Me.Language.GetString("Main.Button.MessageExport"), My.Resources.more_sms, My.Resources.more_sms_light, GetType(frmBackupSMS), Nothing)
        btn.tbAutoSizeEx = True
        'If IniSetting.GetMessageShowNew Then
        '    btn.tbShowNew = True
        'End If

        group.Controls.Add(btn)                      '短信

        If Folder.LangType = LanguageType.zh_CN Then
            '微信
            btn = Me.CreateItem(Me.Language.GetString("Weixin.Text.TitleBackup"), My.Resources.more_weixin, My.Resources.more_weixin_light, GetType(frmWeixinMain))
            btn.tbAutoSizeEx = True
            'If IniSetting.GetWeixiShowNew Then
            '    btn.tbShowNew = True
            'End If
            group.Controls.Add(btn)
            '"微信表情"
            btn = Me.CreateItem(Me.Language.GetString("Weixin.Lable.Emoticon"), My.Resources.more_emoticon, My.Resources.more_emoticon_light, GetType(frmWeixinEmoticon))
            btn.tbAutoSizeEx = True
            'btn.tbShowNew = IniSetting.GetWeixinEmoticonShowNew
            group.Controls.Add(btn)

            '微信闪退修复
            'btn = Me.CreateItem("微信闪退修复", My.Resources.more_weixinrepair, My.Resources.more_weixinrepair_light, GetType(frmWeixinRepair))
            'group.Controls.Add(btn)

            '打开WiFiSync（提供给测试组使用）
            Dim strTestDllPath As String = Path.Combine(Folder.AppFolder, "test.dll")
            If File.Exists(strTestDllPath) Then
                group.Controls.Add(Me.CreateItem("关闭WiFiSync", My.Resources.more_clear, My.Resources.more_clear_light, GetType(String), New Object() {"CloseWiFiSync"})) '"关闭WiFiSync"
            End If
        End If

        '工具====================================================================================================================================
        group = Me.CreateGroup(Me.Language.GetString("Main.Group.Tools"))                                   '"Tools"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.VideoConvert"), My.Resources.more_videoconvert, Nothing, GetType(frmVideoConvert)))  '"Video　Convert"
        btn = Me.CreateItem(Me.Language.GetString("Music.Button.RingtoneMaker"), My.Resources.more_ringmaker, Nothing, GetType(RingMakerForm))  '"Ringtone　Maker"
        If Me.Language.CurrentLanguage.LangName <> "zh-CN" Then
            btn.tbAutoSizeEx = True
        End If
        group.Controls.Add(btn)

        '"修复闪退"
        'If Not Folder.LangType = LanguageType.en_US Then
        '    group.Controls.Add(Me.CreateItem(Me.Language.GetString("App.Button.Repair"), My.Resources.more_repair, My.Resources.more_repair_light, Nothing, New Object() {"Repair"}))
        'End If

        If Folder.LangType = LanguageType.zh_CN Then
            '"白苹果修复"
            group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.RepairWhiteApple"), My.Resources.more_repair_ios, My.Resources.more_repair_ios_light, GetType(frmTools), New Object() {ToolsType.RepairSpringboard}))
        End If

        '"清理灰色图标"
        'btn = Me.CreateItem(Me.Language.GetString("Main.Button.ClearErrorIcon"), My.Resources.more_clearicon, My.Resources.more_clearicon_light, GetType(ClearErrorIcon))
        'btn.tbShowNew = IniSetting.GetClearErrorIconShowNew()
        'group.Controls.Add(btn)

        '"运营商图标"
        'If Folder.LangType = LanguageType.zh_CN Then
        '    group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.ChangeOperatorIcon"), My.Resources.more_motify_carrier, My.Resources.more_motify_carrier_light, GetType(frmMotifyCarrier)))
        'End If

        '"垃圾清理"
        btn = Me.CreateItem(Me.Language.GetString("Main.Button.ClearRubbish"), My.Resources.more_clear, My.Resources.more_clear_light, GetType(frmClearRubbish))
        group.Controls.Add(btn)

        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.CloseDevice"), My.Resources.more_shurtdown, My.Resources.more_shurtdown_light, GetType(String), New Object() {"ShurtDown"})) '"关机"
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.Restar"), My.Resources.more_restar, My.Resources.more_restar_light, GetType(String), New Object() {"Restar"})) '"重启"

        '实时日志
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.SystemLog"), My.Resources.more_systemlog, My.Resources.more_systemlog_light, GetType(frmSystemLog)))                                              '"UDisk"

        '崩溃日志
        group.Controls.Add(Me.CreateItem(Me.Language.GetString("Main.Button.CrashReport"), My.Resources.more_crashreport, My.Resources.more_crashreport_light, GetType(frmFile), New Object() {FileFormType.FileSharingApp, Me.GetCrashReportPackageInfo()}))                                              '"UDisk"

        If Common.IsVshostMode() Then
            group.Controls.Add(Me.CreateItem("清空授权", My.Resources.more_clear, My.Resources.more_clear_light, GetType(String), New Object() {"RemoveAuthData"})) '"清空授权"
        End If

        '同步网络平台============================================================================================================================
        If Folder.LangType = LanguageType.zh_CN Then
            group = Me.CreateGroup(Me.Language.GetString("Main.Group.NetPlatform"))       '"同步网络平台"                            '"Tools"
            '"http://www.tongbu.com/?s=zsv2"
            btn = Me.CreateItem(Me.Language.GetString("PkgInstaller.Label.Home"), My.Resources.more_home, Nothing, GetType(String), New Object() {WebUrl.ChinaMoreHome})
            btn.tbAutoSizeEx = True
            group.Controls.Add(btn) '"同步官网"
            '"http://tui.tongbu.com/?s=zsv2"
            btn = Me.CreateItem(Me.Language.GetString("File.Label.tbTui"), My.Resources.more_tui, Nothing, GetType(String), New Object() {WebUrl.ChinaMoreTui})
            btn.tbAutoSizeEx = True
            group.Controls.Add(btn)   '"同步推"
            '"http://app.tongbu.com/?s=zsv2"
            btn = Me.CreateItem(Me.Language.GetString("Main.Button.AppSite"), My.Resources.more_web, Nothing, GetType(String), New Object() {WebUrl.ChinaMoreApp})
            btn.tbAutoSizeEx = True
            group.Controls.Add(btn)    '"资讯站"
            btn = Me.CreateItem(Me.Language.GetString("Welcome.Button.Jobs"), My.Resources.more_joinme, Nothing, GetType(String), New Object() {"http://hr.tongbu.com/?s=zsmore"})
            btn.tbAutoSizeEx = True
            group.Controls.Add(btn) '"招聘信息"
            btn = Me.CreateItem(Me.Language.GetString("Main.Button.AboutUs"), My.Resources.more_about, Nothing, GetType(frmAbout))
            btn.tbAutoSizeEx = True
            group.Controls.Add(btn)        '"关于我们"
            btn = Me.CreateItem(Me.Language.GetString("Main.Button.Feedback"), My.Resources.more_feedback, Nothing, GetType(String), New Object() {"Feedback"})
            btn.tbAutoSizeEx = True
            group.Controls.Add(btn)        '"意见反馈"
            '服务器控制是否显示充值中心按钮
            If ServerIniSetting.GetShowPayButton() Then
                btn = Me.CreateItem(Me.Language.GetString("Main.Button.Pay"), My.Resources.more_pay, Nothing, GetType(String), New Object() {"http://pay.tongbu.com/?s=zsmore"})
                btn.tbAutoSizeEx = True
                group.Controls.Add(btn)    '"充值中心"
            End If
        End If

        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
            Me.SetItemReadOnly(False)
        End If
    End Sub

    Private Sub InitControlsEx()

        If File.Exists(Folder.AppFolder & "AppUnion.dll") Then
            mAssemblyName = ",AppUnion"
        Else
            mAssemblyName = ",PkgInstaller"
        End If

        Dim group As tbGroupPanel = Nothing

        '信息管理====================================================================================================================================
        group = Me.CreateGroup(Me.Language.GetString("Main.Button.MessageManager"), "MessageManager")

        Dim fntText As Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
        Dim btnItem As tbButton = Nothing

        '"联系人"
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.Contact"), fntText, _
                                  My.Resources.more_contacts, My.Resources.more_contacts_light, _
                                  New Type() {GetType(frmContact), GetType(frmAndroidContact)})
        group.Controls.Add(btnItem)

        '"短信管理"
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.SMSManager"), fntText, _
                                  My.Resources.more_sms, My.Resources.more_sms_light, _
                                  New Type() {GetType(frmBackupSMS), GetType(frmAndroidSMS)})
        group.Controls.Add(btnItem)

        '"短信聊天"
        btnItem = Me.CreateItemEx(Me.Language.GetString("SMS.Label.SMSChat"), fntText, _
                                  My.Resources.more_smschat, My.Resources.more_smschat_light, _
                                  New Type() {GetType(tbSplashBox), GetType(frmAndroidSMSChatGroup)}, _
                                  Nothing, "SmsChat")
        group.Controls.Add(btnItem)


        '备忘录
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.Note"), fntText, _
                          My.Resources.more_note, My.Resources.more_note_light, _
                          New Type() {GetType(frmNote), GetType(tbSplashBox)}, _
                          Nothing, "Note")
        group.Controls.Add(btnItem)


        'If Common.IsVshostMode() Then
        '书签
        btnItem = Me.CreateItemEx(Me.Language.GetString("BookMark.Text.BookMark"), fntText, _
                  My.Resources.more_bookmark, My.Resources.more_bookmark_light, _
                  New Type() {GetType(frmBookmark), GetType(tbSplashBox)}, _
                  Nothing, "Bookmark")
        group.Controls.Add(btnItem)
        'End If

        '"微信表情"
        btnItem = Me.CreateItemEx(Me.Language.GetString("Weixin.Lable.Emoticon"), fntText, _
                                My.Resources.more_emoticon, My.Resources.more_emoticon_light, _
                                New Type() {GetType(frmWeixinEmoticon), GetType(tbSplashBox)}, _
                                Nothing, "WeixinEmoticon") 'GetType(frmWeixinEmoticonApp)
        btnItem.tbAutoSizeEx = True
        'btnItem.tbShowNew = IniSetting.GetWeixinEmoticonShowNew
        group.Controls.Add(btnItem)


        'QQ管理
        'If Common.IsQQManagement() Then
        '    btnItem = Me.CreateItemEx("QQ", fntText, _
        '                            My.Resources.more_qq, My.Resources.more_qq, _
        '                            New Type() {GetType(frmQQ)}, _
        '                            Nothing, "QQManagement")
        '    group.Controls.Add(btnItem)
        'End If



        '安卓微信
        'Dim androidWeixintype As Type = IIf(Common.IsVshostMode(), GetType(frmAndroidWeixin), GetType(tbSplashBox))
        'Dim androidWeixinType As Type = IIf(ServerIniSetting.GetShowAndroidWeixin, GetType(frmAndroidWeixin), GetType(tbSplashBox))
        Dim androidWeixinType As Type = GetType(frmAndroidWeixin)

        Dim wt As String = Me.Language.GetString("Weixin.Text.TitleBackup")
        If Me.mAndroid Is Nothing Then
            wt = Me.Language.GetString("Weixin.Text.TitleBackup")
        End If

        If IniSetting.GetOldAndroidWeChat() Then
            btnItem = Me.CreateItemEx(wt, fntText, _
                                     My.Resources.more_weixin, Nothing, _
                                     New Type() {GetType(frmWeixinMain), androidWeixinType}, _
                                     Nothing, "WeixinText")
        Else
            btnItem = Me.CreateItemEx(wt, fntText, _
                                     My.Resources.more_weixin, Nothing, _
                                     New Type() {GetType(frmWeixinMain), GetType(tbSplashBox)}, _
                                     Nothing, "WeixinText")
        End If


        If Me.mAndroid Is Nothing AndAlso IniSetting.GetShowFindWeChatDataNew Then
            btnItem.tbShowNew = True
        End If
        group.Controls.Add(btnItem)

        If Me.mAndroid Is Nothing Then

            '微信共存版
            If ServerIniSetting.IsShowMoreWeChatCoexist() Then
                btnItem = Me.CreateItemEx(Me.Language.GetString("Weixin.Lable.WeChatCoexist"), fntText, _
                                   My.Resources.more_weixincoexist, Nothing, _
                                   New Type() {GetType(frmWeixinMain), GetType(tbSplashBox)}, _
                                   Nothing, "WeChatCoexist")
                If Me.mAndroid Is Nothing AndAlso IniSetting.GetShowWeChatCoexist Then
                    btnItem.tbShowNew = True
                End If
                group.Controls.Add(btnItem)
            End If

            '微信VIP
            'Dim isShowVIPSpare As Boolean = False
            'If Folder.AppType = RunType.Tongbu_Abroad Then
            '    '海外
            '    isShowVIPSpare = True
            'Else
            '    isShowVIPSpare = PremiumFeatureHelper.GetShowWeChatVIPSpare()
            'End If

            'If isShowVIPSpare Then
            '    btnItem = Me.CreateItemEx("VIP微信", fntText, _
            '                            My.Resources.more_weixincoexist, My.Resources.more_weixincoexist_light, _
            '                            New Type() {GetType(frmWeixinMain), androidWeixinType}, _
            '                            Nothing, "WeChatVIPCoexist")
            '    group.Controls.Add(btnItem)
            'End If

        End If

        Dim strWWA As String = Me.Language.GetString("Weixin.Text.TitleBackup")
        If IniSetting.GetOldAndroidWeChat() Then
            strWWA = Me.Language.GetString("Weixin.Text.WechatAsst")
        End If


        btnItem = Me.CreateItemEx(strWWA, fntText, _
                                     My.Resources.more_android_iWechatAssistant, My.Resources.more_android_iWechatAssistant_light, _
                                     New Type() {GetType(tbSplashBox), GetType(String)}, _
                                     New Object() {"WechatiWechatAssistant"}, "WechatiWechatAssistant")
        group.Controls.Add(btnItem)

        '''' Added by Utmost20170518
        '''' 微信多媒体（图片、视频、语音、文件）
        If IniSetting.GetOldAndroidWeChat() Then
            btnItem = Me.CreateItemEx(Me.Language.GetString("WeixinMedia.Text.Title"), fntText, _
                                     My.Resources.more_androidwechatmedia, My.Resources.more_androidwechatmedia_light, _
                                     New Type() {GetType(tbSplashBox), GetType(frmAndroidWechatMedia)}, _
                                     Nothing, "WechatMedia")
            group.Controls.Add(btnItem)
        End If

        'btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.WechatRecoveryForAndroid"), fntText, _
        '                           My.Resources.more_wechatrecoveryforandroid, My.Resources.more_wechatrecoveryforandroid_light, _
        '                           New Type() {GetType(tbSplashBox), GetType(String)}, _
        '                           Nothing, "iWechatRecoveryForAndroid") '"微信恢复大师"
        'btnItem.tbShowNew = IniSetting.GetShowWechatRecoveryForAndroid()
        'group.Controls.Add(btnItem)

        '资料管理====================================================================================================================================
        group = Me.CreateGroup(Me.Language.GetString("Main.Button.DataManager"), "DataManager")

        'U盘
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.UDisk"), fntText, _
                                  My.Resources.more_udisk, My.Resources.more_udisk_light, _
                                  New Type() {GetType(frmFile), GetType(frmAndroidFile)}, New Object() {FileFormType.UDisk})
        group.Controls.Add(btnItem)

        '文档
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.Document"), fntText, _
                                My.Resources.more_document, My.Resources.more_document_light, _
                                New Type() {GetType(frmFile), GetType(tbSplashBox)}, New Object() {FileFormType.Document}, _
                                "Document")
        group.Controls.Add(btnItem)

        ''电子书
        'btnItem = Me.CreateItemEx(Me.Language.GetString("Media.Type.EBook"), fntText, _
        '                          My.Resources.more_ebook, My.Resources.more_ebook_light, _
        '                          New Type() {GetType(frmEBook), GetType(tbSplashBox)})
        'group.Controls.Add(btnItem)

        '语音备忘录
        btnItem = Me.CreateItemEx(Me.Language.GetString("Media.Type.VoiceMemo"), fntText, _
                                  My.Resources.more_voicememo, My.Resources.more_voicememo_light, _
                                  New Type() {GetType(frmMediaOthers), GetType(tbSplashBox)}, New Object() {MediaType.VoiceMemo}, _
                                  "VoiceMemo")
        group.Controls.Add(btnItem)

        '其他媒体
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.OtherMedia"), fntText, _
                                  My.Resources.more_applemedia, My.Resources.more_applemedia_light, _
                                  New Type() {GetType(frmMediaOthers), GetType(tbSplashBox)}, New Object() {MediaType.Unknown}, _
                                  "OtherMedia")
        group.Controls.Add(btnItem)

        '文件
        'group.Controls.Add(Me.CreateItemEx(Me.Language.GetString("Main.Button.FileSystem"), My.Resources.more_file, My.Resources.more_file_light,  New Type() {GetType(frmFile), GetType(String)}, New Object() {FileFormType.FileSystem}))                                   

        '图标管理
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.IconManager"), fntText, _
                                  My.Resources.more_iconmanager, My.Resources.more_iconmanager_light, _
                                  New Type() {GetType(frmIconManage), GetType(tbSplashBox)}, Nothing, _
                                  "IconManager")
        group.Controls.Add(btnItem)                                     '"Icon"

        ''文件管理
        'btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.FileSystem"), fntText, _
        '                          My.Resources.more_file, My.Resources.more_file_light, _
        '                          New Type() {GetType(frmFile), GetType(frmAndroidFile)}, New Object() {FileFormType.FileSystem}, _
        '                          "FileSystem")
        'group.Controls.Add(btnItem)

        '电子书
        btnItem = Me.CreateItemEx(Me.Language.GetString("Media.Type.EBook"), fntText, _
                                  My.Resources.more_ebook, My.Resources.more_ebook_light, _
                                  New Type() {GetType(frmEBook), GetType(tbSplashBox)})
        group.Controls.Add(btnItem)


        '备份还原
        btnItem = Me.CreateItemEx(Me.Language.GetString("Backup.Text.Title"), fntText, _
                                  My.Resources.more_restore, Nothing, New Type() {GetType(frmBackupList), GetType(tbSplashBox)}, _
                                  Nothing, "BackupTitle")
        btnItem.tbAutoSizeEx = True
        group.Controls.Add(btnItem)


        '系统工具====================================================================================================================================
        group = Me.CreateGroup(Me.Language.GetString("Main.Button.SystemTools"), "SystemTools")

        If Folder.AppType <> RunType.Tongbu_Abroad Then
            '"白苹果修复"
            group.Controls.Add(Me.CreateItemEx(Me.Language.GetString("Main.Button.RepairWhiteApple"), fntText, My.Resources.more_repair_ios, My.Resources.more_repair_ios_light, New Type() {GetType(frmTools), GetType(tbSplashBox)}, New Object() {ToolsType.RepairSpringboard}))
        End If

        '"垃圾清理"
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.ClearRubbish"), fntText, _
                                  My.Resources.more_clear, My.Resources.more_clear_light, _
                                  New Type() {GetType(frmClearRubbish), GetType(tbSplashBox)}, _
                                  Nothing, "ClearRubbish")
        group.Controls.Add(btnItem)

        '"运营商图标"
        'If Folder.LangType = LanguageType.zh_CN Then
        '    btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.ChangeOperatorIcon"), fntText, _
        '                              My.Resources.more_motify_carrier, My.Resources.more_motify_carrier_light, _
        '                              New Type() {GetType(frmMotifyCarrier), GetType(tbSplashBox)}, _
        '                              Nothing, "ChangeOperatorIcon")
        '    group.Controls.Add(btnItem)
        'End If

        '实时日志
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.SystemLog"), fntText, _
                                  My.Resources.more_systemlog, My.Resources.more_systemlog_light, _
                                  New Type() {GetType(frmSystemLog), GetType(tbSplashBox)}, _
                                  Nothing, "SystemLog")
        btnItem.tbAutoSizeEx = True
        group.Controls.Add(btnItem)                                              '"UDisk"

        '崩溃日志
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.CrashReport"), fntText, _
                                  My.Resources.more_crashreport, My.Resources.more_crashreport_light, _
                                  New Type() {GetType(frmFile), GetType(tbSplashBox)}, _
                                  New Object() {FileFormType.FileSharingApp, Me.GetCrashReportPackageInfo()}, _
                                  "CrashReport")
        group.Controls.Add(btnItem)                                              '"UDisk"

        '关机 
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.CloseDevice"), fntText, _
                                  My.Resources.more_shurtdown, My.Resources.more_shurtdown_light, _
                                  New Type() {GetType(String), GetType(tbSplashBox)}, _
                                  New Object() {"ShurtDown"}, "CloseDevice")
        group.Controls.Add(btnItem) '"关机"

        '重启
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.Restar"), fntText, _
                                  My.Resources.more_restar, My.Resources.more_restar_light, _
                                  New Type() {GetType(String), GetType(tbSplashBox)}, _
                                  New Object() {"Restar"}, "Restart")
        group.Controls.Add(btnItem) '"重启"

        ''iTunes自启动
        'btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.OpeniTunes"), fntText, _
        '                          My.Resources.more_itunes_on, My.Resources.more_itunes_light, _
        '                          New Type() {GetType(String), GetType(tbSplashBox)}, _
        '                          New Object() {"OpeniTunes"}, "iTunesStart")
        'btnItem.tbToolTip = Me.Language.GetString("Main.Button.OpeniTunesDescription")      '"接入设备iTunes自动启动。"
        'btnItem.tbAutoSizeEx = True
        'group.Controls.Add(btnItem) '"iTunes自启动"
        'Me.mbtniTunesOpen = btnItem

        If Common.IsVshostMode() = True Then
            btnItem = Me.CreateItemEx("清空授权", fntText, _
                                      My.Resources.more_clear, My.Resources.more_clear_light, _
                                      New Type() {GetType(String), GetType(tbSplashBox)}, _
                                      New Object() {"RemoveAuthData"}, "ClearAuth")
            group.Controls.Add(btnItem) '"清空授权"
        End If


        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.DeleteInvalidIcon"), fntText, _
                                  My.Resources.more_clearapp, My.Resources.more_clearapp_light, _
                                  New Type() {GetType(frmDeleteAppIcon), GetType(tbSplashBox)}, _
                                  Nothing, "DeleteAppIcon") '删除废图标

        btnItem.tbShowNew = IniSetting.GetShowDeleteAppIcon()
        group.Controls.Add(btnItem)


        '常用工具====================================================================================================================================
        group = Me.CreateGroup(Me.Language.GetString("Main.Button.CommonTools"), "CommonTools") 'Me.Language.GetString("Main.Group.Tools")

        '铃声制作
        btnItem = Me.CreateItemEx(Me.Language.GetString("Music.Button.RingtoneMaker"), fntText, _
                                  My.Resources.more_ringmaker, Nothing, _
                                  New Type() {GetType(RingMakerForm), GetType(RingMakerFormAndroid)})  '"Ringtone　Maker"
        btnItem.tbAutoSizeEx = True
        group.Controls.Add(btnItem)

        ''' 铃声制作
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.VideoConvert"), fntText, _
                          My.Resources.more_videoconvert, Nothing, _
                          New Type() {GetType(frmVideoConvert), GetType(tbSplashBox)}, _
                          Nothing, "VideoConvert")
        btnItem.tbAutoSizeEx = True
        group.Controls.Add(btnItem)  '"Video　Convert"

        ''' 图片转换
        btnItem = Me.CreateItemEx(Me.Language.GetString("Media.Button.PictureConvert"), fntText, _
                          My.Resources.more_photoconvert, Nothing, _
                          New Type() {GetType(frmPhotoConvert), GetType(tbSplashBox)}, _
                          Nothing, "PhotoConvert")
        btnItem.tbAutoSizeEx = True
        group.Controls.Add(btnItem)  '"Video　Convert"

        'iTunes卸载 2015-08-05 by chenbihai
        btnItem = Me.CreateItemEx(Me.Language.GetString("Main.Button.iTunesUninstaller"), fntText, _
                                  My.Resources.more_itunesuninstaller, Nothing, _
                                  New Type() {GetType(String), GetType(tbSplashBox)}, _
                                     Nothing, miTunesUninstaller)
        btnItem.tbAutoSizeEx = True
        group.Controls.Add(btnItem)

        'KingRoot
        btnItem = Me.CreateItemEx("KingRoot", fntText, _
                                  My.Resources.more_KingRoot, Nothing, _
                                 New Type() {GetType(tbSplashBox), GetType(frmKingRoot)}, _
                                     Nothing, "KingRoot")
        btnItem.tbAutoSizeEx = True
        btnItem.tbToolTip = Me.Language.GetString("More.Message.KingRootTip")      '支持安卓主流机型一键获取Root权限
        group.Controls.Add(btnItem)
        Me.mbtnKingRoot = btnItem

        ''实时日志
        'btnItem = Me.CreateItemEx(Me.Language.GetString("Welcome.Label.Valuation"), fntText, _
        '                          My.Resources.more_valuation, Nothing, _
        '                          New Type() {GetType(frmDevicePrice), GetType(tbSplashBox)}, _
        '                          Nothing, "DevicePrice")
        'btnItem.tbAutoSizeEx = True
        'group.Controls.Add(btnItem)

        ''同步网络平台============================================================================================================================
        'If Folder.LangType = LanguageType.zh_CN Then
        '    group = Me.CreateGroup(Me.Language.GetString("Main.Group.NetPlatform"))       '"同步网络平台"                            '"Tools"
        '    '"http://www.tongbu.com/?s=zsv2"
        '    btn = Me.CreateItem(Me.Language.GetString("PkgInstaller.Label.Home"), My.Resources.more_home, Nothing, GetType(String), New Object() {WebUrl.ChinaMoreHome})
        '    btn.tbAutoSizeEx = True
        '    group.Controls.Add(btn) '"同步官网"
        '    '"http://tui.tongbu.com/?s=zsv2"
        '    btn = Me.CreateItem(Me.Language.GetString("File.Label.tbTui"), My.Resources.more_tui, Nothing, GetType(String), New Object() {WebUrl.ChinaMoreTui})
        '    btn.tbAutoSizeEx = True
        '    group.Controls.Add(btn)   '"同步推"
        '    '"http://app.tongbu.com/?s=zsv2"
        '    btn = Me.CreateItem(Me.Language.GetString("Main.Button.AppSite"), My.Resources.more_web, Nothing, GetType(String), New Object() {WebUrl.ChinaMoreApp})
        '    btn.tbAutoSizeEx = True
        '    group.Controls.Add(btn)    '"资讯站"
        '    btn = Me.CreateItem(Me.Language.GetString("Welcome.Button.Jobs"), My.Resources.more_joinme, Nothing, GetType(String), New Object() {"http://hr.tongbu.com/?s=zsmore"})
        '    btn.tbAutoSizeEx = True
        '    group.Controls.Add(btn) '"招聘信息"
        '    btn = Me.CreateItem(Me.Language.GetString("Main.Button.AboutUs"), My.Resources.more_about, Nothing, GetType(frmAbout))
        '    btn.tbAutoSizeEx = True
        '    group.Controls.Add(btn)        '"关于我们"
        '    btn = Me.CreateItem(Me.Language.GetString("Main.Button.Feedback"), My.Resources.more_feedback, Nothing, GetType(String), New Object() {"Feedback"})
        '    btn.tbAutoSizeEx = True
        '    group.Controls.Add(btn)        '"意见反馈"
        '    '服务器控制是否显示充值中心按钮
        '    If ServerIniSetting.GetShowPayButton() Then
        '        btn = Me.CreateItem(Me.Language.GetString("Main.Button.Pay"), My.Resources.more_pay, Nothing, GetType(String), New Object() {"http://pay.tongbu.com/?s=zsmore"})
        '        btn.tbAutoSizeEx = True
        '        group.Controls.Add(btn)    '"充值中心"
        '    End If
        'End If


        If Me.mAndroid Is Nothing AndAlso Me.mDevice Is Nothing Then
            Me.ChangeButtonStateInMore()
            Me.SetItemReadOnly(True)
        End If

        '' Added by Utmost20140630
        '' 在此窗体中不需要三个按钮，可以释放其资源
        If Me.btn_close IsNot Nothing Then
            Me.btn_close.Dispose()
        End If

        If Me.btn_minimize IsNot Nothing Then
            Me.btn_minimize.Dispose()
        End If

        If Me.btn_normal IsNot Nothing Then
            Me.btn_normal.Dispose()
        End If


        Me.btn_close = Nothing
        Me.btn_minimize = Nothing
        Me.btn_normal = Nothing
        '''''''''''''''''''''''''''''''''''''''''''''''''
    End Sub


    Public Sub ChangeButtonStateInMore(Optional ByVal devUsb As UsbDevice = Nothing)
        If devUsb IsNot Nothing AndAlso devUsb.MobileDevice IsNot Nothing Then
            Dim mobileDev As IDevice = devUsb.MobileDevice
            If TypeOf mobileDev Is AndroidDevice Then
                Me.mAndroid = CType(mobileDev, AndroidDevice)
                Me.mDevice = Nothing
            ElseIf TypeOf mobileDev Is iPhoneDevice Then
                Me.mAndroid = Nothing
                Me.mDevice = CType(mobileDev, iPhoneDevice)
            Else
                Me.mAndroid = Nothing
                Me.mDevice = Nothing
            End If

        End If


        Dim strSplashBoxName As String = GetType(tbSplashBox).FullName
        Dim devType As DeviceType = DeviceType.iOS
        If Me.mAndroid IsNot Nothing Then
            devType = DeviceType.Android
        End If

        For Each group As tbGroupPanel In Me.flpMain.Controls
            Dim nCount As Integer = 0

            For Each btn As tbButton In group.Controls
                Dim arrFormName() As String = btn.Name.Split("&"c)
                Dim strTypeName As String = String.Empty

                If devType = DeviceType.iOS Then
                    strTypeName = arrFormName(0)

                ElseIf devType = DeviceType.Android Then
                    strTypeName = arrFormName(1)

                End If

                btn.VisibleEx = (strSplashBoxName <> strTypeName)
                'Debug.Print(btn.tbText & vbTab & btn.VisibleEx.ToString())

                If btn.VisibleEx Then
                    nCount += 1
                End If
            Next

            If nCount = 0 Then
                group.Height = 0
            Else
                group.Height = 150
            End If
        Next

    End Sub

    Public Overrides Sub OnUsbConnect(ByVal usbDev As UsbDevice)
        MyBase.OnUsbConnect(usbDev)

        If usbDev IsNot Nothing Then
            If usbDev.DeviceType = DeviceType.Android Then
                If usbDev.MobileDevice IsNot Nothing AndAlso usbDev.MobileDevice.IsConnected Then
                    'AndAlso CType(usbDev.MobileDevice, AndroidDevice).Summary IsNot Nothing
                    OnConnect(usbDev.MobileDevice)
                Else
                    OnDisconnect(usbDev.MobileDevice)
                End If
            Else
                If usbDev.MobileDevice IsNot Nothing AndAlso TypeOf usbDev.MobileDevice Is iPhoneDevice Then
                    OnConnect(usbDev.MobileDevice)
                End If
            End If
        End If

    End Sub

    Public Overrides Sub OnUsbDisconnect(ByVal usbDev As UsbDevice)

        ''' 断开安卓微信备份还原
        Me.KillAdbWeixin()

        MyBase.OnUsbDisconnect(usbDev)

    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

        Me.ChangeButtonStateInMore()
        Me.SetItemReadOnly(False)

        'If device.DeviceType = DeviceType.iOS Then
        '    Me.OpeniTunes(False, True)
        'End If
    End Sub

    'Private Sub SetiTunesOpenStatus()
    '    If Me.mbtniTunesOpen IsNot Nothing Then
    '        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
    '            Dim blnPrefs As Boolean = Me.mDevice.GetiTunesPrefs
    '            Me.SetImg(blnPrefs)
    '        End If
    '    End If
    'End Sub

    'Private Delegate Sub SetImgHandler(ByVal blnPrefs As Boolean)
    'Private Sub SetImg(ByVal blnPrefs As Boolean)
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New SetImgHandler(AddressOf SetImg), blnPrefs)
    '    Else
    '        If blnPrefs Then
    '            Me.mbtniTunesOpen.tbIconImage = My.Resources.more_itunes_on
    '        Else
    '            Me.mbtniTunesOpen.tbIconImage = My.Resources.more_itunes_off
    '        End If
    '    End If

    'End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)

        ''' 断开安卓微信备份还原

        Me.KillAdbWeixin()

        MyBase.OnDisconnect(device)

        Me.ChangeButtonStateInMore()
        Me.SetItemReadOnly(True)

        For Each bwg As System.ComponentModel.BackgroundWorker In Me.mDictBackground.Values
            If bwg.IsBusy Then
                bwg.CancelAsync()
            End If
            bwg.Dispose()
        Next
        Try


            If TypeOf device Is iPhoneDevice Then
                SMSDBHelper.RemoveInstance(device)
            End If
        Catch ex As Exception
        End Try

        Me.mDictBackground.Clear()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        For Each Item As Form In Me.mlstForm
            If Item IsNot Nothing Then
                Try
                    Item.Close()
                Catch ex As Exception
                End Try
            End If
        Next

    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Shadows ReadOnly Property Device() As iPhoneDevice
        Get
            Dim dev As iPhoneDevice = Nothing

            If Me.mDevice IsNot Nothing AndAlso TypeOf Me.mDevice Is iPhoneDevice Then
                dev = Me.mDevice
            End If

            Return dev
        End Get
    End Property

    Private Sub ToolItem_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As tbButton = sender
        If item.tbReadOnly Then
            Return
        End If

        If item.Tag IsNot Nothing AndAlso TypeOf item.Tag(0) Is String Then
            Me.CommandHandle(item, item.Tag(0).ToString())
            Me.SetClick(Me.mDevice, "String" & item.Tag(0).ToString())
            Return
        End If

        Dim frmType As Type = System.Type.GetType(item.Name, True, True)
        If frmType.BaseType.FullName = GetType(tbBackgroundWorker).FullName Then
            ItemRun(frmType, item)
        Else
            If Not frmMore.CheckSupport(frmType.Name, Me.mApplication, Me.mDevice) Then
                Return
            End If
            Dim frm As Form = MainForm.ItemShow(frmType, Me.mDevice, Me.mApplication, item.Tag)
            If frm IsNot Nothing Then
                Me.mlstForm.Add(frm)
            End If
        End If

        Dim strName As String = item.Name
        If item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is Array AndAlso item.Tag(0) IsNot Nothing Then
            strName = strName & item.Tag(0).ToString()
        End If
        Me.SetClick(Me.mDevice, strName)

        '设置不显示new标记
        If item.Name = GetType(frmClearRubbish).ToString() Then
            item.tbShowNew = False
            IniSetting.SetClearRubbishShowNew()
        ElseIf item.Name = GetType(frmBackupList).ToString() Then
            item.tbShowNew = False
            IniSetting.SetBackupShowNew()
        ElseIf item.Name = GetType(frmWeixinRepair).ToString() Then
            item.tbShowNew = False
            IniSetting.SetWeixiRepairShowNew()
        ElseIf item.Name = GetType(frmBackupSMS).ToString() Then
            item.tbShowNew = False
            IniSetting.SetMessageShowNew()
        ElseIf item.Name = GetType(ClearErrorIcon).ToString() Then
            item.tbShowNew = False
            IniSetting.SetClearErrorIconShowNew()
            'ElseIf item.Name.Contains("frmWeixinEmoticon") Then
            '    item.tbShowNew = False
            '    IniSetting.SetWeixinEmoticonShowNew()
        ElseIf item.Name = GetType(frmWeixinMain).ToString() Then
            item.tbShowNew = False
            IniSetting.SetWeixiShowNew()
        ElseIf item.Name = GetType(frmDeleteAppIcon).ToString() Then
            item.tbShowNew = False
            IniSetting.SetShowDeleteAppIcon()
        End If
    End Sub

    Private Sub CommandHandle(ByVal item As Control, ByVal strCmd As String)
        Select Case strCmd
            'Case "Repair"
            '    Me.StartTools(ToolsType.RepairAuthorize)

            Case "Restar"
                '"您确定要重启您的设备？"
                If Me.mDevice IsNot Nothing Then
                    If tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.RestarDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                        'Me.mDevice.Restart()
                        Me.OperatorDeviceOrder("Restar")
                    End If
                Else
                    Me.ShowMsg(item)
                End If


            Case "ShurtDown"
                '"您确定要关闭您的设备？"
                If Me.mDevice IsNot Nothing Then
                    If tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.ShurtDown"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                        'Me.mDevice.Shutdown()
                        Me.OperatorDeviceOrder("ShurtDown")
                    End If
                Else
                    Me.ShowMsg(item)
                End If

            Case "RemoveAuthData"
                If Me.mDevice Is Nothing Then
                    Me.ShowMsg(item)
                    Return
                End If

                '"您确定要清空设备上的授权数据吗？"
                If tbMessageBox.Show(Me, "您确定要清空设备上的授权数据吗？", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                    If Me.Device.Jailbreaked Then
                        Dim strDir As String = "/var/mobile/Library/FairPlay/iTunes_Control/iTunes"
                        Dim arrFile() As String = Me.Device.GetFiles(strDir, True)
                        For Each strFile As String In arrFile
                            Me.Device.DeleteFile(strDir & "/" & strFile)
                        Next
                    End If

                    Dim listDel As New List(Of String)
                    listDel.Add("/iTunes_Control/iTunes/IC-Info.sidf")
                    listDel.Add("/iTunes_Control/iTunes/IC-Info.sidv")
                    listDel.Add("/iTunes_Control/iTunes/AH-Info.plist")
                    listDel.Add("/iTunes_Control/iTunes/Tongbu.plist")

                    For Each strFile As String In listDel
                        Me.Device.DeleteFileByAFC(strFile)
                    Next

                    Me.Device.Restart()
                End If

            Case "Feedback"
                Utility.ShowFeedback()

            Case "CloseWiFiSync"
                If Me.mDevice Is Nothing Then
                    Me.ShowMsg(item)
                    Return
                End If

                If tbMessageBox.Show(Me, "您确定要关闭WiFiSync吗？", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                    Me.mDevice.CloseWiFiSync()
                    'Me.mDevice.OpenWiFiSync()
                End If
            Case "OpeniTunes"
                'Me.OpeniTunes(True, True)

            Case "WeChatCoexist"

                If (tbMessageBox.Show(Me, ServerIniSetting.GetWeChatCoexistHint, Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK) Then
                    Common.JoinQQGroup(QQGroupNumber.Q587033524)
                End If
                IniSetting.SetShowWeChatCoexist()

            Case "WechatiWechatAssistant"
                Common.OpenExplorer("https://at.umeng.com/SnyO1n?cid=6768")

            Case Else
                Try
                    Common.OpenExplorer(strCmd)
                Catch ex As Exception
                End Try

        End Select
        Return
    End Sub

    Private Sub OperatorDeviceOrder(ByVal strCommand As String)
        Dim thr As New Thread(New ParameterizedThreadStart(AddressOf OperatorDeviceOrderThread))
        thr.IsBackground = True
        thr.Start(strCommand)
    End Sub

    Private Sub OperatorDeviceOrderThread(ByVal strCommand As Object)
        If strCommand Is Nothing OrElse Not TypeOf strCommand Is String Then
            Return
        End If
        Select Case strCommand
            Case "Restar"
                Me.mDevice.Restart()

            Case "ShurtDown"
                Me.mDevice.Shutdown()

        End Select
    End Sub



    'Private Sub OpeniTunes(ByVal blnSetValue As Boolean, ByVal blnLoadStatus As Boolean)
    '    Try
    '        If Me.mThrOpeniTunes IsNot Nothing AndAlso Me.mThrOpeniTunes.ThreadState <> ThreadState.Stopped Then
    '            Me.mThrOpeniTunes.Abort()
    '        End If

    '        Dim objPara() As Object = New Object() {blnSetValue, blnLoadStatus}

    '        Me.mThrOpeniTunes = New Thread(New ParameterizedThreadStart(AddressOf OpeniTunesThread))
    '        Me.mThrOpeniTunes.IsBackground = True
    '        Me.mThrOpeniTunes.Start(objPara)
    '    Catch ex As Exception
    '    End Try
    'End Sub

    'Private Sub OpeniTunesThread(ByVal obj As Object)
    '    Try
    '        Dim blnSetValue As Boolean = obj(0)
    '        Dim blnLoadStatus As Boolean = obj(1)

    '        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then

    '            If blnSetValue Then
    '                Dim blnPrefs As Boolean = Me.mDevice.GetiTunesPrefs
    '                Dim blnResult As Boolean = Me.mDevice.SetiTunesPrefs(Not blnPrefs)
    '                Dim strMsg As String = Me.Language.GetString("Main.Message.OpeniTunesSucceed")          '"iTunes 自启动打开成功！"

    '                If blnPrefs Then
    '                    strMsg = Me.Language.GetString("Main.Message.CloseiTunesSucceed")                   '"iTunes 自启动关闭成功！"
    '                    If Not blnResult Then
    '                        strMsg = Me.Language.GetString("Main.Message.CloseiTunesFailure")               '"iTunes 自启动关闭失败！"
    '                    End If
    '                Else
    '                    If Not blnResult Then
    '                        strMsg = Me.Language.GetString("Main.Message.OpeniTunesFailure")                '"iTunes 自启动打开失败！"
    '                    End If
    '                End If
    '                'tbMessageBox.Show(Me.mApplication, strMsg, "消息", MessageBoxButtons.OK, MessageBoxIcon.Information)
    '                Me.ShowMsg(strMsg)
    '            End If

    '            If blnSetValue OrElse blnLoadStatus Then
    '                Me.SetiTunesOpenStatus()
    '            End If
    '        End If
    '    Catch ex As Exception
    '    End Try
    'End Sub

    'Private Delegate Sub ShowMsgHandler(ByVal strMsg As String)
    'Private Sub ShowMsg(ByVal strMsg As String)
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New ShowMsgHandler(AddressOf ShowMsg), strMsg)
    '    Else
    '        tbSplashBox.ShowMessage(strMsg, Me, 3)
    '    End If

    'End Sub

    Private Sub ShowMsg(ByVal item As Control)
        If Me.mUsbDev IsNot Nothing AndAlso Me.mUsbDev.MobileDevice IsNot Nothing Then
            Dim strMsg As String = String.Empty
            If Me.mUsbDev.DeviceType = DeviceType.iOS Then
                strMsg = Me.Language.GetString("Main.Message.UnsupportiOS")             '"很抱歉，该功能不支持iOS设备！"
            Else
                strMsg = Me.Language.GetString("Main.Message.UnsupportAndroid")         '"很抱歉，该功能不支持Android设备！"
            End If

            Dim pos As Point = item.PointToScreen(New Point(0, -30))
            tbSplashBox.ShowMessage(strMsg, pos, 2)
        End If
    End Sub

    Private Sub SetClick(ByVal device As IDevice, ByVal strName As String)
        'iOS
        If strName.Contains("frmContact") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Contact)
        ElseIf strName.Contains("frmBackupSMS") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.SMS)
        ElseIf strName.Contains("frmNote") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Note)
        ElseIf strName.Contains("frmFileUDisk") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Udisk)
        ElseIf strName.Contains("frmFileDocument") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Document)
        ElseIf strName.Contains("frmMediaOthersUnknown") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.OtherMedia)
        ElseIf strName.Contains("frmIconManage") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Icon)
        ElseIf strName.Contains("frmBackupList") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Backup)
        ElseIf strName.Contains("frmClearRubbish") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Rubbish)
        ElseIf strName.Contains("frmMotifyCarrier") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Carrier)
        ElseIf strName.Contains("frmSystemLog") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.SystemLog)
        ElseIf strName.Contains("frmFileFileSharingApp") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.CrashReport)
        ElseIf strName.Contains("StringShurtDown") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.ClosePhone)
        ElseIf strName.Contains("StringRestar") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Restar)
        ElseIf strName.Contains("StringOpeniTunes") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.iTuneAutoStar)
        ElseIf strName.Contains("RingMakerForm") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.RingMacker)
        ElseIf strName.Contains("frmVideoConvert") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.VideoMaker)
        ElseIf strName.Contains("frmWeixinEmoticon") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.WeixinEmoticon)
        ElseIf strName.Contains("frmWeixinMain") AndAlso Not strName.Contains("frmWeixinEmoticon") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.Weixin)
        ElseIf strName.Contains("frmDevicePrice") Then
            ActionCollectHelper.DevicePrice(device)
        End If

        'Android
        If strName.Contains("frmAndroidContact") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.AndroidContact)
        ElseIf strName.Contains("frmAndroidSMSChatGroup") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.AndroidSMSChart)
        ElseIf strName.Contains("frmAndroidSMS") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.AndroidSMS)
        ElseIf strName.Contains("frmAndroidFileUDisk") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.AndroidUdisk)
        ElseIf strName.Contains("RingMakerFormAndroid") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.AndroidRingMaker)
        ElseIf strName.Contains("frmAndroidFileAndroidWechatMedia") Then
            ActionCollectHelper.ClickMoreAccition(device, ModelKey.AndroidMore)
        End If
    End Sub

    Public Shared Function CheckSupport(ByVal strTypeName As String, ByVal app As IApplication, ByVal dev As iPhoneDevice) As Boolean
        Dim blnReturn As Boolean = True
        'If strTypeName.Contains("frmBackupSMS") AndAlso Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 800 AndAlso Not Me.mDevice.Jailbreaked AndAlso Not Me.mDevice.InstallCydia Then
        '    tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.UnSupportiOS8"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
        '    blnReturn = False
        'Else

        If strTypeName.Contains("frmWeixinEmoticon") Then
            '8.3无法读取微信表情
            If dev IsNot Nothing AndAlso dev.VersionNumber >= 830 Then
                frmWeixinEmoticonApp.ShowForm(app, dev)
                blnReturn = False
            End If
        End If

        If strTypeName.Contains("frmMotifyCarrier") AndAlso dev IsNot Nothing AndAlso dev.VersionNumber >= 700 Then
            tbMessageBox.Show(String.Format(LanguageInterface.Instance.GetString("Main.Message.UnSupportiOS7"), "7"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            blnReturn = False
        End If

        If strTypeName = GetType(frmNote).ToString() AndAlso dev IsNot Nothing AndAlso dev.VersionNumber >= 1100 Then
            tbMessageBox.Show(String.Format(LanguageInterface.Instance.GetString("Main.Message.UnSupportiOS7"), "11"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            blnReturn = False
        End If

        Return blnReturn
    End Function

    Private Sub OnCloseWechat(ByVal sender As Object, ByVal IsClosed As Boolean)
        If Me.mAndroid IsNot Nothing Then
            Dim deviceId As String = Me.mAndroid.DeviceID
            If m_dictWechats.ContainsKey(deviceId) Then
                m_dictWechats.Remove(deviceId)
            End If
        End If

        If _frmWechat IsNot Nothing Then
            'RemoveHandler _frmWechat.InstallWechatEventHandler, AddressOf OnInstallWechat
            'RemoveHandler _frmWechat.WechatBackupEventHandler, AddressOf OnWechatBackup
            'RemoveHandler _frmWechat.CloseAndroidWechatEventHandler, AddressOf OnCloseWechat
        End If

    End Sub

    Private Sub OnWechatBackup(ByVal sender As Object, ByVal hasWeixin As Integer)
        If hasWeixin = 1 Then

            If _frmWechat IsNot Nothing Then
                'RemoveHandler _frmWechat.InstallWechatEventHandler, AddressOf OnInstallWechat
                'RemoveHandler _frmWechat.WechatBackupEventHandler, AddressOf OnWechatBackup
                'RemoveHandler _frmWechat.CloseAndroidWechatEventHandler, AddressOf OnCloseWechat

                _frmWechat.Close()
                _frmWechat = Nothing
            End If

            ThreadPool.QueueUserWorkItem(AddressOf StartQqWechatBackup)
        End If
    End Sub
    ''' 安装安卓微信事件
    Private Sub OnInstallWechat(ByVal sender As Object, ByVal args As InstallArgs)
        If args.InstallState = InstallState.Succeed Then
            _IsWeixin = 1

            '''默认使用QQ的微信备份
            Dim bIsQq As Boolean = ServerIniSetting.GetAndroidWeixinCore()

            bIsQq = False
            If _frmWechat IsNot Nothing Then
                'RemoveHandler _frmWechat.InstallWechatEventHandler, AddressOf OnInstallWechat
                'RemoveHandler _frmWechat.WechatBackupEventHandler, AddressOf OnWechatBackup
                'RemoveHandler _frmWechat.CloseAndroidWechatEventHandler, AddressOf OnCloseWechat

                _frmWechat.Close()
                _frmWechat = Nothing
            End If

            If bIsQq Then
                ThreadPool.QueueUserWorkItem(AddressOf StartQqWechatBackup)
            Else
                Dim aryProcesses() As Process = Process.GetProcesses()
                Dim IsAdbProc As Boolean = False

                For Each proc As Process In aryProcesses
                    If proc.ProcessName.Equals("iAdbWeixin", StringComparison.OrdinalIgnoreCase) Then
                        IsAdbProc = True
                        Exit For
                    End If
                Next

                If Not IsAdbProc Then
                    ThreadPool.QueueUserWorkItem(AddressOf StartAdbWeixin)
                End If
            End If
        ElseIf args.InstallState = InstallState.UnSucceed Then
            '_frmWechat.UpdateButtonState(False, False)
            '_frmWechat.SetWeixinConnectText("微信安装失败！")
        End If
    End Sub

    Private Function HasiAdbWechat() As Boolean
        Dim IsAdbProc As Boolean = False
        Dim aryProcesses() As Process = Process.GetProcesses()

        For Each proc As Process In aryProcesses
            If proc.ProcessName.Equals("iAdbWeixin", StringComparison.OrdinalIgnoreCase) Then
                IsAdbProc = True
                Dim hwndWechat As IntPtr = NativeMethods.FindWindow("AdbWeixin", "Weixin")

                If hwndWechat <> IntPtr.Zero Then
                    NativeMethods.ShowWindow(hwndWechat, SW_CMD.SW_RESTORE)
                End If

                Exit For
            End If
        Next

        Return IsAdbProc
    End Function
    Private Sub ToolItem_ClickEx(ByVal sender As Object, ByVal e As EventArgs)
        Dim item As tbButton = sender
        If item.tbReadOnly Then
            Return
        End If

        '设置不显示new标记
        If item.Name.Contains(GetType(frmClearRubbish).ToString()) Then
            item.tbShowNew = False
            IniSetting.SetClearRubbishShowNew()
        ElseIf item.Name.Contains(GetType(frmBackupList).ToString()) Then
            item.tbShowNew = False
            IniSetting.SetBackupShowNew()
        ElseIf item.Name.Contains(GetType(frmWeixinRepair).ToString()) Then
            item.tbShowNew = False
            IniSetting.SetWeixiRepairShowNew()
        ElseIf item.Name.Contains(GetType(frmBackupSMS).ToString()) Then
            item.tbShowNew = False
            IniSetting.SetMessageShowNew()
        ElseIf item.Name.Contains(GetType(ClearErrorIcon).ToString()) Then
            item.tbShowNew = False
            IniSetting.SetClearErrorIconShowNew()
        ElseIf item.Name.Contains(GetType(frmWeixinMain).ToString()) Then
            item.tbShowNew = False
            IniSetting.SetWeixiShowNew()
        ElseIf item.Name.Contains(GetType(frmDeleteAppIcon).ToString()) Then
            item.tbShowNew = False
            IniSetting.SetShowDeleteAppIcon()
        Else
            item.tbShowNew = False
        End If

        '2015-08-05 by chenbihai  卸载iTunes
        If item.AccessibleDescription = miTunesUninstaller Then
            Dim striTunesUninstaller As String = Path.Combine(Folder.AppFolder, "iTunesUninstaller.exe")
            If Not File.Exists(striTunesUninstaller) Then

            End If

            Dim strArgs As String = String.Format("{0}|{1}", Folder.BackupFolder, Me.Handle.ToInt64())
            ProcessHelper.Start(striTunesUninstaller, strArgs)
            Return
        End If

        If item.AccessibleDescription = "KingRoot" Then
            ActionCollectHelper.ClickDeviceHomePageChild(Me.mAndroid, ModelKey.AndroidKingRoot)
            If KingRootHelper.Instance.CheckIsNeedDownloadKingRoot() = False Then
                Return
            End If

            If IniSetting.GetIsShowKingRootForm() = False Then
                KingRootHelper.Instance().DownloadZip()
                Return
            End If
        End If

        Dim frmType As Type = Nothing
        Dim strTypeName As String = String.Empty
        Dim strCtlName As String = String.Empty
        Dim arrFormName() As String = item.Name.Split("&"c)

        Dim devType As DeviceType = DeviceType.iOS
        If Me.mAndroid IsNot Nothing Then
            devType = DeviceType.Android
        End If

        Dim dev As IDevice = Nothing
        If devType = DeviceType.iOS Then
            strTypeName = arrFormName(0)
            strCtlName = strTypeName

            If Me.mDevice IsNot Nothing Then
                strCtlName = Me.mDevice.DeviceID & strTypeName
            End If

            dev = Me.mDevice

            If Not frmMore.CheckSupport(strTypeName, Me.mApplication, Me.mDevice) Then
                Return
            End If

        ElseIf devType = DeviceType.Android Then
            strTypeName = arrFormName(1)
            strCtlName = strTypeName
            If Me.mAndroid IsNot Nothing Then
                strCtlName = Me.mAndroid.DeviceID & strTypeName
            End If
            dev = Me.mAndroid
        End If

        If item.Tag IsNot Nothing Then
            strCtlName &= item.Tag(0).ToString()
        End If

        Me.SetClick(dev, strCtlName)

        If strTypeName = GetType(tbSplashBox).FullName Then
            Me.ShowMsg(item)

            Return
        End If

        If item.Tag IsNot Nothing AndAlso TypeOf item.Tag(0) Is String Then
            '处理命令窗口
            Me.CommandHandle(item, item.Tag(0).ToString())
            Return
        End If

        frmType = System.Type.GetType(strTypeName, True, True)
        If frmType.BaseType.FullName = GetType(tbBackgroundWorker).FullName Then
            ItemRun(frmType, item)
        Else

            If Me.Tag IsNot Nothing AndAlso Me.Tag <> DeviceType.Unknown Then
                Dim mobileDevice As IDevice = Nothing

                If Me.Tag = DeviceType.Android Then
                    mobileDevice = Me.mAndroid
                ElseIf Me.Tag = DeviceType.iOS Then
                    mobileDevice = Me.mDevice
                End If

                Dim abType As AloneBackupType = AloneBackupType.None
                If item.AccessibleDescription = "WeChatCoexist" Then
                    abType = AloneBackupType.WeChatSpare
                ElseIf item.AccessibleDescription = "WeixinText" Then
                    abType = AloneBackupType.WeChat
                ElseIf item.AccessibleDescription = "WeChatVIPCoexist" Then
                    abType = AloneBackupType.WeChatVIPSpare

                    '判断是否是VIP 而且是否过期
                    Dim isEffective As Boolean = False
                    If Folder.AppType = RunType.Tongbu_Abroad Then
                        '海外
                        isEffective = EffectiveAbroad()
                    Else
                        isEffective = Me.Effective()
                    End If
                    If Not isEffective Then
                        Return
                    End If

                ElseIf item.AccessibleDescription = "iWechatRecoveryForAndroid" Then
                    IniSetting.SetShowWechatRecoveryForAndroid()
                    item.tbShowNew = False
                    Common.OpenExplorer("http://rollback.wesafesoft.com/?c_s=L8MSjciE3oE%3d&s=zstj")
                    Return
                End If

                Dim frm As Form = MainForm.ItemShowEx(frmType, strCtlName, mobileDevice, Me.mApplication, item.Tag, True, False, abType)
                If frm IsNot Nothing Then
                    If TypeOf (frm) Is frmWeixinEmoticon Then
                        Dim frmWeixin As frmWeixinEmoticon = CType(frm, frmWeixinEmoticon)
                        RemoveHandler frmWeixin.OnLoadFinished, AddressOf frmWeixin_OnLoadFinished
                        AddHandler frmWeixin.OnLoadFinished, AddressOf frmWeixin_OnLoadFinished

                    ElseIf TypeOf (frm) Is RingMakerForm Then
                        '2015-07-01 by chenbihai 音乐编辑窗体， 那个可以暴露一个事件出来给上层，上层show出窗体来。
                        Dim frmRingMakerForm As RingMakerForm = CType(frm, RingMakerForm)
                        RemoveHandler frmRingMakerForm.OnShowMessageHandler, AddressOf frmRingMakerForm_OnShowMessageHandler
                        AddHandler frmRingMakerForm.OnShowMessageHandler, AddressOf frmRingMakerForm_OnShowMessageHandler

                    ElseIf TypeOf (frm) Is RingMakerFormAndroid Then
                        '2015-07-07 by chenbihai 音乐编辑窗体， 那个可以暴露一个事件出来给上层，上层show出窗体来。
                        Dim frmRingMakerFormAndroid As RingMakerFormAndroid = CType(frm, RingMakerFormAndroid)
                        RemoveHandler frmRingMakerFormAndroid.OnShowMessageHandler, AddressOf frmRingMakerForm_OnShowMessageHandler
                        AddHandler frmRingMakerFormAndroid.OnShowMessageHandler, AddressOf frmRingMakerForm_OnShowMessageHandler

                        RemoveHandler frmRingMakerFormAndroid.HasRingPathEvent, AddressOf OnHasRingPathEvent
                        AddHandler frmRingMakerFormAndroid.HasRingPathEvent, AddressOf OnHasRingPathEvent

                        RemoveHandler frmRingMakerFormAndroid.OnImportToDevice, AddressOf OnMakeAndroidRingtoneImported
                        AddHandler frmRingMakerFormAndroid.OnImportToDevice, AddressOf OnMakeAndroidRingtoneImported

                    End If

                    Dim isShowDialog As Boolean = False
                    '微信窗体 备份还原窗体 ShowDialog 
                    If frmType.Name = "frmWeixinMain" OrElse frmType.Name = "frmBackupList" OrElse frmType.Name = "frmWeixinEmoticon" OrElse frmType.Name = "frmBackupSMS" OrElse frmType.Name = "frmQQ" OrElse frmType.Name = "frmDeleteAppIcon" Then
                        isShowDialog = True
                    End If
                    '因为挂载
                    If Not frm.Visible Then
                        If isShowDialog Then
                            frm.ShowDialog()
                        Else
                            frm.Show()
                        End If
                    End If
                    frm.Activate()

                    Me.mlstForm.Add(frm)
                End If
            End If
        End If

    End Sub

    Private Sub ToolItem_MouseEnter(ByVal sender As Object, ByVal e As EventArgs)
        If CType(sender, tbButton).tbReadOnly AndAlso Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected = False Then
            frmTipInfo.ShowTip(Me.Language.GetString("Main.Message.ConnectDevice"), sender, HorizontalAlignment.Center) '"请接入iOS设备"
        End If
    End Sub

    Private Sub ToolItem_MouseLeave(ByVal sender As Object, ByVal e As EventArgs)
        If CType(sender, tbButton).tbReadOnly Then
            frmTipInfo.HideTip(sender)
        End If
    End Sub

    Private Sub flpMain_OnLayout(ByVal sender As Object, ByVal e As LayoutEventArgs) Handles flpMain.Layout
        If Me.flpMain.VerticalScroll.Visible Then
            For Each ctl As Control In Me.flpMain.Controls
                ctl.Size = New Size(Me.flpMain.Width - SystemInformation.VerticalScrollBarWidth, 150)
            Next
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"
    ''' Added by Utmost20150602
    Private _IsWeixin As Integer = -1 ''' 初始值

    'Private Sub GetWeixinPath()
    '    Dim strWeixinPath As String = mAndroid.GetApkPathEx("com.tencent.mm")

    '    If Not String.IsNullOrEmpty(strWeixinPath) Then
    '        _IsWeixin = 1   '' 设备上安装了微信
    '    Else
    '        _IsWeixin = 0   '' 设备上未安装微信
    '    End If
    'End Sub

    'Private Sub GetWeixinThread()
    '    Try
    '        Dim thd As Thread = New Thread(AddressOf GetWeixinPath)
    '        thd.IsBackground = True
    '        thd.Start()

    '        Dim dt As Date = Now
    '        Do
    '            Utility.WaitSeconds(0.2)
    '        Loop While thd.ThreadState <> ThreadState.Stopped AndAlso New TimeSpan(Now.Ticks - dt.Ticks).TotalSeconds < 3

    '    Catch ex As Exception

    '    End Try
    'End Sub

    ''' 显示安卓设备检测微信安装过程
    'Private _frmPop As frmPopMsg = Nothing
    'Private Delegate Sub ShowPopMsgDelegate()
    'Private Sub ShowPopMsg()

    '    Try
    '        If Me.InvokeRequired Then
    '            Me.Invoke(New ShowPopMsgDelegate(AddressOf ShowPopMsg))
    '        Else
    '            Dim strTitle As String = String.Format("正在检测 {0} 设备是否已安装微信...", mAndroid.DeviceName)

    '            _frmPop = New frmPopMsg(Me.mApplication, strTitle)
    '            _frmPop.Show()
    '        End If
    '    Catch ex As Exception

    '    End Try

    'End Sub

    Private Sub StartAdbWeixin(ByVal state As Object)
        If Me.mAndroid IsNot Nothing Then
            Dim strWeixin As String = Path.Combine(Folder.AppFolder, "zAndroidWeixin\iAdbWeixin.exe")
            Dim strDeviceId As String = Me.mAndroid.DeviceID

            If Not mdictWeixinProcess.ContainsKey(strDeviceId) Then
                mdictWeixinProcess.Add(strDeviceId, strWeixin)
            End If

            Dim strArgs As String = String.Format("{0}|{1}|{2}|{3}|{4}|{5}", _
                                                  Folder.AppFolder, Folder.BackupFolder, Folder.ExceptionFolder, _
                                                  strDeviceId, My.Application.Info.Version.ToString(4), Me.Handle.ToInt64())
            ProcessHelper.Start(strWeixin, strArgs)

        End If

    End Sub

    Private Sub StartQqWechatBackup(ByVal state As Object)

        ''C:\ProgramData\Tencent\WechatBackup\UserIco

        Dim backupPath As String = SystemFolder.GetSpecialFolderPath(CSIDL_Folder.CSIDL_COMMON_APPDATA)
        backupPath = backupPath.TrimEnd(New Char() {"\"c}) + "\Tencent\WechatBackup\UserIco"

        Try

            If Not Directory.Exists(backupPath) Then

                Directory.CreateDirectory(backupPath)

                Dim userIcoPath As String = Path.Combine(Folder.AppFolder, "zWechatBackup\UserIco")
                For Each fName As String In Directory.GetFiles(userIcoPath)
                    Try
                        Dim srcName As String = Path.Combine(userIcoPath, fName)
                        Dim dstName As String = Path.Combine(backupPath, fName)

                        File.Copy(srcName, dstName)
                    Catch ex As Exception

                    End Try
                Next
            End If


            If Me.mAndroid IsNot Nothing Then
                Dim strWechat As String = Path.Combine(Folder.AppFolder, "zWechatBackup\WechatBackup.exe")
                Dim strDeviceId As String = Me.mAndroid.DeviceID

                If Not mdictWeixinProcess.ContainsKey(strDeviceId) Then
                    mdictWeixinProcess.Add(strDeviceId, strWechat)
                End If

                ProcessHelper.Start(strWechat)
            End If

        Catch ex As Exception

        End Try
    End Sub

    Private Sub KillAdbWeixin()
        If Me.mAndroid IsNot Nothing Then
            Dim strDeviceId As String = Me.mAndroid.DeviceID

            If mdictWeixinProcess.ContainsKey(strDeviceId) Then
                Dim strWeixin As String = Path.Combine(Folder.AppFolder, "iAdbWeixin.exe")
                ProcessHelper.KillProcess(strWeixin)
            End If

        End If

    End Sub

    ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

    Private Function GetCrashReportPackageInfo() As FileSharingPackageInfo
        Dim info As New FileSharingPackageInfo()
        info.Name = "CrashReporter"
        info.Identifier = "com.apple.crashreportcopymobile"

        Return info
    End Function

    Private Function CreateItem(ByVal strText As String, ByVal imgIcon As Image, ByVal imgIconReadonly As Image, ByVal frmType As Type, Optional ByVal args() As Object = Nothing) As tbButton
        Dim btn As New tbButton

        With btn
            .BackColor = Color.Transparent
            .Margin = New Padding(2, 2, 2, 2)
            .Padding = New Padding(2, 5, 2, 5)
            .BackColor = Color.Transparent
            .Size = New Size(85, 85)
            .tbAutoSize = False
            .tbIconHoldPlace = True
            .tbIconPlaceText = 10
            .tbIconMore = False
            .tbShadow = False
            '.tbShadowColor = Color.FromArgb(22, 74, 160)
            '.tbShadowOffset = New Point(0, -1)
            .tbShowToolTipOnButton = False
            .tbTextAlign = ContentAlignment.BottomCenter
            .tbTextColor = Color.Black
            .tbIconImageAlign = ContentAlignment.TopCenter
            .tbImageMouseDown = Nothing
            .tbImageMouseHover = Nothing
            .tbImageMouseLeave = Nothing
            .tbBackgroundImage = My.Resources.more_btn_3_bg
            .tbBackgroundImageState = ImageState.ThreeState
            '.tbAutoSizeEx = True
            .MinimumSize = New Size(85, 85)
            .MaximumSize = New Size(150, 85)

            .tbIconImage = imgIcon
            .tbIconReadOnly = imgIconReadonly
            If imgIconReadonly IsNot Nothing Then
                .tbReadOnly = True
                .tbReadOnlyText = True
            End If

            .tbSplit = "6,6,6,6"

            If frmType IsNot Nothing Then
                If frmType.FullName.Contains("AppUnion") Then
                    .Name = frmType.FullName & Me.mAssemblyName
                Else
                    .Name = frmType.FullName
                End If
            End If

            .tbText = strText
            .Font = Common.CreateFont("Arial", 9, FontStyle.Regular)

            If args IsNot Nothing Then
                .Tag = args
            End If
        End With

        AddHandler btn.Click, AddressOf ToolItem_Click
        AddHandler btn.MouseEnter, AddressOf ToolItem_MouseEnter
        AddHandler btn.MouseLeave, AddressOf ToolItem_MouseLeave
        Return btn
    End Function

    '' 添加fntText参数避免在函数中多次调用Common.CreateFont("Arial", 9, FontStyle.Regular)函数创建字体
    '' Added by Utmost20140708
    Private Function CreateItemEx(ByVal strText As String, ByVal fntText As Font, _
                                  ByVal imgIcon As Image, ByVal imgIconReadonly As Image, _
                                  ByVal arrType() As Type, Optional ByVal args() As Object = Nothing, _
                                  Optional ByVal tagDesc As String = "") As tbButton
        Dim btn As New tbButton

        With btn
            .AccessibleDescription = tagDesc
            .BackColor = Color.Transparent
            .Margin = New Padding(2, 2, 2, 2)
            .Padding = New Padding(0, 15, 0, 15)
            .BackColor = Color.Transparent
            .Size = New Size(111, 110)
            .tbAutoSize = False
            .tbIconHoldPlace = True
            .tbIconPlaceText = 10
            .tbIconMore = False
            .tbShadow = False
            '.tbShadowColor = Color.FromArgb(22, 74, 160)
            '.tbShadowOffset = New Point(0, -1)
            .tbShowToolTipOnButton = False
            .tbTextAlign = ContentAlignment.BottomCenter
            .tbTextColor = Color.FromArgb(51, 51, 51)
            .tbIconImageAlign = ContentAlignment.TopCenter
            .tbImageMouseDown = Nothing
            .tbImageMouseHover = Nothing
            .tbImageMouseLeave = Nothing
            .tbBackgroundImage = My.Resources.more_btn_3_bg
            .tbBackgroundImageState = ImageState.ThreeState
            '.tbAutoSizeEx = True
            .MinimumSize = New Size(111, 110)
            .MaximumSize = New Size(150, 110)

            .tbIconImage = imgIcon
            .tbIconReadOnly = imgIconReadonly
            If imgIconReadonly IsNot Nothing Then
                .tbReadOnly = True
                .tbReadOnlyText = True
            End If

            .tbSplit = "6,6,6,6"

            Dim strTypes As String = String.Empty
            For Each t As Type In arrType
                If t.FullName.Contains("AppUnion") Then
                    strTypes &= t.FullName & Me.mAssemblyName & "&"
                Else
                    strTypes &= t.FullName & "&"
                    'If t.FullName.Contains("frmWeixinEmoticon") AndAlso Not t.FullName.Contains("frmWeixinEmoticonApp") Then
                    '    strTypes = t.FullName & Me.mAssemblyNameCoreModule & "&"
                    'End If
                End If
            Next
            strTypes = strTypes.Trim("&"c)
            .Name = strTypes

            .tbText = strText
            .Font = fntText               'Common.CreateFont("Arial", 9, FontStyle.Regular)

            If args IsNot Nothing Then
                .Tag = args
            End If
        End With

        AddHandler btn.Click, AddressOf ToolItem_ClickEx
        AddHandler btn.MouseEnter, AddressOf ToolItem_MouseEnter
        AddHandler btn.MouseLeave, AddressOf ToolItem_MouseLeave
        Return btn
    End Function

    Private Function CreateGroup(ByVal strGroupName As String, Optional ByVal groupTag As String = "") As tbGroupPanel
        Dim group As New tbGroupPanel

        With group
            .Tag = groupTag
            .GroupName = strGroupName
            .GroupNameOffset = New Point(35, 20)
            .Padding = New Padding(15, 44, 15, 12)
            .Margin = System.Windows.Forms.Padding.Empty
            .Size = New Size(Me.flpMain.Width, 150)
            .ForeColor = Color.FromArgb(150, 150, 150)

            .Font = Common.CreateFont("Arial", 12, FontStyle.Regular)

            'If Me.flpMain.Controls.Count > 0 AndAlso Me.flpMain.Controls.Count Mod 2 = 1 Then
            '    .BackColor = Color.FromArgb(250, 251, 252) 'Color.FromArgb(232, 245, 251)
            'Else
            '    .BackColor = Color.FromArgb(242, 244, 247) ' Color.FromArgb(251, 252, 253)
            'End If

            .BackColor = Color.FromArgb(250, 251, 252) ' Color.FromArgb(251, 252, 253)
        End With

        Me.flpMain.Controls.Add(group)

        Return group
    End Function

    Private Sub SetItemReadOnly(ByVal blnEnable As Boolean) '
        'Dim strType As String = GetType(String).FullName

        For Each ctl As Control In Me.flpMain.Controls
            If TypeOf ctl Is tbGroupPanel Then
                Dim group As tbGroupPanel = CType(ctl, tbGroupPanel)

                For Each ctlIcon As Control In group.Controls
                    If TypeOf ctlIcon Is tbButton Then
                        Dim btn As tbButton = CType(ctlIcon, tbButton)
                        If btn.tbIconReadOnly Is Nothing Then 'btn.Name.Contains(strType) OrElse
                            Continue For
                        End If

                        btn.tbReadOnly = blnEnable
                        btn.tbReadOnlyText = blnEnable
                    End If
                Next
            End If
        Next
    End Sub

    Private Sub ItemRun(ByVal frmtype As Type, ByVal item As tbButton)
        Try
            'Dim bgw As System.ComponentModel.BackgroundWorker = Nothing
            'If Me.mDictBackground.ContainsKey(item.Name) Then
            '    bgw = Me.mDictBackground(item.Name)
            'Else
            '    Dim args As New List(Of Object)
            '    args.Add(Me.mApplication)
            '    args.Add(Me.mDevice)
            '    args.Add(item)

            '    bgw = Activator.CreateInstance(frmtype, args.ToArray())
            '    Me.mDictBackground(item.Name) = bgw
            'End If

            'item.tbReadOnly = True
            'If bgw IsNot Nothing AndAlso bgw.IsBusy = False Then
            '    bgw.RunWorkerAsync()
            'End If

            'If item.Name = GetType(ClearRubbish).ToString() Then
            '    Me.StartTools(ToolsType.ClearRubbish)
            'ElseIf item.Name = GetType(RepairSpringboard).ToString() Then
            '    Me.StartTools(ToolsType.RepairSpringboard)
            'ElseIf item.Name = GetType(ClearErrorIcon).ToString() Then
            '    Me.StartTools(ToolsType.ClearErrorIcon)
            'End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ItemRun")
        End Try
    End Sub

    'Private Sub StartTools(ByVal type As ToolsType)
    '    Dim tool As frmTools = New frmTools(Me.mApplication, Me.mDevice, type)
    '    tool.StartPosition = FormStartPosition.CenterParent
    '    tool.Show(Me.mApplication)
    'End Sub

    Private Sub OnInstallKingRoot(ByVal sender As Object, ByVal e As InstallKingRootProgress)
        Try
            If Me.mbtnKingRoot Is Nothing Then
                Return
            End If

            If e.Status = InstallKingRootProgress.InstallKingRootStatus.Downloading Then
                Me.mbtnKingRoot.tbShowProgress = True
                Me.mbtnKingRoot.tbProgressValue = e.Value
                'Me.mbtnKingRoot.Enabled = False
            ElseIf e.Status = InstallKingRootProgress.InstallKingRootStatus.DownloadFailed Then
                Me.mbtnKingRoot.tbShowProgress = False
                Me.mbtnKingRoot.Text = Me.Language.GetString("Tools.Payment.Message.DownloadFailure")  '下载失败
                Me.mbtnKingRoot.Enabled = True
            ElseIf e.Status = InstallKingRootProgress.InstallKingRootStatus.ErrorUnzip Then
                Me.mbtnKingRoot.tbShowProgress = False
                Me.mbtnKingRoot.Text = Me.Language.GetString("PkgInstaller.Label.InstallFail")  '安装失败
                Me.mbtnKingRoot.Enabled = True
            ElseIf e.Status = InstallKingRootProgress.InstallKingRootStatus.Succeed OrElse e.Status = InstallKingRootProgress.InstallKingRootStatus.TaskStop OrElse e.Status = InstallKingRootProgress.InstallKingRootStatus.TaskDelete Then
                Me.mbtnKingRoot.tbShowProgress = False
                Me.mbtnKingRoot.Text = "KingRoot"
                Me.mbtnKingRoot.Enabled = True
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmMore.OnInstallKingRoot")
        End Try

    End Sub
#End Region

#Region "--- 对外方法 ---"

    ''' <summary>
    ''' 铃声制作完成后，改变设备的铃声窗体显示——Added by Utmost20160615
    ''' </summary>
    ''' <param name="sender"></param>
    ''' <param name="e"></param>
    ''' <remarks></remarks>
    Private Sub OnMakeAndroidRingtoneImported(ByVal sender As System.Object, ByVal e As AndroidImportRingtonArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of AndroidImportRingtonArgs)(AddressOf OnMakeAndroidRingtoneImported), sender, e)
        Else

            Dim actType As ActionFuncType = ActionFuncType.Music
            If e.ImportRingtoneType = AndroidMusicRingtoneType.Alarm Then
                actType = ActionFuncType.AlarmRing
            ElseIf e.ImportRingtoneType = AndroidMusicRingtoneType.Notification Then
                actType = ActionFuncType.NotifyRing
            ElseIf e.ImportRingtoneType = AndroidMusicRingtoneType.Ringtone Then
                actType = ActionFuncType.Ringtone
            End If


            For Each frm As Form In Application.OpenForms
                If TypeOf frm Is frmAndroidMusic AndAlso mAndroid IsNot Nothing Then
                    If frm.Name.StartsWith(mAndroid.DeviceID, StringComparison.OrdinalIgnoreCase) Then
                        '' Noted by Utmost20161021
                        'Dim f As frmAndroidMusic = CType(frm, frmAndroidMusic)
                        'f.GoToDeviceNode(actType)
                        'f.Reload(False)
                        'f.SetRington(e.Rington, e.ImportRingtoneType)

                        With CType(frm, frmAndroidMusic)

                            .DeleteMusicRingtone(e.ImportRingtoneType)
                            .GoToDeviceNode(actType)
                            .ReloadRingtone(e.Rington, e.ImportRingtoneType)
                        End With

                        Exit For
                    End If
                End If
            Next
        End If
    End Sub

    Private Sub OnHasRingPathEvent(ByRef ringPath As String, ByRef bIsExist As Boolean)

        bIsExist = False
        Try
            If Not String.IsNullOrEmpty(ringPath) AndAlso File.Exists(ringPath) Then
                ''' Noted by Utmost20161021
                'Dim strMsg As String = String.Format(Me.Language.GetString("RingtoneMaker.Message.ExistIsReMake"), ringPath)  '"本地已存在同名的铃声文件：{0}；是否重新制作？"

                'bIsExist = (tbMessageBox.Show(Me.mApplication, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes)
                'If bIsExist Then
                '	File.Delete(ringPath)
                'Else
                '	Dim strTmp As String = Path.GetFileNameWithoutExtension(ringPath) + Guid.NewGuid().ToString("N").Substring(0, 4) + Path.GetExtension(ringPath)
                '	Dim strDir As String = Path.GetDirectoryName(ringPath)

                '	Dim strMp3Path As String = Path.Combine(strDir, strTmp)

                '	If File.Exists(ringPath) Then
                '		File.Copy(ringPath, strMp3Path, True)
                '	End If

                '	ringPath = strMp3Path
                'End If

                Dim strMsg As String = String.Format(Me.Language.GetString("RingtoneMaker.Message.ExistIsReMake"), ringPath)  '"本地已存在同名的铃声文件：{0}；是否重新制作？"

                bIsExist = (tbMessageBox.Show(Me.mApplication, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes)
                If bIsExist Then

                    Dim strTmp As String = Path.GetFileNameWithoutExtension(ringPath) + Guid.NewGuid().ToString("N").Substring(0, 4) + Path.GetExtension(ringPath)
                    Dim strMp3Path As String = Path.Combine(Path.GetDirectoryName(ringPath), strTmp)

                    ringPath = strMp3Path
                End If

            End If

            ''' Added by Utmost20161021
            For Each frm As Form In Application.OpenForms

                If TypeOf frm Is frmAndroidMusicLocal AndAlso mAndroid IsNot Nothing Then
                    CType(frm, frmAndroidMusicLocal).m_bIsRingMaker = True

                    Exit For
                End If

            Next

        Catch ex As Exception

        End Try
    End Sub

    Private Sub frmRingMakerForm_OnShowMessageHandler(ByVal sender As Object, ByVal obj As Object)

        Dim strMsg As String = obj.ToString()
        tbMessageBox.Show(sender, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)   '"提示"
    End Sub

    Private Sub frmWeixin_OnLoadFinished(ByVal sender As Object, ByVal args As WeixinEmoticonLoadArgs)
        If args.Status = WeixinState.Above6220 Then

            If Me.Tag IsNot Nothing AndAlso Me.Tag <> DeviceType.Unknown Then
                Dim mobileDevice As IDevice = Nothing

                If Me.Tag = DeviceType.Android Then
                    mobileDevice = Me.mAndroid
                ElseIf Me.Tag = DeviceType.iOS Then
                    mobileDevice = Me.mDevice
                End If
                frmWeixinEmoticonApp.ShowForm(Me.mApplication, mobileDevice, False)
            End If
        End If
    End Sub

#End Region

    Private Function EffectiveAbroad()
        Dim isRelust As Boolean = False
        Try
            Dim PFHelper As PremiumFeatureHelper = PremiumFeatureHelper.GetInstance()
            'If ChargeHelper.Instance().CheckIsChargeApp(Me.FunctionMappingKey) = Windows.Forms.DialogResult.None OrElse ChargeHelper.Instance().CheckIsChargeApp(Me.FunctionMappingKey) = Windows.Forms.DialogResult.OK Then
            '    Common.LogException(String.Format("PFHelper.CPremiumFeatureInfo.LstSku.Count:{0}  StrNewSku:{1}", PFHelper.CPremiumFeatureInfo.LstSku.Count, PFHelper.CPremiumFeatureInfo.StrNewSku))
            '    If PFHelper.CPremiumFeatureInfo.LstSku.Count = 0 OrElse PFHelper.CPremiumFeatureInfo.StrNewSku.Length = 0 Then
            '        tbMessageBox.Show(Me, "当前电脑尚未配置该功能，请联系客服！", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
            '    Else
            '        isRelust = True
            '    End If
            'End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "EffectiveAbroad")
        End Try
        Return isRelust
    End Function

    Private Function Effective()
        Dim isRelust As Boolean = False

        If Not PremiumFeatureHelper.GetShowWeChatVIPSpare() Then
            GoTo Do_Exit
        End If

        Try
            Dim PFHelper As PremiumFeatureHelper = PremiumFeatureHelper.GetInstance()
            If Not PFHelper.CPremiumFeatureInfo.IsLoadServer Then
                Dim frm As frmWait = New frmWait(Me.mApplication, "正在获取VIP服务信息，请稍后...")
                frm.ShowDialog(Me)
            End If

            If PFHelper.CPremiumFeatureInfo.IsOverdue Then
                Dim dr As DialogResult = tbMessageBox.Show(Me, "VIP会员已经过期，续费可继续使用！", Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button3, "", False, New String() {"续费", "联系客服", "取消"})
                If dr = Windows.Forms.DialogResult.Yes Then
                    Common.OpenExplorer("http://zs.tongbu.com/form.html")
                ElseIf dr = Windows.Forms.DialogResult.No Then
                    Common.OpenExplorer("http://wpa.qq.com/msgrd?v=3&uin=2827209190&site=qq&menu=yes")
                End If
                GoTo Do_Exit
            End If

            Dim strComtuper As String = Common.GetComputerID()

            If Not PFHelper.CPremiumFeatureInfo.LstComputer.Contains(strComtuper) Then
                If tbMessageBox.Show(Me, "当前电脑尚未配置该功能，请联系客服！", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                    Common.OpenExplorer("http://wpa.qq.com/msgrd?v=3&uin=2827209190&site=qq&menu=yes")
                End If
                GoTo Do_Exit
            End If

            Try
                Common.LogException("VIPDevice:" & Me.mDevice.UniqueDeviceID, "Effective_VIP")
            Catch
            End Try

            If String.IsNullOrEmpty(Me.mDevice.UniqueDeviceID) OrElse Not PFHelper.CPremiumFeatureInfo.LstDevice.Contains(Me.mDevice.UniqueDeviceID) Then
                If tbMessageBox.Show(Me, "当前设备尚未配置该功能，请联系客服！", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                    Common.OpenExplorer("http://wpa.qq.com/msgrd?v=3&uin=2827209190&site=qq&menu=yes")
                End If
                GoTo Do_Exit
            End If

            isRelust = True

        Catch ex As Exception
            Common.LogException(ex.ToString(), "Effective")
        End Try
Do_Exit:
        Return isRelust
    End Function

End Class
