﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmImageSearch
    Inherits frmDeviceBase

    ''' <summary>
    ''' 必需的设计器变量。
    ''' </summary>
    Private components As System.ComponentModel.IContainer = Nothing

    ''' <summary>
    ''' 清理所有正在使用的资源。
    ''' </summary>
    ''' <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso (components IsNot Nothing) Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

#Region "Windows 窗体设计器生成的代码"

    ''' <summary>
    ''' 设计器支持所需的方法 - 不要
    ''' 使用代码编辑器修改此方法的内容。
    ''' </summary>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Me.txtSearch = New tbSearch
        Me.pnlTool = New tbPanel
        Me.btnBack = New tbButton
        Me.btnSaveAs = New tbButton
        Me.btnJustThis = New tbButton
        Me.tbPanel2 = New tbPanel
        Me.SliderPin = New tbButton
        Me.tmrRun = New System.Windows.Forms.Timer(Me.components)
        Me.flpPannel = New System.Windows.Forms.FlowLayoutPanel
        Me.lblFormTitle = New tbLabel
        Me.lblProgress = New tbLabel
        Me.pbSearching = New System.Windows.Forms.PictureBox
        Me.pnlTop = New System.Windows.Forms.Panel
        Me.pnlMain = New System.Windows.Forms.Panel
        Me.pnlTool.SuspendLayout()
        Me.tbPanel2.SuspendLayout()
        CType(Me.pbSearching, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTop.SuspendLayout()
        Me.pnlMain.SuspendLayout()
        Me.SuspendLayout()
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.ForeColor = System.Drawing.SystemColors.WindowText
        Me.txtSearch.Location = New System.Drawing.Point(516, 5)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "搜索专辑封面"
        Me.txtSearch.ShowClear = False
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(279, 23)
        Me.txtSearch.TabIndex = 1
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.5
        '
        'pnlTool
        '
        Me.pnlTool.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.pnlTool.Controls.Add(Me.btnBack)
        Me.pnlTool.Controls.Add(Me.btnSaveAs)
        Me.pnlTool.Controls.Add(Me.btnJustThis)
        Me.pnlTool.Controls.Add(Me.tbPanel2)
        Me.pnlTool.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlTool.Location = New System.Drawing.Point(0, 499)
        Me.pnlTool.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTool.Name = "pnlTool"
        Me.pnlTool.Size = New System.Drawing.Size(798, 67)
        Me.pnlTool.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlTool.TabIndex = 3
        Me.pnlTool.tbBackgroundImage = My.Resources.player_bg
        Me.pnlTool.tbShowWatermark = False
        Me.pnlTool.tbSplit = "3,8,3,8"
        Me.pnlTool.tbWatermark = Nothing
        Me.pnlTool.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTool.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnBack
        '
        Me.btnBack.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBack.BackColor = System.Drawing.Color.Transparent
        Me.btnBack.BindingForm = Nothing
        Me.btnBack.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnBack.Location = New System.Drawing.Point(711, 21)
        Me.btnBack.Name = "btnBack"
        Me.btnBack.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBack.Selectable = True
        Me.btnBack.Size = New System.Drawing.Size(70, 25)
        Me.btnBack.TabIndex = 8
        Me.btnBack.tbAdriftIconWhenHover = False
        Me.btnBack.tbAutoSize = False
        Me.btnBack.tbAutoSizeEx = False
        Me.btnBack.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnBack.tbBackgroundImageState = ImageState.FourState
        Me.btnBack.tbBadgeNumber = 0
        Me.btnBack.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBack.tbEndEllipsis = False
        Me.btnBack.tbIconHoldPlace = True
        Me.btnBack.tbIconImage = Nothing
        Me.btnBack.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.tbIconImageState = ImageState.OneState
        Me.btnBack.tbIconMore = False
        Me.btnBack.tbIconMouseDown = Nothing
        Me.btnBack.tbIconMouseHover = Nothing
        Me.btnBack.tbIconMouseLeave = Nothing
        Me.btnBack.tbIconPlaceText = 2
        Me.btnBack.tbIconReadOnly = Nothing
        Me.btnBack.tbImageMouseDown = Nothing
        Me.btnBack.tbImageMouseHover = Nothing
        Me.btnBack.tbImageMouseLeave = Nothing
        Me.btnBack.tbReadOnly = False
        Me.btnBack.tbReadOnlyText = False
        Me.btnBack.tbShadow = False
        Me.btnBack.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnBack.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnBack.tbShowNew = False
        Me.btnBack.tbShowToolTipOnButton = False
        Me.btnBack.tbSplit = "13,11,13,11"
        Me.btnBack.tbText = "返回"
        Me.btnBack.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.tbTextColor = System.Drawing.Color.Black
        Me.btnBack.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnBack.tbTextColorDown = System.Drawing.Color.Black
        Me.btnBack.tbTextColorHover = System.Drawing.Color.Black
        Me.btnBack.tbTextMouseDownPlace = 0
        Me.btnBack.tbToolTip = ""
        Me.btnBack.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBack.tbToolTipFont = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.btnBack.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnSaveAs
        '
        Me.btnSaveAs.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSaveAs.BackColor = System.Drawing.Color.Transparent
        Me.btnSaveAs.BindingForm = Nothing
        Me.btnSaveAs.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnSaveAs.Location = New System.Drawing.Point(529, 21)
        Me.btnSaveAs.Name = "btnSaveAs"
        Me.btnSaveAs.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSaveAs.Selectable = True
        Me.btnSaveAs.Size = New System.Drawing.Size(85, 25)
        Me.btnSaveAs.TabIndex = 7
        Me.btnSaveAs.Tag = "另存为"
        Me.btnSaveAs.tbAdriftIconWhenHover = False
        Me.btnSaveAs.tbAutoSize = False
        Me.btnSaveAs.tbAutoSizeEx = False
        Me.btnSaveAs.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnSaveAs.tbBackgroundImageState = ImageState.FourState
        Me.btnSaveAs.tbBadgeNumber = 0
        Me.btnSaveAs.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSaveAs.tbEndEllipsis = False
        Me.btnSaveAs.tbIconHoldPlace = True
        Me.btnSaveAs.tbIconImage = Nothing
        Me.btnSaveAs.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSaveAs.tbIconImageState = ImageState.OneState
        Me.btnSaveAs.tbIconMore = False
        Me.btnSaveAs.tbIconMouseDown = Nothing
        Me.btnSaveAs.tbIconMouseHover = Nothing
        Me.btnSaveAs.tbIconMouseLeave = Nothing
        Me.btnSaveAs.tbIconPlaceText = 2
        Me.btnSaveAs.tbIconReadOnly = Nothing
        Me.btnSaveAs.tbImageMouseDown = Nothing
        Me.btnSaveAs.tbImageMouseHover = Nothing
        Me.btnSaveAs.tbImageMouseLeave = Nothing
        Me.btnSaveAs.tbReadOnly = False
        Me.btnSaveAs.tbReadOnlyText = False
        Me.btnSaveAs.tbShadow = True
        Me.btnSaveAs.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnSaveAs.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnSaveAs.tbShowNew = False
        Me.btnSaveAs.tbShowToolTipOnButton = False
        Me.btnSaveAs.tbSplit = "13,11,13,11"
        Me.btnSaveAs.tbText = "另存为"
        Me.btnSaveAs.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSaveAs.tbTextColor = System.Drawing.Color.White
        Me.btnSaveAs.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSaveAs.tbTextColorDown = System.Drawing.Color.White
        Me.btnSaveAs.tbTextColorHover = System.Drawing.Color.White
        Me.btnSaveAs.tbTextMouseDownPlace = 0
        Me.btnSaveAs.tbToolTip = ""
        Me.btnSaveAs.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSaveAs.tbToolTipFont = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.btnSaveAs.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnJustThis
        '
        Me.btnJustThis.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnJustThis.BackColor = System.Drawing.Color.Transparent
        Me.btnJustThis.BindingForm = Nothing
        Me.btnJustThis.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.btnJustThis.Location = New System.Drawing.Point(620, 21)
        Me.btnJustThis.Name = "btnJustThis"
        Me.btnJustThis.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnJustThis.Selectable = True
        Me.btnJustThis.Size = New System.Drawing.Size(85, 25)
        Me.btnJustThis.TabIndex = 6
        Me.btnJustThis.tbAdriftIconWhenHover = False
        Me.btnJustThis.tbAutoSize = False
        Me.btnJustThis.tbAutoSizeEx = False
        Me.btnJustThis.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnJustThis.tbBackgroundImageState = ImageState.FourState
        Me.btnJustThis.tbBadgeNumber = 0
        Me.btnJustThis.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnJustThis.tbEndEllipsis = False
        Me.btnJustThis.tbIconHoldPlace = True
        Me.btnJustThis.tbIconImage = Nothing
        Me.btnJustThis.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJustThis.tbIconImageState = ImageState.OneState
        Me.btnJustThis.tbIconMore = False
        Me.btnJustThis.tbIconMouseDown = Nothing
        Me.btnJustThis.tbIconMouseHover = Nothing
        Me.btnJustThis.tbIconMouseLeave = Nothing
        Me.btnJustThis.tbIconPlaceText = 2
        Me.btnJustThis.tbIconReadOnly = Nothing
        Me.btnJustThis.tbImageMouseDown = Nothing
        Me.btnJustThis.tbImageMouseHover = Nothing
        Me.btnJustThis.tbImageMouseLeave = Nothing
        Me.btnJustThis.tbReadOnly = False
        Me.btnJustThis.tbReadOnlyText = False
        Me.btnJustThis.tbShadow = False
        Me.btnJustThis.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnJustThis.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnJustThis.tbShowNew = False
        Me.btnJustThis.tbShowToolTipOnButton = False
        Me.btnJustThis.tbSplit = "13,11,13,11"
        Me.btnJustThis.tbText = "就用这张"
        Me.btnJustThis.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJustThis.tbTextColor = System.Drawing.Color.White
        Me.btnJustThis.tbTextColorDisable = System.Drawing.Color.White
        Me.btnJustThis.tbTextColorDown = System.Drawing.Color.White
        Me.btnJustThis.tbTextColorHover = System.Drawing.Color.White
        Me.btnJustThis.tbTextMouseDownPlace = 0
        Me.btnJustThis.tbToolTip = ""
        Me.btnJustThis.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnJustThis.tbToolTipFont = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.btnJustThis.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'tbPanel2
        '
        Me.tbPanel2.BackColor = System.Drawing.Color.Transparent
        Me.tbPanel2.Controls.Add(Me.SliderPin)
        Me.tbPanel2.Location = New System.Drawing.Point(44, 12)
        Me.tbPanel2.Name = "tbPanel2"
        Me.tbPanel2.Size = New System.Drawing.Size(175, 46)
        Me.tbPanel2.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.tbPanel2.TabIndex = 5
        Me.tbPanel2.tbBackgroundImage = My.Resources.pnl_Slider
        Me.tbPanel2.tbShowWatermark = False
        Me.tbPanel2.tbSplit = "0,0,0,0"
        Me.tbPanel2.tbWatermark = Nothing
        Me.tbPanel2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.tbPanel2.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'SliderPin
        '
        Me.SliderPin.BackColor = System.Drawing.Color.Transparent
        Me.SliderPin.BindingForm = Nothing
        Me.SliderPin.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.SliderPin.Location = New System.Drawing.Point(60, 17)
        Me.SliderPin.Name = "SliderPin"
        Me.SliderPin.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.SliderPin.Selectable = True
        Me.SliderPin.Size = New System.Drawing.Size(12, 12)
        Me.SliderPin.TabIndex = 4
        Me.SliderPin.TabStop = False
        Me.SliderPin.tbAdriftIconWhenHover = False
        Me.SliderPin.tbAutoSize = True
        Me.SliderPin.tbAutoSizeEx = False
        Me.SliderPin.tbBackgroundImage = Nothing
        Me.SliderPin.tbBackgroundImageState = ImageState.OneState
        Me.SliderPin.tbBadgeNumber = 0
        Me.SliderPin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.SliderPin.tbEndEllipsis = False
        Me.SliderPin.tbIconHoldPlace = True
        Me.SliderPin.tbIconImage = Nothing
        Me.SliderPin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.SliderPin.tbIconImageState = ImageState.OneState
        Me.SliderPin.tbIconMore = False
        Me.SliderPin.tbIconMouseDown = Nothing
        Me.SliderPin.tbIconMouseHover = Nothing
        Me.SliderPin.tbIconMouseLeave = Nothing
        Me.SliderPin.tbIconPlaceText = 2
        Me.SliderPin.tbIconReadOnly = Nothing
        Me.SliderPin.tbImageMouseDown = My.Resources.pnl_slider_blue
        Me.SliderPin.tbImageMouseHover = My.Resources.pnl_slider_normal
        Me.SliderPin.tbImageMouseLeave = My.Resources.pnl_slider_normal
        Me.SliderPin.tbReadOnly = False
        Me.SliderPin.tbReadOnlyText = False
        Me.SliderPin.tbShadow = False
        Me.SliderPin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.SliderPin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.SliderPin.tbShowNew = False
        Me.SliderPin.tbShowToolTipOnButton = False
        Me.SliderPin.tbSplit = "3,3,3,3"
        Me.SliderPin.tbText = ""
        Me.SliderPin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.SliderPin.tbTextColor = System.Drawing.Color.White
        Me.SliderPin.tbTextColorDisable = System.Drawing.Color.White
        Me.SliderPin.tbTextColorDown = System.Drawing.Color.White
        Me.SliderPin.tbTextColorHover = System.Drawing.Color.White
        Me.SliderPin.tbTextMouseDownPlace = 0
        Me.SliderPin.tbToolTip = ""
        Me.SliderPin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.SliderPin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.SliderPin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'tmrRun
        '
        '
        'flpPannel
        '
        Me.flpPannel.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.flpPannel.AutoScroll = True
        Me.flpPannel.BackColor = System.Drawing.Color.White
        Me.flpPannel.Location = New System.Drawing.Point(0, 33)
        Me.flpPannel.Margin = New System.Windows.Forms.Padding(0)
        Me.flpPannel.Name = "flpPannel"
        Me.flpPannel.Size = New System.Drawing.Size(798, 466)
        Me.flpPannel.TabIndex = 4
        '
        'lblFormTitle
        '
        Me.lblFormTitle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblFormTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblFormTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFormTitle.Location = New System.Drawing.Point(393, 5)
        Me.lblFormTitle.Name = "lblFormTitle"
        Me.lblFormTitle.Size = New System.Drawing.Size(117, 22)
        Me.lblFormTitle.TabIndex = 22
        Me.lblFormTitle.tbAdriftWhenHover = False
        Me.lblFormTitle.tbAutoEllipsis = False
        Me.lblFormTitle.tbAutoSize = False
        Me.lblFormTitle.tbHideImage = False
        Me.lblFormTitle.tbIconImage = Nothing
        Me.lblFormTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFormTitle.tbIconPlaceText = 5
        Me.lblFormTitle.tbShadow = True
        Me.lblFormTitle.tbShadowColor = System.Drawing.Color.White
        Me.lblFormTitle.tbShadowOffset = New System.Drawing.Point(0, 1)
        Me.lblFormTitle.tbShowScrolling = False
        Me.lblFormTitle.Text = "封面搜索"
        Me.lblFormTitle.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblProgress
        '
        Me.lblProgress.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblProgress.BackColor = System.Drawing.Color.Transparent
        Me.lblProgress.ForeColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblProgress.Location = New System.Drawing.Point(25, 8)
        Me.lblProgress.Name = "lblProgress"
        Me.lblProgress.Size = New System.Drawing.Size(363, 17)
        Me.lblProgress.TabIndex = 24
        Me.lblProgress.tbAdriftWhenHover = False
        Me.lblProgress.tbAutoEllipsis = False
        Me.lblProgress.tbAutoSize = False
        Me.lblProgress.tbHideImage = False
        Me.lblProgress.tbIconImage = Nothing
        Me.lblProgress.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblProgress.tbIconPlaceText = 5
        Me.lblProgress.tbShadow = True
        Me.lblProgress.tbShadowColor = System.Drawing.Color.White
        Me.lblProgress.tbShadowOffset = New System.Drawing.Point(0, 1)
        Me.lblProgress.tbShowScrolling = False
        Me.lblProgress.Text = "label"
        Me.lblProgress.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbSearching
        '
        Me.pbSearching.BackColor = System.Drawing.Color.Transparent
        Me.pbSearching.Image = My.Resources.gif_searching
        Me.pbSearching.Location = New System.Drawing.Point(5, 8)
        Me.pbSearching.Name = "pbSearching"
        Me.pbSearching.Size = New System.Drawing.Size(16, 16)
        Me.pbSearching.TabIndex = 25
        Me.pbSearching.TabStop = False
        Me.pbSearching.Visible = False
        '
        'pnlTop
        '
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.lblFormTitle)
        Me.pnlTop.Controls.Add(Me.txtSearch)
        Me.pnlTop.Controls.Add(Me.pbSearching)
        Me.pnlTop.Controls.Add(Me.lblProgress)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlTop.Location = New System.Drawing.Point(0, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(798, 32)
        Me.pnlTop.TabIndex = 7
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.pnlMain.Controls.Add(Me.flpPannel)
        Me.pnlMain.Controls.Add(Me.pnlTop)
        Me.pnlMain.Controls.Add(Me.pnlTool)
        Me.pnlMain.Location = New System.Drawing.Point(1, 31)
        Me.pnlMain.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(798, 566)
        Me.pnlMain.TabIndex = 8
        '
        'frmImageSearch
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.SystemColors.Control
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.pnlMain)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New System.Drawing.Size(20, 66)
        Me.Name = "frmImageSearch"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = My.Resources.frm_bg_state
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,33"
        Me.Text = "ImageSearchForm"
        Me.Controls.SetChildIndex(Me.pnlMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlTool.ResumeLayout(False)
        Me.tbPanel2.ResumeLayout(False)
        CType(Me.pbSearching, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTop.ResumeLayout(False)
        Me.pnlMain.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub

#End Region

    Friend WithEvents txtSearch As tbSearch
    Friend WithEvents pnlTool As tbPanel
    Friend WithEvents SliderPin As tbButton
    Friend WithEvents tbPanel2 As tbPanel
    Friend WithEvents btnJustThis As tbButton
    Friend WithEvents btnSaveAs As tbButton
    Friend WithEvents btnBack As tbButton
    Friend WithEvents tmrRun As System.Windows.Forms.Timer
    Friend WithEvents flpPannel As System.Windows.Forms.FlowLayoutPanel
    Friend WithEvents lblFormTitle As tbLabel
    Friend WithEvents lblProgress As tbLabel
    Friend WithEvents pbSearching As System.Windows.Forms.PictureBox
    Friend WithEvents pnlTop As System.Windows.Forms.Panel
    Friend WithEvents pnlMain As System.Windows.Forms.Panel

End Class
