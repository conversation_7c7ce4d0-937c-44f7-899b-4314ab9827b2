﻿Public Class frmImageSearch

    Private _currentSearchString As String = String.Empty
    '当前的搜索关键字
    Private _currentSize As Integer = 155
    Private _minSize As Integer = 155
    Private _maxSize As Integer = 775
    Private _hasLoadCount As Integer = 0
    Private dotCount As Integer = 0
    Private _Mp3Path As String = String.Empty
    Private currentThreadCode As Integer = 0
    Private searchHelper As ImageSearchHelper

    Private _artist As String = String.Empty
    Private _album As String = String.Empty

#Region "-   构造函数　初始化    -"

    Public Sub New(ByVal application As IApplication, ByVal artist As String, ByVal album As String)
        InitializeComponent()

        mApplication = application
        Me.Language = mApplication.Language

        Me.Icon = My.Resources.iTong
        Me._artist = artist
        Me._album = album
        Me.FilletRadius = 5

        Me._minSize = 180
        Me._currentSize = 180
        Me._maxSize = 770

        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbSplit = "10,33,10,33"
        Me.BackColor = System.Drawing.Color.FromArgb(233, 237, 243) ' Color.FromArgb(20, 111, 184)
        Me.CanResize = True
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("ImageSearch.Label.GetCover")                           '"获取封面"
        Me.btnBack.tbText = Me.Language.GetString("Common.Button.Return")                       '"返回"
        Me.btnSaveAs.tbText = Me.Language.GetString("Common.SaveAs")                            '"另存为"
        Me.btnJustThis.tbText = Me.Language.GetString("ImageSearch.Button.JustThis")            '"就用这张"
        Me.lblFormTitle.Text = Me.Language.GetString("ImageSearch.Button.CoverSearch")          '"封面搜索"
        Me.txtSearch.SearchTipText = Me.Language.GetString("ImageSearch.Label.SearchAlbum")     '"搜索专辑封面"
        Me.lblProgress.Text = ""
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        '_currentSize = _minSize;          
        'this.CanbeMove = false;

        flpPannel.Padding = New Padding(5, 5, 5, 5)

        If Me.Parent IsNot Nothing Then
            Me.pnlMain.Dock = DockStyle.Fill
        Else
            Me.btnBack.Visible = False
            Me.btnJustThis.Location = New Point(Me.btnBack.Right - Me.btnJustThis.Width, Me.btnBack.Top)
            Me.btnSaveAs.Location = New Point(Me.btnJustThis.Left - (Me.btnSaveAs.Width + 5), Me.btnJustThis.Top)
        End If

        txtSearch.TimerInterval = 2

        If Common.VerIs30() Then
            Me.SliderPin.Size = New Size(14, 14)
        End If

        tmrRun.Interval = 500
        tmrRun.Enabled = True
        tmrRun.Start()
    End Sub

    Protected Overrides Sub OnShown(ByVal e As EventArgs)
        MyBase.OnShown(e)

        If (Not String.IsNullOrEmpty(_artist) OrElse Not String.IsNullOrEmpty(_album)) Then
            Me.SearchByKey(_artist, _album, String.Empty)
        End If
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Try
            If searchHelper IsNot Nothing Then
                Me.searchHelper.StopSeaching()
            End If
        Catch ex As Exception
        End Try

    End Sub

#End Region

#Region "-   对外接口    -"

    Public Delegate Sub JustThisPicHandler(ByVal imagePath As String, ByVal mp3Path As String)
    Public Event JustThisPic As JustThisPicHandler

    ''' <summary>
    ''' 搜索关键字，没有选项传空值,供外部调用
    ''' </summary>
    ''' <param name="singerName">歌手名</param>
    ''' <param name="albumName">专辑名</param>
    Public Sub SearchByKey(ByVal singerName As String, ByVal albumName As String, ByVal Mp3Path As String)
        singerName = singerName & ""
        albumName = albumName & ""
        Mp3Path = Mp3Path & ""

        Me.Visible = True
        _Mp3Path = Mp3Path
        Dim strTemp As String = singerName & " " & albumName
        If _currentSearchString <> strTemp.Trim() Then
            _currentSearchString = strTemp.Trim()
            txtSearch.Text = _currentSearchString
            Me.BegionSearch(singerName, albumName)
        End If
    End Sub

#End Region

#Region "-   封面下载    -"

    Private Sub BegionSearch(ByVal singerName As String, ByVal albumName As String)
        If searchHelper IsNot Nothing Then
            searchHelper.StopSeaching()
        End If

        searchHelper = New ImageSearchHelper()
        searchHelper.DownloadMsgSuccessed = Me.Language.GetString("ImageSearch.Label.NoDataOver")           '"没有查到数据，结束！"
        _hasLoadCount = 0
        currentThreadCode = New Random().[Next](100000000)
        ImageSearchHelper.CurrentThreadCode = currentThreadCode
        flpPannel.Controls.Clear()
        Application.DoEvents()
        lblProgress.Text = Me.Language.GetString("ImageSearch.Label.Searching")                             '"正在搜索..."
        myDelegate = New addPicFile(AddressOf AddPic2Lv)

        AddHandler searchHelper.SearchProgress, AddressOf searchHelper_SearchProgress

        Dim args As New SearchArgs()
        With args
            .theradCode = currentThreadCode
            .maxLoad = 50
            .savePath = Path.Combine(Folder.TempFolder, "AlbumImage")
            .pageCount = 0
            .author = singerName
            .albumname = albumName
            .pageCount = 2
            .isGoogle = False
        End With


        searchHelper.BeginSearch(args)

        ' 显示正在搜索专辑封面
        Me.pbSearching.Visible = True
    End Sub

    ''' <summary>
    ''' 0：intInfo(图片的评分数)strInfo(图片下到地址);2:下载失败     3：跳过的URL  4：没获取到数据   5：下载完成
    ''' </summary>
    ''' <param name="progState">0：intInfo(图片的评分数)strInfo(图片下到地址);2:下载失败     3：跳过的URL  4：没获取到数据   5：下载完成</param>
    ''' <param name="threadCode">线程编号</param>
    ''' <param name="strInfo"></param>
    Private Sub searchHelper_SearchProgress(ByVal progState As Integer, ByVal threadCode As Integer, ByVal strInfo As String)
        Try
            If threadCode = currentThreadCode Then
                Select Case progState
                    Case 0
                        AddPic2Lv(strInfo)
                        Exit Select
                    Case 1
                        Exit Select
                    Case 4
                        Exit Select
                    Case 5
                        lblProgress.Text = String.Format(Me.Language.GetString("ImageSearch.Label.DownloadedCover"), _hasLoadCount)         '"下载完成，总计{0}张图片"
                        '"下载完成，总计{0}张图片"
                        Me.pbSearching.Visible = False
                        Exit Select
                End Select
            Else
            End If
        Catch
        End Try
    End Sub

    Private Delegate Sub addPicFile(ByVal filepath As [String])
    Private myDelegate As addPicFile
    Private Sub AddPic2Lv(ByVal filePath As String)
        If flpPannel.InvokeRequired Then
            flpPannel.Invoke(myDelegate, New Object(0) {filePath})
        Else
            Dim tbBox As New tbPicBox()
            If tbBox.SetImagePath(filePath) Then
                '"正在下载({0}){1}"
                lblProgress.Text = String.Format(Me.Language.GetString("ImageSearch.Label.Downloading"), System.Threading.Interlocked.Increment(_hasLoadCount), GetLoadingText())
                '正在下载({0}){1}
                AddHandler tbBox.DoubleClick, AddressOf tbBox_DoubleClick
                'tbBox.ImagePath = filePath;
                tbBox.Size = New Size(_currentSize, _currentSize)
                flpPannel.SuspendLayout()
                flpPannel.Controls.Add(tbBox)
                flpPannel.ResumeLayout()
            Else
                '"正在下载({0}){1}"
                lblProgress.Text = String.Format(Me.Language.GetString("ImageSearch.Label.Downloading"), System.Threading.Interlocked.Increment(_hasLoadCount), GetLoadingText())
            End If
        End If

    End Sub

    Private Sub txtSearch_TextChangedByTimer(ByVal sender As Object, ByVal e As EventArgs) Handles txtSearch.TextChangedByTimer
        Dim strSearch As String = txtSearch.Text.Trim()

        If Not String.IsNullOrEmpty(strSearch) AndAlso Not strSearch.Equals(_currentSearchString, StringComparison.OrdinalIgnoreCase) Then
            _currentSearchString = strSearch
            Me.BegionSearch(_currentSearchString, "")
        End If
    End Sub

#End Region

#Region "-   进度条拉动     -"

    Private _sliderMinX As Integer = 30
    Private _silderMaxX As Integer = 130
    Private _silderCurrent As Integer = 30
    Private m_lLastKeyTime As Double = 0
    Private Sub SliderPin_MouseDown(ByVal sender As Object, ByVal e As MouseEventArgs) Handles SliderPin.MouseDown
        If e.Button = System.Windows.Forms.MouseButtons.Left Then
            Me._silderCurrent = SliderPin.Left
        End If
    End Sub

    Private Sub SliderPin_MouseMove(ByVal sender As Object, ByVal e As MouseEventArgs) Handles SliderPin.MouseMove
        If e.Button = System.Windows.Forms.MouseButtons.Left Then
            Dim x As Integer = e.Location.X + SliderPin.Left
            If x < _sliderMinX Then
                x = _sliderMinX
            ElseIf x > _silderMaxX Then
                x = _silderMaxX
            End If

            SliderPin.Left = x
            _silderCurrent = x

            '根据 SliderPin 的位置设置图片大小
            If Me.m_lLastKeyTime = 0 Then
                Me.m_lLastKeyTime = New TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second).TotalSeconds
            End If
        End If
    End Sub

    Private Sub timer1_Tick(ByVal sender As Object, ByVal e As EventArgs) Handles tmrRun.Tick
        If Me.m_lLastKeyTime > 0 AndAlso New TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second).TotalSeconds - Me.m_lLastKeyTime > 1 Then
            Me.m_lLastKeyTime = 0
            Me.SetPictureSize()
        End If
    End Sub

    Private Sub SetPictureSize()
        Dim persent As Integer = _silderCurrent - _sliderMinX
        _currentSize = (_maxSize - _minSize) * persent \ 100 + _minSize
        flpPannel.SuspendLayout()
        For Each ctr As Control In flpPannel.Controls
            Dim pic As tbPicBox = DirectCast(ctr, tbPicBox)
            pic.Size = New Size(_currentSize, _currentSize)
        Next
        flpPannel.ResumeLayout()
        flpPannel.Refresh()
    End Sub

#End Region

#Region "-   按纽事件    -"

    Private Sub btnBack_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnBack.Click
        Me.CheckClose()
    End Sub

    Private Sub CheckClose()
        If Me.Parent Is Nothing Then
            If searchHelper IsNot Nothing Then
                Me.searchHelper.StopSeaching()
            End If
            Me.DialogResult = Windows.Forms.DialogResult.OK
            Me.Close()
        Else
            Me.Hide()
        End If
    End Sub

    Private Sub btnJustThis_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnJustThis.Click
        Me.OnJustThist("")
    End Sub

    Private Sub OnJustThist(ByVal filePath As String)
        Dim selectImagePath As String = filePath

        If selectImagePath.Length = 0 Then
            selectImagePath = GetSelectedImagePath()
        End If

        If Not String.IsNullOrEmpty(selectImagePath) Then
            '将图片写进MP3中
            RaiseEvent JustThisPic(selectImagePath, _Mp3Path)
            Me.CheckClose()
        Else
            '"请选择一张图片。"
            tbMessageBox.Show(Me.Language.GetString("ImageSearch.Message.SelectOnePic"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub btnSaveAs_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btnSaveAs.Click
        Dim str As String = GetSelectedImagePath()

        If Not String.IsNullOrEmpty(str) Then

            Using saveDialog As New SaveFileDialog()
                saveDialog.Filter = "*.jpg|*.jpg|*.png|*.png"
                If saveDialog.ShowDialog() = Windows.Forms.DialogResult.OK Then
                    Try
                        Dim img As Image = Image.FromFile(str)
                        If img Is Nothing Then Exit Sub

                        Dim saveName As String = saveDialog.FileName

                        If saveName.EndsWith(".png", StringComparison.OrdinalIgnoreCase) Then
                            img.Save(saveName, System.Drawing.Imaging.ImageFormat.Png)
                        Else
                            img.Save(saveName, System.Drawing.Imaging.ImageFormat.Jpeg)
                        End If

                        img.Dispose()

                    Catch ex As Exception
                        Common.LogException(ex.ToString())
                    End Try

                End If
            End Using

        Else
            '"请选择一张图片。"
            tbMessageBox.Show(Me.Language.GetString("ImageSearch.Message.SelectOnePic"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Function GetSelectedImagePath() As String
        For Each ctr As Control In flpPannel.Controls
            Dim pic As tbPicBox = DirectCast(ctr, tbPicBox)
            If pic.IsChecked Then
                Return pic.ImagePath
            End If
        Next
        Return String.Empty
    End Function

    Private Function GetLoadingText() As String
        If System.Threading.Interlocked.Increment(dotCount) > 5 Then
            dotCount = 1
        End If
        Return "".PadRight(dotCount, "."c)
    End Function

    Private Sub flpPannel_MouseClick(ByVal sender As Object, ByVal e As MouseEventArgs) Handles flpPannel.MouseClick
        flpPannel.Focus()
    End Sub

    '图片双击事件
    Private Sub tbBox_DoubleClick(ByVal obj As Object, ByVal e As MouseEventArgs)
        '在浏览图片窗体里显示专辑封面
        Dim tbpic As tbPicBox = DirectCast(obj, tbPicBox)
        If tbpic.ImgDraw IsNot Nothing Then
            '显示图片预览窗体
            Dim frmImageView As New tbImageViewForm()

            frmImageView.Icon = My.Resources.iTong
            frmImageView.ViewType = ViewType.FromPC
            frmImageView.ShowPath = False
            frmImageView.ShowListView = True
            frmImageView.SrcPicture = DirectCast(tbpic.ImgDraw.Clone(), Image)
            frmImageView.MainButtonType = MainButtonType.ApplyToCover
            frmImageView.HideButton = ToolBarButtonType.Delete Or ToolBarButtonType.WeiBo

            Dim largeList As New ImageList()
            largeList.ImageSize = New Size(75, 75)
            largeList.ColorDepth = ColorDepth.Depth32Bit

            frmImageView.ListView.LargeImageList = largeList

            For i As Integer = 0 To flpPannel.Controls.Count - 1
                Dim tbpicCurrent As tbPicBox = TryCast(flpPannel.Controls(i), tbPicBox)
                Dim newItem As New ListViewItem()
                newItem.Name = tbpicCurrent.ImagePath
                newItem.ImageKey = tbpicCurrent.ImagePath
                If Not largeList.Images.ContainsKey(tbpicCurrent.ImagePath) Then
                    largeList.Images.Add(newItem.ImageKey, Utility.GetThumbnail(tbpicCurrent.ImgDraw, New Size(75, 75), True))
                End If

                frmImageView.ListView.Items.Add(newItem)
            Next
            frmImageView.SelectItemName = tbpic.ImagePath

            AddHandler frmImageView.MainClick, AddressOf frmImageView_MainClick

            frmImageView.Show()
        End If
    End Sub

    Private Sub frmImageView_MainClick(ByVal sender As Object, ByVal e As ImageEventArgs)
        Dim frmImageView As tbImageViewForm = TryCast(sender, tbImageViewForm)
        frmImageView.DrawPicture.Save(frmImageView.SelectItemName, frmImageView.SrcPicture.RawFormat)
        Me.OnJustThist(frmImageView.SelectItemName)

        frmImageView.Close()
    End Sub


#End Region

End Class
