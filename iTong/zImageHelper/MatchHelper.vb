﻿Imports System.Text.RegularExpressions

Public Class MatchHelper

    ''' <summary>
    ''' 获取所有符合样式的内容
    ''' </summary>
    ''' <param name="strInput">输入串</param>
    ''' <param name="patterns">样式列表</param>
    ''' <returns>index pattern result groupNames[]</returns>
    Public Shared Function GetMatchStrings(ByVal strInput As String, ByVal patterns As String(), ByVal groupNames As String()) As DataTable
        Dim dt As New DataTable("patternResult")
        dt.Columns.Add(New DataColumn("index", GetType(Integer)))
        dt.Columns.Add(New DataColumn("pattern", GetType(String)))
        dt.Columns.Add(New DataColumn("result", GetType(String)))
        If groupNames.Length > 0 Then
            dt.Columns.Add(New DataColumn("flag", GetType(String)))
        End If
        For Each strGroup As String In groupNames
            If strGroup.Trim() <> String.Empty Then
                dt.Columns.Add(New DataColumn(strGroup, GetType(String)))
            End If
        Next
        For Each pattern As String In patterns
            If pattern <> String.Empty Then
                Dim r As New Regex(pattern, RegexOptions.Multiline Or RegexOptions.Compiled Or RegexOptions.IgnorePatternWhitespace)
                Dim match As Match = r.Match(strInput)
                While match.Success
                    Dim gs As GroupCollection = match.Groups
                    Dim dr As DataRow = dt.NewRow()
                    dr("index") = match.Index
                    dr("pattern") = pattern
                    dr("result") = match.ToString()
                    For Each strGroup As String In groupNames
                        If strGroup.Trim() <> String.Empty Then

                            dr(strGroup) = gs(strGroup).ToString()
                            If gs(strGroup).ToString() <> String.Empty Then
                                dr("flag") = strGroup
                            End If
                        End If
                    Next
                    dt.Rows.Add(dr)
                    match = match.NextMatch()
                End While
            End If
        Next
        dt.DefaultView.Sort = "index asc"
        dt = dt.DefaultView.ToTable()
        Return dt
    End Function
End Class