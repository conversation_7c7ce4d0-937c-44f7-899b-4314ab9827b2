﻿Imports System.Collections.Generic
Imports System.ComponentModel
Imports System.Drawing
Imports System.Data
Imports System.Text
Imports System.Windows.Forms
Imports System.Drawing.Drawing2D

Public Class tbPicBox
    Inherits UserControl

    Public Sub New()
        Me.SetStyle(ControlStyles.UserPaint, True)
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)
        Me.SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        Me.SetStyle(ControlStyles.DoubleBuffer, True)
        Me.SetStyle(ControlStyles.Selectable, True)
        Me.SetStyle(ControlStyles.ContainerControl, False)
        Me.SetStyle(ControlStyles.ResizeRedraw, True)

        _tbName = Guid.NewGuid().ToString()

        _CheckedBrush = GuiHelper.GetCacheBrush(Color.FromArgb(180, 142, 153, 162))
        _UnCheckedBrush = GuiHelper.GetCacheBrush(Color.FromArgb(120, 120, 120, 162))
        _InfoBrush = GuiHelper.GetCacheBrush(SystemColors.Info)

        Try
            _TextFont = New Font("monaco", 9)
        Catch ex As Exception
            _TextFont = New Font("宋体", 9)
        End Try
    End Sub

#Region "-   成员变量    -"
    Private _tbName As String
    Private _imgDraw As Image = Nothing
    Private _imagePath As String = String.Empty
    Private _CheckedBrush As Brush
    Private _UnCheckedBrush As Brush
    Private _InfoBrush As Brush
    Private _TextFont As Font

    Private ReadOnly Property DrawRec() As Rectangle
        Get
            Return New Rectangle(4, 4, Me.ClientSize.Width - 8, Me.ClientSize.Height - 8)
        End Get
    End Property
    Private _isMouseEnter As Boolean = False
    Private _isChecked As Boolean = False
#End Region

#Region "-   对外属性    -"
    Public Delegate Sub DoubleClickHandler(ByVal obj As Object, ByVal e As MouseEventArgs)
    Public Shadows Event DoubleClick As DoubleClickHandler

    Public ReadOnly Property tbName() As String
        Get
            Return _tbName
        End Get
    End Property

    Public Property IsChecked() As Boolean
        Get
            Return _isChecked
        End Get
        Set(ByVal value As Boolean)
            If _isChecked <> value Then
                _isChecked = value
                Me.Invalidate()
                If _isChecked Then
                    CallOtherNotCheck()
                End If
            End If
        End Set
    End Property

    Public ReadOnly Property ImagePath() As String
        Get
            Return _imagePath
        End Get
    End Property
    'set
    '{
    '    _imagePath = value;
    '    if (_imagePath != string.Empty)
    '    {
    '        _imgDraw = CoreFoundation.Utility.GetImageFormFile(_imagePath);
    '    }
    '    else
    '    {
    '        _imgDraw = null;
    '    }
    '    this.Invalidate();
    '}

    Public Function SetImagePath(ByVal imagePath As String) As Boolean
        _imagePath = imagePath
        If _imagePath <> String.Empty Then
            If _imgDraw IsNot Nothing Then
                _imgDraw.Dispose()
                _imgDraw = Nothing
            End If

            _imgDraw = CoreFoundation.Utility.GetImageFormFile(_imagePath)
        Else
            If _imgDraw IsNot Nothing Then
                _imgDraw.Dispose()
                _imgDraw = Nothing
            End If
        End If
        Me.Invalidate()

        If _imgDraw Is Nothing Then
            Return False
        Else
            Return True
        End If

    End Function

    Public ReadOnly Property ImgDraw() As Image
        Get
            Return _imgDraw
        End Get
    End Property

#End Region

    Protected Overrides Sub OnPaint(ByVal e As PaintEventArgs)
        MyBase.OnPaint(e)

        If _imgDraw Is Nothing Then
            Return
        End If


        Dim g As Graphics = e.Graphics
        g.InterpolationMode = InterpolationMode.HighQualityBicubic
        g.SmoothingMode = SmoothingMode.HighQuality

        If _isChecked Then
            g.FillRectangle(_CheckedBrush, Me.ClientRectangle)
        ElseIf _isMouseEnter Then
            g.FillRectangle(_UnCheckedBrush, Me.ClientRectangle)
        End If

        Dim imgWidth As Integer = _imgDraw.Width
        Dim imgHeight As Integer = _imgDraw.Height

        Dim destX As Integer = 0
        Dim destY As Integer = 0
        Dim destWidth As Integer = DrawRec.Width
        Dim destHeight As Integer = DrawRec.Height

        If CSng(imgWidth) / CSng(destWidth) > CSng(imgHeight) / CSng(destHeight) Then
            If imgWidth <= destWidth Then
                destWidth = imgWidth
                destHeight = imgHeight
            Else
                destHeight = CInt(Math.Truncate(CSng(destWidth) / CSng(imgWidth) * imgHeight))
            End If
        Else
            If imgHeight <= destHeight Then
                destHeight = imgHeight
                destWidth = imgWidth
            Else
                destWidth = CInt(Math.Truncate(CSng(destHeight) / CSng(imgHeight) * imgWidth))
            End If
        End If
        destX = (DrawRec.Width - destWidth) \ 2 + DrawRec.X
        destY = (DrawRec.Height - destHeight) \ 2 + DrawRec.Y

        g.DrawImage(_imgDraw, New Rectangle(destX, destY, destWidth, destHeight), New Rectangle(0, 0, imgWidth, imgHeight), GraphicsUnit.Pixel)

        Dim drawString As String = String.Format("{0}x{1}", _imgDraw.Width, _imgDraw.Height)
        Dim strSize As SizeF = g.MeasureString(drawString, _TextFont)
        destX = destX + destWidth - CInt(Math.Truncate(strSize.Width)) - 2
        destY = destY + destHeight - CInt(Math.Truncate(strSize.Height)) - 2
        Dim rect As New Rectangle(destX, destY, CInt(Math.Truncate(strSize.Width)), CInt(Math.Truncate(strSize.Height)))

        g.FillRectangle(_InfoBrush, rect)
        g.DrawRectangle(Pens.Black, rect)
        g.DrawString(drawString, _TextFont, Brushes.Black, New PointF(destX, destY))
    End Sub

    Protected Overrides Sub OnMouseEnter(ByVal e As System.EventArgs)
        MyBase.OnMouseEnter(e)

        If Not _isMouseEnter Then
            _isMouseEnter = True
            Me.Invalidate()
        End If
    End Sub

    Protected Overrides Sub OnMouseLeave(ByVal e As System.EventArgs)
        MyBase.OnMouseLeave(e)

        If _isMouseEnter Then
            _isMouseEnter = False
            Me.Invalidate()
        End If
    End Sub

    Private Sub CallOtherNotCheck()
        Dim fp As FlowLayoutPanel = DirectCast(Me.Parent, FlowLayoutPanel)
        For Each ctr As Control In fp.Controls
            Dim pic As tbPicBox = DirectCast(ctr, tbPicBox)
            If pic.tbName <> _tbName Then
                pic.IsChecked = False
            End If
        Next

    End Sub

    Protected Overrides Sub OnClick(ByVal e As System.EventArgs)
        MyBase.OnClick(e)

        IsChecked = True
    End Sub

    Protected Overrides Sub OnMouseDoubleClick(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseDoubleClick(e)

        RaiseEvent DoubleClick(Me, e)
    End Sub

End Class