﻿Imports System.Text
Imports System.IO
Imports System.Drawing
Imports System.Drawing.Imaging
Imports System.Net
Imports System.Web
Imports System.Threading


Public Class ImageSearchHelper
    Public Shared CurrentThreadCode As Integer = 0
    Private strDownloadMsgSuccessed As String = ""
    Private strDownloadMsgNoData As String = ""

    Public Property DownloadMsgSuccessed() As String
        Get
            Return strDownloadMsgSuccessed
        End Get
        Set(ByVal value As String)
            strDownloadMsgSuccessed = value
        End Set
    End Property
    Public Property DownloadMsgNoData() As String
        Get
            Return strDownloadMsgNoData
        End Get
        Set(ByVal value As String)
            strDownloadMsgNoData = value
        End Set
    End Property

    ''' <param name="progState">0：intInfo(图片的评分数)strInfo(图片下到地址);   1：intInfo（获取到图片总数）    2:下载失败 
    ''' 3：跳过的URL  4：没获取到数据   5：下载完成</param>
    ''' <param name="intInfo"></param>
    ''' <param name="strInfo"></param>
    Public Delegate Sub SearchProgressHandler(ByVal progState As Integer, ByVal threadCode As Integer, ByVal strInfo As String)
    Public Event SearchProgress As SearchProgressHandler
    Private mThreadSearch As Thread = Nothing
    Private Sub ActiveEvent(ByVal progState As Integer, ByVal threadCode As Integer, ByVal strInfo As String)
        RaiseEvent SearchProgress(progState, threadCode, strInfo)
    End Sub

    ''' <summary>
    ''' 开始搜索
    '''</summary>
    '''<param name="maxLoad">图片最大下载数</param>
    '''<param name="savePath">图片保存路径</param>
    '''<param name="imgSize">图片尺寸（百度有效）：所有尺寸： 0;小图：1;中图： 2;"大图"：3;</param>
    ''' <param name="author">歌手名</param>
    ''' <param name="albumname">专辑名</param>
    ''' <param name="pageCount">读取搜索引擎的页面数1，2</param>
    ''' <param name="isGoogle">True:利用Google搜索  False:利用百度搜索</param>
    Public Sub BeginSearch(ByVal args As SearchArgs)
        Try
            If mThreadSearch IsNot Nothing AndAlso mThreadSearch.ThreadState <> ThreadState.Stopped Then
                mThreadSearch.Abort()
            End If
        Catch ex As Exception
        End Try
        mThreadSearch = New Thread(AddressOf SearchImages)
        mThreadSearch.IsBackground = True
        mThreadSearch.Start(args)
    End Sub

    Private Sub SearchImages(ByVal state As Object)
        Try
            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = False

            Dim args As SearchArgs = state

            Dim index As Integer = 1
            Dim url1 As String = String.Empty
            Dim url2 As String = String.Empty
            Dim searchValue As String = args.author & " " & args.albumname

            Dim mainName As String = Guid.NewGuid().ToString().Substring(0, 5)
            Dim encode As Encoding = Encoding.UTF8

            searchValue = searchValue.Trim()
            args.savePath = args.savePath.TrimEnd("\"c) & "\"


            If args.isGoogle Then
                Dim strGoogleURL As String = ServerIniSetting.GetUrl(True)
                'http://www.google.com.hk/images?q={0}&um=1&hl=zh-CN&newwindow=1&safe=strict&tbs=isch:1&ei=OUwxTMvAM4ugkQX_k6xL&sa=N&start=0&ndsp=21
                url1 = String.Format(strGoogleURL, HttpUtility.UrlEncode(searchValue, Encoding.UTF8))
                'http://www.google.com.hk/images?q={0}&um=1&hl=zh-CN&newwindow=1&safe=strict&tbs=isch:1&ei=OUwxTMvAM4ugkQX_k6xL&sa=N&start=21&ndsp=21
                url2 = String.Format(strGoogleURL, HttpUtility.UrlEncode(searchValue, Encoding.UTF8))
            Else
                encode = Encoding.GetEncoding("gb2312")

                Dim strBaiduURL As String = ServerIniSetting.GetUrl(False)
                'url1 = string.Format("http://image.baidu.com/i?tn=baidulocal&ct=201326592&cl=2&lm=-1&fr=&pv=&ic=0&z={2}&se=1&word={0}&s=0&sme=0&rn=21&pn={1}&ln=2000",
                url1 = String.Format(strBaiduURL, HttpUtility.UrlEncode(searchValue, Encoding.GetEncoding("gb2312")), 0, args.imgSize)
                'url2 = string.Format("http://image.baidu.com/i?tn=baidulocal&ct=201326592&cl=2&lm=-1&fr=&pv=&ic=0&z={2}&se=1&word={0}&s=0&sme=0&rn=21&pn={1}&ln=2000",
                url2 = String.Format(strBaiduURL, HttpUtility.UrlEncode(searchValue, Encoding.GetEncoding("gb2312")), 21, args.imgSize)
            End If

            '开始获取第一页
            Dim content As String = Utility.GetContentStringFromUrl(url1, encode)
            If args.pageCount > 1 Then
                '开始下载第二页:                
                content += Utility.GetContentStringFromUrl(url2, encode)
            End If

            Dim fields As String() = New String(4) {"width", "height", "discription", "size", "url"}
            Dim strRegularBaidu As String = ServerIniSetting.GetRegular(False)
            '"{[\\w\\W]*?\"objURL\":\"(?<url>[\\w\\W]*?)\"[\\w\\W]*?\"width\":(?<width>[\\d]+),\"height\":(?<height>[\\d]+),[\\w\\W]*?\"filesize\":\"(?<size>[\\d]+)\"[\\w\\W]*?\"fromPageTitle\":\"(?<discription>[\\w\\W]*?)\"[\\w\\W]*?}"
            Dim matchs As String() = New String(0) {strRegularBaidu}
            If args.isGoogle Then
                Dim strRegularGoogle As String = ServerIniSetting.GetRegular(True)
                '"imgres\\?imgurl[\\w\\W]*?\"(?<url>http://[\\S]*?(jpg)|(png))\",\"[\\d]+\",\"[\\d]+\",\"(?<discription>[\\w\\W]*?)\"[\\w\\W]*?(?<width>[\\d]*)[\\s]×[\\s](?<height>[\\d]+)[\\s]-[\\s](?<size>[\\d]+)k"
                matchs = New String(0) {strRegularGoogle}
            End If
            Dim dt As DataTable = MatchHelper.GetMatchStrings(content, matchs, fields)
            If dt IsNot Nothing Then
                Dim count As Integer = dt.Rows.Count
                '总共获取图片数据{0}条，开始对数据进行分析...
                Me.ActiveEvent(1, args.theradCode, count.ToString())
                dt = CaltureDataTable(dt, args.author, args.albumname)
                If count > args.maxLoad Then
                    count = args.maxLoad
                End If
                '分析完成，开始下载前{0}条可下数据
                If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                    Dim filePath As String = String.Empty
                    If Not Directory.Exists(args.savePath & mainName) Then
                        Directory.CreateDirectory(args.savePath & mainName)
                    End If


                    'string pattern = @"(^[\s\t\xa0\u3000]+)|([\u3000\xa0\s\t]+$)";
                    'System.Text.RegularExpressions.Regex reg = new System.Text.RegularExpressions.Regex(pattern);
                    For Each dr As DataRow In dt.Rows
                        Dim strUrl As String = dr("url").ToString()
                        Dim urlJpg As String = strUrl

                        '如果开头不是http:// 也不是 https:// ，则需要转换为真正的url
                        If Not strUrl.ToLower().StartsWith("http://") AndAlso Not strUrl.ToLower().StartsWith("https://") Then
                            urlJpg = Me.ConvertUrlToNormalUrl(strUrl)
                        End If

                        If Me.SkipUrl(urlJpg) Then
                            'ext = Path.GetExtension(urlJpg);
                            filePath = args.savePath & mainName & "\" & Guid.NewGuid().ToString() & ".jpg"
                            If Common.DownloadImage(urlJpg, 6000, filePath) Then
                                '线程立刻退出，以便后续线程进来
                                If args.theradCode <> CurrentThreadCode Then
                                    Return
                                End If

                                Me.ActiveEvent(0, args.theradCode, filePath)
                                index += 1
                                If index > count Then
                                    Exit For
                                End If
                            Else
                                Me.ActiveEvent(2, args.theradCode, urlJpg)
                            End If
                        Else
                            Me.ActiveEvent(3, args.theradCode, urlJpg)

                        End If
                    Next
                End If
                '"下载完成！"
                Me.ActiveEvent(5, args.theradCode, DownloadMsgSuccessed)
            Else
                '"没有查到数据，结束！"
                Me.ActiveEvent(4, args.theradCode, DownloadMsgNoData)
            End If
        Catch
        End Try
    End Sub

    Private Function ConvertUrlToNormalUrl(ByVal strUrl As String) As String
        Dim strReplace As String = strUrl.Replace(" ", "").Replace(vbTab, "").Replace(" ", "").Replace("　", "") _
                                                          .Replace("_z2C$q", ":").Replace("_z&e3B", ".").Replace("AzdH3F", "/")
        Dim sBuilder As New StringBuilder()

        For Each c As Char In strReplace
            Select Case c.ToString()
                Case "w"
                    sBuilder.Append("a")
                Case "k"
                    sBuilder.Append("b")
                Case "v"
                    sBuilder.Append("c")
                Case "1"
                    sBuilder.Append("d")
                Case "j"
                    sBuilder.Append("e")
                Case "u"
                    sBuilder.Append("f")
                Case "2"
                    sBuilder.Append("g")
                Case "i"
                    sBuilder.Append("h")
                Case "t"
                    sBuilder.Append("i")
                Case "3"
                    sBuilder.Append("j")
                Case "h"
                    sBuilder.Append("k")
                Case "s"
                    sBuilder.Append("l")
                Case "4"
                    sBuilder.Append("m")
                Case "g"
                    sBuilder.Append("n")
                Case "5"
                    sBuilder.Append("o")
                Case "r"
                    sBuilder.Append("p")
                Case "q"
                    sBuilder.Append("q")
                Case "6"
                    sBuilder.Append("r")
                Case "f"
                    sBuilder.Append("s")
                Case "p"
                    sBuilder.Append("t")
                Case "7"
                    sBuilder.Append("u")
                Case "e"
                    sBuilder.Append("v")
                Case "o"
                    sBuilder.Append("w")
                Case "8"
                    sBuilder.Append("1")
                Case "d"
                    sBuilder.Append("2")
                Case "n"
                    sBuilder.Append("3")
                Case "9"
                    sBuilder.Append("4")
                Case "c"
                    sBuilder.Append("5")
                Case "m"
                    sBuilder.Append("6")
                Case "0"
                    sBuilder.Append("7")
                Case "b"
                    sBuilder.Append("8")
                Case "l"
                    sBuilder.Append("9")
                Case "a"
                    sBuilder.Append("0")
                Case Else
                    sBuilder.Append(c.ToString())
            End Select
        Next

        Return sBuilder.ToString()
    End Function

    ''' <summary>
    ''' 如果线程存在，则中止线程
    ''' </summary>
    Public Sub StopSeaching()
        If mThreadSearch IsNot Nothing Then
            mThreadSearch.Abort()
        End If
    End Sub

    ''' <summary>
    ''' 跳过无法下载的图片地址
    ''' </summary>
    ''' <param name="strUrl"></param>
    ''' <returns></returns>
    Private Function SkipUrl(ByVal strUrl As String) As Boolean
        If strUrl.Contains("imgsrc.baidu.com") Then
            Return False
        End If
        Return True
    End Function

    ''' <summary>
    ''' 给DataTable中的图片进行估分计算
    ''' </summary>
    ''' <param name="dt"></param>
    ''' <param name="author">搜索的歌手名</param>
    ''' <param name="albumname">搜索的专辑名</param>
    ''' <returns></returns>
    Private Function CaltureDataTable(ByVal dt As DataTable, ByVal author As String, ByVal albumname As String) As DataTable
        If dt Is Nothing Then
            Return Nothing
        End If
        dt.Columns.Add(New DataColumn("score", GetType(Integer)))
        Dim score As Integer = 0
        Dim index As Integer = 0
        For Each dr As DataRow In dt.Rows
            score = 0
            If index < 5 Then
                score += 70 - index * 10
                index += 1
            End If
            Dim strInfo As String = dr("discription").ToString().ToLower()
            author = author.ToLower()
            albumname = albumname.ToLower()
            If strInfo.IndexOf("《" & albumname & "》") > 0 Then
                score += 150
            ElseIf strInfo.IndexOf(albumname) > 0 Then
                score += 120
            End If
            If strInfo.IndexOf(author) > 0 Then
                score += 100
            End If
            'if (strInfo.IndexOf("《") > 0)
            '{
            '    score += 60;
            '}
            If strInfo.IndexOf("专辑") > 0 Then
                score += 50
            End If
            Dim width As Integer = Integer.Parse(dr("width").ToString())
            Dim height As Integer = Integer.Parse(dr("height").ToString())
            If width < 400 OrElse height < 300 Then
                Dim temp As Integer = IIf(width > height, height, width)
                score -= 500 - temp
            End If

            If width > 400 AndAlso height > 400 Then
                Dim temp As Integer = IIf(width > height, height, width)
                temp = (temp - 400) \ 10
                If temp > 60 Then
                    temp = 60
                End If
                score += temp
            End If

            '去除过大的图片
            If width > 1800 OrElse height > 1800 Then
                score -= 10000
            End If

            'int size = int.Parse(dr["size"].ToString());
            'if (size > 30)
            '{
            '    score += (int)((size - 30) * ((float)15 / (float)10));
            '}

            '#Region "- 清晰度 -"
            'int quaity = (size * 1000000) / (width * height);
            'score += quaity;
            '#End Region

            dr("score") = score
        Next

        dt.DefaultView.Sort = "score desc ,width desc"
        dt = dt.DefaultView.ToTable()
        Return dt
    End Function

End Class

Public Class SearchArgs
    Public theradCode As Integer
    Public maxLoad As Integer
    Public savePath As String
    Public imgSize As Integer
    Public author As String
    Public albumname As String
    Public pageCount As Integer
    Public isGoogle As Boolean

End Class