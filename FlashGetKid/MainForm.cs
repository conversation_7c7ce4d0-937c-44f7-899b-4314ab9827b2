﻿using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Device;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Threading;
using System.Windows.Forms;

namespace FlashGetKid
{
    public partial class MainForm : frmBase
    {
        private static List<MainForm> mListMainForm = new List<MainForm>();

        private static Color mDefaultPanelskBorderStrokeColor = Color.FromArgb(228, 230, 234);

        private Color mWhiteColor = Color.White;

        private static skBorderRadius mRightAngle = new skBorderRadius(0);

        private static skBorderType mPanelBorderType = skBorderType.Top;

        private string mCurrentLangDisplayName = string.Empty;

        private SelectDeviceMode mSelectDeviceMode = SelectDeviceMode.Normal;

        private skTimer mTimer = null;

        private tdActionHelper<tdActionItemForFGKid> mActionHelper = tdActionHelper<tdActionItemForFGKid>.Instance();

        public static MainForm ShowForm(bool topMost = false)
        {
            MainForm currentForm = null;

            foreach (var form in mListMainForm)
            {
                if (form is MainForm mainForm)
                {
                    currentForm = mainForm;
                }
            }

            if (currentForm == null)
            {
                currentForm = new MainForm();
            }

            currentForm.Show();
            currentForm.ShowInTaskbar = true;

            currentForm.Activate();
            currentForm.TopMost = true;
            currentForm.TopMost = false;
            return currentForm;
        }

        public static MainForm GetMainForm()
        {
            MainForm currentForm = null;

            foreach (var form in mListMainForm)
            {
                if (form is MainForm mainForm)
                {
                    currentForm = mainForm;
                }
            }

            return currentForm;
        }

        public static string CurrentLangDisplayName;

        //public override int skTitleBarHeight => 40;

        //public override int skStatusBarHeight => 0;

        public override skSplit skTransparentImageSplit => new skSplit(15);


        public MainForm()
        {
            //PageHelper.InitCef();

            ThreadMgr.Start(this.GetIOSModels);

            mListMainForm.Add(this);

            InitializeComponent();

            this.mCurrentLangDisplayName = LanguageInterface.Instance().CurrentLanguage.LangDisplayName;

            this.skShowButtonMin = true;

            this.btnClose.skBackgroundImage = Properties.Resources.ic_4_close;
            this.btnClose.skBackgroundImageState = skImageState.FourState;
            this.btnClose.skShowIcon = false;

            this.btnMin.skBackgroundImage = Properties.Resources.ic_4_hide;
            this.btnMin.skBackgroundImageState = skImageState.FourState;
            this.btnMin.skShowIcon = false;

            this.btnClose.skBackgroundImage = Properties.Resources.ic_4_close;
            this.btnClose.skBackgroundImageState = skImageState.FourState;
            this.btnClose.skShowIcon = false;

            this.btnMin.skBackgroundImage = Properties.Resources.ic_4_hide;
            this.btnMin.skBackgroundImageState = skImageState.FourState;
            this.btnMin.skShowIcon = false;

            this.Icon = Properties.Resources.airdroid;
            this.skIcon = Properties.Resources.logo_all.ToBitmap();
            this.ShowIcon = true;
            this.skIconSize = new Size(24, 24);

            this.skTitle = "FlashGet Kids Connector"; //Airdroid Parental Connect;
            this.skTitleFont = MyFont.CreateFont(9.75f, true);
            this.skTitleColor = Color.FromArgb(45, 47, 51);
            this.skBackgroundColor = mWhiteColor;
            this.skStatusBarBackgroundColor = Color.FromArgb(228, 230, 234);

            MyForm.SetOutScreen(this);
        }

        protected override void InitControls()
        {
            try
            {
                base.InitControls();

                this.skTitleBarHeight = 40;
                this.skStatusBarHeight = 0;

                this.StartListenHandle();

                this.InitStyle();

                this.NotifyIcon.Icon = Properties.Resources.airdroid;
                this.NotifyIcon.Text = "FlashGet Kids Connector";  //Airdroid Parental Connect;

                this.tsmiExit.Text = this.Language.GetString("common.exit");
                this.tsmFeedback.Text = this.Language.GetString("Business.Feedback.logs.title");

                this.skTransparentImagePadding = new Padding(6, 6, 6, 6);
                this.skTransparentImage = MyResource.GetImage("frm_bg_shadow");
                this.skBorderType = skBorderType.Round;
                this.skBorderStrokeColor = mWhiteColor;
                this.skBorderRadius = new skBorderRadius(2);

                string strModify = string.Empty;
                if (MyLog.IsTestMode)
                {
                    strModify = string.Format(" ({0})", new FileInfo(Application.ExecutablePath).LastWriteTime.ToString("yyyy-MM-dd HH:mm"));
                }

                this.NotifyIcon.Text = "FlashGet Kids Connector";  //Airdroid Parental Connect;
                this.lblVersion.skText = string.Format("v{0}{1}", Common.GetSoftVersion(), strModify);
                this.lblInTestMode.Visible = false;

                MyTest.Callback += OnTestCallback;
                MyTest.InitTestMenu(this.cmsBottom);

            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MainForm.InitControls");
            }
        }

        private void InitStyle()
        {
            this.Size = new Size(940, 580);
            Size size = Screen.PrimaryScreen.WorkingArea.Size;
            this.Left = (size.Width - Width) / 2;
            this.Top = (size.Height - Height) / 2;
            this.WindowState = FormWindowState.Normal;

            this.btnLanguageSetting.skAutoSize = true;
            this.btnLanguageSetting.MinimumSize = new Size(75, 28);
            this.btnLanguageSetting.skBorderRadius = new skBorderRadius(12);

            this.btnLanguageSetting.skText = this.LanguageDisplayNameHandle(this.mCurrentLangDisplayName);
            this.btnLanguageSetting.Location = new Point(this.cbsplit.Location.X - 7 - this.btnLanguageSetting.Width, 6);

            this.btnFeedBack.Location = new Point(this.btnLanguageSetting.Location.X - 2 - this.btnFeedBack.Width, 9);

            this.pnlSelectSituation.skBorderType = mPanelBorderType;
            this.pnlFirstGuide.skBorderType = mPanelBorderType;
            this.pnlConnectingGuide.skBorderType = mPanelBorderType;

            this.pnlSelectSituation.skBorderRadius = mRightAngle;
            this.pnlFirstGuide.skBorderRadius = mRightAngle;
            this.pnlConnectingGuide.skBorderRadius = mRightAngle;

            this.pnlSelectSituation.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlFirstGuide.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlConnectingGuide.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlSelectSituationEnable.skBorderRadius = new skBorderRadius(16);
            this.pnlSelectSituationDisable.skBorderRadius = new skBorderRadius(16);
            this.pnlSelectSituationDisableSub.skBorderRadius = new skBorderRadius(10);
            this.pnlSelectSituationEnableSub.skBorderRadius = new skBorderRadius(10);

            this.lblFirstGuideTitle.skText = string.Concat("    ", this.Language.GetString("APC.FirstPage.Title")); //开始之前，请将设备通过 USB 与电脑进行连接
            this.lblFirstGuideContent.skText = this.Language.GetString("APC.First.Description"); //AirDroid Parental Connector 能帮助您开启和关闭设备的监督模式，让您能更好的管控孩子的设备。
            this.lblFirstGuideNote.skText = this.Language.GetString("Parental.Connect.Usb.noteforwin"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.
            this.btnFirstGuideNoDevice.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?
            this.btnFirstGuideLearnMore.skText = string.Concat(this.Language.GetString("rs.ar.not.support.tip.link"), " >"); //Learn More

            this.lblSelectSituationTitle.skText = this.Language.GetString("Parental.Connect.home.select"); //Please Select a Situation to Continue
            this.lblSelectSituationEnableDescribe.skText = this.Language.GetString("Parental.Connect.home.open"); //Enable iOS device supervision mode
            this.lblSelectSituationDisableDescribe.skText = this.Language.GetString("Parental.Connect.home.close"); //Disable iOS device supervision mode

            this.lblEnableSupervisionModeContent.skText = this.Language.GetString("APC.EnableSupervision.Description"); //开启后即可体验完整模式的 AirDroid Parental Control 功能，帮助您更好地管控儿童设备。
            this.lblDisableSupervisionModeContent.skText = this.Language.GetString("APC.DisableSupervision.Description"); //当您不再需要 AirDroid Parental Control 帮助您管理儿童设备时再关闭监督模式。

            this.lblSelectSituationEnableDescribe.Padding = new Padding(0, 0, 0, 5);
            this.lblSelectSituationDisableDescribe.Padding = new Padding(0, 0, 0, 5);

            if (this.HaveNoDevice())
                this.ShowFirstGuideHandle();
            else
                this.ShowSelectModeHandle();

            this.SelectFirstPage(); //选择首页

            if (this.mCurrentPanel == this.pnlConnectingGuide)
            {
                this.ShowSelectModeHandle();

                this.ShowFirstGuideHandle();
            }
            else
            {
                if (this.mCurrentPanel == this.pnlFirstGuide)
                    this.ShowSelectModeHandle();
                else
                    this.ShowFirstGuideHandle();
            }

            #region 边框类型

            this.pnlCloseFindMyiPhoneTip.skBorderType = mPanelBorderType;

            this.pnlUsbConnectTip.skBorderType = mPanelBorderType;
            this.pnlConnecting.skBorderType = mPanelBorderType;
            this.pnlConnectionFailed.skBorderType = mPanelBorderType;
            this.pnlDeviceNotDetected.skBorderType = mPanelBorderType;

            this.pnlEnableConnectedSuccess.skBorderType = mPanelBorderType;
            this.pnlActivatingSupervisionMode.skBorderType = mPanelBorderType;
            this.pnlEnableSupervisionModeSuccess.skBorderType = mPanelBorderType;

            this.pnlDisableConnectedSuccess.skBorderType = mPanelBorderType;
            this.pnlDeactivatingSupervisionMode.skBorderType = mPanelBorderType;
            this.pnlDisableSupervisionModeSuccess.skBorderType = mPanelBorderType;

            #endregion 边框类型

            #region 边框圆角

            this.pnlCloseFindMyiPhoneTip.skBorderRadius = mRightAngle;

            this.pnlUsbConnectTip.skBorderRadius = mRightAngle;
            this.pnlConnecting.skBorderRadius = mRightAngle;
            this.pnlConnectionFailed.skBorderRadius = mRightAngle;
            this.pnlDeviceNotDetected.skBorderRadius = mRightAngle;

            this.pnlEnableConnectedSuccess.skBorderRadius = mRightAngle;
            this.pnlActivatingSupervisionMode.skBorderRadius = mRightAngle;
            this.pnlEnableSupervisionModeSuccess.skBorderRadius = mRightAngle;

            this.pnlDisableConnectedSuccess.skBorderRadius = mRightAngle;
            this.pnlDeactivatingSupervisionMode.skBorderRadius = mRightAngle;
            this.pnlDisableSupervisionModeSuccess.skBorderRadius = mRightAngle;

            #endregion 边框圆角

            #region 边框颜色

            this.pnlCloseFindMyiPhoneTip.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlUsbConnectTip.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlConnecting.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlConnectionFailed.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlDeviceNotDetected.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlEnableConnectedSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlActivatingSupervisionMode.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlEnableSupervisionModeSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            this.pnlDisableConnectedSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlDeactivatingSupervisionMode.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;
            this.pnlDisableSupervisionModeSuccess.skBorderStrokeColor = mDefaultPanelskBorderStrokeColor;

            #endregion 边框颜色

            this.lblUsbConnectTipTitle.skText = this.Language.GetString("Parental.Connect.Usb.title"); //请将设备通过USB与电脑进行连接
            this.lblUsbConnectTipDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text1"); //Enable supervision mode to access the full functionality of AirDroid Parents, including device disabling and app restrictions, etc.
            this.lblUsbConnectTipNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //注意:\r\n1.确保你已经解锁了设备屏幕。\r\n2.请检查USB连接线
            this.btnUsbConnectQuestion.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?

            this.lblDeviceNotDetectedTip1.skBorderRadius = new skBorderRadius(12);
            this.lblDeviceNotDetectedTip2.skBorderRadius = new skBorderRadius(12);
            this.lblDeviceNotDetectedTip3.skBorderRadius = new skBorderRadius(12);

            this.lblDeviceNotDetectedTitle.skText = this.Language.GetString("APC.NotDevice.Description"); //No device detected. Please:         this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?
            //this.lblDeviceNotDetectedDescribe.skText = this.Language.GetString("Parental.Connect.nodevice.tilte"); //Please check with following steps and try again.
            this.lblDeviceNotDetectedMethod1.skText = this.Language.GetString("Parental.Connect.nodevice.text1"); //Unlock your device
            this.lblDeviceNotDetectedMethod2.skText = this.Language.GetString("Parental.Connect.nodevice.text2"); //Select [Trust] from the screen of the device
            this.lblDeviceNotDetectedMethod3.skText = this.Language.GetString("Parental.Connect.nodevice.text3"); //Please change another USB cable or USB port of computer

            this.lblConnectingTitle.skText = this.Language.GetString("Parental.Connect.connectdevice.title"); //Connecting to the device... 
            this.lblConnectingDescribe.skText = this.Language.GetString("Parental.Connect.connectdevice.text"); //Please keep the USB cable connection

            this.lblConnectingGuide1.skBorderRadius = new skBorderRadius(12);
            this.lblConnectingGuide2.skBorderRadius = new skBorderRadius(12);

            this.lblConnectingGuideTitle.skText = this.Language.GetString("Parental.Connect.trusteddevice.title"); //Please select [Trust]on your device and complete the following steps to enter your device password.
            this.lblConnectingGuideStep1.skText = this.Language.GetString("Parental.Connect.nodevice.text2");// Select [Trust] from the screen of the device
            this.lblConnectingGuideStep2.skText = this.Language.GetString("Parental.Connect.nodevice.text1"); //Unlock your device

            this.lblConnectionFailedTitle.skText = this.Language.GetString("Common.ConnectFailed"); //Connection failed
            this.lblConnectionFailedNote.skText = this.Language.GetString("Parental.Connect.Usb.fail.note"); //Note: \r\nPlease ensure your device remains connected during the process
            this.btnConnectionFailedTip.skText = this.Language.GetString("Parental.Connect.Usb.buttion.nodevice"); //Device not detected?

            this.btnConnectionFailedTryAgain.skBorderRadius = new skBorderRadius(18);
            this.btnConnectionFailedTryAgain.skText = this.Language.GetString("Common.TryAgain.Button"); //Try again

            this.lblEnableConnectedSuccessTitle.skText = string.Format(this.Language.GetString("Parental.Connect.Usb.success.title"), this.mDevice == null ? string.Empty : this.mDevice.DeviceName); //Device Emma’s iphone has successfully connected.
            this.lblEnableConnectedSuccessDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text1"); //Enable supervision mode to use the full functionality of AirDroid Parents, which supports device disabling and app restrictions, and more.
            this.lblEnableConnectedSuccessNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.

            this.btnStartEnableSupervisionMode.skText = this.Language.GetString("Common.Start"); //Start
            this.btnStartEnableSupervisionMode.skBorderRadius = new skBorderRadius(18);

            this.lblActivatingSupervisionModeTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.open"); //Activating supervision mode
            this.lblActivatingSupervisionModeDeviceName.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicename"); //Device name:
            this.lblActivatingSupervisionModeDeviceModel.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicemodel"); //Device model:
            this.lblActivatingSupervisionModeTip.skText = this.Language.GetString("Parental.Connect.supervisionmode.note"); //The entire process will take about 1 minute, please be patient

            this.lblStartSuccessfullyTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.text1"); //Congratulations, you have done everything
            this.lblStartSuccessfullyDescribe.skText = this.Language.GetString("turn.off.app.Limits.setting"); //如果您之前正在使用iOS系统的【屏幕使用时间】，为了避免冲突请关闭【App限额】的限制

            this.btnEnableSupervisionModeFinish.skText = this.Language.GetString("Button.Done"); //Finish
            this.btnEnableSupervisionModeFinish.skBorderRadius = new skBorderRadius(18);

            this.lblDisableConnectedSuccessTitle.skText = this.Language.GetString("Parental.Connect.Usb.success.title"); // Device Emma’s iphone has successfully connected
            this.lblDisableConnectedSuccessDescribe.skText = this.Language.GetString("Parental.Connect.Usb.text2"); //Removing AirDroid parental control mode and device monitoring
            this.lblDisableConnectedSuccessNotice.skText = this.Language.GetString("Parental.Connect.Usb.note"); //Notice:1. Make sure you have unlocked the device screen.2.Please check the USB connection cable.

            this.btnStartDisableSupervisionMode.skText = this.Language.GetString("Common.Start"); //Start
            this.btnStartDisableSupervisionMode.skBorderRadius = new skBorderRadius(18);

            this.lblDeactivatingSupervisionModeTitle.skText = this.Language.GetString("Parental.Connect.supervisionmode.close"); //Deactivating supervision mode
            this.lblDeactivatingSupervisionModeDeviceName.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicename"); //Device name:
            this.lblDeactivatingSupervisionModeDeviceModel.skText = this.Language.GetString("Parental.Connect.supervisionmode.devicemodel"); //Device model:
            this.lblDeactivatingSupervisionModeTip.skText = this.Language.GetString("Parental.Connect.supervisionmode.note"); //The entire process will take about 1 minute, please be patient

            this.lblCloseSuccessfullyDescribe.skText = this.Language.GetString("Parental.Connect.supervisionmode.text2"); //Congratulations! You have successfully disabled supervision mode.

            this.btnDisableSupervisionModeFinish.skText = this.Language.GetString("Button.Done"); //Finish

            this.lblDriveInstalled.skText = this.Language.GetString("Parental.InstallDrive.successful"); //驱动已安装完成

            //关闭查找我的手机页面
            this.lblCloseFindMyiPhoneTipTitle.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.title");//Please Follow These Steps to Disable "Find My iPhone"
            this.lblCloseFindMyiPhoneTipContent1.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step1"); //Go to "Setting > [Your Name] > Find My"
            this.lblCloseFindMyiPhoneTipContent2.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step2"); //Select "Find My iPhone",and turn it off
            this.lblCloseFindMyiPhoneTipContent3.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.step3"); //Enter your Apple ID password and tap "Turn Off"
            this.SetCloseFindMyiPhoneRecheckLabelStyle();
            this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"); //Yes,turned off

            this.btnCloseFindMyiPhoneTip.skAutoSize = true;
            this.btnCloseFindMyiPhoneTip.MaximumSize = new Size(218, 18);
            this.btnCloseFindMyiPhoneTip.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.tips");//无法关闭“查找我的设备”
            this.btnCloseFindMyiPhoneTip.Location = new Point(this.picCloseFindMyiPhoneTip3.Location.X + this.picCloseFindMyiPhoneTip3.Width / 2 - this.btnCloseFindMyiPhoneTip.Width / 2, this.lblCloseFindMyiPhoneTipContent3.Location.Y + this.lblCloseFindMyiPhoneTipContent3.Height);

            this.btnDisableSupervisionModeFinish.skBorderRadius = new skBorderRadius(18);

            this.btnLanguageSetting.skBorderRadius = new skBorderRadius(10);            

            this.lblVersion.Anchor = AnchorStyles.Bottom | AnchorStyles.Right | AnchorStyles.Left;
            this.lblVersion.skTextFont = MyFont.CreateFont(10, true, true);
            this.lblVersion.Location = new Point(10, this.lblInTestMode.Bottom + 10);
            this.lblVersion.Size = new Size(this.Width - 20, this.lblVersion.Height);
            this.lblVersion.Visible = true;
        }

        /// <summary>选择模式 显示处理</summary>
        private void ShowSelectModeHandle()
        {
            this.lblEnableSupervisionModeContent.Location = new Point(this.lblSelectSituationEnableDescribe.Location.X, this.lblSelectSituationEnableDescribe.Location.Y + this.lblSelectSituationEnableDescribe.Height);
            this.lblDisableSupervisionModeContent.Location = new Point(this.lblSelectSituationDisableDescribe.Location.X, this.lblSelectSituationDisableDescribe.Location.Y + this.lblSelectSituationDisableDescribe.Height);

            int enableSubPanelHeight = this.lblSelectSituationEnableDescribe.Height + this.lblEnableSupervisionModeContent.Height + 35;
            int disableSubPanelHeight = this.lblSelectSituationDisableDescribe.Height + this.lblDisableSupervisionModeContent.Height + 35;
            int subPanelHeight = enableSubPanelHeight >= disableSubPanelHeight ? enableSubPanelHeight : disableSubPanelHeight;

            this.pnlSelectSituationEnableSub.Size = new Size(312, subPanelHeight);
            this.pnlSelectSituationDisableSub.Size = new Size(312, subPanelHeight);

            int enablePanelHeight = this.picSelectSituationEnableImage.Height + this.pnlSelectSituationEnableSub.Height + 50;
            int disablePanelHeight = this.picSelectSituationDisableImage.Height + this.pnlSelectSituationDisableSub.Height + 50;
            int panelHeight = enablePanelHeight >= disablePanelHeight ? enablePanelHeight : disablePanelHeight;

            this.pnlSelectSituationEnable.Size = new Size(340, panelHeight);
            this.pnlSelectSituationDisable.Size = new Size(340, panelHeight);

            //this.lblSelectSituationTitle.Location = new Point(this.lblSelectSituationTitle.Location.X,
            //    (this.pnlSelectSituation.Height - (this.lblSelectSituationTitle.Height + 25 + this.pnlSelectSituationEnable.Height)) / 2);

            int selectLocationY = this.lblSelectSituationTitle.Bottom + 40;
            this.pnlSelectSituationEnable.Location = new Point(this.pnlSelectSituationEnable.Location.X, selectLocationY);
            this.pnlSelectSituationDisable.Location = new Point(this.pnlSelectSituationDisable.Location.X, selectLocationY);
        }

        /// <summary></summary>
        private void ShowFirstGuideHandle()
        {
            int locationX = 82;
            int marginTopBottom = (this.pnlFirstGuide.Height - (this.lblFirstGuideTitle.Height + this.lblFirstGuideContent.Height + this.btnFirstGuideLearnMore.Height + this.lblFirstGuideNote.Height + this.btnFirstGuideNoDevice.Height + 80)) / 2;

            this.picFirstGuideTitle.Location = new Point(88, marginTopBottom + 3);

            this.lblFirstGuideTitle.Location = new Point(locationX, marginTopBottom);
            this.lblFirstGuideContent.Location = new Point(locationX, marginTopBottom + this.lblFirstGuideTitle.Height + 5);
            this.btnFirstGuideLearnMore.Location = new Point(locationX, this.lblFirstGuideContent.Location.Y + this.lblFirstGuideContent.Height + 10);

            this.btnFirstGuideNoDevice.Location = new Point(locationX, this.pnlFirstGuide.Height - marginTopBottom - this.btnFirstGuideNoDevice.Height);
            this.lblFirstGuideNote.Location = new Point(locationX, this.btnFirstGuideNoDevice.Location.Y - this.lblFirstGuideNote.Height - 15);
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            MyForm.SetScreenCenter(this, true);

            if (!this.mCheckiTunesResult)
                this.mTimer = TimerMgr.Create(0.5, this.InstalliTunesDriveHandle);

            //发送日活数据
            TimerMgr.Create(3600, ChargeHelperForCast.SendLastRunInfo, callbackImediateliy: true);
        }

        private void picDevice_DoubleClick(object sender, EventArgs e)
        {
            MyTest.SetDoubleClick();
        }

        private void OnTestCallback(object sender, TestArg e)
        {
            ///存在notshowtestmode.dll文件则不显示测试模式
            if (File.Exists(Path.Combine(Folder.AppFolder, "notshowtestmode.dll")))
                return;

            this.lblInTestMode.Anchor = AnchorStyles.Left | AnchorStyles.Top;
            this.lblInTestMode.Size = new Size(150, 26);
            this.lblInTestMode.Location = new Point(220, (this.skTitleBarHeight - this.lblInTestMode.Height) / 2);
            this.lblInTestMode.Visible = e.Visible;
            this.lblInTestMode.skText = e.Title;          
        }

        private void SetControlEnabled(Control ctl, bool blnEnabled)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() =>
                {
                    this.SetControlEnabled(ctl, blnEnabled);
                }));
            }
            else
            {
                ctl.Enabled = blnEnabled;
            }
        }

        private void tsmiExit_Click(object sender, EventArgs e)
        {
            this.Hide();
            this.ShowInTaskbar = false;

            this.NotifyIcon.Visible = false;

            OnExitApp();
        }

        private void tsmFeedback_Click(object sender, EventArgs e)
        {
            frmFeedback frmFeedback = new frmFeedback();
            frmFeedback.ShowDialog();
        }

        private static void OnExitApp()
        {
            MyLog.WriteLine("OnExitApp -> Kill Process");

            ThreadMgr.Start(() =>
            {
                Thread.Sleep(500);

                SocketMgr.KillCurrentProcess();
            });
        }

        private void NotifyIcon_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            MainForm.ShowForm();
        }

        private void SetControlEnabled(ToolStripMenuItem ctl, bool blnEnabled)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ThreadStart(() =>
                {
                    this.SetControlEnabled(ctl, blnEnabled);
                }));
            }
            else
            {
                ctl.Enabled = blnEnabled;
            }
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationEnable_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblSelectSituationEnableDescribe_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationEnableSub_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelectSituationEnable_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>首页——开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void picSelectSituationEnableImage_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        /// <summary>连接引导页 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUsbConnectTipBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>设备未信任提示页 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectingGuideBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>没有设备？ 引导 按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeviceNotDetectedBack_Click(object sender, EventArgs e)
        {
            if (this.mPreviousPanel != null)
                this.InitPanel(this.mPreviousPanel);
            else
                this.SelectConnectGuide();
        }

        /// <summary>关闭监督模式  完成按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDisableSupervisionModeFinish_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DisableSupervisionModeFinish);

            this.mSelectDeviceMode = SelectDeviceMode.Normal;

            this.SelectFirstPage();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void picSelectSituationDisableImage_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationDisable_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void lblSelectSituationDisableDescribe_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void pnlSelectSituationDisableSub_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>首页——关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSelectSituationDisable_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }

        /// <summary>正在连接设备 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectingBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>切换语种</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnLanguageSetting_Click(object sender, EventArgs e)
        {
            //语言界面
            this.cmsLanguage.Items.Clear();

            string shortName;

            foreach (LangInfo info in this.Language.Languages)
            {
                shortName = this.LanguageDisplayNameHandle(info.LangDisplayName);

                if (info.LangDisplayName == LanguageInterface.Instance().CurrentLanguage.LangDisplayName)
                    this.cmsLanguage.Items.Add(shortName, Properties.Resources.ic_selected);
                else
                    this.cmsLanguage.Items.Add(shortName);
            }
            this.cmsLanguage.Show(this.btnLanguageSetting, new Point(0, this.btnLanguageSetting.Height));
        }

        /// <summary>选择语种</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cmsLanguage_ItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            string selectLanguage = this.LanguageDisplayNameHandle(e.ClickedItem.Text.ToString()); //selectLanguage; 

            string currentLanguageDisplayName = this.LanguageDisplayNameHandle(this.mCurrentLangDisplayName);
            if (selectLanguage == currentLanguageDisplayName)
                return;

            using (frmLanguageChange frmLanguageChange = new frmLanguageChange(selectLanguage))
            {
                if (frmLanguageChange.ShowDialog(this) != DialogResult.OK)
                    return;

                this.btnLanguageSetting.skText = selectLanguage;

                if (this.btnLanguageSetting.skText == currentLanguageDisplayName)
                    return;

                foreach (LangInfo info in this.Language.Languages)
                {
                    if (this.LanguageDisplayNameHandle(info.LangDisplayName) != this.btnLanguageSetting.Text)
                        continue;

                    this.mCurrentLangDisplayName = info.LangDisplayName;
                    IniHelper.SetValue(SettingKey.Language, info.LangName);
                }
                this.RestartApplication();
            }
        }

        /// <summary>开始 开启监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartEnableSupervisionMode_Click(object sender, EventArgs e)
        {
            if (this.mDevice == null)
            {
                this.SelectConnectGuide();
            }
            else
            {
                if (this.mDevice.FindMyPhone)
                    this.InitPanel(this.pnlCloseFindMyiPhoneTip);
                else
                    this.ActivateSuperviseStart();
            }
        }

        /// <summary>开始 关闭监督模式</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartDisableSupervisionMode_Click(object sender, EventArgs e)
        {
            if (this.mDevice == null)
            {
                this.SelectConnectGuide();
            }
            else
            {
                if (this.mDevice.FindMyPhone)
                    this.InitPanel(this.pnlCloseFindMyiPhoneTip);
                else
                    this.DeactivateSuperviseStart();
            }
        }

        /// <summary>开启监督模式 完成按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnEnableSupervisionModeFinish_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.EnableSupervisionModeFinish);

            this.mSelectDeviceMode = SelectDeviceMode.Normal;

            this.SelectFirstPage();
        }

        /// <summary>开启监督模式 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnActivatingSupervisionModeBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接成功 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectedSuccessfullyBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary></summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCloseSuperviseBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>关闭监督模式 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeactivatingSupervisionModeBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接/执行失败 返回按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectionFailedBack_Click(object sender, EventArgs e)
        {
            this.SelectFirstPage();
        }

        /// <summary>连接/执行失败 重试按钮</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnConnectionFailedTryAgain_Click(object sender, EventArgs e)
        {
            ThreadMgr.Start(() =>
            {
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.ConnectionRetry);
            });

            if (this.lblConnectionFailedTitle.skText == this.Language.GetString("Common.ConnectFailed"))
            {
                this.DeviceConnectHandle();
            }
            else
            {
                if (this.HaveNoDevice())
                {
                    this.InitPanel(this.pnlUsbConnectTip);
                }
                else
                {
                    if (this.mSelectDeviceMode == SelectDeviceMode.Activating)
                        this.ActivateSuperviseStart();
                    else
                        this.DeactivateSuperviseStart();
                }
            }
        }

        private string LanguageDisplayNameHandle(string langDisplayName)
        {
            string result = langDisplayName;

            if (!langDisplayName.Contains("中文") && langDisplayName.Contains("("))
                result = langDisplayName.Substring(0, langDisplayName.IndexOf('(')).ToString();

            return result;
        }

        private void pnlSelectSituationEnable_MouseEnter(object sender, EventArgs e)
        {
            this.pnlSelectSituationEnable.skMouseStateCustom = skMouseState.MouseHover;

            this.pnlSelectSituationEnable.Invalidate();
        }

        private void pnlSelectSituationDisable_MouseEnter(object sender, EventArgs e)
        {
            this.pnlSelectSituationDisable.skMouseStateCustom = skMouseState.MouseHover;

            this.pnlSelectSituationDisable.Invalidate();
        }

        private void pnlSelectSituation_MouseEnter(object sender, EventArgs e)
        {
            if (this.pnlSelectSituationEnable.skMouseStateCustom != skMouseState.MouseLeave)
            {
                this.pnlSelectSituationEnable.skMouseStateCustom = skMouseState.MouseLeave;
                this.pnlSelectSituationEnable.Invalidate();
            }

            if (this.pnlSelectSituationDisable.skMouseStateCustom != skMouseState.MouseLeave)
            {
                this.pnlSelectSituationDisable.skMouseStateCustom = skMouseState.MouseLeave;
                this.pnlSelectSituationDisable.Invalidate();
            }
        }

        private void lblSelectSituationTitle_DoubleClick(object sender, EventArgs e)
        {
            this.picDevice_DoubleClick(sender, e);
        }

        private void btnCloseFindMyiPhoneTip_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.UnableToCloseFindMyiPhone);

            Common.OpenUrl(string.Format("https://parental-control.flashget.com/{0}/how-to-fix-the-issue-of-not-being-able-to-turn-off-find-my-iphone",this.Language.CurrentLanguage.LangWithoutRegion));
        }

        private void btnCloseFindMyiPhoneBack_Click(object sender, EventArgs e)
        {
            //回到开启/关闭监督模式
            if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
                this.InitPanel(this.pnlEnableConnectedSuccess);
            else
                this.InitPanel(this.pnlDisableConnectedSuccess);
        }

        /// <summary>问题反馈</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnFeedBack_Click(object sender, EventArgs e)
        {
            Form form = MyForm.GetMainForm(typeof(frmFeedback).Name, false);

            if (form == null)
            {
                frmFeedback frmFeedback = new frmFeedback();
                frmFeedback.Owner = this;
                frmFeedback.ShowDialog();
            }
        }

        /// <summary></summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUsbConnectQuestion_Click(object sender, EventArgs e)
        {
            this.InitPanel(this.pnlDeviceNotDetected);

            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.UsbConnectNoDevice);
        }

        private void btnConnectionFailedTip_Click(object sender, EventArgs e)
        {
            this.InitPanel(this.pnlDeviceNotDetected);

            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.ConnectedFailedNoDevice);
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            //不响应DPI缩放
            MyRegistry.SetDpiAwareness(DpiAwareness.unaware);

            ChargeHelperForCast.SetDailyActiveData("FlashGet Kids Connector", UserOpetateState.None);

            base.OnClosing(e);
        }

        private void btnUploadLog_Click(object sender, EventArgs e)
        {
            skMsgInfoNew skMsgInfo = this.GetDefaultMsgInfo("请输入JIRA任务的完整Id，用于上传日志到JIRA上", "上传日志", this.Language);

            skMsgInfo.InputBoxInfo.Visible = true;
            skMsgInfo.InputBoxInfo.ButtonClear = new skMsgButton
            {
                Visible = true,
                Size = new Size(16, 16),
                skIconState = skImageState.FourState,
                Padding = new Padding(0, 0, 4, 0),
                skIcon = Properties.Resources.btn_4_cancel
            };
            skMsgInfo.MessageInfo.Padding = new Padding(0, 0, 0, 6);
            skMsgInfo.InputBoxInfo.IsPasswordMode = false;

            if (MsgBoxMgr.Show(this, skMsgInfo) != DialogResult.OK)
                return;

            this.DoCreateLogZip(skMsgInfo.InputBoxInfo.skText);
        }

        private void cmsEnableConnectedSuccessDevices_ItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            string selectDeviceName = e.ClickedItem.Text.ToString();

            foreach (iPhoneDevice device in this.mDevMgr.ConnectedDevices)
            {
                if (selectDeviceName != device.DeviceName)
                {
                    continue;
                }
                else
                {
                    this.mDevice = device;
                    break;
                }
            }

            if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
            {
                this.btnEnableConnectedSuccessDevice.skText = selectDeviceName;

                this.ConnectedSuccessTitleHandle(this.lblEnableConnectedSuccessTitle, selectDeviceName);
            }
            else
            {
                this.btnDisableConnectedSuccessDevice.skText = selectDeviceName;

                this.ConnectedSuccessTitleHandle(this.lblDisableConnectedSuccessTitle, selectDeviceName);
            }
        }

        private void btnDisableConnectedSuccessDevice_Click(object sender, EventArgs e)
        {
            //关闭监督模式 设备列表
            this.InitDeviceMenu(this.btnDisableConnectedSuccessDevice, true);
        }

        private void btnEnableConnectedSuccessDevice_Click(object sender, EventArgs e)
        {
            //开启监督模式 设备列表
            this.InitDeviceMenu(this.btnEnableConnectedSuccessDevice, true);
        }

        private void btnTurnOffFindMyiPhone_Click(object sender, EventArgs e)
        {
            if (this.btnTurnOffFindMyiPhone.skText.Equals(this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"))) //已关闭
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.TurnedOffFindMyiPhone);
            else
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.RecheckCloseFindMyiPhone);

            if (this.mDevice.FindMyPhone)
            {
                this.SetCloseFindMyiPhoneRecheckLabelStyle(true);

                this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button2"); //"Recheck"; 

                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.CloseFindMyiPhoneRecheck);
            }
            else
            {
                this.SetCloseFindMyiPhoneRecheckLabelStyle();

                this.btnTurnOffFindMyiPhone.skText = this.Language.GetString("Parental.Connect.turnofffindmyiphone.button1"); //Yes,turned off;

                //回到开启/关闭监督模式
                if (this.mSelectDeviceMode == SelectDeviceMode.Enable)
                    this.ActivateSuperviseStart();
                else
                    this.DeactivateSuperviseStart();
            }
        }

        private void btnFirstGuideLearnMore_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.FirstGuideLearnMore);
            
            Common.OpenUrl(string.Format(MyUrl.More, Language.CurrentLanguage.LangWithoutRegion));
        }

        private void btnFirstGuideNoDevice_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.FirstGuideNoDevice);

            this.InitPanel(this.pnlDeviceNotDetected);
        }

        private void lblEnableSupervisionModeContent_Click(object sender, EventArgs e)
        {
            this.EnableDeviceSupervisionMode();
        }

        private void lblDisableSupervisionModeContent_Click(object sender, EventArgs e)
        {
            this.DisableDeviceSupervisionMode();
        }
    }
}
